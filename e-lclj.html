<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>临床路径</title>
  <meta name="description" content="临床路径">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
.container_lclj{
  height: 100%;
}
.container_inner{
  padding: 20px;
}

.lclj_right{
  padding-top: 40px;
}
.br_box{
  width: 450px;
  /* height: 180px; */
  height: 210px;
  background: #FFFFFF;
  border: 1px solid rgb(0 0 0 / 40%);
  border-radius: 8px;
  padding: 8px 10px;
}
.box_inner{
  float: left;
  height: 100%;
  padding-left: 20px;
  width: 75%;
}
.box_inner h{
  font-family: 'Microsoft YaHei';
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
}
.box_inner span{
  font-family: 'Microsoft YaHei';
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
}
.box_btnlists{
  float: right;
  /* padding-right: 30px; */
}
.btn_list{
  width: 100px;
  margin: 3px 0;
}
.yz_box{
  width: 450px;
  height: 50px;
  background: #F6F8FF;
  border: 1px solid rgb(0 0 0 / 40%);
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  margin-top: 20px;
}
.box_header_title{
  font-family: 'Microsoft YaHei';
  font-style: normal;
  font-weight: 700;
  font-size: 15px;
  line-height: 50px;
  padding-left: 20px;
}
.input-group{
  width: 200px;
  float: right;
  margin: 7px 20px 0px 0px;
}
.box_table{
  width: 450px;
}
.box_table tr{
  border: 1px solid;
}
.box_table input[type="checkbox"]{
  width: 45px;
  height: 15px;
}
.table_td{
  padding: 3px 5px;
}
.lclj_left{
  padding-right: 15px;
}
.nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
  border: none;
  border-bottom: 3px solid #3D6CC8;
  cursor: default;
  background-color: none;
  color: #3D6CC8;
}
.ryts_title{
  padding: 10px;
  font-size: 12px;
}
.ylhl_box{
  padding: 0px 10px 10px 10px;
}
/*  */
.small_a{
  display: inline-block;
  width: 9px;
  height: 9px;
  border-radius: 4px;
  margin-right: 10px;
  vertical-align: middle;
}
.middle_a{
  display: inline-block;
  width: 14px;
  height: 14px;
  border-radius: 16px;
  margin-right: 10px;
  vertical-align: middle;
}
.orange{
  background: #FFAB6F;
}
.grey{
  /* background: rgba(128, 128, 128, 0.5); */
  background: #fff;
}
.crile_blue{
  background: #3D6CC8;
}
.blue{
  background: #A4CEFF;
}
.yellow{
  background: #FFDB5B;
}
.step_content{
  width: 100%;
  height: 100%;
  border: 1px solid #DDDDDD;
  border-radius: 8px 8px 0px 0px;
}
.yz_title,.ylhl_title{
  font-family: 'Microsoft YaHei';
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
}
.ylhl_content{
  /* background: #D1D1D2; */
  padding-left: 20px;
  min-height: 100px;
}
.ylhl_content ul{
  padding: 10px 20px;
  background: rgb(209 209 210 / 60%);
  border-radius: 8px;
}
.ylhl_content ul li{
  width: 50%;
  float: left;
}
.ylhl_content ul li span{
  font-size: 15px;
  font-family: Tahoma,Verdana,微软雅黑,新宋体;
}
.ylhl_content ul li .font_black{
  color:#000
}
.ylhl_content ul li .font_red{
  color:#bf0404
}
.yz_table{
  width: 100%;
}
.yz_table thead{
  background: #A3BEFF;
  height: 40px;
}
.yz_table tbody tr{
  border-bottom: 1px solid rgb(0 0 0 / 30%);
}
.yz_table tbody tr:hover{
  background: #EBEBEB;
}
.yz_table tbody tr .first_td{
  text-align: center;
}
.two_td{
  font-family: 'Microsoft YaHei';
  font-style: normal;
  font-weight: 700;
  cursor: pointer;
}
.four_td{
  padding: 5px 0px;
}
.box_table .td_text{
  width: 50px;
  border-bottom: 1px solid;
  text-align: center;
}
.box_table .td_text:focus{
  border: 1px solid;
  border-radius: 3px;
}
.yz_table tbody tr{
  height: 35px;
}
.none{
  display: none;
}
.yz_lists{
  height: 600px;
  overflow-y: auto;
}
.load_model{
  position: absolute;
  width: 100%;
  height: 100%;
  display: none;
  align-items: center;
  justify-content: center;
  flex-flow: column;
  background: rgb(0 0 0 / 10%);
  z-index: 2000;
}
.load_img{
  display: flex;
}
.yf_input{
  right: 260px;
  width: 90px;
}
.pl_input{
  right: 195px;
  width: 100px;
}
.model_input{
  border: 1px solid;
  position: absolute;
  height: 265px;
  z-index: 10;
  background-color: white;
  visibility: visible;
  overflow: hidden auto;
}
.model_input input{
  cursor: context-menu;
}
.model_input input:focus{
  background: #A3BEFF;
}
.crile_color{
  background: #5cb85c;
}
.crile_grey{
  background: rgba(128, 128, 128, 0.5);
}
.lb_ts_tooltip {
    position: relative;
    text-decoration: none;
    display: inline-block;
    /* border-bottom: 1px dotted black; 悬停元素上显示点线 */
}
/* Tooltip 文本 */
.lb_ts_tooltip .lb_ts_tooltiptext {
    visibility: hidden;
    width: 240px;
    background-color: #6e6969de;
    color: #fff;
    text-align: left;
    padding: 5px 0px 5px 10px;
    border-radius: 6px;
    /* 定位 */
    position: absolute;
    top: 20px;
    z-index: 99;
}
.lb_ts_tooltip .lb_ts_tooltiptext span {
  font-size: 14px;
}
/* 鼠标移动上去后显示提示框 */
.lb_ts_tooltip:hover .lb_ts_tooltiptext {
    visibility: visible;
}

#left_content>.tab-content>.tab-pane {
  /* display: block; */
  opacity: 0;
}
#left_content>.tab-content>.active {
    /* display: block; */
    opacity: 1;
}
.table_td .kssj_wdata{
  width: 140px;
  border-bottom: 1px solid;
  border-left: none;
  border-right: none;
  border-top: none;
}
.table_td .kssj_wdata:focus{
  border: 1px solid;
  border-radius: 3px;
  height: 25px;
}

</style>
<body>
  <div class="container_lclj flex-row flex-column">
    <div class="container_inner flex-row flex-fill">
      <div id="left_content" class="lclj_left flex-fill">

      </div>
      <div class="lclj_right">
        <div class="br_box">
          <div id="person_list" class="box_inner">
            
          </div>
          <div class="box_btnlists">
            <a-button class="e_btn btn_list" onclick="onSave()">保存</a-button></br>
            <a-button class="e_btn btn_list" onclick="GetEnable()">重新评估</a-button></br>
            <a-button class="e_btn btn_list" onclick="Getqzcj()">强制出径</a-button></br>
            <a-button class="e_btn btn_list" onclick="textOut()">操作说明</a-button></br>
            <!-- <a-button class="e_btn btn_list" onclick="taskDone()">完成</a-button> -->
          </div>
        </div>
        <div>
          <div class="yz_box">
            <span class="box_header_title">医嘱内容</span>
            <div class="input-group">
              <input id="search_text" type="text" class="form-control" autocomplete="off" placeholder="输入关键字" onkeydown="calAge()">
              <div class="input-group-addon" onclick="onSearch()"><i class="glyphicon glyphicon-search"></i></div>
            </div>
          </div>
          <div style="height: 600px;overflow-y: auto;overflow-x: hidden;">
            <table id="YpmbDetail" class="box_table">
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-lclj.js?v=" + Date.now() + "='><\/script>");
  </script>
</body>
</html>