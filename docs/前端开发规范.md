## 前端开发规范

| 版本号 | 变更点说明 | 变更日期 | 变更人 | 审批人 |
| -- | -- | -- | -- | -- |
| V0.1 | 创建 | 2021/03/01 | 金建 |  |  |

### 目标
在附一医开发项目过程中，实现前后端分离，统一前端开发规范。前端开发人员务必了解和遵守。

### 开发环境

| 项目 | 名称 | 备注 |
| -- | -- | -- | 
| Js 架构 | 	Jquery 1.9.1| 	DOM操作和通讯| 
| 配套扩展模块| 	Jquery.easyui等| 	Jq 相关扩展模块| 
| Css 架构| 	Bootstrap v3| 自适应CSS 架构，配合JQ实现布局| 
| 前端模板工具| 	UnderScore	| 实现前端html渲染| 
| 通用界面工具| 	自行开发	| 统一表格、日期、弹框、通知等通用界面| 
| 模块化封装| 	立即执行函数 IIFE	| 隐藏模块私有成员，减少全局冲突，暂不采用mvc| 
| 数据交换方式| 	Ajax JSON格式| 	现有部分html格式返回的接口需要改造| 
| 打包工具| 	无	| 不采用打包工具| 
| 代码管理| 	GitLab|  | 	
| IDE工具| 	VS Code|  | 	

### 测试和运行环境

|项目|	名称|	备注|
| -- | -- | -- | 
|浏览器	|Chrome |69以上|	
|分辨率	|主要：1920x1080 	|能自适应其他分辨率
|测试Web端	|Asp.net|	开启localhost:8080跨域，方便开发环境下的测试工作|
|测试数据库		|医院维护。|测试数据需配合测试用例要求。|
|部署策略	||手工部署到正式服务器	|

### 目录规范
+ 项目主目录：	
  +	所有的Html 文件，小写英文，使用 - 分割，e-后缀.html。
  + config.js配置文件 config.js。config.js为公用模块，每个页面必须加载。
  + readme.md 项目更新记录。
+ Lib 子目录：所有第三方模块。
+ Js 子目录：项目组开发的js文件。
  +	e-common.js 为公用模块，每个页面必须加载。
  +	e-interface.{模块}.js 保存所有接口。Ajax只能出现在interface文件。
+ Css 子目录：项目组开发的 css 文件。
  -	e-common.css 公用模块，每个页面必须加载。
+ Images 子目录：存放静态图片。
+ Docs 子目录：存放相关文档。
  +	前端开发规范.md 本文件


### 格式规范
所有代码使用Vs Code 进行统一格式化，采用默认配置。

### Html 编写规范

+ Html 不得出现 js 代码。通过引用同名 e-<名称>.js 开始页面周期。
+ 引用文件不得分开书写，放到一个段落。 先 link 后 script  
+ 统一使用相对路径。
+ 避免 css 直接嵌入 html。可以在文件末尾增加本页的样式定义。

``` html
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>登录</title>
  <meta name="description" content="登录页">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <link rel="stylesheet" href="css/e-common.css">
  <!-- 页面CSS -->
  <link rel="stylesheet" href="css/e-style.css">
</head>

<body>
  。。。。。
  。。。。。

  <!-- 公用模块JS -->
  <script src="js/e-common.js"></script>
  <!-- 页面JS -->
  <script src="js/e-login.js"></script>
</body>
</html>
```

### Js 编写规范

+ Js 文件头须描述本 js 的基本功能和目标
+ 页面启动代码统一放到 $(document).ready，只在页面同名js 中启用，并只出现一次。
+ 函数需描述功能

``` JavaScript
/* 
e-login.js 登陆页面代码
*/

// 统一页面启动
$(document).ready(
    function() {

    }
)

// 模块组件类，只实现 render 渲染
function E_Block() {
    this.$el = null;
    this.options = {};
    this.data = null;
}

```


