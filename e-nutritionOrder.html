<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>营养医嘱单</title>
  <meta name="description" content="营养医嘱单(新增)">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
#nutritionOrder {
  width: 100%;
  /* height: auto; */
}
.nutriOrderMain {
  padding-top: 50px;
}
#nutritionOrder, #nutriOrderShowBaseInfo, #nutriOrderTableAll, .nutriOrderTitle, .nutriOrderList, .nutriOrderCountTable, .nutriOrderBtnAll, .nutriOrderShowTable {
  text-align: center;
  margin: auto;
}

 /* 基础信息 */
#nutriOrderShowBaseInfo {
  font-family: 'Microsoft YaHei';
  width: 96%;
  /* height: 60px;
  line-height: 60px; */
  /* padding: 15px 10px 15px 5px; */
  /* padding: 15px 20px; */
  padding: 1%;
  padding-top: 1.5%;
  border: 1px solid #E6E6E6;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  align-items: flex-start;
  flex-direction: column;
  /* display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  align-items: center; */
}
.noTBinfoR {
  /* width: 60%; */
  /* width: 82%; */
  /* width: 79%; */
    /* display: contents; */
  display: flex;
  align-items: center;
  text-align: left;
  font-size: 12px;
}
.noTBinfoR > span {
  /* padding-right: 15px; */
  padding-right: 10px;
  /* padding-right: 2%; */
}
.tBinfoPoint {
  color:#489CFF;
  font-weight: bolder;
}
.tBinfoBold {
  font-weight: bolder;
}
.tBinfoInput {
  /* width:48px; */
  width:55px;
  /* width:64px; */
  /* border: 1px solid #B7C3DA; */
  border: 1px solid #E6E6E6;
  height: 24px;
  border-radius: 4px;
}
.tBinfoBcNum {
  font-family: 'Microsoft YaHei';
  font-style: normal;
  font-weight: 400;
  /* line-height: 22px; */
  color: #215FEB;
  text-align: center;
  background: #C4D2F2;
  border-radius: 4px;
}
.noTBtipL {
  /* width: 18%; */
  /* width: 21%; */
  padding: 10px 0 0 33px;
  color: red;
}


/* 第二部分 */
#nutriOrderTableAll {
  font-family: 'Microsoft YaHei';
  width: 95%;
  padding: 0 10px;
  /* padding: 0 22px; */
  border: 1px solid #E6E6E6;
  /* border: 1px solid #B7C3DA; */
  border-radius: 8px;
}
.nutriOrderLT {
  padding-bottom: 12px;
}
.nutriOrderTitle {
  display: flex;
  justify-content: space-between;
  /* padding: 18px 22px;  */
  padding: 18px 0px; 
  align-items: center;
}
.nutriOrderTitle >span {
  font-family: 'Microsoft YaHei';
  font-style: normal;
  font-weight: 700;
  font-size: 18px;
  line-height: 22px;
}
.iconChange {
  cursor: pointer;
}
.nutriOrderList {
  display: flex;
  justify-content: space-between;
}
#tb_Table {
  font-size: 12px;
  width: 395px;
}
.tb_title0 {
  margin-right: 2px;
}
.tb_title1 {
  margin-left: 2px;
}
#table_header .table-title {
  /* height: 40px;
  line-height: 40px; */
  font-weight: bold;
  /* padding-left: 30px; */
  color: #4A4A4A;
}
.table_headerT {
  height: 30px;
  line-height: 30px;
}
/* #table_header .table-nr {
  height: 40px;
  line-height: 40px;
  padding-left: 30px;
} */
.table_headerD {
  border-bottom: 1px solid #E6E6E6;
}
.table_nrInput {
  width: 48px;
  color: red;
}
.table_nrInput:focus {
  border: 1px solid #E6E6E6;
}

/* 计算表格 */
.nutriOrderCountTable{
  background: #F5F8FD;
  border: 1px solid #EBEBEB;
  border-radius: 4px;
}
.ct_Table {
  width: 100%;
  background: #F5F8FD;
  /* background: linear-gradient(0deg, #EBEBEB, #EBEBEB),linear-gradient(0deg, #F5F8FD, #F5F8FD); */
  border: 1px solid #EBEBEB;
  border-radius: 4px;
}
.ct_Table .table-nr{
  padding: 2px 5px;
}
.ctTable_nrInput, .ctTable_nrInputLong {
  color: red;
  background: #F5F8FD;
}
.ctTable_nrInput {
  /* width: 48px; */
  width: 68px;
}
.ctTable_nrInputLong:focus {
  border: 1px solid #E6E6E6;
}
.ctTable_nrInput:focus {
  border: 1px solid #E6E6E6;
}

/* 按钮汇总 */
.nutriOrderBtnAll {
  padding: 12px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
}
.inputBtn>span {
  padding-left: 5px;
}
.inputTit{
  font-family: 'Microsoft YaHei';
  font-style: normal;
  font-weight: 700;
  /* font-size: 15px; */
  line-height: 22px;
}
.Btn_nrInput {
  /* border: 1px solid #F9F9FB; */
  border: 1px solid #b4b4b4;
  border-radius: 4px;
}
.e-btn {
  border: 1px solid #fff;
  background: #489CFF;
  color: #fff;
  padding: 2px 15px;
  border-radius: 4px;
  cursor: pointer;
}

/* 最终数据展示部分 */
.nutriOrderShowTable {
  background: #F9F9FB;
  border-radius: 8px;
  padding: 12px 0;
}
.nutriOrderShowTable > span {
  font-weight: 700;
  float: left;
  font-size: 15px;
  padding: 5px 14px 10px;
  /* padding: 14px; */
}
.show_Table {
  width: 96%;
  margin: auto;
  border: 1px solid #EBEBEB;
  /* border-collapse: separate !important;
  border-spacing: 0 !important;
  border-radius: 4px !important;
  border: solid 1px #dfdfdf !important; */
  /* border-radius: 4px; */
}
.show_Table .table-nr{
  padding: 2px 5px;
}
</style>
<body id="nutritionOrder">
  <div class="nutriOrderMain">
    <!-- 基础 -->
    <div id="nutriOrderShowBaseInfo"></div>
    <!-- 表格汇总 -->
    <div id="nutriOrderTableAll">
      <!-- <div class="nutriOrderLT"> -->
        <!-- 医嘱单名字<eg.胃肠外营养医嘱单></eg.> -->
        <!-- <div class="nutriOrderTitle"></div> -->
        <!-- 输入(操作)列表 -->
        <!-- <div class="nutriOrderList"></div> -->
      <!-- </div> -->
      <!-- 计算表格 -->
      <!-- <div class="nutriOrderCountTable"></div> -->
      <!-- 按钮汇总（开始时间，持续天数，用药频率，用法， 保存 提交） -->
      <!-- <div class="nutriOrderBtnAll"></div> -->
      <!-- 展示表格（最终数据展示） -->
      <!-- <div class="nutriOrderShowTable"></div> -->
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-nutritionOrder.js'><\/script>");
  </script>
</body>
</html>