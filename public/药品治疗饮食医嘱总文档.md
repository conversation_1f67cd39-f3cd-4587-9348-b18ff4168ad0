## 全局公共参数
#### 全局Header参数
参数名 | 示例值 | 参数描述
--- | --- | ---
token | eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiLljZXln7nku4EiLCJpc3MiOiI5OSIsInlpbmdZb25nRE0iOiIxMjAiLCJleHAiOjE3MDI4NjY1ODYsInVzZXJJZCI6MTExMDF9.MFCaM4DNzADFYNnaFSeM_ljMjucdqheBWZfIVUpEV8Y | -
#### 全局Query参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 全局Body参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 全局认证方式
```text
noauth
```
#### 全局预执行脚本
```javascript
暂无预执行脚本
```
#### 全局后执行脚本
```javascript
暂无后执行脚本
```
## /住院医嘱
```text
Advice Inpatient Controller
```
#### Header参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### Query参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### Body参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
## /住院医嘱/新增医嘱执行记录审批
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/addYzZxJlSp

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"yiZhuList":"","bingLiID":"","zhuYuanID":"","bingQuID":"","zhuanKeID":"","zhiXingSJ":"","zhiXingRYID":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
yiZhuList | - | String | 否 | 医嘱列表:yp+组号,zl+医嘱ID
bingLiID | - | Integer | 否 | 病历ID
zhuYuanID | - | Integer | 否 | 住院ID
bingQuID | - | Integer | 否 | 病区ID
zhuanKeID | - | Integer | 否 | 专科ID
zhiXingSJ | - | String | 否 | 执行时间
zhiXingRYID | - | Integer | 否 | 执行人员ID
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/修改_医生撤销执行
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/cheXiaoZxByYs

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"yiZhuIDList":[{}],"bingLiID":"","zhuYuanID":"","cheXiaoLY":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
yiZhuIDList | - | Array | 否 | 医嘱ID列表
bingLiID | - | Integer | 否 | 病历ID
zhuYuanID | - | Integer | 否 | 住院ID
cheXiaoLY | - | String | 否 | 撤销理由
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_有无出院录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/checkChuYuanLu?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Integer | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_检查伏立康唑
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/checkFuLiKangZuo?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Boolean | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/修改_检查今日出院
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/checkJinRiCY

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"bingLiID":"","yiZhuIDs":[{}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yiZhuIDs | - | Array | 否 | 医嘱ID列表，yl_yzall.yzid
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/修改_检查术前医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/checkShuQianYz

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"bingLiID":"","yiZhuIDs":[{}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yiZhuIDs | - | Array | 否 | 医嘱ID列表，yl_yzall.yzid
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_获取最后一次抗菌药物医嘱的使用方法
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/checkYuFangYY?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/修改_检查执行病区
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/checkZhiXingBQ

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"bingLiID":"","zhiXingBQs":[{}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
zhiXingBQs | - | Array | 否 | 执行病区列表
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_检查中药饮片的一次用量必须是小包装规格组合
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/checkZhongYaoYiCiYL?jiLiangDW=&yaoFangDM=&yaoPinID=&yaoPinMC=&yiCiYL=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
jiLiangDW | - | Text | 是 | 剂量单位
yaoFangDM | - | Text | 是 | 药房代码
yaoPinID | - | Text | 是 | 药品ID
yaoPinMC | - | Text | 是 | 药品名称
yiCiYL | - | Text | 是 | 一次用量
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/删除时间针提醒
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/deleteShiJianZhenTx

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/撤回医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/disSubmitYiZhu

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","yiZhuList":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yiZhuList | - | String | 否 | 医嘱列表（yp222222,zl333333,ys444444）
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_获取所有省
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getAllProvince

#### 请求方式
> GET

#### Content-Type
> none

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"jiGuanDM":"","jiGuanMC":"","pinYin":"","wuBi":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.jiGuanDM | - | String | 籍贯代码
data.jiGuanMC | - | String | 籍贯名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_根据城市代码获取区
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getAreasByChengShiDM?chengShiDM=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
chengShiDM | - | Text | 是 | 城市代码
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"jiGuanDM":"","jiGuanMC":"","pinYin":"","wuBi":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.jiGuanDM | - | String | 籍贯代码
data.jiGuanMC | - | String | 籍贯名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/获取病人医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getBingRenYz

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","yiZhuLB":"","zaiYong":"","tingZhi":"","leiBie":"","feiYongLX":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yiZhuLB | - | String | 否 | 医嘱类别：1=长期，2=临时，jcyy=检查用药(当作临时医嘱)
zaiYong | - | String | 否 | 是否在用：1=取在用 0=取所有
tingZhi | - | String | 否 | 是否显示停止医嘱：1=显示，0=不显示
leiBie | - | String | 否 | 类别：空=全部，1药品，0饮食，2治疗，3化验，4检查，98嘱托，99其他
feiYongLX | - | String | 否 | 费用类型：空=全部，0自费，1甲类，2乙类，99其他
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yiZhuID":"","bingLiID":"","zhuYuanID":"","zhuanKeID":"","bingQuID":"","bingQuMC":"","ziFei":"","xiangMuID":"","zuHao":"","yaoFangDM":"","mingCheng":"","danJia":"","guiGe":"","jiXing":"","yiCiYL":"","jiLiangDW":"","zhiXingFF":"","zhiXingPL":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","kaiShiSJ_rq":"","kaiShiSJ_sj":"","jieShuSJ":"","chiXuTS":"","yiZhuLB":"","shouFeiSL":"","shouFeiCS":"","yanSe":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","luRuSJ":"","yiShengID":"","yiShengXM":"","tingZhiSJ":"","tingZhiYSID":"","tingZhiYSXM":"","cheXiaoZT":"","cheXiaoZTMC":"","cheXiaoSJ":"","cheXiaoRYID":"","cheXiaoRYXM":"","xuShenPi":"","shenQingSL":"","tongZhiDanID":"","tongZhiDanIDMC":"","kangJunYP":"","zuHaoTXT":"","yongYaoTS":"","kaiDanYSZKID":"","mianPiShi":"","daoRuRYID":"","daoRuRYXM":"","shiFouBL":"","danWei":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRRYXM":"","tingZhiDRSJ":"","yaoPinLB":"","yaoPinLBMC":"","shiFouZB":"","shiFouZBMC":"","zhiXingSJ":"","teShuKJYWHZDID":"","zhiXingRYID":"","sanLianKJYWHZDID":"","zhiXingRYXM":"","jinRiCY":"","jinRiCYMC":"","zanShiBQ":"","zanShiBQMC":"","yongYaoSQ":"","yaoPinDS":"","zhiXingPL2":"","yaoPinDSBZ":"","laiYuan":"","maZuiYT":"","xianDingFW":"","jiGouDM":"","jinRiLT":"","tingZhiLX":"","tingZhiLXMC":"","leiBie":"","leiBieMC":"","leiXing":"","leiXingMC":"","val":"","yiZhuLX":"","yiZhuLXMC":"","feiYongLX":"","feiYongLXMC":"","biHuanDZ":"","buLiangFYSB":"","tiaoXingMa":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.zhuanKeID | - | Integer | 专科ID
data.bingQuID | - | Integer | 病区ID
data.bingQuMC | - | String | 病区名称
data.ziFei | - | String | 自费(1=是，0=否)
data.xiangMuID | - | String | 项目ID
data.zuHao | - | Integer | 组号（真实组号）
data.yaoFangDM | - | String | 药房代码
data.mingCheng | - | String | 名称
data.danJia | - | Number | 单价
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 计量单位
data.zhiXingFF | - | String | 执行方法
data.zhiXingPL | - | String | 执行频率
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间（yyyy-MM-dd HH:mm:ss）
data.kaiShiSJ_rq | - | String | 开始时间（yyyy-MM-dd）
data.kaiShiSJ_sj | - | String | 开始时间（HH:mm）
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuLB | - | String | 医嘱类别
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.yanSe | - | String | 颜色
data.zhuangTaiBZ | - | String | 状态标志
data.zhuangTaiBZMC | - | String | 状态标志名称
data.luRuSJ | - | String | 录入时间
data.yiShengID | - | Integer | 医生ID
data.yiShengXM | - | String | 医生姓名
data.tingZhiSJ | - | String | 停止时间
data.tingZhiYSID | - | Integer | 停止医生ID
data.tingZhiYSXM | - | String | 停止医生姓名
data.cheXiaoZT | - | String | 撤销状态
data.cheXiaoZTMC | - | String | 撤销状态名称
data.cheXiaoSJ | - | String | 撤销时间
data.cheXiaoRYID | - | Integer | 撤销人员ID
data.cheXiaoRYXM | - | String | 撤销人员姓名
data.xuShenPi | - | String | 需审批
data.shenQingSL | - | Number | 申请数量
data.tongZhiDanID | - | Integer | 通知单ID
data.tongZhiDanIDMC | - | String | 通知单ID名称
data.kangJunYP | - | String | 抗菌药品
data.zuHaoTXT | - | String | 组号TXT
data.yongYaoTS | - | Number | 用药天数
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.mianPiShi | - | String | 免皮试
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuRYXM | - | String | 导入人员姓名
data.shiFouBL | - | String | 是否补录
data.danWei | - | String | 单位
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRRYXM | - | String | 停止导入人员姓名
data.tingZhiDRSJ | - | String | 停止导入时间
data.yaoPinLB | - | String | 药品类别
data.yaoPinLBMC | - | String | 药品类别名称
data.shiFouZB | - | String | 是否自备
data.shiFouZBMC | - | String | 是否自备名称
data.zhiXingSJ | - | String | 执行时间
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.zhiXingRYID | - | Integer | 执行人员ID
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.zhiXingRYXM | - | String | 执行人员姓名
data.jinRiCY | - | String | 今日出院
data.jinRiCYMC | - | String | 今日出院名称
data.zanShiBQ | - | String | 暂时不取
data.zanShiBQMC | - | String | 暂时不取名称
data.yongYaoSQ | - | String | 用药时期
data.yaoPinDS | - | Number | 药品滴速
data.zhiXingPL2 | - | String | 执行频率2
data.yaoPinDSBZ | - | String | 药品滴速备注
data.laiYuan | - | String | 来源
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
data.tingZhiLXMC | - | String | 停止类型名称
data.leiBie | - | String | 类别（yp=药品，zl=治疗，ys=饮食，zt=嘱托）
data.leiBieMC | - | String | 类别（yp=药品，zl=治疗，ys=饮食，zt=嘱托）
data.leiXing | - | String | 类型（cq=长期医嘱，ls=临时医嘱，cy=草药，cydy=出院带药，zcydy=草药带药）
data.leiXingMC | - | String | 类型（cq=长期医嘱，ls=临时医嘱，cy=草药，cydy=出院带药，zcydy=草药带药）
data.val | - | String | 药品=yp+组号，治疗=zl+医嘱ID，饮食=ys+医嘱ID
data.yiZhuLX | - | String | 医嘱类型（0饮食药品治疗化验检查医用食品）
data.yiZhuLXMC | - | String | 医嘱类型名称（0饮食药品治疗化验检查医用食品）
data.feiYongLX | - | String | 费用类型（0自费，1甲类，2乙类，99其他）
data.feiYongLXMC | - | String | 费用类型名称
data.biHuanDZ | - | String | 闭环地址
data.buLiangFYSB | - | String | 不良反应上报
data.tiaoXingMa | - | String | 条形码
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/肠内营养决策提醒
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getChangNeiYyjcTx?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"biaoTi":"","bingLiID":"","laiYuan":"","moKuaiMC":"","neiRongs":[{}],"tiXingLB":"","tiXingWZ":"","tiaoJian":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.biaoTi | - | String | 标题
data.bingLiID | - | Integer | 病历ID
data.laiYuan | - | String | 来源
data.moKuaiMC | - | String | 模块名称
data.neiRongs | - | Array | 内容
data.tiXingLB | - | String | 提醒类别
data.tiXingWZ | - | String | 提醒位置，1=电子病历，2=护理平台
data.tiaoJian | - | String | 条件
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_处方诊断
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getChuFangZD?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"icd":"","id":"","bingLiID":"","leiBie":"","xuHao":"","fuID":"","daiMa":"","mingChen":"","zhuanGuiQK":"","caoZuoZhe":"","caoZuoZheID":"","xiuGaiSJ":"","jiBingID":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.icd | - | String | 
data.id | - | Integer | 
data.bingLiID | - | Integer | 病历ID
data.leiBie | - | String | 类别
data.xuHao | - | Integer | 序号
data.fuID | - | Integer | 父ID
data.daiMa | - | String | 代码
data.mingChen | - | String | 名称
data.zhuanGuiQK | - | String | 转归情况
data.caoZuoZhe | - | String | 操作者
data.caoZuoZheID | - | Integer | 操作者ID
data.xiuGaiSJ | - | String | 修改时间
data.jiBingID | - | Integer | 疾病ID
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_根据省份代码获取市
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getCitysByShengFenDM?shengFenDM=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
shengFenDM | - | Text | 是 | 省份代码
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"jiGuanDM":"","jiGuanMC":"","pinYin":"","wuBi":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.jiGuanDM | - | String | 籍贯代码
data.jiGuanMC | - | String | 籍贯名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_获取当前时间yyyy-MM-dd HH:mm:ss
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getCurrentDateTime

#### 请求方式
> GET

#### Content-Type
> none

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/根据病历ID列表查询到期抗菌药品
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getDaoQiKjypByBlids

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"bingQuZDYM":"","chuangWeiHao":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","tingZhiSJ":"","shouShuMC":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yaoPinID":"","jiLiang":"","jiXing":"","baoZhuangLiang":"","yiCiYL":"","jiLiangDW":"","ciShu":"","qingJieSS":"","qingJieSSGWYS":"","zhiHangFF":"","qingJieSSSPYSID":"","yaoPinMC":"","kangJunFL":"","qingJieSSSPYJ":"","qingJieSSSPSJ":"","touBaoJB":"","fuHeKJ":"","shiFouYWCG":"","fuKuiNT":"","weiChanGWYSS":"","qiTaWCGWYS":"","shiYongFF":"","shiFouYLCBX":"","yongYaoFL":"","linChuangBX":"","shiQuanSYFL":"","shiYongYY":"","yiShengID":"","xiuGaiSJ":"","zhuanKeID":"","qieKouLB":"","zhiLiaoZuID":"","weiShengWu":"","weiShengWuBZ":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.bingQuZDYM | - | String | 病区自定义码
data.chuangWeiHao | - | String | 床位号
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.tingZhiSJ | - | String | 停止时间
data.shouShuMC | - | String | 手术名称
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yaoPinID | - | Integer | 药品ID
data.jiLiang | - | String | 剂量
data.jiXing | - | String | 剂型
data.baoZhuangLiang | - | Integer | 包装量
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.ciShu | - | Integer | 次数
data.qingJieSS | - | String | 清洁手术
data.qingJieSSGWYS | - | String | 清洁手术_高危因素
data.zhiHangFF | - | String | 执行方法
data.qingJieSSSPYSID | - | Integer | 清洁手术_审批医生ID
data.yaoPinMC | - | String | 药品名称
data.kangJunFL | - | String | 抗菌分类
data.qingJieSSSPYJ | - | String | 清洁手术_审批意见
data.qingJieSSSPSJ | - | String | 清洁手术_审批时间
data.touBaoJB | - | String | 头孢级别
data.fuHeKJ | - | String | 符合抗菌
data.shiFouYWCG | - | String | 新生儿预防用药，是否有围产高危因素，1=是，0=否
data.fuKuiNT | - | String | 氟奎诺同
data.weiChanGWYSS | - | String | 新生儿预防用药，围产高危因素
data.qiTaWCGWYS | - | String | 新生儿预防用药，其他围产高危因素
data.shiYongFF | - | String | 使用方法
data.shiFouYLCBX | - | String | 新生儿预防用药，是否有临床表现，1=是，0=否
data.yongYaoFL | - | String | 用药分类
data.linChuangBX | - | String | 新生儿预防用药，临床表现
data.shiQuanSYFL | - | String | 石泉使用分类
data.shiYongYY | - | String | 使用原因
data.yiShengID | - | Integer | 医生ID
data.xiuGaiSJ | - | String | 修改时间
data.zhuanKeID | - | Integer | 专科ID
data.qieKouLB | - | String | 切口类别
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.weiShengWu | - | String | 微生物
data.weiShengWuBZ | - | String | 微生物备注
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/根据专科ID查询到期抗菌药品
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getDaoQiKjypZkid?zhuanKeID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
zhuanKeID | - | Text | 是 | 专科ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"bingQuZDYM":"","chuangWeiHao":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","tingZhiSJ":"","shouShuMC":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yaoPinID":"","jiLiang":"","jiXing":"","baoZhuangLiang":"","yiCiYL":"","jiLiangDW":"","ciShu":"","qingJieSS":"","qingJieSSGWYS":"","zhiHangFF":"","qingJieSSSPYSID":"","yaoPinMC":"","kangJunFL":"","qingJieSSSPYJ":"","qingJieSSSPSJ":"","touBaoJB":"","fuHeKJ":"","shiFouYWCG":"","fuKuiNT":"","weiChanGWYSS":"","qiTaWCGWYS":"","shiYongFF":"","shiFouYLCBX":"","yongYaoFL":"","linChuangBX":"","shiQuanSYFL":"","shiYongYY":"","yiShengID":"","xiuGaiSJ":"","zhuanKeID":"","qieKouLB":"","zhiLiaoZuID":"","weiShengWu":"","weiShengWuBZ":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.bingQuZDYM | - | String | 病区自定义码
data.chuangWeiHao | - | String | 床位号
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.tingZhiSJ | - | String | 停止时间
data.shouShuMC | - | String | 手术名称
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yaoPinID | - | Integer | 药品ID
data.jiLiang | - | String | 剂量
data.jiXing | - | String | 剂型
data.baoZhuangLiang | - | Integer | 包装量
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.ciShu | - | Integer | 次数
data.qingJieSS | - | String | 清洁手术
data.qingJieSSGWYS | - | String | 清洁手术_高危因素
data.zhiHangFF | - | String | 执行方法
data.qingJieSSSPYSID | - | Integer | 清洁手术_审批医生ID
data.yaoPinMC | - | String | 药品名称
data.kangJunFL | - | String | 抗菌分类
data.qingJieSSSPYJ | - | String | 清洁手术_审批意见
data.qingJieSSSPSJ | - | String | 清洁手术_审批时间
data.touBaoJB | - | String | 头孢级别
data.fuHeKJ | - | String | 符合抗菌
data.shiFouYWCG | - | String | 新生儿预防用药，是否有围产高危因素，1=是，0=否
data.fuKuiNT | - | String | 氟奎诺同
data.weiChanGWYSS | - | String | 新生儿预防用药，围产高危因素
data.qiTaWCGWYS | - | String | 新生儿预防用药，其他围产高危因素
data.shiYongFF | - | String | 使用方法
data.shiFouYLCBX | - | String | 新生儿预防用药，是否有临床表现，1=是，0=否
data.yongYaoFL | - | String | 用药分类
data.linChuangBX | - | String | 新生儿预防用药，临床表现
data.shiQuanSYFL | - | String | 石泉使用分类
data.shiYongYY | - | String | 使用原因
data.yiShengID | - | Integer | 医生ID
data.xiuGaiSJ | - | String | 修改时间
data.zhuanKeID | - | Integer | 专科ID
data.qieKouLB | - | String | 切口类别
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.weiShengWu | - | String | 微生物
data.weiShengWuBZ | - | String | 微生物备注
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/获取感染诊断
```text
抗菌药品管理相关
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getGanRanZD?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"icd":"","id":"","bingLiID":"","leiBie":"","xuHao":"","fuID":"","daiMa":"","mingChen":"","zhuanGuiQK":"","caoZuoZhe":"","caoZuoZheID":"","xiuGaiSJ":"","jiBingID":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.icd | - | String | 
data.id | - | Integer | 
data.bingLiID | - | Integer | 病历ID
data.leiBie | - | String | 类别
data.xuHao | - | Integer | 序号
data.fuID | - | Integer | 父ID
data.daiMa | - | String | 代码
data.mingChen | - | String | 名称
data.zhuanGuiQK | - | String | 转归情况
data.caoZuoZhe | - | String | 操作者
data.caoZuoZheID | - | Integer | 操作者ID
data.xiuGaiSJ | - | String | 修改时间
data.jiBingID | - | Integer | 疾病ID
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/获取华法令调整表
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getHuaFaLingTzb?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"ezyblbrVo":{"idb":"","zhuYuanHao":"","xingMing":"","xingBie":"","chuShengRQ":"","bingQuDM":"","zhuanKeDM":"","chuangWeiHao":"","ruYuanRQ":"","chuYuanRQ":"","geShiDM":"","zhuangTaiBZ":"","caoZuoZhe":"","xiuGaiSJ":"","ruYuanZD":"","menZhenHao":"","bingLiID":"","caoZuoZheID":"","bingQuRYRQ":"","bingQuCYRQ":"","ruYuanJLRQ":"","shouCiJLRQ":"","beiZhu":"","liYuanZT":"","zhuanGuiDW":"","zhuanGuiLY":"","guiDangBZ":"","zhuanKeID":"","riJianSS":"","riJianSSXGRY":"","riJianSSXGSJ":"","siWangSJ":"","ruYuanZKID":"","ruYuanBQID":"","ruYuanCWH":"","bingQuID":"","xinBanBS":"","fuZhuJCZT":"","fuZhuJCSJ":""},"inPatientVo":{"banZuDM":"","bingAnHao":"","bingLiID":"","bingQuChuYuanSJ":"","bingQuID":"","bingQuRuYuanSJ":"","bingRenBH":"","bingRenXM":"","caiWuRuYuanSJ":"","caoZuoZheID":"","chuShengRQ":"","chuShiHuaSJ":"","chuYuanSJ":"","chuangWeiHao":"","danWeiDM":"","danWeiMC":"","guoJiDM":"","h1N1":"","huLiJB":"","huZhao":"","hunYinZK":"","jiBingID":"","jiGuanDM":"","jiaShuDH":"","jiaShuGX":"","jiaShuXM":"","jieSuanDM":"","jieSuanLX":"","jieSuanSJ":"","lianXiDH":"","lianXiDZ":"","minZuDM":"","qiTaZJH":"","riJianSS":"","ruYuanQK":"","ruYuanYQ":"","ruYuanZD":"","shenFenZH":"","shouCiRYSJ":"","xingBie":"","xiuGaiRYID":"","xiuGaiSJ":"","yuZhuYuan":"","zhenDuanDM":"","zhiLiaoZuID":"","zhiYeDM":"","zhongTuJS":"","zhuGuanHSID":"","zhuGuanYSID":"","zhuYuanHao":"","zhuYuanID":"","zhuYuanID_MOTHER":"","zhuanKeID":"","zhuangTaiBZ":"","ziFuYE":""},"ylZyypyzVos":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"huaYanJG":[{"ceShiSJ":"","danWei":"","huaYanJG":"","huaYanJGXMID":"","huaYanJGXMMC":"","shenHeSJ":"","tiShi":"","yangBenHao":""}],"yaoPinBHDZ":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.ezyblbrVo | - | Object | 
data.ezyblbrVo.idb | - | Integer | 
data.ezyblbrVo.zhuYuanHao | - | String | 住院号
data.ezyblbrVo.xingMing | - | String | 姓名
data.ezyblbrVo.xingBie | - | String | 性别
data.ezyblbrVo.chuShengRQ | - | String | 出生日期
data.ezyblbrVo.bingQuDM | - | String | 病区代码
data.ezyblbrVo.zhuanKeDM | - | String | 专科代码
data.ezyblbrVo.chuangWeiHao | - | String | 床位号
data.ezyblbrVo.ruYuanRQ | - | String | 入院日期
data.ezyblbrVo.chuYuanRQ | - | String | 出院日期
data.ezyblbrVo.geShiDM | - | String | 格式代码
data.ezyblbrVo.zhuangTaiBZ | - | String | 状态标志
data.ezyblbrVo.caoZuoZhe | - | String | 操作者
data.ezyblbrVo.xiuGaiSJ | - | String | 修改时间
data.ezyblbrVo.ruYuanZD | - | String | 入院诊断
data.ezyblbrVo.menZhenHao | - | String | 门诊号
data.ezyblbrVo.bingLiID | - | Integer | 病历id
data.ezyblbrVo.caoZuoZheID | - | Integer | 操作者id
data.ezyblbrVo.bingQuRYRQ | - | String | 病区入院日期
data.ezyblbrVo.bingQuCYRQ | - | String | 病区出院日期
data.ezyblbrVo.ruYuanJLRQ | - | String | 入院记录日期
data.ezyblbrVo.shouCiJLRQ | - | String | 首次记录日期
data.ezyblbrVo.beiZhu | - | String | 备注
data.ezyblbrVo.liYuanZT | - | String | 离院状态
data.ezyblbrVo.zhuanGuiDW | - | String | 转归单位
data.ezyblbrVo.zhuanGuiLY | - | String | 转归理由
data.ezyblbrVo.guiDangBZ | - | String | 归档标志
data.ezyblbrVo.zhuanKeID | - | Integer | 专科id
data.ezyblbrVo.riJianSS | - | String | 日间手术 0/null非日间没有进过日间；1日间；2退出日间
data.ezyblbrVo.riJianSSXGRY | - | Integer | 日间手术修改人员
data.ezyblbrVo.riJianSSXGSJ | - | String | 日间手术修改时间
data.ezyblbrVo.siWangSJ | - | String | 死亡时间
data.ezyblbrVo.ruYuanZKID | - | Integer | 入院专科id
data.ezyblbrVo.ruYuanBQID | - | Integer | 入院病区id
data.ezyblbrVo.ruYuanCWH | - | String | 入院床位号
data.ezyblbrVo.bingQuID | - | Integer | 病区id
data.ezyblbrVo.xinBanBS | - | String | 新版标示
data.ezyblbrVo.fuZhuJCZT | - | String | 辅助检查状态
data.ezyblbrVo.fuZhuJCSJ | - | String | 辅助检查时间
data.inPatientVo | - | Object | 
data.inPatientVo.banZuDM | - | String | BZDM
data.inPatientVo.bingAnHao | - | String | 病案号
data.inPatientVo.bingLiID | - | Integer | 病历ID
data.inPatientVo.bingQuChuYuanSJ | - | String | 病区出院时间
data.inPatientVo.bingQuID | - | Integer | 病区ID
data.inPatientVo.bingQuRuYuanSJ | - | String | 病区入院时间
data.inPatientVo.bingRenBH | - | String | 病人编号
data.inPatientVo.bingRenXM | - | String | 病人姓名
data.inPatientVo.caiWuRuYuanSJ | - | String | 财务入院时间
data.inPatientVo.caoZuoZheID | - | Integer | 操作者ID
data.inPatientVo.chuShengRQ | - | String | 出生日期
data.inPatientVo.chuShiHuaSJ | - | String | 初始化时间
data.inPatientVo.chuYuanSJ | - | String | 出院时间
data.inPatientVo.chuangWeiHao | - | String | 床位号
data.inPatientVo.danWeiDM | - | String | 单位代码
data.inPatientVo.danWeiMC | - | String | 单位代码
data.inPatientVo.guoJiDM | - | String | 国籍代码
data.inPatientVo.h1N1 | - | String | 
data.inPatientVo.huLiJB | - | String | 护理级别
data.inPatientVo.huZhao | - | String | 护照
data.inPatientVo.hunYinZK | - | String | 婚姻状况
data.inPatientVo.jiBingID | - | Integer | JBID
data.inPatientVo.jiGuanDM | - | String | 籍贯代码
data.inPatientVo.jiaShuDH | - | String | 家属电话
data.inPatientVo.jiaShuGX | - | String | 家属关系
data.inPatientVo.jiaShuXM | - | String | 家属姓名
data.inPatientVo.jieSuanDM | - | String | 结算代码
data.inPatientVo.jieSuanLX | - | String | 结算类型
data.inPatientVo.jieSuanSJ | - | String | 结算时间
data.inPatientVo.lianXiDH | - | String | 联系电话
data.inPatientVo.lianXiDZ | - | String | 联系地址
data.inPatientVo.minZuDM | - | String | 民族代码
data.inPatientVo.qiTaZJH | - | String | 其他证件号
data.inPatientVo.riJianSS | - | String | 日间病人
data.inPatientVo.ruYuanQK | - | String | 入院情况
data.inPatientVo.ruYuanYQ | - | String | 入院院区
data.inPatientVo.ruYuanZD | - | String | 入院诊断
data.inPatientVo.shenFenZH | - | String | 身份证号
data.inPatientVo.shouCiRYSJ | - | String | 首次入院时间
data.inPatientVo.xingBie | - | String | 病人性别
data.inPatientVo.xiuGaiRYID | - | Integer | 修改人员ID
data.inPatientVo.xiuGaiSJ | - | String | 修改时间
data.inPatientVo.yuZhuYuan | - | String | 预住院
data.inPatientVo.zhenDuanDM | - | String | 诊断代码
data.inPatientVo.zhiLiaoZuID | - | Integer | 治疗组ID
data.inPatientVo.zhiYeDM | - | String | 职业代码
data.inPatientVo.zhongTuJS | - | String | 中途结算
data.inPatientVo.zhuGuanHSID | - | Integer | 主管护士ID
data.inPatientVo.zhuGuanYSID | - | Integer | 主管医师ID
data.inPatientVo.zhuYuanHao | - | String | 住院号
data.inPatientVo.zhuYuanID | - | Integer | 住院ID
data.inPatientVo.zhuYuanID_MOTHER | - | Integer | 妈妈的住院ID
data.inPatientVo.zhuanKeID | - | Integer | 专科ID
data.inPatientVo.zhuangTaiBZ | - | String | 状态标志
data.inPatientVo.ziFuYE | - | Number | 自费余额
data.ylZyypyzVos | - | Array | 药品信息
data.ylZyypyzVos.gcp | - | String | 
data.ylZyypyzVos.yiZhuID | - | Integer | 医嘱ID
data.ylZyypyzVos.bingLiID | - | Integer | 病历ID
data.ylZyypyzVos.zhuYuanID | - | Integer | 住院ID
data.ylZyypyzVos.bingRenXM | - | String | 病人姓名
data.ylZyypyzVos.bingQuID | - | Integer | 病区ID
data.ylZyypyzVos.zhuanKeID | - | Integer | 专科ID
data.ylZyypyzVos.ziFei | - | String | 自费
data.ylZyypyzVos.zuHao | - | Integer | 组号
data.ylZyypyzVos.yaoPinID | - | Integer | 药品ID
data.ylZyypyzVos.fenLeiMa | - | String | 分类码
data.ylZyypyzVos.mingCheng | - | String | 名称
data.ylZyypyzVos.danWei | - | String | 单位
data.ylZyypyzVos.jiXing | - | String | 剂型
data.ylZyypyzVos.jiLiang | - | String | 剂量
data.ylZyypyzVos.baoZhuangLiang | - | Integer | 包装量
data.ylZyypyzVos.shouJia | - | Number | 售价
data.ylZyypyzVos.yiCiYL | - | Number | 一次用量
data.ylZyypyzVos.jiLiangDW | - | String | 剂量单位
data.ylZyypyzVos.zhiXingPL | - | String | 执行频率
data.ylZyypyzVos.zhiXingFF | - | String | 执行方法
data.ylZyypyzVos.geiYaoSJ | - | String | 给药时间
data.ylZyypyzVos.teShuYF | - | String | 特殊用法
data.ylZyypyzVos.kaiShiSJ | - | String | 开始时间
data.ylZyypyzVos.jieShuSJ | - | String | 结束时间
data.ylZyypyzVos.chiXuTS | - | Integer | 持续天数
data.ylZyypyzVos.yiZhuCS | - | Integer | 医嘱次数
data.ylZyypyzVos.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.ylZyypyzVos.yiZhuJB | - | String | 医嘱级别
data.ylZyypyzVos.sheBaoBS | - | String | 社保标识
data.ylZyypyzVos.huLiZT | - | String | 护理状态
data.ylZyypyzVos.yiZhuZT | - | String | 医嘱状态
data.ylZyypyzVos.yiShengID | - | Integer | 医生ID
data.ylZyypyzVos.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.ylZyypyzVos.shouCiLRSJ | - | String | 首次录入时间
data.ylZyypyzVos.xiuGaiYSID | - | Integer | 修改医生ID
data.ylZyypyzVos.yaoFangDM | - | String | 药房代码
data.ylZyypyzVos.quYaoFS | - | String | 取药方式
data.ylZyypyzVos.shouFeiSL | - | Number | 收费数量
data.ylZyypyzVos.shouFeiCS | - | Integer | 收费次数
data.ylZyypyzVos.buFeiZL | - | Number | 补费总量
data.ylZyypyzVos.changQiBY | - | Number | 长期补药
data.ylZyypyzVos.shouFeiRYID | - | Integer | 收费人员ID
data.ylZyypyzVos.shouFeiSJ | - | String | 收费时间
data.ylZyypyzVos.weiShouBZ | - | String | 未收标志
data.ylZyypyzVos.bingQuBZ | - | String | 病区备注
data.ylZyypyzVos.daoRuRYID | - | Integer | 导入人员ID
data.ylZyypyzVos.daoRuSJ | - | String | 导入时间
data.ylZyypyzVos.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.ylZyypyzVos.tingZhiDRSJ | - | String | 停止导入时间
data.ylZyypyzVos.zhiXingJLSJ | - | String | 执行记录时间
data.ylZyypyzVos.xiuGaiRYID | - | Integer | 修改人员ID
data.ylZyypyzVos.xiuGaiSJ | - | String | 修改时间
data.ylZyypyzVos.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.ylZyypyzVos.tiZaoQYTS | - | Integer | 提早取药天数
data.ylZyypyzVos.zhiLiaoZuID | - | Integer | 治疗组ID
data.ylZyypyzVos.piShiJG | - | String | 皮试结果
data.ylZyypyzVos.zhiXingRYID | - | Integer | 执行人员ID
data.ylZyypyzVos.shenHeRYID | - | Integer | 审核人员ID
data.ylZyypyzVos.cheXiaoZT | - | String | 撤销状态
data.ylZyypyzVos.shenHeYSID | - | Integer | 审核医生ID
data.ylZyypyzVos.yiZhuSHSJ | - | String | 医嘱审核时间
data.ylZyypyzVos.piShiSJ | - | String | 皮试时间
data.ylZyypyzVos.yiZhuBZ | - | String | 医嘱备注
data.ylZyypyzVos.tongZhiDanID | - | Integer | 通知单ID
data.ylZyypyzVos.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.ylZyypyzVos.shiFouZB | - | String | 是否自备
data.ylZyypyzVos.shiFouBL | - | String | 是否补录
data.ylZyypyzVos.mianPiShi | - | String | 免皮试
data.ylZyypyzVos.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.ylZyypyzVos.yongYaoTS | - | Integer | 用药天数
data.ylZyypyzVos.xiangMuBM | - | String | 项目编码
data.ylZyypyzVos.xiangMuLX | - | String | 项目类型
data.ylZyypyzVos.shuangQianMingYHID | - | Integer | 双签名用户ID
data.ylZyypyzVos.shuangQianMingSJ | - | String | 双签名时间
data.ylZyypyzVos.shuangQianMingBZ | - | String | 双签名备注
data.ylZyypyzVos.shenQingDanSJID | - | Integer | 申请单数据ID
data.ylZyypyzVos.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.ylZyypyzVos.shenFangCLJG | - | String | 审方处理结果
data.ylZyypyzVos.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.ylZyypyzVos.jinRiCY | - | String | 今日出院
data.ylZyypyzVos.zanShiBQ | - | String | 暂时不取
data.ylZyypyzVos.piShiGCSJ | - | String | 皮试观察时间
data.ylZyypyzVos.piShiGCYHID | - | Integer | 皮试观察用户ID
data.ylZyypyzVos.yaoPinDS | - | Number | 药品滴速
data.ylZyypyzVos.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.ylZyypyzVos.linShiTL | - | Number | 临时调量
data.ylZyypyzVos.changQiBF | - | String | 长期调量
data.ylZyypyzVos.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.ylZyypyzVos.zhongChengYaoZD | - | String | 中成药诊断
data.ylZyypyzVos.zhongChengYaoZZ | - | String | 中成药症状
data.ylZyypyzVos.yaoPinDSBZ | - | String | 药品滴速备注
data.ylZyypyzVos.waiGouYPSQID | - | Integer | 外购药品申请ID
data.ylZyypyzVos.laiYuan | - | String | 来源
data.ylZyypyzVos.shenFangJK | - | String | 审方接口
data.ylZyypyzVos.maZuiYT | - | String | 麻醉用途
data.ylZyypyzVos.xianDingFW | - | String | 限定范围
data.ylZyypyzVos.geiYaoTJGLID | - | Integer | 给药途径关联id
data.ylZyypyzVos.mingCheng2 | - | String | 名称2
data.ylZyypyzVos.jiGouDM | - | String | 机构代码
data.ylZyypyzVos.jinRiLT | - | String | 今日临停
data.ylZyypyzVos.tingZhiLX | - | String | 停止类型
data.huaYanJG | - | Array | 化验结果
data.huaYanJG.ceShiSJ | - | String | 
data.huaYanJG.danWei | - | String | 
data.huaYanJG.huaYanJG | - | String | 
data.huaYanJG.huaYanJGXMID | - | String | 
data.huaYanJG.huaYanJGXMMC | - | String | 
data.huaYanJG.shenHeSJ | - | String | 
data.huaYanJG.tiShi | - | String | 
data.huaYanJG.yangBenHao | - | String | 
data.yaoPinBHDZ | - | String | 药品闭环地址
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/获取抗菌药品管理
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getKangJunYpGl

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","yaoPinID":"","zhiLiaoSYYPIDList":[{}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yaoPinID | - | Integer | 否 | 药品ID
zhiLiaoSYYPIDList | - | Array | 否 | 治疗使用药品ID列表
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"piShiYPLX":"","shiYongFF":[{"daiMa":"","mingCheng":""}],"qieKouLB":[{"daiMa":"","mingCheng":""}],"yongFaFL":[{"daiMa":"","mingCheng":""}],"shuQianSY":[{"daiMa":"","mingCheng":""}],"weiShengWuSJ":"","lianHeKJ":"","touBaoPSJG":"","zhuanKeLB":"","ganRanZK":"","yuFangQS":"","yuFangMZ":"","isZhuanKeYF":"","zhuanKeYF":"","shouShuJSSJ":"","drugAttributesVos":[{"beiZhu":"","shuXingDM":"","shuXingZhi":"","yaoPinID":""}],"yaoPinFJSXDZ":[{"daiMa":"","mingCheng":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.piShiYPLX | - | String | 
data.shiYongFF | - | Array | 使用方法(选项)
data.shiYongFF.daiMa | - | String | 代码
data.shiYongFF.mingCheng | - | String | 名称
data.qieKouLB | - | Array | 切口类别(选项)
data.qieKouLB.daiMa | - | String | 代码
data.qieKouLB.mingCheng | - | String | 名称
data.yongFaFL | - | Array | 用法分类(选项)
data.yongFaFL.daiMa | - | String | 代码
data.yongFaFL.mingCheng | - | String | 名称
data.shuQianSY | - | Array | 术前使用(选项)
data.shuQianSY.daiMa | - | String | 代码
data.shuQianSY.mingCheng | - | String | 名称
data.weiShengWuSJ | - | String | 微生物送检(1=已送检，0=未送检)
data.lianHeKJ | - | String | 联合抗菌(1=是，0=否)
data.touBaoPSJG | - | String | 头孢皮试结果(+=阳性，-=阴性)
data.zhuanKeLB | - | String | 专科类别(1=内科，2=外科，3=通科)
data.ganRanZK | - | String | 感染专科(1=是，0=否)
data.yuFangQS | - | String | 该专科是否可以预防使用头孢曲松(0不可用，1可用)
data.yuFangMZ | - | String | 该专科是否可以预防使用头孢美唑(0不可用，1可用)
data.isZhuanKeYF | - | String | 专科预防判断走另一个分支(1=是)
data.zhuanKeYF | - | String | 专科预防判断能不能开具(空=可用，不为空=提示内容)
data.shouShuJSSJ | - | String | 手术结束时间
data.drugAttributesVos | - | Array | 药品附加属性
data.drugAttributesVos.beiZhu | - | String | 备注
data.drugAttributesVos.shuXingDM | - | String | 属性代码
data.drugAttributesVos.shuXingZhi | - | String | 属性值
data.drugAttributesVos.yaoPinID | - | Integer | 药品id
data.yaoPinFJSXDZ | - | Array | 药品附加属性对照
data.yaoPinFJSXDZ.daiMa | - | String | 代码
data.yaoPinFJSXDZ.mingCheng | - | String | 名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_麻醉用途
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getMaZuiYT

#### 请求方式
> GET

#### Content-Type
> none

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yingYongDM":"","leiBie":"","daiMa":"","mingCheng":"","pinYin":"","wuBi":"","beiZhu":"","paiXu":"","daiMaCD":"","mingChengCD":"","fuJiaSX":"","neiBuLB":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","caoZuoZheID":"","xiuGaiSJ":"","nengFouXG":"","yiLiaoJGDM":"","caoZuoZhe":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yingYongDM | - | String | 应用代码
data.leiBie | - | String | 类别
data.daiMa | - | String | 代码
data.mingCheng | - | String | 名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
data.beiZhu | - | String | 备注
data.paiXu | - | Integer | 排序
data.daiMaCD | - | Integer | 代码长度
data.mingChengCD | - | Integer | 名称长度
data.fuJiaSX | - | String | 附加属性
data.neiBuLB | - | String | 内部类别
data.zhuangTaiBZ | - | String | 状态标志
data.zhuangTaiBZMC | - | String | 状态标志名称
data.caoZuoZheID | - | Integer | 操作者ID
data.xiuGaiSJ | - | String | 修改时间
data.nengFouXG | - | Integer | 能否修改
data.yiLiaoJGDM | - | String | 医疗机构代码
data.caoZuoZhe | - | String | 操作者
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/根据病历ID列表查询耐药菌感染标志信息
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getMdroInpatientList

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"bingLiIdList":[{}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiIdList | - | Array | 否 | 病历ID列表
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"bingLiID":"","mdroBiaoZhi":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.bingLiID | - | Integer | 病历ID
data.mdroBiaoZhi | - | String | MDRO标志
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_新外购药品
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getNewWaiGouYP?key=&yaoFangDMs=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
key | - | Text | 是 | 搜索关键字
yaoFangDMs | - | Text | 是 | 药房代码列表，以逗号隔开
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yaoFangDM":"","zhuShe":"","zhuanKeMC":"","id":"","zuHao":"","yiZhuLB":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","jiLiang":"","baoZhuangLiang":"","shuLiang":"","ciShu":"","yiCiYL":"","pinLv":"","fangFa":"","geiYaoSJ":"","teShuYF":"","yiShengYHID":"","leiBie":"","xiuGaiSJ":"","paiXu":"","jiLiangDW":"","muBanMC":"","pinYin":"","wuBi":"","zhuanKeID":"","shenHeZT":"","shenHeRYID":"","shsj":"","beiZhu":"","zhuanHuanYZLB":"","leiXing":"","mrcs":"","jiaShuiLiang":"","cypl":"","fyff":"","fenZhuangQYPID":"","zhuangTaiBZ":"","caoZuoZhe":"","shenHeRYXM":"","chiXuTS":"","kaiShiSJ":"","danWei":"","guiGe":"","jiXing":"","shouJia":"","yaoPinLB":"","sheBaoDM":"","maZuiYaopin":"","guanLiLX":"","kongZhiJB":"","chanDiMC":"","guoTanYPMC":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yaoFangDM | - | String | 药房代码
data.zhuShe | - | String | 
data.zhuanKeMC | - | String | 
data.id | - | Integer | 模板ID
data.zuHao | - | Integer | 组号
data.yiZhuLB | - | String | 医嘱类别
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 药品名称
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shuLiang | - | Integer | 数量
data.ciShu | - | Integer | 次数
data.yiCiYL | - | Number | 一次用量
data.pinLv | - | String | 频率
data.fangFa | - | String | 方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.yiShengYHID | - | Integer | 医生ID
data.leiBie | - | String | 类别
data.xiuGaiSJ | - | String | 修改时间
data.paiXu | - | Integer | 排序
data.jiLiangDW | - | String | 剂量单位
data.muBanMC | - | String | 模板名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
data.zhuanKeID | - | Integer | 专科ID
data.shenHeZT | - | String | 审核状态
data.shenHeRYID | - | Integer | 审核人员ID
data.shsj | - | String | 审核时间
data.beiZhu | - | String | 备注
data.zhuanHuanYZLB | - | String | 转换医嘱类别
data.leiXing | - | String | 类型
data.mrcs | - | Integer | 每日次数
data.jiaShuiLiang | - | String | 加水量
data.cypl | - | String | 草药频率
data.fyff | - | String | 使用方法
data.fenZhuangQYPID | - | Integer | 分装前药品ID
data.zhuangTaiBZ | - | String | 状态标志（0暂停 1普通）
data.caoZuoZhe | - | String | 操作者
data.shenHeRYXM | - | String | 审核人员姓名
data.chiXuTS | - | Integer | 持续天数
data.kaiShiSJ | - | String | 开始时间
data.danWei | - | String | 单位
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.shouJia | - | Number | 售价
data.yaoPinLB | - | String | 药品类别(1=外购药，2=赠送药品)
data.sheBaoDM | - | String | 社保代码
data.maZuiYaopin | - | String | 麻醉药品
data.guanLiLX | - | String | 管理类型
data.kongZhiJB | - | String | 控制级别
data.chanDiMC | - | String | 产地名称（厂家信息）
data.guoTanYPMC | - | String | 国谈药品名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_根据省份代码获取省
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getProvinceByShengFenDM?shengFenDM=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
shengFenDM | - | Text | 是 | 省份代码
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"jiGuanDM":"","jiGuanMC":"","pinYin":"","wuBi":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.jiGuanDM | - | String | 籍贯代码
data.jiGuanMC | - | String | 籍贯名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/是否一类手术
```text
抗菌药品管理相关
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getShiFouYLSS?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"chuShouSJSJ":"","jinShouSJSJ":"","shiShiSS":[{"icd":"","tongZhiDanID":"","zhuShouShu":"","daiMa":"","mingCheng":"","shouShuLB":"","guanLiJB":"","shouShuBW":"","caoZuoZheID":"","xiuGaiSJ":"","shouShuDJ":""}],"leiBie":"","tongZhiDID":"","bingAnHao":"","bingRenBH":"","zhuYuanHao":"","zhuYuanID":"","bingLiID":"","bingQuID":"","bingQuMC":"","zhuDaoZKID":"","zhuanKeID":"","zhuanKeMC":"","bingRenXM":"","bingRenXB":"","bingRenXBMC":"","chuShengRQ":"","chuangWeiHao":"","chuangWeiHao2":"","nianLing":"","nianLing2":"","zhiYeDM":"","zhiYeMC":"","hunYinZK":"","hunYinZKMC":"","lianXiDH":"","lianXiDZ":"","linChuangZD":"","zhuanKeXZ":"","xiaoZuTX":"","shouShuYYSJ":"","yuJiSSSC":"","niShouShuSJ":"","zhuDaoYSID":"","zhuDaoYSXM":"","diErZSID":"","diYiZSID":"","diErZSXM":"","diSanZSID":"","diSanZSXM":"","diSiZSID":"","diSiZSXM":"","diYiZSXM":"","taiShangZDID":"","taiShangZDXM":"","shiFouJZ":"","shiFouJZMC":"","shiFouMZ":"","kaiDanYSID":"","kaiDanYSXM":"","kaiDanSJ":"","shenPiYSID":"","shenPiYSXM":"","shenPiSJ":"","shenPiYJ":"","shenPiYJMC":"","shouShuJianMC":"","shouShuShiDM":"","shouShuShiMC":"","shouShuJianDM":"","taiXu":"","shouShuJB":"","shouShuJBMC":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","qieKouLB":"","menZhenSSLX":"","menZhenSSLXMC":"","shouShouJL":"","canGuanZhe":"","shouShuXM":[{"icd":"","tongZhiDID":"","zhuShouShu":"","shouShuDM":"","kaiDanYSID":"","shouShuMC":"","shangXiaWu":"","shouShuJB":"","shouShuJBMC":"","guanLiJB":"","shouShuBW":"","caoZuoZheID":"","xiuGaiSJ":""}],"tongZhiSJ":"","ganRanCZ1ID":"","ganRanCZ1XM":"","huaYanXM":[{"huaYanDM":"","tongZhiDanID":"","huaYanMC":"","huaYanJG":"","huaYanSJ":"","leiBie":""}],"kaiDanBZ":"","maZuiHZ":"","maZuiYS1ID":"","maZuiYS1XM":"","maZuiYS2ID":"","maZuiYS2XM":"","maZuiYS3ID":"","maZuiYS3XM":"","qiXieHS1ID":"","qiXieHS1XM":"","qiXieHS2ID":"","qiXieHS2XM":"","shouShuJSSJ":"","shouShuKSSJ":"","xunHuiHS1ID":"","xunHuiHS1XM":"","xunHuiHS2ID":"","xunHuiHS2XM":"","xunHuiHS3ID":"","xunHuiHS3XM":"","xunHuiHS4ID":"","xunHuiHS4XM":"","yuJiSSDDSJ":"","zhiLiaoZuID":"","zhiLiaoZuMC":"","menZhenSS":"","qiTaSXs":[{"tongZhiDanID":"","shuXingDM":"","shuXingZhi":"","caoZuoZheID":"","caoZuoZheXM":"","xiuGaiSJ":""}],"shuQianCLSQ":[{"shenQingJLID":"","tongZhiDanID":"","xuHao":"","erJiFLM":"","shenQingLX":"","pinZhongID":"","shuLiang":"","chanDi":"","zhuangTaiBZ":"","caoZuoZheID":"","xiuGaiSJ":""}],"youXianJB":"","erCiSP":{"tongZhiDID":"","shenPiZT":"","shenPiZTMC":"","beiZhu":"","shenPiRYID":"","shenPiSJ":""},"xinJiShuXXM":"","kaiDanYQ":"","shuZhongHZ":[{"tongZhiDanID":"","renYuanKuID":"","xingMing":"","caoZuoZheID":"","xiuGaiSJ":"","jiLuID":""}],"biaoBen":"","biaoBenMC":"","geLiZL":"","chuanRanBingYXZB":"","riJianSS":"","teShuSSTW":"","xuYaoJS":"","bingDongQP":"","biHuanDZ":"","genTaiRY":[{"tongZhiDID":"","shuXingDM":"","shuXingZhi":"","caoZuoZID":"","xiuGaiSJ":"","beiZhu":"","bingQuID":"","zhuanKeID":"","chuangWeiHao":"","xuHao":"","beiZhu2":""}],"shouShuHCURL":"","shuQianTLWSID":"","shuQianTLWSMC":"","zhiQingTYSWSID":"","zhiQingTYSWSMC":"","ziTiXueHS":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.chuShouSJSJ | - | String | 出手术间时间
data.jinShouSJSJ | - | String | 进手术间时间
data.shiShiSS | - | Array | 实施手术
data.shiShiSS.icd | - | String | 
data.shiShiSS.tongZhiDanID | - | Integer | 通知单ID
data.shiShiSS.zhuShouShu | - | String | 主手术
data.shiShiSS.daiMa | - | String | 代码
data.shiShiSS.mingCheng | - | String | 名称
data.shiShiSS.shouShuLB | - | String | 手术类别
data.shiShiSS.guanLiJB | - | String | 管理级别
data.shiShiSS.shouShuBW | - | String | 手术部位
data.shiShiSS.caoZuoZheID | - | Integer | 操作者ID
data.shiShiSS.xiuGaiSJ | - | String | 修改时间
data.shiShiSS.shouShuDJ | - | String | 手术登记
data.leiBie | - | String | 类别（0=普通，1=心导管，2=外周导管）
data.tongZhiDID | - | Integer | 通知单ID
data.bingAnHao | - | String | 病案号
data.bingRenBH | - | String | 病人编号
data.zhuYuanHao | - | String | 住院号
data.zhuYuanID | - | Integer | 住院ID
data.bingLiID | - | Integer | 病历id  门诊记诊疗活动ID
data.bingQuID | - | Integer | 病区ID
data.bingQuMC | - | String | 病区名称
data.zhuDaoZKID | - | Integer | 主刀专科id
data.zhuanKeID | - | Integer | 专科id
data.zhuanKeMC | - | String | 专科名称
data.bingRenXM | - | String | 病人姓名
data.bingRenXB | - | String | 病人性别
data.bingRenXBMC | - | String | 病人性别名称
data.chuShengRQ | - | String | 出生日期
data.chuangWeiHao | - | String | 床位号（例：001）
data.chuangWeiHao2 | - | String | 床位号2（例：001床）
data.nianLing | - | Integer | 年龄（例：18）
data.nianLing2 | - | String | 年龄2（例：18岁）
data.zhiYeDM | - | String | 职业代码
data.zhiYeMC | - | String | 职业名称
data.hunYinZK | - | String | 婚姻状况
data.hunYinZKMC | - | String | 婚姻状况名称
data.lianXiDH | - | String | 联系电话
data.lianXiDZ | - | String | 联系地址
data.linChuangZD | - | String | 临床诊断
data.zhuanKeXZ | - | String | 专科小组
data.xiaoZuTX | - | Integer | 小组台序
data.shouShuYYSJ | - | String | 手术预约时间
data.yuJiSSSC | - | Integer | 预计时长
data.niShouShuSJ | - | String | 拟手术时间
data.zhuDaoYSID | - | Integer | 主刀医生ID
data.zhuDaoYSXM | - | String | 主刀医生姓名
data.diErZSID | - | Integer | 第二助手ID
data.diYiZSID | - | Integer | 第一助手ID
data.diErZSXM | - | String | 第一助手姓名
data.diSanZSID | - | Integer | 第三助手ID
data.diSanZSXM | - | String | 第三助手姓名
data.diSiZSID | - | Integer | 第四助手ID
data.diSiZSXM | - | String | 第四助手姓名
data.diYiZSXM | - | String | 第二助手姓名
data.taiShangZDID | - | Integer | 台上指导ID
data.taiShangZDXM | - | String | 台上指导姓名
data.shiFouJZ | - | Boolean | 是否急诊
data.shiFouJZMC | - | String | 是否急诊名称（急诊、平诊）
data.shiFouMZ | - | String | 是否麻醉
data.kaiDanYSID | - | Integer | 开单医生ID
data.kaiDanYSXM | - | String | 开单医生姓名
data.kaiDanSJ | - | String | 开单时间
data.shenPiYSID | - | Integer | 审批医生ID
data.shenPiYSXM | - | String | 审批医生姓名
data.shenPiSJ | - | String | 审批时间
data.shenPiYJ | - | String | 审批意见
data.shenPiYJMC | - | String | 审批意见名称
data.shouShuJianMC | - | String | 手术间名称
data.shouShuShiDM | - | String | 手术室代码
data.shouShuShiMC | - | String | 手术室名称
data.shouShuJianDM | - | String | 手术间
data.taiXu | - | Integer | 台序
data.shouShuJB | - | String | 手术级别
data.shouShuJBMC | - | String | 手术级别名称
data.zhuangTaiBZ | - | String | 状态标志
data.zhuangTaiBZMC | - | String | 状态标志名称
data.qieKouLB | - | String | 切口类别
data.menZhenSSLX | - | String | 门诊手术类型
data.menZhenSSLXMC | - | String | 门诊手术类型名称
data.shouShouJL | - | String | 门诊手术记录
data.canGuanZhe | - | String | 参观者
data.shouShuXM | - | Array | 门诊手术项目信息
data.shouShuXM.icd | - | String | 
data.shouShuXM.tongZhiDID | - | Integer | 通知单ID
data.shouShuXM.zhuShouShu | - | String | 主手术
data.shouShuXM.shouShuDM | - | String | 手术代码
data.shouShuXM.kaiDanYSID | - | Integer | 开单医生ID
data.shouShuXM.shouShuMC | - | String | 手术名称
data.shouShuXM.shangXiaWu | - | String | 上下午
data.shouShuXM.shouShuJB | - | String | 手术级别
data.shouShuXM.shouShuJBMC | - | String | 手术级别名称
data.shouShuXM.guanLiJB | - | String | 管理级别
data.shouShuXM.shouShuBW | - | String | 手术部位
data.shouShuXM.caoZuoZheID | - | String | 操作者ID
data.shouShuXM.xiuGaiSJ | - | String | 修改时间
data.tongZhiSJ | - | String | 通知患者时间
data.ganRanCZ1ID | - | Integer | 感染处置1ID
data.ganRanCZ1XM | - | String | 感染处置1姓名
data.huaYanXM | - | Array | 化验项目
data.huaYanXM.huaYanDM | - | String | 化验代码
data.huaYanXM.tongZhiDanID | - | Integer | 通知单ID
data.huaYanXM.huaYanMC | - | String | 化验名称
data.huaYanXM.huaYanJG | - | String | 化验结果
data.huaYanXM.huaYanSJ | - | String | 化验时间
data.huaYanXM.leiBie | - | String | 类别
data.kaiDanBZ | - | String | 开单备注
data.maZuiHZ | - | String | 麻醉会诊
data.maZuiYS1ID | - | Integer | 麻醉医师1ID
data.maZuiYS1XM | - | String | 麻醉医师1姓名
data.maZuiYS2ID | - | Integer | 麻醉医师2ID
data.maZuiYS2XM | - | String | 麻醉医师2姓名
data.maZuiYS3ID | - | Integer | 麻醉医师3ID
data.maZuiYS3XM | - | String | 麻醉医师3姓名
data.qiXieHS1ID | - | Integer | 器械护士1ID
data.qiXieHS1XM | - | String | 器械护士1姓名
data.qiXieHS2ID | - | Integer | 器械护士2ID
data.qiXieHS2XM | - | String | 器械护士2姓名
data.shouShuJSSJ | - | String | 手术结束时间
data.shouShuKSSJ | - | String | 手术开始时间
data.xunHuiHS1ID | - | Integer | 巡回护士1ID
data.xunHuiHS1XM | - | String | 巡回护士1姓名
data.xunHuiHS2ID | - | Integer | 巡回护士2ID
data.xunHuiHS2XM | - | String | 巡回护士2姓名
data.xunHuiHS3ID | - | Integer | 巡回护士3ID
data.xunHuiHS3XM | - | String | 巡回护士3姓名
data.xunHuiHS4ID | - | Integer | 巡回护士4ID
data.xunHuiHS4XM | - | String | 巡回护士4姓名
data.yuJiSSDDSJ | - | String | 预计手术等待时间
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.zhiLiaoZuMC | - | String | 治疗组名称
data.menZhenSS | - | String | 是否为门诊手术，1=是，0=否
data.qiTaSXs | - | Array | 其他属性列表
data.qiTaSXs.tongZhiDanID | - | Integer | 通知单ID
data.qiTaSXs.shuXingDM | - | String | 属性代码（01=气压治疗，ZD=重大手术，FJ=非计划再次手术，KY=科研标识）
data.qiTaSXs.shuXingZhi | - | String | 属性值（1=是，0=否）
data.qiTaSXs.caoZuoZheID | - | Integer | 操作者ID
data.qiTaSXs.caoZuoZheXM | - | String | 操作者姓名
data.qiTaSXs.xiuGaiSJ | - | String | 修改时间
data.shuQianCLSQ | - | Array | 术前材料申请
data.shuQianCLSQ.shenQingJLID | - | Integer | 申请记录ID
data.shuQianCLSQ.tongZhiDanID | - | Integer | 通知单ID
data.shuQianCLSQ.xuHao | - | Integer | 序号
data.shuQianCLSQ.erJiFLM | - | String | 二级分类码
data.shuQianCLSQ.shenQingLX | - | String | 申请类型
data.shuQianCLSQ.pinZhongID | - | Integer | 品种ID
data.shuQianCLSQ.shuLiang | - | Integer | 数量
data.shuQianCLSQ.chanDi | - | Integer | 产地
data.shuQianCLSQ.zhuangTaiBZ | - | String | 状态标志
data.shuQianCLSQ.caoZuoZheID | - | Integer | 操作者ID
data.shuQianCLSQ.xiuGaiSJ | - | String | 修改时间
data.youXianJB | - | String | 优先级别
data.erCiSP | - | Object | 
data.erCiSP.tongZhiDID | - | Integer | 通知单ID
data.erCiSP.shenPiZT | - | String | 审批状态（1=同意，0=不同意）
data.erCiSP.shenPiZTMC | - | String | 审批状态名称（1=同意，0=不同意）
data.erCiSP.beiZhu | - | String | 备注
data.erCiSP.shenPiRYID | - | Integer | 审批人员ID
data.erCiSP.shenPiSJ | - | String | 审批时间
data.xinJiShuXXM | - | String | 新技术新项目
data.kaiDanYQ | - | String | 开单院区
data.shuZhongHZ | - | Array | 术中会诊
data.shuZhongHZ.tongZhiDanID | - | Integer | 通知单ID
data.shuZhongHZ.renYuanKuID | - | Integer | 人员库ID
data.shuZhongHZ.xingMing | - | String | 姓名
data.shuZhongHZ.caoZuoZheID | - | Integer | 操作者ID
data.shuZhongHZ.xiuGaiSJ | - | String | 修改时间
data.shuZhongHZ.jiLuID | - | Integer | 记录ID
data.biaoBen | - | String | 是否有标本（1=有，0=无）
data.biaoBenMC | - | String | 预计标本名称
data.geLiZL | - | String | 隔离种类
data.chuanRanBingYXZB | - | String | 传染病阳性指标
data.riJianSS | - | String | 日间手术
data.teShuSSTW | - | String | 特殊手术体位
data.xuYaoJS | - | String | 需要技师
data.bingDongQP | - | String | 冰冻切片
data.biHuanDZ | - | String | 闭环地址
data.genTaiRY | - | Array | 跟台人员
data.genTaiRY.tongZhiDID | - | Integer | 通知单ID
data.genTaiRY.shuXingDM | - | String | 属性代码
data.genTaiRY.shuXingZhi | - | String | 属性值
data.genTaiRY.caoZuoZID | - | Integer | 操作者ID
data.genTaiRY.xiuGaiSJ | - | String | 修改时间
data.genTaiRY.beiZhu | - | String | 备注
data.genTaiRY.bingQuID | - | Integer | 病区ID
data.genTaiRY.zhuanKeID | - | Integer | 专科ID
data.genTaiRY.chuangWeiHao | - | String | 床位号
data.genTaiRY.xuHao | - | Integer | 序号
data.genTaiRY.beiZhu2 | - | String | 备注2
data.shouShuHCURL | - | String | 手术耗材URL（SPD）
data.shuQianTLWSID | - | Integer | 术前讨论文书ID
data.shuQianTLWSMC | - | String | 术前讨论文书名称
data.zhiQingTYSWSID | - | Integer | 知情同意书文书ID
data.zhiQingTYSWSMC | - | String | 知情同意书文书名称
data.ziTiXueHS | - | String | 自体血回输（1=是，0=否）
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/时间针提醒
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getShiJianZhenTX

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"id":"","bingAnHao":"","yiZhuID":"","zuHao":"","bingLiID":"","zuHaoTXT":"","bingRenXM":"","shiJianDian":"","bingQuID":"","bingQuZDYM":"","chuangWeiHao":"","zhuangTaiBZ":"","xiuGaiSJ":"","zhuanKeID":"","caoZuoZheID":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","zhiXingPL2":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.id | - | Integer | 
data.bingAnHao | - | String | 病案号
data.yiZhuID | - | Integer | 医嘱ID
data.zuHao | - | Integer | 组号
data.bingLiID | - | Integer | 病历ID
data.zuHaoTXT | - | String | 组号TXT
data.bingRenXM | - | String | 病人姓名
data.shiJianDian | - | String | 时间点
data.bingQuID | - | Integer | 病区ID
data.bingQuZDYM | - | String | 病区自定义码
data.chuangWeiHao | - | String | 床位号
data.zhuangTaiBZ | - | String | 状态标志(1=有效，0=无效，9=医生删除)
data.xiuGaiSJ | - | String | 修改时间
data.zhuanKeID | - | Integer | 专科ID
data.caoZuoZheID | - | Integer | 操作者ID
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/获取时间针医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getShiJianZhenYz

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"id":"","bingAnHao":"","yiZhuID":"","zuHao":"","bingLiID":"","zuHaoTXT":"","bingRenXM":"","shiJianDian":"","bingQuID":"","bingQuZDYM":"","chuangWeiHao":"","zhuangTaiBZ":"","xiuGaiSJ":"","zhuanKeID":"","caoZuoZheID":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","zhiXingPL2":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.id | - | Integer | 
data.bingAnHao | - | String | 病案号
data.yiZhuID | - | Integer | 医嘱ID
data.zuHao | - | Integer | 组号
data.bingLiID | - | Integer | 病历ID
data.zuHaoTXT | - | String | 组号TXT
data.bingRenXM | - | String | 病人姓名
data.shiJianDian | - | String | 时间点
data.bingQuID | - | Integer | 病区ID
data.bingQuZDYM | - | String | 病区自定义码
data.chuangWeiHao | - | String | 床位号
data.zhuangTaiBZ | - | String | 状态标志(1=有效，0=无效，9=医生删除)
data.xiuGaiSJ | - | String | 修改时间
data.zhuanKeID | - | Integer | 专科ID
data.caoZuoZheID | - | Integer | 操作者ID
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/医嘱按时间间隔统计
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getTongJiYzBySjjg?jieShuSJ=&kaiShiSJ=&leiXing=&pageIndex=1&pageSize=10&shiJianJG=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
jieShuSJ | - | Text | 是 | 结束时间(yyyy-MM-dd)
kaiShiSJ | - | Text | 是 | 开始时间(yyyy-MM-dd)
leiXing | - | Text | 是 | 类型(yp=药品,zl=治疗)
pageIndex | 1 | Text | 否 | 当前页码
pageSize | 10 | Text | 否 | 每页条数
shiJianJG | - | Text | 是 | 时间间隔(分钟)
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"bingAnHao":"","bingLiID":"","yiZhuID":"","luRuSJ":"","shouCiLRSJ":"","yiZhuMC":"","jianGeSJ":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.bingAnHao | - | String | 病案号
data.bingLiID | - | Integer | 病历ID
data.yiZhuID | - | Integer | 医嘱ID
data.luRuSJ | - | String | 录入时间
data.shouCiLRSJ | - | String | 首次录入时间
data.yiZhuMC | - | String | 医嘱名称
data.jianGeSJ | - | Integer | 间隔时间(分钟)
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询微生物无样可采理由(病案号)
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getWeiShengWuWykclyByBah?bingAnHao=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingAnHao | - | Text | 是 | 病案号
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yiZhuID":"","bingLiID":"","zhuYuanID":"","tingZhiSJ":"","shouShuMC":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yaoPinID":"","jiLiang":"","jiXing":"","baoZhuangLiang":"","yiCiYL":"","jiLiangDW":"","ciShu":"","qingJieSS":"","qingJieSSGWYS":"","zhiHangFF":"","qingJieSSSPYSID":"","yaoPinMC":"","kangJunFL":"","qingJieSSSPYJ":"","qingJieSSSPSJ":"","touBaoJB":"","fuHeKJ":"","shiFouYWCG":"","fuKuiNT":"","weiChanGWYSS":"","qiTaWCGWYS":"","shiYongFF":"","shiFouYLCBX":"","yongYaoFL":"","linChuangBX":"","shiQuanSYFL":"","shiYongYY":"","bingAnHao":"","bingRenXM":"","chuangWeiHao":"","nianLing":"","yiShengID":"","yiShengXM":"","xiuGaiSJ":"","bingQuID":"","bingQuZDYM":"","ganRanZD":"","wuYangKCLY":"","zhuanKeID":"","zhuanKeMC":"","qieKouLB":"","zhiLiaoZuID":"","weiShengWu":"","weiShengWuBZ":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.tingZhiSJ | - | String | 停止时间
data.shouShuMC | - | String | 手术名称
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yaoPinID | - | Integer | 药品ID
data.jiLiang | - | String | 剂量
data.jiXing | - | String | 剂型
data.baoZhuangLiang | - | Integer | 包装量
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.ciShu | - | Integer | 次数
data.qingJieSS | - | String | 清洁手术
data.qingJieSSGWYS | - | String | 清洁手术_高危因素
data.zhiHangFF | - | String | 执行方法
data.qingJieSSSPYSID | - | Integer | 清洁手术_审批医生ID
data.yaoPinMC | - | String | 药品名称
data.kangJunFL | - | String | 抗菌分类
data.qingJieSSSPYJ | - | String | 清洁手术_审批意见
data.qingJieSSSPSJ | - | String | 清洁手术_审批时间
data.touBaoJB | - | String | 头孢级别
data.fuHeKJ | - | String | 符合抗菌
data.shiFouYWCG | - | String | 新生儿预防用药，是否有围产高危因素，1=是，0=否
data.fuKuiNT | - | String | 氟奎诺同
data.weiChanGWYSS | - | String | 新生儿预防用药，围产高危因素
data.qiTaWCGWYS | - | String | 新生儿预防用药，其他围产高危因素
data.shiYongFF | - | String | 使用方法
data.shiFouYLCBX | - | String | 新生儿预防用药，是否有临床表现，1=是，0=否
data.yongYaoFL | - | String | 用药分类
data.linChuangBX | - | String | 新生儿预防用药，临床表现
data.shiQuanSYFL | - | String | 石泉使用分类
data.shiYongYY | - | String | 使用原因
data.bingAnHao | - | String | 病案号
data.bingRenXM | - | String | 病人姓名
data.chuangWeiHao | - | String | 床位号
data.nianLing | - | Integer | 年龄
data.yiShengID | - | Integer | 医生ID
data.yiShengXM | - | String | 医生姓名
data.xiuGaiSJ | - | String | 修改时间
data.bingQuID | - | Integer | 病区ID
data.bingQuZDYM | - | String | 病区自定义码
data.ganRanZD | - | String | 感染诊断
data.wuYangKCLY | - | String | 无样可采理由
data.zhuanKeID | - | Integer | 专科ID
data.zhuanKeMC | - | String | 专科名称
data.qieKouLB | - | String | 切口类别
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.weiShengWu | - | String | 微生物
data.weiShengWuBZ | - | String | 微生物备注
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询微生物无样可采理由(专科)
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getWeiShengWuWykclyByZk?jieShuSJ=&kaiShiSJ=&zhuanKeID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
jieShuSJ | - | Text | 是 | 结束时间(yyyy-MM-dd)
kaiShiSJ | - | Text | 是 | 开始时间(yyyy-MM-dd)
zhuanKeID | - | Text | 否 | 专科ID(空=全院)
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yiZhuID":"","bingLiID":"","zhuYuanID":"","tingZhiSJ":"","shouShuMC":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yaoPinID":"","jiLiang":"","jiXing":"","baoZhuangLiang":"","yiCiYL":"","jiLiangDW":"","ciShu":"","qingJieSS":"","qingJieSSGWYS":"","zhiHangFF":"","qingJieSSSPYSID":"","yaoPinMC":"","kangJunFL":"","qingJieSSSPYJ":"","qingJieSSSPSJ":"","touBaoJB":"","fuHeKJ":"","shiFouYWCG":"","fuKuiNT":"","weiChanGWYSS":"","qiTaWCGWYS":"","shiYongFF":"","shiFouYLCBX":"","yongYaoFL":"","linChuangBX":"","shiQuanSYFL":"","shiYongYY":"","bingAnHao":"","bingRenXM":"","chuangWeiHao":"","nianLing":"","yiShengID":"","yiShengXM":"","xiuGaiSJ":"","bingQuID":"","bingQuZDYM":"","ganRanZD":"","wuYangKCLY":"","zhuanKeID":"","zhuanKeMC":"","qieKouLB":"","zhiLiaoZuID":"","weiShengWu":"","weiShengWuBZ":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.tingZhiSJ | - | String | 停止时间
data.shouShuMC | - | String | 手术名称
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yaoPinID | - | Integer | 药品ID
data.jiLiang | - | String | 剂量
data.jiXing | - | String | 剂型
data.baoZhuangLiang | - | Integer | 包装量
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.ciShu | - | Integer | 次数
data.qingJieSS | - | String | 清洁手术
data.qingJieSSGWYS | - | String | 清洁手术_高危因素
data.zhiHangFF | - | String | 执行方法
data.qingJieSSSPYSID | - | Integer | 清洁手术_审批医生ID
data.yaoPinMC | - | String | 药品名称
data.kangJunFL | - | String | 抗菌分类
data.qingJieSSSPYJ | - | String | 清洁手术_审批意见
data.qingJieSSSPSJ | - | String | 清洁手术_审批时间
data.touBaoJB | - | String | 头孢级别
data.fuHeKJ | - | String | 符合抗菌
data.shiFouYWCG | - | String | 新生儿预防用药，是否有围产高危因素，1=是，0=否
data.fuKuiNT | - | String | 氟奎诺同
data.weiChanGWYSS | - | String | 新生儿预防用药，围产高危因素
data.qiTaWCGWYS | - | String | 新生儿预防用药，其他围产高危因素
data.shiYongFF | - | String | 使用方法
data.shiFouYLCBX | - | String | 新生儿预防用药，是否有临床表现，1=是，0=否
data.yongYaoFL | - | String | 用药分类
data.linChuangBX | - | String | 新生儿预防用药，临床表现
data.shiQuanSYFL | - | String | 石泉使用分类
data.shiYongYY | - | String | 使用原因
data.bingAnHao | - | String | 病案号
data.bingRenXM | - | String | 病人姓名
data.chuangWeiHao | - | String | 床位号
data.nianLing | - | Integer | 年龄
data.yiShengID | - | Integer | 医生ID
data.yiShengXM | - | String | 医生姓名
data.xiuGaiSJ | - | String | 修改时间
data.bingQuID | - | Integer | 病区ID
data.bingQuZDYM | - | String | 病区自定义码
data.ganRanZD | - | String | 感染诊断
data.wuYangKCLY | - | String | 无样可采理由
data.zhuanKeID | - | Integer | 专科ID
data.zhuanKeMC | - | String | 专科名称
data.qieKouLB | - | String | 切口类别
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.weiShengWu | - | String | 微生物
data.weiShengWuBZ | - | String | 微生物备注
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/获取未提交医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getWeiTiJiaoYz?bingLiID=&leiXing=&shenQingDanID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
leiXing | - | Text | 是 | 类型（cq=长期，ls=临时，jcyy=检查用药）
shenQingDanID | - | Text | 是 | (特检)申请单ID，当类型=jcyy时才必传，其余传0
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"canprescribe_CYDY":"","canprescribe_ZY":"","crrt":"","gcp":"","gcpywbh":"","tiBiaoMJ":"","yiZhuID":"","leiBie":"","xianLiang":"","yiZhuLX":"","changQiCY":"","guanLianYZID":"","xuNiZH":"","bingLiID":"","liShiYYTS":"","vteYzVo":{"vteyzid":"","yiZhuID":"","bingLiID":"","zuoTuiLX":"","youTuiLX":"","xiaoShi":"","fenZhong":""},"guanLiLX":"","kongZhiJBMC":"","yaoPinZdVos":[{"icd":"","yiZhuID":"","xuHao":"","bingLiID":"","jiBingID":"","mingCheng":"","xiuGaiSJ":"","xiuGaiYHID":""}],"zhuYuanID":"","_3000":"","crrtYzVos":[{"crrtid":"","crrtxh":"","zhiLiaoYZID":"","bingLiID":"","zhuYuanID":"","yiZhuID":""}],"zhiLiaoyzmxVos":[{"id":"","sheBaoDM":"","sheBaoDM2":"","sheBaoSP":{"diagnose":"","itemID":"","limit":"","mingCheng":"","sheBaoBM":"","type":"","xiangDingFW":[{}]},"yiZhuID":"","bingLiID":"","zhuYuanID":"","muLuID":"","caoZuoMLID":"","yiZhuMC":"","muLuMXID":"","xiangMuID":"","shouFeiZHID":"","shouFeiXMID":"","shouFeiXMMC":"","ziFei":"","danJia":"","shuLiang":"","zuiDaSL":"","zuiShaoSL":"","ciShu":"","weiShouBZ":"","huLiZT":"","shouFeiSJ":"","shouFeiRYID":"","caoZuoZheID":"","xiuGaiSJ":"","yiZhuSJ":"","yiShiID":"","jiGouDM":""}],"zhuanKeID":"","bingQuID":"","jingPeiYP":"","kangJunYaoYfVo":{"yiZhuID":"","kangJunFL":"","touBaoJB":"","fuHeKJ":"","fuKuiNT":"","shiYongFF":"","yongYaoFL":"","shuQianSYFL":"","shiYongYY":"","tingZhiSJ":"","qieKouLB":"","shouShuMC":"","weiShengWu":"","weiShengWBZ":"","qingJieSS":"","qingJieSSGWYS":"","qingJieSSSPYSID":"","qingJieSSSPYJ":"","qingJieSSSPSJ":"","shiFouYWCG":"","weiChanGWYSS":"","qiTaWCGW":"","shiFouYLCBX":"","linChuangBX":""},"danWeiYP":"","fenZhuangQianYPID":"","ziFei":"","biXuJSSJ":"","xiangMuID":"","zuHao":"","yaoFangDM":"","guoJiaCG":"","mingCheng":"","danJia":"","zhiLiaoSFXMID":"","guiGe":"","shouJia":"","jiXing":"","yiCiYL":"","jiLiangDW":"","zhiXingFF":"","zhiXingPL":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuLB":"","shouFeiSL":"","shouFeiCS":"","zhuangTaiBZ":"","luRuSJ":"","yiShengID":"","tingZhiSJ":"","tingZhiYSID":"","cheXiaoZT":"","cheXiaoSJ":"","cheXiaoRYID":"","xuShenPi":"","shenQingSL":"","tongZhiDanID":"","kangJunYP":"","zuHaoTXT":"","bianMaLX":"","xiangMuBM":"","yongYaoTS":"","kaiDanYSZKID":"","mianPiShi":"","shiFouBL":"","danWei":"","shenQingDanSJID":"","yaoPinLB":"","shiFouZB":"","teShuKJYWHZDID":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","jiBingDM":"","jiBingMC":"","shenQingYY":"","yongYaoSQ":"","yaoPinDS":"","changWaiYYHZDID":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQJLID":"","laiYuan":"","maZuiYT":"","xianDingFW":"","fenLeiMa":"","sheBaoDM":"","sheBaoDM2":"","laiYuanLB":"","jiGouDM":"","jinRiLT":"","tingZhiLX":"","jianFa":"","yuanJiHZZQTYSID":"","wuXuDR":"","xueYuanYHID":"","xueYuanYHXM":"","buShouFei":"","bingBingQMS":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.canprescribe_CYDY | - | Number | 
data.canprescribe_ZY | - | Number | 
data.crrt | - | String | 
data.gcp | - | String | 
data.gcpywbh | - | String | 
data.tiBiaoMJ | - | String | 体表面积，1=是
data.yiZhuID | - | Integer | 医嘱ID
data.leiBie | - | String | 类别(1=治疗，2=饮食，3=药品，4=嘱托)
data.xianLiang | - | Integer | 限量
data.yiZhuLX | - | String | 医嘱类型（cq=长期，ls=临时，cy=草药，cydy=出院带药，zcydy=草药带药）
data.changQiCY | - | String | 中成药分餐，0=暂停，1=目录内，2=目录外可用
data.guanLianYZID | - | Integer | 关联医嘱ID
data.xuNiZH | - | Integer | 虚拟组号
data.bingLiID | - | Integer | 病历ID
data.liShiYYTS | - | Integer | 历史用药天数
data.vteYzVo | - | Object | 
data.vteYzVo.vteyzid | - | String | 
data.vteYzVo.yiZhuID | - | Integer | 医嘱ID(ylyzall.yzid)
data.vteYzVo.bingLiID | - | Integer | 病历ID
data.vteYzVo.zuoTuiLX | - | String | 左腿类型
data.vteYzVo.youTuiLX | - | String | 右腿类型
data.vteYzVo.xiaoShi | - | Number | 小时
data.vteYzVo.fenZhong | - | Number | 分钟
data.guanLiLX | - | String | 管理类型，1一类精药，2二类精药，E易制毒，D毒性药品，M麻醉药品
data.kongZhiJBMC | - | String | 控制级别名称，0=自费，1=甲类，2=乙类，3=乙类(10%)，4=乙类(20%)，5=乙类(30%)，6=纯自费
data.yaoPinZdVos | - | Array | 药品诊断
data.yaoPinZdVos.icd | - | String | 
data.yaoPinZdVos.yiZhuID | - | Integer | 医嘱ID
data.yaoPinZdVos.xuHao | - | Integer | 序号
data.yaoPinZdVos.bingLiID | - | Integer | 病历ID
data.yaoPinZdVos.jiBingID | - | Integer | 疾病ID
data.yaoPinZdVos.mingCheng | - | String | 名称
data.yaoPinZdVos.xiuGaiSJ | - | String | 修改时间
data.yaoPinZdVos.xiuGaiYHID | - | Integer | 修改用户ID
data.zhuYuanID | - | Integer | 住院ID
data._3000 | - | String | 不进出院带药的药品（该药有分装）
data.crrtYzVos | - | Array | crrt医嘱
data.crrtYzVos.crrtid | - | Integer | 
data.crrtYzVos.crrtxh | - | Integer | 
data.crrtYzVos.zhiLiaoYZID | - | Integer | 治疗医嘱ID，对应yl_yzall.yzid
data.crrtYzVos.bingLiID | - | Integer | 病历ID
data.crrtYzVos.zhuYuanID | - | Integer | 住院ID
data.crrtYzVos.yiZhuID | - | Integer | 医嘱ID，对应yl_yzall.yzid
data.zhiLiaoyzmxVos | - | Array | 治疗医嘱明细
data.zhiLiaoyzmxVos.id | - | Integer | 
data.zhiLiaoyzmxVos.sheBaoDM | - | String | 
data.zhiLiaoyzmxVos.sheBaoDM2 | - | String | 
data.zhiLiaoyzmxVos.sheBaoSP | - | Object | 
data.zhiLiaoyzmxVos.sheBaoSP.diagnose | - | String | 诊断
data.zhiLiaoyzmxVos.sheBaoSP.itemID | - | Integer | 项目ID
data.zhiLiaoyzmxVos.sheBaoSP.limit | - | String | 限制范围
data.zhiLiaoyzmxVos.sheBaoSP.mingCheng | - | String | 项目名称
data.zhiLiaoyzmxVos.sheBaoSP.sheBaoBM | - | String | 社保编码
data.zhiLiaoyzmxVos.sheBaoSP.type | - | String | 类别:我知道了(1,I Know);提交审批(2,Submit);无(0, None)
data.zhiLiaoyzmxVos.sheBaoSP.xiangDingFW | - | Array | 限定范围
data.zhiLiaoyzmxVos.yiZhuID | - | Integer | 医嘱ID(yl_yzall.yzid)
data.zhiLiaoyzmxVos.bingLiID | - | Integer | 病历ID
data.zhiLiaoyzmxVos.zhuYuanID | - | Integer | 住院ID
data.zhiLiaoyzmxVos.muLuID | - | Integer | 目录ID
data.zhiLiaoyzmxVos.caoZuoMLID | - | Integer | 操作目录ID
data.zhiLiaoyzmxVos.yiZhuMC | - | String | 医嘱名称
data.zhiLiaoyzmxVos.muLuMXID | - | Integer | 目录明细ID
data.zhiLiaoyzmxVos.xiangMuID | - | Integer | 项目ID
data.zhiLiaoyzmxVos.shouFeiZHID | - | Integer | 收费组合ID
data.zhiLiaoyzmxVos.shouFeiXMID | - | Integer | 收费项目ID
data.zhiLiaoyzmxVos.shouFeiXMMC | - | String | 收费项目名称
data.zhiLiaoyzmxVos.ziFei | - | String | 自费
data.zhiLiaoyzmxVos.danJia | - | Number | 单价
data.zhiLiaoyzmxVos.shuLiang | - | Number | 数量
data.zhiLiaoyzmxVos.zuiDaSL | - | Integer | 最大数量
data.zhiLiaoyzmxVos.zuiShaoSL | - | Integer | 最少数量
data.zhiLiaoyzmxVos.ciShu | - | Integer | 次数
data.zhiLiaoyzmxVos.weiShouBZ | - | String | 未收标志
data.zhiLiaoyzmxVos.huLiZT | - | String | 护理状态
data.zhiLiaoyzmxVos.shouFeiSJ | - | String | 收费时间
data.zhiLiaoyzmxVos.shouFeiRYID | - | Integer | 收费人员ID
data.zhiLiaoyzmxVos.caoZuoZheID | - | Integer | 操作者ID
data.zhiLiaoyzmxVos.xiuGaiSJ | - | String | 修改时间
data.zhiLiaoyzmxVos.yiZhuSJ | - | String | 医嘱时间
data.zhiLiaoyzmxVos.yiShiID | - | Integer | 医师ID
data.zhiLiaoyzmxVos.jiGouDM | - | String | 机构代码
data.zhuanKeID | - | Integer | 专科ID
data.bingQuID | - | Integer | 病区ID
data.jingPeiYP | - | String | 静脉营养药品
data.kangJunYaoYfVo | - | Object | 
data.kangJunYaoYfVo.yiZhuID | - | Integer | 医嘱ID
data.kangJunYaoYfVo.kangJunFL | - | String | 抗菌分类
data.kangJunYaoYfVo.touBaoJB | - | String | 头孢级别
data.kangJunYaoYfVo.fuHeKJ | - | String | 复合抗菌
data.kangJunYaoYfVo.fuKuiNT | - | String | 氟奎诺同
data.kangJunYaoYfVo.shiYongFF | - | String | 使用方法
data.kangJunYaoYfVo.yongYaoFL | - | String | 用药分类
data.kangJunYaoYfVo.shuQianSYFL | - | String | 术前使用分类
data.kangJunYaoYfVo.shiYongYY | - | String | 使用原因
data.kangJunYaoYfVo.tingZhiSJ | - | String | 停止时间
data.kangJunYaoYfVo.qieKouLB | - | String | 切口类别
data.kangJunYaoYfVo.shouShuMC | - | String | 手术名称
data.kangJunYaoYfVo.weiShengWu | - | String | 微生物
data.kangJunYaoYfVo.weiShengWBZ | - | String | 微生物备注
data.kangJunYaoYfVo.qingJieSS | - | String | 清洁手术
data.kangJunYaoYfVo.qingJieSSGWYS | - | String | 清洁手术_高危因素
data.kangJunYaoYfVo.qingJieSSSPYSID | - | Integer | 清洁手术_审批医生ID
data.kangJunYaoYfVo.qingJieSSSPYJ | - | String | 清洁手术_审批意见
data.kangJunYaoYfVo.qingJieSSSPSJ | - | String | 清洁手术_审批时间
data.kangJunYaoYfVo.shiFouYWCG | - | String | 新生儿预防用药，是否有围产高危因素，1=是，0=否
data.kangJunYaoYfVo.weiChanGWYSS | - | String | 新生儿预防用药，围产高危因素
data.kangJunYaoYfVo.qiTaWCGW | - | String | 新生儿预防用药，其他围产高危因素
data.kangJunYaoYfVo.shiFouYLCBX | - | String | 新生儿预防用药，是否有临床表现，1=是，0=否
data.kangJunYaoYfVo.linChuangBX | - | String | 新生儿预防用药，临床表现
data.danWeiYP | - | String | 单味药品，1=是
data.fenZhuangQianYPID | - | Integer | 分装前药品ID
data.ziFei | - | String | 自费
data.biXuJSSJ | - | String | 必须填写结束时间的药品，1=是
data.xiangMuID | - | String | 项目ID：药品为药品ID，治疗为z+医嘱目录ID，饮食为y+饮食代码，嘱托是t6712
data.zuHao | - | Integer | 组号（真实组号）
data.yaoFangDM | - | String | 药房代码
data.guoJiaCG | - | String | 国家采购
data.mingCheng | - | String | 名称
data.danJia | - | Number | 单价
data.zhiLiaoSFXMID | - | Integer | 治疗收费项目ID（用于规则引擎）
data.guiGe | - | String | 规格
data.shouJia | - | Number | 售价
data.jiXing | - | String | 剂型
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 计量单位
data.zhiXingFF | - | String | 执行方法
data.zhiXingPL | - | String | 执行频率
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuLB | - | String | 医嘱类别：1西药，2中成药，3草药
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.zhuangTaiBZ | - | String | 状态标志
data.luRuSJ | - | String | 录入时间
data.yiShengID | - | Integer | 医生ID
data.tingZhiSJ | - | String | 停止时间
data.tingZhiYSID | - | Integer | 停止医生ID
data.cheXiaoZT | - | String | 撤销状态
data.cheXiaoSJ | - | String | 撤销时间
data.cheXiaoRYID | - | Integer | 撤销人员ID
data.xuShenPi | - | String | 需审批
data.shenQingSL | - | Number | 申请数量
data.tongZhiDanID | - | Integer | 通知单ID
data.kangJunYP | - | String | 抗菌药品
data.zuHaoTXT | - | String | 组号TXT
data.bianMaLX | - | String | 编码类型
data.xiangMuBM | - | String | 项目编码
data.yongYaoTS | - | Integer | 用药天数
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.mianPiShi | - | String | 免皮试
data.shiFouBL | - | String | 是否补录
data.danWei | - | String | 单位
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别
data.shiFouZB | - | String | 是否自备
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.jiBingDM | - | String | 疾病代码
data.jiBingMC | - | String | 疾病名称
data.shenQingYY | - | String | 申请原因
data.yongYaoSQ | - | String | 用药时期
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.zhiXingPL2 | - | String | 执行频率2
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQJLID | - | Integer | 外购药品申请记ID
data.laiYuan | - | String | 来源，2=ehr2，3=ehr3，4=微服务
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.fenLeiMa | - | String | 分类码
data.sheBaoDM | - | String | 社保代码
data.sheBaoDM2 | - | String | 社保代码2
data.laiYuanLB | - | String | 来源类别
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
data.jianFa | - | String | 煎法
data.yuanJiHZZQTYSID | - | Integer | 院际会诊(远程会诊特需服务)知情同意书ID，对应e_blwsjl.id
data.wuXuDR | - | String | 无需导入，！！注意，1=院际会诊
data.xueYuanYHID | - | Integer | 学员用户ID
data.xueYuanYHXM | - | String | 学员用户姓名
data.buShouFei | - | String | 不收费
data.bingBingQMS | - | String | 病病情描述
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_新生儿提醒
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getXinShengErTx

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"kaJieMiaoTXBRs":[{"banZuDM":"","bingAnHao":"","bingLiID":"","bingQuChuYuanSJ":"","bingQuID":"","bingQuRuYuanSJ":"","bingRenBH":"","bingRenXM":"","caiWuRuYuanSJ":"","caoZuoZheID":"","chuShengRQ":"","chuShiHuaSJ":"","chuYuanSJ":"","chuangWeiHao":"","danWeiDM":"","danWeiMC":"","guoJiDM":"","h1N1":"","huLiJB":"","huZhao":"","hunYinZK":"","jiBingID":"","jiGuanDM":"","jiaShuDH":"","jiaShuGX":"","jiaShuXM":"","jieSuanDM":"","jieSuanLX":"","jieSuanSJ":"","lianXiDH":"","lianXiDZ":"","minZuDM":"","qiTaZJH":"","riJianSS":"","ruYuanQK":"","ruYuanYQ":"","ruYuanZD":"","shenFenZH":"","shouCiRYSJ":"","xingBie":"","xiuGaiRYID":"","xiuGaiSJ":"","yuZhuYuan":"","zhenDuanDM":"","zhiLiaoZuID":"","zhiYeDM":"","zhongTuJS":"","zhuGuanHSID":"","zhuGuanYSID":"","zhuYuanHao":"","zhuYuanID":"","zhuYuanID_MOTHER":"","zhuanKeID":"","zhuangTaiBZ":"","ziFuYE":""}],"zuDiXieTXBRs":[{"banZuDM":"","bingAnHao":"","bingLiID":"","bingQuChuYuanSJ":"","bingQuID":"","bingQuRuYuanSJ":"","bingRenBH":"","bingRenXM":"","caiWuRuYuanSJ":"","caoZuoZheID":"","chuShengRQ":"","chuShiHuaSJ":"","chuYuanSJ":"","chuangWeiHao":"","danWeiDM":"","danWeiMC":"","guoJiDM":"","h1N1":"","huLiJB":"","huZhao":"","hunYinZK":"","jiBingID":"","jiGuanDM":"","jiaShuDH":"","jiaShuGX":"","jiaShuXM":"","jieSuanDM":"","jieSuanLX":"","jieSuanSJ":"","lianXiDH":"","lianXiDZ":"","minZuDM":"","qiTaZJH":"","riJianSS":"","ruYuanQK":"","ruYuanYQ":"","ruYuanZD":"","shenFenZH":"","shouCiRYSJ":"","xingBie":"","xiuGaiRYID":"","xiuGaiSJ":"","yuZhuYuan":"","zhenDuanDM":"","zhiLiaoZuID":"","zhiYeDM":"","zhongTuJS":"","zhuGuanHSID":"","zhuGuanYSID":"","zhuYuanHao":"","zhuYuanID":"","zhuYuanID_MOTHER":"","zhuanKeID":"","zhuangTaiBZ":"","ziFuYE":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.kaJieMiaoTXBRs | - | Array | 卡介苗提醒病人列表
data.kaJieMiaoTXBRs.banZuDM | - | String | BZDM
data.kaJieMiaoTXBRs.bingAnHao | - | String | 病案号
data.kaJieMiaoTXBRs.bingLiID | - | Integer | 病历ID
data.kaJieMiaoTXBRs.bingQuChuYuanSJ | - | String | 病区出院时间
data.kaJieMiaoTXBRs.bingQuID | - | Integer | 病区ID
data.kaJieMiaoTXBRs.bingQuRuYuanSJ | - | String | 病区入院时间
data.kaJieMiaoTXBRs.bingRenBH | - | String | 病人编号
data.kaJieMiaoTXBRs.bingRenXM | - | String | 病人姓名
data.kaJieMiaoTXBRs.caiWuRuYuanSJ | - | String | 财务入院时间
data.kaJieMiaoTXBRs.caoZuoZheID | - | Integer | 操作者ID
data.kaJieMiaoTXBRs.chuShengRQ | - | String | 出生日期
data.kaJieMiaoTXBRs.chuShiHuaSJ | - | String | 初始化时间
data.kaJieMiaoTXBRs.chuYuanSJ | - | String | 出院时间
data.kaJieMiaoTXBRs.chuangWeiHao | - | String | 床位号
data.kaJieMiaoTXBRs.danWeiDM | - | String | 单位代码
data.kaJieMiaoTXBRs.danWeiMC | - | String | 单位代码
data.kaJieMiaoTXBRs.guoJiDM | - | String | 国籍代码
data.kaJieMiaoTXBRs.h1N1 | - | String | 
data.kaJieMiaoTXBRs.huLiJB | - | String | 护理级别
data.kaJieMiaoTXBRs.huZhao | - | String | 护照
data.kaJieMiaoTXBRs.hunYinZK | - | String | 婚姻状况
data.kaJieMiaoTXBRs.jiBingID | - | Integer | JBID
data.kaJieMiaoTXBRs.jiGuanDM | - | String | 籍贯代码
data.kaJieMiaoTXBRs.jiaShuDH | - | String | 家属电话
data.kaJieMiaoTXBRs.jiaShuGX | - | String | 家属关系
data.kaJieMiaoTXBRs.jiaShuXM | - | String | 家属姓名
data.kaJieMiaoTXBRs.jieSuanDM | - | String | 结算代码
data.kaJieMiaoTXBRs.jieSuanLX | - | String | 结算类型
data.kaJieMiaoTXBRs.jieSuanSJ | - | String | 结算时间
data.kaJieMiaoTXBRs.lianXiDH | - | String | 联系电话
data.kaJieMiaoTXBRs.lianXiDZ | - | String | 联系地址
data.kaJieMiaoTXBRs.minZuDM | - | String | 民族代码
data.kaJieMiaoTXBRs.qiTaZJH | - | String | 其他证件号
data.kaJieMiaoTXBRs.riJianSS | - | String | 日间病人
data.kaJieMiaoTXBRs.ruYuanQK | - | String | 入院情况
data.kaJieMiaoTXBRs.ruYuanYQ | - | String | 入院院区
data.kaJieMiaoTXBRs.ruYuanZD | - | String | 入院诊断
data.kaJieMiaoTXBRs.shenFenZH | - | String | 身份证号
data.kaJieMiaoTXBRs.shouCiRYSJ | - | String | 首次入院时间
data.kaJieMiaoTXBRs.xingBie | - | String | 病人性别
data.kaJieMiaoTXBRs.xiuGaiRYID | - | Integer | 修改人员ID
data.kaJieMiaoTXBRs.xiuGaiSJ | - | String | 修改时间
data.kaJieMiaoTXBRs.yuZhuYuan | - | String | 预住院
data.kaJieMiaoTXBRs.zhenDuanDM | - | String | 诊断代码
data.kaJieMiaoTXBRs.zhiLiaoZuID | - | Integer | 治疗组ID
data.kaJieMiaoTXBRs.zhiYeDM | - | String | 职业代码
data.kaJieMiaoTXBRs.zhongTuJS | - | String | 中途结算
data.kaJieMiaoTXBRs.zhuGuanHSID | - | Integer | 主管护士ID
data.kaJieMiaoTXBRs.zhuGuanYSID | - | Integer | 主管医师ID
data.kaJieMiaoTXBRs.zhuYuanHao | - | String | 住院号
data.kaJieMiaoTXBRs.zhuYuanID | - | Integer | 住院ID
data.kaJieMiaoTXBRs.zhuYuanID_MOTHER | - | Integer | 妈妈的住院ID
data.kaJieMiaoTXBRs.zhuanKeID | - | Integer | 专科ID
data.kaJieMiaoTXBRs.zhuangTaiBZ | - | String | 状态标志
data.kaJieMiaoTXBRs.ziFuYE | - | Number | 自费余额
data.zuDiXieTXBRs | - | Array | 足底血提醒病人列表
data.zuDiXieTXBRs.banZuDM | - | String | BZDM
data.zuDiXieTXBRs.bingAnHao | - | String | 病案号
data.zuDiXieTXBRs.bingLiID | - | Integer | 病历ID
data.zuDiXieTXBRs.bingQuChuYuanSJ | - | String | 病区出院时间
data.zuDiXieTXBRs.bingQuID | - | Integer | 病区ID
data.zuDiXieTXBRs.bingQuRuYuanSJ | - | String | 病区入院时间
data.zuDiXieTXBRs.bingRenBH | - | String | 病人编号
data.zuDiXieTXBRs.bingRenXM | - | String | 病人姓名
data.zuDiXieTXBRs.caiWuRuYuanSJ | - | String | 财务入院时间
data.zuDiXieTXBRs.caoZuoZheID | - | Integer | 操作者ID
data.zuDiXieTXBRs.chuShengRQ | - | String | 出生日期
data.zuDiXieTXBRs.chuShiHuaSJ | - | String | 初始化时间
data.zuDiXieTXBRs.chuYuanSJ | - | String | 出院时间
data.zuDiXieTXBRs.chuangWeiHao | - | String | 床位号
data.zuDiXieTXBRs.danWeiDM | - | String | 单位代码
data.zuDiXieTXBRs.danWeiMC | - | String | 单位代码
data.zuDiXieTXBRs.guoJiDM | - | String | 国籍代码
data.zuDiXieTXBRs.h1N1 | - | String | 
data.zuDiXieTXBRs.huLiJB | - | String | 护理级别
data.zuDiXieTXBRs.huZhao | - | String | 护照
data.zuDiXieTXBRs.hunYinZK | - | String | 婚姻状况
data.zuDiXieTXBRs.jiBingID | - | Integer | JBID
data.zuDiXieTXBRs.jiGuanDM | - | String | 籍贯代码
data.zuDiXieTXBRs.jiaShuDH | - | String | 家属电话
data.zuDiXieTXBRs.jiaShuGX | - | String | 家属关系
data.zuDiXieTXBRs.jiaShuXM | - | String | 家属姓名
data.zuDiXieTXBRs.jieSuanDM | - | String | 结算代码
data.zuDiXieTXBRs.jieSuanLX | - | String | 结算类型
data.zuDiXieTXBRs.jieSuanSJ | - | String | 结算时间
data.zuDiXieTXBRs.lianXiDH | - | String | 联系电话
data.zuDiXieTXBRs.lianXiDZ | - | String | 联系地址
data.zuDiXieTXBRs.minZuDM | - | String | 民族代码
data.zuDiXieTXBRs.qiTaZJH | - | String | 其他证件号
data.zuDiXieTXBRs.riJianSS | - | String | 日间病人
data.zuDiXieTXBRs.ruYuanQK | - | String | 入院情况
data.zuDiXieTXBRs.ruYuanYQ | - | String | 入院院区
data.zuDiXieTXBRs.ruYuanZD | - | String | 入院诊断
data.zuDiXieTXBRs.shenFenZH | - | String | 身份证号
data.zuDiXieTXBRs.shouCiRYSJ | - | String | 首次入院时间
data.zuDiXieTXBRs.xingBie | - | String | 病人性别
data.zuDiXieTXBRs.xiuGaiRYID | - | Integer | 修改人员ID
data.zuDiXieTXBRs.xiuGaiSJ | - | String | 修改时间
data.zuDiXieTXBRs.yuZhuYuan | - | String | 预住院
data.zuDiXieTXBRs.zhenDuanDM | - | String | 诊断代码
data.zuDiXieTXBRs.zhiLiaoZuID | - | Integer | 治疗组ID
data.zuDiXieTXBRs.zhiYeDM | - | String | 职业代码
data.zuDiXieTXBRs.zhongTuJS | - | String | 中途结算
data.zuDiXieTXBRs.zhuGuanHSID | - | Integer | 主管护士ID
data.zuDiXieTXBRs.zhuGuanYSID | - | Integer | 主管医师ID
data.zuDiXieTXBRs.zhuYuanHao | - | String | 住院号
data.zuDiXieTXBRs.zhuYuanID | - | Integer | 住院ID
data.zuDiXieTXBRs.zhuYuanID_MOTHER | - | Integer | 妈妈的住院ID
data.zuDiXieTXBRs.zhuanKeID | - | Integer | 专科ID
data.zuDiXieTXBRs.zhuangTaiBZ | - | String | 状态标志
data.zuDiXieTXBRs.ziFuYE | - | Number | 自费余额
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/获取药品附加信息
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getYaoPinFjxx

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","jiLiangDW":"","yaoFangDM":"","yaoPinID":"","yaoPinLB":"","yaoPinMC":"","yiCiYL":"","yiZhuLB":"","yiZhuLX":"","zhiXingFF":"","zhiXingPL":"","zhuYuanID":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
jiLiangDW | - | String | 否 | 计量单位
yaoFangDM | - | String | 否 | 药房代码
yaoPinID | - | Integer | 否 | 药品ID
yaoPinLB | - | String | 否 | 药品类别（0=本院药品，1=外购药品，2=赠送药品）
yaoPinMC | - | String | 否 | 药品名称
yiCiYL | - | Number | 否 | 一次用量
yiZhuLB | - | String | 否 | 医嘱类别（大类，cq长期,ls临时）
yiZhuLX | - | String | 否 | 医嘱类型（cq=长期,ls=临时,cy=草药,zcydy=草药带药,cydy=出院带药）
zhiXingFF | - | String | 否 | 执行方法
zhiXingPL | - | String | 否 | 执行频率
zhuYuanID | - | Integer | 否 | 住院ID
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"canprescribe_cydy":"","canprescribe_zy":"","cuoWuDM":"","cuoWuXX":"","drugAttributesVos":[{"beiZhu":"","shuXingDM":"","shuXingZhi":"","yaoPinID":""}],"erJiaShuangGuaTX":"","erTongYYTS":"","guoJiaCG":"","guoMinTX":"","kongZhiJBMC":"","liShiYYTS":"","maZuiYP":"","naiYaoLvTS":"","needBMI":"","needNIHSS":"","shangCiXdfw":"","sheBaoDM":"","sheBaoSP":{"diagnose":"","itemID":"","limit":"","mingCheng":"","sheBaoBM":"","type":"","xiangDingFW":[{}]},"shengYuSYLTS":"","shouShuTZDs":[{"daiMa":"","mingCheng":""}],"teShuKjywHzdID":"","xianZhiDJ":"","xueZhiPinTX":"","yaoPinID":"","yaoPinMC":"","yaoPinXX":{"baoZhuangLiang":"","chuYuanDYKFCF":"","daBaoZhuangLiang":"","danWei":"","fenLeiMa":"","fzqyaoPinID":"","guanLiLX":"","jiLiang":"","jiXing":"","jiZhenKFCF":"","keFouChaiFen":"","keShouFeiYL":"","kongZhiJiBie":"","kuCunZT":"","lianFangYPKFCF":"","menZhenBL":"","menZhenKFCF":"","menZhenSP":"","menZhenXL":"","mingCheng":"","piShiYP":"","piShiYPLX":"","piShiYeLX":"","pinYin":"","shouJia":"","wuBi":"","yaoFangDM":"","yaoPinID":"","yiLiaoSR":"","zaiYongKCL":"","zhuYuanBL":"","zhuYuanCFKFCF":"","zhuYuanSP":"","zhuYuanXL":"","zhuangTaiBZ":"","zuiXiaoDanWei":""},"yiBaoDDTC":"","yiZhuLB":"","zhiOuYPTX":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.canprescribe_cydy | - | Number | 出院带药开集采药品可用数量，大于0可以开
data.canprescribe_zy | - | Number | 住院开集采药品可用数量，大于0可以开
data.cuoWuDM | - | String | 错误代码（0=可以开具，1=拦截）
data.cuoWuXX | - | String | 错误信息
data.drugAttributesVos | - | Array | 药品附加属性列表
data.drugAttributesVos.beiZhu | - | String | 备注
data.drugAttributesVos.shuXingDM | - | String | 属性代码
data.drugAttributesVos.shuXingZhi | - | String | 属性值
data.drugAttributesVos.yaoPinID | - | Integer | 药品id
data.erJiaShuangGuaTX | - | String | 二甲双胍(guā)提醒
data.erTongYYTS | - | String | 儿童用药提示
data.guoJiaCG | - | String | 国家采购
data.guoMinTX | - | String | 过敏提醒
data.kongZhiJBMC | - | String | 控制级别名称
data.liShiYYTS | - | Integer | 历史用药天数
data.maZuiYP | - | String | 麻醉药品，1=是，0=否
data.naiYaoLvTS | - | String | 抗菌药物耐药率提示
data.needBMI | - | String | 是否需要BMI，1=需要，0=不需要
data.needNIHSS | - | String | NIHSS评分表，1需要填写
data.shangCiXdfw | - | String | 上次限定范围
data.sheBaoDM | - | String | 社保代码
data.sheBaoSP | - | Object | 
data.sheBaoSP.diagnose | - | String | 诊断
data.sheBaoSP.itemID | - | Integer | 项目ID
data.sheBaoSP.limit | - | String | 限制范围
data.sheBaoSP.mingCheng | - | String | 项目名称
data.sheBaoSP.sheBaoBM | - | String | 社保编码
data.sheBaoSP.type | - | String | 类别:我知道了(1,I Know);提交审批(2,Submit);无(0, None)
data.sheBaoSP.xiangDingFW | - | Array | 限定范围
data.shengYuSYLTS | - | String | 剩余使用量提示
data.shouShuTZDs | - | Array | 手术通知单
data.shouShuTZDs.daiMa | - | String | 代码
data.shouShuTZDs.mingCheng | - | String | 名称
data.teShuKjywHzdID | - | Integer | 特殊抗菌药物会诊单ID
data.xianZhiDJ | - | Number | 限制单价
data.xueZhiPinTX | - | String | 血制品提醒
data.yaoPinID | - | Integer | 药品ID
data.yaoPinMC | - | String | 药品名称
data.yaoPinXX | - | Object | 
data.yaoPinXX.baoZhuangLiang | - | Integer | 包装量
data.yaoPinXX.chuYuanDYKFCF | - | String | 出院带药可否拆分
data.yaoPinXX.daBaoZhuangLiang | - | Integer | 大包装量
data.yaoPinXX.danWei | - | String | 单位
data.yaoPinXX.fenLeiMa | - | String | 分类码
data.yaoPinXX.fzqyaoPinID | - | Integer | 分装前药品ID
data.yaoPinXX.guanLiLX | - | String | 管理类型
data.yaoPinXX.jiLiang | - | String | 剂量
data.yaoPinXX.jiXing | - | String | 剂型
data.yaoPinXX.jiZhenKFCF | - | String | 急诊可否拆分
data.yaoPinXX.keFouChaiFen | - | String | 可否拆分
data.yaoPinXX.keShouFeiYL | - | Number | 可收费用量
data.yaoPinXX.kongZhiJiBie | - | String | 控制级别
data.yaoPinXX.kuCunZT | - | String | 库存状态
data.yaoPinXX.lianFangYPKFCF | - | String | 联方药盘可否拆分
data.yaoPinXX.menZhenBL | - | Number | 门诊比例
data.yaoPinXX.menZhenKFCF | - | String | 门诊可否拆分
data.yaoPinXX.menZhenSP | - | String | 门诊审批
data.yaoPinXX.menZhenXL | - | Number | 门诊限量
data.yaoPinXX.mingCheng | - | String | 药品名称
data.yaoPinXX.piShiYP | - | Boolean | 是否皮试药品
data.yaoPinXX.piShiYPLX | - | String | 皮试药品类型
data.yaoPinXX.piShiYeLX | - | String | 皮试液类型
data.yaoPinXX.pinYin | - | String | 拼音
data.yaoPinXX.shouJia | - | Number | 售价
data.yaoPinXX.wuBi | - | String | 五笔
data.yaoPinXX.yaoFangDM | - | String | 药房代码
data.yaoPinXX.yaoPinID | - | Integer | 药品id
data.yaoPinXX.yiLiaoSR | - | String | 医疗收入
data.yaoPinXX.zaiYongKCL | - | Number | 在用库存量
data.yaoPinXX.zhuYuanBL | - | Number | 住院比例
data.yaoPinXX.zhuYuanCFKFCF | - | String | 住院处方可否拆分
data.yaoPinXX.zhuYuanSP | - | String | 住院审批
data.yaoPinXX.zhuYuanXL | - | Number | 住院限量
data.yaoPinXX.zhuangTaiBZ | - | String | 状态标志
data.yaoPinXX.zuiXiaoDanWei | - | String | 最小单位
data.yiBaoDDTC | - | String | 医保单独弹窗（肠外营养医嘱单），1=是
data.yiZhuLB | - | String | 医嘱类别：1西药，2中成药，3草药
data.zhiOuYPTX | - | String | 致呕药物分级提醒
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_药品基本使用方法
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getYaoPinJBSYFF?yaoPinID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
yaoPinID | - | Text | 是 | 药品ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"yongFaYL":[{"changYongLiang":"","fangFa":"","fuYongBZ":"","geiYaoSJ":"","jiLiangDW":"","paiXu":"","pinLu":"","yaoPinID":""}],"yaoPinJL":[{"jiLiangDW":"","jiLiangZhi":"","paiXu":"","yaoPinID":"","zhuangTaiBZ":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.yongFaYL | - | Array | 用法用量
data.yongFaYL.changYongLiang | - | Number | 常用量
data.yongFaYL.fangFa | - | String | 方法
data.yongFaYL.fuYongBZ | - | String | 复用标志
data.yongFaYL.geiYaoSJ | - | String | 给药时间
data.yongFaYL.jiLiangDW | - | String | 剂量单位
data.yongFaYL.paiXu | - | Integer | 排序
data.yongFaYL.pinLu | - | String | 频率
data.yongFaYL.yaoPinID | - | Integer | 药品id
data.yaoPinJL | - | Array | 药品剂量
data.yaoPinJL.jiLiangDW | - | String | 剂量单位
data.yaoPinJL.jiLiangZhi | - | Number | 剂量值
data.yaoPinJL.paiXu | - | Integer | 排序
data.yaoPinJL.yaoPinID | - | Integer | 药品id
data.yaoPinJL.zhuangTaiBZ | - | String | 状态标志:0停用 1可用 2可用但不允许医生作为数量单位
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/获取饮食附加信息
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getYinShiFjxx

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","yinShiDM":"","zhuYuanID":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yinShiDM | - | String | 否 | 饮食代码
zhuYuanID | - | Integer | 否 | 住院ID
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"cuoWuDM":"","cuoWuXX":"","hszYsdmVo":{"daiMa":"","mingCheng":"","pinYin":"","wuBi":"","danJia":"","leiBie":"","zuHeID":"","xiangMuID":"","zhuangTaiBZ":"","paiXu":"","xiuGaiSJ":"","caoZuoZID":"","shiFouPSDX":"","shiFouXJ":"","zaoCanSFBL":"","zhongCanSFBL":"","wanCanSFBL":"","shiFouZLYS":"","jigouDM":""}},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.cuoWuDM | - | String | 错误代码（0=可以开具，1=拦截）
data.cuoWuXX | - | String | 错误信息
data.hszYsdmVo | - | Object | 
data.hszYsdmVo.daiMa | - | String | 代码
data.hszYsdmVo.mingCheng | - | String | 名称
data.hszYsdmVo.pinYin | - | String | 拼音
data.hszYsdmVo.wuBi | - | String | 五笔
data.hszYsdmVo.danJia | - | Number | 单价
data.hszYsdmVo.leiBie | - | String | 类别
data.hszYsdmVo.zuHeID | - | Integer | 组合ID
data.hszYsdmVo.xiangMuID | - | Integer | 项目ID
data.hszYsdmVo.zhuangTaiBZ | - | String | 状态标志
data.hszYsdmVo.paiXu | - | Integer | 排序
data.hszYsdmVo.xiuGaiSJ | - | String | 修改时间
data.hszYsdmVo.caoZuoZID | - | Integer | 操作者ID
data.hszYsdmVo.shiFouPSDX | - | String | 是否配送点心
data.hszYsdmVo.shiFouXJ | - | String | 是否宣教
data.hszYsdmVo.zaoCanSFBL | - | Integer | 早餐收费比例
data.hszYsdmVo.zhongCanSFBL | - | Integer | 中餐收费比例
data.hszYsdmVo.wanCanSFBL | - | Integer | 晚餐收费比例
data.hszYsdmVo.shiFouZLYS | - | String | 是否治疗饮食
data.hszYsdmVo.jigouDM | - | String | 机构代码
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_根据交易ID获取医嘱转费用
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getYzZfyByJyid?jiaoYiID=&leiXing=&yiZhuID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
jiaoYiID | - | Text | 是 | 交易ID
leiXing | - | Text | 是 | 类型
yiZhuID | - | Text | 是 | 医嘱ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"id":"","bingLiID":"","zhuYuanID":"","yiZhuID":"","leiXing":"","ziFei":"","ziFeiMC":"","mingCheng":"","jiaoYiID":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","shenQingLY":"","shenQingYHID":"","shenQingYHXM":"","shenQingSJ":"","shenPiYJ":"","shenPiYJMC":"","shenPiBZ":"","shenPiYHID":"","shenPiYHXM":"","shenPiSJ":"","xiangMuID":"","jiLuID":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.id | - | Integer | 
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.yiZhuID | - | Integer | 医嘱ID
data.leiXing | - | String | 类型，yp=药品，zl=治疗
data.ziFei | - | String | 自费（转换前）
data.ziFeiMC | - | String | 自费名称（转换前）
data.mingCheng | - | String | 名称
data.jiaoYiID | - | Integer | 交易ID
data.zhuangTaiBZ | - | String | 状态标志，0=失效，1=申请，2=审批
data.zhuangTaiBZMC | - | String | 状态标志名称
data.shenQingLY | - | String | 申请理由
data.shenQingYHID | - | Integer | 申请用户ID
data.shenQingYHXM | - | String | 申请用户姓名
data.shenQingSJ | - | String | 申请时间
data.shenPiYJ | - | String | 审批意见，1=同意，2=拒绝
data.shenPiYJMC | - | String | 审批意见名称
data.shenPiBZ | - | String | 审批备注
data.shenPiYHID | - | Integer | 审批用户ID
data.shenPiYHXM | - | String | 审批用户姓名
data.shenPiSJ | - | String | 审批时间
data.xiangMuID | - | Integer | 项目ID
data.jiLuID | - | Integer | 记录ID
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/获取治疗附加信息
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getZhiLiaoFjxx

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","yiZhuLB":"","yiZhuMLID":"","yiZhuMLMC":"","zhuYuanID":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yiZhuLB | - | String | 否 | 医嘱类别（大类，cq长期,ls临时）
yiZhuMLID | - | Integer | 否 | 医嘱目录ID
yiZhuMLMC | - | String | 否 | 医嘱目录名称
zhuYuanID | - | Integer | 否 | 住院ID
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"cuoWuDM":"","cuoWuXX":"","yiZhuMXVos":[{"id":"","sheBaoDM":"","sheBaoDM2":"","sheBaoSP":{"diagnose":"","itemID":"","limit":"","mingCheng":"","sheBaoBM":"","type":"","xiangDingFW":[{}]},"yiZhuID":"","bingLiID":"","zhuYuanID":"","muLuID":"","caoZuoMLID":"","yiZhuMC":"","muLuMXID":"","xiangMuID":"","shouFeiZHID":"","shouFeiXMID":"","shouFeiXMMC":"","ziFei":"","danJia":"","shuLiang":"","zuiDaSL":"","zuiShaoSL":"","ciShu":"","weiShouBZ":"","huLiZT":"","shouFeiSJ":"","shouFeiRYID":"","caoZuoZheID":"","xiuGaiSJ":"","yiZhuSJ":"","yiShiID":"","jiGouDM":""}],"ylYzmlVo":{"sfbz":"","zjlx":"","yiZhuMLID":"","fuMuLuID":"","canZhaoMLID":"","yiZhuLBDM":"","mingChen":"","pinYin":"","wuBi":"","zhuangTaiBZ":"","zhuYiSX":"","beiZhu":"","xuanZeZXM":"","shiFouDY":"","zhengChanZXQR":"","xianShiPX":"","caoZuoZID":"","caoZuoZXM":"","xiuGaiSJ":"","ziJieSNR":"","ziJieSLB":"","jianChaDD":"","tuiFeiCX":"","zhuChanDYKZ":"","zhiZhiBG":"","yiZhuBZ":"","shiFouHSQM":"","shiFouYSQM":"","shiFouTZ":"","zhiKaiDBSF":"","yiDongZJNR":"","geShiDM":"","chuBaoGaoSC":"","zhongYiBianZhengFY":"","zhuYuanXSPX":"","zhongYiLeiYZ":"","shouQuZYBZFY":"","zhongYiSYJS":""}},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.cuoWuDM | - | String | 错误代码（0=可以开具，1=拦截）
data.cuoWuXX | - | String | 错误信息
data.yiZhuMXVos | - | Array | 治疗医嘱明细列表
data.yiZhuMXVos.id | - | Integer | 
data.yiZhuMXVos.sheBaoDM | - | String | 
data.yiZhuMXVos.sheBaoDM2 | - | String | 
data.yiZhuMXVos.sheBaoSP | - | Object | 
data.yiZhuMXVos.sheBaoSP.diagnose | - | String | 诊断
data.yiZhuMXVos.sheBaoSP.itemID | - | Integer | 项目ID
data.yiZhuMXVos.sheBaoSP.limit | - | String | 限制范围
data.yiZhuMXVos.sheBaoSP.mingCheng | - | String | 项目名称
data.yiZhuMXVos.sheBaoSP.sheBaoBM | - | String | 社保编码
data.yiZhuMXVos.sheBaoSP.type | - | String | 类别:我知道了(1,I Know);提交审批(2,Submit);无(0, None)
data.yiZhuMXVos.sheBaoSP.xiangDingFW | - | Array | 限定范围
data.yiZhuMXVos.yiZhuID | - | Integer | 医嘱ID(yl_yzall.yzid)
data.yiZhuMXVos.bingLiID | - | Integer | 病历ID
data.yiZhuMXVos.zhuYuanID | - | Integer | 住院ID
data.yiZhuMXVos.muLuID | - | Integer | 目录ID
data.yiZhuMXVos.caoZuoMLID | - | Integer | 操作目录ID
data.yiZhuMXVos.yiZhuMC | - | String | 医嘱名称
data.yiZhuMXVos.muLuMXID | - | Integer | 目录明细ID
data.yiZhuMXVos.xiangMuID | - | Integer | 项目ID
data.yiZhuMXVos.shouFeiZHID | - | Integer | 收费组合ID
data.yiZhuMXVos.shouFeiXMID | - | Integer | 收费项目ID
data.yiZhuMXVos.shouFeiXMMC | - | String | 收费项目名称
data.yiZhuMXVos.ziFei | - | String | 自费
data.yiZhuMXVos.danJia | - | Number | 单价
data.yiZhuMXVos.shuLiang | - | Number | 数量
data.yiZhuMXVos.zuiDaSL | - | Integer | 最大数量
data.yiZhuMXVos.zuiShaoSL | - | Integer | 最少数量
data.yiZhuMXVos.ciShu | - | Integer | 次数
data.yiZhuMXVos.weiShouBZ | - | String | 未收标志
data.yiZhuMXVos.huLiZT | - | String | 护理状态
data.yiZhuMXVos.shouFeiSJ | - | String | 收费时间
data.yiZhuMXVos.shouFeiRYID | - | Integer | 收费人员ID
data.yiZhuMXVos.caoZuoZheID | - | Integer | 操作者ID
data.yiZhuMXVos.xiuGaiSJ | - | String | 修改时间
data.yiZhuMXVos.yiZhuSJ | - | String | 医嘱时间
data.yiZhuMXVos.yiShiID | - | Integer | 医师ID
data.yiZhuMXVos.jiGouDM | - | String | 机构代码
data.ylYzmlVo | - | Object | 
data.ylYzmlVo.sfbz | - | String | 
data.ylYzmlVo.zjlx | - | String | 
data.ylYzmlVo.yiZhuMLID | - | Integer | 医嘱目录ID
data.ylYzmlVo.fuMuLuID | - | Integer | 父目录ID
data.ylYzmlVo.canZhaoMLID | - | Integer | 参照目录ID
data.ylYzmlVo.yiZhuLBDM | - | String | 医嘱类别代码
data.ylYzmlVo.mingChen | - | String | 名称
data.ylYzmlVo.pinYin | - | String | 拼音
data.ylYzmlVo.wuBi | - | String | 五笔
data.ylYzmlVo.zhuangTaiBZ | - | String | 状态标志
data.ylYzmlVo.zhuYiSX | - | String | 注意事项
data.ylYzmlVo.beiZhu | - | String | 备注
data.ylYzmlVo.xuanZeZXM | - | String | 选择子项目
data.ylYzmlVo.shiFouDY | - | String | 是否打印
data.ylYzmlVo.zhengChanZXQR | - | String | 整单执行确认
data.ylYzmlVo.xianShiPX | - | Integer | 显示排序
data.ylYzmlVo.caoZuoZID | - | Integer | 操作者ID
data.ylYzmlVo.caoZuoZXM | - | String | 操作者姓名
data.ylYzmlVo.xiuGaiSJ | - | String | 修改时间
data.ylYzmlVo.ziJieSNR | - | String | 自解释内容
data.ylYzmlVo.ziJieSLB | - | String | 自解释类别
data.ylYzmlVo.jianChaDD | - | String | 检查地点
data.ylYzmlVo.tuiFeiCX | - | String | 退费撤销
data.ylYzmlVo.zhuChanDYKZ | - | String | 嘱单打印控制
data.ylYzmlVo.zhiZhiBG | - | String | 纸质报告
data.ylYzmlVo.yiZhuBZ | - | String | 医嘱标准
data.ylYzmlVo.shiFouHSQM | - | String | 是否护士签名
data.ylYzmlVo.shiFouYSQM | - | String | 是否医生签名
data.ylYzmlVo.shiFouTZ | - | String | 是否透支
data.ylYzmlVo.zhiKaiDBSF | - | String | 只开单不收费
data.ylYzmlVo.yiDongZJNR | - | String | 移动自解内容
data.ylYzmlVo.geShiDM | - | String | 格式代码
data.ylYzmlVo.chuBaoGaoSC | - | Number | 出报告时长
data.ylYzmlVo.zhongYiBianZhengFY | - | String | 收取中医辨证费用
data.ylYzmlVo.zhuYuanXSPX | - | Integer | 住院显示排序
data.ylYzmlVo.zhongYiLeiYZ | - | String | 中医类医嘱
data.ylYzmlVo.shouQuZYBZFY | - | String | 收取中医辨证费用
data.ylYzmlVo.zhongYiSYJS | - | String | 中医适宜技术
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_根据非中选药品ID获取对应的中选药品
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getZhongXuanYpByYpid?feiZhongXuanYPID=&yaoFangDM=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
feiZhongXuanYPID | - | Text | 是 | 非中选药品ID
yaoFangDM | - | Text | 是 | 药房代码
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_中医辩证信息
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getZhongYiBZXX?yaoPinID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
yaoPinID | - | Text | 是 | 药品ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"yaoPinMC":"","zhenDuanXX":[{"jiLuID":"","yaoPinID":"","zuHao":"","zhenDuanID":"","zhenDuanMC":"","beiZhu":"","zhuangTaiBZ":"","caoZuoZID":"","xiuGaiSJ":""}],"zhengZhuangXX":[{"jiLuID":"","yaoPinID":"","zuHao":"","zhengZhuangID":"","zhengZhuangMC":"","beiZhu":"","zhuangTaiBZ":"","caoZuoZID":"","xiuGaiSJ":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.yaoPinMC | - | String | 药品名称
data.zhenDuanXX | - | Array | 诊断信息
data.zhenDuanXX.jiLuID | - | Integer | 记录ID
data.zhenDuanXX.yaoPinID | - | Integer | 药品ID
data.zhenDuanXX.zuHao | - | Integer | 组号
data.zhenDuanXX.zhenDuanID | - | Integer | 诊断ID
data.zhenDuanXX.zhenDuanMC | - | String | 诊断名称
data.zhenDuanXX.beiZhu | - | String | 备注
data.zhenDuanXX.zhuangTaiBZ | - | String | 状态标志
data.zhenDuanXX.caoZuoZID | - | Integer | 操作者ID
data.zhenDuanXX.xiuGaiSJ | - | String | 修改时间
data.zhengZhuangXX | - | Array | 症状信息
data.zhengZhuangXX.jiLuID | - | Integer | 记录ID
data.zhengZhuangXX.yaoPinID | - | Integer | 药品ID
data.zhengZhuangXX.zuHao | - | Integer | 组号
data.zhengZhuangXX.zhengZhuangID | - | Integer | 症状ID
data.zhengZhuangXX.zhengZhuangMC | - | String | 症状名称
data.zhengZhuangXX.beiZhu | - | String | 备注
data.zhengZhuangXX.zhuangTaiBZ | - | String | 状态标志
data.zhengZhuangXX.caoZuoZID | - | Integer | 操作者ID
data.zhengZhuangXX.xiuGaiSJ | - | String | 修改时间
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_(健海1017)根据【住院流水号】和【医嘱开立开始、结束时间（可选）】查询住院医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getZhuYuanYzByZylsh?jieShuSJ=&kaiShiSJ=&zhuYuanLSH=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
jieShuSJ | - | Text | 否 | 结束时间(yyyy-MM-dd HH:mm:ss)
kaiShiSJ | - | Text | 否 | 开始时间(yyyy-MM-dd HH:mm:ss)
zhuYuanLSH | - | Text | 是 | 住院流水号
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"BASE_AUX_DRUG_FLAG":"","DISCHARGE_ORDER_FLAG":"","DOSE_WAY_CODE":"","DOSE_WAY_NAME":"","DRUG_ABBREV":"","DRUG_AMOUNT":"","DRUG_CODE":"","DRUG_DESCR":"","DRUG_FORM_CODE":"","DRUG_FORM_NAME":"","DRUG_NAME":"","DRUG_SPECIFICATIONS":"","DRUG_UNIT":"","DRUG_UNIT_PRICE":"","DRUG_USE_FREQUENCY_CODE":"","DRUG_USE_FREQUENCY_NAME":"","DRUG_USE_ONE_DOSAGE":"","DRUG_USE_ONE_DOSAGE_UNIT":"","DR_ENTRUST":"","INHOSP_NO":"","INHOSP_NUM":"","INHOSP_SERIAL_NO":"","NOTE":"","ORDER_BEGIN_DATE":"","ORDER_CATEG_CODE":"","ORDER_CATEG_NAME":"","ORDER_DURATION":"","ORDER_DURATION_UNIT":"","ORDER_END_DATE":"","ORDER_EXECUTE_DATE":"","ORDER_GROUP_NO":"","ORDER_ITEM_CODE":"","ORDER_ITEM_NAME":"","ORDER_ITEM_TYPE_CODE":"","ORDER_ITEM_TYPE_NAME":"","ORDER_NO":"","ORDER_OPEN_DEPT_CODE":"","ORDER_OPEN_DEPT_NAME":"","ORDER_OPEN_DR_CODE":"","ORDER_OPEN_DR_NAME":"","ORDER_ORDER_DATE":"","ORDER_PLAN_BEGIN_DATE":"","ORDER_PLAN_END_DATE":"","ORGAN_CODE":"","PAT_INDEX_NO":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.BASE_AUX_DRUG_FLAG | - | String | 主副药标志 0、非主药 1、主药
data.DISCHARGE_ORDER_FLAG | - | String | 出院医嘱标志 0、非出院医嘱 1、出院医嘱
data.DOSE_WAY_CODE | - | String | 用药途径代码
data.DOSE_WAY_NAME | - | String | 用药途径名称
data.DRUG_ABBREV | - | String | 药品简称
data.DRUG_AMOUNT | - | String | 药品数量
data.DRUG_CODE | - | String | 药品代码
data.DRUG_DESCR | - | String | 药品描述
data.DRUG_FORM_CODE | - | String | 药品剂型代码
data.DRUG_FORM_NAME | - | String | 药品剂型名称
data.DRUG_NAME | - | String | 药品名称
data.DRUG_SPECIFICATIONS | - | String | 药品规格
data.DRUG_UNIT | - | String | 药品单位
data.DRUG_UNIT_PRICE | - | String | 药品单价
data.DRUG_USE_FREQUENCY_CODE | - | String | 药品使用频次代码
data.DRUG_USE_FREQUENCY_NAME | - | String | 药品使用频次名称
data.DRUG_USE_ONE_DOSAGE | - | String | 药品使用次剂量
data.DRUG_USE_ONE_DOSAGE_UNIT | - | String | 药品使用次剂量单位
data.DR_ENTRUST | - | String | 医生嘱托
data.INHOSP_NO | - | String | 住院号
data.INHOSP_NUM | - | Integer | 住院次数
data.INHOSP_SERIAL_NO | - | String | 住院流水号
data.NOTE | - | String | 备注
data.ORDER_BEGIN_DATE | - | String | 医嘱开始日期
data.ORDER_CATEG_CODE | - | String | 医嘱类别代码，1=长期医嘱，2=临时医嘱，9=其他
data.ORDER_CATEG_NAME | - | String | 医嘱类别名称
data.ORDER_DURATION | - | String | 医嘱持续时间
data.ORDER_DURATION_UNIT | - | String | 医嘱持续时间单位
data.ORDER_END_DATE | - | String | 医嘱结束日期
data.ORDER_EXECUTE_DATE | - | String | 医嘱执行日期
data.ORDER_GROUP_NO | - | String | 医嘱组号
data.ORDER_ITEM_CODE | - | String | 医嘱项目代码
data.ORDER_ITEM_NAME | - | String | 医嘱项目名称
data.ORDER_ITEM_TYPE_CODE | - | String | 医嘱项目类型代码
data.ORDER_ITEM_TYPE_NAME | - | String | 医嘱项目类型名称
data.ORDER_NO | - | String | 医嘱号
data.ORDER_OPEN_DEPT_CODE | - | String | 医嘱开立科室代码
data.ORDER_OPEN_DEPT_NAME | - | String | 医嘱开立科室名称
data.ORDER_OPEN_DR_CODE | - | String | 医嘱开立医生工号
data.ORDER_OPEN_DR_NAME | - | String | 医嘱开立医生姓名
data.ORDER_ORDER_DATE | - | String | 医嘱开立日期
data.ORDER_PLAN_BEGIN_DATE | - | String | 医嘱计划开始日期
data.ORDER_PLAN_END_DATE | - | String | 医嘱计划结束日期
data.ORGAN_CODE | - | String | 组织机构代码
data.PAT_INDEX_NO | - | String | 患者索引号
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/根据病历ID查询转床记录和病人信息
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getZhuanChuangJL?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"ezyblbrVo":{"idb":"","zhuYuanHao":"","xingMing":"","xingBie":"","chuShengRQ":"","bingQuDM":"","zhuanKeDM":"","chuangWeiHao":"","ruYuanRQ":"","chuYuanRQ":"","geShiDM":"","zhuangTaiBZ":"","caoZuoZhe":"","xiuGaiSJ":"","ruYuanZD":"","menZhenHao":"","bingLiID":"","caoZuoZheID":"","bingQuRYRQ":"","bingQuCYRQ":"","ruYuanJLRQ":"","shouCiJLRQ":"","beiZhu":"","liYuanZT":"","zhuanGuiDW":"","zhuanGuiLY":"","guiDangBZ":"","zhuanKeID":"","riJianSS":"","riJianSSXGRY":"","riJianSSXGSJ":"","siWangSJ":"","ruYuanZKID":"","ruYuanBQID":"","ruYuanCWH":"","bingQuID":"","xinBanBS":"","fuZhuJCZT":"","fuZhuJCSJ":""},"dangRiZbqJls":[{"beiZhu":"","bingLiId":"","bingQuId":"","bingQuRuYuanSJ":"","bingRenXM":"","caoZuoZheId":"","chuangWeiHao":"","jiZhangXE":"","jiZhangXEZJ":"","muBiaoBqId":"","muBiaoCwHao":"","muBiaoZkId":"","ruYuanSJ":"","zhiLiaoZuId":"","zhuYuanHao":"","zhuYuanId":"","zhuanChuangSJ":"","zhuanChuangShiJian2":"","zhuanKeId":"","zhuangTaiBZ":{"daiMa":"","mingCheng":""},"ziFuTZJE":"","ziFuYJK":"","zongJiZhangJE":"","zongJianMianJE":"","zongZiFJinE":"","zongZiLiJinE":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.ezyblbrVo | - | Object | 
data.ezyblbrVo.idb | - | Integer | 
data.ezyblbrVo.zhuYuanHao | - | String | 住院号
data.ezyblbrVo.xingMing | - | String | 姓名
data.ezyblbrVo.xingBie | - | String | 性别
data.ezyblbrVo.chuShengRQ | - | String | 出生日期
data.ezyblbrVo.bingQuDM | - | String | 病区代码
data.ezyblbrVo.zhuanKeDM | - | String | 专科代码
data.ezyblbrVo.chuangWeiHao | - | String | 床位号
data.ezyblbrVo.ruYuanRQ | - | String | 入院日期
data.ezyblbrVo.chuYuanRQ | - | String | 出院日期
data.ezyblbrVo.geShiDM | - | String | 格式代码
data.ezyblbrVo.zhuangTaiBZ | - | String | 状态标志
data.ezyblbrVo.caoZuoZhe | - | String | 操作者
data.ezyblbrVo.xiuGaiSJ | - | String | 修改时间
data.ezyblbrVo.ruYuanZD | - | String | 入院诊断
data.ezyblbrVo.menZhenHao | - | String | 门诊号
data.ezyblbrVo.bingLiID | - | Integer | 病历id
data.ezyblbrVo.caoZuoZheID | - | Integer | 操作者id
data.ezyblbrVo.bingQuRYRQ | - | String | 病区入院日期
data.ezyblbrVo.bingQuCYRQ | - | String | 病区出院日期
data.ezyblbrVo.ruYuanJLRQ | - | String | 入院记录日期
data.ezyblbrVo.shouCiJLRQ | - | String | 首次记录日期
data.ezyblbrVo.beiZhu | - | String | 备注
data.ezyblbrVo.liYuanZT | - | String | 离院状态
data.ezyblbrVo.zhuanGuiDW | - | String | 转归单位
data.ezyblbrVo.zhuanGuiLY | - | String | 转归理由
data.ezyblbrVo.guiDangBZ | - | String | 归档标志
data.ezyblbrVo.zhuanKeID | - | Integer | 专科id
data.ezyblbrVo.riJianSS | - | String | 日间手术 0/null非日间没有进过日间；1日间；2退出日间
data.ezyblbrVo.riJianSSXGRY | - | Integer | 日间手术修改人员
data.ezyblbrVo.riJianSSXGSJ | - | String | 日间手术修改时间
data.ezyblbrVo.siWangSJ | - | String | 死亡时间
data.ezyblbrVo.ruYuanZKID | - | Integer | 入院专科id
data.ezyblbrVo.ruYuanBQID | - | Integer | 入院病区id
data.ezyblbrVo.ruYuanCWH | - | String | 入院床位号
data.ezyblbrVo.bingQuID | - | Integer | 病区id
data.ezyblbrVo.xinBanBS | - | String | 新版标示
data.ezyblbrVo.fuZhuJCZT | - | String | 辅助检查状态
data.ezyblbrVo.fuZhuJCSJ | - | String | 辅助检查时间
data.dangRiZbqJls | - | Array | 当天转病区记录
data.dangRiZbqJls.beiZhu | - | String | 备注
data.dangRiZbqJls.bingLiId | - | Integer | 病历ID
data.dangRiZbqJls.bingQuId | - | Integer | 病区ID
data.dangRiZbqJls.bingQuRuYuanSJ | - | String | 病区入院时间
data.dangRiZbqJls.bingRenXM | - | String | 病人姓名
data.dangRiZbqJls.caoZuoZheId | - | Integer | 操作者ID
data.dangRiZbqJls.chuangWeiHao | - | String | 床位号
data.dangRiZbqJls.jiZhangXE | - | Number | 记账限额
data.dangRiZbqJls.jiZhangXEZJ | - | Number | 记账限额追加
data.dangRiZbqJls.muBiaoBqId | - | Integer | 目标病区ID
data.dangRiZbqJls.muBiaoCwHao | - | String | 目标床位号
data.dangRiZbqJls.muBiaoZkId | - | Integer | 目标专科ID
data.dangRiZbqJls.ruYuanSJ | - | String | 入院时间
data.dangRiZbqJls.zhiLiaoZuId | - | Integer | 治疗组ID
data.dangRiZbqJls.zhuYuanHao | - | String | 住院号
data.dangRiZbqJls.zhuYuanId | - | Integer | 住院ID
data.dangRiZbqJls.zhuanChuangSJ | - | String | 转床时间
data.dangRiZbqJls.zhuanChuangShiJian2 | - | String | 转床时间2
data.dangRiZbqJls.zhuanKeId | - | Integer | 专科ID
data.dangRiZbqJls.zhuangTaiBZ | - | Object | 
data.dangRiZbqJls.zhuangTaiBZ.daiMa | - | String | 代码
data.dangRiZbqJls.zhuangTaiBZ.mingCheng | - | String | 名称
data.dangRiZbqJls.ziFuTZJE | - | Number | 自负透支总额
data.dangRiZbqJls.ziFuYJK | - | Number | 自负预交款
data.dangRiZbqJls.zongJiZhangJE | - | Number | 总记账金额
data.dangRiZbqJls.zongJianMianJE | - | Number | 总减免金额
data.dangRiZbqJls.zongZiFJinE | - | Number | 总自负金额
data.dangRiZbqJls.zongZiLiJinE | - | Number | 总自理金额
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/根据ID获取综合医嘱模板目录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMlByID?moBanID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
moBanID | - | Text | 是 | 模板ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"moBanID":"","mingCheng":"","pinYin":"","wuBi":"","leiBie":"","guanLianID":"","caoZuoZhe":"","zhuangTaiBZ":"","xiuGaiSJ":"","xuHao":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.moBanID | - | Integer | 模板ID
data.mingCheng | - | String | 名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
data.leiBie | - | String | 类别 1全院，2病区，3专科，4个人，与关联ID对照
data.guanLianID | - | Integer | 关联ID
data.caoZuoZhe | - | Integer | 操作者
data.zhuangTaiBZ | - | String | 状态标志
data.xiuGaiSJ | - | String | 修改时间
data.xuHao | - | Integer | 序号
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/根据类别获取综合医嘱模板目录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMlByLb?moBanLB=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
moBanLB | - | Text | 是 | 模板类别(1全院，3专科，4个人)
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"moBanMXID":"","moBanID":"","xuHao":"","zuHao":"","zuHaoTXT":"","leiBie":"","xiangMuID":"","yaoFangDM":"","mingCheng":"","danJia":"","guiGe":"","jiXing":"","yiCiYL":"","jiLiangDW":"","zhiXingFF":"","zhiXingPL":"","geiYaoSJ":"","teShuYF":"","chiXuTS":"","yiZhuLB":"","shouFeiSL":"","shouFeiCS":"","danWei":"","zhuangTaiBZ":"","caoZuoZhe":"","xiuGaiSJ":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.moBanMXID | - | Integer | 模板明细ID
data.moBanID | - | Integer | 模板ID
data.xuHao | - | Integer | 序号
data.zuHao | - | Integer | 组号
data.zuHaoTXT | - | String | 组号TXT
data.leiBie | - | String | 类别 1=治疗，2=饮食，3=药品，4=嘱托
data.xiangMuID | - | String | 项目ID 药品为药品ID，治疗为z+医嘱目录ID，饮食为y+饮食代码，嘱托是t6712
data.yaoFangDM | - | String | 药房代码
data.mingCheng | - | String | 名称
data.danJia | - | Number | 单价
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingFF | - | String | 执行方法
data.zhiXingPL | - | String | 执行频率
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.chiXuTS | - | Integer | 持续天数
data.yiZhuLB | - | String | 医嘱类别（ 1 西药；2 中成药；3 草药；）
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.danWei | - | String | 单位
data.zhuangTaiBZ | - | String | 状态标志
data.caoZuoZhe | - | Integer | 操作者
data.xiuGaiSJ | - | String | 修改时间
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/根据综合医嘱模板ID查模板明细
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMx?moBanID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
moBanID | - | Text | 是 | 模板ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"moBanMXID":"","moBanID":"","xuHao":"","zuHao":"","zuHaoTXT":"","leiBie":"","xiangMuID":"","yaoFangDM":"","mingCheng":"","danJia":"","guiGe":"","jiXing":"","yiCiYL":"","jiLiangDW":"","zhiXingFF":"","zhiXingPL":"","geiYaoSJ":"","teShuYF":"","chiXuTS":"","yiZhuLB":"","shouFeiSL":"","shouFeiCS":"","danWei":"","zhuangTaiBZ":"","caoZuoZhe":"","xiuGaiSJ":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.moBanMXID | - | Integer | 模板明细ID
data.moBanID | - | Integer | 模板ID
data.xuHao | - | Integer | 序号
data.zuHao | - | Integer | 组号
data.zuHaoTXT | - | String | 组号TXT
data.leiBie | - | String | 类别 1=治疗，2=饮食，3=药品，4=嘱托
data.xiangMuID | - | String | 项目ID 药品为药品ID，治疗为z+医嘱目录ID，饮食为y+饮食代码，嘱托是t6712
data.yaoFangDM | - | String | 药房代码
data.mingCheng | - | String | 名称
data.danJia | - | Number | 单价
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingFF | - | String | 执行方法
data.zhiXingPL | - | String | 执行频率
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.chiXuTS | - | Integer | 持续天数
data.yiZhuLB | - | String | 医嘱类别（ 1 西药；2 中成药；3 草药；）
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.danWei | - | String | 单位
data.zhuangTaiBZ | - | String | 状态标志
data.caoZuoZhe | - | Integer | 操作者
data.xiuGaiSJ | - | String | 修改时间
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/医嘱界面初始化
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/initAdvice

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"age":"","ageNumber":"","biHuanZS":"","bingQuZDYM":"","bingRenXZQH":"","bmi":"","buLiangSJSB":"","caoYaoJF":[{"beiZhu":"","caoZuoZheID":"","fangFaDM":"","fangFaMC":"","jianShuiLiang":"","klyf":"","leiBie":"","pinYin":"","shiBieBS":"","shuChuPX":"","shuRuPX":"","wuBi":"","xiuGaiSJ":"","yinPianCF":"","yinPianYZ":"","zhuangTaiBZ":""}],"caoYaoPL":[{"yingYongDM":"","leiBie":"","daiMa":"","mingCheng":"","pinYin":"","wuBi":"","beiZhu":"","paiXu":"","daiMaCD":"","mingChengCD":"","fuJiaSX":"","neiBuLB":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","caoZuoZheID":"","xiuGaiSJ":"","nengFouXG":"","yiLiaoJGDM":"","caoZuoZhe":""}],"caoYaoYF":[{"yingYongDM":"","leiBie":"","daiMa":"","mingCheng":"","pinYin":"","wuBi":"","beiZhu":"","paiXu":"","daiMaCD":"","mingChengCD":"","fuJiaSX":"","neiBuLB":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","caoZuoZheID":"","xiuGaiSJ":"","nengFouXG":"","yiLiaoJGDM":"","caoZuoZhe":""}],"existFuGaoZC":"","existZhengGaoZC":"","ezyblbrVo":{"idb":"","zhuYuanHao":"","xingMing":"","xingBie":"","chuShengRQ":"","bingQuDM":"","zhuanKeDM":"","chuangWeiHao":"","ruYuanRQ":"","chuYuanRQ":"","geShiDM":"","zhuangTaiBZ":"","caoZuoZhe":"","xiuGaiSJ":"","ruYuanZD":"","menZhenHao":"","bingLiID":"","caoZuoZheID":"","bingQuRYRQ":"","bingQuCYRQ":"","ruYuanJLRQ":"","shouCiJLRQ":"","beiZhu":"","liYuanZT":"","zhuanGuiDW":"","zhuanGuiLY":"","guiDangBZ":"","zhuanKeID":"","riJianSS":"","riJianSSXGRY":"","riJianSSXGSJ":"","siWangSJ":"","ruYuanZKID":"","ruYuanBQID":"","ruYuanCWH":"","bingQuID":"","xinBanBS":"","fuZhuJCZT":"","fuZhuJCSJ":""},"ganRanZKBZ":"","gongZhongDM":"","guoJiaMaKG":"","hospitalAreaByIp":"","huanZheYZ360":"","huiMeiZK":"","iszhiXingZR":"","muRuKCL":"","ruYuanZBZXXNBQID":"","shangBaoBLSJ":"","sheBaoXLZFYPs":[{"yaoFangDM":"","zhuShe":"","zhuanKeMC":"","id":"","zuHao":"","yiZhuLB":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","jiLiang":"","baoZhuangLiang":"","shuLiang":"","ciShu":"","yiCiYL":"","pinLv":"","fangFa":"","geiYaoSJ":"","teShuYF":"","yiShengYHID":"","leiBie":"","xiuGaiSJ":"","paiXu":"","jiLiangDW":"","muBanMC":"","pinYin":"","wuBi":"","zhuanKeID":"","shenHeZT":"","shenHeRYID":"","shsj":"","beiZhu":"","zhuanHuanYZLB":"","leiXing":"","mrcs":"","jiaShuiLiang":"","cypl":"","fyff":"","fenZhuangQYPID":"","zhuangTaiBZ":"","caoZuoZhe":"","shenHeRYXM":"","chiXuTS":"","kaiShiSJ":"","danWei":"","guiGe":"","jiXing":"","shouJia":"","yaoPinLB":"","sheBaoDM":"","maZuiYaopin":"","guanLiLX":"","kongZhiJB":"","chanDiMC":"","guoTanYPMC":""}],"shouShuTZDs":[{"daiMa":"","mingCheng":""}],"teShuKSSCFQX":"","yaoPinSMS":"","yiShengZKZYKS":"","zhiWuDM":"","zhuYuanKT":"","yongYaoPL":[{"beiZhu":"","caoZuoZheID":"","jianGeTS":"","leiBie":"","menZhenPX":"","pinLuDM":"","pinLuMC":"","pinYin":"","sheBaoBM":"","shuChuPX":"","shuRuPX":"","wuBi":"","xiuGaiSJ":"","yiTianCS":"","zhuangTaiBZ":""}],"puTaoTangCDMLIDs":[{}],"xueTangPL":[{"yingYongDM":"","leiBie":"","daiMa":"","mingCheng":"","pinYin":"","wuBi":"","beiZhu":"","paiXu":"","daiMaCD":"","mingChengCD":"","fuJiaSX":"","neiBuLB":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","caoZuoZheID":"","xiuGaiSJ":"","nengFouXG":"","yiLiaoJGDM":"","caoZuoZhe":""}],"geiYaoSJ":[{"geiYaoSJDM":"","geiYaoSJMC":""}],"caoYaoTSJF":[{"beiZhu":"","caoZuoZheID":"","fangFaDM":"","fangFaMC":"","jianShuiLiang":"","klyf":"","leiBie":"","pinYin":"","shiBieBS":"","shuChuPX":"","shuRuPX":"","wuBi":"","xiuGaiSJ":"","yinPianCF":"","yinPianYZ":"","zhuangTaiBZ":""}],"caoYaoJYFF":[{"beiZhu":"","caoZuoZheID":"","fangFaDM":"","fangFaMC":"","jianShuiLiang":"","klyf":"","leiBie":"","pinYin":"","shiBieBS":"","shuChuPX":"","shuRuPX":"","wuBi":"","xiuGaiSJ":"","yinPianCF":"","yinPianYZ":"","zhuangTaiBZ":""}],"feiCaoYaoYYFF":[{"beiZhu":"","caoZuoZheID":"","fangFaDM":"","fangFaMC":"","jianShuiLiang":"","klyf":"","leiBie":"","pinYin":"","shiBieBS":"","shuChuPX":"","shuRuPX":"","wuBi":"","xiuGaiSJ":"","yinPianCF":"","yinPianYZ":"","zhuangTaiBZ":""}],"biaoTiGXH":{"geXingHuaID":"","fuGeXHID":"","yiShengYHID":"","yeWuDM":"","geXingHuaNR":"","paiXu":"","caoZuoZheID":"","chuangJianSJ":""},"inPatientVo":{"banZuDM":"","bingAnHao":"","bingLiID":"","bingQuChuYuanSJ":"","bingQuID":"","bingQuRuYuanSJ":"","bingRenBH":"","bingRenXM":"","caiWuRuYuanSJ":"","caoZuoZheID":"","chuShengRQ":"","chuShiHuaSJ":"","chuYuanSJ":"","chuangWeiHao":"","danWeiDM":"","danWeiMC":"","guoJiDM":"","h1N1":"","huLiJB":"","huZhao":"","hunYinZK":"","jiBingID":"","jiGuanDM":"","jiaShuDH":"","jiaShuGX":"","jiaShuXM":"","jieSuanDM":"","jieSuanLX":"","jieSuanSJ":"","lianXiDH":"","lianXiDZ":"","minZuDM":"","qiTaZJH":"","riJianSS":"","ruYuanQK":"","ruYuanYQ":"","ruYuanZD":"","shenFenZH":"","shouCiRYSJ":"","xingBie":"","xiuGaiRYID":"","xiuGaiSJ":"","yuZhuYuan":"","zhenDuanDM":"","zhiLiaoZuID":"","zhiYeDM":"","zhongTuJS":"","zhuGuanHSID":"","zhuGuanYSID":"","zhuYuanHao":"","zhuYuanID":"","zhuYuanID_MOTHER":"","zhuanKeID":"","zhuangTaiBZ":"","ziFuYE":""},"chuFangQX":"","maZuiCFQX":"","puTongJiZLYWQX":"","xianZhiJiZLYWQX":"","duXingYPCFQX":"","fangSheYPCFQX":"","zhongChengYaoCFQX":"","zhiXingBQs":[{"daiMa":"","mingCheng":""}],"yaoFangDMs":[{"leiBie":"","moRenDY":"","moRenTS":"","moRenWeiZhiDM":"","weiZhiDM":"","yaoFangDM":"","yiZhuLB":"","youXianCK":"","youXianJB":"","zhuanKeDY":"","zhuangTaiBZ":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.age | - | String | 年龄，带单位，如：XX岁、XX月、XX天
data.ageNumber | - | Integer | 年龄，数字，单位为岁
data.biHuanZS | - | String | 闭环展示
data.bingQuZDYM | - | String | 病区自定义码
data.bingRenXZQH | - | String | 病人行政区划
data.bmi | - | Number | 
data.buLiangSJSB | - | String | 不良事件上报
data.caoYaoJF | - | Array | 草药煎法 2025-03
data.caoYaoJF.beiZhu | - | String | 备注
data.caoYaoJF.caoZuoZheID | - | Integer | (操作者id)修改人员id
data.caoYaoJF.fangFaDM | - | String | 方法代码
data.caoYaoJF.fangFaMC | - | String | 方法名称
data.caoYaoJF.jianShuiLiang | - | String | 煎水量
data.caoYaoJF.klyf | - | String | 
data.caoYaoJF.leiBie | - | String | 类别: 具体可见ddlbn(lb= '0013')
data.caoYaoJF.pinYin | - | String | 拼音码
data.caoYaoJF.shiBieBS | - | Number | 识别标识:1联方 6药盘
data.caoYaoJF.shuChuPX | - | Number | 输出排序
data.caoYaoJF.shuRuPX | - | Number | 输入排序
data.caoYaoJF.wuBi | - | String | 五笔码
data.caoYaoJF.xiuGaiSJ | - | String | 修改时间
data.caoYaoJF.yinPianCF | - | String | 饮片处方
data.caoYaoJF.yinPianYZ | - | String | 饮片医嘱
data.caoYaoJF.zhuangTaiBZ | - | String | 状态标志
data.caoYaoPL | - | Array | 草药频率 2025-03
data.caoYaoPL.yingYongDM | - | String | 应用代码
data.caoYaoPL.leiBie | - | String | 类别
data.caoYaoPL.daiMa | - | String | 代码
data.caoYaoPL.mingCheng | - | String | 名称
data.caoYaoPL.pinYin | - | String | 拼音
data.caoYaoPL.wuBi | - | String | 五笔
data.caoYaoPL.beiZhu | - | String | 备注
data.caoYaoPL.paiXu | - | Integer | 排序
data.caoYaoPL.daiMaCD | - | Integer | 代码长度
data.caoYaoPL.mingChengCD | - | Integer | 名称长度
data.caoYaoPL.fuJiaSX | - | String | 附加属性
data.caoYaoPL.neiBuLB | - | String | 内部类别
data.caoYaoPL.zhuangTaiBZ | - | String | 状态标志
data.caoYaoPL.zhuangTaiBZMC | - | String | 状态标志名称
data.caoYaoPL.caoZuoZheID | - | Integer | 操作者ID
data.caoYaoPL.xiuGaiSJ | - | String | 修改时间
data.caoYaoPL.nengFouXG | - | Integer | 能否修改
data.caoYaoPL.yiLiaoJGDM | - | String | 医疗机构代码
data.caoYaoPL.caoZuoZhe | - | String | 操作者
data.caoYaoYF | - | Array | 草药用法 2025-03
data.caoYaoYF.yingYongDM | - | String | 应用代码
data.caoYaoYF.leiBie | - | String | 类别
data.caoYaoYF.daiMa | - | String | 代码
data.caoYaoYF.mingCheng | - | String | 名称
data.caoYaoYF.pinYin | - | String | 拼音
data.caoYaoYF.wuBi | - | String | 五笔
data.caoYaoYF.beiZhu | - | String | 备注
data.caoYaoYF.paiXu | - | Integer | 排序
data.caoYaoYF.daiMaCD | - | Integer | 代码长度
data.caoYaoYF.mingChengCD | - | Integer | 名称长度
data.caoYaoYF.fuJiaSX | - | String | 附加属性
data.caoYaoYF.neiBuLB | - | String | 内部类别
data.caoYaoYF.zhuangTaiBZ | - | String | 状态标志
data.caoYaoYF.zhuangTaiBZMC | - | String | 状态标志名称
data.caoYaoYF.caoZuoZheID | - | Integer | 操作者ID
data.caoYaoYF.xiuGaiSJ | - | String | 修改时间
data.caoYaoYF.nengFouXG | - | Integer | 能否修改
data.caoYaoYF.yiLiaoJGDM | - | String | 医疗机构代码
data.caoYaoYF.caoZuoZhe | - | String | 操作者
data.existFuGaoZC | - | String | 科室里有无【副高】及以上的职称，1=有
data.existZhengGaoZC | - | String | 科室里有无【正高】及以上的职称，1=有
data.ezyblbrVo | - | Object | 
data.ezyblbrVo.idb | - | Integer | 
data.ezyblbrVo.zhuYuanHao | - | String | 住院号
data.ezyblbrVo.xingMing | - | String | 姓名
data.ezyblbrVo.xingBie | - | String | 性别
data.ezyblbrVo.chuShengRQ | - | String | 出生日期
data.ezyblbrVo.bingQuDM | - | String | 病区代码
data.ezyblbrVo.zhuanKeDM | - | String | 专科代码
data.ezyblbrVo.chuangWeiHao | - | String | 床位号
data.ezyblbrVo.ruYuanRQ | - | String | 入院日期
data.ezyblbrVo.chuYuanRQ | - | String | 出院日期
data.ezyblbrVo.geShiDM | - | String | 格式代码
data.ezyblbrVo.zhuangTaiBZ | - | String | 状态标志
data.ezyblbrVo.caoZuoZhe | - | String | 操作者
data.ezyblbrVo.xiuGaiSJ | - | String | 修改时间
data.ezyblbrVo.ruYuanZD | - | String | 入院诊断
data.ezyblbrVo.menZhenHao | - | String | 门诊号
data.ezyblbrVo.bingLiID | - | Integer | 病历id
data.ezyblbrVo.caoZuoZheID | - | Integer | 操作者id
data.ezyblbrVo.bingQuRYRQ | - | String | 病区入院日期
data.ezyblbrVo.bingQuCYRQ | - | String | 病区出院日期
data.ezyblbrVo.ruYuanJLRQ | - | String | 入院记录日期
data.ezyblbrVo.shouCiJLRQ | - | String | 首次记录日期
data.ezyblbrVo.beiZhu | - | String | 备注
data.ezyblbrVo.liYuanZT | - | String | 离院状态
data.ezyblbrVo.zhuanGuiDW | - | String | 转归单位
data.ezyblbrVo.zhuanGuiLY | - | String | 转归理由
data.ezyblbrVo.guiDangBZ | - | String | 归档标志
data.ezyblbrVo.zhuanKeID | - | Integer | 专科id
data.ezyblbrVo.riJianSS | - | String | 日间手术 0/null非日间没有进过日间；1日间；2退出日间
data.ezyblbrVo.riJianSSXGRY | - | Integer | 日间手术修改人员
data.ezyblbrVo.riJianSSXGSJ | - | String | 日间手术修改时间
data.ezyblbrVo.siWangSJ | - | String | 死亡时间
data.ezyblbrVo.ruYuanZKID | - | Integer | 入院专科id
data.ezyblbrVo.ruYuanBQID | - | Integer | 入院病区id
data.ezyblbrVo.ruYuanCWH | - | String | 入院床位号
data.ezyblbrVo.bingQuID | - | Integer | 病区id
data.ezyblbrVo.xinBanBS | - | String | 新版标示
data.ezyblbrVo.fuZhuJCZT | - | String | 辅助检查状态
data.ezyblbrVo.fuZhuJCSJ | - | String | 辅助检查时间
data.ganRanZKBZ | - | String | 感染专科标志，1=是
data.gongZhongDM | - | String | 工种代码，例如0011=主任医师，0012=副主任医师
data.guoJiaMaKG | - | String | 国家码开关，0=关，1=显示点点点，2=直接显示
data.hospitalAreaByIp | - | String | 根据ip获取院区代码
data.huanZheYZ360 | - | String | 360患者视图
data.huiMeiZK | - | String | 惠每质控
data.iszhiXingZR | - | String | 
data.muRuKCL | - | String | 母乳库存量，xxml
data.ruYuanZBZXXNBQID | - | Integer | 入院准备中心虚拟病区ID
data.shangBaoBLSJ | - | String | 上报不良事件（直接对某条医嘱上报）
data.sheBaoXLZFYPs | - | Array | 社保限量支付药品
data.sheBaoXLZFYPs.yaoFangDM | - | String | 药房代码
data.sheBaoXLZFYPs.zhuShe | - | String | 
data.sheBaoXLZFYPs.zhuanKeMC | - | String | 
data.sheBaoXLZFYPs.id | - | Integer | 模板ID
data.sheBaoXLZFYPs.zuHao | - | Integer | 组号
data.sheBaoXLZFYPs.yiZhuLB | - | String | 医嘱类别
data.sheBaoXLZFYPs.yaoPinID | - | Integer | 药品ID
data.sheBaoXLZFYPs.fenLeiMa | - | String | 分类码
data.sheBaoXLZFYPs.mingCheng | - | String | 药品名称
data.sheBaoXLZFYPs.jiLiang | - | String | 剂量
data.sheBaoXLZFYPs.baoZhuangLiang | - | Integer | 包装量
data.sheBaoXLZFYPs.shuLiang | - | Integer | 数量
data.sheBaoXLZFYPs.ciShu | - | Integer | 次数
data.sheBaoXLZFYPs.yiCiYL | - | Number | 一次用量
data.sheBaoXLZFYPs.pinLv | - | String | 频率
data.sheBaoXLZFYPs.fangFa | - | String | 方法
data.sheBaoXLZFYPs.geiYaoSJ | - | String | 给药时间
data.sheBaoXLZFYPs.teShuYF | - | String | 特殊用法
data.sheBaoXLZFYPs.yiShengYHID | - | Integer | 医生ID
data.sheBaoXLZFYPs.leiBie | - | String | 类别
data.sheBaoXLZFYPs.xiuGaiSJ | - | String | 修改时间
data.sheBaoXLZFYPs.paiXu | - | Integer | 排序
data.sheBaoXLZFYPs.jiLiangDW | - | String | 剂量单位
data.sheBaoXLZFYPs.muBanMC | - | String | 模板名称
data.sheBaoXLZFYPs.pinYin | - | String | 拼音
data.sheBaoXLZFYPs.wuBi | - | String | 五笔
data.sheBaoXLZFYPs.zhuanKeID | - | Integer | 专科ID
data.sheBaoXLZFYPs.shenHeZT | - | String | 审核状态
data.sheBaoXLZFYPs.shenHeRYID | - | Integer | 审核人员ID
data.sheBaoXLZFYPs.shsj | - | String | 审核时间
data.sheBaoXLZFYPs.beiZhu | - | String | 备注
data.sheBaoXLZFYPs.zhuanHuanYZLB | - | String | 转换医嘱类别
data.sheBaoXLZFYPs.leiXing | - | String | 类型
data.sheBaoXLZFYPs.mrcs | - | Integer | 每日次数
data.sheBaoXLZFYPs.jiaShuiLiang | - | String | 加水量
data.sheBaoXLZFYPs.cypl | - | String | 草药频率
data.sheBaoXLZFYPs.fyff | - | String | 使用方法
data.sheBaoXLZFYPs.fenZhuangQYPID | - | Integer | 分装前药品ID
data.sheBaoXLZFYPs.zhuangTaiBZ | - | String | 状态标志（0暂停 1普通）
data.sheBaoXLZFYPs.caoZuoZhe | - | String | 操作者
data.sheBaoXLZFYPs.shenHeRYXM | - | String | 审核人员姓名
data.sheBaoXLZFYPs.chiXuTS | - | Integer | 持续天数
data.sheBaoXLZFYPs.kaiShiSJ | - | String | 开始时间
data.sheBaoXLZFYPs.danWei | - | String | 单位
data.sheBaoXLZFYPs.guiGe | - | String | 规格
data.sheBaoXLZFYPs.jiXing | - | String | 剂型
data.sheBaoXLZFYPs.shouJia | - | Number | 售价
data.sheBaoXLZFYPs.yaoPinLB | - | String | 药品类别(1=外购药，2=赠送药品)
data.sheBaoXLZFYPs.sheBaoDM | - | String | 社保代码
data.sheBaoXLZFYPs.maZuiYaopin | - | String | 麻醉药品
data.sheBaoXLZFYPs.guanLiLX | - | String | 管理类型
data.sheBaoXLZFYPs.kongZhiJB | - | String | 控制级别
data.sheBaoXLZFYPs.chanDiMC | - | String | 产地名称（厂家信息）
data.sheBaoXLZFYPs.guoTanYPMC | - | String | 国谈药品名称
data.shouShuTZDs | - | Array | 手术通知单
data.shouShuTZDs.daiMa | - | String | 代码
data.shouShuTZDs.mingCheng | - | String | 名称
data.teShuKSSCFQX | - | Boolean | 特殊抗生素处方权限
data.yaoPinSMS | - | String | 药品说明书
data.yiShengZKZYKS | - | String | 医生专科是否中医科室，1=是，0=否
data.zhiWuDM | - | String | 职务代码，例如0401=主任，0402=副主任
data.zhuYuanKT | - | String | 病人是否住院开通 1=开通
data.yongYaoPL | - | Array | 用药频率
data.yongYaoPL.beiZhu | - | String | 备注
data.yongYaoPL.caoZuoZheID | - | Integer | (操作者id)修改人员id
data.yongYaoPL.jianGeTS | - | Number | 间隔天数:间隔天数+1天范围内的次数
data.yongYaoPL.leiBie | - | String | 类别
data.yongYaoPL.menZhenPX | - | Number | 门诊排序
data.yongYaoPL.pinLuDM | - | String | 频率代码
data.yongYaoPL.pinLuMC | - | String | 频率名称
data.yongYaoPL.pinYin | - | String | 拼音码
data.yongYaoPL.sheBaoBM | - | Number | 社保编码
data.yongYaoPL.shuChuPX | - | Number | 输出排序
data.yongYaoPL.shuRuPX | - | Number | 输入排序
data.yongYaoPL.wuBi | - | String | 五笔码
data.yongYaoPL.xiuGaiSJ | - | String | 修改时间
data.yongYaoPL.yiTianCS | - | Number | 一天次数:一周天数，一周几次
data.yongYaoPL.zhuangTaiBZ | - | String | 状态标志
data.puTaoTangCDMLIDs | - | Array | 葡萄糖测定mlid
data.xueTangPL | - | Array | 血糖频率
data.xueTangPL.yingYongDM | - | String | 应用代码
data.xueTangPL.leiBie | - | String | 类别
data.xueTangPL.daiMa | - | String | 代码
data.xueTangPL.mingCheng | - | String | 名称
data.xueTangPL.pinYin | - | String | 拼音
data.xueTangPL.wuBi | - | String | 五笔
data.xueTangPL.beiZhu | - | String | 备注
data.xueTangPL.paiXu | - | Integer | 排序
data.xueTangPL.daiMaCD | - | Integer | 代码长度
data.xueTangPL.mingChengCD | - | Integer | 名称长度
data.xueTangPL.fuJiaSX | - | String | 附加属性
data.xueTangPL.neiBuLB | - | String | 内部类别
data.xueTangPL.zhuangTaiBZ | - | String | 状态标志
data.xueTangPL.zhuangTaiBZMC | - | String | 状态标志名称
data.xueTangPL.caoZuoZheID | - | Integer | 操作者ID
data.xueTangPL.xiuGaiSJ | - | String | 修改时间
data.xueTangPL.nengFouXG | - | Integer | 能否修改
data.xueTangPL.yiLiaoJGDM | - | String | 医疗机构代码
data.xueTangPL.caoZuoZhe | - | String | 操作者
data.geiYaoSJ | - | Array | 给药时间
data.geiYaoSJ.geiYaoSJDM | - | String | 给药时间代码
data.geiYaoSJ.geiYaoSJMC | - | String | 给药时间名称
data.caoYaoTSJF | - | Array | 草药特殊煎法
data.caoYaoTSJF.beiZhu | - | String | 备注
data.caoYaoTSJF.caoZuoZheID | - | Integer | (操作者id)修改人员id
data.caoYaoTSJF.fangFaDM | - | String | 方法代码
data.caoYaoTSJF.fangFaMC | - | String | 方法名称
data.caoYaoTSJF.jianShuiLiang | - | String | 煎水量
data.caoYaoTSJF.klyf | - | String | 
data.caoYaoTSJF.leiBie | - | String | 类别: 具体可见ddlbn(lb= '0013')
data.caoYaoTSJF.pinYin | - | String | 拼音码
data.caoYaoTSJF.shiBieBS | - | Number | 识别标识:1联方 6药盘
data.caoYaoTSJF.shuChuPX | - | Number | 输出排序
data.caoYaoTSJF.shuRuPX | - | Number | 输入排序
data.caoYaoTSJF.wuBi | - | String | 五笔码
data.caoYaoTSJF.xiuGaiSJ | - | String | 修改时间
data.caoYaoTSJF.yinPianCF | - | String | 饮片处方
data.caoYaoTSJF.yinPianYZ | - | String | 饮片医嘱
data.caoYaoTSJF.zhuangTaiBZ | - | String | 状态标志
data.caoYaoJYFF | - | Array | 草药煎药方法
data.caoYaoJYFF.beiZhu | - | String | 备注
data.caoYaoJYFF.caoZuoZheID | - | Integer | (操作者id)修改人员id
data.caoYaoJYFF.fangFaDM | - | String | 方法代码
data.caoYaoJYFF.fangFaMC | - | String | 方法名称
data.caoYaoJYFF.jianShuiLiang | - | String | 煎水量
data.caoYaoJYFF.klyf | - | String | 
data.caoYaoJYFF.leiBie | - | String | 类别: 具体可见ddlbn(lb= '0013')
data.caoYaoJYFF.pinYin | - | String | 拼音码
data.caoYaoJYFF.shiBieBS | - | Number | 识别标识:1联方 6药盘
data.caoYaoJYFF.shuChuPX | - | Number | 输出排序
data.caoYaoJYFF.shuRuPX | - | Number | 输入排序
data.caoYaoJYFF.wuBi | - | String | 五笔码
data.caoYaoJYFF.xiuGaiSJ | - | String | 修改时间
data.caoYaoJYFF.yinPianCF | - | String | 饮片处方
data.caoYaoJYFF.yinPianYZ | - | String | 饮片医嘱
data.caoYaoJYFF.zhuangTaiBZ | - | String | 状态标志
data.feiCaoYaoYYFF | - | Array | 非草药用药方法
data.feiCaoYaoYYFF.beiZhu | - | String | 备注
data.feiCaoYaoYYFF.caoZuoZheID | - | Integer | (操作者id)修改人员id
data.feiCaoYaoYYFF.fangFaDM | - | String | 方法代码
data.feiCaoYaoYYFF.fangFaMC | - | String | 方法名称
data.feiCaoYaoYYFF.jianShuiLiang | - | String | 煎水量
data.feiCaoYaoYYFF.klyf | - | String | 
data.feiCaoYaoYYFF.leiBie | - | String | 类别: 具体可见ddlbn(lb= '0013')
data.feiCaoYaoYYFF.pinYin | - | String | 拼音码
data.feiCaoYaoYYFF.shiBieBS | - | Number | 识别标识:1联方 6药盘
data.feiCaoYaoYYFF.shuChuPX | - | Number | 输出排序
data.feiCaoYaoYYFF.shuRuPX | - | Number | 输入排序
data.feiCaoYaoYYFF.wuBi | - | String | 五笔码
data.feiCaoYaoYYFF.xiuGaiSJ | - | String | 修改时间
data.feiCaoYaoYYFF.yinPianCF | - | String | 饮片处方
data.feiCaoYaoYYFF.yinPianYZ | - | String | 饮片医嘱
data.feiCaoYaoYYFF.zhuangTaiBZ | - | String | 状态标志
data.biaoTiGXH | - | Object | 
data.biaoTiGXH.geXingHuaID | - | Integer | 个性化ID
data.biaoTiGXH.fuGeXHID | - | Integer | 父个性化ID
data.biaoTiGXH.yiShengYHID | - | Integer | 医生用户ID
data.biaoTiGXH.yeWuDM | - | String | 业务代码
data.biaoTiGXH.geXingHuaNR | - | String | 个性化内容
data.biaoTiGXH.paiXu | - | Integer | 排序
data.biaoTiGXH.caoZuoZheID | - | Integer | 操作者ID
data.biaoTiGXH.chuangJianSJ | - | String | 创建时间
data.inPatientVo | - | Object | 
data.inPatientVo.banZuDM | - | String | BZDM
data.inPatientVo.bingAnHao | - | String | 病案号
data.inPatientVo.bingLiID | - | Integer | 病历ID
data.inPatientVo.bingQuChuYuanSJ | - | String | 病区出院时间
data.inPatientVo.bingQuID | - | Integer | 病区ID
data.inPatientVo.bingQuRuYuanSJ | - | String | 病区入院时间
data.inPatientVo.bingRenBH | - | String | 病人编号
data.inPatientVo.bingRenXM | - | String | 病人姓名
data.inPatientVo.caiWuRuYuanSJ | - | String | 财务入院时间
data.inPatientVo.caoZuoZheID | - | Integer | 操作者ID
data.inPatientVo.chuShengRQ | - | String | 出生日期
data.inPatientVo.chuShiHuaSJ | - | String | 初始化时间
data.inPatientVo.chuYuanSJ | - | String | 出院时间
data.inPatientVo.chuangWeiHao | - | String | 床位号
data.inPatientVo.danWeiDM | - | String | 单位代码
data.inPatientVo.danWeiMC | - | String | 单位代码
data.inPatientVo.guoJiDM | - | String | 国籍代码
data.inPatientVo.h1N1 | - | String | 
data.inPatientVo.huLiJB | - | String | 护理级别
data.inPatientVo.huZhao | - | String | 护照
data.inPatientVo.hunYinZK | - | String | 婚姻状况
data.inPatientVo.jiBingID | - | Integer | JBID
data.inPatientVo.jiGuanDM | - | String | 籍贯代码
data.inPatientVo.jiaShuDH | - | String | 家属电话
data.inPatientVo.jiaShuGX | - | String | 家属关系
data.inPatientVo.jiaShuXM | - | String | 家属姓名
data.inPatientVo.jieSuanDM | - | String | 结算代码
data.inPatientVo.jieSuanLX | - | String | 结算类型
data.inPatientVo.jieSuanSJ | - | String | 结算时间
data.inPatientVo.lianXiDH | - | String | 联系电话
data.inPatientVo.lianXiDZ | - | String | 联系地址
data.inPatientVo.minZuDM | - | String | 民族代码
data.inPatientVo.qiTaZJH | - | String | 其他证件号
data.inPatientVo.riJianSS | - | String | 日间病人
data.inPatientVo.ruYuanQK | - | String | 入院情况
data.inPatientVo.ruYuanYQ | - | String | 入院院区
data.inPatientVo.ruYuanZD | - | String | 入院诊断
data.inPatientVo.shenFenZH | - | String | 身份证号
data.inPatientVo.shouCiRYSJ | - | String | 首次入院时间
data.inPatientVo.xingBie | - | String | 病人性别
data.inPatientVo.xiuGaiRYID | - | Integer | 修改人员ID
data.inPatientVo.xiuGaiSJ | - | String | 修改时间
data.inPatientVo.yuZhuYuan | - | String | 预住院
data.inPatientVo.zhenDuanDM | - | String | 诊断代码
data.inPatientVo.zhiLiaoZuID | - | Integer | 治疗组ID
data.inPatientVo.zhiYeDM | - | String | 职业代码
data.inPatientVo.zhongTuJS | - | String | 中途结算
data.inPatientVo.zhuGuanHSID | - | Integer | 主管护士ID
data.inPatientVo.zhuGuanYSID | - | Integer | 主管医师ID
data.inPatientVo.zhuYuanHao | - | String | 住院号
data.inPatientVo.zhuYuanID | - | Integer | 住院ID
data.inPatientVo.zhuYuanID_MOTHER | - | Integer | 妈妈的住院ID
data.inPatientVo.zhuanKeID | - | Integer | 专科ID
data.inPatientVo.zhuangTaiBZ | - | String | 状态标志
data.inPatientVo.ziFuYE | - | Number | 自费余额
data.chuFangQX | - | Boolean | 处方权限
data.maZuiCFQX | - | Boolean | 麻醉处方权限
data.puTongJiZLYWQX | - | Boolean | 普通级肿瘤药物权限
data.xianZhiJiZLYWQX | - | Boolean | 限制级肿瘤药物权限
data.duXingYPCFQX | - | Boolean | 毒性药品处方权限
data.fangSheYPCFQX | - | Boolean | 放射药品处方权限
data.zhongChengYaoCFQX | - | Boolean | 中成药处方权
data.zhiXingBQs | - | Array | 执行病区列表
data.zhiXingBQs.daiMa | - | String | 代码
data.zhiXingBQs.mingCheng | - | String | 名称
data.yaoFangDMs | - | Array | 药房代码列表
data.yaoFangDMs.leiBie | - | String | 类别
data.yaoFangDMs.moRenDY | - | String | 默认单元
data.yaoFangDMs.moRenTS | - | String | 默认提示
data.yaoFangDMs.moRenWeiZhiDM | - | String | 默认位置代码
data.yaoFangDMs.weiZhiDM | - | String | 位置代码
data.yaoFangDMs.yaoFangDM | - | String | 药房代码
data.yaoFangDMs.yiZhuLB | - | String | 医嘱类别
data.yaoFangDMs.youXianCK | - | String | 优先窗口
data.yaoFangDMs.youXianJB | - | Integer | 优先级别
data.yaoFangDMs.zhuanKeDY | - | String | 专科单元
data.yaoFangDMs.zhuangTaiBZ | - | Integer | 状态标志
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/初始化清洁手术高危因素
```text
抗菌药品管理相关
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/initQingJieSsGwYs

#### 请求方式
> GET

#### Content-Type
> none

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"tiShi1":"","xiaoBiaoTi1":"","gaoWeiYSList":[{"daiMa":"","mingCheng":""}],"zhuRenQM":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.tiShi1 | - | String | 提示1
data.xiaoBiaoTi1 | - | String | 小标题1
data.gaoWeiYSList | - | Array | 高危因素列表
data.gaoWeiYSList.daiMa | - | String | 代码
data.gaoWeiYSList.mingCheng | - | String | 名称
data.zhuRenQM | - | String | 主任签名
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/初始化医嘱按时间间隔统计
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/initTongJiYzBySjjg

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"kaiShiSJ":"","jieShuSJ":"","shiJianJG":[{"code":"","name":"","pinYin":"","type":"","wuBi":""}],"leiXing":[{"code":"","name":"","pinYin":"","type":"","wuBi":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.shiJianJG | - | Array | 时间间隔
data.shiJianJG.code | - | String | 
data.shiJianJG.name | - | String | 
data.shiJianJG.pinYin | - | String | 
data.shiJianJG.type | - | String | 
data.shiJianJG.wuBi | - | String | 
data.leiXing | - | Array | 类型
data.leiXing.code | - | String | 
data.leiXing.name | - | String | 
data.leiXing.pinYin | - | String | 
data.leiXing.type | - | String | 
data.leiXing.wuBi | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/初始化微生物送检情况选项
```text
抗菌药品管理相关
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/initWeiShengWuSjQkXx

#### 请求方式
> GET

#### Content-Type
> none

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"daiMa":"","mingCheng":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.daiMa | - | String | 代码
data.mingCheng | - | String | 名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/初始化新生儿抗生素预防使用问卷
```text
抗菌药品管理相关
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/initXinShengErKssYtSyWq

#### 请求方式
> GET

#### Content-Type
> none

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"wenTi1MS":"","wenTi1":[{"daiMa":"","mingCheng":""}],"weiChanGWYSs":[{"daiMa":"","mingCheng":""}],"wenTi2MS":"","wenTi2":[{"daiMa":"","mingCheng":""}],"huanErLCBXs":[{"daiMa":"","mingCheng":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.wenTi1MS | - | String | 问题1描述
data.wenTi1 | - | Array | 是否具有围产高危因素
data.wenTi1.daiMa | - | String | 代码
data.wenTi1.mingCheng | - | String | 名称
data.weiChanGWYSs | - | Array | 围产高危因素列表
data.weiChanGWYSs.daiMa | - | String | 代码
data.weiChanGWYSs.mingCheng | - | String | 名称
data.wenTi2MS | - | String | 问题2描述
data.wenTi2 | - | Array | 是否患儿具有临床表现
data.wenTi2.daiMa | - | String | 代码
data.wenTi2.mingCheng | - | String | 名称
data.huanErLCBXs | - | Array | 患儿临床表现列表
data.huanErLCBXs.daiMa | - | String | 代码
data.huanErLCBXs.mingCheng | - | String | 名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_计算出院带药数量
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/jiSuanCYDYSL?jiLiangDW=&yaoFangDM=&yaoPinID=&yiCiYL=&yongYaoFF=&yongYaoPL=&yongYaoTS=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
jiLiangDW | - | Text | 是 | 剂量单位
yaoFangDM | - | Text | 是 | 药房代码
yaoPinID | - | Text | 是 | 药品ID
yiCiYL | - | Text | 是 | 一次用量
yongYaoFF | - | Text | 是 | 用药方法
yongYaoPL | - | Text | 是 | 用药频率
yongYaoTS | - | Text | 是 | 用药天数
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"shuLiang":"","xiaoXi":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.shuLiang | - | Number | 数量
data.xiaoXi | - | String | 消息
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_计算用药天数
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/jiSuanYYTS?jiLiangDW=&shuLiang=&yaoFangDM=&yaoPinID=&yiCiYL=&yongYaoFF=&yongYaoPL=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
jiLiangDW | - | Text | 是 | 剂量单位
shuLiang | - | Text | 是 | 数量
yaoFangDM | - | Text | 是 | 药房代码
yaoPinID | - | Text | 是 | 药品ID
yiCiYL | - | Text | 是 | 一次用量
yongYaoFF | - | Text | 是 | 用药方法
yongYaoPL | - | Text | 是 | 用药频率
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Number | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/临时医嘱执行
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/linShiYzZx

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"yiZhuList":"","bingLiID":"","zhuYuanID":"","bingQuID":"","zhuanKeID":"","zhiXingSJ":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
yiZhuList | - | String | 否 | 医嘱列表:yp+组号,zl+医嘱ID
bingLiID | - | Integer | 否 | 病历ID
zhuYuanID | - | Integer | 否 | 住院ID
bingQuID | - | Integer | 否 | 病区ID
zhuanKeID | - | Integer | 否 | 专科ID
zhiXingSJ | - | String | 否 | 执行时间
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/保存肠内营养决策提醒知晓
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/saveChangNeiYyjcTxZx

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"id":"","bingLiID":"","tiXingLB":"","zhiXiaoYHID":"","zhiXiaoYHXM":"","zhiXiaoSJ":"","tiXingWZ":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
id | - | Integer | 否 | -
bingLiID | - | Integer | 否 | 病历ID
tiXingLB | - | String | 否 | 提醒类别
zhiXiaoYHID | - | Integer | 否 | 知晓用户ID
zhiXiaoYHXM | - | String | 否 | 知晓用户姓名
zhiXiaoSJ | - | String | 否 | 知晓时间
tiXingWZ | - | String | 否 | 提醒位置
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/开立CRRT医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/saveCrrtYz

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","crrtMuBanMC":[{}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
crrtMuBanMC | - | Array | 否 | CRRT处方单的模板名称(抗凝方案：无、肝素、枸橼酸、比伐卢定、萘莫司他)
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"crrt":"","gcp":"","gcpywbh":"","yiZhuID":"","leiBie":"","guanLianYZID":"","bingLiID":"","zhuYuanID":"","zhuanKeID":"","bingQuID":"","ziFei":"","xiangMuID":"","zuHao":"","yaoFangDM":"","mingCheng":"","danJia":"","guiGe":"","jiXing":"","yiCiYL":"","jiLiangDW":"","zhiXingFF":"","zhiXingPL":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuLB":"","shouFeiSL":"","shouFeiCS":"","zhuangTaiBZ":"","luRuSJ":"","yiShengID":"","tingZhiSJ":"","tingZhiYSID":"","cheXiaoZT":"","cheXiaoSJ":"","cheXiaoRYID":"","xuShenPi":"","shenQingSL":"","tongZhiDanID":"","kangJunYP":"","zuHaoTXT":"","bianMaLX":"","xiangMuBM":"","yongYaoTS":"","kaiDanYSZKID":"","mianPiShi":"","shiFouBL":"","danWei":"","shenQingDanSJID":"","yaoPinLB":"","shiFouZB":"","teShuKJYWHZDID":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","jiBingDM":"","jiBingMC":"","shenQingYY":"","yongYaoSQ":"","yaoPinDS":"","changWaiYYHZDID":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQJLID":"","laiYuan":"","maZuiYT":"","xianDingFW":"","fenLeiMa":"","sheBaoDM":"","laiYuanLB":"","jiGouDM":"","jinRiLT":"","tingZhiLX":"","jianFa":"","yuanJiHZZQTYSID":"","wuXuDR":"","xueYuanYHID":"","xueYuanYHXM":"","buShouFei":"","bingBingQMS":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.crrt | - | String | 
data.gcp | - | String | 
data.gcpywbh | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.leiBie | - | String | 类别(1=治疗，2=饮食，3=药品，4=嘱托)
data.guanLianYZID | - | Integer | 关联医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.zhuanKeID | - | Integer | 专科ID
data.bingQuID | - | Integer | 病区ID
data.ziFei | - | String | 自费
data.xiangMuID | - | String | 项目ID：药品为药品ID，治疗为z+医嘱目录ID，饮食为y+饮食代码，嘱托是t6712
data.zuHao | - | Integer | 组号（真实组号）
data.yaoFangDM | - | String | 药房代码
data.mingCheng | - | String | 名称
data.danJia | - | Number | 单价
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 计量单位
data.zhiXingFF | - | String | 执行方法
data.zhiXingPL | - | String | 执行频率
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuLB | - | String | 医嘱类别：1西药，2中成药，3草药
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.zhuangTaiBZ | - | String | 状态标志
data.luRuSJ | - | String | 录入时间
data.yiShengID | - | Integer | 医生ID
data.tingZhiSJ | - | String | 停止时间
data.tingZhiYSID | - | Integer | 停止医生ID
data.cheXiaoZT | - | String | 撤销状态
data.cheXiaoSJ | - | String | 撤销时间
data.cheXiaoRYID | - | Integer | 撤销人员ID
data.xuShenPi | - | String | 需审批
data.shenQingSL | - | Number | 申请数量
data.tongZhiDanID | - | Integer | 通知单ID
data.kangJunYP | - | String | 抗菌药品
data.zuHaoTXT | - | String | 组号TXT
data.bianMaLX | - | String | 编码类型
data.xiangMuBM | - | String | 项目编码
data.yongYaoTS | - | Integer | 用药天数
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.mianPiShi | - | String | 免皮试
data.shiFouBL | - | String | 是否补录
data.danWei | - | String | 单位
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别
data.shiFouZB | - | String | 是否自备
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.jiBingDM | - | String | 疾病代码
data.jiBingMC | - | String | 疾病名称
data.shenQingYY | - | String | 申请原因
data.yongYaoSQ | - | String | 用药时期
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.zhiXingPL2 | - | String | 执行频率2
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQJLID | - | Integer | 外购药品申请记ID
data.laiYuan | - | String | 来源，2=ehr2，3=ehr3，4=微服务
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.fenLeiMa | - | String | 分类码
data.sheBaoDM | - | String | 社保代码
data.laiYuanLB | - | String | 来源类别
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
data.jianFa | - | String | 煎法
data.yuanJiHZZQTYSID | - | Integer | 院际会诊(远程会诊特需服务)知情同意书ID，对应e_blwsjl.id
data.wuXuDR | - | String | 无需导入，！！注意，1=院际会诊
data.xueYuanYHID | - | Integer | 学员用户ID
data.xueYuanYHXM | - | String | 学员用户姓名
data.buShouFei | - | String | 不收费
data.bingBingQMS | - | String | 病病情描述
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/保存医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/saveYiZhu

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","yiZhuLB":"","ylYzallExtendVos":[{"crrt":"","gcp":"","gcpywbh":"","yiZhuID":"","leiBie":"","yiZhuLX":"","guanLianYZID":"","guoJiaCG":"","jingPeiYP":"","xuNiZH":"","bingLiID":"","vteYzVo":{"vteyzid":"","yiZhuID":"","bingLiID":"","zuoTuiLX":"","youTuiLX":"","xiaoShi":"","fenZhong":""},"yaoPinZdVos":[{"icd":"","yiZhuID":"","xuHao":"","bingLiID":"","jiBingID":"","mingCheng":"","xiuGaiSJ":"","xiuGaiYHID":""}],"zhuYuanID":"","crrtYzVos":[{"crrtid":"","crrtxh":"","zhiLiaoYZID":"","bingLiID":"","zhuYuanID":"","yiZhuID":""}],"zhiLiaoyzmxVos":[{"id":"","sheBaoDM":"","sheBaoDM2":"","sheBaoSP":{"diagnose":"","itemID":"","limit":"","mingCheng":"","sheBaoBM":"","type":"","xiangDingFW":[{}]},"yiZhuID":"","bingLiID":"","zhuYuanID":"","muLuID":"","caoZuoMLID":"","yiZhuMC":"","muLuMXID":"","xiangMuID":"","shouFeiZHID":"","shouFeiXMID":"","shouFeiXMMC":"","ziFei":"","danJia":"","shuLiang":"","zuiDaSL":"","zuiShaoSL":"","ciShu":"","weiShouBZ":"","huLiZT":"","shouFeiSJ":"","shouFeiRYID":"","caoZuoZheID":"","xiuGaiSJ":"","yiZhuSJ":"","yiShiID":"","jiGouDM":""}],"zhuanKeID":"","bingQuID":"","kangJunYaoYfVo":{"yiZhuID":"","kangJunFL":"","touBaoJB":"","fuHeKJ":"","fuKuiNT":"","shiYongFF":"","yongYaoFL":"","shuQianSYFL":"","shiYongYY":"","tingZhiSJ":"","qieKouLB":"","shouShuMC":"","weiShengWu":"","weiShengWBZ":"","qingJieSS":"","qingJieSSGWYS":"","qingJieSSSPYSID":"","qingJieSSSPYJ":"","qingJieSSSPSJ":"","shiFouYWCG":"","weiChanGWYSS":"","qiTaWCGW":"","shiFouYLCBX":"","linChuangBX":""},"danWeiYP":"","ziFei":"","xiangMuID":"","zuHao":"","yaoFangDM":"","mingCheng":"","danJia":"","guiGe":"","jiXing":"","yiCiYL":"","jiLiangDW":"","zhiXingFF":"","zhiXingPL":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuLB":"","shouFeiSL":"","shouFeiCS":"","zhuangTaiBZ":"","luRuSJ":"","yiShengID":"","tingZhiSJ":"","tingZhiYSID":"","cheXiaoZT":"","cheXiaoSJ":"","cheXiaoRYID":"","xuShenPi":"","shenQingSL":"","tongZhiDanID":"","kangJunYP":"","zuHaoTXT":"","bianMaLX":"","xiangMuBM":"","yongYaoTS":"","kaiDanYSZKID":"","mianPiShi":"","shiFouBL":"","danWei":"","shenQingDanSJID":"","yaoPinLB":"","shiFouZB":"","teShuKJYWHZDID":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","jiBingDM":"","jiBingMC":"","shenQingYY":"","yongYaoSQ":"","yaoPinDS":"","changWaiYYHZDID":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQJLID":"","laiYuan":"","maZuiYT":"","xianDingFW":"","fenLeiMa":"","sheBaoDM":"","laiYuanLB":"","jiGouDM":"","jinRiLT":"","tingZhiLX":"","jianFa":"","yuanJiHZZQTYSID":"","wuXuDR":"","xueYuanYHID":"","xueYuanYHXM":"","buShouFei":"","bingBingQMS":""}],"shanChuYzIds":[{}],"ganRanZD":"","teShuYWBAs":[{"yaoPinID":"","jiBingDM":"","jiBingMC":"","shenQingYY":"","yongYaoSQ":""}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yiZhuLB | - | String | 否 | 医嘱类别（大类，cq=长期，ls=临时）
ylYzallExtendVos | - | Array | 否 | 医嘱列表
ylYzallExtendVos.crrt | - | String | 否 | -
ylYzallExtendVos.gcp | - | String | 否 | -
ylYzallExtendVos.gcpywbh | - | String | 否 | -
ylYzallExtendVos.yiZhuID | - | Integer | 否 | 医嘱ID
ylYzallExtendVos.leiBie | - | String | 否 | 类别(1=治疗，2=饮食，3=药品，4=嘱托)
ylYzallExtendVos.yiZhuLX | - | String | 否 | 医嘱类型（cq=长期，ls=临时，cy=草药，cydy=出院带药，zcydy=草药带药）
ylYzallExtendVos.guanLianYZID | - | Integer | 否 | 关联医嘱ID
ylYzallExtendVos.guoJiaCG | - | String | 否 | 国家采购
ylYzallExtendVos.jingPeiYP | - | String | 否 | 静脉营养药品（药品附加属性值）
ylYzallExtendVos.xuNiZH | - | Integer | 否 | 虚拟组号
ylYzallExtendVos.bingLiID | - | Integer | 否 | 病历ID
ylYzallExtendVos.vteYzVo | - | Object | 否 | -
ylYzallExtendVos.vteYzVo.vteyzid | - | String | 否 | -
ylYzallExtendVos.vteYzVo.yiZhuID | - | Integer | 否 | 医嘱ID(ylyzall.yzid)
ylYzallExtendVos.vteYzVo.bingLiID | - | Integer | 否 | 病历ID
ylYzallExtendVos.vteYzVo.zuoTuiLX | - | String | 否 | 左腿类型
ylYzallExtendVos.vteYzVo.youTuiLX | - | String | 否 | 右腿类型
ylYzallExtendVos.vteYzVo.xiaoShi | - | Number | 否 | 小时
ylYzallExtendVos.vteYzVo.fenZhong | - | Number | 否 | 分钟
ylYzallExtendVos.yaoPinZdVos | - | Array | 否 | 药品诊断
ylYzallExtendVos.yaoPinZdVos.icd | - | String | 否 | -
ylYzallExtendVos.yaoPinZdVos.yiZhuID | - | Integer | 否 | 医嘱ID
ylYzallExtendVos.yaoPinZdVos.xuHao | - | Integer | 否 | 序号
ylYzallExtendVos.yaoPinZdVos.bingLiID | - | Integer | 否 | 病历ID
ylYzallExtendVos.yaoPinZdVos.jiBingID | - | Integer | 否 | 疾病ID
ylYzallExtendVos.yaoPinZdVos.mingCheng | - | String | 否 | 名称
ylYzallExtendVos.yaoPinZdVos.xiuGaiSJ | - | String | 否 | 修改时间
ylYzallExtendVos.yaoPinZdVos.xiuGaiYHID | - | Integer | 否 | 修改用户ID
ylYzallExtendVos.zhuYuanID | - | Integer | 否 | 住院ID
ylYzallExtendVos.crrtYzVos | - | Array | 否 | crrt医嘱
ylYzallExtendVos.crrtYzVos.crrtid | - | Integer | 否 | -
ylYzallExtendVos.crrtYzVos.crrtxh | - | Integer | 否 | -
ylYzallExtendVos.crrtYzVos.zhiLiaoYZID | - | Integer | 否 | 治疗医嘱ID，对应yl_yzall.yzid
ylYzallExtendVos.crrtYzVos.bingLiID | - | Integer | 否 | 病历ID
ylYzallExtendVos.crrtYzVos.zhuYuanID | - | Integer | 否 | 住院ID
ylYzallExtendVos.crrtYzVos.yiZhuID | - | Integer | 否 | 医嘱ID，对应yl_yzall.yzid
ylYzallExtendVos.zhiLiaoyzmxVos | - | Array | 否 | 治疗医嘱明细
ylYzallExtendVos.zhiLiaoyzmxVos.id | - | Integer | 否 | -
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoDM | - | String | 否 | -
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoDM2 | - | String | 否 | -
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoSP | - | Object | 否 | -
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoSP.diagnose | - | String | 否 | 诊断
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoSP.itemID | - | Integer | 否 | 项目ID
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoSP.limit | - | String | 否 | 限制范围
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoSP.mingCheng | - | String | 否 | 项目名称
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoSP.sheBaoBM | - | String | 否 | 社保编码
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoSP.type | - | String | 否 | 类别:我知道了(1,I Know);提交审批(2,Submit);无(0, None)
ylYzallExtendVos.zhiLiaoyzmxVos.sheBaoSP.xiangDingFW | - | Array | 否 | 限定范围
ylYzallExtendVos.zhiLiaoyzmxVos.yiZhuID | - | Integer | 否 | 医嘱ID(yl_yzall.yzid)
ylYzallExtendVos.zhiLiaoyzmxVos.bingLiID | - | Integer | 否 | 病历ID
ylYzallExtendVos.zhiLiaoyzmxVos.zhuYuanID | - | Integer | 否 | 住院ID
ylYzallExtendVos.zhiLiaoyzmxVos.muLuID | - | Integer | 否 | 目录ID
ylYzallExtendVos.zhiLiaoyzmxVos.caoZuoMLID | - | Integer | 否 | 操作目录ID
ylYzallExtendVos.zhiLiaoyzmxVos.yiZhuMC | - | String | 否 | 医嘱名称
ylYzallExtendVos.zhiLiaoyzmxVos.muLuMXID | - | Integer | 否 | 目录明细ID
ylYzallExtendVos.zhiLiaoyzmxVos.xiangMuID | - | Integer | 否 | 项目ID
ylYzallExtendVos.zhiLiaoyzmxVos.shouFeiZHID | - | Integer | 否 | 收费组合ID
ylYzallExtendVos.zhiLiaoyzmxVos.shouFeiXMID | - | Integer | 否 | 收费项目ID
ylYzallExtendVos.zhiLiaoyzmxVos.shouFeiXMMC | - | String | 否 | 收费项目名称
ylYzallExtendVos.zhiLiaoyzmxVos.ziFei | - | String | 否 | 自费
ylYzallExtendVos.zhiLiaoyzmxVos.danJia | - | Number | 否 | 单价
ylYzallExtendVos.zhiLiaoyzmxVos.shuLiang | - | Number | 否 | 数量
ylYzallExtendVos.zhiLiaoyzmxVos.zuiDaSL | - | Integer | 否 | 最大数量
ylYzallExtendVos.zhiLiaoyzmxVos.zuiShaoSL | - | Integer | 否 | 最少数量
ylYzallExtendVos.zhiLiaoyzmxVos.ciShu | - | Integer | 否 | 次数
ylYzallExtendVos.zhiLiaoyzmxVos.weiShouBZ | - | String | 否 | 未收标志
ylYzallExtendVos.zhiLiaoyzmxVos.huLiZT | - | String | 否 | 护理状态
ylYzallExtendVos.zhiLiaoyzmxVos.shouFeiSJ | - | String | 否 | 收费时间
ylYzallExtendVos.zhiLiaoyzmxVos.shouFeiRYID | - | Integer | 否 | 收费人员ID
ylYzallExtendVos.zhiLiaoyzmxVos.caoZuoZheID | - | Integer | 否 | 操作者ID
ylYzallExtendVos.zhiLiaoyzmxVos.xiuGaiSJ | - | String | 否 | 修改时间
ylYzallExtendVos.zhiLiaoyzmxVos.yiZhuSJ | - | String | 否 | 医嘱时间
ylYzallExtendVos.zhiLiaoyzmxVos.yiShiID | - | Integer | 否 | 医师ID
ylYzallExtendVos.zhiLiaoyzmxVos.jiGouDM | - | String | 否 | 机构代码
ylYzallExtendVos.zhuanKeID | - | Integer | 否 | 专科ID
ylYzallExtendVos.bingQuID | - | Integer | 否 | 病区ID
ylYzallExtendVos.kangJunYaoYfVo | - | Object | 否 | -
ylYzallExtendVos.kangJunYaoYfVo.yiZhuID | - | Integer | 否 | 医嘱ID
ylYzallExtendVos.kangJunYaoYfVo.kangJunFL | - | String | 否 | 抗菌分类
ylYzallExtendVos.kangJunYaoYfVo.touBaoJB | - | String | 否 | 头孢级别
ylYzallExtendVos.kangJunYaoYfVo.fuHeKJ | - | String | 否 | 复合抗菌
ylYzallExtendVos.kangJunYaoYfVo.fuKuiNT | - | String | 否 | 氟奎诺同
ylYzallExtendVos.kangJunYaoYfVo.shiYongFF | - | String | 否 | 使用方法
ylYzallExtendVos.kangJunYaoYfVo.yongYaoFL | - | String | 否 | 用药分类
ylYzallExtendVos.kangJunYaoYfVo.shuQianSYFL | - | String | 否 | 术前使用分类
ylYzallExtendVos.kangJunYaoYfVo.shiYongYY | - | String | 否 | 使用原因
ylYzallExtendVos.kangJunYaoYfVo.tingZhiSJ | - | String | 否 | 停止时间
ylYzallExtendVos.kangJunYaoYfVo.qieKouLB | - | String | 否 | 切口类别
ylYzallExtendVos.kangJunYaoYfVo.shouShuMC | - | String | 否 | 手术名称
ylYzallExtendVos.kangJunYaoYfVo.weiShengWu | - | String | 否 | 微生物
ylYzallExtendVos.kangJunYaoYfVo.weiShengWBZ | - | String | 否 | 微生物备注
ylYzallExtendVos.kangJunYaoYfVo.qingJieSS | - | String | 否 | 清洁手术
ylYzallExtendVos.kangJunYaoYfVo.qingJieSSGWYS | - | String | 否 | 清洁手术_高危因素
ylYzallExtendVos.kangJunYaoYfVo.qingJieSSSPYSID | - | Integer | 否 | 清洁手术_审批医生ID
ylYzallExtendVos.kangJunYaoYfVo.qingJieSSSPYJ | - | String | 否 | 清洁手术_审批意见
ylYzallExtendVos.kangJunYaoYfVo.qingJieSSSPSJ | - | String | 否 | 清洁手术_审批时间
ylYzallExtendVos.kangJunYaoYfVo.shiFouYWCG | - | String | 否 | 新生儿预防用药，是否有围产高危因素，1=是，0=否
ylYzallExtendVos.kangJunYaoYfVo.weiChanGWYSS | - | String | 否 | 新生儿预防用药，围产高危因素
ylYzallExtendVos.kangJunYaoYfVo.qiTaWCGW | - | String | 否 | 新生儿预防用药，其他围产高危因素
ylYzallExtendVos.kangJunYaoYfVo.shiFouYLCBX | - | String | 否 | 新生儿预防用药，是否有临床表现，1=是，0=否
ylYzallExtendVos.kangJunYaoYfVo.linChuangBX | - | String | 否 | 新生儿预防用药，临床表现
ylYzallExtendVos.danWeiYP | - | String | 否 | 单味药品，1=是
ylYzallExtendVos.ziFei | - | String | 否 | 自费
ylYzallExtendVos.xiangMuID | - | String | 否 | 项目ID：药品为药品ID，治疗为z+医嘱目录ID，饮食为y+饮食代码，嘱托是t6712
ylYzallExtendVos.zuHao | - | Integer | 否 | 组号（真实组号）
ylYzallExtendVos.yaoFangDM | - | String | 否 | 药房代码
ylYzallExtendVos.mingCheng | - | String | 否 | 名称
ylYzallExtendVos.danJia | - | Number | 否 | 单价
ylYzallExtendVos.guiGe | - | String | 否 | 规格
ylYzallExtendVos.jiXing | - | String | 否 | 剂型
ylYzallExtendVos.yiCiYL | - | Number | 否 | 一次用量
ylYzallExtendVos.jiLiangDW | - | String | 否 | 计量单位
ylYzallExtendVos.zhiXingFF | - | String | 否 | 执行方法
ylYzallExtendVos.zhiXingPL | - | String | 否 | 执行频率
ylYzallExtendVos.geiYaoSJ | - | String | 否 | 给药时间
ylYzallExtendVos.teShuYF | - | String | 否 | 特殊用法
ylYzallExtendVos.kaiShiSJ | - | String | 否 | 开始时间
ylYzallExtendVos.jieShuSJ | - | String | 否 | 结束时间
ylYzallExtendVos.chiXuTS | - | Integer | 否 | 持续天数
ylYzallExtendVos.yiZhuLB | - | String | 否 | 医嘱类别：1西药，2中成药，3草药
ylYzallExtendVos.shouFeiSL | - | Number | 否 | 收费数量
ylYzallExtendVos.shouFeiCS | - | Integer | 否 | 收费次数
ylYzallExtendVos.zhuangTaiBZ | - | String | 否 | 状态标志
ylYzallExtendVos.luRuSJ | - | String | 否 | 录入时间
ylYzallExtendVos.yiShengID | - | Integer | 否 | 医生ID
ylYzallExtendVos.tingZhiSJ | - | String | 否 | 停止时间
ylYzallExtendVos.tingZhiYSID | - | Integer | 否 | 停止医生ID
ylYzallExtendVos.cheXiaoZT | - | String | 否 | 撤销状态
ylYzallExtendVos.cheXiaoSJ | - | String | 否 | 撤销时间
ylYzallExtendVos.cheXiaoRYID | - | Integer | 否 | 撤销人员ID
ylYzallExtendVos.xuShenPi | - | String | 否 | 需审批
ylYzallExtendVos.shenQingSL | - | Number | 否 | 申请数量
ylYzallExtendVos.tongZhiDanID | - | Integer | 否 | 通知单ID
ylYzallExtendVos.kangJunYP | - | String | 否 | 抗菌药品
ylYzallExtendVos.zuHaoTXT | - | String | 否 | 组号TXT
ylYzallExtendVos.bianMaLX | - | String | 否 | 编码类型
ylYzallExtendVos.xiangMuBM | - | String | 否 | 项目编码
ylYzallExtendVos.yongYaoTS | - | Integer | 否 | 用药天数
ylYzallExtendVos.kaiDanYSZKID | - | Integer | 否 | 开单医生专科ID
ylYzallExtendVos.mianPiShi | - | String | 否 | 免皮试
ylYzallExtendVos.shiFouBL | - | String | 否 | 是否补录
ylYzallExtendVos.danWei | - | String | 否 | 单位
ylYzallExtendVos.shenQingDanSJID | - | Integer | 否 | 申请单数据ID
ylYzallExtendVos.yaoPinLB | - | String | 否 | 药品类别
ylYzallExtendVos.shiFouZB | - | String | 否 | 是否自备
ylYzallExtendVos.teShuKJYWHZDID | - | Integer | 否 | 特殊抗菌药物会诊单ID
ylYzallExtendVos.sanLianKJYWHZDID | - | Integer | 否 | 三联抗菌药物会诊单ID
ylYzallExtendVos.jinRiCY | - | String | 否 | 今日出院
ylYzallExtendVos.zanShiBQ | - | String | 否 | 暂时不取
ylYzallExtendVos.jiBingDM | - | String | 否 | 疾病代码
ylYzallExtendVos.jiBingMC | - | String | 否 | 疾病名称
ylYzallExtendVos.shenQingYY | - | String | 否 | 申请原因
ylYzallExtendVos.yongYaoSQ | - | String | 否 | 用药时期
ylYzallExtendVos.yaoPinDS | - | Number | 否 | 药品滴速
ylYzallExtendVos.changWaiYYHZDID | - | Integer | 否 | 肠外营养会诊单id
ylYzallExtendVos.zhiXingPL2 | - | String | 否 | 执行频率2
ylYzallExtendVos.zhongChengYaoZD | - | String | 否 | 中成药诊断
ylYzallExtendVos.zhongChengYaoZZ | - | String | 否 | 中成药症状
ylYzallExtendVos.yaoPinDSBZ | - | String | 否 | 药品滴速备注
ylYzallExtendVos.waiGouYPSQJLID | - | Integer | 否 | 外购药品申请记ID
ylYzallExtendVos.laiYuan | - | String | 否 | 来源，2=ehr2，3=ehr3，4=微服务
ylYzallExtendVos.maZuiYT | - | String | 否 | 麻醉用途
ylYzallExtendVos.xianDingFW | - | String | 否 | 限定范围
ylYzallExtendVos.fenLeiMa | - | String | 否 | 分类码
ylYzallExtendVos.sheBaoDM | - | String | 否 | 社保代码
ylYzallExtendVos.laiYuanLB | - | String | 否 | 来源类别
ylYzallExtendVos.jiGouDM | - | String | 否 | 机构代码
ylYzallExtendVos.jinRiLT | - | String | 否 | 今日临停
ylYzallExtendVos.tingZhiLX | - | String | 否 | 停止类型
ylYzallExtendVos.jianFa | - | String | 否 | 煎法
ylYzallExtendVos.yuanJiHZZQTYSID | - | Integer | 否 | 院际会诊(远程会诊特需服务)知情同意书ID，对应e_blwsjl.id
ylYzallExtendVos.wuXuDR | - | String | 否 | 无需导入，！！注意，1=院际会诊
ylYzallExtendVos.xueYuanYHID | - | Integer | 否 | 学员用户ID
ylYzallExtendVos.xueYuanYHXM | - | String | 否 | 学员用户姓名
ylYzallExtendVos.buShouFei | - | String | 否 | 不收费
ylYzallExtendVos.bingBingQMS | - | String | 否 | 病病情描述
shanChuYzIds | - | Array | 否 | 删除医嘱ID列表（ylyzall.yzid）
ganRanZD | - | String | 否 | 感染诊断
teShuYWBAs | - | Array | 否 | 特殊药物备案
teShuYWBAs.yaoPinID | - | Integer | 否 | 药品ID
teShuYWBAs.jiBingDM | - | String | 否 | 疾病代码
teShuYWBAs.jiBingMC | - | String | 否 | 疾病名称
teShuYWBAs.shenQingYY | - | String | 否 | 申请原因
teShuYWBAs.yongYaoSQ | - | String | 否 | 用药时期
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/修改_保存医嘱标题个性化排序
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/saveYzBiaoTiGxh

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"geXingHuaID":"","fuGeXHID":"","yiShengYHID":"","yeWuDM":"","geXingHuaNR":"","paiXu":"","caoZuoZheID":"","chuangJianSJ":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
geXingHuaID | - | Integer | 否 | 个性化ID
fuGeXHID | - | Integer | 否 | 父个性化ID
yiShengYHID | - | Integer | 否 | 医生用户ID
yeWuDM | - | String | 否 | 业务代码
geXingHuaNR | - | String | 否 | 个性化内容
paiXu | - | Integer | 否 | 排序
caoZuoZheID | - | Integer | 否 | 操作者ID
chuangJianSJ | - | String | 否 | 创建时间
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Boolean | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/保存综合医嘱模板目录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/saveZongHeYzMbMl

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"addList":[{"moBanID":"","mingCheng":"","pinYin":"","wuBi":"","leiBie":"","guanLianID":"","caoZuoZhe":"","zhuangTaiBZ":"","xiuGaiSJ":"","xuHao":""}],"updateList":[{"moBanID":"","mingCheng":"","pinYin":"","wuBi":"","leiBie":"","guanLianID":"","caoZuoZhe":"","zhuangTaiBZ":"","xiuGaiSJ":"","xuHao":""}],"deleteList":[{"moBanID":"","mingCheng":"","pinYin":"","wuBi":"","leiBie":"","guanLianID":"","caoZuoZhe":"","zhuangTaiBZ":"","xiuGaiSJ":"","xuHao":""}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
addList | - | Array | 否 | 新增模板列表
addList.moBanID | - | Integer | 否 | 模板ID
addList.mingCheng | - | String | 否 | 名称
addList.pinYin | - | String | 否 | 拼音
addList.wuBi | - | String | 否 | 五笔
addList.leiBie | - | String | 否 | 类别 1全院，2病区，3专科，4个人，与关联ID对照
addList.guanLianID | - | Integer | 否 | 关联ID
addList.caoZuoZhe | - | Integer | 否 | 操作者
addList.zhuangTaiBZ | - | String | 否 | 状态标志
addList.xiuGaiSJ | - | String | 否 | 修改时间
addList.xuHao | - | Integer | 否 | 序号
updateList | - | Array | 否 | 修改模板列表
updateList.moBanID | - | Integer | 否 | 模板ID
updateList.mingCheng | - | String | 否 | 名称
updateList.pinYin | - | String | 否 | 拼音
updateList.wuBi | - | String | 否 | 五笔
updateList.leiBie | - | String | 否 | 类别 1全院，2病区，3专科，4个人，与关联ID对照
updateList.guanLianID | - | Integer | 否 | 关联ID
updateList.caoZuoZhe | - | Integer | 否 | 操作者
updateList.zhuangTaiBZ | - | String | 否 | 状态标志
updateList.xiuGaiSJ | - | String | 否 | 修改时间
updateList.xuHao | - | Integer | 否 | 序号
deleteList | - | Array | 否 | 删除模板列表
deleteList.moBanID | - | Integer | 否 | 模板ID
deleteList.mingCheng | - | String | 否 | 名称
deleteList.pinYin | - | String | 否 | 拼音
deleteList.wuBi | - | String | 否 | 五笔
deleteList.leiBie | - | String | 否 | 类别 1全院，2病区，3专科，4个人，与关联ID对照
deleteList.guanLianID | - | Integer | 否 | 关联ID
deleteList.caoZuoZhe | - | Integer | 否 | 操作者
deleteList.zhuangTaiBZ | - | String | 否 | 状态标志
deleteList.xiuGaiSJ | - | String | 否 | 修改时间
deleteList.xuHao | - | Integer | 否 | 序号
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/保存综合医嘱模板明细
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/saveZongHeYzMbMx

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"ylZhxyzmbmxVos":[{"moBanMXID":"","moBanID":"","xuHao":"","zuHao":"","zuHaoTXT":"","leiBie":"","xiangMuID":"","yaoFangDM":"","mingCheng":"","danJia":"","guiGe":"","jiXing":"","yiCiYL":"","jiLiangDW":"","zhiXingFF":"","zhiXingPL":"","geiYaoSJ":"","teShuYF":"","chiXuTS":"","yiZhuLB":"","shouFeiSL":"","shouFeiCS":"","danWei":"","zhuangTaiBZ":"","caoZuoZhe":"","xiuGaiSJ":""}],"deleteMBMXIDs":[{}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
ylZhxyzmbmxVos | - | Array | 否 | 模板明细列表
ylZhxyzmbmxVos.moBanMXID | - | Integer | 否 | 模板明细ID
ylZhxyzmbmxVos.moBanID | - | Integer | 否 | 模板ID
ylZhxyzmbmxVos.xuHao | - | Integer | 否 | 序号
ylZhxyzmbmxVos.zuHao | - | Integer | 否 | 组号
ylZhxyzmbmxVos.zuHaoTXT | - | String | 否 | 组号TXT
ylZhxyzmbmxVos.leiBie | - | String | 否 | 类别 1=治疗，2=饮食，3=药品，4=嘱托
ylZhxyzmbmxVos.xiangMuID | - | String | 否 | 项目ID 药品为药品ID，治疗为z+医嘱目录ID，饮食为y+饮食代码，嘱托是t6712
ylZhxyzmbmxVos.yaoFangDM | - | String | 否 | 药房代码
ylZhxyzmbmxVos.mingCheng | - | String | 否 | 名称
ylZhxyzmbmxVos.danJia | - | Number | 否 | 单价
ylZhxyzmbmxVos.guiGe | - | String | 否 | 规格
ylZhxyzmbmxVos.jiXing | - | String | 否 | 剂型
ylZhxyzmbmxVos.yiCiYL | - | Number | 否 | 一次用量
ylZhxyzmbmxVos.jiLiangDW | - | String | 否 | 剂量单位
ylZhxyzmbmxVos.zhiXingFF | - | String | 否 | 执行方法
ylZhxyzmbmxVos.zhiXingPL | - | String | 否 | 执行频率
ylZhxyzmbmxVos.geiYaoSJ | - | String | 否 | 给药时间
ylZhxyzmbmxVos.teShuYF | - | String | 否 | 特殊用法
ylZhxyzmbmxVos.chiXuTS | - | Integer | 否 | 持续天数
ylZhxyzmbmxVos.yiZhuLB | - | String | 否 | 医嘱类别（ 1 西药；2 中成药；3 草药；）
ylZhxyzmbmxVos.shouFeiSL | - | Number | 否 | 收费数量
ylZhxyzmbmxVos.shouFeiCS | - | Integer | 否 | 收费次数
ylZhxyzmbmxVos.danWei | - | String | 否 | 单位
ylZhxyzmbmxVos.zhuangTaiBZ | - | String | 否 | 状态标志
ylZhxyzmbmxVos.caoZuoZhe | - | Integer | 否 | 操作者
ylZhxyzmbmxVos.xiuGaiSJ | - | String | 否 | 修改时间
deleteMBMXIDs | - | Array | 否 | 删除模板明细ID
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/搜索医嘱项目
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/searchItems

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"leiXing":"","key":"","moHuSS":"","jiXing":"","yiZhuLB":"","yiZhuLX":"","bingLiID":"","drugTakePharmacyVos":[{"leiBie":"","moRenDY":"","moRenTS":"","moRenWeiZhiDM":"","weiZhiDM":"","yaoFangDM":"","yiZhuLB":"","youXianCK":"","youXianJB":"","zhuanKeDY":"","zhuangTaiBZ":""}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
leiXing | - | String | 否 | 类型：1111 第1位=药品，第2位=草药，第3位=治疗，第4位=饮食
key | - | String | 否 | 搜索关键词
moHuSS | - | String | 否 | 模糊搜索（1=是，0=否）
jiXing | - | String | 否 | 剂型
yiZhuLB | - | String | 否 | 医嘱类别（1=西药，2=中成药，3=草药）
yiZhuLX | - | String | 否 | 医嘱类型（cq=长期，ls=临时）
bingLiID | - | Integer | 否 | 病历ID
drugTakePharmacyVos | - | Array | 否 | 药房代码列表
drugTakePharmacyVos.leiBie | - | String | 否 | 类别
drugTakePharmacyVos.moRenDY | - | String | 否 | 默认单元
drugTakePharmacyVos.moRenTS | - | String | 否 | 默认提示
drugTakePharmacyVos.moRenWeiZhiDM | - | String | 否 | 默认位置代码
drugTakePharmacyVos.weiZhiDM | - | String | 否 | 位置代码
drugTakePharmacyVos.yaoFangDM | - | String | 否 | 药房代码
drugTakePharmacyVos.yiZhuLB | - | String | 否 | 医嘱类别
drugTakePharmacyVos.youXianCK | - | String | 否 | 优先窗口
drugTakePharmacyVos.youXianJB | - | Integer | 否 | 优先级别
drugTakePharmacyVos.zhuanKeDY | - | String | 否 | 专科单元
drugTakePharmacyVos.zhuangTaiBZ | - | Integer | 否 | 状态标志
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"biaoZhun":"","daiMa":"","dataop":"","guiGe":"","guoJiaMa":"","jiCai":"","leiXing":"","leiXingMC":"","mingCheng":"","shouJia":"","val":"","yaoPinData":{"baoZhuangLiang":"","courier":"","danWei":"","fenLeiMa":"","fenZhuangQianYPID":"","guanLiLX":"","guoJiaCaiGou":"","jiLiang":"","jiXing":"","keFouCF":"","keShouFeiYL":"","kongZhiJB":"","kuaiDiYP":"","mingCheng":"","pinYin":"","sheBaoBM":"","shouJia":"","wuBi":"","yaoFangDM":"","yaoPinID":"","yaoPinLB":"","yaoPinLY":"","yiZhuLB":"","zaiYongKCL":"","zhuangTaiBZ":""},"yinShiData":{"daiMa":"","danJia":"","leiBie":"","mingCheng":"","pinYin":"","wuBi":""},"zhiLiaoData":{"jiGouDM":"","mingCheng":"","pinYin":"","shiFouZF":"","wuBi":"","xianShiPX":"","yiZhuBZ":"","yiZhuMLID":"","zhuYuanXSPX":"","zongJia":""}}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.biaoZhun | - | String | 标准
data.daiMa | - | String | 药品ID/治疗医嘱目录/饮食代码，ypid/yzmlid/ysdm
data.dataop | - | String | dataop
data.guiGe | - | String | 规格
data.guoJiaMa | - | String | 国家码
data.jiCai | - | String | 集采
data.leiXing | - | String | 类型：1=治疗，2=饮食，3=药品，4嘱托，同yzall.LB
data.leiXingMC | - | String | 类型：(药)、(治)、(食)
data.mingCheng | - | String | 药品名称/治疗名称/饮食名称
data.shouJia | - | String | 售价
data.val | - | String | 药品ID/治疗医嘱目录/饮食代码，ypid/z+yzmlid/y+ysdm
data.yaoPinData | - | Object | 
data.yaoPinData.baoZhuangLiang | - | Integer | 包装量
data.yaoPinData.courier | - | String | 
data.yaoPinData.danWei | - | String | 单位
data.yaoPinData.fenLeiMa | - | String | 分类码
data.yaoPinData.fenZhuangQianYPID | - | Integer | 分装前药品ID
data.yaoPinData.guanLiLX | - | String | 管理类型
data.yaoPinData.guoJiaCaiGou | - | String | 是否集采（国家采购）
data.yaoPinData.jiLiang | - | String | 剂量
data.yaoPinData.jiXing | - | String | 剂型
data.yaoPinData.keFouCF | - | String | 可否拆分
data.yaoPinData.keShouFeiYL | - | Number | 可收费用量
data.yaoPinData.kongZhiJB | - | String | 控制级别
data.yaoPinData.kuaiDiYP | - | String | 可否快递
data.yaoPinData.mingCheng | - | String | 名称
data.yaoPinData.pinYin | - | String | 拼音
data.yaoPinData.sheBaoBM | - | String | 社保编码
data.yaoPinData.shouJia | - | Number | 售价
data.yaoPinData.wuBi | - | String | 五笔
data.yaoPinData.yaoFangDM | - | String | 药房代码
data.yaoPinData.yaoPinID | - | Integer | 药品ID
data.yaoPinData.yaoPinLB | - | String | 药品类别：0=本院药品，1=外购药品，2=赠送药品
data.yaoPinData.yaoPinLY | - | String | 药品来源 1.本院 2.外院药房 3.外购
data.yaoPinData.yiZhuLB | - | String | 医嘱类别：1=西药，2=中成药，3=草药
data.yaoPinData.zaiYongKCL | - | String | 在用库存量
data.yaoPinData.zhuangTaiBZ | - | String | 状态标志
data.yinShiData | - | Object | 
data.yinShiData.daiMa | - | String | 
data.yinShiData.danJia | - | Number | 
data.yinShiData.leiBie | - | String | 
data.yinShiData.mingCheng | - | String | 
data.yinShiData.pinYin | - | String | 
data.yinShiData.wuBi | - | String | 
data.zhiLiaoData | - | Object | 
data.zhiLiaoData.jiGouDM | - | String | 
data.zhiLiaoData.mingCheng | - | String | 
data.zhiLiaoData.pinYin | - | String | 
data.zhiLiaoData.shiFouZF | - | String | 
data.zhiLiaoData.wuBi | - | String | 
data.zhiLiaoData.xianShiPX | - | Integer | 
data.zhiLiaoData.yiZhuBZ | - | String | 
data.zhiLiaoData.yiZhuMLID | - | Integer | 
data.zhiLiaoData.zhuYuanXSPX | - | Integer | 
data.zhiLiaoData.zongJia | - | Number | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/修改_申请医嘱转费用
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/shenQingYzZfy

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","yiZhuID":"","leiXing":"","ziFei":"","ziFeiMC":"","mingCheng":"","jiaoYiID":"","shenQingLY":"","xiangMuID":"","jiLuID":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yiZhuID | - | Integer | 否 | 医嘱ID
leiXing | - | String | 否 | 类型，yp=药品，zl=治疗
ziFei | - | String | 否 | 自费（转换前）
ziFeiMC | - | String | 否 | 自费名称（转换前）
mingCheng | - | String | 否 | 名称
jiaoYiID | - | Integer | 否 | 交易ID
shenQingLY | - | String | 否 | 申请理由(选填)
xiangMuID | - | Integer | 否 | 项目ID，药品医嘱为药品ID
jiLuID | - | Integer | 否 | 记录ID
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/(106205签约状态)查询精细化住院开单医嘱签约信息
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/signWriteback

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"organCode":"","orderNo":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
organCode | - | String | 否 | 业务医疗机构代码
orderNo | - | String | 否 | 医嘱号
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"result_code":"","reason":"","order_no":"","sign_status":"","sign_time":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.result_code | - | String | 响应状态 0=成功，-1=失败
data.reason | - | String | 响应描述 失败时返回失败原因
data.order_no | - | String | 签约的医嘱号
data.sign_status | - | String | 签约状态 1=已签约，0=未签约
data.sign_time | - | String | 签约时间 yyyy-MM-dd HH:mm:ss
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/查询_停止全部长期医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/stopAllCqYz

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"bingLiID":"","tingZhiLX":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
tingZhiLX | - | String | 否 | 停止类型
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/停止医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/stopYiZhu

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","yiZhuList":"","leiBie":"","tingZhiLX":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
yiZhuList | - | String | 否 | 医嘱列表（药品=yp+组号,治疗=zl+医嘱ID,饮食=ys+医嘱ID,多条时以英文逗号隔开）
leiBie | - | String | 否 | 类别（1=长期，0=临时(包含出院带药)）
tingZhiLX | - | String | 否 | 停止类型（仅用于长期医嘱，1=立即停，2=执行后停，3=次日停）
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/提交医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/submitYiZhu

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","cyDaiJian":"","piShiYz":"","yiZhuLB":"","yzIds":[{}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
cyDaiJian | - | String | 否 | 草药代煎（1=我院代煎，0=不代煎，9=非草药）
piShiYz | - | String | 否 | 皮试医嘱（1=自动生成一条药品医嘱（皮试），0=不生成）
yiZhuLB | - | String | 否 | 医嘱类别（大类，cq=长期，ls=临时）
yzIds | - | Array | 否 | 医嘱ID列表（ylyzall.yzid）
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/根据药品模板名称开立医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/submitYiZhuByMbmc?bingLiID=&leiBie=&muBanMC=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
leiBie | - | Text | 是 | 模板类别
muBanMC | - | Text | 是 | 模板名称
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院医嘱/综合医嘱模板初始化
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/AdviceInpatient/zongHeYzMbInit

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"muBanLB":[{"code":"","name":"","pinYin":"","type":"","wuBi":""}],"geRenMBs":[{"moBanID":"","mingCheng":"","pinYin":"","wuBi":"","leiBie":"","guanLianID":"","caoZuoZhe":"","zhuangTaiBZ":"","xiuGaiSJ":"","xuHao":""}],"zhuanKeMBs":[{"moBanID":"","mingCheng":"","pinYin":"","wuBi":"","leiBie":"","guanLianID":"","caoZuoZhe":"","zhuangTaiBZ":"","xiuGaiSJ":"","xuHao":""}],"quanYuanMBs":[{"moBanID":"","mingCheng":"","pinYin":"","wuBi":"","leiBie":"","guanLianID":"","caoZuoZhe":"","zhuangTaiBZ":"","xiuGaiSJ":"","xuHao":""}],"yuanQus":[{"daiMa":"","mingCheng":"","paiXu":"","xiuGaiRY":"","xiuGaiSJ":"","yiLiaoJGDM":"","zhuangTaiBZ":""}],"zhuanKeQyYfs":[{"leiBie":"","moRenDY":"","moRenTS":"","moRenWeiZhiDM":"","weiZhiDM":"","yaoFangDM":"","yiZhuLB":"","youXianCK":"","youXianJB":"","zhuanKeDY":"","zhuangTaiBZ":""}],"yongYaoPL":[{"beiZhu":"","caoZuoZheID":"","jianGeTS":"","leiBie":"","menZhenPX":"","pinLuDM":"","pinLuMC":"","pinYin":"","sheBaoBM":"","shuChuPX":"","shuRuPX":"","wuBi":"","xiuGaiSJ":"","yiTianCS":"","zhuangTaiBZ":""}],"puTaoTangCDMLIDs":[{}],"xueTangPL":[{"yingYongDM":"","leiBie":"","daiMa":"","mingCheng":"","pinYin":"","wuBi":"","beiZhu":"","paiXu":"","daiMaCD":"","mingChengCD":"","fuJiaSX":"","neiBuLB":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","caoZuoZheID":"","xiuGaiSJ":"","nengFouXG":"","yiLiaoJGDM":"","caoZuoZhe":""}],"geiYaoSJ":[{"geiYaoSJDM":"","geiYaoSJMC":""}],"caoYaoTSJF":[{"beiZhu":"","caoZuoZheID":"","fangFaDM":"","fangFaMC":"","jianShuiLiang":"","klyf":"","leiBie":"","pinYin":"","shiBieBS":"","shuChuPX":"","shuRuPX":"","wuBi":"","xiuGaiSJ":"","yinPianCF":"","yinPianYZ":"","zhuangTaiBZ":""}],"caoYaoJYFF":[{"beiZhu":"","caoZuoZheID":"","fangFaDM":"","fangFaMC":"","jianShuiLiang":"","klyf":"","leiBie":"","pinYin":"","shiBieBS":"","shuChuPX":"","shuRuPX":"","wuBi":"","xiuGaiSJ":"","yinPianCF":"","yinPianYZ":"","zhuangTaiBZ":""}],"feiCaoYaoYYFF":[{"beiZhu":"","caoZuoZheID":"","fangFaDM":"","fangFaMC":"","jianShuiLiang":"","klyf":"","leiBie":"","pinYin":"","shiBieBS":"","shuChuPX":"","shuRuPX":"","wuBi":"","xiuGaiSJ":"","yinPianCF":"","yinPianYZ":"","zhuangTaiBZ":""}],"zhiXingBQs":[{"daiMa":"","mingCheng":""}],"leiBie":[{"daiMa":"","mingCheng":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.muBanLB | - | Array | 模板类别
data.muBanLB.code | - | String | 
data.muBanLB.name | - | String | 
data.muBanLB.pinYin | - | String | 
data.muBanLB.type | - | String | 
data.muBanLB.wuBi | - | String | 
data.geRenMBs | - | Array | 个人模板
data.geRenMBs.moBanID | - | Integer | 模板ID
data.geRenMBs.mingCheng | - | String | 名称
data.geRenMBs.pinYin | - | String | 拼音
data.geRenMBs.wuBi | - | String | 五笔
data.geRenMBs.leiBie | - | String | 类别 1全院，2病区，3专科，4个人，与关联ID对照
data.geRenMBs.guanLianID | - | Integer | 关联ID
data.geRenMBs.caoZuoZhe | - | Integer | 操作者
data.geRenMBs.zhuangTaiBZ | - | String | 状态标志
data.geRenMBs.xiuGaiSJ | - | String | 修改时间
data.geRenMBs.xuHao | - | Integer | 序号
data.zhuanKeMBs | - | Array | 专科模板
data.zhuanKeMBs.moBanID | - | Integer | 模板ID
data.zhuanKeMBs.mingCheng | - | String | 名称
data.zhuanKeMBs.pinYin | - | String | 拼音
data.zhuanKeMBs.wuBi | - | String | 五笔
data.zhuanKeMBs.leiBie | - | String | 类别 1全院，2病区，3专科，4个人，与关联ID对照
data.zhuanKeMBs.guanLianID | - | Integer | 关联ID
data.zhuanKeMBs.caoZuoZhe | - | Integer | 操作者
data.zhuanKeMBs.zhuangTaiBZ | - | String | 状态标志
data.zhuanKeMBs.xiuGaiSJ | - | String | 修改时间
data.zhuanKeMBs.xuHao | - | Integer | 序号
data.quanYuanMBs | - | Array | 全院模板
data.quanYuanMBs.moBanID | - | Integer | 模板ID
data.quanYuanMBs.mingCheng | - | String | 名称
data.quanYuanMBs.pinYin | - | String | 拼音
data.quanYuanMBs.wuBi | - | String | 五笔
data.quanYuanMBs.leiBie | - | String | 类别 1全院，2病区，3专科，4个人，与关联ID对照
data.quanYuanMBs.guanLianID | - | Integer | 关联ID
data.quanYuanMBs.caoZuoZhe | - | Integer | 操作者
data.quanYuanMBs.zhuangTaiBZ | - | String | 状态标志
data.quanYuanMBs.xiuGaiSJ | - | String | 修改时间
data.quanYuanMBs.xuHao | - | Integer | 序号
data.yuanQus | - | Array | 院区
data.yuanQus.daiMa | - | String | 
data.yuanQus.mingCheng | - | String | 
data.yuanQus.paiXu | - | Integer | 
data.yuanQus.xiuGaiRY | - | String | 
data.yuanQus.xiuGaiSJ | - | String | 
data.yuanQus.yiLiaoJGDM | - | String | 
data.yuanQus.zhuangTaiBZ | - | String | 
data.zhuanKeQyYfs | - | Array | 专科取药药房
data.zhuanKeQyYfs.leiBie | - | String | 类别
data.zhuanKeQyYfs.moRenDY | - | String | 默认单元
data.zhuanKeQyYfs.moRenTS | - | String | 默认提示
data.zhuanKeQyYfs.moRenWeiZhiDM | - | String | 默认位置代码
data.zhuanKeQyYfs.weiZhiDM | - | String | 位置代码
data.zhuanKeQyYfs.yaoFangDM | - | String | 药房代码
data.zhuanKeQyYfs.yiZhuLB | - | String | 医嘱类别
data.zhuanKeQyYfs.youXianCK | - | String | 优先窗口
data.zhuanKeQyYfs.youXianJB | - | Integer | 优先级别
data.zhuanKeQyYfs.zhuanKeDY | - | String | 专科单元
data.zhuanKeQyYfs.zhuangTaiBZ | - | Integer | 状态标志
data.yongYaoPL | - | Array | 用药频率字典
data.yongYaoPL.beiZhu | - | String | 备注
data.yongYaoPL.caoZuoZheID | - | Integer | (操作者id)修改人员id
data.yongYaoPL.jianGeTS | - | Number | 间隔天数:间隔天数+1天范围内的次数
data.yongYaoPL.leiBie | - | String | 类别
data.yongYaoPL.menZhenPX | - | Number | 门诊排序
data.yongYaoPL.pinLuDM | - | String | 频率代码
data.yongYaoPL.pinLuMC | - | String | 频率名称
data.yongYaoPL.pinYin | - | String | 拼音码
data.yongYaoPL.sheBaoBM | - | Number | 社保编码
data.yongYaoPL.shuChuPX | - | Number | 输出排序
data.yongYaoPL.shuRuPX | - | Number | 输入排序
data.yongYaoPL.wuBi | - | String | 五笔码
data.yongYaoPL.xiuGaiSJ | - | String | 修改时间
data.yongYaoPL.yiTianCS | - | Number | 一天次数:一周天数，一周几次
data.yongYaoPL.zhuangTaiBZ | - | String | 状态标志
data.puTaoTangCDMLIDs | - | Array | 葡萄糖测定mlid
data.xueTangPL | - | Array | 血糖频率
data.xueTangPL.yingYongDM | - | String | 应用代码
data.xueTangPL.leiBie | - | String | 类别
data.xueTangPL.daiMa | - | String | 代码
data.xueTangPL.mingCheng | - | String | 名称
data.xueTangPL.pinYin | - | String | 拼音
data.xueTangPL.wuBi | - | String | 五笔
data.xueTangPL.beiZhu | - | String | 备注
data.xueTangPL.paiXu | - | Integer | 排序
data.xueTangPL.daiMaCD | - | Integer | 代码长度
data.xueTangPL.mingChengCD | - | Integer | 名称长度
data.xueTangPL.fuJiaSX | - | String | 附加属性
data.xueTangPL.neiBuLB | - | String | 内部类别
data.xueTangPL.zhuangTaiBZ | - | String | 状态标志
data.xueTangPL.zhuangTaiBZMC | - | String | 状态标志名称
data.xueTangPL.caoZuoZheID | - | Integer | 操作者ID
data.xueTangPL.xiuGaiSJ | - | String | 修改时间
data.xueTangPL.nengFouXG | - | Integer | 能否修改
data.xueTangPL.yiLiaoJGDM | - | String | 医疗机构代码
data.xueTangPL.caoZuoZhe | - | String | 操作者
data.geiYaoSJ | - | Array | 给药时间
data.geiYaoSJ.geiYaoSJDM | - | String | 给药时间代码
data.geiYaoSJ.geiYaoSJMC | - | String | 给药时间名称
data.caoYaoTSJF | - | Array | 草药特殊煎法
data.caoYaoTSJF.beiZhu | - | String | 备注
data.caoYaoTSJF.caoZuoZheID | - | Integer | (操作者id)修改人员id
data.caoYaoTSJF.fangFaDM | - | String | 方法代码
data.caoYaoTSJF.fangFaMC | - | String | 方法名称
data.caoYaoTSJF.jianShuiLiang | - | String | 煎水量
data.caoYaoTSJF.klyf | - | String | 
data.caoYaoTSJF.leiBie | - | String | 类别: 具体可见ddlbn(lb= '0013')
data.caoYaoTSJF.pinYin | - | String | 拼音码
data.caoYaoTSJF.shiBieBS | - | Number | 识别标识:1联方 6药盘
data.caoYaoTSJF.shuChuPX | - | Number | 输出排序
data.caoYaoTSJF.shuRuPX | - | Number | 输入排序
data.caoYaoTSJF.wuBi | - | String | 五笔码
data.caoYaoTSJF.xiuGaiSJ | - | String | 修改时间
data.caoYaoTSJF.yinPianCF | - | String | 饮片处方
data.caoYaoTSJF.yinPianYZ | - | String | 饮片医嘱
data.caoYaoTSJF.zhuangTaiBZ | - | String | 状态标志
data.caoYaoJYFF | - | Array | 草药煎药方法
data.caoYaoJYFF.beiZhu | - | String | 备注
data.caoYaoJYFF.caoZuoZheID | - | Integer | (操作者id)修改人员id
data.caoYaoJYFF.fangFaDM | - | String | 方法代码
data.caoYaoJYFF.fangFaMC | - | String | 方法名称
data.caoYaoJYFF.jianShuiLiang | - | String | 煎水量
data.caoYaoJYFF.klyf | - | String | 
data.caoYaoJYFF.leiBie | - | String | 类别: 具体可见ddlbn(lb= '0013')
data.caoYaoJYFF.pinYin | - | String | 拼音码
data.caoYaoJYFF.shiBieBS | - | Number | 识别标识:1联方 6药盘
data.caoYaoJYFF.shuChuPX | - | Number | 输出排序
data.caoYaoJYFF.shuRuPX | - | Number | 输入排序
data.caoYaoJYFF.wuBi | - | String | 五笔码
data.caoYaoJYFF.xiuGaiSJ | - | String | 修改时间
data.caoYaoJYFF.yinPianCF | - | String | 饮片处方
data.caoYaoJYFF.yinPianYZ | - | String | 饮片医嘱
data.caoYaoJYFF.zhuangTaiBZ | - | String | 状态标志
data.feiCaoYaoYYFF | - | Array | 非草药
data.feiCaoYaoYYFF.beiZhu | - | String | 备注
data.feiCaoYaoYYFF.caoZuoZheID | - | Integer | (操作者id)修改人员id
data.feiCaoYaoYYFF.fangFaDM | - | String | 方法代码
data.feiCaoYaoYYFF.fangFaMC | - | String | 方法名称
data.feiCaoYaoYYFF.jianShuiLiang | - | String | 煎水量
data.feiCaoYaoYYFF.klyf | - | String | 
data.feiCaoYaoYYFF.leiBie | - | String | 类别: 具体可见ddlbn(lb= '0013')
data.feiCaoYaoYYFF.pinYin | - | String | 拼音码
data.feiCaoYaoYYFF.shiBieBS | - | Number | 识别标识:1联方 6药盘
data.feiCaoYaoYYFF.shuChuPX | - | Number | 输出排序
data.feiCaoYaoYYFF.shuRuPX | - | Number | 输入排序
data.feiCaoYaoYYFF.wuBi | - | String | 五笔码
data.feiCaoYaoYYFF.xiuGaiSJ | - | String | 修改时间
data.feiCaoYaoYYFF.yinPianCF | - | String | 饮片处方
data.feiCaoYaoYYFF.yinPianYZ | - | String | 饮片医嘱
data.feiCaoYaoYYFF.zhuangTaiBZ | - | String | 状态标志
data.zhiXingBQs | - | Array | 执行病区（仅全院属性）
data.zhiXingBQs.daiMa | - | String | 代码
data.zhiXingBQs.mingCheng | - | String | 名称
data.leiBie | - | Array | 类别：yp=药品，zl=治疗，ys=饮食，zt=嘱托
data.leiBie.daiMa | - | String | 代码
data.leiBie.mingCheng | - | String | 名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱
```text
Drug Inpatient Controller
```
#### Header参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### Query参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### Body参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
## /住院药品医嘱/将外购药品插入虚拟库
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/addWaiGouYP

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/检查出院带药收费状态
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/checkCydyShouFeiZt

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"zuHaoList":[{}],"bingLiID":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
zuHaoList | - | Array | 否 | 组号列表
bingLiID | - | Integer | 否 | 病历ID
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/修改_删除药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/deleteYaoPinYz

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/5天内开过的草药
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getBrLiShiCaoYao?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yaoFangDM":"","zhuShe":"","zhuanKeMC":"","id":"","zuHao":"","yiZhuLB":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","jiLiang":"","baoZhuangLiang":"","shuLiang":"","ciShu":"","yiCiYL":"","pinLv":"","fangFa":"","geiYaoSJ":"","teShuYF":"","yiShengYHID":"","leiBie":"","xiuGaiSJ":"","paiXu":"","jiLiangDW":"","muBanMC":"","pinYin":"","wuBi":"","zhuanKeID":"","shenHeZT":"","shenHeRYID":"","shsj":"","beiZhu":"","zhuanHuanYZLB":"","leiXing":"","mrcs":"","jiaShuiLiang":"","cypl":"","fyff":"","fenZhuangQYPID":"","zhuangTaiBZ":"","caoZuoZhe":"","shenHeRYXM":"","chiXuTS":"","kaiShiSJ":"","danWei":"","guiGe":"","jiXing":"","shouJia":"","yaoPinLB":"","sheBaoDM":"","maZuiYaopin":"","guanLiLX":"","kongZhiJB":"","chanDiMC":"","guoTanYPMC":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yaoFangDM | - | String | 药房代码
data.zhuShe | - | String | 
data.zhuanKeMC | - | String | 
data.id | - | Integer | 模板ID
data.zuHao | - | Integer | 组号
data.yiZhuLB | - | String | 医嘱类别
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 药品名称
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shuLiang | - | Integer | 数量
data.ciShu | - | Integer | 次数
data.yiCiYL | - | Number | 一次用量
data.pinLv | - | String | 频率
data.fangFa | - | String | 方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.yiShengYHID | - | Integer | 医生ID
data.leiBie | - | String | 类别
data.xiuGaiSJ | - | String | 修改时间
data.paiXu | - | Integer | 排序
data.jiLiangDW | - | String | 剂量单位
data.muBanMC | - | String | 模板名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
data.zhuanKeID | - | Integer | 专科ID
data.shenHeZT | - | String | 审核状态
data.shenHeRYID | - | Integer | 审核人员ID
data.shsj | - | String | 审核时间
data.beiZhu | - | String | 备注
data.zhuanHuanYZLB | - | String | 转换医嘱类别
data.leiXing | - | String | 类型
data.mrcs | - | Integer | 每日次数
data.jiaShuiLiang | - | String | 加水量
data.cypl | - | String | 草药频率
data.fyff | - | String | 使用方法
data.fenZhuangQYPID | - | Integer | 分装前药品ID
data.zhuangTaiBZ | - | String | 状态标志（0暂停 1普通）
data.caoZuoZhe | - | String | 操作者
data.shenHeRYXM | - | String | 审核人员姓名
data.chiXuTS | - | Integer | 持续天数
data.kaiShiSJ | - | String | 开始时间
data.danWei | - | String | 单位
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.shouJia | - | Number | 售价
data.yaoPinLB | - | String | 药品类别(1=外购药，2=赠送药品)
data.sheBaoDM | - | String | 社保代码
data.maZuiYaopin | - | String | 麻醉药品
data.guanLiLX | - | String | 管理类型
data.kongZhiJB | - | String | 控制级别
data.chanDiMC | - | String | 产地名称（厂家信息）
data.guoTanYPMC | - | String | 国谈药品名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/根据处方号获取草药煎药记录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getCaoYaoJYJLByCfh?chuFangHao=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
chuFangHao | - | Text | 是 | 处方号
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"bingRenXM":"","zhuYuanID":"","zhuanKeID":"","bingQuID":"","chuangWeiHao":"","zuHao":"","kaiFangTS":"","jianYaoTS":"","yaoFangDM":"","chuFangHao":"","shouFeiSJ":"","shouFeiRYID":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.bingRenXM | - | String | 病人姓名
data.zhuYuanID | - | Integer | 住院id
data.zhuanKeID | - | Integer | 专科id
data.bingQuID | - | Integer | 病区id
data.chuangWeiHao | - | String | 床位号
data.zuHao | - | Integer | 组号
data.kaiFangTS | - | Integer | 开方贴数
data.jianYaoTS | - | Integer | 煎药贴数
data.yaoFangDM | - | String | 药房代码
data.chuFangHao | - | String | 处方号
data.shouFeiSJ | - | String | 收费时间
data.shouFeiRYID | - | Integer | 收费人员id
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/根据组号获取草药煎药记录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getCaoYaoJYJLByZh?zuHao=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
zuHao | - | Text | 是 | 组号
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"bingRenXM":"","zhuYuanID":"","zhuanKeID":"","bingQuID":"","chuangWeiHao":"","zuHao":"","kaiFangTS":"","jianYaoTS":"","yaoFangDM":"","chuFangHao":"","shouFeiSJ":"","shouFeiRYID":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.bingRenXM | - | String | 病人姓名
data.zhuYuanID | - | Integer | 住院id
data.zhuanKeID | - | Integer | 专科id
data.bingQuID | - | Integer | 病区id
data.chuangWeiHao | - | String | 床位号
data.zuHao | - | Integer | 组号
data.kaiFangTS | - | Integer | 开方贴数
data.jianYaoTS | - | Integer | 煎药贴数
data.yaoFangDM | - | String | 药房代码
data.chuFangHao | - | String | 处方号
data.shouFeiSJ | - | String | 收费时间
data.shouFeiRYID | - | Integer | 收费人员id
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/获取出院带药信息
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getChuYuanDY?bingLiID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/查询_获取当日静脉输液量
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getDangRiJmSyl?bingLiID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/查询_根据病历ID和通知单ID查询在用半衰药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getInUseBanShuaiYpyzByBlidAndTzdid?bingLiID=&tongZhiDanID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
tongZhiDanID | - | Text | 是 | 通知单ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/根据病历ID获取审批表数据
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getJYSPJLListByBingLiID?bingLiID=&zhuangTaiBZ=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
zhuangTaiBZ | - | Text | 否 | 状态标志
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"jiluid":"","renWuMXID":"","yiZhuZH":"","jiHuaSJ":"","zanTingYY":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","faQiSJ":"","faQiRYXM":"","faQiRYID":"","shenPiSJ":"","juJueYY":"","shenPiJG":"","shenPiRYID":"","shenPiRYXM":"","shenPiLSH":"","zhiLiaoZuID":"","mingXiCZID":"","bingLiID":"","ylZyypyzVos":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}]}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.jiluid | - | Integer | 记录ID
data.renWuMXID | - | Integer | 任务明细ID
data.yiZhuZH | - | Integer | 医嘱组号
data.jiHuaSJ | - | String | 计划时间
data.zanTingYY | - | String | 暂停原因
data.zhuangTaiBZ | - | String | 审批状态标志
data.zhuangTaiBZMC | - | String | 审批状态标志名称
data.faQiSJ | - | String | 发起时间
data.faQiRYXM | - | String | 发起人员姓名
data.faQiRYID | - | Integer | 发起人员ID
data.shenPiSJ | - | String | 审批时间
data.juJueYY | - | String | 拒绝原因
data.shenPiJG | - | String | 审批结果
data.shenPiRYID | - | Integer | 审批人员ID
data.shenPiRYXM | - | String | 审批人员姓名
data.shenPiLSH | - | Integer | 审批流水号
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.mingXiCZID | - | Integer | 业务明细操作ID
data.bingLiID | - | Integer | 病历ID
data.ylZyypyzVos | - | Array | 医嘱列表
data.ylZyypyzVos.gcp | - | String | 
data.ylZyypyzVos.yiZhuID | - | Integer | 医嘱ID
data.ylZyypyzVos.bingLiID | - | Integer | 病历ID
data.ylZyypyzVos.zhuYuanID | - | Integer | 住院ID
data.ylZyypyzVos.bingRenXM | - | String | 病人姓名
data.ylZyypyzVos.bingQuID | - | Integer | 病区ID
data.ylZyypyzVos.zhuanKeID | - | Integer | 专科ID
data.ylZyypyzVos.ziFei | - | String | 自费
data.ylZyypyzVos.zuHao | - | Integer | 组号
data.ylZyypyzVos.yaoPinID | - | Integer | 药品ID
data.ylZyypyzVos.fenLeiMa | - | String | 分类码
data.ylZyypyzVos.mingCheng | - | String | 名称
data.ylZyypyzVos.danWei | - | String | 单位
data.ylZyypyzVos.jiXing | - | String | 剂型
data.ylZyypyzVos.jiLiang | - | String | 剂量
data.ylZyypyzVos.baoZhuangLiang | - | Integer | 包装量
data.ylZyypyzVos.shouJia | - | Number | 售价
data.ylZyypyzVos.yiCiYL | - | Number | 一次用量
data.ylZyypyzVos.jiLiangDW | - | String | 剂量单位
data.ylZyypyzVos.zhiXingPL | - | String | 执行频率
data.ylZyypyzVos.zhiXingFF | - | String | 执行方法
data.ylZyypyzVos.geiYaoSJ | - | String | 给药时间
data.ylZyypyzVos.teShuYF | - | String | 特殊用法
data.ylZyypyzVos.kaiShiSJ | - | String | 开始时间
data.ylZyypyzVos.jieShuSJ | - | String | 结束时间
data.ylZyypyzVos.chiXuTS | - | Integer | 持续天数
data.ylZyypyzVos.yiZhuCS | - | Integer | 医嘱次数
data.ylZyypyzVos.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.ylZyypyzVos.yiZhuJB | - | String | 医嘱级别
data.ylZyypyzVos.sheBaoBS | - | String | 社保标识
data.ylZyypyzVos.huLiZT | - | String | 护理状态
data.ylZyypyzVos.yiZhuZT | - | String | 医嘱状态
data.ylZyypyzVos.yiShengID | - | Integer | 医生ID
data.ylZyypyzVos.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.ylZyypyzVos.shouCiLRSJ | - | String | 首次录入时间
data.ylZyypyzVos.xiuGaiYSID | - | Integer | 修改医生ID
data.ylZyypyzVos.yaoFangDM | - | String | 药房代码
data.ylZyypyzVos.quYaoFS | - | String | 取药方式
data.ylZyypyzVos.shouFeiSL | - | Number | 收费数量
data.ylZyypyzVos.shouFeiCS | - | Integer | 收费次数
data.ylZyypyzVos.buFeiZL | - | Number | 补费总量
data.ylZyypyzVos.changQiBY | - | Number | 长期补药
data.ylZyypyzVos.shouFeiRYID | - | Integer | 收费人员ID
data.ylZyypyzVos.shouFeiSJ | - | String | 收费时间
data.ylZyypyzVos.weiShouBZ | - | String | 未收标志
data.ylZyypyzVos.bingQuBZ | - | String | 病区备注
data.ylZyypyzVos.daoRuRYID | - | Integer | 导入人员ID
data.ylZyypyzVos.daoRuSJ | - | String | 导入时间
data.ylZyypyzVos.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.ylZyypyzVos.tingZhiDRSJ | - | String | 停止导入时间
data.ylZyypyzVos.zhiXingJLSJ | - | String | 执行记录时间
data.ylZyypyzVos.xiuGaiRYID | - | Integer | 修改人员ID
data.ylZyypyzVos.xiuGaiSJ | - | String | 修改时间
data.ylZyypyzVos.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.ylZyypyzVos.tiZaoQYTS | - | Integer | 提早取药天数
data.ylZyypyzVos.zhiLiaoZuID | - | Integer | 治疗组ID
data.ylZyypyzVos.piShiJG | - | String | 皮试结果
data.ylZyypyzVos.zhiXingRYID | - | Integer | 执行人员ID
data.ylZyypyzVos.shenHeRYID | - | Integer | 审核人员ID
data.ylZyypyzVos.cheXiaoZT | - | String | 撤销状态
data.ylZyypyzVos.shenHeYSID | - | Integer | 审核医生ID
data.ylZyypyzVos.yiZhuSHSJ | - | String | 医嘱审核时间
data.ylZyypyzVos.piShiSJ | - | String | 皮试时间
data.ylZyypyzVos.yiZhuBZ | - | String | 医嘱备注
data.ylZyypyzVos.tongZhiDanID | - | Integer | 通知单ID
data.ylZyypyzVos.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.ylZyypyzVos.shiFouZB | - | String | 是否自备
data.ylZyypyzVos.shiFouBL | - | String | 是否补录
data.ylZyypyzVos.mianPiShi | - | String | 免皮试
data.ylZyypyzVos.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.ylZyypyzVos.yongYaoTS | - | Integer | 用药天数
data.ylZyypyzVos.xiangMuBM | - | String | 项目编码
data.ylZyypyzVos.xiangMuLX | - | String | 项目类型
data.ylZyypyzVos.shuangQianMingYHID | - | Integer | 双签名用户ID
data.ylZyypyzVos.shuangQianMingSJ | - | String | 双签名时间
data.ylZyypyzVos.shuangQianMingBZ | - | String | 双签名备注
data.ylZyypyzVos.shenQingDanSJID | - | Integer | 申请单数据ID
data.ylZyypyzVos.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.ylZyypyzVos.shenFangCLJG | - | String | 审方处理结果
data.ylZyypyzVos.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.ylZyypyzVos.jinRiCY | - | String | 今日出院
data.ylZyypyzVos.zanShiBQ | - | String | 暂时不取
data.ylZyypyzVos.piShiGCSJ | - | String | 皮试观察时间
data.ylZyypyzVos.piShiGCYHID | - | Integer | 皮试观察用户ID
data.ylZyypyzVos.yaoPinDS | - | Number | 药品滴速
data.ylZyypyzVos.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.ylZyypyzVos.linShiTL | - | Number | 临时调量
data.ylZyypyzVos.changQiBF | - | String | 长期调量
data.ylZyypyzVos.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.ylZyypyzVos.zhongChengYaoZD | - | String | 中成药诊断
data.ylZyypyzVos.zhongChengYaoZZ | - | String | 中成药症状
data.ylZyypyzVos.yaoPinDSBZ | - | String | 药品滴速备注
data.ylZyypyzVos.waiGouYPSQID | - | Integer | 外购药品申请ID
data.ylZyypyzVos.laiYuan | - | String | 来源
data.ylZyypyzVos.shenFangJK | - | String | 审方接口
data.ylZyypyzVos.maZuiYT | - | String | 麻醉用途
data.ylZyypyzVos.xianDingFW | - | String | 限定范围
data.ylZyypyzVos.geiYaoTJGLID | - | Integer | 给药途径关联id
data.ylZyypyzVos.mingCheng2 | - | String | 名称2
data.ylZyypyzVos.jiGouDM | - | String | 机构代码
data.ylZyypyzVos.jinRiLT | - | String | 今日临停
data.ylZyypyzVos.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/查询_根据组号列表查询静配配置记录信息
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getJingPeiZtByZh

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/查询_查询可收费的药品医嘱列表
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getKeShouFeiYpYzList?bingLiID=&jieShuSJ=&kaiShiSJ=&yaoFangDM=&yiZhuLB=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
jieShuSJ | - | Text | 是 | 结束时间(yyyy-MM-dd HH:mm:ss)
kaiShiSJ | - | Text | 是 | 开始时间(yyyy-MM-dd HH:mm:ss)
yaoFangDM | - | Text | 是 | 药房代码
yiZhuLB | - | Text | 是 | 医嘱类别
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yiZhuID":"","bingLiID":"","fenZhuangQianYPID":"","zhuYuanID":"","bingRenXM":"","yaoPinMC":"","bingQuID":"","fenLeiMa":"","pinYin":"","zhuanKeID":"","wuBi":"","ziFei":"","jiXing":"","zuHao":"","danWei":"","yaoPinID":"","jiLiang":"","baoZhuangLiang":"","mingCheng":"","shouJia":"","zaiYongKCL":"","yaoFangKCL":"","keShouFYL":"","guanLiLX":"","kongZhiJB":"","yiCiYL":"","jiLiangDW":"","keFouCF":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","shiYongFW":"","teShuYF":"","kaiShiSJ":"","tiaoJiaRQ":"","jieShuSJ":"","tiaoHouSJ":"","chiXuTS":"","shangPinMC":"","yiZhuCS":"","yingWenMC":"","yiZhuLB":"","yingWenSPMC":"","keFouCFLFYP":"","yiZhuJB":"","keFouCFCYDY":"","sheBaoBS":"","huLiZT":"","keFouCFZYCF":"","keFouCFMZCF":"","yiZhuZT":"","keFouCFJZCF":"","yiShengID":"","tiaoJiaBS":"","yiZhuSJ":"","shouCiLRSJ":"","tiaoJiaSPZT":"","mingCheng2":"","xiuGaiYSID":"","yaoFangDM":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.fenZhuangQianYPID | - | Integer | 分装前药品ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.yaoPinMC | - | String | 药品名称
data.bingQuID | - | Integer | 病区ID
data.fenLeiMa | - | String | 分类码
data.pinYin | - | String | 拼音
data.zhuanKeID | - | Integer | 专科ID
data.wuBi | - | String | 五笔
data.ziFei | - | String | 自费
data.jiXing | - | String | 剂型
data.zuHao | - | Integer | 组号
data.danWei | - | String | 单位
data.yaoPinID | - | Integer | 药品ID
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.mingCheng | - | String | 名称
data.shouJia | - | Number | 售价
data.zaiYongKCL | - | Number | 在用库存量
data.yaoFangKCL | - | Number | 药房库存量
data.keShouFYL | - | Number | 可收费用量
data.guanLiLX | - | String | 管理类型
data.kongZhiJB | - | String | 控制级别
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.keFouCF | - | String | 可否拆分
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.shiYongFW | - | String | 使用范围
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.tiaoJiaRQ | - | String | 调价日期
data.jieShuSJ | - | String | 结束时间
data.tiaoHouSJ | - | Number | 调后售价
data.chiXuTS | - | Integer | 持续天数
data.shangPinMC | - | String | 商品名称
data.yiZhuCS | - | Integer | 医嘱次数
data.yingWenMC | - | String | 英文名称
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yingWenSPMC | - | String | 英文商品名称
data.keFouCFLFYP | - | String | 可否拆分_联方药盘
data.yiZhuJB | - | String | 医嘱级别
data.keFouCFCYDY | - | String | 可否拆分_出院带药
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.keFouCFZYCF | - | String | 可否拆分_住院处方
data.keFouCFMZCF | - | String | 可否拆分_门诊处方
data.yiZhuZT | - | String | 医嘱状态
data.keFouCFJZCF | - | String | 可否拆分_急诊处方
data.yiShengID | - | Integer | 医生ID
data.tiaoJiaBS | - | String | 调价标识
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.tiaoJiaSPZT | - | String | 调价审批状态
data.mingCheng2 | - | String | 名称2
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/根据病历ID获取患者住院快递地址，护士站调用
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getKuaiDiDZByBlid?bingLiID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"bingLiID":"","shouHuoRenXM":"","lianXiDH":"","shengFen":"","chengShi":"","qu":"","lianXiDZ":"","xiuGaiSJ":"","xiuGaiYHID":"","beiZhu":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.bingLiID | - | Integer | 病历ID
data.shouHuoRenXM | - | String | 收货人姓名
data.lianXiDH | - | String | 联系电话
data.shengFen | - | String | 省份
data.chengShi | - | String | 城市
data.qu | - | String | 区
data.lianXiDZ | - | String | 联系地址
data.xiuGaiSJ | - | String | 修改时间
data.xiuGaiYHID | - | Integer | 修改用户ID
data.beiZhu | - | String | 备注
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/获取病人历史限定范围
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getLiShiXdfw?bingAnHao=&jieShuSJ=&kaiShiSJ=&waiGouYP=&yaoPinID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingAnHao | - | Text | 是 | 病案号
jieShuSJ | - | Text | 是 | 结束时间(yyyy-MM-dd)
kaiShiSJ | - | Text | 是 | 开始时间(yyyy-MM-dd)
waiGouYP | - | Text | 否 | 外购药品（可为空，空表示全部，true=外购药品，false=本院药品）
yaoPinID | - | Text | 是 | 药品ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"bingAnHao":"","bingRenXM":"","bingRenXB":"","jiuZhenLX":"","jiuZhenLXMC":"","jiuZhenLSH":"","yaoPinLB":"","yaoPinLBMC":"","ziFei":"","ziFeiMC":"","xianZhiFW":"","zuHao":"","yiZhuID":"","sheBaoDM":"","yaoPinID":"","yaoPinMC":"","guiGe":"","shenQingSL":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","yongFaBCSM":"","shouCiLRYSID":"","shouCiLRYSXM":"","shouCiLRSJ":"","xiuGaiYSID":"","xiuGaiYSXM":"","xiuGaiSJ":"","zhuanKeID":"","zhuanKeMC":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.bingAnHao | - | String | 病案号
data.bingRenXM | - | String | 病人姓名
data.bingRenXB | - | String | 病人性别
data.jiuZhenLX | - | String | 就诊类型：1=门诊，2=住院
data.jiuZhenLXMC | - | String | 就诊类型名称：门诊、住院
data.jiuZhenLSH | - | Integer | 就诊流水号：门诊为诊疗活动ID，住院为病历ID
data.yaoPinLB | - | String | 药品类别：0=本院，1=外购
data.yaoPinLBMC | - | String | 药品类别名称：0=本院，1=外购
data.ziFei | - | String | 是否自费，0=社保，1=自费
data.ziFeiMC | - | String | 是否自费名称，0=社保，1=自费
data.xianZhiFW | - | String | 限制范围
data.zuHao | - | Integer | 组号
data.yiZhuID | - | Integer | 医嘱ID
data.sheBaoDM | - | String | 社保代码
data.yaoPinID | - | Integer | 药品ID
data.yaoPinMC | - | String | 药品名称
data.guiGe | - | String | 规格
data.shenQingSL | - | Number | 申请数量
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.yongFaBCSM | - | String | 用法补充说明
data.shouCiLRYSID | - | Integer | 首次录入医生ID
data.shouCiLRYSXM | - | String | 首次录入医生姓名
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.xiuGaiYSXM | - | String | 修改医生姓名
data.xiuGaiSJ | - | String | 修改时间
data.zhuanKeID | - | Integer | 专科ID
data.zhuanKeMC | - | String | 专科名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/历史用药模板
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getLiShiYyMb

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"bingLiID":"","oldBLID":"","moBanLX":"","kaiShiSJ":"","jieShuSJ":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID(用于读药房)
oldBLID | - | Integer | 否 | 历史病历ID(用于读医嘱)
moBanLX | - | String | 否 | 模板类型：lscq历史用药（长期），lsls历史用药（临时），cqzy长期在用药品，cydy出院带药
kaiShiSJ | - | String | 否 | 开始时间(yyyy-MM-dd)
jieShuSJ | - | String | 否 | 结束时间(yyyy-MM-dd)
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yaoFangDM":"","zhuShe":"","zhuanKeMC":"","id":"","zuHao":"","yiZhuLB":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","jiLiang":"","baoZhuangLiang":"","shuLiang":"","ciShu":"","yiCiYL":"","pinLv":"","fangFa":"","geiYaoSJ":"","teShuYF":"","yiShengYHID":"","leiBie":"","xiuGaiSJ":"","paiXu":"","jiLiangDW":"","muBanMC":"","pinYin":"","wuBi":"","zhuanKeID":"","shenHeZT":"","shenHeRYID":"","shsj":"","beiZhu":"","zhuanHuanYZLB":"","leiXing":"","mrcs":"","jiaShuiLiang":"","cypl":"","fyff":"","fenZhuangQYPID":"","zhuangTaiBZ":"","caoZuoZhe":"","shenHeRYXM":"","chiXuTS":"","kaiShiSJ":"","danWei":"","guiGe":"","jiXing":"","shouJia":"","yaoPinLB":"","sheBaoDM":"","maZuiYaopin":"","guanLiLX":"","kongZhiJB":"","chanDiMC":"","guoTanYPMC":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yaoFangDM | - | String | 药房代码
data.zhuShe | - | String | 
data.zhuanKeMC | - | String | 
data.id | - | Integer | 模板ID
data.zuHao | - | Integer | 组号
data.yiZhuLB | - | String | 医嘱类别
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 药品名称
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shuLiang | - | Integer | 数量
data.ciShu | - | Integer | 次数
data.yiCiYL | - | Number | 一次用量
data.pinLv | - | String | 频率
data.fangFa | - | String | 方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.yiShengYHID | - | Integer | 医生ID
data.leiBie | - | String | 类别
data.xiuGaiSJ | - | String | 修改时间
data.paiXu | - | Integer | 排序
data.jiLiangDW | - | String | 剂量单位
data.muBanMC | - | String | 模板名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
data.zhuanKeID | - | Integer | 专科ID
data.shenHeZT | - | String | 审核状态
data.shenHeRYID | - | Integer | 审核人员ID
data.shsj | - | String | 审核时间
data.beiZhu | - | String | 备注
data.zhuanHuanYZLB | - | String | 转换医嘱类别
data.leiXing | - | String | 类型
data.mrcs | - | Integer | 每日次数
data.jiaShuiLiang | - | String | 加水量
data.cypl | - | String | 草药频率
data.fyff | - | String | 使用方法
data.fenZhuangQYPID | - | Integer | 分装前药品ID
data.zhuangTaiBZ | - | String | 状态标志（0暂停 1普通）
data.caoZuoZhe | - | String | 操作者
data.shenHeRYXM | - | String | 审核人员姓名
data.chiXuTS | - | Integer | 持续天数
data.kaiShiSJ | - | String | 开始时间
data.danWei | - | String | 单位
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.shouJia | - | Number | 售价
data.yaoPinLB | - | String | 药品类别(1=外购药，2=赠送药品)
data.sheBaoDM | - | String | 社保代码
data.maZuiYaopin | - | String | 麻醉药品
data.guanLiLX | - | String | 管理类型
data.kongZhiJB | - | String | 控制级别
data.chanDiMC | - | String | 产地名称（厂家信息）
data.guoTanYPMC | - | String | 国谈药品名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/三阶梯药物使用标志
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getSanJieTiYWSYBZ?bingLiID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/查询_根据模板类别查询药品模板
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYaoPinMbByLb

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","muBanLB":"","yiZhuLX":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
muBanLB | - | String | 否 | 模板类别(0=常用药模板，1=我的模板，2=病人历史用药，4=在用口服药，tytj=通用汤剂，jcyy=检查用药，zcyfc=中成药分餐，zyzj在用针剂，wgyp=外购药品(老版)，zsyp=赠送药品)
yiZhuLX | - | String | 否 | 医嘱类型(cq=长期)
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yaoFangDM":"","zhuShe":"","zhuanKeMC":"","id":"","zuHao":"","yiZhuLB":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","jiLiang":"","baoZhuangLiang":"","shuLiang":"","ciShu":"","yiCiYL":"","pinLv":"","fangFa":"","geiYaoSJ":"","teShuYF":"","yiShengYHID":"","leiBie":"","xiuGaiSJ":"","paiXu":"","jiLiangDW":"","muBanMC":"","pinYin":"","wuBi":"","zhuanKeID":"","shenHeZT":"","shenHeRYID":"","shsj":"","beiZhu":"","zhuanHuanYZLB":"","leiXing":"","mrcs":"","jiaShuiLiang":"","cypl":"","fyff":"","fenZhuangQYPID":"","zhuangTaiBZ":"","caoZuoZhe":"","shenHeRYXM":"","chiXuTS":"","kaiShiSJ":"","danWei":"","guiGe":"","jiXing":"","shouJia":"","yaoPinLB":"","sheBaoDM":"","maZuiYaopin":"","guanLiLX":"","kongZhiJB":"","chanDiMC":"","guoTanYPMC":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yaoFangDM | - | String | 药房代码
data.zhuShe | - | String | 
data.zhuanKeMC | - | String | 
data.id | - | Integer | 模板ID
data.zuHao | - | Integer | 组号
data.yiZhuLB | - | String | 医嘱类别
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 药品名称
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shuLiang | - | Integer | 数量
data.ciShu | - | Integer | 次数
data.yiCiYL | - | Number | 一次用量
data.pinLv | - | String | 频率
data.fangFa | - | String | 方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.yiShengYHID | - | Integer | 医生ID
data.leiBie | - | String | 类别
data.xiuGaiSJ | - | String | 修改时间
data.paiXu | - | Integer | 排序
data.jiLiangDW | - | String | 剂量单位
data.muBanMC | - | String | 模板名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
data.zhuanKeID | - | Integer | 专科ID
data.shenHeZT | - | String | 审核状态
data.shenHeRYID | - | Integer | 审核人员ID
data.shsj | - | String | 审核时间
data.beiZhu | - | String | 备注
data.zhuanHuanYZLB | - | String | 转换医嘱类别
data.leiXing | - | String | 类型
data.mrcs | - | Integer | 每日次数
data.jiaShuiLiang | - | String | 加水量
data.cypl | - | String | 草药频率
data.fyff | - | String | 使用方法
data.fenZhuangQYPID | - | Integer | 分装前药品ID
data.zhuangTaiBZ | - | String | 状态标志（0暂停 1普通）
data.caoZuoZhe | - | String | 操作者
data.shenHeRYXM | - | String | 审核人员姓名
data.chiXuTS | - | Integer | 持续天数
data.kaiShiSJ | - | String | 开始时间
data.danWei | - | String | 单位
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.shouJia | - | Number | 售价
data.yaoPinLB | - | String | 药品类别(1=外购药，2=赠送药品)
data.sheBaoDM | - | String | 社保代码
data.maZuiYaopin | - | String | 麻醉药品
data.guanLiLX | - | String | 管理类型
data.kongZhiJB | - | String | 控制级别
data.chanDiMC | - | String | 产地名称（厂家信息）
data.guoTanYPMC | - | String | 国谈药品名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/查询_根据模板名称获取模板详细内容
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYaoPinMbDetail

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingLiID":"","muBanMC":"","leiBie":"","yiZhuLB":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
muBanMC | - | String | 否 | 模板名称
leiBie | - | String | 否 | 类别（yl_ypmb.lb）
yiZhuLB | - | String | 否 | 医嘱类别（1=西药，2=中成药，3=草药）
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yaoFangDM":"","zhuShe":"","zhuanKeMC":"","id":"","zuHao":"","yiZhuLB":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","jiLiang":"","baoZhuangLiang":"","shuLiang":"","ciShu":"","yiCiYL":"","pinLv":"","fangFa":"","geiYaoSJ":"","teShuYF":"","yiShengYHID":"","leiBie":"","xiuGaiSJ":"","paiXu":"","jiLiangDW":"","muBanMC":"","pinYin":"","wuBi":"","zhuanKeID":"","shenHeZT":"","shenHeRYID":"","shsj":"","beiZhu":"","zhuanHuanYZLB":"","leiXing":"","mrcs":"","jiaShuiLiang":"","cypl":"","fyff":"","fenZhuangQYPID":"","zhuangTaiBZ":"","caoZuoZhe":"","shenHeRYXM":"","chiXuTS":"","kaiShiSJ":"","danWei":"","guiGe":"","jiXing":"","shouJia":"","yaoPinLB":"","sheBaoDM":"","maZuiYaopin":"","guanLiLX":"","kongZhiJB":"","chanDiMC":"","guoTanYPMC":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yaoFangDM | - | String | 药房代码
data.zhuShe | - | String | 
data.zhuanKeMC | - | String | 
data.id | - | Integer | 模板ID
data.zuHao | - | Integer | 组号
data.yiZhuLB | - | String | 医嘱类别
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 药品名称
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shuLiang | - | Integer | 数量
data.ciShu | - | Integer | 次数
data.yiCiYL | - | Number | 一次用量
data.pinLv | - | String | 频率
data.fangFa | - | String | 方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.yiShengYHID | - | Integer | 医生ID
data.leiBie | - | String | 类别
data.xiuGaiSJ | - | String | 修改时间
data.paiXu | - | Integer | 排序
data.jiLiangDW | - | String | 剂量单位
data.muBanMC | - | String | 模板名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
data.zhuanKeID | - | Integer | 专科ID
data.shenHeZT | - | String | 审核状态
data.shenHeRYID | - | Integer | 审核人员ID
data.shsj | - | String | 审核时间
data.beiZhu | - | String | 备注
data.zhuanHuanYZLB | - | String | 转换医嘱类别
data.leiXing | - | String | 类型
data.mrcs | - | Integer | 每日次数
data.jiaShuiLiang | - | String | 加水量
data.cypl | - | String | 草药频率
data.fyff | - | String | 使用方法
data.fenZhuangQYPID | - | Integer | 分装前药品ID
data.zhuangTaiBZ | - | String | 状态标志（0暂停 1普通）
data.caoZuoZhe | - | String | 操作者
data.shenHeRYXM | - | String | 审核人员姓名
data.chiXuTS | - | Integer | 持续天数
data.kaiShiSJ | - | String | 开始时间
data.danWei | - | String | 单位
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.shouJia | - | Number | 售价
data.yaoPinLB | - | String | 药品类别(1=外购药，2=赠送药品)
data.sheBaoDM | - | String | 社保代码
data.maZuiYaopin | - | String | 麻醉药品
data.guanLiLX | - | String | 管理类型
data.kongZhiJB | - | String | 控制级别
data.chanDiMC | - | String | 产地名称（厂家信息）
data.guoTanYPMC | - | String | 国谈药品名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/2天内开过的药品
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYaoPinYzByTime?bingLiID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"yaoFangDM":"","zhuShe":"","zhuanKeMC":"","id":"","zuHao":"","yiZhuLB":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","jiLiang":"","baoZhuangLiang":"","shuLiang":"","ciShu":"","yiCiYL":"","pinLv":"","fangFa":"","geiYaoSJ":"","teShuYF":"","yiShengYHID":"","leiBie":"","xiuGaiSJ":"","paiXu":"","jiLiangDW":"","muBanMC":"","pinYin":"","wuBi":"","zhuanKeID":"","shenHeZT":"","shenHeRYID":"","shsj":"","beiZhu":"","zhuanHuanYZLB":"","leiXing":"","mrcs":"","jiaShuiLiang":"","cypl":"","fyff":"","fenZhuangQYPID":"","zhuangTaiBZ":"","caoZuoZhe":"","shenHeRYXM":"","chiXuTS":"","kaiShiSJ":"","danWei":"","guiGe":"","jiXing":"","shouJia":"","yaoPinLB":"","sheBaoDM":"","maZuiYaopin":"","guanLiLX":"","kongZhiJB":"","chanDiMC":"","guoTanYPMC":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.yaoFangDM | - | String | 药房代码
data.zhuShe | - | String | 
data.zhuanKeMC | - | String | 
data.id | - | Integer | 模板ID
data.zuHao | - | Integer | 组号
data.yiZhuLB | - | String | 医嘱类别
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 药品名称
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shuLiang | - | Integer | 数量
data.ciShu | - | Integer | 次数
data.yiCiYL | - | Number | 一次用量
data.pinLv | - | String | 频率
data.fangFa | - | String | 方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.yiShengYHID | - | Integer | 医生ID
data.leiBie | - | String | 类别
data.xiuGaiSJ | - | String | 修改时间
data.paiXu | - | Integer | 排序
data.jiLiangDW | - | String | 剂量单位
data.muBanMC | - | String | 模板名称
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
data.zhuanKeID | - | Integer | 专科ID
data.shenHeZT | - | String | 审核状态
data.shenHeRYID | - | Integer | 审核人员ID
data.shsj | - | String | 审核时间
data.beiZhu | - | String | 备注
data.zhuanHuanYZLB | - | String | 转换医嘱类别
data.leiXing | - | String | 类型
data.mrcs | - | Integer | 每日次数
data.jiaShuiLiang | - | String | 加水量
data.cypl | - | String | 草药频率
data.fyff | - | String | 使用方法
data.fenZhuangQYPID | - | Integer | 分装前药品ID
data.zhuangTaiBZ | - | String | 状态标志（0暂停 1普通）
data.caoZuoZhe | - | String | 操作者
data.shenHeRYXM | - | String | 审核人员姓名
data.chiXuTS | - | Integer | 持续天数
data.kaiShiSJ | - | String | 开始时间
data.danWei | - | String | 单位
data.guiGe | - | String | 规格
data.jiXing | - | String | 剂型
data.shouJia | - | Number | 售价
data.yaoPinLB | - | String | 药品类别(1=外购药，2=赠送药品)
data.sheBaoDM | - | String | 社保代码
data.maZuiYaopin | - | String | 麻醉药品
data.guanLiLX | - | String | 管理类型
data.kongZhiJB | - | String | 控制级别
data.chanDiMC | - | String | 产地名称（厂家信息）
data.guoTanYPMC | - | String | 国谈药品名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/根据医嘱ID获取药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYaoPinYzByYzid?yiZhuID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
yiZhuID | - | Text | 是 | 医嘱ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/根据医嘱ID列表获取药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYaoPinYzByYzids

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/根据组号获取药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYaoPinYzByZh?zuHao=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
zuHao | - | Text | 是 | 组号
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/药占比报表
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYaoZhanBiBB

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"kaiShiSJ":"","jieShuSJ":"","leiBie":"","bingRenLB":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
kaiShiSJ | - | String | 否 | 开始时间(yyyy-MM-dd)
jieShuSJ | - | String | 否 | 结束时间(yyyy-MM-dd)
leiBie | - | String | 否 | 类别（1=按专科，2=按病区，3=按治疗组）
bingRenLB | - | String | 否 | 病人类别(1=社保病人，0=全部)
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"zhuanKeID":"","zhuanKeMC":"","shouRuHZVoList":[{"zhuanKeID":"","buMenMC":"","daiMa":"","mingCheng":"","zongJinE":""}],"heJi":"","dianHaiChunHDFC":"","yaoZhanBi":"","yaoZhanBiBFB":"","xiYaoZB":"","kangJunYaoZB":"","kangJunYaoZBBFB":"","chuYuanRC":"","zhuYuanZFY":"","junCiFY":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.zhuanKeID | - | Integer | 专科ID
data.zhuanKeMC | - | String | 专科名称
data.shouRuHZVoList | - | Array | 收入汇总
data.shouRuHZVoList.zhuanKeID | - | Integer | 专科ID
data.shouRuHZVoList.buMenMC | - | String | 部门名称
data.shouRuHZVoList.daiMa | - | String | 代码
data.shouRuHZVoList.mingCheng | - | String | 名称
data.shouRuHZVoList.zongJinE | - | Number | 总金额
data.heJi | - | Number | 合计
data.dianHaiChunHDFC | - | Number | 碘海醇和碘佛醇
data.yaoZhanBi | - | Number | 药品占比(小数)
data.yaoZhanBiBFB | - | String | 药品占比(百分比)
data.xiYaoZB | - | Number | 西药占比
data.kangJunYaoZB | - | Number | 抗菌药物占比(小数)
data.kangJunYaoZBBFB | - | String | 抗菌药物占比(百分比)
data.chuYuanRC | - | Integer | 出院人次(本地居保)
data.zhuYuanZFY | - | Number | 出院总费用(本地居保)
data.junCiFY | - | Number | 均次费用(本地居保)
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/易跌倒药物使用标志
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYiDieDaoYWSYBZ?bingLiID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/易致低钾药物使用标志
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYiZhiDiJiaYWSYBZ?bingLiID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/查询_根据病历ID查询医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYiZhuListByBlid?bingLiID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/根据病案号获取已审批外购药品申请记录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getYspWaiGouYpSqjlByBah?bingAnHao=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingAnHao | - | Text | 是 | 病案号
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"icd":"","jiLuID":"","yaoPinID":"","mingCheng":"","yiZhuLB":"","pinYin":"","wuBi":"","jiXing":"","danWei":"","jiLiang":"","baoZhuangLiang":"","zhuangTaiBZ":"","shiYongFW":"","caoZuoZheID":"","caoZuoZheXM":"","xiuGaiSJ":"","fenLeiMa":"","bingLiID":"","shenPiZT":"","keZhuRenSPSJ":"","shenPiZTMC":"","keZhuRenYHID":"","huShiZhangSPSJ":"","huShiZhangXM":"","huShiZhangYHID":"","yaoXueShenPSJ":"","yaoXueBuRYID":"","yiWuShenPSJ":"","yiWuChuRYID":"","juJueSPLY":"","yaoPinLX":"","yaoPinLXMC":"","shiFouYQS":"","piWen":"","shiYongLY":"","zhenDuan":"","yaoXueBuBZXX":"","yiWuChuBZXX":"","shiFouYBY":"","shiFouJBTJ":"","shiYongQX":"","yuanLingDaoSPSJ":"","yuanLingDaoYHID":"","guiGe":"","chaoShiYZ":"","shangPinMing":"","zhuYuanHao":"","shiFouSC":"","shenFenZhengH":"","bingRenBH":"","zhuanKeID":"","jiLiangZhi":"","jiLiangDW":"","ganBuBJ":"","changJia":"","zongLiang":"","jiaRuXNK":"","bingAnHao":"","sheBaoDM":"","sheBaoDMDYGG":"","jiBingID":"","jiBingIDS":"","shiFouBA":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.icd | - | String | 
data.jiLuID | - | Integer | 记录ID
data.yaoPinID | - | Integer | 药品ID
data.mingCheng | - | String | 名称
data.yiZhuLB | - | String | 医嘱类别
data.pinYin | - | String | 拼音
data.wuBi | - | String | 五笔
data.jiXing | - | String | 剂型
data.danWei | - | String | 单位
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.zhuangTaiBZ | - | String | 状态标志 0:停用 1:普通,2:审批通过
data.shiYongFW | - | String | 使用范围 1:门诊 2:病区 3:全院
data.caoZuoZheID | - | Integer | 操作者ID
data.caoZuoZheXM | - | String | 操作者姓名
data.xiuGaiSJ | - | String | 修改时间
data.fenLeiMa | - | String | 分类码
data.bingLiID | - | Integer | 病历ID
data.shenPiZT | - | String | 审批状态 0 审批不通过 1 未审批 2 科主任审批通过 3 护士长审批通过 4 药学部审批 通过  5 医务处审批通过
data.keZhuRenSPSJ | - | String | 科主任审批时间
data.shenPiZTMC | - | String | 审批状态名称
data.keZhuRenYHID | - | Integer | 科主任用户ID
data.huShiZhangSPSJ | - | String | 护士长审批时间
data.huShiZhangXM | - | String | 护士长姓名
data.huShiZhangYHID | - | Integer | 护士长用户ID
data.yaoXueShenPSJ | - | String | 药学审批时间
data.yaoXueBuRYID | - | Integer | 药学部人员ID
data.yiWuShenPSJ | - | String | 医务审批时间
data.yiWuChuRYID | - | Integer | 医务处人员ID
data.juJueSPLY | - | String | 拒绝审批理由
data.yaoPinLX | - | String | 药品类型 0外购药品 1赠送药品
data.yaoPinLXMC | - | String | 药品类型名称
data.shiFouYQS | - | String | 患者是否已签署使用承诺书 1是 0 否
data.piWen | - | String | 批文
data.shiYongLY | - | String | 使用理由
data.zhenDuan | - | String | 诊断
data.yaoXueBuBZXX | - | String | 药学部备注信息
data.yiWuChuBZXX | - | String | 医务处备注信息
data.shiFouYBY | - | String | 是否有必要
data.shiFouJBTJ | - | String | 是否具备条件
data.shiYongQX | - | String | 使用期限
data.yuanLingDaoSPSJ | - | String | 院领导审批时间
data.yuanLingDaoYHID | - | Integer | 院领导用户ID
data.guiGe | - | String | 规格
data.chaoShiYZ | - | String | 超适应症。0为否，1为是，2为超说明书
data.shangPinMing | - | String | 商品名
data.zhuYuanHao | - | String | 住院号
data.shiFouSC | - | String | 是否首次
data.shenFenZhengH | - | String | 身份证号
data.bingRenBH | - | String | 病人编号
data.zhuanKeID | - | Integer | 专科ID
data.jiLiangZhi | - | Number | 计量值
data.jiLiangDW | - | String | 计量单位
data.ganBuBJ | - | String | 干部保健，1是,0不是
data.changJia | - | String | 厂家
data.zongLiang | - | String | 总量
data.jiaRuXNK | - | String | 加入虚拟库
data.bingAnHao | - | String | 病案号
data.sheBaoDM | - | String | 社保代码
data.sheBaoDMDYGG | - | String | 社保代码对应规格
data.jiBingID | - | Integer | 疾病ID
data.jiBingIDS | - | String | 疾病IDS
data.shiFouBA | - | String | 是否备案
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/获取患者住院快递地址
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/getZyKuaiDiDz?bingLiID=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"bingLiID":"","shouHuoRenXM":"","lianXiDH":"","shengFen":"","chengShi":"","qu":"","lianXiDZ":"","xiuGaiSJ":"","xiuGaiYHID":"","beiZhu":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.bingLiID | - | Integer | 病历ID
data.shouHuoRenXM | - | String | 收货人姓名
data.lianXiDH | - | String | 联系电话
data.shengFen | - | String | 省份
data.chengShi | - | String | 城市
data.qu | - | String | 区
data.lianXiDZ | - | String | 联系地址
data.xiuGaiSJ | - | String | 修改时间
data.xiuGaiYHID | - | Integer | 修改用户ID
data.beiZhu | - | String | 备注
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/特殊使用级抗菌药物使用情况统计初始化
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/initTeShuKjywSyqkTj

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"kaiShiSJ":"","jieShuSJ":"","leiBieList":[{"code":"","name":"","pinYin":"","type":"","wuBi":""}],"leiXingList":[{"code":"","name":"","pinYin":"","type":"","wuBi":""}],"yaoPinZLList":[{"yingYongDM":"","leiBie":"","daiMa":"","mingCheng":"","pinYin":"","wuBi":"","beiZhu":"","paiXu":"","daiMaCD":"","mingChengCD":"","fuJiaSX":"","neiBuLB":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","caoZuoZheID":"","xiuGaiSJ":"","nengFouXG":"","yiLiaoJGDM":"","caoZuoZhe":""}],"zhuangTaiBZList":[{"code":"","name":"","pinYin":"","type":"","wuBi":""}],"shenPiZTList":[{"code":"","name":"","pinYin":"","type":"","wuBi":""}],"spztbzList":[{"code":"","name":"","pinYin":"","type":"","wuBi":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.kaiShiSJ | - | String | 开始时间(yyyy-MM-dd)
data.jieShuSJ | - | String | 结束时间(yyyy-MM-dd)
data.leiBieList | - | Array | 类别(1=药品名称,2=药品种类)
data.leiBieList.code | - | String | 
data.leiBieList.name | - | String | 
data.leiBieList.pinYin | - | String | 
data.leiBieList.type | - | String | 
data.leiBieList.wuBi | - | String | 
data.leiXingList | - | Array | 类型(1=医生,2=科室)
data.leiXingList.code | - | String | 
data.leiXingList.name | - | String | 
data.leiXingList.pinYin | - | String | 
data.leiXingList.type | - | String | 
data.leiXingList.wuBi | - | String | 
data.yaoPinZLList | - | Array | 药品种类列表
data.yaoPinZLList.yingYongDM | - | String | 应用代码
data.yaoPinZLList.leiBie | - | String | 类别
data.yaoPinZLList.daiMa | - | String | 代码
data.yaoPinZLList.mingCheng | - | String | 名称
data.yaoPinZLList.pinYin | - | String | 拼音
data.yaoPinZLList.wuBi | - | String | 五笔
data.yaoPinZLList.beiZhu | - | String | 备注
data.yaoPinZLList.paiXu | - | Integer | 排序
data.yaoPinZLList.daiMaCD | - | Integer | 代码长度
data.yaoPinZLList.mingChengCD | - | Integer | 名称长度
data.yaoPinZLList.fuJiaSX | - | String | 附加属性
data.yaoPinZLList.neiBuLB | - | String | 内部类别
data.yaoPinZLList.zhuangTaiBZ | - | String | 状态标志
data.yaoPinZLList.zhuangTaiBZMC | - | String | 状态标志名称
data.yaoPinZLList.caoZuoZheID | - | Integer | 操作者ID
data.yaoPinZLList.xiuGaiSJ | - | String | 修改时间
data.yaoPinZLList.nengFouXG | - | Integer | 能否修改
data.yaoPinZLList.yiLiaoJGDM | - | String | 医疗机构代码
data.yaoPinZLList.caoZuoZhe | - | String | 操作者
data.zhuangTaiBZList | - | Array | 会诊单状态标志
data.zhuangTaiBZList.code | - | String | 
data.zhuangTaiBZList.name | - | String | 
data.zhuangTaiBZList.pinYin | - | String | 
data.zhuangTaiBZList.type | - | String | 
data.zhuangTaiBZList.wuBi | - | String | 
data.shenPiZTList | - | Array | 审批状态
data.shenPiZTList.code | - | String | 
data.shenPiZTList.name | - | String | 
data.shenPiZTList.pinYin | - | String | 
data.shenPiZTList.type | - | String | 
data.shenPiZTList.wuBi | - | String | 
data.spztbzList | - | Array | 审批状态(用于查询)
data.spztbzList.code | - | String | 
data.spztbzList.name | - | String | 
data.spztbzList.pinYin | - | String | 
data.spztbzList.type | - | String | 
data.spztbzList.wuBi | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/初始化药占比报表
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/initYaoZhanBiBB

#### 请求方式
> GET

#### Content-Type
> none

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"kaiShiSJ":"","jieShuSJ":"","leiBieList":[{"daiMa":"","mingCheng":""}],"bingRenLBList":[{"daiMa":"","mingCheng":""}],"biaoTouList":[{"yingYongDM":"","leiBie":"","daiMa":"","mingCheng":"","pinYin":"","wuBi":"","beiZhu":"","paiXu":"","daiMaCD":"","mingChengCD":"","fuJiaSX":"","neiBuLB":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","caoZuoZheID":"","xiuGaiSJ":"","nengFouXG":"","yiLiaoJGDM":"","caoZuoZhe":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.kaiShiSJ | - | String | 开始时间(yyyy-MM-dd)
data.jieShuSJ | - | String | 结束时间(yyyy-MM-dd)
data.leiBieList | - | Array | 类别（1=按专科，2=按病区，3=按治疗组）
data.leiBieList.daiMa | - | String | 代码
data.leiBieList.mingCheng | - | String | 名称
data.bingRenLBList | - | Array | 病人类别(1=社保病人，0=全部)
data.bingRenLBList.daiMa | - | String | 代码
data.bingRenLBList.mingCheng | - | String | 名称
data.biaoTouList | - | Array | 表头
data.biaoTouList.yingYongDM | - | String | 应用代码
data.biaoTouList.leiBie | - | String | 类别
data.biaoTouList.daiMa | - | String | 代码
data.biaoTouList.mingCheng | - | String | 名称
data.biaoTouList.pinYin | - | String | 拼音
data.biaoTouList.wuBi | - | String | 五笔
data.biaoTouList.beiZhu | - | String | 备注
data.biaoTouList.paiXu | - | Integer | 排序
data.biaoTouList.daiMaCD | - | Integer | 代码长度
data.biaoTouList.mingChengCD | - | Integer | 名称长度
data.biaoTouList.fuJiaSX | - | String | 附加属性
data.biaoTouList.neiBuLB | - | String | 内部类别
data.biaoTouList.zhuangTaiBZ | - | String | 状态标志
data.biaoTouList.zhuangTaiBZMC | - | String | 状态标志名称
data.biaoTouList.caoZuoZheID | - | Integer | 操作者ID
data.biaoTouList.xiuGaiSJ | - | String | 修改时间
data.biaoTouList.nengFouXG | - | Integer | 能否修改
data.biaoTouList.yiLiaoJGDM | - | String | 医疗机构代码
data.biaoTouList.caoZuoZhe | - | String | 操作者
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/修改_新增药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/insertYaoPinYzListReturnYz

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
gcp | - | String | 否 | -
yiZhuID | - | Integer | 否 | 医嘱ID
bingLiID | - | Integer | 否 | 病历ID
zhuYuanID | - | Integer | 否 | 住院ID
bingRenXM | - | String | 否 | 病人姓名
bingQuID | - | Integer | 否 | 病区ID
zhuanKeID | - | Integer | 否 | 专科ID
ziFei | - | String | 否 | 自费
zuHao | - | Integer | 否 | 组号
yaoPinID | - | Integer | 否 | 药品ID
fenLeiMa | - | String | 否 | 分类码
mingCheng | - | String | 否 | 名称
danWei | - | String | 否 | 单位
jiXing | - | String | 否 | 剂型
jiLiang | - | String | 否 | 剂量
baoZhuangLiang | - | Integer | 否 | 包装量
shouJia | - | Number | 否 | 售价
yiCiYL | - | Number | 否 | 一次用量
jiLiangDW | - | String | 否 | 剂量单位
zhiXingPL | - | String | 否 | 执行频率
zhiXingFF | - | String | 否 | 执行方法
geiYaoSJ | - | String | 否 | 给药时间
teShuYF | - | String | 否 | 特殊用法
kaiShiSJ | - | String | 否 | 开始时间
jieShuSJ | - | String | 否 | 结束时间
chiXuTS | - | Integer | 否 | 持续天数
yiZhuCS | - | Integer | 否 | 医嘱次数
yiZhuLB | - | String | 否 | 医嘱类别（1西药，2中成药，3草药）
yiZhuJB | - | String | 否 | 医嘱级别
sheBaoBS | - | String | 否 | 社保标识
huLiZT | - | String | 否 | 护理状态
yiZhuZT | - | String | 否 | 医嘱状态
yiShengID | - | Integer | 否 | 医生ID
yiZhuSJ | - | String | 否 | 医嘱时间（实际表示停止时间）
shouCiLRSJ | - | String | 否 | 首次录入时间
xiuGaiYSID | - | Integer | 否 | 修改医生ID
yaoFangDM | - | String | 否 | 药房代码
quYaoFS | - | String | 否 | 取药方式
shouFeiSL | - | Number | 否 | 收费数量
shouFeiCS | - | Integer | 否 | 收费次数
buFeiZL | - | Number | 否 | 补费总量
changQiBY | - | Number | 否 | 长期补药
shouFeiRYID | - | Integer | 否 | 收费人员ID
shouFeiSJ | - | String | 否 | 收费时间
weiShouBZ | - | String | 否 | 未收标志
bingQuBZ | - | String | 否 | 病区备注
daoRuRYID | - | Integer | 否 | 导入人员ID
daoRuSJ | - | String | 否 | 导入时间
tingZhiDRRYID | - | Integer | 否 | 停止导入人员ID
tingZhiDRSJ | - | String | 否 | 停止导入时间
zhiXingJLSJ | - | String | 否 | 执行记录时间
xiuGaiRYID | - | Integer | 否 | 修改人员ID
xiuGaiSJ | - | String | 否 | 修改时间
leiBie | - | String | 否 | 类别（1=医生医嘱，2=护士医嘱）
tiZaoQYTS | - | Integer | 否 | 提早取药天数
zhiLiaoZuID | - | Integer | 否 | 治疗组ID
piShiJG | - | String | 否 | 皮试结果
zhiXingRYID | - | Integer | 否 | 执行人员ID
shenHeRYID | - | Integer | 否 | 审核人员ID
cheXiaoZT | - | String | 否 | 撤销状态
shenHeYSID | - | Integer | 否 | 审核医生ID
yiZhuSHSJ | - | String | 否 | 医嘱审核时间
piShiSJ | - | String | 否 | 皮试时间
yiZhuBZ | - | String | 否 | 医嘱备注
tongZhiDanID | - | Integer | 否 | 通知单ID
teShuKJYWHZDID | - | Integer | 否 | 特殊抗菌药物会诊单ID
shiFouZB | - | String | 否 | 是否自备
shiFouBL | - | String | 否 | 是否补录
mianPiShi | - | String | 否 | 免皮试
kaiDanYSZKID | - | Integer | 否 | 开单医生专科ID
yongYaoTS | - | Integer | 否 | 用药天数
xiangMuBM | - | String | 否 | 项目编码
xiangMuLX | - | String | 否 | 项目类型
shuangQianMingYHID | - | Integer | 否 | 双签名用户ID
shuangQianMingSJ | - | String | 否 | 双签名时间
shuangQianMingBZ | - | String | 否 | 双签名备注
shenQingDanSJID | - | Integer | 否 | 申请单数据ID
yaoPinLB | - | String | 否 | 药品类别，0=本院药品,1=外购药品,2=赠送药品
shenFangCLJG | - | String | 否 | 审方处理结果
sanLianKJYWHZDID | - | Integer | 否 | 三联抗菌药物会诊单ID
jinRiCY | - | String | 否 | 今日出院
zanShiBQ | - | String | 否 | 暂时不取
piShiGCSJ | - | String | 否 | 皮试观察时间
piShiGCYHID | - | Integer | 否 | 皮试观察用户ID
yaoPinDS | - | Number | 否 | 药品滴速
changWaiYYHZDID | - | Integer | 否 | 肠外营养会诊单id
linShiTL | - | Number | 否 | 临时调量
changQiBF | - | String | 否 | 长期调量
zhiXingPL2 | - | String | 否 | 执行频率2（时间针频率）
zhongChengYaoZD | - | String | 否 | 中成药诊断
zhongChengYaoZZ | - | String | 否 | 中成药症状
yaoPinDSBZ | - | String | 否 | 药品滴速备注
waiGouYPSQID | - | Integer | 否 | 外购药品申请ID
laiYuan | - | String | 否 | 来源
shenFangJK | - | String | 否 | 审方接口
maZuiYT | - | String | 否 | 麻醉用途
xianDingFW | - | String | 否 | 限定范围
geiYaoTJGLID | - | Integer | 否 | 给药途径关联id
mingCheng2 | - | String | 否 | 名称2
jiGouDM | - | String | 否 | 机构代码
jinRiLT | - | String | 否 | 今日临停
tingZhiLX | - | String | 否 | 停止类型
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/修改_新增药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/insertYaoPinYzListReturnYzid

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
gcp | - | String | 否 | -
yiZhuID | - | Integer | 否 | 医嘱ID
bingLiID | - | Integer | 否 | 病历ID
zhuYuanID | - | Integer | 否 | 住院ID
bingRenXM | - | String | 否 | 病人姓名
bingQuID | - | Integer | 否 | 病区ID
zhuanKeID | - | Integer | 否 | 专科ID
ziFei | - | String | 否 | 自费
zuHao | - | Integer | 否 | 组号
yaoPinID | - | Integer | 否 | 药品ID
fenLeiMa | - | String | 否 | 分类码
mingCheng | - | String | 否 | 名称
danWei | - | String | 否 | 单位
jiXing | - | String | 否 | 剂型
jiLiang | - | String | 否 | 剂量
baoZhuangLiang | - | Integer | 否 | 包装量
shouJia | - | Number | 否 | 售价
yiCiYL | - | Number | 否 | 一次用量
jiLiangDW | - | String | 否 | 剂量单位
zhiXingPL | - | String | 否 | 执行频率
zhiXingFF | - | String | 否 | 执行方法
geiYaoSJ | - | String | 否 | 给药时间
teShuYF | - | String | 否 | 特殊用法
kaiShiSJ | - | String | 否 | 开始时间
jieShuSJ | - | String | 否 | 结束时间
chiXuTS | - | Integer | 否 | 持续天数
yiZhuCS | - | Integer | 否 | 医嘱次数
yiZhuLB | - | String | 否 | 医嘱类别（1西药，2中成药，3草药）
yiZhuJB | - | String | 否 | 医嘱级别
sheBaoBS | - | String | 否 | 社保标识
huLiZT | - | String | 否 | 护理状态
yiZhuZT | - | String | 否 | 医嘱状态
yiShengID | - | Integer | 否 | 医生ID
yiZhuSJ | - | String | 否 | 医嘱时间（实际表示停止时间）
shouCiLRSJ | - | String | 否 | 首次录入时间
xiuGaiYSID | - | Integer | 否 | 修改医生ID
yaoFangDM | - | String | 否 | 药房代码
quYaoFS | - | String | 否 | 取药方式
shouFeiSL | - | Number | 否 | 收费数量
shouFeiCS | - | Integer | 否 | 收费次数
buFeiZL | - | Number | 否 | 补费总量
changQiBY | - | Number | 否 | 长期补药
shouFeiRYID | - | Integer | 否 | 收费人员ID
shouFeiSJ | - | String | 否 | 收费时间
weiShouBZ | - | String | 否 | 未收标志
bingQuBZ | - | String | 否 | 病区备注
daoRuRYID | - | Integer | 否 | 导入人员ID
daoRuSJ | - | String | 否 | 导入时间
tingZhiDRRYID | - | Integer | 否 | 停止导入人员ID
tingZhiDRSJ | - | String | 否 | 停止导入时间
zhiXingJLSJ | - | String | 否 | 执行记录时间
xiuGaiRYID | - | Integer | 否 | 修改人员ID
xiuGaiSJ | - | String | 否 | 修改时间
leiBie | - | String | 否 | 类别（1=医生医嘱，2=护士医嘱）
tiZaoQYTS | - | Integer | 否 | 提早取药天数
zhiLiaoZuID | - | Integer | 否 | 治疗组ID
piShiJG | - | String | 否 | 皮试结果
zhiXingRYID | - | Integer | 否 | 执行人员ID
shenHeRYID | - | Integer | 否 | 审核人员ID
cheXiaoZT | - | String | 否 | 撤销状态
shenHeYSID | - | Integer | 否 | 审核医生ID
yiZhuSHSJ | - | String | 否 | 医嘱审核时间
piShiSJ | - | String | 否 | 皮试时间
yiZhuBZ | - | String | 否 | 医嘱备注
tongZhiDanID | - | Integer | 否 | 通知单ID
teShuKJYWHZDID | - | Integer | 否 | 特殊抗菌药物会诊单ID
shiFouZB | - | String | 否 | 是否自备
shiFouBL | - | String | 否 | 是否补录
mianPiShi | - | String | 否 | 免皮试
kaiDanYSZKID | - | Integer | 否 | 开单医生专科ID
yongYaoTS | - | Integer | 否 | 用药天数
xiangMuBM | - | String | 否 | 项目编码
xiangMuLX | - | String | 否 | 项目类型
shuangQianMingYHID | - | Integer | 否 | 双签名用户ID
shuangQianMingSJ | - | String | 否 | 双签名时间
shuangQianMingBZ | - | String | 否 | 双签名备注
shenQingDanSJID | - | Integer | 否 | 申请单数据ID
yaoPinLB | - | String | 否 | 药品类别，0=本院药品,1=外购药品,2=赠送药品
shenFangCLJG | - | String | 否 | 审方处理结果
sanLianKJYWHZDID | - | Integer | 否 | 三联抗菌药物会诊单ID
jinRiCY | - | String | 否 | 今日出院
zanShiBQ | - | String | 否 | 暂时不取
piShiGCSJ | - | String | 否 | 皮试观察时间
piShiGCYHID | - | Integer | 否 | 皮试观察用户ID
yaoPinDS | - | Number | 否 | 药品滴速
changWaiYYHZDID | - | Integer | 否 | 肠外营养会诊单id
linShiTL | - | Number | 否 | 临时调量
changQiBF | - | String | 否 | 长期调量
zhiXingPL2 | - | String | 否 | 执行频率2（时间针频率）
zhongChengYaoZD | - | String | 否 | 中成药诊断
zhongChengYaoZZ | - | String | 否 | 中成药症状
yaoPinDSBZ | - | String | 否 | 药品滴速备注
waiGouYPSQID | - | Integer | 否 | 外购药品申请ID
laiYuan | - | String | 否 | 来源
shenFangJK | - | String | 否 | 审方接口
maZuiYT | - | String | 否 | 麻醉用途
xianDingFW | - | String | 否 | 限定范围
geiYaoTJGLID | - | Integer | 否 | 给药途径关联id
mingCheng2 | - | String | 否 | 名称2
jiGouDM | - | String | 否 | 机构代码
jinRiLT | - | String | 否 | 今日临停
tingZhiLX | - | String | 否 | 停止类型
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/修改_新增药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/insertYaoPinYzReturnYz

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
gcp | - | String | 否 | -
yiZhuID | - | Integer | 否 | 医嘱ID
bingLiID | - | Integer | 否 | 病历ID
zhuYuanID | - | Integer | 否 | 住院ID
bingRenXM | - | String | 否 | 病人姓名
bingQuID | - | Integer | 否 | 病区ID
zhuanKeID | - | Integer | 否 | 专科ID
ziFei | - | String | 否 | 自费
zuHao | - | Integer | 否 | 组号
yaoPinID | - | Integer | 否 | 药品ID
fenLeiMa | - | String | 否 | 分类码
mingCheng | - | String | 否 | 名称
danWei | - | String | 否 | 单位
jiXing | - | String | 否 | 剂型
jiLiang | - | String | 否 | 剂量
baoZhuangLiang | - | Integer | 否 | 包装量
shouJia | - | Number | 否 | 售价
yiCiYL | - | Number | 否 | 一次用量
jiLiangDW | - | String | 否 | 剂量单位
zhiXingPL | - | String | 否 | 执行频率
zhiXingFF | - | String | 否 | 执行方法
geiYaoSJ | - | String | 否 | 给药时间
teShuYF | - | String | 否 | 特殊用法
kaiShiSJ | - | String | 否 | 开始时间
jieShuSJ | - | String | 否 | 结束时间
chiXuTS | - | Integer | 否 | 持续天数
yiZhuCS | - | Integer | 否 | 医嘱次数
yiZhuLB | - | String | 否 | 医嘱类别（1西药，2中成药，3草药）
yiZhuJB | - | String | 否 | 医嘱级别
sheBaoBS | - | String | 否 | 社保标识
huLiZT | - | String | 否 | 护理状态
yiZhuZT | - | String | 否 | 医嘱状态
yiShengID | - | Integer | 否 | 医生ID
yiZhuSJ | - | String | 否 | 医嘱时间（实际表示停止时间）
shouCiLRSJ | - | String | 否 | 首次录入时间
xiuGaiYSID | - | Integer | 否 | 修改医生ID
yaoFangDM | - | String | 否 | 药房代码
quYaoFS | - | String | 否 | 取药方式
shouFeiSL | - | Number | 否 | 收费数量
shouFeiCS | - | Integer | 否 | 收费次数
buFeiZL | - | Number | 否 | 补费总量
changQiBY | - | Number | 否 | 长期补药
shouFeiRYID | - | Integer | 否 | 收费人员ID
shouFeiSJ | - | String | 否 | 收费时间
weiShouBZ | - | String | 否 | 未收标志
bingQuBZ | - | String | 否 | 病区备注
daoRuRYID | - | Integer | 否 | 导入人员ID
daoRuSJ | - | String | 否 | 导入时间
tingZhiDRRYID | - | Integer | 否 | 停止导入人员ID
tingZhiDRSJ | - | String | 否 | 停止导入时间
zhiXingJLSJ | - | String | 否 | 执行记录时间
xiuGaiRYID | - | Integer | 否 | 修改人员ID
xiuGaiSJ | - | String | 否 | 修改时间
leiBie | - | String | 否 | 类别（1=医生医嘱，2=护士医嘱）
tiZaoQYTS | - | Integer | 否 | 提早取药天数
zhiLiaoZuID | - | Integer | 否 | 治疗组ID
piShiJG | - | String | 否 | 皮试结果
zhiXingRYID | - | Integer | 否 | 执行人员ID
shenHeRYID | - | Integer | 否 | 审核人员ID
cheXiaoZT | - | String | 否 | 撤销状态
shenHeYSID | - | Integer | 否 | 审核医生ID
yiZhuSHSJ | - | String | 否 | 医嘱审核时间
piShiSJ | - | String | 否 | 皮试时间
yiZhuBZ | - | String | 否 | 医嘱备注
tongZhiDanID | - | Integer | 否 | 通知单ID
teShuKJYWHZDID | - | Integer | 否 | 特殊抗菌药物会诊单ID
shiFouZB | - | String | 否 | 是否自备
shiFouBL | - | String | 否 | 是否补录
mianPiShi | - | String | 否 | 免皮试
kaiDanYSZKID | - | Integer | 否 | 开单医生专科ID
yongYaoTS | - | Integer | 否 | 用药天数
xiangMuBM | - | String | 否 | 项目编码
xiangMuLX | - | String | 否 | 项目类型
shuangQianMingYHID | - | Integer | 否 | 双签名用户ID
shuangQianMingSJ | - | String | 否 | 双签名时间
shuangQianMingBZ | - | String | 否 | 双签名备注
shenQingDanSJID | - | Integer | 否 | 申请单数据ID
yaoPinLB | - | String | 否 | 药品类别，0=本院药品,1=外购药品,2=赠送药品
shenFangCLJG | - | String | 否 | 审方处理结果
sanLianKJYWHZDID | - | Integer | 否 | 三联抗菌药物会诊单ID
jinRiCY | - | String | 否 | 今日出院
zanShiBQ | - | String | 否 | 暂时不取
piShiGCSJ | - | String | 否 | 皮试观察时间
piShiGCYHID | - | Integer | 否 | 皮试观察用户ID
yaoPinDS | - | Number | 否 | 药品滴速
changWaiYYHZDID | - | Integer | 否 | 肠外营养会诊单id
linShiTL | - | Number | 否 | 临时调量
changQiBF | - | String | 否 | 长期调量
zhiXingPL2 | - | String | 否 | 执行频率2（时间针频率）
zhongChengYaoZD | - | String | 否 | 中成药诊断
zhongChengYaoZZ | - | String | 否 | 中成药症状
yaoPinDSBZ | - | String | 否 | 药品滴速备注
waiGouYPSQID | - | Integer | 否 | 外购药品申请ID
laiYuan | - | String | 否 | 来源
shenFangJK | - | String | 否 | 审方接口
maZuiYT | - | String | 否 | 麻醉用途
xianDingFW | - | String | 否 | 限定范围
geiYaoTJGLID | - | Integer | 否 | 给药途径关联id
mingCheng2 | - | String | 否 | 名称2
jiGouDM | - | String | 否 | 机构代码
jinRiLT | - | String | 否 | 今日临停
tingZhiLX | - | String | 否 | 停止类型
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.gcp | - | String | 
data.yiZhuID | - | Integer | 医嘱ID
data.bingLiID | - | Integer | 病历ID
data.zhuYuanID | - | Integer | 住院ID
data.bingRenXM | - | String | 病人姓名
data.bingQuID | - | Integer | 病区ID
data.zhuanKeID | - | Integer | 专科ID
data.ziFei | - | String | 自费
data.zuHao | - | Integer | 组号
data.yaoPinID | - | Integer | 药品ID
data.fenLeiMa | - | String | 分类码
data.mingCheng | - | String | 名称
data.danWei | - | String | 单位
data.jiXing | - | String | 剂型
data.jiLiang | - | String | 剂量
data.baoZhuangLiang | - | Integer | 包装量
data.shouJia | - | Number | 售价
data.yiCiYL | - | Number | 一次用量
data.jiLiangDW | - | String | 剂量单位
data.zhiXingPL | - | String | 执行频率
data.zhiXingFF | - | String | 执行方法
data.geiYaoSJ | - | String | 给药时间
data.teShuYF | - | String | 特殊用法
data.kaiShiSJ | - | String | 开始时间
data.jieShuSJ | - | String | 结束时间
data.chiXuTS | - | Integer | 持续天数
data.yiZhuCS | - | Integer | 医嘱次数
data.yiZhuLB | - | String | 医嘱类别（1西药，2中成药，3草药）
data.yiZhuJB | - | String | 医嘱级别
data.sheBaoBS | - | String | 社保标识
data.huLiZT | - | String | 护理状态
data.yiZhuZT | - | String | 医嘱状态
data.yiShengID | - | Integer | 医生ID
data.yiZhuSJ | - | String | 医嘱时间（实际表示停止时间）
data.shouCiLRSJ | - | String | 首次录入时间
data.xiuGaiYSID | - | Integer | 修改医生ID
data.yaoFangDM | - | String | 药房代码
data.quYaoFS | - | String | 取药方式
data.shouFeiSL | - | Number | 收费数量
data.shouFeiCS | - | Integer | 收费次数
data.buFeiZL | - | Number | 补费总量
data.changQiBY | - | Number | 长期补药
data.shouFeiRYID | - | Integer | 收费人员ID
data.shouFeiSJ | - | String | 收费时间
data.weiShouBZ | - | String | 未收标志
data.bingQuBZ | - | String | 病区备注
data.daoRuRYID | - | Integer | 导入人员ID
data.daoRuSJ | - | String | 导入时间
data.tingZhiDRRYID | - | Integer | 停止导入人员ID
data.tingZhiDRSJ | - | String | 停止导入时间
data.zhiXingJLSJ | - | String | 执行记录时间
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
data.leiBie | - | String | 类别（1=医生医嘱，2=护士医嘱）
data.tiZaoQYTS | - | Integer | 提早取药天数
data.zhiLiaoZuID | - | Integer | 治疗组ID
data.piShiJG | - | String | 皮试结果
data.zhiXingRYID | - | Integer | 执行人员ID
data.shenHeRYID | - | Integer | 审核人员ID
data.cheXiaoZT | - | String | 撤销状态
data.shenHeYSID | - | Integer | 审核医生ID
data.yiZhuSHSJ | - | String | 医嘱审核时间
data.piShiSJ | - | String | 皮试时间
data.yiZhuBZ | - | String | 医嘱备注
data.tongZhiDanID | - | Integer | 通知单ID
data.teShuKJYWHZDID | - | Integer | 特殊抗菌药物会诊单ID
data.shiFouZB | - | String | 是否自备
data.shiFouBL | - | String | 是否补录
data.mianPiShi | - | String | 免皮试
data.kaiDanYSZKID | - | Integer | 开单医生专科ID
data.yongYaoTS | - | Integer | 用药天数
data.xiangMuBM | - | String | 项目编码
data.xiangMuLX | - | String | 项目类型
data.shuangQianMingYHID | - | Integer | 双签名用户ID
data.shuangQianMingSJ | - | String | 双签名时间
data.shuangQianMingBZ | - | String | 双签名备注
data.shenQingDanSJID | - | Integer | 申请单数据ID
data.yaoPinLB | - | String | 药品类别，0=本院药品,1=外购药品,2=赠送药品
data.shenFangCLJG | - | String | 审方处理结果
data.sanLianKJYWHZDID | - | Integer | 三联抗菌药物会诊单ID
data.jinRiCY | - | String | 今日出院
data.zanShiBQ | - | String | 暂时不取
data.piShiGCSJ | - | String | 皮试观察时间
data.piShiGCYHID | - | Integer | 皮试观察用户ID
data.yaoPinDS | - | Number | 药品滴速
data.changWaiYYHZDID | - | Integer | 肠外营养会诊单id
data.linShiTL | - | Number | 临时调量
data.changQiBF | - | String | 长期调量
data.zhiXingPL2 | - | String | 执行频率2（时间针频率）
data.zhongChengYaoZD | - | String | 中成药诊断
data.zhongChengYaoZZ | - | String | 中成药症状
data.yaoPinDSBZ | - | String | 药品滴速备注
data.waiGouYPSQID | - | Integer | 外购药品申请ID
data.laiYuan | - | String | 来源
data.shenFangJK | - | String | 审方接口
data.maZuiYT | - | String | 麻醉用途
data.xianDingFW | - | String | 限定范围
data.geiYaoTJGLID | - | Integer | 给药途径关联id
data.mingCheng2 | - | String | 名称2
data.jiGouDM | - | String | 机构代码
data.jinRiLT | - | String | 今日临停
data.tingZhiLX | - | String | 停止类型
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/保存为药品模板
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/saveAsYaoPinMb

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"bingLiID":"","zuHaos":[{}],"leiXing":"","muBanMC":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
zuHaos | - | Array | 否 | 组号列表
leiXing | - | String | 否 | 类型：cyy=常用药模板，grmb=个人模板
muBanMC | - | String | 否 | 模板名称，仅个人模板需要
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/【备注】特殊使用级抗菌药物使用情况统计
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/saveBeiZhuTsKjywSyqkTj

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"yiZhuID":"","beiZhu":"","xiuGaiRYID":"","xiuGaiSJ":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
yiZhuID | - | Integer | 否 | 医嘱ID
beiZhu | - | String | 否 | 备注
xiuGaiRYID | - | Integer | 否 | 修改人员ID
xiuGaiSJ | - | String | 否 | 修改时间
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"yiZhuID":"","beiZhu":"","xiuGaiRYID":"","xiuGaiSJ":""},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.yiZhuID | - | Integer | 医嘱ID
data.beiZhu | - | String | 备注
data.xiuGaiRYID | - | Integer | 修改人员ID
data.xiuGaiSJ | - | String | 修改时间
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/修改_保存住院快递地址
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/saveZyKuaiDiDz

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"bingLiID":"","shouHuoRenXM":"","lianXiDH":"","shengFen":"","chengShi":"","qu":"","lianXiDZ":"","xiuGaiSJ":"","xiuGaiYHID":"","beiZhu":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Integer | 否 | 病历ID
shouHuoRenXM | - | String | 否 | 收货人姓名
lianXiDH | - | String | 否 | 联系电话
shengFen | - | String | 否 | 省份
chengShi | - | String | 否 | 城市
qu | - | String | 否 | 区
lianXiDZ | - | String | 否 | 联系地址
xiuGaiSJ | - | String | 否 | 修改时间
xiuGaiYHID | - | Integer | 否 | 修改用户ID
beiZhu | - | String | 否 | 备注
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/【审批】特殊使用级抗菌药物使用情况统计
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/shenPiTsKjywSyqkTj

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"zhiDingZKID":"","yaoPinZL":"","kaiShiSJ":"","jieShuSJ":"","shenPiJLS":"","shenPiYJ":"","zhuangTaiBZ":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
zhiDingZKID | - | Integer | 否 | 指定专科ID
yaoPinZL | - | String | 否 | 药品种类
kaiShiSJ | - | String | 否 | 开始时间yyyy-MM-dd HH:mm:ss
jieShuSJ | - | String | 否 | 结束时间yyyy-MM-dd HH:mm:ss
shenPiJLS | - | Integer | 否 | 审批记录数
shenPiYJ | - | String | 否 | 审批意见，纯文本
zhuangTaiBZ | - | String | 否 | 状态标志
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/特殊使用级抗菌药物使用情况统计
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/teShuKjywSyqkTj

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"kaiShiSJ":"","jieShuSJ":"","leiXing":"","leiXingID":"","leiBie":"","leiBieID":"","zhuangTaiBZList":[{}]}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
kaiShiSJ | - | String | 否 | 开始时间(yyyy-MM-dd)
jieShuSJ | - | String | 否 | 结束时间(yyyy-MM-dd)
leiXing | - | String | 否 | 类型(ys=处方医生,ks=科室)
leiXingID | - | Integer | 否 | 类型ID(医生ID或科室ID)
leiBie | - | String | 否 | 类别(ypmc=药品名称,ypzl=药品种类)
leiBieID | - | String | 否 | 类别ID(药品ID或药品种类)
zhuangTaiBZList | - | Array | 否 | 会诊单状态标志
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/根据处方号更新草药煎药记录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/updateCaoYaoJYJLByCfh

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"bingRenXM":"","zhuYuanID":"","zhuanKeID":"","bingQuID":"","chuangWeiHao":"","zuHao":"","kaiFangTS":"","jianYaoTS":"","yaoFangDM":"","chuFangHao":"","shouFeiSJ":"","shouFeiRYID":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingRenXM | - | String | 否 | 病人姓名
zhuYuanID | - | Integer | 否 | 住院id
zhuanKeID | - | Integer | 否 | 专科id
bingQuID | - | Integer | 否 | 病区id
chuangWeiHao | - | String | 否 | 床位号
zuHao | - | Integer | 否 | 组号
kaiFangTS | - | Integer | 否 | 开方贴数
jianYaoTS | - | Integer | 否 | 煎药贴数
yaoFangDM | - | String | 否 | 药房代码
chuFangHao | - | String | 否 | 处方号
shouFeiSJ | - | String | 否 | 收费时间
shouFeiRYID | - | Integer | 否 | 收费人员id
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/修改_根据医嘱ID和医嘱状态更新药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/updateYaoPinYzByIdAndZt?yiZhuID=&zhuangTaiBZ=

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
yiZhuID | - | Text | 是 | 医嘱ID
zhuangTaiBZ | - | Text | 是 | 医嘱状态
#### 请求Body参数
```javascript
{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
gcp | - | String | 否 | -
yiZhuID | - | Integer | 否 | 医嘱ID
bingLiID | - | Integer | 否 | 病历ID
zhuYuanID | - | Integer | 否 | 住院ID
bingRenXM | - | String | 否 | 病人姓名
bingQuID | - | Integer | 否 | 病区ID
zhuanKeID | - | Integer | 否 | 专科ID
ziFei | - | String | 否 | 自费
zuHao | - | Integer | 否 | 组号
yaoPinID | - | Integer | 否 | 药品ID
fenLeiMa | - | String | 否 | 分类码
mingCheng | - | String | 否 | 名称
danWei | - | String | 否 | 单位
jiXing | - | String | 否 | 剂型
jiLiang | - | String | 否 | 剂量
baoZhuangLiang | - | Integer | 否 | 包装量
shouJia | - | Number | 否 | 售价
yiCiYL | - | Number | 否 | 一次用量
jiLiangDW | - | String | 否 | 剂量单位
zhiXingPL | - | String | 否 | 执行频率
zhiXingFF | - | String | 否 | 执行方法
geiYaoSJ | - | String | 否 | 给药时间
teShuYF | - | String | 否 | 特殊用法
kaiShiSJ | - | String | 否 | 开始时间
jieShuSJ | - | String | 否 | 结束时间
chiXuTS | - | Integer | 否 | 持续天数
yiZhuCS | - | Integer | 否 | 医嘱次数
yiZhuLB | - | String | 否 | 医嘱类别（1西药，2中成药，3草药）
yiZhuJB | - | String | 否 | 医嘱级别
sheBaoBS | - | String | 否 | 社保标识
huLiZT | - | String | 否 | 护理状态
yiZhuZT | - | String | 否 | 医嘱状态
yiShengID | - | Integer | 否 | 医生ID
yiZhuSJ | - | String | 否 | 医嘱时间（实际表示停止时间）
shouCiLRSJ | - | String | 否 | 首次录入时间
xiuGaiYSID | - | Integer | 否 | 修改医生ID
yaoFangDM | - | String | 否 | 药房代码
quYaoFS | - | String | 否 | 取药方式
shouFeiSL | - | Number | 否 | 收费数量
shouFeiCS | - | Integer | 否 | 收费次数
buFeiZL | - | Number | 否 | 补费总量
changQiBY | - | Number | 否 | 长期补药
shouFeiRYID | - | Integer | 否 | 收费人员ID
shouFeiSJ | - | String | 否 | 收费时间
weiShouBZ | - | String | 否 | 未收标志
bingQuBZ | - | String | 否 | 病区备注
daoRuRYID | - | Integer | 否 | 导入人员ID
daoRuSJ | - | String | 否 | 导入时间
tingZhiDRRYID | - | Integer | 否 | 停止导入人员ID
tingZhiDRSJ | - | String | 否 | 停止导入时间
zhiXingJLSJ | - | String | 否 | 执行记录时间
xiuGaiRYID | - | Integer | 否 | 修改人员ID
xiuGaiSJ | - | String | 否 | 修改时间
leiBie | - | String | 否 | 类别（1=医生医嘱，2=护士医嘱）
tiZaoQYTS | - | Integer | 否 | 提早取药天数
zhiLiaoZuID | - | Integer | 否 | 治疗组ID
piShiJG | - | String | 否 | 皮试结果
zhiXingRYID | - | Integer | 否 | 执行人员ID
shenHeRYID | - | Integer | 否 | 审核人员ID
cheXiaoZT | - | String | 否 | 撤销状态
shenHeYSID | - | Integer | 否 | 审核医生ID
yiZhuSHSJ | - | String | 否 | 医嘱审核时间
piShiSJ | - | String | 否 | 皮试时间
yiZhuBZ | - | String | 否 | 医嘱备注
tongZhiDanID | - | Integer | 否 | 通知单ID
teShuKJYWHZDID | - | Integer | 否 | 特殊抗菌药物会诊单ID
shiFouZB | - | String | 否 | 是否自备
shiFouBL | - | String | 否 | 是否补录
mianPiShi | - | String | 否 | 免皮试
kaiDanYSZKID | - | Integer | 否 | 开单医生专科ID
yongYaoTS | - | Integer | 否 | 用药天数
xiangMuBM | - | String | 否 | 项目编码
xiangMuLX | - | String | 否 | 项目类型
shuangQianMingYHID | - | Integer | 否 | 双签名用户ID
shuangQianMingSJ | - | String | 否 | 双签名时间
shuangQianMingBZ | - | String | 否 | 双签名备注
shenQingDanSJID | - | Integer | 否 | 申请单数据ID
yaoPinLB | - | String | 否 | 药品类别，0=本院药品,1=外购药品,2=赠送药品
shenFangCLJG | - | String | 否 | 审方处理结果
sanLianKJYWHZDID | - | Integer | 否 | 三联抗菌药物会诊单ID
jinRiCY | - | String | 否 | 今日出院
zanShiBQ | - | String | 否 | 暂时不取
piShiGCSJ | - | String | 否 | 皮试观察时间
piShiGCYHID | - | Integer | 否 | 皮试观察用户ID
yaoPinDS | - | Number | 否 | 药品滴速
changWaiYYHZDID | - | Integer | 否 | 肠外营养会诊单id
linShiTL | - | Number | 否 | 临时调量
changQiBF | - | String | 否 | 长期调量
zhiXingPL2 | - | String | 否 | 执行频率2（时间针频率）
zhongChengYaoZD | - | String | 否 | 中成药诊断
zhongChengYaoZZ | - | String | 否 | 中成药症状
yaoPinDSBZ | - | String | 否 | 药品滴速备注
waiGouYPSQID | - | Integer | 否 | 外购药品申请ID
laiYuan | - | String | 否 | 来源
shenFangJK | - | String | 否 | 审方接口
maZuiYT | - | String | 否 | 麻醉用途
xianDingFW | - | String | 否 | 限定范围
geiYaoTJGLID | - | Integer | 否 | 给药途径关联id
mingCheng2 | - | String | 否 | 名称2
jiGouDM | - | String | 否 | 机构代码
jinRiLT | - | String | 否 | 今日临停
tingZhiLX | - | String | 否 | 停止类型
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院药品医嘱/修改_根据医嘱ID更新药品医嘱
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/DrugInpatient/updateYaoPinYzByYzids

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
currUserId | - | Text | 是 | currUserId
#### 请求Body参数
```javascript
{"gcp":"","yiZhuID":"","bingLiID":"","zhuYuanID":"","bingRenXM":"","bingQuID":"","zhuanKeID":"","ziFei":"","zuHao":"","yaoPinID":"","fenLeiMa":"","mingCheng":"","danWei":"","jiXing":"","jiLiang":"","baoZhuangLiang":"","shouJia":"","yiCiYL":"","jiLiangDW":"","zhiXingPL":"","zhiXingFF":"","geiYaoSJ":"","teShuYF":"","kaiShiSJ":"","jieShuSJ":"","chiXuTS":"","yiZhuCS":"","yiZhuLB":"","yiZhuJB":"","sheBaoBS":"","huLiZT":"","yiZhuZT":"","yiShengID":"","yiZhuSJ":"","shouCiLRSJ":"","xiuGaiYSID":"","yaoFangDM":"","quYaoFS":"","shouFeiSL":"","shouFeiCS":"","buFeiZL":"","changQiBY":"","shouFeiRYID":"","shouFeiSJ":"","weiShouBZ":"","bingQuBZ":"","daoRuRYID":"","daoRuSJ":"","tingZhiDRRYID":"","tingZhiDRSJ":"","zhiXingJLSJ":"","xiuGaiRYID":"","xiuGaiSJ":"","leiBie":"","tiZaoQYTS":"","zhiLiaoZuID":"","piShiJG":"","zhiXingRYID":"","shenHeRYID":"","cheXiaoZT":"","shenHeYSID":"","yiZhuSHSJ":"","piShiSJ":"","yiZhuBZ":"","tongZhiDanID":"","teShuKJYWHZDID":"","shiFouZB":"","shiFouBL":"","mianPiShi":"","kaiDanYSZKID":"","yongYaoTS":"","xiangMuBM":"","xiangMuLX":"","shuangQianMingYHID":"","shuangQianMingSJ":"","shuangQianMingBZ":"","shenQingDanSJID":"","yaoPinLB":"","shenFangCLJG":"","sanLianKJYWHZDID":"","jinRiCY":"","zanShiBQ":"","piShiGCSJ":"","piShiGCYHID":"","yaoPinDS":"","changWaiYYHZDID":"","linShiTL":"","changQiBF":"","zhiXingPL2":"","zhongChengYaoZD":"","zhongChengYaoZZ":"","yaoPinDSBZ":"","waiGouYPSQID":"","laiYuan":"","shenFangJK":"","maZuiYT":"","xianDingFW":"","geiYaoTJGLID":"","mingCheng2":"","jiGouDM":"","jinRiLT":"","tingZhiLX":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
gcp | - | String | 否 | -
yiZhuID | - | Integer | 否 | 医嘱ID
bingLiID | - | Integer | 否 | 病历ID
zhuYuanID | - | Integer | 否 | 住院ID
bingRenXM | - | String | 否 | 病人姓名
bingQuID | - | Integer | 否 | 病区ID
zhuanKeID | - | Integer | 否 | 专科ID
ziFei | - | String | 否 | 自费
zuHao | - | Integer | 否 | 组号
yaoPinID | - | Integer | 否 | 药品ID
fenLeiMa | - | String | 否 | 分类码
mingCheng | - | String | 否 | 名称
danWei | - | String | 否 | 单位
jiXing | - | String | 否 | 剂型
jiLiang | - | String | 否 | 剂量
baoZhuangLiang | - | Integer | 否 | 包装量
shouJia | - | Number | 否 | 售价
yiCiYL | - | Number | 否 | 一次用量
jiLiangDW | - | String | 否 | 剂量单位
zhiXingPL | - | String | 否 | 执行频率
zhiXingFF | - | String | 否 | 执行方法
geiYaoSJ | - | String | 否 | 给药时间
teShuYF | - | String | 否 | 特殊用法
kaiShiSJ | - | String | 否 | 开始时间
jieShuSJ | - | String | 否 | 结束时间
chiXuTS | - | Integer | 否 | 持续天数
yiZhuCS | - | Integer | 否 | 医嘱次数
yiZhuLB | - | String | 否 | 医嘱类别（1西药，2中成药，3草药）
yiZhuJB | - | String | 否 | 医嘱级别
sheBaoBS | - | String | 否 | 社保标识
huLiZT | - | String | 否 | 护理状态
yiZhuZT | - | String | 否 | 医嘱状态
yiShengID | - | Integer | 否 | 医生ID
yiZhuSJ | - | String | 否 | 医嘱时间（实际表示停止时间）
shouCiLRSJ | - | String | 否 | 首次录入时间
xiuGaiYSID | - | Integer | 否 | 修改医生ID
yaoFangDM | - | String | 否 | 药房代码
quYaoFS | - | String | 否 | 取药方式
shouFeiSL | - | Number | 否 | 收费数量
shouFeiCS | - | Integer | 否 | 收费次数
buFeiZL | - | Number | 否 | 补费总量
changQiBY | - | Number | 否 | 长期补药
shouFeiRYID | - | Integer | 否 | 收费人员ID
shouFeiSJ | - | String | 否 | 收费时间
weiShouBZ | - | String | 否 | 未收标志
bingQuBZ | - | String | 否 | 病区备注
daoRuRYID | - | Integer | 否 | 导入人员ID
daoRuSJ | - | String | 否 | 导入时间
tingZhiDRRYID | - | Integer | 否 | 停止导入人员ID
tingZhiDRSJ | - | String | 否 | 停止导入时间
zhiXingJLSJ | - | String | 否 | 执行记录时间
xiuGaiRYID | - | Integer | 否 | 修改人员ID
xiuGaiSJ | - | String | 否 | 修改时间
leiBie | - | String | 否 | 类别（1=医生医嘱，2=护士医嘱）
tiZaoQYTS | - | Integer | 否 | 提早取药天数
zhiLiaoZuID | - | Integer | 否 | 治疗组ID
piShiJG | - | String | 否 | 皮试结果
zhiXingRYID | - | Integer | 否 | 执行人员ID
shenHeRYID | - | Integer | 否 | 审核人员ID
cheXiaoZT | - | String | 否 | 撤销状态
shenHeYSID | - | Integer | 否 | 审核医生ID
yiZhuSHSJ | - | String | 否 | 医嘱审核时间
piShiSJ | - | String | 否 | 皮试时间
yiZhuBZ | - | String | 否 | 医嘱备注
tongZhiDanID | - | Integer | 否 | 通知单ID
teShuKJYWHZDID | - | Integer | 否 | 特殊抗菌药物会诊单ID
shiFouZB | - | String | 否 | 是否自备
shiFouBL | - | String | 否 | 是否补录
mianPiShi | - | String | 否 | 免皮试
kaiDanYSZKID | - | Integer | 否 | 开单医生专科ID
yongYaoTS | - | Integer | 否 | 用药天数
xiangMuBM | - | String | 否 | 项目编码
xiangMuLX | - | String | 否 | 项目类型
shuangQianMingYHID | - | Integer | 否 | 双签名用户ID
shuangQianMingSJ | - | String | 否 | 双签名时间
shuangQianMingBZ | - | String | 否 | 双签名备注
shenQingDanSJID | - | Integer | 否 | 申请单数据ID
yaoPinLB | - | String | 否 | 药品类别，0=本院药品,1=外购药品,2=赠送药品
shenFangCLJG | - | String | 否 | 审方处理结果
sanLianKJYWHZDID | - | Integer | 否 | 三联抗菌药物会诊单ID
jinRiCY | - | String | 否 | 今日出院
zanShiBQ | - | String | 否 | 暂时不取
piShiGCSJ | - | String | 否 | 皮试观察时间
piShiGCYHID | - | Integer | 否 | 皮试观察用户ID
yaoPinDS | - | Number | 否 | 药品滴速
changWaiYYHZDID | - | Integer | 否 | 肠外营养会诊单id
linShiTL | - | Number | 否 | 临时调量
changQiBF | - | String | 否 | 长期调量
zhiXingPL2 | - | String | 否 | 执行频率2（时间针频率）
zhongChengYaoZD | - | String | 否 | 中成药诊断
zhongChengYaoZZ | - | String | 否 | 中成药症状
yaoPinDSBZ | - | String | 否 | 药品滴速备注
waiGouYPSQID | - | Integer | 否 | 外购药品申请ID
laiYuan | - | String | 否 | 来源
shenFangJK | - | String | 否 | 审方接口
maZuiYT | - | String | 否 | 麻醉用途
xianDingFW | - | String | 否 | 限定范围
geiYaoTJGLID | - | Integer | 否 | 给药途径关联id
mingCheng2 | - | String | 否 | 名称2
jiGouDM | - | String | 否 | 机构代码
jinRiLT | - | String | 否 | 今日临停
tingZhiLX | - | String | 否 | 停止类型
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Boolean | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 