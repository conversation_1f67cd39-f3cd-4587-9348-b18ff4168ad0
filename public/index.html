<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <meta name="renderer" content="webkit" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
    />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= webpackConfig.name %></title>
    <style>
      html,
      body,
      #app {
        height: 100%;
        padding: 0;
        margin: 0;
      }

      .chromeframe {
        padding: 0.2em 0;
        margin: 0.2em 0;
        color: #000;
        background: #ccc;
      }

      #loader-wrapper {
        position: fixed;
        top: 0;
        left: 0;
        z-index: 999999;
        width: 100%;
        height: 100%;
      }

      #loader {
        position: relative;
        top: 50%;
        left: 50%;
        z-index: 1001;
        display: block;
        width: 150px;
        height: 150px;
        margin: -75px 0 0 -75px;
        border: 3px solid transparent;
        border-top-color: #fff;
        border-radius: 50%;
        animation: spin 2s linear infinite;
      }

      #loader::before {
        position: absolute;
        inset: 5px;
        content: '';
        border: 3px solid transparent;
        border-top-color: #fff;
        border-radius: 50%;
        animation: spin 3s linear infinite;
      }

      #loader::after {
        position: absolute;
        inset: 15px;
        content: '';
        border: 3px solid transparent;
        border-top-color: #fff;
        border-radius: 50%;
        animation: spin 1.5s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }

        100% {
          transform: rotate(360deg);
        }
      }

      #loader-wrapper .loader-section {
        position: fixed;
        top: 0;
        z-index: 1000;
        width: 51%;
        height: 100%;
        background: #7171c6;
        transform: translateX(0);
      }

      #loader-wrapper .loader-section.section-left {
        left: 0;
      }

      #loader-wrapper .loader-section.section-right {
        right: 0;
      }

      .loaded #loader-wrapper .loader-section.section-left {
        transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        transform: translateX(-100%);
      }

      .loaded #loader-wrapper .loader-section.section-right {
        transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        transform: translateX(100%);
      }

      .loaded #loader {
        opacity: 0;
        transition: all 0.3s ease-out;
      }

      .loaded #loader-wrapper {
        visibility: hidden;
        transition: all 0.3s 1s ease-out;
        transform: translateY(-100%);
      }

      .no-js #loader-wrapper {
        display: none;
      }

      .no-js h1 {
        color: #222;
      }

      #loader-wrapper .load_title {
        position: absolute;
        top: 60%;
        z-index: 9999999999999;
        width: 100%;
        font-family: 'Open Sans';
        font-size: 19px;
        line-height: 30px;
        color: #fff;
        text-align: center;
        opacity: 1;
      }

      #loader-wrapper .load_title span {
        font-size: 13px;
        font-style: italic;
        font-weight: normal;
        color: #fff;
        opacity: 0.5;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div id="loader-wrapper">
        <div id="loader"></div>
        <div class="loader-section section-left"></div>
        <div class="loader-section section-right"></div>
        <div class="load_title">正在加载系统资源，请耐心等待</div>
      </div>
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
