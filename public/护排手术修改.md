## 全局公共参数
#### 全局Header参数
参数名 | 示例值 | 参数描述
--- | --- | ---
token | eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiLljZXln7nku4EiLCJpc3MiOiI5OSIsInlpbmdZb25nRE0iOiIxMjAiLCJleHAiOjE3MDI4NjY1ODYsInVzZXJJZCI6MTExMDF9.MFCaM4DNzADFYNnaFSeM_ljMjucdqheBWZfIVUpEV8Y | -
#### 全局Query参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 全局Body参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 全局认证方式
```text
noauth
```
#### 全局预执行脚本
```javascript
暂无预执行脚本
```
#### 全局后执行脚本
```javascript
暂无后执行脚本
```
## /住院手术通知单
```text
Operation Inpatient Controller
```
#### Header参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### Query参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### Body参数
参数名 | 示例值 | 参数描述
--- | --- | ---
暂无参数
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
## /住院手术通知单/护排手术修改_删除修改记录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/operationInpatient/deleteOperationXiuGaiJL

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"id":"","tongZhiDID":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
id | - | Integer | 否 | -
tongZhiDID | - | Integer | 否 | 通知单ID
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院手术通知单/护排手术修改_获取修改记录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/operationInpatient/getOperationXiuGaiJL?bingLiID=&leiBie=&tongZhiDanID=&zhuYuanID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
leiBie | - | Text | 是 | 类别（0=普通，1=心导管，2=外周导管）
tongZhiDanID | - | Text | 是 | 通知单ID
zhuYuanID | - | Text | 是 | 住院ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":[{"id":"","bingLiID":"","tongZhiDID":"","ziDuanMC":"","ziDuanMing":"","shouShuDM":"","shouShuMC":"","jiuMingCheng":"","jiuDaiMa":"","xinMingCheng":"","xinDaiMa":"","zhuangTaiBZ":"","zhuangTaiBZMC":"","xiuGaiRYYHID":"","xiuGaiYHXM":"","xiuGaiSJ":"","shenQingYHID":"","shenQingYHXM":"","shenQingSJ":"","keShiSPYHID":"","keShiSPYHXM":"","keShiSPSJ":"","keShiSPYJ":"","keShiSPYJMC":"","shouShuSSPYHID":"","shouShuSSPYHXM":"","shouShuSSPSJ":"","shouShuSSPYJ":"","shouShuSSPYJMC":""}],"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Array | 
data.id | - | Integer | 
data.bingLiID | - | Integer | 病历ID
data.tongZhiDID | - | Integer | 通知单ID
data.ziDuanMC | - | String | 字段名（中文）
data.ziDuanMing | - | String | 字段名（代码），比如zdys=主刀医生
data.shouShuDM | - | String | 手术代码
data.shouShuMC | - | String | 手术名称
data.jiuMingCheng | - | String | 旧名称
data.jiuDaiMa | - | String | 旧代码
data.xinMingCheng | - | String | 新名称
data.xinDaiMa | - | String | 新代码
data.zhuangTaiBZ | - | String | 状态标志，1=有效，0=失效
data.zhuangTaiBZMC | - | String | 状态标志名称，1=有效，0=失效
data.xiuGaiRYYHID | - | Integer | 修改人员yhid
data.xiuGaiYHXM | - | String | 修改用户姓名
data.xiuGaiSJ | - | String | 修改时间
data.shenQingYHID | - | Integer | 申请用户ID
data.shenQingYHXM | - | String | 申请用户姓名
data.shenQingSJ | - | String | 申请时间
data.keShiSPYHID | - | Integer | 科室审批用户ID
data.keShiSPYHXM | - | String | 科室审批用户姓名
data.keShiSPSJ | - | String | 科室审批时间
data.keShiSPYJ | - | String | 科室审批意见，1=同意，0=不同意，空=未审批
data.keShiSPYJMC | - | String | 科室审批意见名称
data.shouShuSSPYHID | - | Integer | 手术室审批用户ID
data.shouShuSSPYHXM | - | String | 手术室审批用户姓名
data.shouShuSSPSJ | - | String | 手术室审批时间
data.shouShuSSPYJ | - | String | 手术室审批意见，1=同意，0=不同意，空=未审批
data.shouShuSSPYJMC | - | String | 手术室审批意见名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院手术通知单/护排手术修改_初始化
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/operationInpatient/initOperationXiuGaiJL?bingLiID=&leiBie=&tongZhiDanID=&zhuYuanID=

#### 请求方式
> GET

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
#### 请求Query参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
bingLiID | - | Text | 是 | 病历ID
leiBie | - | Text | 是 | 类别（0=普通，1=心导管，2=外周导管）
tongZhiDanID | - | Text | 是 | 通知单ID
zhuYuanID | - | Text | 是 | 住院ID
#### 请求Body参数
```javascript
{}
```
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":{"ziDuanList":[{"daiMa":"","mingCheng":""}]},"errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | Object | 
data.ziDuanList | - | Array | 字段列表，比如：zdys=主刀医生
data.ziDuanList.daiMa | - | String | 代码
data.ziDuanList.mingCheng | - | String | 名称
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 
## /住院手术通知单/护排手术修改_保存修改记录
```text
暂无描述
```
#### 接口状态
> 开发中

#### 接口URL
> **************:38081/medicaladvice/v1/operationInpatient/saveOperationXiuGaiJL

#### 请求方式
> POST

#### Content-Type
> json

#### 请求Header参数
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
Accept | */* | Text | 是 | -
Content-Type | application/json | Text | 是 | -
#### 请求Body参数
```javascript
{"leiBie":"","bingLiID":"","tongZhiDID":"","ziDuanMC":"","ziDuanMing":"","shouShuDM":"","shouShuMC":"","jiuMingCheng":"","jiuDaiMa":"","xinMingCheng":"","xinDaiMa":""}
```
参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述
--- | --- | --- | --- | ---
leiBie | - | String | 否 | 类别（0=普通，1=心导管，2=外周导管）
bingLiID | - | Integer | 否 | 病历ID
tongZhiDID | - | Integer | 否 | 通知单ID
ziDuanMC | - | String | 否 | 字段名（中文）
ziDuanMing | - | String | 否 | 字段名（代码），比如zdys=主刀医生
shouShuDM | - | String | 否 | 手术代码
shouShuMC | - | String | 否 | 手术名称
jiuMingCheng | - | String | 否 | 旧名称
jiuDaiMa | - | String | 否 | 旧代码
xinMingCheng | - | String | 否 | 新名称
xinDaiMa | - | String | 否 | 新代码
#### 认证方式
```text
noauth
```
#### 预执行脚本
```javascript
暂无预执行脚本
```
#### 后执行脚本
```javascript
暂无后执行脚本
```
#### 成功响应示例
```javascript
{"data":"","errorMessage":"","extendData":{},"hasError":""}
```
参数名 | 示例值 | 参数类型 | 参数描述
--- | --- | --- | ---
data | - | String | 
errorMessage | - | String | 
extendData | - | Object | 
hasError | - | Integer | 