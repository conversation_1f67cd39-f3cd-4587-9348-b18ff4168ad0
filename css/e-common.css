/* 插件引用 */
/* easyui_ehrsz */
@import url('../lib/jquery/easyui.css');
/* Bootstrap */
@import url('../lib/bootstrap/css/bootstrap.min.css');
/* bootstrap-theme */
@import url('../lib/bootstrap/css/bootstrap-theme.min.css');
/* 对话框 */
@import url('../lib/iziModal/css/iziModal.min.css');
/* 提示框 */
@import url('../lib/toastr/toastr.min.css');
/* iconfont图标 */
@import url('../lib/iconfont/iconfont.css');
@import url('../lib/font/iconfont.css');

/* 通用样式 */
html,
body {
  height: 100%;
  color: #000;
}

input {
  border: none;
  outline: none;
}

a {
  color: #3D6CC8;
}

a:focus,
a:hover {
  color: #3D6CC8;
}

ul,
li {
  margin: 0;
  padding: 0;
  list-style-type: none;
}

hr {
  -moz-border-bottom-colors: none;
  -moz-border-image: none;
  -moz-border-left-colors: none;
  -moz-border-right-colors: none;
  -moz-border-top-colors: none;
  border-color: #EEEEEE;
  border-style: solid none;
  border-width: 1px 0;
  margin: 18px 0;
}

.margin_0 {
  margin: 0 !important;
}

.padding_0 {
  padding: 0 !important;
}

.red {
  color: #C82222 !important;
}

.orange {
  color: #FFA654 !important;
}

.green {
  color: #45E498 !important;
}

.e_inner {
  position: relative;
  padding: 5px;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
}

.e_inner>.row {
  margin-right: -10px;
  margin-left: -10px;
  height: 100%;
}

.e_inner>.row>div[class*='col-'] {
  padding-right: 0px;
  padding-left: 5px;
}

button {
  outline: none;
}

/* 按钮样式 */
.btn_s1 {
  line-height: 32px;
  padding: 0 25px;
  border: 1px solid #3D6CC8;
  box-sizing: border-box;
  border-radius: 4px;
  color: #3D6CC8;
  background: transparent;
  cursor: pointer;
  transition: 0.2s;
}

.btn_s1:hover {
  background: #3D6CC8;
  color: #fff;
}

.e_btn {
  line-height: 2.5em;
  position: relative;
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
  cursor: pointer;
  transition: all .3s cubic-bezier(.645, .045, .355, 1);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: manipulation;
  height: 32px;
  padding: 0 15px;
  font-size: 14px;
  border-radius: 4px;
  color: rgba(0, 0, 0, .65);
  background-color: #fff;
  border: 1px solid #d9d9d9;
}

.e_btn:hover {
  color: #40a9ff;
  background-color: #fff;
  border-color: #40a9ff;
}

.e_btn:active {
  background-image: none;
  outline: 0;
  box-shadow: inset 0px 3px 10px rgb(0 0 0 / 13%);
}

.e_btn_primary {
  border: none;
  line-height: 1.499;
  position: relative;
  display: inline-block;
  font-weight: 400;
  white-space: nowrap;
  text-align: center;
  background-image: none;
  box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
  cursor: pointer;
  transition: all .3s cubic-bezier(.645, .045, .355, 1);
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  touch-action: manipulation;
  height: 32px;
  padding: 0 15px;
  font-size: 14px;
  border-radius: 4px;
  color: #fff;
  background-color: #3D6CC8;
  border: 1px solid #d9d9d9;
  border-color: #3D6CC8;
  text-shadow: 0 -1px 0 rgb(0 0 0 / 12%);
  box-shadow: 0 2px 0 rgb(0 0 0 / 5%);
}

.e_btn_primary:hover {
  color: #fff;
  background-color: #5d8ae4;
  border-color: #5d8ae4;
}

.e_btn_primary:active {
  background-image: none;
  outline: 0;
  box-shadow: inset 0px 3px 10px rgb(0 0 0 / 13%);
}

.e_btn_primary.ghost {
  color: #3D6CC8;
  background-color: transparent;
  border-color: #3D6CC8;
  text-shadow: none;
}

.e_btn_primary.ghost:hover {
  color: #fff;
  background-color: #3D6CC8;
}
.e_btn_primary[disabled]:hover,
.e_btn_primary[disabled] {
  color: rgba(0, 0, 0, .25);
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  text-shadow: none;
  box-shadow: none;
}

/* 设置滚动条的样式 */
::-webkit-scrollbar {
  width: 20px;
  height: 20px;
  background: #F3F2F3;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  border: 5px solid #F3F2F3;
  border-radius: 20px;
  background: #CFCFCF;
}

::-webkit-scrollbar-corner {
  background: #dFdFdF;
}

/* 按钮 */
::-webkit-scrollbar-button:vertical:start {
  background: url('../images/scroll_btn_top.png') center no-repeat;
}

::-webkit-scrollbar-button:vertical:end {
  background: url('../images/scroll_btn_bottom.png') center no-repeat;
  transform: rotate(180deg)
}

::-webkit-scrollbar-button:horizontal:start {
  background: url('../images/scroll_btn_left.png') center no-repeat;
}

::-webkit-scrollbar-button:horizontal:end {
  background: url('../images/scroll_btn_right.png') center no-repeat;
  transform: rotate(180deg)
}

/* 修改iziModal样式 */
.izi_message {
  padding: 30px 30px 20px;
}

.izi_message_body>.glyphicon {
  float: left;
  margin-right: 16px;
  font-size: 22px;
}

.izi_message_body>.glyphicon-info-sign {
  color: #1890ff;
}

.izi_message_body>.glyphicon-info-ok-sign {
  color: #52c41a;
}

.izi_message_body>.glyphicon-remove-sign {
  color: #f5222d;
}

.izi_message_body>.glyphicon-question-sign {
  color: #faad14;
}
.izi_message_body>.glyphicon-warning-sign {
  color: #faad14;
}

.izi_message_body>.izi_message_body_title {
  display: block;
  overflow: hidden;
  color: rgba(0, 0, 0, .85);
  font-weight: 700;
  font-size: 16px;
  line-height: 26px;
}

.izi_message_body>.izi_message_body_content {
  margin: 8px 0 20px 38px;
  color: rgba(0, 0, 0, .65);
  font-size: 14px;
}

.izi_message_btn>.btn+.btn {
  margin-left: 8px;

}

/* modal */

.izi_modal_content {
  padding: 24px;
}

.izi_modal_btn {
  padding: 6px 12px;
  border-top: 1px solid #e8e8e8;
}

.izi_modal_btn>.btn+.btn {
  margin-left: 8px;
}

/* 浮动布局 */
.flex-row {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters>.col,
.no-gutters>[class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

.flex-column {
  -ms-flex-direction: column !important;
  flex-direction: column !important;
}

.flex-row-reverse {
  -ms-flex-direction: row-reverse !important;
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  -ms-flex-direction: column-reverse !important;
  flex-direction: column-reverse !important;
}

.flex-wrap {
  -ms-flex-wrap: wrap !important;
  flex-wrap: wrap !important;
}

.flex-nowrap {
  -ms-flex-wrap: nowrap !important;
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  -ms-flex-wrap: wrap-reverse !important;
  flex-wrap: wrap-reverse !important;
}

.flex-fill {
  -ms-flex: 1 1 0% !important;
  flex: 1 1 0% !important;
}

.flex-grow-0 {
  -ms-flex-positive: 0 !important;
  flex-grow: 0 !important;
}

.flex-grow-1 {
  -ms-flex-positive: 1 !important;
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  -ms-flex-negative: 0 !important;
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  -ms-flex-negative: 1 !important;
  flex-shrink: 1 !important;
}

.justify-content-start {
  -ms-flex-pack: start !important;
  justify-content: flex-start !important;
}

.justify-content-end {
  -ms-flex-pack: end !important;
  justify-content: flex-end !important;
}

.justify-content-center {
  -ms-flex-pack: center !important;
  justify-content: center !important;
}

.justify-content-between {
  -ms-flex-pack: justify !important;
  justify-content: space-between !important;
}

.justify-content-around {
  -ms-flex-pack: distribute !important;
  justify-content: space-around !important;
}

.align-items-start {
  -ms-flex-align: start !important;
  align-items: flex-start !important;
}

.align-items-end {
  -ms-flex-align: end !important;
  align-items: flex-end !important;
}

.align-items-center {
  -ms-flex-align: center !important;
  align-items: center !important;
}

.align-items-baseline {
  -ms-flex-align: baseline !important;
  align-items: baseline !important;
}

.align-items-stretch {
  -ms-flex-align: stretch !important;
  align-items: stretch !important;
}

.align-content-start {
  -ms-flex-line-pack: start !important;
  align-content: flex-start !important;
}

.align-content-end {
  -ms-flex-line-pack: end !important;
  align-content: flex-end !important;
}

.align-content-center {
  -ms-flex-line-pack: center !important;
  align-content: center !important;
}

.align-content-between {
  -ms-flex-line-pack: justify !important;
  align-content: space-between !important;
}

.align-content-around {
  -ms-flex-line-pack: distribute !important;
  align-content: space-around !important;
}

.align-content-stretch {
  -ms-flex-line-pack: stretch !important;
  align-content: stretch !important;
}

.align-self-auto {
  -ms-flex-item-align: auto !important;
  align-self: auto !important;
}

.align-self-start {
  -ms-flex-item-align: start !important;
  align-self: flex-start !important;
}

.align-self-end {
  -ms-flex-item-align: end !important;
  align-self: flex-end !important;
}

.align-self-center {
  -ms-flex-item-align: center !important;
  align-self: center !important;
}

.align-self-baseline {
  -ms-flex-item-align: baseline !important;
  align-self: baseline !important;
}

.align-self-stretch {
  -ms-flex-item-align: stretch !important;
  align-self: stretch !important;
}
/*按钮*/
.e-btn-del{
  border: 1px solid #c82222;
  color: #c82222;
  padding: 2px;
  border-radius: 4px;
  cursor: pointer;
}
.e-btn-del:hover{
  border: 1px solid #fff;
  background: #c82222;
  color: #fff;
  padding: 2px;
  border-radius: 4px;
}
.e-btn-blue{
  border: 1px solid #7F00FE;
  color: #7F00FE;
  padding: 2px;
  border-radius: 4px;
  cursor: pointer;
}
.e-btn-blue:hover{
  border: 1px solid #fff;
  background: #7F00FE;
  color: #fff;
  padding: 2px;
  border-radius: 4px;
}
/* tag标签 */
.e_tag_green{
  color: green;
  background: rgb(0 128 0 / 30%);
  padding: 2px;
}
.e_tag_red{
  color: red;
  background: rgb(255, 0, 0,0.3);
  padding: 2px;
}
.e-tag {
  box-sizing: border-box;
  color: rgba(0, 0, 0, .65);
  font-size: 14px;
  font-weight: 700;
  font-variant: tabular-nums;
  line-height: 1.5;
  list-style: none;
  font-feature-settings: "tnum";
  display: inline-block;
  height: auto;
  /* margin: 0 8px 0 0; */
  font-size: 13px;
  padding: 0 3px;
  line-height: 20px;
  white-space: nowrap;
  background: #fafafa;
  /* border: 1px solid #d9d9d9; */
  border-radius: 4px;
  opacity: 1;
  transition: all .3s cubic-bezier(.78, .14, .15, .86);
}

.e-tag-blue {
  color: #1890ff;
  background: rgb(24 144 255 / 20%);
  border-color: rgb(24 144 255 / 20%);
}

.e-tag-red {
  color: #f5222d;
  background: rgb(245 34 45 / 20%);
  border-color: rgb(245 34 45 / 20%);
}

.e-tag-orange {
  color: #fa8c16;
  background: rgb(250 140 22 / 20%);
  border-color: rgb(250 140 22 / 20%);
}

.e-tag-black {
  color: #ececec;
  background: #555555;
  border-color: #555555;
}
.e-tagbtn-prime{
  background: #3D6CC8;
  color: #fff;
  padding: 0 3px;
  border-radius: 4px;
}
.e-tagbtn-prime:hover{
  text-decoration: none;
  color: #fff;
  background: rgb(61, 108, 200,0.5);
}

/* 自适应隐藏 */
@media (max-width: 768px) {
  .hidden-sm {
    display: none !important;
  }
}

@media (max-width: 992px) {
  .hidden-md {
    display: none !important;
  }
}

@media (max-width: 1200px) {
  .hidden-lg {
    display: none !important;
  }
}

@media (max-width: 1600px) {
  .hidden-xl {
    display: none !important;
  }
}

@media (min-width: 1600px) {

  .col-xl-1,
  .col-xl-2,
  .col-xl-3,
  .col-xl-4,
  .col-xl-5,
  .col-xl-6,
  .col-xl-7,
  .col-xl-8,
  .col-xl-9,
  .col-xl-10,
  .col-xl-11,
  .col-xl-12 {
    float: left;
  }

  .col-xl-12 {
    width: 100%;
  }

  .col-xl-11 {
    width: 91.66666667%;
  }

  .col-xl-10 {
    width: 83.33333333%;
  }

  .col-xl-9 {
    width: 75%;
  }

  .col-xl-8 {
    width: 66.66666667%;
  }

  .col-xl-7 {
    width: 58.33333333%;
  }

  .col-xl-6 {
    width: 50%;
  }

  .col-xl-5 {
    width: 41.66666667%;
  }

  .col-xl-4 {
    width: 33.33333333%;
  }

  .col-xl-3 {
    width: 25%;
  }

  .col-xl-2 {
    width: 16.66666667%;
  }

  .col-xl-1 {
    width: 8.33333333%;
  }

  .col-xl-pull-12 {
    right: 100%;
  }

  .col-xl-pull-11 {
    right: 91.66666667%;
  }

  .col-xl-pull-10 {
    right: 83.33333333%;
  }

  .col-xl-pull-9 {
    right: 75%;
  }

  .col-xl-pull-8 {
    right: 66.66666667%;
  }

  .col-xl-pull-7 {
    right: 58.33333333%;
  }

  .col-xl-pull-6 {
    right: 50%;
  }

  .col-xl-pull-5 {
    right: 41.66666667%;
  }

  .col-xl-pull-4 {
    right: 33.33333333%;
  }

  .col-xl-pull-3 {
    right: 25%;
  }

  .col-xl-pull-2 {
    right: 16.66666667%;
  }

  .col-xl-pull-1 {
    right: 8.33333333%;
  }

  .col-xl-pull-0 {
    right: auto;
  }

  .col-xl-push-12 {
    left: 100%;
  }

  .col-xl-push-11 {
    left: 91.66666667%;
  }

  .col-xl-push-10 {
    left: 83.33333333%;
  }

  .col-xl-push-9 {
    left: 75%;
  }

  .col-xl-push-8 {
    left: 66.66666667%;
  }

  .col-xl-push-7 {
    left: 58.33333333%;
  }

  .col-xl-push-6 {
    left: 50%;
  }

  .col-xl-push-5 {
    left: 41.66666667%;
  }

  .col-xl-push-4 {
    left: 33.33333333%;
  }

  .col-xl-push-3 {
    left: 25%;
  }

  .col-xl-push-2 {
    left: 16.66666667%;
  }

  .col-xl-push-1 {
    left: 8.33333333%;
  }

  .col-xl-push-0 {
    left: auto;
  }

  .col-xl-offset-12 {
    margin-left: 100%;
  }

  .col-xl-offset-11 {
    margin-left: 91.66666667%;
  }

  .col-xl-offset-10 {
    margin-left: 83.33333333%;
  }

  .col-xl-offset-9 {
    margin-left: 75%;
  }

  .col-xl-offset-8 {
    margin-left: 66.66666667%;
  }

  .col-xl-offset-7 {
    margin-left: 58.33333333%;
  }

  .col-xl-offset-6 {
    margin-left: 50%;
  }

  .col-xl-offset-5 {
    margin-left: 41.66666667%;
  }

  .col-xl-offset-4 {
    margin-left: 33.33333333%;
  }

  .col-xl-offset-3 {
    margin-left: 25%;
  }

  .col-xl-offset-2 {
    margin-left: 16.66666667%;
  }

  .col-xl-offset-1 {
    margin-left: 8.33333333%;
  }

  .col-xl-offset-0 {
    margin-left: 0;
  }
}

/* bootstrap样式 */
.btn.active.focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn:active:focus,
.btn:focus {
  outline: none;
}

.nav>li>a:focus,
.nav>li>a:hover {
  background-color: transparent;
}

.dropdown-submenu {
  position: relative;
}

.dropdown-submenu>.dropdown-menu {
  top: 0;
  left: 100%;
  margin-top: -6px;
  margin-left: -1px;
  -webkit-border-radius: 0 6px 6px 6px;
  -moz-border-radius: 0 6px 6px;
  border-radius: 0 6px 6px 6px;
}

.dropdown-submenu:hover>.dropdown-menu {
  display: block;
}

.dropdown-submenu>a:after {
  display: block;
  content: " ";
  float: right;
  width: 0;
  height: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 5px 0 5px 5px;
  border-left-color: #ccc;
  margin-top: 5px;
  margin-right: -10px;
}

.dropdown-submenu:hover>a:after {
  border-left-color: #fff;
}

.dropdown-submenu.pull-left {
  float: none;
}

.dropdown-submenu.pull-left>.dropdown-menu {
  left: -100%;
  margin-left: 10px;
  -webkit-border-radius: 6px 0 6px 6px;
  -moz-border-radius: 6px 0 6px 6px;
  border-radius: 6px 0 6px 6px;
}

/* 空状态 */
.e_empty {
  height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-flow: column;
}

.e_empty_image {
  width: 125px;
  margin-bottom: 8px;
}

.e_empty_description {
  color: rgba(0, 0, 0, .45);
  margin-bottom: 0;
}


.shade {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0px;
  left: 0px;

}

.wrap-ms-right {
  list-style: none;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 9999;
  padding: 5px 0;
  min-width: 80px;
  margin: 0;
  display: none;
  font-family: "微软雅黑";
  font-size: 14px;
  background-color: #fff;
  border: 1px solid rgba(0, 0, 0, .15);
  box-sizing: border-box;
  border-radius: 4px;
  -webkit-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  -ms-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  -o-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.ms-item {
  height: 30px;
  line-height: 30px;
  text-align: center;
  cursor: pointer;
}

.ms-item:hover {
  background-color: #343a40;
  color: #FFFFFF;
}

.m {
  width: 800px;
  margin-left: auto;
  margin-right: auto;
}

/* 右键菜单 */
.bootstrapMenu {
  max-width: 240px;
}

.bootstrapMenu>.dropdown-menu>li>a {
  white-space: normal;
}