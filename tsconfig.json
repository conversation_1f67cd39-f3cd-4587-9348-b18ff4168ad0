{"include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.js", "tests/**/*.ts", "tests/**/*.tsx"], "compilerOptions": {"target": "es2019", "lib": ["es2019", "dom"], "module": "commonjs", "moduleResolution": "node", "sourceMap": true, "composite": true, "declaration": true, "strict": true, "resolveJsonModule": true, "skipLibCheck": true, "noUnusedLocals": false, "esModuleInterop": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}, "allowJs": true, "noEmit": true, "types": ["element-ui", "wyyy-component", "vue"]}, "exclude": ["node_modules", "dist"], "vueCompilerOptions": {"target": 2.7, "extensions": [".vue"]}}