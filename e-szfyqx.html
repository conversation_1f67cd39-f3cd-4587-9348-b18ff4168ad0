<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>首页</title>
  <meta name="description" content="首页">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  #sx_form{
    padding: 10px;
    width: 69%;
  }
  .input_text{
    border: 1px solid rgb(0 0 0 / 50%);
    border-radius: 5px;
    line-height: 20px;
    width: 70px;
    padding-left: 5px;
    height: 25px;
  }
  .szfyqx_main {
    overflow: hidden;
    padding: 14px;
  }

  .szfyqx_lssqd>.head {
    /* background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%); */
    background: #3D6CC8;
    border: 1px solid #3D6CC8;
    text-align: center;
    color: #fff;
  }

  .szfyqx_lssqd>.list {
    padding: 12px;
  }

  .szfyqx_lssqd>.list>table tr {
    line-height: 1;
  }

  .szfyqx_lssqd>.list>table tr>td {
    padding: 10px 0;
  }
</style>
<body>
  <div class="szfyqx">
    <div id="sx_form">
      
    </div>
    <div id="charts" style="width: 70%;float: left;">
      <div id="main" style="width: 100%;height:600px;"></div>
    </div>
    <div id="right_list" style="width: 30%;float: right;"></div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <script src="./js/e-interface.Szfyqx.js"></script>
  <script src="./lib/charts/echarts.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-Szfyqx.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-Szfyqx.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>

</html>