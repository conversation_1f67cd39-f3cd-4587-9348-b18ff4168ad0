<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>体温单</title>
  <meta name="description" content="体温单">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <style>
    #twd_list {
      max-height: 175px;
      overflow-x: hidden;
      overflow-y: auto;
      width: 100%;
    }

    .twd_list {
      display: flex;
      flex-flow: wrap;
      margin: 0 -8px;
    }

    .twd_list .item {
      border: 1px solid #ddd;
      border-left-width: 5px;
      border-left-color: #3D6CC8;
      border-radius: 3px;
      padding: 8px 10px;
      cursor: pointer;
      margin-bottom: 10px;
      margin: 8px;
      text-decoration: none;
      display: flex;
      align-items: center;
      width: 300px;
    }

    .twd_list .item:hover {
      box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    }

    .twd_list .item_title {
      font-size: 16px;
      margin-bottom: 2px;
    }

    .twd_list .item_inner {
      color: #aaa;
    }

    .container-twd {
      overflow: hidden;
      height: 100%;
      display: flex;
      flex-flow: column;
    }

    .container-twd .fixed_btn {
      position: fixed;
      bottom: 15px;
      right: 20px;
      display: flex;
      z-index: 10;
    }

    .container-twd .fixed_btn .item {
      width: 40px;
      line-height: 40px;
      font-size: 16px;
      text-align: center;
      border-radius: 100%;
      background: #eee;
      border: 1px solid rgba(0, 0, 0, 0.1);
      cursor: pointer;
      margin-right: 10px;
    }

    .twd_iframe {
      flex: 1;
      margin-top: 20px;
      overflow: auto;
      white-space: nowrap;
    }
  </style>
</head>

<body>
  <div class="container-twd">
    <p class="bg-info" style="flex:0 0 20px;padding:0 4px;margin: 0;">
      自2019-01-09零点开始，生命体征24h出入量将自动显示到体温单前一天相应的空格内，2019-01-09零点之前的数据照旧规则显示。</p>
    <div id="twd_list"></div>
    <div class="twd_iframe">
      <iframe id="fr_twd" frameborder="0" width="700px" height="1020px"></iframe>
    </div>
    <div class="fixed_btn">
      <div class="item" data-toggle="tooltip" data-placement="top" title="放大" onclick="ZoomIt('+')">
        <i class="glyphicon glyphicon-zoom-in"></i>
      </div>
      <div class="item" data-toggle="tooltip" data-placement="top" title="缩小" onclick="ZoomIt('-')">
        <i class="glyphicon glyphicon-zoom-out"></i>
      </div>
      <div class="item" data-toggle="tooltip" data-placement="top" title="打印"
        onclick="$('#fr_twd')[0].contentWindow.preview()">
        <i class="glyphicon glyphicon-print"></i>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 登录页js文件 -->
  <script>
    var params = {} //url带参
    //统一页面启动
    $(document).ready(() => {
      //初始化
      init()
    })
    /********************初始化********************/
    function init() {
      //分析url
      for (let i of window.location.href.split("?")[1].split("&")) {
        let fd = i.split("=")
        params[fd[0]] = fd[1]
      }
      //提示
      $('[data-toggle="tooltip"]').tooltip()
      //获取体温单列表
      WRT_e.api.brtwdlb.getTwdList({
        params: {
          as_blid: params['as_blid']
        },
        success(data) {
          if (data.Code == 1) {
            let arr = JSON.parse(data.Result)
            $("#twd_list").html(
              new twdList_View().init({
                data: arr
              }).render().$el
            )
            if (arr[arr.length - 1].as_kssj <= new Date().Format("yyyy-MM-dd") && arr[arr.length - 1].as_jssj >=
              new Date().Format("yyyy-MM-dd")) {
              let obj = arr[arr.length - 1]
              $('.twd_iframe').html(
                `<iframe id="fr_twd" src="e-showtwd.html?as_blid=${params['as_blid']}&as_kssj=${obj['as_kssj']}&as_jssj=${obj['as_jssj']}&as_week=${obj['as_week']}" frameborder="0" width="700px" height="1020px" style="zoom: 1.5;"></iframe>`
              )

            }
          }
        }
      })
    }
    //体温单列表
    var twdList_View = WRT_e.view.extend({
      render: function () {
        this.$el.html(`
          <div class="twd_list">
            ${_.map(this.data, obj => `
            <a class="item" data-as_kssj=${obj.as_kssj} data-as_jssj=${obj.as_jssj} data-as_week=${obj.as_week}">
              <div class="item_title">第${obj.as_week}星期</div>
              <div class="item_inner">&nbsp;<span class="e-tag">${obj.as_kssj}</span>至&nbsp;&nbsp;<span class="e-tag">${obj.as_jssj}</span></div>
            </a>  
            `).join('')}
          </div>
        `)
        return this
      },
      events: {
        "click .twd_list>.item": "showTwd",
      },
      showTwd(event) {
        let obj = $(event.currentTarget).data()
        // let obj2 = $(event.currentTarget).next.data()
        // ${(document.body.clientWidth >= 1440 && $(event.currentTarget).next().length > 0)
        //如果宽度大于1440显示当前点击体温单和下一条体温单
        $('.twd_iframe').html(
          `<iframe id="fr_twd" src="e-showtwd.html?as_blid=${params['as_blid']}&as_kssj=${obj['as_kssj']}&as_jssj=${obj['as_jssj']}&as_week=${obj['as_week']}" frameborder="0" width="700px" height="1020px" style="zoom: 1.5;"></iframe>`
        )
      }
    })
    // 放大缩小
    function ZoomIt(flag) {
      var zoom = Number($('#fr_twd').css('zoom'))
      if (!(zoom >= 2 && flag == '+') && !(zoom <= 0.5 && flag == '-')) {
        $('#fr_twd').css('zoom', eval('zoom' + flag + '=0.5;'));
        $('#fr_twd')[0].contentWindow.ZoomIt(flag)
      }
    }
  </script>
</body>

</html>