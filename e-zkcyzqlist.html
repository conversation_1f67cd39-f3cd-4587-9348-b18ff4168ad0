<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>专科常用知情记录列表维护</title>
  <meta name="description" content="专科常用知情记录列表维护">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <style>
    /* 专科常用知情记录列表维护 */
    /* 最外层框 */
    .container_zkcyzqlist {
      height: 100%;
    }

    .zkcyzqlist_inner {
      position: relative;
      margin-top: 27px;
    }

    /* 左边 */
    /* 左侧外框 */
    .zkcyzqlist_inner_left {
      position: relative;
      /* width: 19.3%; */
      width: 302px;
      margin-right: 10px;
      padding: 0 10px;
    }

    .title {
      text-align: center;
      font-size: 14px;
      font-weight: 700;
      padding-bottom: 15px;
    }

    .mc_search {
      width: 100% !important;
      margin: auto;
      box-shadow: none;
      height: 34px;
      padding: 6px 12px;
      font-size: 14px;
      line-height: 1.42857143;
      color: #555;
      background-color: #fff;
      background-image: none;
      border: 1px solid #ccc;
      border-radius: 4px;
      /* position: absolute; */
    }

    .input-tag {
      float: right;
      width: 20px;
    }

    #left_Search_tag {
      position: relative;
      background: none;
      color: #4975cb;
      border: none;
      z-index: 999;
    }
    #zkcyzqlist_lists{
      height: 540px;
      overflow-y: auto;
    }
    #zkcyzqlist_lists li{
      font-size: 14px;
    }
    #zkcyzqlist_lists li label{
      padding-left: 3px;
      display: initial;
      font-weight: normal;
    }
    .table_title{
      background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%);
      color: #fff;
      border: 1px solid #3D6CC8;
      line-height: 28px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      max-width: 600px;
      margin: 5px 0 0 0;
    }
    table tr th{
      background: #85A2DB;
      font-weight: 700;
      color: #fff;
      line-height: 25px;
      text-align: center;
    }
    
    table tr td{
      text-align: center;
      line-height: 25px;
    }
    table tr:nth-child(2n) {
      background: #EFF3FB;
    }
    .model_text{
      width: 90px;
      margin: 5px 3px;
      box-shadow: none;
      height: 34px;
      padding: 6px 12px;
      font-size: 14px;
      line-height: 1.42857143;
      color: #555;
      background-color: #fff;
      background-image: none;
      border: 1px solid #ccc;
      border-radius: 4px;
    }
    .none{
      display: none;
    }
  </style>
</head>

<body>
  <div class="container_zkcyzqlist flex-row flex-column">
    <div class="zkcyzqlist_inner flex-row flex-fill">
      <div class="zkcyzqlist_inner_left">
        <div class="title">知情记录列表维护</div>
        <div>
          <input type="secrch" class="mc_search" onkeydown="calAge(event)"  />
          
        </div>
        <div style="padding-top: 15px;">
          <ul id="zkcyzqlist_lists">
          </ul>
        </div>
      </div>

      <div class="zkcyzqlist_inner_right flex-fill">
        <div>
          <button class="e_btn" onclick="add()">新增</button>
          <button id="update_btn" class="e_btn" onclick="update()">修改</button>
          <button id="save_btn" class="e_btn none" onclick="onSave()">保存</button>
          <button id="cancel_btn" class="e_btn none" onclick="onCancel()">取消</button>
          <button class="e_btn" onclick="del()">删除</button>
        </div>
        <div class="table_title"><span>专科现有的常用文书</span></div>
        <table>
          <thead>
            <tr>
              <th width="50"></th>
              <th width="100">文书类型</th>
              <th width="350">文书名字</th>
              <th width="100">文书排序</th>
            </tr>
          </thead>
        </table>
        <div style="height: 540px;overflow: auto;">
          <table style="width: 600px;">
            <tbody id="zkxyws_lists">
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js?v=" + Date.now() + ""></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面js文件 -->
  <!-- <script src="js/e-zqjllistall.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-interface.zkcyzqlist.js?v=" + Date.now() + "'><\/script>");
    document.write("<script type='text/javascript' src='js/e-zkcyzqlist.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>

</html>