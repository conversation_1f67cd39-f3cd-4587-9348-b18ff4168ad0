<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>按病人基本信息查询病历(高级)</title>
  <meta name="description" content="按病人基本信息查询病历(高级)">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>

<body id="blgjkong">
  <div id="container_blcxgj" class="container_blcxgj">
    <!-- <div class="title_patientbl">
      <span>按病人基本信息查询病历（高级）</span>
    </div>
    <button id="open_btn" type="submit"><span>设置查询条件</span></button>
    <div id="light" class="inner_table"></div>
    <div id="fade" class="black_overlay"></div>
    <div id="resultShow"></div> -->
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 按病人基本信息查询病历页js文件 -->
  <!-- <script src="js/e-blcxgj.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-blcxgj.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>
<style> 
/* 按病人信息查询 */
.container-blcxgj {
  height: 900px;
  background: #FFFFFF;
}
.title_patientbl {
  position: relative;
  padding-top: 69px;
  margin-bottom: 20px;
  text-align: center;
}
.title_patientbl span{
  font-family: Microsoft YaHei;
  font-size: 32px;
  font-style: normal;
  font-weight: 400;
  line-height: 42px;
  letter-spacing: 0em;
  /* text-align: left; */
}
#open_btn {
  display:block;
  margin: auto;
  background: #3D6CC8;
  border: 1px solid #3D6CC8;
  height: 42px;
  width: 256px;
  border-radius: 4px;
  outline: none;
}
#open_btn> span{
  font-family: Microsoft YaHei;
  font-size: 24px;
  font-style: normal;
  font-weight: 400;
  line-height: 32px;
  letter-spacing: 0em;
  text-align: left;
  color: #FFFFFF;
  text-align: center;
}
/* 弹出查询窗口 */
.black_overlay{
  display: none;
  position: absolute;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  background-color: #FFFFFF;
  z-index:1001;
  -moz-opacity: 0.8;
  opacity:.80;
  filter: alpha(opacity=88);
}
.inner_table {
  /* display: none; */
  position: absolute;
  margin: auto;
  width: 100%;
  top:35%;
  z-index:1002;
  overflow: auto;
}
#br_search_table {
  position: relative;
  display: table;
  margin: auto;
  width: 52%;
  border: 1px solid #3D6CC8;
  background-color: white;
  border-radius: 4px;
}
/* 表标题 */
#br_search_table> .table-header {
  position: relative;
  background: linear-gradient(180deg, rgba(61, 108, 200, 0.24) 0%, rgba(61, 108, 200, 0) 100%);
  width: 100%;
  height: 48px;
}
#br_search_table> .table-header>span {
  font-family: Microsoft YaHei;
  font-size: 20px;
  font-style: normal;
  font-weight: 400;
  margin-left: 14px;
  line-height: 46px;
  text-align: left;
}
/* 表格 */
.overall_table_frame {
  position: relative;
  width: 100%;
  border: 1px solid #B7C3DA;
  margin-bottom: 10px;
}
.overall_table_frame> table {
  position: relative;
  width: 100%;
}
#table_internal {
  position: relative;
  border: 1px solid #B7C3DA;
}
/* 行标题 */
.row_head {
  background: #85A2DB;
  color: #FFFFFF;
  height: 40px;
}
.row_head >th{
  background: #85A2DB;
  color: #FFFFFF;
  text-align: center;
  font-family: Microsoft YaHei;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0em;
}
.row_head >.left_bracketa{
  position: relative;
  width: 8.23%;
}
.row_head >.fieldName{
  position: relative;
  width: 17.9%;
}
.row_head >.comparison_operators{
  position: relative;
  width: 15.02%;
}
.row_head >.qery_val{
  position: relative;
  width: 37.55%;
}
.row_head >.right_bracket{
  position: relative;
  width: 8.23%;
}
.row_head >.logical_operator{
  position: relative;
  width: 12.34%;
}
/* 行内容 */
#table_internal>.line_add {
  position: relative;
  width: 100%;
}
.line_add>tr>td {
  position: relative;
  width: 100%;
  height: 39px;
  border: 2px solid #B7C3DA;
}
#table_internal .line_add >td>select {
  position: relative;
  width: 100%;
  height: 40px;
  border-radius: 4px;
  border: 2px solid #B7C3DA;
}
#table_internal .line_add>td>input {
  position: relative;
  width: 100%;
  height: 40px;
  border-radius: 4px;
  border: 2px solid #B7C3DA;
}
#table_internal>.line_add>.left_bracket {
  position: relative;
  width: 8.23%;
}
#table_internal>.line_add>.fieldName {
  position: relative;
  width: 17.9%;
}
#table_internal>.line_add>.comparison_operators {
  position: relative;
  width: 15.02%;
}
#table_internal>.line_add>.qery_val {
  position: relative;
  width: 37.55%;
}
#table_internal>.line_add>.right_bracket {
  position: relative;
  width: 8.23%;
}
#table_internal>.line_add>.logical_operator {
  position: relative;
  width: 12.34%;
}
#blgjkong>.combo-p>.panel-body {
  width: 180px;
  height: auto;
  overflow-x: hidden;
  padding: 0;
}
#blgjkong>.combo {
  position: relative;
  width: 100%;
}
/* 表按钮 */
.table-footer {
  position: relative;
  margin-bottom: 8px;
}
.table-footer> button {
  background: #FFFFFF;
  border: 1px solid #3D6CC8;
  height: 40px;
  width: 80px;
  border-radius: 4px;
}
.table-footer> .add {
  position: relative;
  margin-left: 14px;
}
.table-footer> .delete {
  position: relative;
  margin-left: 0.82%;
}
.table-footer> .ok {
  position: relative;
  float: right;
  right: 0.82%;
  margin-right: 1.6%;
}
.table-footer> .countermand {
  position: relative;
  float: right;
  right: 1.75%;
  margin-left: 0.82%;
}
/* 结果表 */
.Object_list {
  position: relative;
  width: 100%;

}
.table_searchContent {
  position: relative;
  width: 95%;
  margin: auto;
  margin-top:70px;
  /* margin-bottom: 577px; */
  border-collapse:collapse;
  vertical-align: middle;
  border: 1px solid #FFFFFF;
}

.search_table>th {
  position: relative;
  background: #85A2DB;
  color: #FFFFFF;
  text-align: center;
  height: 40px;
}
.search_content>td {
  text-align: center;
  background: #D3DEF3;
  color: #707070;
  height: 40px;
}
.znum {
  position: relative;
  font-size: 18px;
  text-align: center;
  font-family: Microsoft YaHei;
  color: #555555;
  margin-top:10px;
  font-weight:600;
}
</style>
</html>