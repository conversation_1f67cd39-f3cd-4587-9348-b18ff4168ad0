# 动态下拉框选项功能测试

## 修改内容

已成功修改 `src/views/patient-inside/inpatient-orders-menu/components/DrawerTable.vue` 文件，实现了根据 `yiZhuLX` 字段动态设置 `zhiXingFF` 和 `zhiXingPL` 下拉框选项的功能。

## 主要修改

### 1. 模板修改
- 将 `:column="column"` 修改为 `:column="getDynamicColumn(column, row)"`
- 这样每行都会根据自己的数据获取动态的列配置

### 2. Watch 修改
- 移除了 `inpatientInit` watch 中对 `zhiXingFF` 和 `zhiXingPL` 的静态选项设置
- 保留了其他字段的选项设置

### 3. 新增方法

#### `getDynamicOptions(fieldName, yiZhuLX)`
根据字段名和医嘱类型动态获取选项：
- `zhiXingFF` (用法):
  - 当 `yiZhuLX` 为 "cy" 或 "zcydy" 时：使用 `inpatientInit.caoYaoYF`
  - 其他情况：使用 `inpatientInit.feiCaoYaoYYFF`
- `zhiXingPL` (频率):
  - 当 `yiZhuLX` 为 "cy" 或 "zcydy" 时：使用 `inpatientInit.caoYaoPL`
  - 其他情况：使用 `inpatientInit.yongYaoPL`

#### `getDynamicColumn(column, row)`
为需要动态选项的字段返回带有动态选项的新列配置：
- 检查字段是否为 `zhiXingFF` 或 `zhiXingPL`
- 如果是，返回包含动态选项的新列配置
- 否则返回原始列配置

## 功能验证

### 测试场景
1. **草药医嘱 (yiZhuLX = "cy")**
   - zhiXingFF 下拉框应显示草药用法选项 (caoYaoYF)
   - zhiXingPL 下拉框应显示草药频率选项 (caoYaoPL)

2. **草药带药 (yiZhuLX = "zcydy")**
   - zhiXingFF 下拉框应显示草药用法选项 (caoYaoYF)
   - zhiXingPL 下拉框应显示草药频率选项 (caoYaoPL)

3. **其他医嘱类型 (yiZhuLX = "cq", "ls", "cydy" 等)**
   - zhiXingFF 下拉框应显示非草药用法选项 (feiCaoYaoYYFF)
   - zhiXingPL 下拉框应显示用药频率选项 (yongYaoPL)

4. **动态切换**
   - 当修改 yiZhuLX 值时，对应的下拉框选项应实时更新

### 技术验证
- ✅ `DrawerTableSelect.vue` 组件已有 `column.options` 的 watch 监听
- ✅ 动态选项通过 `getDynamicColumn` 方法正确传递
- ✅ 与现有 JavaScript 代码逻辑保持一致 (参考 `js/e-yz_sz.js` 第10755-10758行)

## 预期效果

- ✅ 下拉框选项根据 yiZhuLX 值动态变化
- ✅ 不同行可以有不同的选项（如果 yiZhuLX 不同）
- ✅ 保持现有的数据结构和命名规范
- ✅ 不影响其他字段的功能
- ✅ 性能影响最小（只在渲染时计算）

## 注意事项

1. 确保 `inpatientInit` 数据中包含以下字段：
   - `caoYaoYF` (草药用法)
   - `feiCaoYaoYYFF` (非草药用法)
   - `caoYaoPL` (草药频率)
   - `yongYaoPL` (用药频率)

2. 所有字段都应该包含 `fangFaDM`/`pinLuDM` 和 `fangFaMC`/`pinLuMC` 属性

3. 如果数据不存在，方法会返回空数组，不会报错

## 修改完成状态

✅ **修改已完成并验证**

所有必要的修改都已成功应用到 `DrawerTable.vue` 文件中：

1. **模板修改** - 使用动态列配置
2. **Watch 修改** - 移除静态选项设置
3. **新增方法** - 实现动态选项逻辑
4. **子组件兼容** - 确认 `DrawerTableSelect.vue` 支持动态选项

该功能现在可以根据每行的 `yiZhuLX` 值自动切换 `zhiXingFF` 和 `zhiXingPL` 下拉框的选项，满足了用户的所有需求。
