button {
  position: relative;
  height: 30px;
  width: 100px;
  background: transparent;
  border:2px solid #43CD80;
  border-radius: 0px;
  color: #2E8B57;
  font-weight: bold;
  font-size: 10px;
  transition: all 0.3s;
}
button:hover{
  background: #43CD80;
  color: #fff;
  font-size: 14px;
} 
/* 病人数据部分 */
.main2 {
  display: flex;
  flex-flow: wrap;
  margin: 10px -15px;
  padding: 0 20px;
}

.main2 .item {
  padding: 10px;
  flex: 0px 0px 20%;
}

@media (max-width: 1700px) {
  .main2 .item {
    flex: 0 0 25%;
  }
}

@media (max-width: 1500px) {
  .main2 .item {
    flex: 0 0 33.33%;
  }
}

@media (max-width: 1200px) {
  .main2 .item {
    flex: 0 0 50%;
  }
}
.item_box {
  padding: 10px;
  height: 156px;
  min-width: 233px;
  background: #8cadbc21;
  border: 1px solid #C7D3DF;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
}
.main2 .item_box .user_text {
  font-size: 12px;
  margin: 10px 10px;
}
.main2 .item_box .status {
  height: 2px;
  background: #517accf6;
  margin-bottom: 4px;
  /* box-shadow: 0 8px 15px #555; */
}

.main2 .item_box .message {
  font-size: 14px;
}
/* 专科日志数据 */
#main3 {
  width: 360px;
}

#main3 .panel-case {
  margin-bottom: 0;
  border-left: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
  overflow: hidden;
  /* position: absolute; */
  width: 360px;
  /* top: 0;
  bottom: 0; */
}

.panel-case .panel {
  border: none;
  margin-bottom: 0px;
}

#main3 .panel-case .panel+.panel {
  margin-top: 2px;
}

#main3 .panel-heading {
  padding: 0;
}

#main3 .panel-title {
  background: #3D6CC8;
  color: #fff;
  line-height: 48px;
  font-weight: 700;
  font-size: 16px;
  padding: 0 27px;
  text-decoration: none;
}

#main3 .panel-title-text {
  margin-left: 10px;
}
.panel-body {
  text-align: center;
}
.panel-body .box_label{
  margin: 5px 0;
}
.panel-body .box_value {
  width: 86px;
  line-height: 40px;
  background: #F4F4F4;
  border-radius: 4px;
  margin: auto;
  font-size: 18px;
  color: #3D6CC8;
}

/* 病例部分 */
#main4 {
  width: 360px;
}

#main4 .panel-case {
  margin-bottom: 0;
  border-left: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
  overflow: hidden;
  /* position: absolute; */
  width: 360px;
  /* top: 0;
  bottom: 0; */
}

.panel-case .panel {
  border: none;
  margin-bottom: 0px;
}

#main4 .panel-case .panel+.panel {
  margin-top: 2px;
}

#main4 .panel-heading {
  padding: 0;
}
/* .icon-right {
  right: -100px;
} */
#main4 .panel-title1 {
  background: #3D6CC8;
  color: #fff;
  line-height: 48px;
  font-weight: 700;
  font-size: 16px;
  padding: 0 27px;
  text-decoration: none;
}
#main4 .panel-title-text {
  margin-left: 10px;
}
.panel-body {
  text-align: center;
}
.line {
  padding-top: 5px;
  height: 30px;
}
.title_lm_bule {
  color: #3D6CC8;
  font-weight: bolder;
  float: left;
  width: 83px;
}
.title_lm_gray{
  color: #7c7c7c;
  font-weight: bold;
  float: left;
  /* width: 130px; */
}
.title_lm_gray_1 {
  color: #7c7c7c;
  font-weight: bold;
  float: right;
}
.title_lm_gray_2 {
  color: #7c7c7c;
  font-weight: bold;
  float: right;
}
/* 视图显示添加、删除部分 */
.outline_border {
  position: relative;
  top: 10px;
}
.addOne {
  position: relative;
  top: 10px;
}
.delall {
  position: relative;
  /* left: 10px; */
  top: 10px;
}


/* 病人 */
#main5 {
  width: 360px;
}

#main5 .panel-case {
  margin-bottom: 0;
  border-left: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
  overflow: hidden;
  /* position: absolute; */
  width: 360px;
  /* top: 0;
  bottom: 0; */
}

.panel-case .panel {
  border: none;
  margin-bottom: 0px;
}

#main5 .panel-case .panel+.panel {
  margin-top: 2px;
}

#main5 .panel-heading {
  padding: 0;
}

#main5 .panel-title {
  background: #3D6CC8;
  color: #fff;
  line-height: 48px;
  font-weight: 700;
  font-size: 16px;
  padding: 0 27px;
  text-decoration: none;
}

#main5 .panel-title-text {
  margin-left: 10px;
}
.panel-body {
  text-align: center;
}
.panel-body .box_label{
  margin: 5px 0;
}
.panel-body .box_value {
  width: 86px;
  line-height: 40px;
  background: #F4F4F4;
  border-radius: 4px;
  margin: auto;
  font-size: 18px;
  color: #3D6CC8;
}
.line1 {
  width: 320px;
  margin: auto;
}