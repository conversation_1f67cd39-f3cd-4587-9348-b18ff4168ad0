//统一页面启动
$(document).ready(
  function () {
    var data = [{name : "小王", age:"55岁"},
      {name : "小李", age:"32岁"},
      {name : "小明", age:"41岁"}]
      for(var i = 1; i < 3; i ++) {
        data.push({name:"小王" + i, age: 50+i});
      }
    var data1 = 1
    var test = (new Object_View()).init({data:data1});
    console.log(data1)
    $("#main1").append(test.render().$el);
    var test1 = (new ArrayTest_View()).init({data:data});
    $("#test1").append(test1.render().$el);
  }
)

var Object_View = WRT_e.view.extend ({
  render:function(){
    this.$el.html("<div class=test><h4>1) 每次点击加1</h4><h4>" + this.data + "</h4></div><button class='add data-add' type=button>加1</button>")
    return this
  },
  events:{
    "click .data-add":"addMe",
    "click .data-back":"backMe"
  },
  // 事件方法
  addMe:function(){
    if (this.data < 10) {
      this.data ++
      this.render()
    } else {
      this.data = '点击已到上限'
      this.$el.html("<div class=test><h4>1) 每次点击加1</h4>" + this.data +"</div><br><button class='bank data-back' type=button>重新计数</button>")
      return this
      // this.render()
    }
  },
  backMe:function () {
    if (this.data = '点击已到上限') {
      this.data = 0
      this.render()
      this.$el.html("<div class=test><h4>1) 每次点击加1</h4><h4>" + this.data + "</h4></div><button class='add data-add' type=button>加1</button>")
      // this.render()
    }
  }
})

var Test_View = WRT_e.view.extend ({
  render:function () {
    this.$el.html(`
    <div class="outline_border">
      <div class="item-data">
        nane:`+ this.data.name +`</br>
        <span>age:</span>` + this.data.age+`
      </div>
      <button class="del delate-data" type="button">删除</button>
    </div>`)
    return this
  },
  events: {
    "click .delate-data":"delMe"
  },
  delMe:function(){
    this.trigger("del");
  }
})

var ArrayTest_View = WRT_e.view.extend ({
  render:function(){
    this.$el.html(`
    <button class="add add1-data"type="button">添加</button>
    <div>
      <button class="delall delall-data"type="button">一键删除</button>
    </div>`);
    this.children = []
    for (i = 0; i < this.data.length; i++) {
      var v = new Test_View();
      v.init({data:this.data[i]});
      this.$el.append(v.render().$el)
      v.on("del", this.deleteItem, this)
      this.children.push(v)
    }
    return this
  },
  events: {
    "click .add1-data":"addMe",
    // "click .addOne-data":"addOne",
    "click .delall-data":"delallMe"
  },
  // addOne:function(){
  //   // var onedata =[{name : "小王0", age:"55岁"}]
  //   if((this.data.length = 3)){
  //     for(var i = 0; i < 3; i ++) {
  //       var onedata =[]
  //       onedata.push({name:"小王" + i, age: 50+i});
  //       console.log(onedata)
  //     }
  //   }
  //   return this.render(onedata);
  // },
  addMe:function(){
    for(var i=0;i<this.data.length;i++){
      if((this.data.length >= 3)) {
        this.data = [{name : "小王0", age:"55岁"},
        {name : "小李0", age:"32岁"},
        {name : "小明0", age:"41岁"},
        {name : "小王1", age:"55岁"},
        {name : "小李1", age:"32岁"},
        {name : "小明1", age:"41岁"}]
        return this.render();
      }else{
        this.data = [{name : "小王2", age:"55岁"},
        {name : "小李2", age:"32岁"},
        {name : "小明2", age:"41岁"}]
        return this.render();
      }
    }
  },
  delallMe:function() {
    delete this.data[0];
    this.render();
  },
  deleteItem:function(item, o){
    this.data = _.without(this.data, item)
    this.render();
  },
})


