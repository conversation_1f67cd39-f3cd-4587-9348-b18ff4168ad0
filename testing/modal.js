//统一页面启动
$(document).ready(
  function () {
    //点击出现弹窗hint
    $('#btn').click(() => {
      console.log("1");
      model_1.text1()
      // WRT_e.api.au.getModal_1()
    })
    $('#btn1').click(() => {
      console.log("2");
      model_1.text2()
    })
    $('#btn2').click(() => {
      console.log("3");
      model_1.text3()
    })
    $('#btn3').click(() => {
      console.log("4");
      model_1.text4()
    })
    $('#btn4').click(() => {
      console.log("5");
      model_1.text5()
    })
    $('#btn5').click(() => {
      console.log("6");
      model_1.text6()
    })
    $('#btn6').click(() => {
      console.log("7");
      model_1.text7()
    })

    // 可关闭弹窗model
    $('#btn_1').click(() => {
      console.log("1_1");
      model_2.text_1()
    })
    $('#btn_2').click(() => {
      console.log("1_2");
      model_2.text_2()
    })
    //可关闭弹窗message
    $('#btn_3').click(() => {
      console.log("1_3");
      model_2.text_3()
    })
    $('#btn_4').click(() => {
      console.log("1_4");
      model_2.text_4()
    })
  }
)

// 弹窗
var model_1 = {
  text1: function () {
    WRT_e.ui.hint({
      type: 'success',
      msg: '这是一个提示错误的弹窗'
    })
  },
  text2: function () {
    WRT_e.ui.hint({
      type: 'error',
      msg: '这是一个提示错误的弹窗'
    })
  },
  text3: function () {
    WRT_e.ui.hint({
      type: 'info',
      msg: '这是一个提示信息的弹窗'
    })
  },
  text4: function () {
    WRT_e.ui.hint({
      type: 'warning',
      msg: '这是一个提示警告的弹窗',
    })
  },
  text5: function () {
    toastr.options = {  
      closeButton: true, // 设置显示"X" 关闭按钮
      debug: false, // 是否为调试；
      positionClass: "toast-top-right", // 显示位置top-left,top-center,top-right,right-bottom,bottom-center,left-bottom
      onclick: null, // 点击消息框自定义事件 
      showDuration: "300", // 显示动作时间
      hideDuration: "2000", // 隐藏动作时间
      timeOut: false, // 超过 xx 毫秒，消息框自动关闭
      showEasing: "swing",  // toastr显示时的动画缓冲方式
      hideEasing: "linear",  // toastr消失时的动画缓冲方式
      showMethod: "fadeIn", // 显示的方式，和jquery相同
      hideMethod: "fadeOut",  // 隐藏的方式，和jquery相同
      tapToDismiss: true, // 设置toastr被点击时关闭
    };  
    WRT_e.ui.hint({
      type: 'success',
      msg: '这是一个可点击关闭的弹窗'
    })
  },
  text6: function () {
    toastr.options = {  
      closeButton: true, // 设置显示"X" 关闭按钮
      debug: false, // 是否为调试；
      progressBar: true, // 设置显示timeout时间进度条
      positionClass: "toast-top-right", // 显示位置top-left,top-center,top-right,right-bottom,bottom-center,left-bottom
      onclick: null, // 点击消息框自定义事件
      showDuration: "300", // 显示动作时间
      hideDuration: "2000", // 隐藏动作时间
      timeOut: "3000", // 超过 xx 毫秒，消息框自动关闭
      extendedTimeOut: "1000",  //加长展示时间
      showEasing: "swing",  // toastr显示时的动画缓冲方式
      hideEasing: "linear",  // toastr消失时的动画缓冲方式
      showMethod: "fadeIn", // 显示的方式，和jquery相同
      hideMethod: "fadeOut",  // 隐藏的方式，和jquery相同
      tapToDismiss: false, // 设置toastr被点击时关闭
    };  
    WRT_e.ui.hint({
      type: 'success',
      msg: '这是一个提示成功的弹窗'
    })
  },
  text7: function () {
    toastr.options = {  
      closeButton: true, // 设置显示"X" 关闭按钮
      positionClass: "toast-top-right", // 显示位置top-left,top-center,top-right,right-bottom,bottom-center,left-bottom
      onclick: onclick1(), // 点击消息框自定义事件
      showDuration: "300", // 显示动作时间
      hideDuration: "2000", // 隐藏动作时间
      timeOut: false, // 超过 xx 毫秒，消息框自动关闭
      extendedTimeOut: "1000",  //加长展示时间
      showEasing: "swing",  // toastr显示时的动画缓冲方式
      hideEasing: "linear",  // toastr消失时的动画缓冲方式
      showMethod: "fadeIn", // 显示的方式，和jquery相同
      hideMethod: "fadeOut",  // 隐藏的方式，和jquery相同
      tapToDismiss: false, // 设置toastr被点击时关闭
    };
    function onclick1 () {
      WRT_e.ui.message({
        title: '提示',
        content: `请确认是否查看信息`,
        onOk() {
          WRT_e.ui.hint({
            type: 'success',
            msg: '这是一个提示成功的弹窗'
          })
          console.log('onOk')
        },
        onCancel() {
          console.log('onCancel')
        },
      })
    }
  },
}

// 弹窗2
var model_2 = {
  text_1: function () {
    WRT_e.ui.model({
      id: 'text_1',
      title: 'btn_1',
      content: `这是提条需要手动关闭通知`
    })
  },
  // 有问题
  text_2: function () {
    $("#btn_1").iziModal({
      title: "",
      subtitle: "",
      theme: "",
      headerColor: "#88A0B9",
      overlayColor: "rgba(0, 0, 0, 0.4)",
      iconColor: "",
      iconClass: null,
      width: 600,
      padding: 0,
      iframe: false,
      iframeHeight: 400,
      iframeURL: null,
      overlayClose: true,
      closeOnEscape: true,
      bodyOverflow: false,
      focusInput: true,
      autoOpen: false,
      transitionInModal: 'transitionIn',
      transitionOutModal: 'transitionOut',
      transitionInOverlay: 'fadeIn',
      transitionOutOverlay: 'fadeOut',
      onOpening: function() {},
      onOpened: function() {},
      onClosing: function() {},
      onClosed: function() {}
    });
    // WRT_e.ui.model({
    //   id: 'text_1',
    //   title: 'btn_1',
    //   content: `<button type="button" class="btn btn-default" onclick="model_2.text_1()">新的model</button>`
    // })
    $(document).on('onOpening', '#btn_1', function (e) {
      WRT_e.ui.model({
        id: 'text_1',
        title: 'btn_1',
        content: `1323`
      })
    });
  },
  text_3: function () {
    WRT_e.ui.message({
      title: 'text_3 ->btn_3',
      content: `这是提条需要点击的通知窗`,
      onOk() {
        console.log('onOk')
      },
      onCancel() {
        console.log('onCancel')
      },
    })
  },
  text_4: function () {
    WRT_e.ui.message({
      title: 'text_4',
      content: `这是提一条有关闭按钮的的通知窗`,
      onOk() {
        console.log('onOk')
      },
      onCancel() {
        console.log('onCancel')
      },
    })
  }
}
