//统一页面启动
$(document).ready(
  function () {
    one_case1.init()
  }
)
var one_case1 = {
  init:function(){
    var persondata=[
      {
        brname:"赛思敏",
        brno:"73312",
        brtitle_lm:"263-005",
        brgender:"女",
        brage:"32岁",
      },
      {
        brname:"吴鑫泽",
        brno:"318543",
        brtitle_lm:"263-007",
        brgender:"男",
        brage:"72岁",
      },
      {
        brname:"金眸",
        brno:"323234",
        brtitle_lm:"263-008",
        brgender:"男",
        brage:"39岁",
      },
      {
        brname:"陈离西",
        brno:"875564",
        brtitle_lm:"263-009",
        brgender:"男",
        brage:"42岁",
      },
      {
        brname:"金明",
        brno:"1212762",
        brtitle_lm:"263-010",
        brgender:"男",
        brage:"40岁",
      },
      {
        brname:"秋欧木",
        brno:"984124",
        brtitle_lm:"263-012",
        brgender:"女",
        brage:"38岁",
      },
      {
        brname:"胡一上",
        brno:"284114",
        brtitle_lm:"263-013",
        brgender:"男",
        brage:"56岁",
      },
      {
        brname:"李欣欣",
        brno:"668229",
        brtitle_lm:"263-014",
        brgender:"女",
        brage:"47岁",
      },
    ]
    $(".panel-body1").append(new person_View().init({data:persondata}).render().$el)
    // $("#main5").append()
  }
}
var person_View = WRT_e.view.extend({
  render:function(){
    this.$el.html(`
      ${_.map(this.data, (obj)=> 
      `<div class="row line">
        <div class="col-md-12">
          <span class="col-md-3 title_lm_bule">${obj.brtitle_lm}</span>
          <span class="col-md-5 title_lm_gray">${obj.brname+obj.brno}</span>
          <span class="col-md-2 title_lm_gray_1">${obj.brgender}</span>
          <span class="col-md-2 title_lm_gray_2">${obj.brage}</span>
        </div>
      </div>`)}
    `)
    return this
  }
})
// 专科日志
// var brPortal1_View = WRT_e.view.extend({
//   render:function(){
//     let html=`
//     <span>
//       <i class="glyphicon glyphicon-list-alt"></i>
//       <span class="panel-title-text">需及时上交的病例：${this.data.YXPSRS}</span>
//     </span>
//     <i class="glyphicon glyphicon-chevron-down icon-right"></i>`
//     this.$el.html(html)
//     return this
//   }
// })

// var massage_View = WRT_e.view.extend({
//   rander:function(){
//     this.html.$el(`
//     `)
//     return this
//   }
// })
// var group_View = WRT_e.view.extend({
  
// })