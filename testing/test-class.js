/*
 * @Author: your name
 * @Date: 2021-03-17 10:16:25
 * @LastEditTime: 2021-03-29 10:34:42
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \wzfyy\testing\test-class.js
 */
/*
类测试
*/
$(document).ready(
  function(){
      var data = [{name : "t1 ", count:0},
      {name : "t2 ", count:3},
      {name : "t3 ", count:4}]

      for(var i = 1; i < 10; i ++) {
          data.push({name:"name" + i, count:i});
      }
      
      var test = (new Array_View()).init({data:data});
      $("#main").append(test.render().$el)
  }
)

// 单个视图
var Object_View = WRT_e.view.extend({
    render:function(){
        this.$el.html("<div class=test>" + this.data.name + this.data.count + "</div><button class=delete data-delete type=button>delete</button>")
        return this
    },
    events:{
        "click .test":"callMe",
        "click [data-delete]":"deleteMe",
    },
    callMe:function(test){
        this.data.count ++;
        this.render()
    },
    deleteMe:function(){
        this.trigger("delete");
    }
})

// 组合视图
var Array_View = WRT_e.view.extend({
  render:function(){
    this.$el.html("");
    if(this.children){
      for (i = 0; i < this.children.length; i++) {
        this.children[i].remove();
      }
    }
    this.children = []
    for (i = 0; i < this.data.length; i++) {
      var v = new Object_View();
      v.init({data:this.data[i]});
      this.$el.append(v.render().$el)
      v.on("delete", this.deleteItem, this)
      this.children.push(v)
    }
    return this
  },
  deleteItem:function(item, o){
    this.data = _.without(this.data, item)
    this.render();
  }
})
// 创建视图类