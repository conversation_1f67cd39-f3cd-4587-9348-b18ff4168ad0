//统一页面启动
$(document).ready(
  function () {
    // try2
    one_case.init()
  }
)
// try2
var one_case = {
  init:function(){
    //获取我我的病人数据
    // WRT_e.api.ehrSz.getMyPatByLX({
    //   params: {
    //     as_lx: '2'
    //   },
    //   success(data) {
    //     if (data.Code == 1) {
    //       $("#patient_list").html(
    //         new brList_View().init({
    //           data: JSON.parse(data.Result)['BRXX']
    //         }).render().$el
    //       )
    //     }
    //   }
    // })
    var data1 = 111
    $("#main2").append(new brList_View().init({data:brLists[1].data}).render().$el);
    $("#main2_1").append(new brList_View1().init({data:brLists[1].data}).render().$el)
    $("#main3").append(new brPortal_View().init({data:portal}).render().$el)
    $("#main5").append(new massage_View().init({data:brLists[1].data}).render().$el)
    // $("#main5").append(new massage_View().init({data:brLists[1].data}).render().$el)
  }
}
// 1个病人
var brList_View = WRT_e.view.extend({
  render:function () {
    // console.log(brLists[1].data[0].brtitle_lm)
    let html =`
    <div class="item">
      <div class="item_box">
        <div class="tag flex-row justify-content-between">
          <div>
            <span class="e-tag e-tag-blue">
            ${brLists[1].data[0].brtitle_lm}
            </span>
            <span class="e-tag e-tag-red">
              893天
            </span>
            <span class="e-tag e-tag-black">
              6
            </span>
          </div>
          <div>
            <span class="e-tag e-tag-orange">
            ${brLists[1].data[0].brtitle_rm}
            </span>
          </div>
        </div>
        <div class="flex-row align-items-center">
          <img src="../images/man.png" width="40" alt="">
          <div class="flex-fill user_text">
            <strong>${brLists[1].data[0].brno}</strong><br />
            <b>${brLists[1].data[0].brname}</b>&nbsp;&nbsp;男&nbsp;&nbsp;63岁
          </div>
        </div>
        <div class="status"></div>
        <div class="message">${brLists[1].data[0].brzd}</div>
      </div>
    </div>`
    this.$el.html(`<div class="main2">${html}</div>`)
    return this
  }
})
// 多个
var brList_View1 = WRT_e.view.extend({
  render:function () {
    this.$el.html(`
    <div class="main2">
      ${_.map(this.data, (obj)=> 
        //循环病人列表
        `<div class="item">
          <div class="item_box">
            <div class="tag flex-row justify-content-between">
              <div>
                <span class="e-tag e-tag-blue">
                ${obj.brtitle_lm}
                </span>
                <span class="e-tag e-tag-red">
                  893天
                </span>
                <span class="e-tag e-tag-black">
                  6
                </span>
              </div>
              <div>
                <span class="e-tag e-tag-orange">
                ${obj.brtitle_rm}
                </span>
              </div>
            </div>
            <div class="flex-row align-items-center">
              <img src="../images/man.png" width="40" alt="">
              <div class="flex-fill user_text">
                <strong>${obj.brno}</strong><br/>
                <b>${obj.brname}</b>&nbsp;&nbsp;男&nbsp;&nbsp;63岁
              </div>
            </div>
            <div class="status"></div>
            <div class="message">${obj.brzd}</div>
          </div>
        </div>`)}
    </div>`)
    return this
  }
})
// main3,扩展一个新的视图类
var brPortal_View = WRT_e.view.extend({
  render:function(){
    this.$el.html(`
    <div class="subject_log">
      <div class="panel-case" id="accordion">
        <div class="panel">
          <div class="panel-heading">
            <a class="flex-row align-items-center justify-content-between panel-title" data-toggle="collapse" data-parent="#accordion" href="#panel1">
              <span>
                <i class="glyphicon glyphicon-list-alt"></i>
                <span class="panel-title-text">专科日志</span>
              </span>
              <i class="glyphicon glyphicon-chevron-down"></i>
            </a>
          </div>
          <div id="panel1" class="collapse-all collapse">
            <div class="panel-body">
              <div class="row ">
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">在院</div>
                    <div class="box_value">${this.data.ZYRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">新入院</div>
                    <div class="box_value">${this.data.XRYRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">出院</div>
                    <div class="box_value">${this.data.CYRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">转入</div>
                    <div class="box_value">${this.data.ZRRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">转出</div>
                    <div class="box_value">${this.data.ZCRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">病危病人</div>
                    <div class="box_value">${this.data.WZBRRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">特级护理</div>
                    <div class="box_value">${this.data.TJHLRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">一级护理</div>
                    <div class="box_value">${this.data.YJHLRS}</div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="box">
                    <div class="box_label">二级护理</div>
                    <div class="box_value">${this.data.EJHLRS}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>`)
    return this
  }
})
// 侧边
var massage_View = WRT_e.view.extend({
  render:function () {
    this.$el.html(`

      <table>
        <tr>
          <th><input type=“”/></th>
          <th>序号</th>
          <th>医嘱名字</th>
          <th>每次用量</th>
          <th>计量单位</th>
        </tr>
        <tr>
          <td><input/></td>
          <td>1</td>
          <td>xxx</td>
          <td>10</td>
          <td>
            <select>
              <option value ="volvo">ml</option>
              <option value ="volvo">包</option>
              <option value ="volvo">针</option>
              <option value ="volvo">g</option>
            <select>
          </td>
        </tr>
      </table>
    `)
    return this
  }
})