<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>首页</title>
  <meta name="description" content="首页">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
table thead th{
  text-align: center;
  padding: 2px;
}
table tbody td{
  padding: 2px;
}
table tr:nth-child(even) { 
  background-color: #f2f2f2;
}
table tr:nth-child(odd) { 
  background-color: #ffffff;
}
select{
  height: 25px;
  width: 90px;
  border: none;
}
label{
  margin-left: 10px;
}
</style>
<body id="body_content">
  <div class="main" style="padding: 10px;">
    <div id="head_info" style="margin: 0px auto;width: 1340px;background: rgb(115 115 174 / 10%);padding: 10px;">
      <form>
        <label for="lx">选择类型：  </label>
        <select><option>全部</option></select>
        <label><input type="radio" name="yy" value="1" />未预约</label>
        <label><input type="radio" name="yy" value="2" />已预约 </label>
        <label for="yyrq">预约日期：  </label>
        <input type="text" name="yyrq" style="width: 100px;" />
        <label for="jczt">检查状态：  </label>
        <select><option>全部</option></select>
        <label for="dyrq">打印日期：  </label>
        <input type="text" name="dyrq" /></br>
        <label for="ch"> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 床号：  </label>
        <input type="text" name="ch" style="width: 30px;" />
        <label for="zyh">住院号：  </label>
        <input type="text" name="zyh" style="width: 100px;" />
        <label for="xm">姓名：  </label>
        <input type="text" name="xm" style="width: 100px;" />
        <label for="jcxm">检查项目：  </label>
        <select><option>全部</option></select>
        <label for="zxks">执行科室：  </label>
        <select><option>全部</option></select>
        <label><input type="checkbox" checked/>未打印</label>
        <label><input type="checkbox" checked/>已打印</label>
        <button class="e_btn_primary">查找</button>
      </form>
    </div>
    <div style="margin: 6px auto;">
      <table border="1" style="width: 1340px;border: 1px solid #cdc7c7;">
        <thead>
          <tr style="background-color: #f2f2f2;">
            <th width="30"><input type="checkbox" /></th>
            <th width="40">床号</th>
            <th width="80">姓名</th>
            <th width="80">患者ID</th>
            <th width="80">申请单号</th>
            <th width="400">检查主题</th>
            <th width="80">检查状态</th>
            <th width="100">申请时间</th>
            <th width="100">预约时间</th>
            <th width="100">打印日期</th>
            <th width="100">执行科室</th>
            <th width="120">注意事项</th>
            <th width="70">打印状态</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td style="text-align: center;"><input type="checkbox" /></td>
            <td>22</td>
            <td>吴以墨</td>
            <td>91625499</td>
            <td>2207310451</td>
            <td>版部越两(肝、起、脚、预(额部越而):场、始家警(暖部超而)]</td>
            <td>确认报告</td>
            <td>07-31 10:57</td>
            <td>08-01 12:00</td>
            <td>08-01 12:00</td>
            <td>超声医学科</td>
            <td>件行冠旅成</td>
            <td></td>
          </tr>
          <tr>
            <td style="text-align: center;"><input type="checkbox" /></td>
            <td>22</td>
            <td>吴以墨</td>
            <td>91625499</td>
            <td>2207310451</td>
            <td>版部越两(肝、起、脚、预(额部越而):场、始家警(暖部超而)]</td>
            <td>确认报告</td>
            <td>07-31 10:57</td>
            <td>08-01 12:00</td>
            <td>08-01 12:00</td>
            <td>超声医学科</td>
            <td>件行冠旅成</td>
            <td></td>
          </tr>
          <tr>
            <td style="text-align: center;"><input type="checkbox" /></td>
            <td>22</td>
            <td>吴以墨</td>
            <td>91625499</td>
            <td>2207310451</td>
            <td>版部越两(肝、起、脚、预(额部越而):场、始家警(暖部超而)]</td>
            <td>确认报告</td>
            <td>07-31 10:57</td>
            <td>08-01 12:00</td>
            <td>08-01 12:00</td>
            <td>超声医学科</td>
            <td>件行冠旅成</td>
            <td></td>
          </tr>
          <tr>
            <td style="text-align: center;"><input type="checkbox" /></td>
            <td>22</td>
            <td>吴以墨</td>
            <td>91625499</td>
            <td>2207310451</td>
            <td>版部越两(肝、起、脚、预(额部越而):场、始家警(暖部超而)]</td>
            <td>确认报告</td>
            <td>07-31 10:57</td>
            <td>08-01 12:00</td>
            <td>08-01 12:00</td>
            <td>超声医学科</td>
            <td>件行冠旅成</td>
            <td></td>
          </tr>
        </tbody>
      </table>
    </div>
    <div style="margin: 10px auto;">
      <button class="e_btn_primary">预约平台</button>
      <button class="e_btn_primary">打印(P)</button>
      <button class="e_btn_primary">关闭(C)</button>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    // document.write("<script type='text/javascript' src='js/e-.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>
</html>