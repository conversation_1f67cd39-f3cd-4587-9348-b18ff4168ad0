<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>病程查询</title>
  <meta name="description" content="病程查询">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <style>
    /* Body */
    #blwsList {
      position: absolute;
      /* position: relative; */
      margin: auto;
      overflow:hidden;
      width: 100%;
      height: 100%;
      font-size: 16px;
    }
    /* 病程记录（最外层） */
    .container_blwslist {
      position: relative;
      width: 100%;
      height: 100%;
      /* padding: 5px 10px; */
      padding-left: 10px;
      padding-top: 5px;
    }
    /* @media (max-height: 815px) {
      #blwsList {
        position: relative;
        overflow:hidden;
        height: 98%;
      }
      .blwslist_info {
        position: relative;
        height: 6%;
        margin-top: 0.5%;
        margin-bottom: 0.5%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: row;
        align-content: flex-end;
        flex-wrap: nowrap;
      }
    }
    @media (max-height: 600px) {
      #blwsList {
        position: relative;
        overflow:hidden;
        height: 98%;
      }
      .blwslist_info {
        position: relative;
        height: 6%;
        margin-top: 2%;
        margin-bottom: 2%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: row;
        align-content: flex-end;
        flex-wrap: nowrap;
      }
    }
    @media (max-height: 400px) {
      #blwsList {
        position: relative;
        overflow:hidden;
        height: 94%;
      }
      .blwslist_info {
        position: relative;
        height: 6%;
        margin-top: 2%;
        margin-bottom: 2%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: row;
        align-content: flex-end;
        flex-wrap: nowrap;
      }
    }
    @media (max-height: 300px) {
      #blwsList {
        position: relative;
        overflow:hidden;
        height: 88%;
      }
      .blwslist_info {
        position: relative;
        height: 6%;
        margin-top: 3%;
        margin-bottom: 3%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        flex-direction: row;
        align-content: flex-end;
        flex-wrap: nowrap;
      }
    } */
    /* 需要调整（新加入下方按钮以及其他内容位置） */
    /* 顶部（刷新页面+病人信息） */
    .blwslist_info .titleTop{
      position: relative;
      /* height: 6.3%; */
      /* width: 100%; */
      height: 60px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      flex-direction: row;
      /* align-content: flex-end; */
      flex-wrap: wrap;
      /* flex-wrap: nowrap; */
      font-size: 16px;
      padding-top: 10px;
      padding-bottom: 10px;
    }
    .blwslist_info .rightBtnTop {
      position: relative;
      width: 324;
      margin: 0;
      /* height: 66.6%; */
      /* padding-top: 10px; */
      /* padding-bottom: 10px; */
    }
    .blwslist_info .rightBtnTop>span>.bcadd{
      position: relative;
      width: 100px;
      height: 40px;
      margin-right: 5px;
      cursor: pointer;
    }
    .blwslist_info .rightBtnTop>span>.bcadd:hover,.blwslist_info .rightTop .btn_sx:hover{
      color: #1890FF;
      border: 1px solid #1890FF;
    }
    .rightTop .tui-checkbox {
      position: relative;
      border-radius: 10px;
    }
    .blwslist_info .rightBtnTop>span>.right_hide{
      position: relative;
      width: 80px;
      height: 40px;
      /* margin-right: 5px; */
    }
    .blwslist_info .rightBtnTop>span>.right_show{
      position: relative;
      width: 80px;
      height: 40px;
      margin-right: 5px;
    }
    .brBlh, .brName {
      font-size: 18px;
    }
    .blwslist_info .rightBtnTop>span {
      position: relative;
      text-align: center;
      margin: auto;
      /* height: 66.6%; */
    }
    .blwslist_info .rightBtnTop>span>a {
      position: relative;
      line-height: 40px;
      border-radius: 4px;
      font-size: 15px;
      text-decoration: none;
    }
    .blwslist_info .brBlue{
      position: relative;
      /* background: #3D6CC8; */
      margin-right: 10px;
    }
    .blwslist_info .brRed{
      position: relative;
      /* background: #C82222; */
      /* margin-right: 10px; */
    }
    .blwslist_info .brGray{
      position: relative;
      background: #555555;
      color:#FFFFFF;
      /* margin-right: 20px; */
    }
    .blwslist_info .brChangeColor {
      /* color:#FFFFFF; */
      margin-left: 20px;
    }
    .titleTop div:nth-child(3) {
        margin-left: auto;
        display: flex;
        align-items: center;
      }
    .blwslist_info .rightTop .btn_sx{
      height: 40px;
      width: 96px;
      font-size: 16px;
      margin: 0 21px 0 10px;
      Font-family:Microsoft YaHei;
      Font-style: Regular;
      Vertical-align: Center;
    }
    .blwslist_info .rightTop .simpleMode {
      color: #707070;
      font-size: 15px;
      /* margin: 0 30px 0 10px; */
      margin-right: 20px;
      margin-left: auto;
      border-radius: 50%;
    }

    /* .container_blwslist .flex-row {
      position: relative;
      display: flex;
      flex-wrap: nowrap;
      height: 94%;
      height: 93%;
      flex-direction: row;
      align-content: flex-end;
    } blwslist_info？*/
    .align-items-center {
      position: relative;
    }
    
    /* 文书内容总 */
    .blwslist_inner {
      position: relative;
      height: 100%;
      display: flex;
      flex-wrap: nowrap;
    }
    /* 左侧列表样式 */
    /* 需要调整（按钮位置样式，li标签的央视图标及位置） */
    .blwslist_inner_left {
      /* flex: 0 0 324px; */
      flex: 0 0 280px;
       /* 287px; */
      /* flex: 0 0 19.5%; */
      position: relative;
      /* height: 99.9%; */
      height: 100%;
      /* height: 98.45%; */
      /* height: 790px; */
      /* margin-right: 6px; */
      border: 1px solid #E8E8E8;
      /* background: #F4F4F4; */
      /* overflow-y: auto; */
    }
    /* 滚动条（整体，滑块，轨道，两端按钮，轨道边角） */
    /* .blwslist_inner_left::-webkit-scrollbar{ 
      width:8px; 
    }
    .blwslist_inner_left::-webkit-scrollbar-thumb{
      border: 1px solid #F3F2F3;
      width:6px;
      border-radius:5px;
    }
    .blwslist_inner_left::-webkit-scrollbar-button{
      display: none;
    } */
    .list {
      position: relative;
      /* border: 1px solid #E8E8E8; */
      /* height: 95%; */
      height: 100%;
    }
    .list>div:first-child{
      position: relative;
      height: 100%;
    }
    /* 想到的样式 */
    /* .blwslist_inner_left .list_title {
      width: 295px;
      line-height: 66px;
      text-align: center;
      color: #3d6cc8;
      margin: auto;
      font-size: 22px;
      font-weight: bold;
      border-bottom: 2px solid #3d6cc8;
      // background: linear-gradient(
      180deg
      , #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%);
    } */
    .blwslist_inner_left .list_title {
      /* // width: 324px; */
      /* line-height: 24px;
      text-align: center;
      color: #fff;
      border: 2px solid #3D6CC8;
      background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%); */
      color: #fff;
      /* background: linear-gradient(180deg, rgba(61, 108, 200, 0.8) 0%, rgba(61, 108, 200, 0.48) 100%); */
      background: rgb(61 108 200 / 80%);
      border: 1px solid #3D6CC8;
      height: 40px;
      font-size:16px;
      line-height: 40px;
      padding-left: 20px;
    }
    #sidebar {
      position: relative;
      /* height: 100%; */
      /* background: #F4F4F4; */
      height: calc(100% - 40px);
    }
    /* .left_nr_line1 {
      position: relative;
      width: 92%;
      margin: auto;
      margin-top: 6px;
      // background: #fff;
    } */
    .left_nr_line1 {
      position: relative;
      width: 100%;
      /* // position: fixed;
      // width: 17.5%; */
      margin-top: 9px;
      height: 30px;
      /* // background: #fff; */
    }
    .xz_nr {
      /* position: relative;
      padding: 0px;
      height: 30px;
      width: 60%;
      display: inline;
      font-size: 16px; */
      position: relative;
      padding: 0px;
      height: 30px;
      width: 100%;
      padding: 0 20px;
      display: inline;
      font-size: 17px;
    }
    /* .left_nr_line1>span {
      position: relative;
      text-align: center;
      margin: auto;=
    }
    .left_nr_line1>span>a {
      position: relative;
      margin-right: 2px;
      padding: 2px 2px;
      border-radius: 4px;
      font-size: 15px;
      text-decoration: none;
    } */
    /* .left_nr_line1>span>a:hover {
      border: 1px solid #3D6CC8;
    } */
    .blwslist_inner_left .list_inner {
      margin: auto;
      /* margin: 0px 18px; */
      /* padding: 20px 18px; */
      position: relative;
      /* height: 700px; */
      /* height: calc(100% - 30px); */
      overflow-y: auto;
      font-size: 16px;
      /* background: #FFFFFF; */
      background: #F4F4F4;
      /* display: flex; */
      /* position: fixed; */
      /* width: 80%; */
      /* height: 100%;
      overflow-y: auto;
      flex-wrap: wrap; */
    }
    /* 滚动条（整体，滑块，轨道，两端按钮，轨道边角） */
    .blwslist_inner_left .list_inner::-webkit-scrollbar{ 
      width:8px; 
    }
    .blwslist_inner_left .list_inner::-webkit-scrollbar-thumb{
      border: 1px solid #F3F2F3;
      width:6px;
      border-radius:5px;
    }
    .blwslist_inner_left .list_inner::-webkit-scrollbar-button{
      display: none;
    }
    .list_inner_W>div:first-child {
      position: relative;
      height: 100%;
    }
    /* .blwslist_inner_left #sidebar>ul {
      position: flex;
      height: 100%;
      overflow-y: auto;
    } */
    /* .blwslist_inner_left #sidebar {
      position: relative;
      height: 100%;
      overflow-y: auto;
    } */
    /* 左侧列表内容 */
    .submenu {
      display: flex;
      /* margin: 15px 0; */
      /* font-size: 16px; */
      font-size: 14px;
      cursor: pointer;
      /* height: 100px; */
      /* box-shadow: 0px -1px 0px 0px #888888 inset; */
      background: #FFFFFF;
      justify-content: space-around;
      /* line-height: 10px; */
    }
    .submenu:hover{
      background: #E1F1FF;
      color: #1890FF;
      /* border: 1px solid #1890FF; */
    }
    .submenu>a {
      position: relative;
      width: 234px;
      padding: 2px 0;
      /* box-shadow: 0px -1px 0px 0px #888888 inset; */
      /* background: #FFFFFF; */
      display: flex;
      vertical-align: middle;
      align-items: center;
      text-decoration: none;
    }
    .submenu>a>i {
      font-size: 16px;
      margin-right: 12px;
      margin-top: -5px;
      color: #888888;
    }
    .submenu>a:hover i{
      color: #1890FF;
    }
    .submenu>a>div {
      position: relative;
      width: 234px;
    }
    .submenu>a #submenu_title{
      position: relative;
      padding-bottom: 1px;
    }
    .submenu>a #submenu_name_time {
      position: relative;
      padding-top: 1px;
      padding-top: 3px;
      /* font-size: 14px; */
      font-size: 15px;
      display: flex;
      align-items: center;
    }
    .submenu>a #submenu_name1, .submenu>a #submenu_time1 {
      /* padding-top: 1px; */
      font-size: 18px;
    }
    .submenu>a #submenu_time1 {
      position: relative;
      float: right;
      padding-top: 0px;
    }
    .submenu>a #submenu_title1 {
      margin-bottom: 3px;
    }
    .submenu>a #submenu_name_time #submenu_time {
      position: relative;
      margin-left: auto;
    }
    /* .submenu>a {
      color: #707070;
      height: 100px;
    }
    .submenu>a:hover {
      color: #1890FF;
      background: #E1F1FF;
    } */
  

    /* 右侧 */

    /* 右上部分 */
    .right_top {
      position: relative;
      border: 1px solid #3D6CC8;
      background: #fff;
      margin-bottom: 5px;
      width: 99.9%;
    }
    .right_top_title {
      /* position: relative;
      background: linear-gradient(180deg, rgba(61, 108, 200, 0.24) 0%, rgba(61, 108, 200, 0) 100%);
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #3D6CC8; */
      /* height: 20%; */
      /* border-bottom: 1px solid #3D6CC8; */
      position: relative;
      width: 100%;
      height: 40px;
      border-bottom: 1px solid #3D6CC8;
      /* background: linear-gradient(180deg, rgba(61, 108, 200, 0.8) 0%, rgba(61, 108, 200, 0.48) 100%); */
      background: rgb(61 108 200 / 80%);
      color: #FFFFFF;
      cursor: pointer;
      
    }
    .right_top_title>span {
      font-family: Microsoft YaHei;
      /* font-size: 22px; */
      font-size: 18px;
      font-style: normal;
      font-weight: bolder;
      margin-left: 15px;
      line-height: 40px;
      text-align: left;
    }
    .right_top_content {
      overflow:hidden;
      zoom:1;
    }
    .topline {
      list-style: none;
      position: relative;
      /* width: 100%; */
      height: 34px;
      float: left;
      /* border-bottom: 1px solid #DADADA; */
      display: inline-flex;
      flex-wrap: nowrap;
      align-content: space-around;
      align-items: center;
      padding: 4.7px 0px 4px 15px;
      text-overflow: ellipsis;
      font-size: 14px;
      white-space: nowrap;
      flex-direction: row;
      justify-content: flex-start;
    }
    .right_top .right_top_content>li>span {
      position: relative;
      margin: 1px ;
    }
    .bgsj {
      position: relative;
      display:-moz-inline-box;
      display:inline-block;
      width:161px;
    }
    .jcxm {
      position: relative;
    }
    .jcjg{
      color: red;
    }
    .xsNow {
      position: relative;
      /* padding: 5px 5px 0px 5px; */
      padding: 15px;
    }
    /* @media (max-width: 758px) {
      .topline:hover{
        position: relative;
        top: auto;
        width: auto;
        text-overflow:inherit; 
        overflow: visible; 
        height: 70px;
        white-space: pre-line;
      }
      .topline:hover>span{
        position: relative;
        margin: 1px ;
        top: -18px;
      }
    } */
    /* 右下部分 */
    /* 高度百分比调不了 */
    iframe {
      position: relative;
      /* width: 100%; */
      overflow: auto !important;
      /* max-height: 780px !important;
      height: 410px !important; */
    }
    /* 右侧下拉框 */
    .blwslist_inner_right {
      position: relative;
      box-shadow: none;
      font-size: 14px;
      border: 1px solid #E8E8E8;
      /* background: #E5E5E5; */
      /* height: 100% ; */
      height: calc(100% - 1px);;
      width: calc(100% - 279px);
      border-radius: 0;
      margin-left: 6px;
    }
    
    .blwslist_inner_right .panel-group {
      position: relative;
      width: 100%;
      /* height: 100%; */
      height: calc(100% - 2px);
    }
    .panel-group>div:first-child {
      position: relative;
      width: 100%;
      height: 100%;
    }
    .zd{
      position: relative;
      width: 100%;
      overflow-y: auto;
       /* overflow: hidden auto; */
    }
    .zd .panel {
      position: relative;
      margin-bottom: 12px;
    }
    /* 需要调整（title底色，按钮内容位置及图标） */
    .blwslist_inner_right .panel-group .panel+.panel {
      margin-top: 2px;
      position: relative;
      /* margin-bottom: 12px; */
    }
    #blwsList >.blwslist_inner_right .panel {
      position: relative;
      height: 100%;
      width: 100% !important;
      /* margin-bottom: 12px; */
    }
    .blwslist_inner_right .panel-title_line {
        position: relative;
        width:100%;
        height: 40px;
        /* background: linear-gradient(180deg, rgba(61, 108, 200, 0.8) 0%, rgba(61, 108, 200, 0.48) 100%); */
        /* background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%); */
        background: rgb(61 108 200 / 80%);
        color: #fff;
        border: 1px solid #9A9A9A;
        /* margin-bottom: 12px; */
        /* border: 1px solid #3D6CC8; */
        line-height: 40px;
        float: left;
        padding: 0px 0px 0px 19px;
        cursor: pointer;
        /* font-size: 18px; */
        display: flex;
      }
    @media screen and (max-width: 1920px){
      .blwslist_inner_right .panel-title_line {
        font-size: 18px;
      }
      .panel-title_line>.titleNow{
        position: relative;
        margin-left: 14.2px;
        line-height: 40px;
        /* width: calc(100% - 15px - 401px); */
        width: calc(100% - 15px - 354px);
        display:block;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        /* font-size: 16px; */
        /* display: flex;
        justify-content: space-evenly; */
        /* justify-content: space-between; */
      }
      .panel-title_line>.titleNow p:nth-child(1) {
        position: relative;
        margin-right: 4%;
        max-width: 430px;
        min-width: 430px;
        float: left;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 19px;
      }
      .panel-title_line>.titleNow p:nth-child(2) {
        position: relative;
        margin-right: 4%;
        min-width: 100px;
        float: left;
        font-size: 17px;
      }
      .panel-title_line>.titleNow p:nth-child(3) {
        position: relative;
        margin-right: 4%;
        min-width: 144px;
        float: left;
      }
      .panel-title_line>.titleAdd{
        position: relative;
        margin-right: 8px;
      }
      .panel-title_line>.titleBtn{
        position: relative;
        margin-left: auto;
        line-height: 40px;
        right: 8px;
      }
      .panel-title_line>.titleBtn>span {
        margin-right: 12px;
        /* margin-right: 6px; */
      }
    }
    @media screen and (max-width: 1600px){
      .blwslist_inner_right .panel-title_line {
        font-size: 18px;
      }
      .panel-title_line>.titleNow{
        position: relative;
        margin-left: 14.2px;
        line-height: 40px;
        /* width: calc(100% - 15px - 401px); */
        width: calc(100% - 15px - 354px);
        display:block;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        /* font-size: 16px; */
        /* display: flex;
        justify-content: space-evenly; */
        /* justify-content: space-between; */
      }
      .panel-title_line>.titleNow p:nth-child(1) {
        position: relative;
        margin-right: 3%;
        min-width: 430px;
        float: left;
      }
      .panel-title_line>.titleNow p:nth-child(2) {
        position: relative;
        margin-right: 3%;
        min-width: 100px;
        float: left;
      }
      .panel-title_line>.titleNow p:nth-child(3) {
        position: relative;
        margin-right: 3%;
        min-width: 144px;
        float: left;
      }
      .panel-title_line>.titleAdd{
        position: relative;
        margin-right: 8px;
        /* font-size: 16px; */
      }
      .panel-title_line>.titleBtn{
        position: relative;
        margin-left: auto;
        line-height: 40px;
        right: 8px;
        /* font-size: 16px; */
      }
      .panel-title_line>.titleBtn>span {
        /* margin-right: 12px; */
        margin-right: 6px;
      }
    }
    @media screen and (max-width: 1366px){
      .blwslist_inner_right .panel-title_line {
        font-size: 14px;
      }
      .panel-title_line>.titleNow{
        position: relative;
        margin-left: 7.2px;
        line-height: 40px;
        /* width: calc(100% - 15px - 401px); */
        width: calc(100% - 15px - 291px);
        /* font-size: 16px; */
        display:block;
        white-space:nowrap;
        overflow:hidden;
        text-overflow:ellipsis;
        /* display: flex;
        justify-content: space-evenly;
        justify-content: space-between; */
      }
      .panel-title_line>.titleNow p:nth-child(1) {
        position: relative;
        /* margin-right: 6%; */
        margin-right: 2%;
        min-width: 336px;
        float: left;
      }
      .panel-title_line>.titleNow p:nth-child(2) {
        position: relative;
        margin-right: 2%;
        min-width: 77px;
        float: left;
      }
      .panel-title_line>.titleNow p:nth-child(3) {
        position: relative;
        margin-right: 2%;
        min-width: 112px;
        float: left;
      }
      .panel-title_line>.titleAdd{
        position: relative;
        margin-right: 8px;
      }
      .panel-title_line>.titleBtn{
        position: relative;
        margin-left: auto;
        line-height: 40px;
        right: 8px;
      }
      .panel-title_line>.titleBtn>span {
        /* margin-right: 12px; */
        margin-right: 6px;
      }
    }
    
    .panel-title_line>i {
      position: relative;
      line-height: 40px;
      /* margin-right:auto */
    }
    

    
    .panel-title_line>.titleBtn>span>a,.panel-title_line>.titleBtn>.panel_ct>span>a{
      background: #FFFFFF;
      padding: 5px 4px;
      border-radius: 4px;
      text-decoration: none;
      height: 28px;
    }
    .panel-title_line>.titleBtn>span .editclick {
      position: relative;
      color: #707070;
      border: 1px solid #888888;
    }
    .panel-title_line>.titleBtn>span .editclick:hover {
      color: #FFFFFF;
      background: #888888;
    }
    .panel-title_line>.titleBtn>span .imgclick {
      position: relative;
      color: #FFA654;
      border: 1px solid #FFA654;
    }
    .panel-title_line>.titleBtn>span .imgclick:hover {
      color: #FFFFFF;
      background: #FFA654;
    }
    /* .panel-title_line>.titleBtn>..panel_ct {
      margin-right: 0px !important;
    } */

    .panel-title_line>.titleBtn>.panel_ct>span {
      /* margin-left: 12px; */
      margin-right: 6px;
    }

    .panel-title_line>.titleBtn>.panel_ct>span .saveclick {
      position: relative;
      color: #1890FF;
      border: 1px solid #1890FF;
    }
    .panel-title_line>.titleBtn>.panel_ct>span .saveclick:hover {
      color: #FFFFFF;
      background: #1890FF;
    }
    .panel-title_line>.titleBtn>.panel_ct>span .delclick {
      position: relative;
      color: #C82222;
      border: 1px solid #C82222;
    }
    .panel-title_line>.titleBtn>.panel_ct>span .delclick:hover {
      color: #FFFFFF;
      background: #C82222;
    }
    .panel-title_line>.titleBtn>.panel_ct>span .printclick {
      position: relative;
      color: #7F00FE;
      border: 1px solid #7F00FE;
    }
    .panel-title_line>.titleBtn>.panel_ct>span .printclick:hover {
      color: #FFFFFF;
      background: #7F00FE;
    }
    .blwslist_inner_right .panel-title_line.collapsed {
      background: #FFFFFF;
      /* margin-bottom: 12px; */
      /* background: linear-gradient(180deg, rgba(61, 108, 200, 0.24) 0%, rgba(61, 108, 200, 0) 100%); */
      color: #333333;
    }
    .blwslist_inner_right .panel-title_line.collapsed>i {
      color: #888888;
    }
    .blwslist_inner_right .panel-title_line.collapsed>.glyphicon-ok {
      color: #1890FF;
    }
    .blwslist_inner_right .panel-title_line.collapsed>span>a, .blwslist_inner_right .panel-title_line.collapsed>.titleBtn>.panel_ct>span>a {
      background: #FFFFFF;
      text-decoration: none;
    }
    .panel-body {
      position: relative;
      height: 100%;
      /* width: 99.92%; */
      width: 100%;
      overflow-x: auto !important;
      padding: 0px !important;
      margin: 0;
    }
    /* 嵌套iframe内部内容样式 */
.c001
{
    FONT-SIZE: 18px;
    COLOR: black;
    FONT-FAMILY: 黑体
}
.c002
{
    BORDER-RIGHT: medium none;
    BORDER-TOP: medium none;
    FONT-SIZE: 14px;
    MARGIN-LEFT: 20px;
    BORDER-LEFT: medium none;
    COLOR: black;
    BORDER-BOTTOM: medium none;
    font-family: Microsoft YaHei;
}
.c004
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 15px;
    COLOR: black;
    FONT-FAMILY: 黑体
}
.c005
{
    PADDING-RIGHT: 4px;
    PADDING-LEFT: 4px;
    FONT-SIZE: 16px;
    PADDING-BOTTOM: 4px;
    MARGIN: 10px;
    COLOR: olive;
    PADDING-TOP: 4px;
    font-family: Microsoft YaHei;
}
.c006
{
    PADDING-RIGHT: 4px;
    PADDING-LEFT: 4px;
    FONT-SIZE: 18px;
    PADDING-BOTTOM: 4px;
    MARGIN: 10px;
    COLOR: purple;
    PADDING-TOP: 4px;
    font-family: Microsoft YaHei;
}
.c007
{
    PADDING-RIGHT: 4px;
    PADDING-LEFT: 4px;
    FONT-SIZE: 18px;
    PADDING-BOTTOM: 4px;
    MARGIN: 10px;
    COLOR: red;
    PADDING-TOP: 4px;
    font-family: Microsoft YaHei;
}
.c008
{
    BORDER-RIGHT: outset;
    PADDING-RIGHT: 4px;
    BORDER-TOP: outset;
    PADDING-LEFT: 4px;
    FONT-SIZE: 18px;
    /* BACKGROUND-IMAGE: url(file://C:\My Documents\beijin.jpg); */
    PADDING-BOTTOM: 4px;
    MARGIN: 10px;
    BORDER-LEFT: outset;
    WIDTH: 200pt;
    COLOR: red;
    PADDING-TOP: 4px;
    BORDER-BOTTOM: outset;
    font-family: Microsoft YaHei;
}
.c011
{
    PADDING-RIGHT: 4px;
    PADDING-LEFT: 4px;
    FONT-SIZE: 18px;
    PADDING-BOTTOM: 4px;
    MARGIN: 10px;
    COLOR: blue;
    PADDING-TOP: 4px;
    font-family: Microsoft YaHei;
}
.h1
{
    PADDING-RIGHT: 0px;
    FONT-WEIGHT: bolder;
    FONT-SIZE: 22px;
    COLOR: black;
    FONT-FAMILY: 隶书;
    TEXT-ALIGN: center
}
.h2
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 24px;
    COLOR: black;
    FONT-FAMILY: 黑体;
    TEXT-ALIGN: center
}
.h3
{
    FONT-SIZE: 16px;
    MARGIN-LEFT: 40px;
    FONT-FAMILY: 隶书;
    TEXT-ALIGN: left
}
.h4
{
    PADDING-RIGHT: 4px;
    PADDING-LEFT: 4px;
    FONT-SIZE: 20px;
    PADDING-BOTTOM: 4px;
    MARGIN: 10px;
    COLOR: black;
    PADDING-TOP: 4px;
    font-family: Microsoft YaHei;;
    TEXT-ALIGN: left
}
.h5
{
    PADDING-RIGHT: 4px;
    PADDING-LEFT: 4px;
    FONT-SIZE: 18px;
    PADDING-BOTTOM: 4px;
    MARGIN: 10px;
    COLOR: gray;
    PADDING-TOP: 4px;
    font-family: Microsoft YaHei;;
    TEXT-ALIGN: left
}
TABLE
{
    FONT-SIZE: 12px;
    COLOR: black;
    font-family: Microsoft YaHei;
}
.t002
{
    BORDER-RIGHT: black;
    BORDER-TOP: black;
    OVERFLOW: auto;
    BORDER-LEFT: black;
    BORDER-BOTTOM: black;
    font-size:14px;
    font-family: Tahoma,Verdana,微软雅黑,新宋体;
}
.t003
{
    BORDER-RIGHT: thin;
    BORDER-TOP: thin;
    BORDER-LEFT: thin;
    BORDER-BOTTOM: thin
}
.zwmc1
{
    FONT-WEIGHT: bolder;
    FONT-SIZE: 14px;
    COLOR: black;
    font-family: Microsoft YaHei;
}
.zwz1
{
    FONT-SIZE: 14px;
    COLOR: black;
    font-family: Microsoft YaHei;
}
.scmc
{
    FONT-SIZE: 15px;
    FONT-FAMILY: 黑体
}
.scz
{
    FONT-SIZE: 15px;
    font-family: Microsoft YaHei;
}
.scjj
{
    FONT-SIZE: 17px;
    LINE-HEIGHT: 25px;
    font-family: Microsoft YaHei;
}
.tymc
{
    FONT-SIZE: 16px;
    COLOR: black;
    FONT-FAMILY: 黑体
}
.tyz
{
    BORDER-RIGHT: medium none;
    BORDER-TOP: medium none;
    FONT-SIZE: 16px;
    BORDER-LEFT: medium none;
    BORDER-BOTTOM: medium none;
    font-family: Microsoft YaHei;
}
    /* 入院记录大病历 */
    .dblLine1 {
      position: relative;
      left: 10px;
    }
    .xhx {
      /* float: right; */
      position: relative;
      left: 70px;
      top: -2px;
      width: 195px;
      border-bottom: 1px solid #cfcfcf;
    }

  .list_body{
    background: #F6F6F6;
  }
  .img_list{
    position: absolute;
    top: 50%;
    z-index: 100;
    /* margin-left: 6px; */
  }
  .none{
    display: none;
  }
  </style>
</head>

<body id = "blwsList">
  <div class="container_blwslist flex-column">
    <div class="blwslist_info">

    </div>
    <div class="blwslist_inner flex-row flex-fill">
      <div class="blwslist_inner_left">
        <div class="list">
          <!-- <div class="list_title">
            病程记录列表
            <span id="dv_wxts">红色表示上级医师未签名</span>
          </div>
          
          <div id="sidebar">

          </div> -->
        </div>
      </div>
      <div id="left_img" >
        <img id="arrow_close" class="img_list none" src="./images/arrow_close.gif" onclick="img_click('close')">
        <img id="arrow_open" class="img_list" src="./images/arrow_open.gif" onclick="img_click('open')">
      </div>
      <div class="blwslist_inner_right flex-fill">
        <div class="panel-group">
         
        </div>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面js文件 -->
  <!-- <script src="js/e-blwslist.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-blwslist.js?v=" + Date.now() + "'><\/script>");
    window.onload=function(){
      // var TopHeight=document.getElementsByClassName("titleTop")[0].offsetHeight
      var htmlHeight=document.getElementsByClassName("container_blwslist")[0].offsetHeight
      var DownHeight=document.getElementsByClassName("blwslist_inner")[0].style.height=`calc(100% - 65px)`
      // console.log("顶部高度",TopHeight,"高度",htmlHeight,"下方高度",DownHeight)
    }
  </script>
</body>
</html>