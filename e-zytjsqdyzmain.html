<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>医技检查</title>
  <meta name="description" content="医技检查">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  .container_zytjsqdyzmain {
    overflow: hidden;
    padding: 14px;
  }

  .zytjsqdyzmain_lssqd>.head {
    /* background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%); */
    background: #3D6CC8;
    border: 1px solid #3D6CC8;
    text-align: center;
    color: #fff;
  }

  .zytjsqdyzmain_lssqd>.list {
    padding: 12px;
  }

  .zytjsqdyzmain_lssqd>.list>table tr {
    line-height: 1;
  }

  .zytjsqdyzmain_lssqd>.list>table tr>td {
    padding: 10px 0;
  }
  #treeDemo{
    /* height: 450px; */
    overflow: auto;
    width: 100%;
  }
  #treeDemo .firstli{
    background: linear-gradient(180deg, #FFFFFF 0%, #D1D1D1 100%);
    border: 1px solid #9A9A9A;
    box-sizing: border-box;
    padding-left: 5px;
    cursor:context-menu;
    line-height: 22px;
    /* width: 315px; */
  }
  #treeDemo .firstli i{
    float: right;
    right: 5px;
  }
  #treeDemo .firstactive{
    background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%);
    border: 1px solid #3D6CC8;
    color: #fff;
  }
  #treeDemo .boxul{
    background: #F4F4F4;
  }
  #treeDemo .boxli{
    padding-left: 5px;
    cursor: context-menu;
    color: #333333;
  }
  #treeDemo .boxactive{
    color: #1890FF;
  }
</style>

<body>
  <div class="container_zytjsqdyzmain" class="container-fluid">
    <!-- 保存成功回调 -->
    <input type="submit" name="btn_retrieve" value="刷新" onclick="return f_jumpok();" id="btn_retrieve"
      style="display:none;" />
    <div class="flex-row">
      <div style="flex:0 0 240px;margin-right: 24px;">
        <div id="zytjsqdyzmain_list" style="height: calc(100vh - 30px);overflow-y: scroll;">
          <div class="input-group">
            <div class="input-group-addon"><i class="glyphicon glyphicon-search"></i></div>
            <input id="txtKeySearch" type="text" class="form-control" placeholder="输入关键字">
          </div>
          <div id="treeDemo" class="ztree"></div>
        </div>
      </div>
      <div class="flex-fill">
        <div class="flex-row" style="border:1px solid #C6C6C6;">
          <div class="flex-fill">
            <div id="zytjsqdyzmain_details"
              style="height: calc(100vh - 30px);">
              <iframe id="tjsqdmain" name="tjsqdmain" scrolling="yes" frameborder="0" height="100%"
                width="100%"></iframe>
            </div>
          </div>
          <div style="flex:0 0 420px;">
            <div id="zytjsqdyzmain_lssqd"
              style="height: calc(100vh - 30px);overflow-y: scroll;border-left:1px solid #C6C6C6;">
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 树状控件引用-->
  <!-- <link rel="stylesheet" href="./lib/zTree/css/zTreeStyle.css" type="text/css">
  <script type="text/javascript" src="./lib/zTree/js/jquery.ztree.core.js"></script> -->
  <!-- 登录页js文件 -->
  <!-- <script src="js/e-zytjsqdyzmain.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-zytjsqdyzmain.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>

</html>