# ProgressNotes.vue ID更新解决方案

## 问题描述

新增记录（ID以't'开头的临时ID）保存成功后，记录仍然保持临时ID，但实际上应该更新为服务器返回的真实ID。这导致后续操作（如编辑、删除）使用错误的ID。

## 解决方案

### 1. 修改iframe通信模块 (`src/utils/iframe-communication.js`)

**修改内容：**
- 更新 `handleMessage` 方法，支持返回包含 `newRecordId` 的完整响应对象
- 修改返回格式从字符串改为对象：`{message, data, newRecordId}`

**关键代码：**
```javascript
// 处理结果
if (data.success) {
  // 返回完整的响应数据，而不仅仅是消息
  pendingMessage.resolve({
    message: data.message,
    data: data.data || null,
    newRecordId: data.newRecordId || null
  })
} else {
  pendingMessage.reject(new Error(data.message))
}
```

### 2. 修改保存逻辑 (`handleSaveClick` 方法)

**修改内容：**
- 检测是否为新增记录（临时ID）
- 兼容新旧返回格式
- 当获取到新记录ID时，调用 `updateRecordId` 方法更新ID
- 使用 `refreshYiShuXieWenShuList(record)` 而不是 `fetchInitData()`

**关键代码：**
```javascript
// 检查是否为新增记录（临时ID）
const isNewRecord = recordId.toString().startsWith('t')
const originalRecordId = recordId

// 兼容旧版本返回格式（字符串）和新版本返回格式（对象）
const message = typeof result === 'string' ? result : result.message
const newRecordId = typeof result === 'object' ? result.newRecordId : null

// 如果是新增记录且获取到了新的记录ID，需要更新记录ID
if (isNewRecord && newRecordId) {
  console.log(`更新记录ID: ${originalRecordId} -> ${newRecordId}`)
  await this.updateRecordId(originalRecordId, newRecordId, record)
}
```

### 3. 新增ID更新方法 (`updateRecordId`)

**功能：**
- 更新记录对象的ID
- 更新DOM元素ID（iframe、面板）
- 重新生成编辑URL
- 强制更新iframe的key以触发重新渲染

**关键代码：**
```javascript
async updateRecordId(oldRecordId, newRecordId, record) {
  // 1. 更新记录对象的ID
  record.id = newRecordId
  
  // 2. 更新recordDetailList中的记录
  const recordIndex = this.recordDetailList.findIndex(item => item.id === oldRecordId)
  if (recordIndex !== -1) {
    this.recordDetailList[recordIndex].id = newRecordId
  }
  
  // 3. 更新DOM元素ID
  this.$nextTick(() => {
    // 更新iframe的ID
    const oldIframe = document.getElementById(`iframe_${oldRecordId}`)
    if (oldIframe) {
      oldIframe.id = `iframe_${newRecordId}`
    }
    
    // 更新面板的ID
    const oldPanel = document.getElementById(`panel_${oldRecordId}`)
    if (oldPanel) {
      oldPanel.id = `panel_${newRecordId}`
    }
  })
  
  // 4. 更新记录的URL
  record.url = this.getRecordEditUrl({
    ...record,
    id: newRecordId
  })
  
  // 5. 强制更新iframe的key以触发重新渲染
  record.urlVersion = (record.urlVersion || 0) + 1
}
```

### 4. 改进刷新逻辑 (`refreshYiShuXieWenShuList`)

**修改内容：**
- 保存当前记录的展开状态和编辑状态
- 智能判断刚保存的记录，当预览URL为空时继续使用编辑URL
- 恢复之前的状态设置

### 5. 增强iframe处理

**新增功能：**
- `handleIframeError`: 处理iframe加载错误
- `retryLoadRecord`: 提供重试加载机制
- 改进的 `handleIframeLoad`: 检测加载问题并自动修复

**模板更新：**
```html
<iframe
  v-if="record.url && record.url.trim() !== ''"
  :id="'iframe_' + record.id"
  :key="record.id + '_' + (record.urlVersion || 0)"
  :src="record.url"
  frameborder="0"
  @load="handleIframeLoad($event, record.id)"
  @error="handleIframeError($event, record.id)"
></iframe>
```

## 测试步骤

### 1. 基本功能测试
1. 新增一条记录
2. 填写内容并保存
3. 检查控制台日志，确认ID更新过程
4. 验证保存后iframe正常显示内容
5. 尝试再次编辑该记录，确认使用正确的ID

### 2. 错误恢复测试
1. 新增记录保存后，如果iframe无法加载
2. 点击"重试加载"按钮
3. 验证重试机制是否有效

### 3. 兼容性测试
1. 测试旧版本iframe通信（返回字符串）
2. 测试新版本iframe通信（返回对象）
3. 确保两种情况都能正常工作

## 注意事项

1. **iframe通信协议**：需要确保iframe内的页面支持返回 `newRecordId` 字段
2. **ID格式**：临时ID必须以't'开头，真实ID应该是数字或其他格式
3. **时序控制**：ID更新在保存成功后立即执行，刷新在1秒后执行
4. **错误处理**：如果ID更新失败，会显示警告但不影响保存结果

## 预期效果

1. 新增记录保存后，ID正确更新为服务器返回的真实ID
2. iframe能够正常重新加载并显示内容
3. 后续的编辑、删除操作使用正确的ID
4. 提供完善的错误恢复机制
5. 保持良好的用户体验，状态保持和自动重试
