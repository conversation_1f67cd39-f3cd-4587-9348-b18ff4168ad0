# 流水线发布机制

## dev分支
<p>代码提交到origin/dev，自动触发流水线</p>
<p>自动执行install,build指令，制作容器镜像并上传（$IMAGE_ADDRESS:$IMAGE_VERSION_TEST.test）</p>
<p>最终在打包成功以后自动执行kubectl脚本更新（强制替换）对应的dev容器镜像</p>

## pre分支对应不同需求的预生产环境
<p>代码提交到origin/pre，自动触发流水线</p>
<p>自动执行install,build指令，制作容器镜像容器名并上传（$IMAGE_ADDRESS:$IMAGE_VERSION_PRE.pre）</p>
<p>最终在打包成功以后自动执行kubectl脚本更新（强制替换）对应的pre容器镜像</p>

## master分支对应
<p>合并pre分支到master分支，对将要发布生产环境的commit打tag</p>
<p>将合并与tags提交到origin/master，自动触发流水线（只有tags才会触发生产环境流水线）</p>
<p>自动执行install,build指令，制作容器镜像并上传</p>
<p>最终在打包成功以后自动执行kubectl脚本更新（apply）对应的prod容器镜像</p>
