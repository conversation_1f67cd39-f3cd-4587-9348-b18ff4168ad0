<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>CRRT医嘱记录单</title>
	<meta name="description" content="CRRT医嘱记录单">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
	<link rel="icon" href="favicon.ico" type="image/x-icon">
	<!-- 公用模块CSS -->
	<!-- <link rel="stylesheet" href="css/e-common.css"> -->
	<script type="text/javascript">
		document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
	</script>
	<!-- cryptojs加密插件 -->
	<!-- <script src='https://cdnjs.cloudflare.com/ajax/libs/crypto-js/3.1.9-1/crypto-js.js'></script> -->
	<script src='./lib/cryptoJs/crypto-js.js'></script>
	<!-- jsencrypt加密插件 -->
	<!-- <script src='https://cdnjs.cloudflare.com/ajax/libs/jsencrypt/3.0.0-beta.1/jsencrypt.min.js'></script> -->
	<script src='./lib/jsencrypt/jsencrypt.min.js'></script>
	<!-- 页面CSS -->
	<!-- <link rel="stylesheet" href="css/e-style.css"> -->
	<script type="text/javascript">
		document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
	</script>
</head>
<style>
	#CRRTyzjld {
		position: relative;
		width: 90%;
		margin: auto;
	}

	/* 血液净化医嘱 */
	.CRRTyz {
		position: relative;
		width: 100%;
		min-height: 40px;
	}

	/* 患者基本信息 */
	.Ltitle {
		position: relative;
		padding: 0 16px;
		font-size: 20px;
		border-left: 3px solid #155bd4;
		height: 34px;
		line-height: 34px;
	}

	.backBtn {
		position: relative;
		float: right;
		right: 0;
	}
	.backBtn[disabled] {
		cursor: default;
		position: relative;
    display: inline-block;
    font-weight: 400;
    white-space: nowrap;
    text-align: center;
    box-shadow: 0 2px 0 rgb(0 0 0 / 2%);
    cursor: pointer;
    transition: all .3s cubic-bezier(.645, .045, .355, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    touch-action: manipulation;
    height: 32px;
    padding: 0 15px;
    font-size: 14px;
	}

	.LInfo {
		position: relative;
		display: flex;
		width: calc(100% - 20px);
		margin: auto;
		justify-content: flex-start;
		align-items: center;
		padding: 10px;
	}

	.infoCondition {
		position: relative;
		/* width:100%; */
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		align-items: baseline;
	}

	.xyjhYzTb {
		position: relative;
		width: 100%;
		margin: 0 auto;
		padding: 25px 0;
	}

	.overall_table_frame {
		position: relative;
		width: calc(100%);
		border: 1px solid #B7C3DA;
		margin: 0 auto;
		/* margin-bottom: 10px; */
	}

	.overall_table_frame>table {
		position: relative;
		width: 100%;
	}

	#crrt_table {
		position: relative;
		border: 1px solid #B7C3DA;
	}

	.row_head>th {
		background: #85A2DB;
		/* background: #f4f6fe; */
		/* color: #FFFFFF; */
		color: #000;
		text-align: center;
		font-family: Microsoft YaHei;
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 30px;
		letter-spacing: 0em;
	}

	.row_nr>td {
		text-align: center;
		font-family: Microsoft YaHei;
		color: #000;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 26px;
		letter-spacing: 0em;
		padding: 4px 8px;
	}

	.CRRTcfd {
		position: relative;

	}

	.CFDForm {
		position: relative;

	}

	.formSort {
		position: relative;
		padding: 4px 16px;
	}

	.LFormTitle {
		position: relative;
		padding: 0 16px;
		font-size: 18px;
		border-left: 3px solid #155bd4;
		/* height: 40px; */
		line-height: 30px;
		display: flex;
		align-items: center;
		color: #000;
		height: auto;
	}


	.changeRadio {
		position: relative;
		display: flex;
		align-items: center;
		/* justify-content: space-around; */
		justify-content: flex-start;
		/* width: calc(100vw - 165px); */
		/* font-family: Microsoft YaHei; */
		color: #000;
		flex-direction: row;
    flex-wrap: wrap;
	}

	.CFDForm .checkbox-inline+.checkbox-inline,
	.radio-inline+.radio-inline {
		margin-left: 0px;
	}

	.radio-inline {
		display: flex;
		/* padding-left: 15px; */
		/* align-items: center; */
		align-items: baseline;
		padding-top: 2px;
    padding-bottom: 2px;
	}

	.radio-inline1 {
		font-weight: 400;
		vertical-align: middle;
		cursor: pointer;
		padding-top: 2px;
    padding-bottom: 2px;
	}

	.changecheck {
		position: relative;
		display: flex;
	}

	.sTitle {
		font-size: 16px;
		/* padding: 3px 0 3px 0; */
		padding: 6px 0;
		color: #000;
	}

	.sTitle>span:first-child {
		font-weight: bold;
		width: 120px !important;
	}

	.sTitle1 {
		padding: 0 0 20px 0;
		color: #000;
	}

	.sTitle1>span:first-child {
		font-weight: bold;
		width: 76px !important;
		font-size: 14px;
	}

	/* 输入框 text */
	.allInput {
		border-radius: 4px;
		color: rgba(0, 0, 0, .65);
		background-color: #fff;
		border: 1px solid #d9d9d9;
	}

	.iTabel {
		padding: 0 10px 0 5px;
	}

	.iTabel1 {
		padding: 0 18px 0 5px;
	}

	.form-inline {
		position: relative;
		padding-left: 20px;
		font-weight: 400;

		display: flex;
		flex-direction: row;
		justify-content: flex-start;
	}

	.form-inline>span:first-child {
		width: 100px;
	}

	.form-inline>span:last-child {
		position: relative;
		left: 10px;
	}

	.CFDTB {
		position: relative;
		/* padding: 10px 0; */
		/* width: calc(100% - 40px); */
		margin: 0 auto;
		padding: 0 0 20px 0;
	}

	.allBtn {
		position: relative;
		padding: 10px 0;
		width: calc(100%);
		margin: 0 auto;
	}

	/* 首列固定/最后一列固定*/
	.crrt_tableCFD th:last-child,
	.crrt_tableCFD td:last-child {
		position: sticky;
		left: 0;
		text-align: center;
		right: 0px;
		border-left: 1px solid #fff;
		width: 400px !important;
		background: #F4F4F5;
	}

	/* 表头首列和最后一列强制最顶层 */
	.crrt_tableCFD th:last-child {
		z-index: 3;
		/*左上角单元格z-index，切记要设置，不然表格纵向横向滚动时会被该单元格右方或者下方的单元格遮挡*/
		background: #F4F4F5;
	}
	.xgjyxmTb {
		position: relative;
		text-align: center;
		margin: 0 auto;
    /* background: #f4f4f4; */
    border-radius: 8px;
		padding: 0 0 50px 0;
	}
  .show_Table {
    width: 100%;
    margin: auto;
    border: 1px solid #767676;
  }
  .show_Table .table-nr{
    padding: 2px 5px;
  }
</style>

<body style="padding:10px">
	<div id="CRRTyzjld">
		<h3 style="text-align: center;">血液净化处方单</h3>
		<hr>
		<!-- 血液净化医嘱 -->
		<div class="CRRTyz">
			<!-- 基本信息 -->
			<div class="brBasicInfo">
				<div class="Ltitle label-name">
					<span style="font-weight: bold;">患者基本信息</span>
					<span style="position: relative; float: right;">
						<button class="e_btn addBtn" onclick="addCRRTcfd()">新增</button>
					</span>
				</div>
				<div class="LInfo"> </div>
			</div>
			<!-- 血液净化医嘱 表格 -->
			<div class="CRRTYzTb"> </div>
		</div>
		<!-- 血液净化处方单 -->
		<div class="CRRTcfd">
			<!-- 填写表单 -->
			<div class="CFDForm"> </div>
			<!-- 按钮 + 展示表格 -->
			<div class="CFDTB">

			</div>
		</div>
		<!-- 相关检验项目 -->
		<div class="xgjyxmTb">

		</div>
		<!-- <table id="crrt_table">
			<thead>
				<tr>
					<th>日期时间</th>
					<th>
						<div>抗凝方案</div>
						<div><span>首剂</span><span>维剂</span></div>
					</th>
					<th>
						<div>CRRT剂量</div>
						<div><span>血流速</span><span>目标越速</span><span>PBP</span><span>前稀释比例</span><span>PBP</span></div>
					</th>
				</tr>
			</thead>
		</table> -->
	</div>
	<!-- 配置文件 -->
	<script src="./e-config.js"></script>
	<!-- 公用模块JS -->
	<script type="text/javascript">
		document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
		document.write("<script type='text/javascript' src='js/e-interface.CrrtYzjld.js?v=" + Date.now() + "'><\/script>");
	</script>
	<!-- 临床营养中心会诊js文件 -->
	<script type="text/javascript">
		document.write("<script type='text/javascript' src='js/e-CrrtYzjld.js?v=" + Date.now() + "'><\/script>");
	</script>

</body>

</html>