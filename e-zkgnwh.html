<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>专科功能维护</title>
	<meta name="description" content="专科功能维护">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
	<link rel="icon" href="favicon.ico" type="image/x-icon">
	<!-- 公用模块CSS -->
	<!-- <link rel="stylesheet" href="css/e-common.css"> -->
	<script type="text/javascript">
		document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
	</script>
	<!-- 页面CSS -->
	<!-- <link rel="stylesheet" href="css/e-style.css"> -->
	<script type="text/javascript">
		document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
	</script>
</head>
<style>
	#zkgnwh {
		position: relative;
		font-size: 16px;
		padding: 10px 5px 10px;
		width: 80%;
		margin: 0 auto;
		/* border: 1px solid #000; */
	}

	#bottomShow {
		position: relative;
		width: 100%;
		display: flex;
    flex-wrap: nowrap;
		margin-top: 25px;
		/* height: 100vh; */
	}
	.showLeft {
		position: relative;
		/* width: 20%; */
		min-width: 230px;
		/* height: 50vh; */
		min-height: 80vh;
		border-right: 1px solid #ccc;
	}
	.showRight {
		position: relative;
		width: 80%;

		min-height: 80vh;
		/* border: 1px solid rgb(68, 73, 125); */
	}
  .Llist{
    padding-left: 20px;
    height: 800px;
    overflow: auto;
  }
	.Ltitle, .Rtitle {
		position: relative;
		padding: 0 16px;
		font-size: 18px;
    border-left: 3px solid #155bd4;
    height: 18px;
    line-height: 14px;
	}
	.Rtitle {
		margin: 0 20px;
	}

	.Rtable{
		position: relative;
		width: 100%;
		margin: 0 20px;
    padding: 20px 0;
	}
  .Ltable_td{
    cursor:pointer;
    padding: 1px 5px 0px 5px;
  }
	.Ltable_td_active{
    cursor:pointer;
    padding: 1px 5px 0px 5px;
    background: #bcc9de;
  }
  .edit_btn{
    cursor: pointer;
  }
	.right_table_tr{
		height: 35px;
	}
	.text_input{
		border: 1px solid #000;
    padding: 1px;
    width: 190px;
	}
</style>

<body style="padding:10px">
	<div id="zkgnwh">
		<div id="bottomShow">
			<!-- 左 -->
			<div class="showLeft">
				<div class="Ltitle label-name">
					专科列表
				</div>
				<div class="Llist">
          <table id="left_lists" style="width: 100%">
            
          </table>
        </div>
			</div>
			<!-- 右 -->
			<div class="showRight">
				<div class="RTop">
					<div class="Rtitle label-name">
						专科功能维护
					</div>
				</div>
				<div class="Rtable">
          <table border="1" style="text-align: center">
            <thead>
              <tr style="height: 40px;color: #000;background: #b0c4de;">
                <th width="100" style="text-align: center">部门属性</th>
                <th width="100" style="text-align: center">属性值</th>
								<th width="200" style="text-align: center">属性备注</th>
                <th width="100" style="text-align: center">状态标志</th>
                <th width="100" style="text-align: center">操作者</th>
                <th width="170" style="text-align: center">修改时间</th>
                <th width="100" style="text-align: center">操作<button class="e_btn" onclick="add()">新增</button></th>
              </tr>
            </thead>
            <tbody id="right_lists">
            </tbody>
          </table>
				</div>
			</div>
		</div>
	</div>
	<!-- 配置文件 -->
	<script src="./e-config.js"></script>
  <!-- <script src="./lib/echarts/echarts.min.js"></script>
  <script src="./lib/charts/echarts.js"></script> -->
	<!-- 公用模块JS -->
	<!-- <script src="js/e-common.js"></script> -->
	<script type="text/javascript">
		document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
      document.write("<script type='text/javascript' src='js/e-interface-zkgnwh.js?v=" + Date.now() + "'><\/script>");
	</script>
	<!-- 临床营养中心会诊js文件 -->
	<script type="text/javascript">
		document.write("<script type='text/javascript' src='js/e-zkgnwh.js?v=" + Date.now() + "'><\/script>");
	</script>
  
</body>

</html>