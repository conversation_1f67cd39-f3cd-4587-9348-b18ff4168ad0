body,
div,
span,
p,
h1,
h2,
h3,
h4,
h5,
h6,
img,
del,
em,
strong,
b,
i,
dl,
dt,
dd,
ol,
ul,
li,
fieldset,
form,
label,
legend,
table,
caption,
tbody,
tfoot,
thead,
tr,
th,
td,
article,
aside,
canvas,
footer,
header,
nav,
section,
audio,
video,
textarea,
select,
a,
button,
hr {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

article,
aside,
footer,
header,
nav,
section {
  display: block;
}

ol,
ul,
li,
dl,
dt,
dd {
  list-style: none;
}

h1,
h2,
h3,
h4,
h5,
h6,
b,
strong {
  font-weight: normal;
}

html,
body {
  height: 100%;
}

body {
  color: #333;
  font-size: 14px;
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

legend {
  color: #333;
}

table {
  border-collapse: collapse;
  border-spacing: 0;
}

img {
  border: none;
}

a {
  color: inherit;
  text-decoration: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  background-color: transparent;
  cursor: pointer;
}
a:hover,
a:active,
a:focus {
  outline: none;
}

input,
button,
select,
option,
textarea {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  outline: none;
  border: none;
  vertical-align: middle;
  border-radius: 0;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
textarea {
  resize: none;
}
button,
label {
  cursor: pointer;
}
select::-ms-expand {
  display: none;
}

em,
i {
  font-style: normal;
  display: inline-block;
}

// 清除input[number]类型右边上下箭头
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
}
// 清除input[number]类型右边上下箭头，兼容火狐浏览器
input[type='number'] {
  -moz-appearance: textfield;
}

// 单行溢出隐藏
.one-txt-cut {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

// 两行溢出隐藏
.two-txt-cut {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

// 隐藏滚动条
.hideBar::-webkit-scrollbar {
  display: none;
}
