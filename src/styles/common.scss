// $yuYueList: (
//   ("label": "xx",
//     "value": "1",
//     "color": #ff4d4f,
//   )
// );

// .yyzt {
//   display: flex;
//   justify-content: flex-end;

//   @each $item in $yuYueList {
//     $disabled: #{map-get($item, "disabled")};
//     $color: #{map-get($item, "color")};
//     $value: #{map-get($item, "value")};

//     &-legend {
//       &-#{$value} {

//         @if $disabled =="true" {
//           display: none;
//         }

//         @else {
//           display: inline-flex;
//         }

//         align-items: center;
//         $font-size: calc(var(--font-size-regular) - 2px);
//         line-height: $font-size;
//         font-size: $font-size;
//         color: var(--form-label-text-color);
//         margin-right: calc(var(--common-margin) * 1.2);

//         &:after {
//           content: '#{map-get($item, "label")}';
//           display: inline-block;
//         }

//         &:before {
//           content: "";
//           width: 10px;
//           height: 10px;
//           display: inline-block;
//           background-color: $color;
//           margin-right: 4px;
//         }
//       }
//     }


//     &-#{$value} {
//       color: $color;
//     }

//     &-row {
//       &-#{$value} {
//         td {
//           $round-size: 16px;

//           &:first-child {
//             .cell {
//               vertical-align: middle;
//               padding: 0;
//               width: $round-size;
//               height: $round-size;
//               line-height: $round-size;
//               border-radius: $round-size;
//               background-color: #7884FF;
//               display: inline-block;
//               color: #fff;
//               font-size: $round-size - 4;
//             }
//           }

//           @if $value =='DRFZ' or $value =='WKYY' {
//             &:first-child {
//               .cell {
//                 background-color: $color !important;
//               }
//             }

//           }

//           @else {
//             .cell {
//               color: $color;
//             }
//           }

//         }

//       }
//     }
//   }
// }
