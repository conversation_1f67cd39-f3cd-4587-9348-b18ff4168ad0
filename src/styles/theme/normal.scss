/* 公共样式参数
-------------------------- */
// 元素之间公共间距
$--common-margin: 8px !default;
// 元素公共内边距
$--common-padding: 8px !default;

// 表单名称与值之间的间距
$--label-value-margin: 6px !default;
// 主色调
$--color-primary: #356AC5 !default;
// 成功颜色
$--color-success: #66BE74 !default;
/// 警告颜色
$--color-warning: #ED6A0C !default;
// 错误颜色
$--color-danger: #D40000 !default;
/// 通知颜色
$--color-info: #909399 !default;
// 色彩-中性色
$--color-text-primary: #171C28 !default;
//色彩-中性色85%
$--color-text-regular: rgb(23 28 40 / 85%) !default;
//色彩-中性色65%
$--color-text-secondary: rgb(23 28 40 / 65%) !default;
//色彩-中性色45%
$--color-text-placeholder: rgb(23 28 40 / 45%) !default;
//色彩-背景色
$--background-color-base: #FAFBFC !default;
// 基础字体颜色
$--font-color-base: #171C28;
// disabled 颜色
$--disabled-color: rgba($--font-color-base, 0.05) !default;
/// placeholder 字体颜色
$--color-text-placeholder: rgba($--font-color-base, 0.45) !default;
// 正文字体颜色
$--font-color-regular: rgba($--font-color-base, 0.85) !default;
// 正文字体大小
$--font-size-regular: 14px !default;
// 正文字体
$--font-family-regular: sourcehansanscn-regular,
sourcehansanscn !default;
// 控件内部字体颜色
$--font-color-inner: #fff;
// 正文字体粗细
$--font-weight-regular: 400 !default;
// 加粗字体颜色
$--font-color-medium: rgb(0 0 0 / 85%) !default;
// 加粗字体大小
$--font-size-medium: 16px !default;
// 加粗字体
$--font-family-medium: sourcehansanscn-medium,
sourcehansanscn !default;
// 加粗字体粗细
$--font-weight-medium: 500 !default;
// 默认边框宽度
$--border-width-base: 1px !default;
// 默认边框颜色
$--border-color-base: #DADEE5 !default;
//layout布局-圆角弧度4px
$--border-radius-base: 4px !default;
// layout布局-圆角弧度2px
$--border-radius-small: 2px !default; //layout布局-圆角弧度2px

// 表头背景色
$--table-header-background-color: rgb(46 85 237 / 5%) !default;
// 表头文本颜色
$--table-header-font-color: $--color-text-primary !default;
// 表格当前行颜色
$--table-current-row-background-color: rgb(89 176 255 / 45%) !default;
// 表格当前行文本颜色
$--table-current-row-background-color: $--color-primary !default;
// 表格单元格上下内边距
$--table-cell-padding-vertical: 8px;
// 表格单元格左右内边距
$--table-cell-padding-horizontal: 10px;
// 表格斑马纹颜色
$--table-stripe-color: #EFF3FB;

// 表单label字体颜色
$--form-label-text-color: rgba($--font-color-base, 0.65) !default;
// 表单输入元素宽度
$--form-item-content-width: 262px !default;
// 表单输入框高度
$--form-item-content-height: 32px !default;
// 小宽度弹窗
$--dialog-width-mini: 350px !default;
// 小宽度弹窗
$--dialog-width-small: 480px !default;
// 中等宽度弹窗
$--dialog-width-medium: 850px !default;
// 大宽度弹窗
$--dialog-width-large: 1000px !default;

// 内容区域固定高度
$--content-header-height: 179px !default;

// select聚集边框颜色
$--select-border-color-hover: $--color-primary !default;
// select禁用边框颜色
$--select-disabled-border: $--border-color-base !default;
// select输入框字体大小
$--select-input-font-size: $--font-size-regular !default;
// select-option禁用字体颜色
$--select-option-disabled-color: $--color-text-secondary !default;
// select-option禁用背景颜色
$--select-option-disabled-background: rgb(23 28 40 / 5%) !default;

//input文本颜色
$--input-font-color: $--font-color-base !default;
// input输入框高度
$--input-height: 32px !default;
// input输入框聚焦边框颜色
$--input-border-color-hover: $--color-primary !default;
// input输入框背景颜色
$--input-background-color: #FFF !default;
// input输入框最大宽度
$--input-max-width: 284px !default;
// input输入框中等高度
$--input-medium-height: 32px !default;
// input较小输入框文本大小
$--input-small-font-size: 14px !default;
// input较小输入框高度
$--input-small-height: 32px !default;
// input更小输入框文本大小
$--input-mini-font-size: 14px !default;
// input更小输入框高度
$--input-mini-height: 32px !default;

// card边框圆角大小
$--card-border-radius: 2px !default;

// checkbox文字粗细
$--checkbox-font-weight: 400 !default;
// checkbox文本颜色
$--checkbox-font-color: $--color-text-primary !default;
// checkbox禁用边框颜色
$--checkbox-disabled-border-color: rgb(23 28 40 / 45%) !default;

// radio文字粗细
$--radio-font-weight: 400 !default;
// radio文本颜色
$--radio-font-color: $--color-text-primary !default;

// tree悬浮背景颜色
$--tree-node-hover-background-color: #EEF1FE !default;
// tree文本颜色
$--tree-font-color: $--color-text-primary !default;
// tree图标颜色
$--tree-expand-icon-color: #8590B3 !default;

// datepicker文本颜色
$--datepicker-font-color: $--color-text-primary !default;
// datepicker非本月文本颜色
$--datepicker-off-font-color: rgb(23 28 40 / 45%) !default;
// datepicker头部文本颜色
$--datepicker-header-font-color: $--color-text-primary !default;
// datepicker日期范围背景颜色
$--datepicker-inrange-background-color: rgb(21 91 212 / 10%) !default;
// datepicker日期范围聚焦背景颜色
$--datepicker-inrange-hover-background-color: rgb(21 91 212 / 10%) !default;

// cascader文本颜色
$--cascader-menu-font-color: $--color-text-primary !default;
// cascader选中背景颜色 ++
$--cascader-menu-selected-background-color: $--color-primary !default;
// cascader聚焦背景颜色
$--cascader-node-background-hover: inline !default;

// tooltip背景颜色
$--tooltip-fill: #000 !default;
// tooltip文本大小
$--tooltip-font-size: 14px !default;
// tooltip内边距
$--tooltip-padding: 9px 12px !default;

//tab文本颜色
$--tab-text-color: $--color-text-placeholder !default;

// pagination分页器背景颜色
$--pagination-background-color: #F0F1F4 !default;
// pagination分页器文本大小
$--pagination-font-size: 14px !default;
// pagination分页器单个宽度
$--pagination-button-width: 30px !default;
// pagination分页器单个高度
$--pagination-button-height: 30px !default;

// messagebox内边距
$--msgbox-padding-primary: 16px !default;
// messagebox正文字体颜色
$--messagebox-content-color: $--color-text-placeholder !default;

// message关闭图标颜色
$--message-close-icon-color: #000 !default;
// message关闭图标大小
$--message-close-size: 12px !default;

@font-face {
  font-family: SourceHanSansCN-Medium;
  src: url("~@/assets/custom-theme/fonts/SourceHanSansCN-Medium.otf");
}

@font-face {
  font-family: SourceHanSansCN-Regular;
  src: url("~@/assets/custom-theme/fonts/SourceHanSansCN-Regular.otf");
}

@font-face {
  font-family: SourceHanSansCN-Bold;
  src: url("~@/assets/custom-theme/fonts/SourceHanSansCN-Bold.otf");
}

@font-face {
  font-family: SourceHanSansCN-Heavy;
  src: url("~@/assets/custom-theme/fonts/SourceHanSansCN-Heavy.otf");
}

@import "../element-variable";
@import "../index";
