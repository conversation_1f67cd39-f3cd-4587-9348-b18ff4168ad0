.app__night {
    .side-container {
        background: $menuBgNight;

        .el-scrollbar {
            .el-menu {
                background: $menuBgNight;

                .el-menu-item {
                    .menu-item-title {
                        color: $menuTextNight;
                    }

                    &.is-active {
                        background: $menuActiveNight;

                        &::before {
                            background: radial-gradient(circle at 0% 0%,
                                    rgb(255 255 255 / 0%) 0%,
                                    rgb(255 255 255 / 0%) 70%,
                                    $menuActiveNight 71%,
                                    $menuActiveNight 0%);
                        }

                        &::after {
                            background: radial-gradient(circle at 0% 100%,
                                    rgb(255 255 255 / 0%) 0%,
                                    rgb(255 255 255 / 0%) 70%,
                                    $menuActiveNight 71%,
                                    $menuActiveNight 0%);
                        }

                        .menu-item-title {
                            color: $menuActiveTextNight;
                        }
                    }

                    &:hover:not(.is-active) {
                        background: $menuHoverNight;

                        // &::before {
                        //   background: radial-gradient(circle at 0% 0%,
                        //       rgb(255 255 255 / 0%) 0%,
                        //       rgb(255 255 255 / 0%) 70%,
                        //       $menuHoverNight 71%,
                        //       $menuHoverNight 0%);
                        // }

                        // &::after {
                        //   background: radial-gradient(circle at 0% 100%,
                        //       rgb(255 255 255 / 0%) 0%,
                        //       rgb(255 255 255 / 0%) 70%,
                        //       $menuHoverNight 71%,
                        //       $menuHoverNight 0%);
                        // }
                    }
                }

                .el-submenu {
                    background: $menuBgNight;

                    .el-submenu__title {
                        .menu-item-title {
                            color: $menuTextNight;
                        }

                        &:hover {
                            background: $subMenuHoverNight;

                            // &::before {
                            //   background: radial-gradient(circle at 0% 0%,
                            //       rgb(255 255 255 / 0%) 0%,
                            //       rgb(255 255 255 / 0%) 70%,
                            //       $subMenuHoverNight 71%,
                            //       $subMenuHoverNight 0%);
                            // }

                            // &::after {
                            //   background: radial-gradient(circle at 0% 100%,
                            //       rgb(255 255 255 / 0%) 0%,
                            //       rgb(255 255 255 / 0%) 70%,
                            //       $subMenuHoverNight 71%,
                            //       $subMenuHoverNight 0%);
                            // }
                        }
                    }

                    .el-menu {
                        background: $subMenuBgNight;
                    }

                    &.is-active:not(.is-opened) {
                        .el-submenu__title {
                            background: $menuActiveNight;

                            .menu-item-title {
                                color: $menuActiveTextNight;
                            }

                            &::before {
                                background: radial-gradient(circle at 0% 0%,
                                        rgb(255 255 255 / 0%) 0%,
                                        rgb(255 255 255 / 0%) 70%,
                                        $menuActiveNight 71%,
                                        $menuActiveNight 0%);
                            }

                            &::after {
                                background: radial-gradient(circle at 0% 100%,
                                        rgb(255 255 255 / 0%) 0%,
                                        rgb(255 255 255 / 0%) 70%,
                                        $menuActiveNight 71%,
                                        $menuActiveNight 0%);
                            }
                        }
                    }
                }
            }
        }
    }
}