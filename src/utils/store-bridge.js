/**
 * <AUTHOR>
 * @Date 2024-02-27
 * @LastEditors hybtalented
 * @LastEditTime 2024-02-28
 * @FilePath /src/utils/store-bridge.js
 * @Description store 调用个中转
 */
/**
 * @typedef {{  eventType: typeof EventType, type: 'action', action: string; payload: any}} ActionMessage
 * @typedef {{  eventType: typeof EventType, type: 'mutation', mutation: string; payload: any}} MutationMessage
 * @typedef {ActionMessage | MutationMessage} EventMessage
 */

import store from '@/store'
const EventType = '__wyyy_store_event__'

/**
 * @param {MessageEvent<EventMessage>} event 时间
 */
function handleEvent(event) {
  const data = event.data
  if (data.eventType === EventType) {
    switch (data.type) {
      case 'action':
        store.dispatch(data.action, data.payload)
        break
      case 'mutation':
        store.dispatch(data.mutation, data.payload)
        break
    }
  }
}
window.addEventListener('message', handleEvent, false)

/**
 * 发送 store action 事件
 * @param {Window} target
 * @param {string} action
 * @param {any} payload
 */
export function dispatchActionEvent(target, action, payload) {
  /**
   * @type {ActionMessage}
   */
  const message = {
    eventType: EventType,
    type: 'action',
    action,
    payload
  }
  target.postMessage(message, '*')
}
/**
 * 发送 store mutation 事件
 * @param {Window} target 目标窗口
 * @param {string} mutation
 * @param {unknown} payload
 */
export function dispatchMutationEvent(target, mutation, payload) {
  /**
   * @type {MutationMessage} 消息
   */
  const message = {
    eventType: EventType,
    type: 'mutation',
    mutation,
    payload
  }
  target.postMessage(message, '*')
}
