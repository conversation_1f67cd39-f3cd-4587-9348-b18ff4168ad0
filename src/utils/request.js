import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getRefreshToken, getToken, getUUID } from '@/utils/auth'
import { v4 as uuidv4 } from 'uuid'
import { getDefaultState } from '@/store/modules/user'
import { refreshToken } from '@/api/user'
import { hideLoading, showLoading } from '@/utils/loading'

let isShowNetError = false
let tokenRefreshing = false
let cacheRequestArr = []

const cacheRequestArrHandle = (cb) => {
  cacheRequestArr.push(cb)
}
const reRequest = (token) => {
  cacheRequestArr.map((cb) => cb(token))
  cacheRequestArr = []
}

// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 15000 // request timeout
})

const noNeedLoadingList = [
  {
    url: '/sysmanage/v1/appsystem/getAppcodes',
    name: '获取全部应用信息'
  }
]

// 接口领域限制
const verifyAppFuc = ({ headers, url }) => {
  console.log('go to')
  if (!headers.hasOwnProperty('verifyApp') || headers['verifyApp'] === true) {
    // 进行path校验,端口后的路径必须是app-开头,
    const pathArr = url.replace(/^(https?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+\/))?)/, '').split('/')
    if (
      !(pathArr[0] === '' ? pathArr?.[1] : pathArr?.[0])?.includes('app-') &&
      !store?.state?.app?.noNeedVerifyAppList.some((sI) => url.includes(sI))
    ) {
      return true
    }
  }
  return false
}

// request interceptor
service.interceptors.request.use(
  (config) => {
    if (config.globalLoading !== false) {
      showLoading(config?.loading)
    }
    console.log('请求的url:', config.url)
    config.headers['token'] = getToken()
    config.headers['requestID'] = uuidv4()
    config.headers['yingYongDM'] = getDefaultState().systemID
    if (config.data?.['ms-sign']) {
      config.headers['ms-sign'] = config.data['ms-sign']
    }
    if (verifyAppFuc(config)) {
      config.cancelToken = true
      return Promise.reject(new Error('领域层接口禁止访问，请调用应用层接口,url:' + config.url))
    }
    return config
  },
  (error) => {
    hideLoading()
    // do something with request error
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  async (response) => {
    console.log('接口返回:response', response)
    if (response.config.globalLoading !== false) {
      hideLoading()
    }
    let res = response.data
    const config = response.config
    const { filename } = response.headers

    // 兼容ArrayBuffer类型输出的报错
    filename && sessionStorage.setItem('filename', decodeURI(filename))
    if (res instanceof ArrayBuffer) {
      try {
        res = JSON.parse(Buffer.from(res, 'utf8').toString('utf-8'))
      } catch {
        return response
      }
    }

    if (
      response.request.responseType !== 'blob' &&
      res.hasOwnProperty('hasError') &&
      res.hasError !== 0
    ) {
      const { showError = true } = response.config
      if (showError) {
        res.errorMessage &&
          Message({
            message: res.errorMessage || 'Error',
            type: 'error',
            duration: 5 * 1000
          })
      }

      res.message = config?.rawResponse ? res : res.errorMessage || 'Error'
      return Promise.reject(res)
    } else {
      if (response.request.responseType === 'blob') {
        return response
      }
      return res
    }
  },
  async (error) => {
    hideLoading()
    console.log('请求出错!!🚀 ~ file: request.js:60 ~ error:', error)
    const status = error.response?.status
    const config = error.response?.config

    if (axios.isCancel(error)) {
      return Promise.reject(error)
    }
    if (
      error.code === 'ECONNABORTED' ||
      error.message === 'Network Error' ||
      error.message.includes('timeout')
    ) {
      Message({
        message: '请求超时，请稍后重试',
        type: 'error',
        duration: 3 * 1000
      })
      return Promise.reject(new Error('请求超时，请稍后重试'))
    } else {
      if (status === 606) {
        if (config.url.includes('/login/logout')) {
          Message({
            message: '用户认证无效，请重新登录',
            type: 'error',
            duration: 5 * 1000
          })
          return Promise.reject(new Error('用户认证无效，请重新登录'))
        } else {
          await store.dispatch('user/logOut')
        }
      } else if (status === 607) {
        const refresh_token = getRefreshToken()
        const uuid = getUUID()
        const yingYongDMList = [getDefaultState().systemID]
        // const yingYongDMList = getAuthrizedYYDMList()
        console.log('!refreshToken || !uuid', refresh_token, uuid)
        if (!refresh_token || !uuid) {
          Message({
            message: '用户认证过期，请更新后载入该页面',
            type: 'error',
            duration: 5 * 1000
          })
          return store.dispatch('user/resetToken')
        }
        // 刷新完成后直接返回请求队列
        if (!tokenRefreshing) {
          tokenRefreshing = true
          return new Promise((resolve, reject) => {
            refreshToken({ refresh_token, uuid, yingYongDMList })
              .then(async (res) => {
                console.log('refreshing!!!!!!!!!!!!!!!!', res)
                if (res.hasError !== 0) {
                  return Promise.reject(res.errorMessage)
                }
                const refreshToken = res?.data?.refreshToken
                const accessToken = res?.data?.accessToken

                if (accessToken) {
                  await store.dispatch('user/saveLoginToken', { refreshToken, accessToken })
                  config.headers.token = accessToken
                  reRequest(accessToken)
                  resolve(service(config))
                } else {
                  Message({
                    message: '用户认证过期，请更新后载入该页面',
                    type: 'error',
                    duration: 5 * 1000
                  })
                  await store.dispatch('user/resetToken')
                }
              })
              .catch((error) => {
                console.log('retryrequest', error)
              })
              .finally(() => {
                tokenRefreshing = false
              })
          })
        } else {
          return new Promise((resolve) => {
            cacheRequestArrHandle((token) => {
              config.headers.token = token
              resolve(service(config))
            })
          })
        }
      } else if (status === 403) {
        Message({
          message: error.response?.data?.errorMessage,
          type: 'error',
          duration: 5 * 1000
        })
        return Promise.reject(error)
      } else {
        Message({
          message: error.message,
          type: 'error',
          duration: 5 * 1000
        })
        return Promise.reject(error)
      }
    }

    return Promise.reject(error)
  }
)

export default service
