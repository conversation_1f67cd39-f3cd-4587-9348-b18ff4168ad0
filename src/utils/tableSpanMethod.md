# 表格行合并工具使用说明

## 概述

`tableSpanMethod.js` 提供了一套通用的表格行合并工具函数，用于 Element UI 表格的 `span-method` 属性。这些工具函数可以根据指定的分组字段（如 `zuHao`）自动合并表格行。

## 功能特性

- 🔧 **通用性强**：可用于任何需要行合并的 Element UI 表格
- 🎯 **灵活配置**：支持自定义分组字段和需要合并的列
- 📦 **开箱即用**：提供预配置的医嘱表格和通用表格合并方法
- 🚀 **性能优化**：高效的行合并算法，适用于大数据量表格
- 🎨 **重复值隐藏**：支持隐藏相邻行中重复的字段值，提升表格可读性

## API 文档

### createSpanMethod(dataSource, mergeColumns, groupField)

创建通用的表格行合并方法。

**参数：**
- `dataSource` (Array): 表格数据源
- `mergeColumns` (Array): 需要合并的列属性名数组，默认为 `[]`
- `groupField` (String): 分组字段名，默认为 `'zuHao'`

**返回值：**
- Function: 可用于 `el-table` 的 `span-method` 函数

### createYiZhuSpanMethod(dataSource)

为医嘱表格创建专用的行合并方法，预配置了医嘱表格常用的合并列。

**参数：**
- `dataSource` (Array): 医嘱数据源

**返回值：**
- Function: 配置好的 span-method 函数

**预配置的合并列：**
- `xuNiZH` - 虚拟组号
- `zhuangTaiBZMC` - 状态标志名称
- `leiXingMC` - 类型名称
- `kaiShiSJ_rq` - 开始时间日期
- `kaiShiSJ_sj` - 开始时间时间
- `zhiXingFF` - 执行方法
- `zhiXingPL` - 执行频率

### createGenericSpanMethod(dataSource, groupField)

为通用表格创建行合并方法，只合并选择列和 select 列。

**参数：**
- `dataSource` (Array): 表格数据源
- `groupField` (String): 分组字段名，默认为 `'zuHao'`

**返回值：**
- Function: 配置好的 span-method 函数

### shouldShowRepeatedField(dataSource, rowIndex, fieldName)

判断是否应该显示重复的相邻字段值。

**参数：**
- `dataSource` (Array): 表格数据源
- `rowIndex` (Number): 当前行索引
- `fieldName` (String): 要检查的字段名

**返回值：**
- Boolean: 是否应该显示该字段值

### getDisplayFieldValue(dataSource, row, rowIndex, fieldName)

获取要显示的字段内容（用于隐藏重复的相邻值）。

**参数：**
- `dataSource` (Array): 表格数据源
- `row` (Object): 当前行数据
- `rowIndex` (Number): 当前行索引
- `fieldName` (String): 字段名

**返回值：**
- String: 要显示的内容

## 使用示例

### 1. 基本用法

```javascript
import { createGenericSpanMethod } from '@/utils/tableSpanMethod'

export default {
  data() {
    return {
      tableData: [
        { zuHao: '001', name: '药品A', spec: '10mg' },
        { zuHao: '001', name: '药品B', spec: '20mg' },
        { zuHao: '002', name: '药品C', spec: '5mg' }
      ]
    }
  },
  methods: {
    spanMethod({ row, column, rowIndex }) {
      const spanMethod = createGenericSpanMethod(this.tableData)
      return spanMethod({ row, column, rowIndex })
    }
  }
}
```

### 2. 医嘱表格用法

```javascript
import { createYiZhuSpanMethod } from '@/utils/tableSpanMethod'

export default {
  data() {
    return {
      yiZhuList: [
        { zuHao: '001', mingCheng: '阿司匹林', xuNiZH: 'VG001' },
        { zuHao: '001', mingCheng: '维生素C', xuNiZH: 'VG001' }
      ]
    }
  },
  methods: {
    handleSpanMethod({ row, column, rowIndex }) {
      const spanMethod = createYiZhuSpanMethod(this.yiZhuList)
      return spanMethod({ row, column, rowIndex })
    }
  }
}
```

### 3. 自定义合并列

```javascript
import { createSpanMethod } from '@/utils/tableSpanMethod'

export default {
  methods: {
    customSpanMethod({ row, column, rowIndex }) {
      // 自定义需要合并的列
      const mergeColumns = ['category', 'type', 'status']
      const spanMethod = createSpanMethod(this.tableData, mergeColumns, 'groupId')
      return spanMethod({ row, column, rowIndex })
    }
  }
}
```

### 4. 隐藏重复的相邻字段值

```javascript
import { shouldShowRepeatedField, getDisplayFieldValue } from '@/utils/tableSpanMethod'

export default {
  data() {
    return {
      tableData: [
        { date: '2023-12-01', name: '药品A' },
        { date: '2023-12-01', name: '药品B' }, // 相同日期
        { date: '2023-12-02', name: '药品C' }
      ],
      hideRepeatedFields: ['date'] // 配置需要隐藏重复值的字段
    }
  },
  methods: {
    getDisplayValue(row, rowIndex, fieldName) {
      return getDisplayFieldValue(this.tableData, row, rowIndex, fieldName)
    }
  }
}
```

### 5. 在模板中使用

```vue
<template>
  <el-table
    :data="tableData"
    :span-method="spanMethod"
    border
  >
    <el-table-column type="selection" width="55"></el-table-column>
    <el-table-column prop="name" label="名称"></el-table-column>
    <el-table-column prop="spec" label="规格"></el-table-column>
    <!-- 日期列：隐藏重复的相邻日期 -->
    <el-table-column label="日期">
      <template #default="{ row, $index }">
        <template v-if="hideRepeatedFields.includes('date')">
          {{ getDisplayValue(row, $index, 'date') }}
        </template>
        <template v-else>
          {{ row.date }}
        </template>
      </template>
    </el-table-column>
  </el-table>
</template>
```

## 注意事项

1. **数据源要求**：确保传入的数据源是最新的，函数会基于传入的数据源计算合并行数
2. **分组字段**：确保数据中包含用于分组的字段（默认为 `zuHao`）
3. **性能考虑**：对于大数据量表格，建议使用虚拟滚动或分页来提升性能
4. **选择列合并**：所有方法都会自动合并 `type="selection"` 和 `property="select"` 的列

## 迁移指南

如果你正在从旧的 spanMethod 实现迁移到新的工具函数：

### 旧代码：
```javascript
spanMethod({ row, column, rowIndex }) {
  if (column.type === 'selection') {
    // 复杂的合并逻辑...
  }
}
```

### 新代码：
```javascript
spanMethod({ row, column, rowIndex }) {
  const spanMethod = createGenericSpanMethod(this.tableData)
  return spanMethod({ row, column, rowIndex })
}
```

这样可以大大简化代码，提高可维护性和复用性。
