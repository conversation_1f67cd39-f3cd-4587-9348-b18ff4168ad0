/*
 * @Description: 封装loading
 * @Author: lxc
 * @Date: 2019-08-09 08:29:15
 * @LastEditTime: 2022-03-16 10:22:00
 * @LastEditors: jiajia
 */
import { Loading } from 'element-ui'

let loadingCount = 0
let loading
let timer
const startLoading = () => {
  timer = setInterval(() => {
    if (loadingCount !== 0) {
      loading = Loading.service({
        lock: true,
        text: '加载中……',
        background: 'rgba(0, 0, 0, 0.5)',
        fullscreen: true
      })
    }
  }, 500)
}

const endLoading = () => {
  loading && loading.close()
  if (timer) clearInterval(timer)
}

export const showLoading = (loading = true) => {
  loadingCount++
  loading && startLoading()
}

export const hideLoading = () => {
  loadingCount--
  if (loadingCount === 0) {
    endLoading()
  }
}

const startLoadingByStr = (loadStr) => {
  loading = Loading.service({
    lock: true,
    text: loadStr,
    background: 'rgba(0, 0, 0, 0.5)',
    fullscreen: true
  })
}

export const showLoadingByStr = (loadStr) => {
  hideLoading()
  if (loadingCount === 0) {
    startLoadingByStr(loadStr)
  }
  loadingCount += 1
}
