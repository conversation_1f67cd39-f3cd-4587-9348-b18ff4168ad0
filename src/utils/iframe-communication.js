/**
 * iframe通信工具类
 * 专注于解决医疗文书iframe跨域通信问题
 * 支持保存、删除、打印三个基本操作
 */

export class IframeCommunication {
  constructor() {
    // 待处理的消息队列
    this.pendingMessages = new Map()
    // 消息超时时间（毫秒）
    this.timeout = 10000
    // 是否已初始化
    this.initialized = false
  }

  /**
   * 初始化通信
   */
  init() {
    if (this.initialized) return

    window.addEventListener('message', this.handleMessage.bind(this), false)
    this.initialized = true
    console.log('iframe通信已初始化')
  }

  /**
   * 销毁通信
   */
  destroy() {
    if (!this.initialized) return

    window.removeEventListener('message', this.handleMessage.bind(this), false)
    this.pendingMessages.clear()
    this.initialized = false
    console.log('iframe通信已销毁')
  }

  /**
   * 处理iframe返回的消息
   */
  handleMessage(event) {
    const data = event.data

    // 验证消息格式
    if (!data || typeof data !== 'object' || !data.messageId) {
      return
    }

    console.log('收到iframe响应:', data)

    // 查找对应的待处理消息
    const pendingMessage = this.pendingMessages.get(data.messageId)
    if (!pendingMessage) {
      return
    }

    // 清理超时定时器
    clearTimeout(pendingMessage.timeoutId)

    // 从队列中移除
    this.pendingMessages.delete(data.messageId)

    // 处理结果
    if (data.success) {
      // 返回完整的响应数据，而不仅仅是消息
      pendingMessage.resolve({
        message: data.message,
        data: data.data || null,
        newRecordId: data.newRecordId || null
      })
    } else {
      pendingMessage.reject(new Error(data.message))
    }
  }

  /**
   * 发送消息给iframe
   * @param {string} action 操作类型：save, delete, print
   * @param {string|number} recordId 记录ID
   * @param {HTMLIFrameElement} iframe iframe元素
   * @returns {Promise<Object>} 返回操作结果对象 {message, data, newRecordId}
   */
  sendMessage(action, recordId, iframe) {
    return new Promise((resolve, reject) => {
      if (!iframe || !iframe.contentWindow) {
        reject(new Error('iframe未加载完成'))
        return
      }

      // 生成消息ID
      const messageId = 'msg_' + Date.now() + '_' + Math.random().toString(36).substring(2, 8)

      // 构建消息
      const message = {
        action: action,
        recordId: recordId,
        messageId: messageId
      }

      // 设置超时处理
      const timeoutId = setTimeout(() => {
        this.pendingMessages.delete(messageId)
        reject(new Error(`操作超时: ${action}`))
      }, this.timeout)

      // 添加到待处理队列
      this.pendingMessages.set(messageId, {
        resolve,
        reject,
        timeoutId
      })

      try {
        // 发送消息
        iframe.contentWindow.postMessage(message, '*')
        console.log('发送消息给iframe:', message)
      } catch (error) {
        // 清理
        clearTimeout(timeoutId)
        this.pendingMessages.delete(messageId)
        reject(error)
      }
    })
  }

  /**
   * 保存操作
   * @param {string|number} recordId 记录ID
   * @param {HTMLIFrameElement} iframe iframe元素
   * @returns {Promise<Object>} 返回 {message, data, newRecordId}
   */
  save(recordId, iframe) {
    return this.sendMessage('save', recordId, iframe)
  }

  /**
   * 删除操作
   * @param {string|number} recordId 记录ID
   * @param {HTMLIFrameElement} iframe iframe元素
   * @returns {Promise<string>}
   */
  delete(recordId, iframe) {
    return this.sendMessage('delete', recordId, iframe)
  }

  /**
   * 打印操作
   * @param {string|number} recordId 记录ID
   * @param {HTMLIFrameElement} iframe iframe元素
   * @returns {Promise<string>}
   */
  print(recordId, iframe) {
    return this.sendMessage('print', recordId, iframe)
  }
}

// 创建全局实例
export const iframeCommunication = new IframeCommunication()

// 默认导出
export default iframeCommunication
