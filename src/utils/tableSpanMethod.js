/**
 * 表格行合并工具函数
 * 用于 Element UI 表格的 span-method 属性
 */

/**
 * 创建表格行合并方法
 * @param {Array} dataSource - 表格数据源
 * @param {Array} mergeColumns - 需要合并的列属性名数组
 * @param {string} groupField - 分组字段名，默认为 'zuHao'
 * @returns {Function} 返回可用于 el-table span-method 的函数
 */
export function createSpanMethod(dataSource, mergeColumns = [], groupField = 'zuHao') {
  return function ({ row, column, rowIndex }) {
    // 检查是否需要合并的列
    const shouldMerge =
      column.type === 'selection' ||
      column.property === 'select' ||
      mergeColumns.includes(column.property)

    if (shouldMerge) {
      // 对于没有分组字段的行，不进行合并
      if (!row[groupField]) {
        return [1, 1]
      }

      let rowspan = 1
      // 计算后续行中相同分组字段值的行数
      for (let i = rowIndex + 1; i < dataSource.length; i++) {
        if (dataSource[i][groupField] === row[groupField]) {
          rowspan++
        } else {
          break
        }
      }

      // 判断是否为该组的首行
      if (rowIndex === 0 || dataSource[rowIndex - 1][groupField] !== row[groupField]) {
        return [rowspan, 1] // 首行，合并下方 rowspan 行
      } else {
        return [0, 0] // 非首行，隐藏单元格
      }
    }
  }
}

/**
 * 为医嘱表格创建专用的行合并方法
 * @param {Array} dataSource - 医嘱数据源
 * @returns {Function} 返回配置好的 span-method 函数
 */
export function createYiZhuSpanMethod(dataSource) {
  // 医嘱表格需要合并的列
  const mergeCols = [
    'xuNiZH',
    'zhuangTaiBZMC',
    'leiXingMC',
    'kaiShiSJ_rq',
    'kaiShiSJ_sj',
    'zhiXingFF',
    'zhiXingPL'
  ]

  return createSpanMethod(dataSource, mergeCols, 'zuHao')
}

/**
 * 为通用表格创建行合并方法（只合并选择列和 select 列）
 * @param {Array} dataSource - 表格数据源
 * @param {string} groupField - 分组字段名，默认为 'zuHao'
 * @returns {Function} 返回配置好的 span-method 函数
 */
export function createGenericSpanMethod(dataSource, groupField = 'zuHao') {
  return createSpanMethod(dataSource, [], groupField)
}

/**
 * 判断是否应该显示重复的相邻字段值
 * @param {Array} dataSource - 表格数据源
 * @param {number} rowIndex - 当前行索引
 * @param {string} fieldName - 要检查的字段名
 * @returns {boolean} 是否应该显示该字段值
 */
export function shouldShowRepeatedField(dataSource, rowIndex, fieldName) {
  // 如果是第一行，总是显示
  if (rowIndex === 0) {
    return true
  }

  // 获取当前行和上一行的数据
  const currentRow = dataSource[rowIndex]
  const previousRow = dataSource[rowIndex - 1]

  // 如果当前行或上一行不存在，显示字段
  if (!currentRow || !previousRow) {
    return true
  }

  // 比较字段值是否相同
  const currentValue = currentRow[fieldName]
  const previousValue = previousRow[fieldName]

  // 如果值不同，显示字段；如果相同，隐藏字段
  return currentValue !== previousValue
}

/**
 * 获取要显示的字段内容（用于隐藏重复的相邻值）
 * @param {Array} dataSource - 表格数据源
 * @param {Object} row - 当前行数据
 * @param {number} rowIndex - 当前行索引
 * @param {string} fieldName - 字段名
 * @returns {string} 要显示的内容
 */
export function getDisplayFieldValue(dataSource, row, rowIndex, fieldName) {
  if (shouldShowRepeatedField(dataSource, rowIndex, fieldName)) {
    return row[fieldName] || ''
  }
  return ''
}
