import store from '@/store'

const name = 'base'

const LOGOUT_MSG = 'logout-msg'
const GU_ID = `guid`
const ORG_KEY = `orgKey`
const PREVIEW_TOKEN_KEY = `yulan-token`
const YYDM_LIST = `yydmList`

const TOKEN_KEY = `token`
const YONG_HU_ID = `yongHuID`
const YI_LIAO_JGDM = `yiLiaoJGDM`
const REFRESH_TOKEN_KEY = `refreshToken`
const UUID = `uuid`

export function setToken(token) {
  if (token) {
    store.commit('user/SET_TOKEN', token)
  }
  // return Cookies.set(TOKEN_KEY, token)
  // return sessionStorage.setItem(TOKEN_KEY, token)
}

export function getToken() {
  // 比对 localstorage的token
  let localToken = null
  try {
    localToken = JSON.parse(window.localStorage.getItem('authMap'))[
      `${store?.getters['app_key'] || ''}${getYongHuID() || ''}`
    ]['token']
  } catch (error) {
    localToken = store?.getters[TOKEN_KEY]
  }
  if (localToken !== store?.getters[TOKEN_KEY]) {
    setToken(localToken)
  }
  return localToken
  // return Cookies.get(TOKEN_KEY)
  // return sessionStorage.getItem(TOKEN_KEY)
}

export function removeToken() {
  return store.commit('user/SET_TOKEN', '')
  // return Cookies.remove(TOKEN_KEY)
  // return sessionStorage.removeItem(TOKEN_KEY)
}

export function setYongHuID(yongHuID) {
  if (yongHuID) {
    store.commit('user/SET_TOKEN', yongHuID)
  }
  // return Cookies.set(YONG_HU_ID, yongHuID)
  // return sessionStorage.setItem(YONG_HU_ID, yongHuID)
}

export function getYongHuID() {
  return store.getters[YONG_HU_ID]
  // return Cookies.get(YONG_HU_ID)
  // return sessionStorage.getItem(YONG_HU_ID)
}

export function removeYongHuID() {
  return store.commit('user/SET_USER_ID', '')
  // return Cookies.remove(YONG_HU_ID)
  // return sessionStorage.removeItem(YONG_HU_ID)
}

export function setYiLiaoJGDM(yiLiaoJGDM) {
  if (yiLiaoJGDM) {
    store.commit('user/SET_YLJGDM', yiLiaoJGDM)
  }
  // return Cookies.set(YI_LIAO_JGDM, yiLiaoJGDM)
  // return sessionStorage.setItem(YI_LIAO_JGDM, yiLiaoJGDM)
}

export function getYiLiaoJGDM() {
  return store.getters[YI_LIAO_JGDM]
  // return Cookies.get(YI_LIAO_JGDM)
  // return sessionStorage.getItem(YI_LIAO_JGDM)
}

export function removeYiLiaoJGDM() {
  return store.commit('user/SET_YLJGDM', '')
  // return Cookies.remove(YI_LIAO_JGDM)
  // return sessionStorage.removeItem(YI_LIAO_JGDM)
}

export function setLogoutMsg(logout_msg) {
  if (logout_msg) {
    store.dispatch('auth/setLogoutMsg', logout_msg)
  }
  // return Cookies.set(LOGOUT_MSG, logout_msg)
  // return sessionStorage.setItem(LOGOUT_MSG, logout_msg)
}

export function getLogoutMsg() {
  return store.getters['auth/' + LOGOUT_MSG]
  // return Cookies.get(LOGOUT_MSG)
  // return sessionStorage.getItem(LOGOUT_MSG)
}

export function removeLogoutMsg() {
  return store.dispatch('auth/removeLogoutMsg')
  // return Cookies.remove(LOGOUT_MSG)
  // return sessionStorage.removeItem(LOGOUT_MSG)
}

export function setOrgKey(orgKey) {
  if (orgKey) {
    store.dispatch('auth/setOrgKey', orgKey)
  }
  // return Cookies.set(ORG_KEY, orgKey)
  // return sessionStorage.setItem(ORG_KEY, orgKey)
}

export function getOrgKey() {
  return store.getters['auth/' + ORG_KEY]
  // return Cookies.get(ORG_KEY)
  // return sessionStorage.getItem(ORG_KEY)
}

export function removeOrgKey() {
  return store.dispatch('auth/removeOrgKey')
  // return Cookies.remove(ORG_KEY)
  // return sessionStorage.removeItem(ORG_KEY)
}

export function getGuId() {
  return store.getters['auth/' + GU_ID]
  // return Cookies.get(GU_ID)
}

export function setGuId(guid) {
  if (guid) {
    store.dispatch('auth/setGuId', guid)
  }
  // return Cookies.set(GU_ID, guid)
}

export function removeGuId() {
  return store.dispatch('auth/removeGuId')
  // return Cookies.remove(GU_ID)
}

export function setYuLanToken(token) {
  if (token) {
    store.dispatch('auth/setYuLanToken', token)
  }
}

export function getYuLanToken() {
  return store.getters['auth/' + PREVIEW_TOKEN_KEY]
}

export function removeYuLanToken() {
  return store.dispatch('auth/removeYuLanToken')
}

export function getAuthrizedYYDMList() {
  return store.getters['auth/' + YYDM_LIST]
  // try {
  //   return JSON.parse(Cookies.get(YYDM_LIST))
  // } catch (err) {
  //   return []
  // }
}

export function setAuthrizedYYDMList(yydm) {
  if (yydm) {
    store.dispatch('auth/setAuthrizedYYDMList', yydm)
  }
  // const _YYDMList = getAuthrizedYYDMList() || []
  // _YYDMList.push(yydm)
  // const noRepeatArray = [...new Set(_YYDMList)]
  // return Cookies.setItem(YYDM_LIST, JSON.stringify(noRepeatArray))
}

export function removeAuthrizedYYDMList() {
  return store.dispatch('auth/removeAuthrizedYYDMList')
  // Cookies.remove(YYDM_LIST)
}

// 刷新用
export function setRefreshToken(refreshToken) {
  if (refreshToken) {
    store.commit('user/SET_REFRESH_TOKEN', refreshToken)
  }
  // return Cookies.set(REFRESH_TOKEN_KEY, refreshToken)
}

export function getRefreshToken() {
  // 比对 localstorage的token
  let localRefreshToken = null
  try {
    localRefreshToken = JSON.parse(window.localStorage.getItem('authMap'))[
      `${store?.getters['app_key'] || ''}${getYongHuID() || ''}`
    ]['refreshToken']
  } catch (error) {
    localRefreshToken = store?.getters[REFRESH_TOKEN_KEY]
  }
  if (localRefreshToken !== store?.getters[REFRESH_TOKEN_KEY]) {
    setRefreshToken(localRefreshToken)
  }
  return localRefreshToken
  // return Cookies.get(REFRESH_TOKEN_KEY)
}

export function removeRefreshToken() {
  return store.commit('user/SET_REFRESH_TOKEN', '')
  // return Cookies.remove(REFRESH_TOKEN_KEY)
}

export function getUUID() {
  return store.getters[UUID]
  // return Cookies.get(UUID)
}

export function setUUID(uuid) {
  if (uuid) {
    store.commit('user/SET_UUID', uuid)
  }
  // return Cookies.set(UUID, uuid)
}

export function removeUUID() {
  return store.commit('user/SET_UUID', '')
  // return Cookies.remove(UUID)
}

export function clearAllUserAuth() {
  removeToken()
  removeUUID()
  removeYongHuID()
  removeRefreshToken()
  removeAuthrizedYYDMList()
}
