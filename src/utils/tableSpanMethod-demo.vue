<template>
  <div class="table-span-demo">
    <h2>表格行合并和重复值隐藏演示</h2>
    
    <div class="demo-section">
      <h3>1. 原始数据显示</h3>
      <el-table :data="demoData" border size="mini">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="zuHao" label="组号" width="80"></el-table-column>
        <el-table-column prop="kaiShiSJ_rq" label="开始日期" width="100"></el-table-column>
        <el-table-column prop="mingCheng" label="医嘱名称" width="200"></el-table-column>
        <el-table-column prop="yiCiYL" label="一次用量" width="80"></el-table-column>
      </el-table>
    </div>

    <div class="demo-section">
      <h3>2. 应用行合并后的效果</h3>
      <el-table :data="demoData" border size="mini" :span-method="spanMethod">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="zuHao" label="组号" width="80"></el-table-column>
        <el-table-column prop="kaiShiSJ_rq" label="开始日期" width="100"></el-table-column>
        <el-table-column prop="mingCheng" label="医嘱名称" width="200"></el-table-column>
        <el-table-column prop="yiCiYL" label="一次用量" width="80"></el-table-column>
      </el-table>
    </div>

    <div class="demo-section">
      <h3>3. 应用行合并 + 隐藏重复日期后的效果</h3>
      <el-table :data="demoData" border size="mini" :span-method="spanMethod">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="zuHao" label="组号" width="80"></el-table-column>
        <el-table-column label="开始日期" width="100">
          <template #default="{ row, $index }">
            {{ getDisplayDate(row, $index, 'kaiShiSJ_rq') }}
          </template>
        </el-table-column>
        <el-table-column prop="mingCheng" label="医嘱名称" width="200"></el-table-column>
        <el-table-column prop="yiCiYL" label="一次用量" width="80"></el-table-column>
      </el-table>
    </div>

    <div class="demo-section">
      <h3>配置说明</h3>
      <el-card>
        <div slot="header">
          <span>功能特性</span>
        </div>
        <ul>
          <li><strong>行合并</strong>：相同组号（zuHao）的行会自动合并选择列和组号列</li>
          <li><strong>重复值隐藏</strong>：相邻行中重复的开始日期会被隐藏，只在第一行显示</li>
          <li><strong>灵活配置</strong>：可以通过 hideRepeatedFields 数组配置需要隐藏重复值的字段</li>
          <li><strong>保持交互</strong>：不影响表格的选择、点击等交互功能</li>
        </ul>
      </el-card>
    </div>
  </div>
</template>

<script>
import { createYiZhuSpanMethod, getDisplayFieldValue } from '@/utils/tableSpanMethod'

export default {
  name: 'TableSpanDemo',
  data() {
    return {
      // 演示数据
      demoData: [
        { zuHao: '001', kaiShiSJ_rq: '2023-12-01', mingCheng: '阿司匹林肠溶片', yiCiYL: '100mg' },
        { zuHao: '001', kaiShiSJ_rq: '2023-12-01', mingCheng: '维生素C片', yiCiYL: '100mg' },
        { zuHao: '002', kaiShiSJ_rq: '2023-12-01', mingCheng: '头孢克肟胶囊', yiCiYL: '100mg' },
        { zuHao: '003', kaiShiSJ_rq: '2023-12-02', mingCheng: '布洛芬缓释胶囊', yiCiYL: '300mg' },
        { zuHao: '003', kaiShiSJ_rq: '2023-12-02', mingCheng: '奥美拉唑肠溶胶囊', yiCiYL: '20mg' },
        { zuHao: '004', kaiShiSJ_rq: '2023-12-02', mingCheng: '甲硝唑片', yiCiYL: '200mg' },
        { zuHao: '005', kaiShiSJ_rq: '2023-12-03', mingCheng: '复方甘草片', yiCiYL: '3片' }
      ],
      // 需要隐藏重复值的字段
      hideRepeatedFields: ['kaiShiSJ_rq']
    }
  },
  methods: {
    // 表格行合并方法
    spanMethod({ row, column, rowIndex }) {
      const spanMethod = createYiZhuSpanMethod(this.demoData)
      return spanMethod({ row, column, rowIndex })
    },
    
    // 获取要显示的日期内容
    getDisplayDate(row, rowIndex, fieldName) {
      return getDisplayFieldValue(this.demoData, row, rowIndex, fieldName)
    }
  }
}
</script>

<style scoped>
.table-span-demo {
  padding: 20px;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-section h3 {
  margin-bottom: 15px;
  color: #409EFF;
}

.el-card {
  margin-top: 20px;
}

.el-card ul {
  margin: 0;
  padding-left: 20px;
}

.el-card li {
  margin-bottom: 8px;
  line-height: 1.5;
}
</style>
