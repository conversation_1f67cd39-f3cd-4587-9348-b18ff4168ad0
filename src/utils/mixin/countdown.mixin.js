export const countDown = (count) => {
  return {
    data() {
      return {
        timer: null,
        [count]: 0,
        time2: 1231
      }
    },
    watch: {
      [count](val) {
        if (val === 60) {
          this.countDown()
        }
      }
    },
    methods: {
      countDown() {
        if (!this.timer) {
          this.count = 60
          this.timer = setInterval(() => {
            this[count]--
            if (this[count] === 0) {
              clearInterval(this.timer)
              this.timer = null
            }
          }, 1000)
        }
      }
    }
  }
}
