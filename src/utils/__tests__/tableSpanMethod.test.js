/**
 * 表格行合并工具函数测试
 */

import {
  createSpanMethod,
  createYiZhuSpanMethod,
  createGenericSpanMethod,
  shouldShowRepeatedField,
  getDisplayFieldValue
} from '../tableSpanMethod'

describe('tableSpanMethod', () => {
  // 测试数据
  const testData = [
    { zuHao: '001', mingCheng: '药品A', xuNiZH: 'VG001' },
    { zuHao: '001', mingCheng: '药品B', xuNiZH: 'VG001' },
    { zuHao: '002', mingCheng: '药品C', xuNiZH: 'VG002' },
    { zuHao: '003', mingCheng: '药品D', xuNiZH: 'VG003' }
  ]

  describe('createSpanMethod', () => {
    it('应该为选择列正确合并行', () => {
      const spanMethod = createSpanMethod(testData, [], 'zuHao')

      // 测试第一组的第一行（应该合并2行）
      const result1 = spanMethod({
        row: testData[0],
        column: { type: 'selection' },
        rowIndex: 0
      })
      expect(result1).toEqual([2, 1])

      // 测试第一组的第二行（应该隐藏）
      const result2 = spanMethod({
        row: testData[1],
        column: { type: 'selection' },
        rowIndex: 1
      })
      expect(result2).toEqual([0, 0])

      // 测试第二组的第一行（应该不合并）
      const result3 = spanMethod({
        row: testData[2],
        column: { type: 'selection' },
        rowIndex: 2
      })
      expect(result3).toEqual([1, 1])
    })

    it('应该为指定列正确合并行', () => {
      const spanMethod = createSpanMethod(testData, ['xuNiZH'], 'zuHao')

      // 测试指定列的合并
      const result = spanMethod({
        row: testData[0],
        column: { property: 'xuNiZH' },
        rowIndex: 0
      })
      expect(result).toEqual([2, 1])
    })

    it('应该为非合并列返回默认值', () => {
      const spanMethod = createSpanMethod(testData, [], 'zuHao')

      const result = spanMethod({
        row: testData[0],
        column: { property: 'mingCheng' },
        rowIndex: 0
      })
      expect(result).toBeUndefined()
    })

    it('应该处理没有分组字段的行', () => {
      const dataWithoutGroup = [{ mingCheng: '药品A' }]
      const spanMethod = createSpanMethod(dataWithoutGroup, [], 'zuHao')

      const result = spanMethod({
        row: dataWithoutGroup[0],
        column: { type: 'selection' },
        rowIndex: 0
      })
      expect(result).toEqual([1, 1])
    })
  })

  describe('createYiZhuSpanMethod', () => {
    it('应该为医嘱表格预配置的列正确合并', () => {
      const spanMethod = createYiZhuSpanMethod(testData)

      // 测试预配置的列
      const result = spanMethod({
        row: testData[0],
        column: { property: 'xuNiZH' },
        rowIndex: 0
      })
      expect(result).toEqual([2, 1])
    })
  })

  describe('createGenericSpanMethod', () => {
    it('应该只为选择列和select列合并', () => {
      const spanMethod = createGenericSpanMethod(testData)

      // 测试选择列
      const result1 = spanMethod({
        row: testData[0],
        column: { type: 'selection' },
        rowIndex: 0
      })
      expect(result1).toEqual([2, 1])

      // 测试select列
      const result2 = spanMethod({
        row: testData[0],
        column: { property: 'select' },
        rowIndex: 0
      })
      expect(result2).toEqual([2, 1])

      // 测试其他列（不应该合并）
      const result3 = spanMethod({
        row: testData[0],
        column: { property: 'mingCheng' },
        rowIndex: 0
      })
      expect(result3).toBeUndefined()
    })
  })

  describe('边界情况', () => {
    it('应该处理空数据源', () => {
      const spanMethod = createSpanMethod([], [], 'zuHao')

      const result = spanMethod({
        row: {},
        column: { type: 'selection' },
        rowIndex: 0
      })
      expect(result).toEqual([1, 1])
    })

    it('应该处理自定义分组字段', () => {
      const customData = [
        { groupId: 'A', name: '项目1' },
        { groupId: 'A', name: '项目2' },
        { groupId: 'B', name: '项目3' }
      ]

      const spanMethod = createSpanMethod(customData, [], 'groupId')

      const result = spanMethod({
        row: customData[0],
        column: { type: 'selection' },
        rowIndex: 0
      })
      expect(result).toEqual([2, 1])
    })
  })

  describe('shouldShowRepeatedField', () => {
    const dateTestData = [
      { date: '2023-12-01', name: '药品A' },
      { date: '2023-12-01', name: '药品B' },
      { date: '2023-12-02', name: '药品C' },
      { date: '2023-12-02', name: '药品D' },
      { date: '2023-12-03', name: '药品E' }
    ]

    it('应该在第一行显示字段值', () => {
      const result = shouldShowRepeatedField(dateTestData, 0, 'date')
      expect(result).toBe(true)
    })

    it('应该隐藏重复的相邻字段值', () => {
      // 第二行的日期与第一行相同，应该隐藏
      const result = shouldShowRepeatedField(dateTestData, 1, 'date')
      expect(result).toBe(false)
    })

    it('应该显示不同的字段值', () => {
      // 第三行的日期与第二行不同，应该显示
      const result = shouldShowRepeatedField(dateTestData, 2, 'date')
      expect(result).toBe(true)
    })

    it('应该处理空数据源', () => {
      const result = shouldShowRepeatedField([], 0, 'date')
      expect(result).toBe(true)
    })
  })

  describe('getDisplayFieldValue', () => {
    const dateTestData = [
      { date: '2023-12-01', name: '药品A' },
      { date: '2023-12-01', name: '药品B' },
      { date: '2023-12-02', name: '药品C' }
    ]

    it('应该返回第一行的字段值', () => {
      const result = getDisplayFieldValue(dateTestData, dateTestData[0], 0, 'date')
      expect(result).toBe('2023-12-01')
    })

    it('应该返回空字符串对于重复的相邻值', () => {
      const result = getDisplayFieldValue(dateTestData, dateTestData[1], 1, 'date')
      expect(result).toBe('')
    })

    it('应该返回不同的字段值', () => {
      const result = getDisplayFieldValue(dateTestData, dateTestData[2], 2, 'date')
      expect(result).toBe('2023-12-02')
    })

    it('应该处理空字段值', () => {
      const dataWithEmptyField = [{ date: null }]
      const result = getDisplayFieldValue(dataWithEmptyField, dataWithEmptyField[0], 0, 'date')
      expect(result).toBe('')
    })
  })
})
