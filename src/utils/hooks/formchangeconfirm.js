import { computed, watch } from 'vue'
import store from '@/store'
import { confirm } from '@/global/dialog'

/**
 * 表单离开之前的弹出保存提示
 * @param {Object} targetData  监听的属性对象
 * @param {function} targetDataCallback 传入一个保存表单的方法,用于用户点击【是】按钮
 * @returns {Object} {deleteFormWatcher} 返回一个关闭监听的方法
 */
export const useFormChangeConfirm = (targetData, targetDataCallback) => {
  const observeTarget = computed(() => {
    try {
      return JSON.parse(JSON.stringify(targetData))
    } catch (err) {
      console.error(err)
    }

    return {}
  })

  watch(
    observeTarget,
    (newValue, oldValue) => {
      if (!targetData) {
        return
      }

      if (JSON.stringify(newValue) !== JSON.stringify(oldValue)) {
        console.log('newValue', newValue, oldValue)
        store.dispatch('app/setCurrentDataChange', {
          value: true,
          callback: targetDataCallback || (() => {})
        })
      }
    },
    {
      deep: true
    }
  )

  /**
   * 关闭、离开之前调用此方法
   * @returns {Promise<unknown>}
   */
  const beforeLeave = async () => {
    if (store.state.app.isCurrentDataChange) {
      return new Promise((resolve, reject) => {
        confirm(
          '是否保存已修改的数据？',
          async () => {
            await store.dispatch('app/useCallback')
            await store.dispatch('app/setCurrentDataChange', { value: false })
            resolve()
          },
          {
            popType: 'save',
            closeCallback: async () => {
              console.log('关闭')
              await store.dispatch('app/setCurrentDataChange', { value: false })
              // 变化,可以关闭tag
              await store.dispatch('app/setTagClose', true)
              resolve()
            }
          }
        )
      })
    } else {
      return new Promise((resolve) => {
        resolve()
      })
    }
  }

  /**
   * 清除监听
   */
  const clearFormWatcher = () => {
    setTimeout(() => {
      store.dispatch('app/setCurrentDataChange', { value: false })
    })
  }

  return { beforeLeave, clearFormWatcher }
}
