const { getIconfontUrl } = require('@/api/basic')

// 添加iconfont配置
export const getIconfontUrlMethod = async () => {
  const rsp = await getIconfontUrl()
  if (rsp.hasError !== 0 || !rsp.data.data) {
    return
  }
  const sourceList = rsp.data.data

  const iconfontJS = document.createElement('script')
  iconfontJS.src = sourceList.find((e) => e.includes('iconfont.js'))
  document.head.appendChild(iconfontJS)

  const iconfontLink = document.createElement('link')
  iconfontLink.rel = 'stylesheet'
  iconfontLink.href = sourceList.find((e) => e.includes('iconfont.css'))
  document.head.appendChild(iconfontLink)

  const woff2Url = sourceList.find((e) => e.includes('iconfont.woff2'))
  const woffUrl = sourceList.find((e) => e.includes('iconfont.woff'))
  const tffUrl = sourceList.find((e) => e.includes('iconfont.ttf'))

  // 覆盖fontface
  const fontfaceStyle = document.createElement('style')
  const cssContent = `
        @charset "UTF-8";
        @font-face {
          font-family: "iconfont"; /* Project id fontface */
          src: url(${woff2Url}) format('woff2'),
               url(${woffUrl}) format('woff'),
               url('${tffUrl}') format('truetype');
        }
        `
  fontfaceStyle.type = 'text/css'
  fontfaceStyle.appendChild(document.createTextNode(cssContent))
  document.head.appendChild(fontfaceStyle)
}
