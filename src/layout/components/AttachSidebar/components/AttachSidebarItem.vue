<template>
  <div v-if="ifRenderOnlyChild && onlyOneChild.meta">
    <app-link v-if="onlyOneChild.meta" :to="resolvePath(onlyOneChild.path)">
      <el-menu-item
        v-if="onlyOneChild.meta"
        :index="resolvePath(onlyOneChild.path)"
        :class="{ 'submenu-title-noDropdown': !isNest }"
      >
        <item :icon-visible="!isNest" :x-meta="onlyOneChild.meta" />
      </el-menu-item>
    </app-link>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import AppLink from '@/layout/components/Sidebar/components/Link.vue'
import Item from '@/layout/components/Sidebar/components/Item.vue'
export default {
  name: 'AttachSidebarItem',
  components: {
    Item,
    AppLink
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    }
  },
  computed: {
    onlyOneChild() {
      const { caiDanLX } = this?.item
      // 菜单类型
      if (caiDanLX === 'C') {
        return this.item
      }
      // 目录类型
      if (caiDanLX === 'M') {
        // 获取item.children最后一个元素
        return this.item?.children?.[this.item?.children?.length - 1]
      }
      // 按钮、功能类型
      if (caiDanLX === 'F') {
        return this?.item
      }
      return {}
    }
  },
  mounted() {
    console.log('item', this.item)
  },
  methods: {
    resolvePath(_path) {
      if (!_path) {
        return ''
      }
      if (isExternal(_path)) {
        return _path
      }
      if (isExternal(this.basePath)) {
        return this._path
      }
      return path.resolve(this.basePath, _path)
    },

    ifRenderOnlyChild(item) {
      const { caiDanLX, hidden } = item
      if (hidden) return false
      // 菜单类型
      if (caiDanLX === 'C') {
        return false
      }
      // 目录类型
      if (caiDanLX === 'M') {
        return true
      }
      // 按钮、功能类型
      if (caiDanLX === 'F') {
        return false
      }
      return true
    }
  }
}
</script>

<style lang="scss" scoped></style>
