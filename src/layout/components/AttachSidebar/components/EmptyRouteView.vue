<template>
  <section class="el-container">
    <transition name="fade-transform" mode="out-in">
      <keep-alive :include="cachedViews">
        <router-view />
      </keep-alive>
    </transition>
  </section>
</template>
<script>
export default {
  name: 'EmptyRouteView',
  computed: {
    // 需要缓存的页面 固钉
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.fullPath
    }
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  flex: 1;
  width: 100%;
  padding: 10px;
  overflow: auto;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    min-height: 50vh;
    overflow: auto;
  }
}
</style>
