<template>
  <div
    class="container"
    :class="{
      'attach-sidebar-close': !attachSidebar.opened,
      'sidebar-close': !sidebar.opened
    }"
  >
    <hamburger
      :is-active="attachSidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleAttachSideBar"
    />

    <el-menu
      v-if="attachSidebar.opened"
      :key="new Date().getTime()"
      :default-active="activeMenu"
      :collapse="isCollapse"
      :unique-opened="true"
      :collapse-transition="false"
    >
      <div v-for="(item, index) in attachSidebarRoutes" :key="index" class="menu-item-single">
        <attach-sidebar-item :item="item" :base-path="attachSidebarBasePath"></attach-sidebar-item>
      </div>
    </el-menu>
  </div>
</template>

<script>
import Hamburger from '@/components/Hamburger/index.vue'
import AttachSidebarItem from './components/AttachSidebarItem.vue'
import { mapGetters, mapState } from 'vuex'
export default {
  name: 'AttachSidebar',
  components: { Hamburger, AttachSidebarItem },
  data() {
    return {}
  },
  computed: {
    ...mapState({
      attachSidebar: ({ app }) => app.attachSidebar,
      attachSidebarRoutes: ({ app }) => app.attachSidebarRoutes,
      attachSidebarBasePath: ({ app }) => app.attachSidebarBasePath,
      sidebar: ({ app }) => app.sidebar
    }),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      console.log('🚀 ~ activeMenu ~ path:', path)
      return path
    },
    isCollapse() {
      return !this.attachSidebar.opened
    }
  },
  methods: {
    toggleAttachSideBar() {
      this.$store.dispatch('app/toggleAttachSideBar')
    },
    changeAttachSidebar(bol, ...args) {
      let path = ''
      args.forEach((fI) => {
        path = fI.path + '/' + path
      })
      const item = args[0]
      if (bol) {
        this.$store.dispatch('app/setAttachSidebarRoutes', item?.children || [])
        this.$store.dispatch('app/setAttachSidebarBasePath', path || '')
        // this.$router.push(path + item?.children[0]?.path)
        if (!item?.children || item?.children?.length <= 0) {
          this.$store.dispatch('app/changeAttachSideBar', false)
        } else {
          this.$store.dispatch('app/changeAttachSideBar', true)
        }
      } else {
        this.$store.dispatch('app/setAttachSidebarRoutes', [])
        this.$store.dispatch('app/setAttachSidebarBasePath', '')
        this.$store.dispatch('app/changeAttachSideBar', false)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  width: 155px;
  height: calc(100% - 10px);
  margin: 6px 0 0 6px;
  background: #eff3fb;
  border-radius: 4px;
  box-shadow: 0 6px 24px 0 rgb(0 0 0 / 20%);
  transition: width 0.3s;

  .hamburger-container {
    width: 40px;
    margin-top: 10px;
    cursor: pointer;
    transition: background 0.3s;
    transition: transform 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      // background: rgba(0, 0, 0, 0.025);
    }

    ::v-deep .hamburger {
      color: rgb(23 28 40 / 70%);
    }
  }

  ::v-deep .el-menu {
    padding: 6px 0;
    background-color: transparent;

    .el-menu-item {
      height: 40px;
      padding-left: 6px !important;
      margin-left: 6px;
      line-height: 40px;
      border-radius: 6px 0 0 6px;
    }

    .el-menu-item.is-active {
      background-color: #fff;
    }
  }

  &.attach-sidebar-close {
    position: absolute;
    left: 165px;
    z-index: 2000;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    opacity: 0.7;

    ::v-deep .hamburger-container {
      padding: 0 !important;
      margin: 0;
      transform: rotate(180deg);
    }
  }

  &.sidebar-close.attach-sidebar-close {
    display: none;
  }
}
</style>
