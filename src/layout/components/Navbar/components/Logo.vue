<template>
  <div class="sidebar-logo-container" :class="{ collapse }">
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <span v-if="logo" :class="logo" class="font_family outer">
          <img :src="logoPng" fit="fit" class="img-icon" />
        </span>
        <div v-else class="sidebar-info">
          <div class="sidebar-title">{{ title }}</div>
          <div>
            <span class="sidebar-ip">{{ ipAddress }}</span>
            <span class="sidebar-screen">({{ screenResolution }})</span>
          </div>
        </div>
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <span v-if="logo" :class="logo" class="font_family outer">
          <img :src="logoPng" fit="fit" class="img-icon" />
        </span>
        <div class="sidebar-info">
          <div class="sidebar-title">
            {{ title }}
            <span class="sidebar-ip">v0.0.10</span>
          </div>
          <div>
            <span class="sidebar-ip">{{ ipAddress }}</span>
            <span class="sidebar-screen">({{ screenResolution }})</span>
          </div>
        </div>
      </router-link>
    </transition>
  </div>
</template>

<script>
import logoPng from '@/assets/images/logo.png'
import { mapGetters } from 'vuex'
import ResizeMixin from '@/layout/mixin/ResizeHandler'
export default {
  name: 'SidebarLogo',
  mixins: [ResizeMixin],
  props: {
    collapse: {
      type: Boolean
    }
  },
  data() {
    return {
      logoPng,
      // title: require('@/settings').title,
      logo: 'icon-a-logodibu3x'
    }
  },
  computed: {
    ...mapGetters({
      title: 'appInfo/title',
      ipAddress: 'user/ipAddress'
    })
  }
}
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  position: relative;
  display: flex;
  width: 100%;
  height: 52px;
  overflow: hidden;
  text-align: center;

  & .sidebar-logo-link {
    display: flex !important;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .outer {
      display: flex;
      justify-content: flex-end;
      margin: 6px;

      /* .svg-icon {
      } */
      .img-icon {
        width: 40px;
        height: 40px;
        margin: 0 !important;
      }
    }

    .sidebar-info {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      margin-left: 8px;
      .sidebar-title {
        font-family: 'Source Han Sans';
        font-size: 14px;
        font-weight: 700;
        font-feature-settings: 'kern' on;
        white-space: nowrap;
        color: #fff;
      }
      .sidebar-ip,
      .sidebar-screen {
        font-size: 12px;
        margin-top: 2px;
        font-family: 'Source Han Sans';
        color: #cdcdcd;
      }
    }
  }

  &.collapse {
    .sidebar-logo {
      margin-right: 0;
    }
  }
}
</style>
