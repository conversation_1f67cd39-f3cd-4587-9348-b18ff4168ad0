<template>
  <el-menu ref="menuRef" mode="horizontal" @select="handleSelect">
    <template v-for="(item, index) in headerMenuRouters">
      <el-menu-item
        v-if="!item.children || !item.children.length"
        :key="index"
        :index="resolvePath(item.path)"
      >
        {{ item.meta.title }}
      </el-menu-item>
      <el-submenu
        v-else
        ref="subMenu"
        :key="index"
        popper-class="popper-submenu"
        :index="resolvePath(item.path)"
      >
        <template #title>
          {{ item.meta.title }}
        </template>
        <sub-menu-item
          v-for="(child, childIndex) in item.children"
          :key="childIndex"
          :item="child"
          :base-path="resolvePath(item.path)"
        />
      </el-submenu>
    </template>
  </el-menu>
</template>

<script>
import SubMenuItem from './SubMenuItem.vue'
import { mapGetters } from 'vuex'
import { isExternal } from '@/utils/validate'
import path from 'path'

export default {
  name: 'HeaderMenu',
  components: { SubMenuItem },
  data() {
    return {
      itemClickNativeVal: ''
    }
  },
  computed: {
    ...mapGetters(['permission_routes', 'sidebar', 'sidebarRouters']),
    headerMenuRouters() {
      return this.sidebarRouters.filter(
        (item) => item.path !== '/' && item.path !== '/patient-inside' && !item.hidden
      )
    }
  },
  watch: {
    // activeMenu(val) {
    //   const title = val === this.$route.path ? this.$route.meta.title : ''
    //   const data = {
    //     appKey: getDefaultState().app_key,
    //     xiangXiMS: title,
    //     shuJuLX: 1,
    //     caoZuoLX: 4
    //   }
    //   addWebAuditLog(data)
    // }
  },
  mounted() {
    console.log(this.headerMenuRouters, 'headerMenuRouters')
  },
  methods: {
    itemClickNative(path) {
      this.itemClickNativeVal = ''
      this.itemClickNativeVal = path
    },
    handleSelect(key, keyPath) {
      console.log(key, keyPath, '这里是keyPath')
      if (isExternal(key)) {
        window.open(key)
      } else {
        this.$router.push({ path: key })
      }
    },
    resolvePath(_path) {
      if (isExternal(_path)) {
        return _path
      }
      return path.resolve('/', _path)
    }
  }
}
</script>

<style lang="scss" scoped>
.center-menu .el-menu--horizontal {
  .el-menu-item,
  .el-submenu ::v-deep .el-submenu__title {
    height: 51px;
    line-height: 51px;
    font-size: 14px;
    color: #333333;
  }
}
</style>
