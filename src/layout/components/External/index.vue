<!--
 -- <AUTHOR>
 -- @Date 2024-02-03
 -- @LastEditors hybtalented
 -- @LastEditTime 2024-02-29
 -- @FilePath /base-wyyy-template/src/layout/components/External/index.vue
 -- @Description
 -->
<template>
  <embed-frame
    :src="getIframeSrc"
    height="calc(var(--content-fixed-height) + 110px)"
    :interactive="false"
  ></embed-frame>
</template>

<script>
import EmbedFrame from '@/components/EmbedFrame/index.vue'
import { refreshLinkToken } from '@/utils/tools'
import { getRefreshToken, getToken, getUUID, getYiLiaoJGDM, getYongHuID } from '@/utils/auth'
import { getDefaultState } from '@/store/modules/user'

export default {
  components: {
    EmbedFrame
  },
  props: {
    url: {
      type: String,
      required: true
    }
  },
  methods: {
    getIframeSrc() {
      if (this.url) {
        return refreshLinkToken(this.url, getDefaultState()['systemID'], getYiLiaoJGDM)
      }
    }
  }
}
</script>

<style scoped></style>
