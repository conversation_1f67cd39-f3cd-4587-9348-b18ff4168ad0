const getters = {
  sidebar: (state) => state.app.sidebar,
  language: (state) => state.app.language,
  size: (state) => state.app.size,
  device: (state) => state.app.device,
  visitedViews: (state) => state.tagsView.visitedViews,
  cachedViews: (state) => state.tagsView.cachedViews,
  token: (state) => state.user.token,
  refresh_token: (state) => state.user.refresh_token,
  refreshToken: (state) => state.user.refreshToken,
  yiLiaoJGDM: (state) => state.user.yiLiaoJGDM,
  uuid: (state) => state.user.uuid,
  yongHuID: (state) => state.user.yongHuID,
  avatar: (state) => state.user.avatar,
  name: (state) => state.user.name,
  introduction: (state) => state.user.introduction,
  roles: (state) => state.user.roles,
  app_key: (state) => state.user.app_key,
  permission_routes: (state) => state.permission.routes,
  errorLogs: (state) => state.errorLog.logs,
  sidebarRouters: (state) => state.permission.sidebarRouters,
  isLock: (state) => state.lockscreen.isLock
}
export default getters
