import { getLoginInfo, getDoctorInfoByUserID, getZhuanKeList } from '@/api/user'
import { getBingqQuListByZhuanKeID, getZhiLiaoZuListByZhuanKeID, mainPageInit } from '@/api/patient'
import { getLastDiagnoseByBingLiIDList, PatientInit } from '@/api/patient-info'
const state = {
  initInfo: {}, // 初始化数据，包括专科ID、专科名称
  jianYiSP: [], // 移动建议审批通知
  geXingHua: [], // 个性化设置
  gongGaoXX: [], // 公告信息
  patientList: [], // 病人列表
  patientInit: {}, // 病人内页初始化数据
  zhuanKeList: [], // 专科列表
  bingQuList: [], // 病区列表’
  zhiLiaoZuList: [], // 治疗组列表
  bingQuID: '', // 当前病区ID
  zhiLiaoZuID: '', // 当前治疗组ID
  doctorInfo: {}, // 医生信息
  sideBarRoute: '' //左侧菜单路由
}

const mutations = {
  GET_PATIENT_LIST: (state, arr) => {
    state.patientList = arr
  },
  GET_PATIENT_INIT: (state, obj) => {
    state.patientInit = obj
  },
  GET_INIT_INFO: (state, obj) => {
    state.initInfo = obj
  },
  GET_OTHER_INIT: (state, obj) => {
    state.jianYiSP = obj.jianYiSP
    state.geXingHua = obj.geXingHua
    state.gongGaoXX = obj.gongGaoXX
  },
  GET_ZHUAN_KE_LIST: (state, arr) => {
    state.zhuanKeList = arr
  },
  GET_BING_QU_LIST: (state, arr) => {
    state.bingQuList = arr
  },
  GET_ZHI_LIAO_ZU_LIST: (state, arr) => {
    state.zhiLiaoZuList = arr
  },
  SET_BING_QU_ID: (state, id) => {
    state.bingQuID = id
  },
  SET_ZHI_LIAO_ZU_ID: (state, id) => {
    state.zhiLiaoZuID = id
  },
  SET_DOCTOR_INFO: (state, info) => {
    state.doctorInfo = info
  },
  SET_SIDEBAR_ROUTE: (state, info) => {
    state.sideBarRoute = info
  }
}

const actions = {
  async getInit({ commit }) {
    const res = await getLoginInfo()
    if (res.hasError === 0) {
      commit('GET_INIT_INFO', res.data)
    }
    const res2 = await mainPageInit()
    if (res2.hasError === 0) {
      commit('GET_OTHER_INIT', res2.data)
    }
  },

  async getPatientInit({ commit }, bingLiID) {
    const res = await PatientInit(bingLiID)
    if (res.hasError === 0) {
      //根据病历ID列表获取住院病人最新诊断
      getLastDiagnoseByBingLiIDList({ bingLiIDList: bingLiID }).then((res2) => {
        if (res2.hasError === 0) {
          if (res2.data[bingLiID] && res2.data[bingLiID].length) {
            res.data.linChuangZD = res2.data[bingLiID][res2.data[bingLiID].length - 1].mingChen
          }
          commit('GET_PATIENT_INIT', res.data)
        }
      })
    }
  },

  async setPatientList({ commit }, data) {
    commit('GET_PATIENT_LIST', data)
  },
  async getZhuanKeList({ commit }) {
    const res = await getZhuanKeList()
    if (res.hasError === 0) {
      commit('GET_ZHUAN_KE_LIST', res.data)
    }
  },

  async getBingQuList({ commit, state }) {
    const res = await getBingqQuListByZhuanKeID({
      ZKID: state.initInfo.zhuanKeID
    })
    if (res.hasError === 0) {
      commit('GET_BING_QU_LIST', res.data)
      if (res.data.length > 0) {
        commit('SET_BING_QU_ID', res.data[0].buMenID)
      } else {
        commit('SET_BING_QU_ID', '')
      }
    }
  },
  async getZhiLiaoZuList({ commit, state }) {
    const res = await getZhiLiaoZuListByZhuanKeID({
      zhuanKeID: state.initInfo.zhuanKeID
    })
    if (res.hasError === 0) {
      commit('GET_ZHI_LIAO_ZU_LIST', res.data)
      if (res.data.length > 0) {
        commit('SET_ZHI_LIAO_ZU_ID', res.data[0].zhiLiaoZuID)
      } else {
        commit('SET_ZHI_LIAO_ZU_ID', '')
      }
    }
  },
  async setBingQuID({ commit }, id) {
    commit('SET_BING_QU_ID', id)
  },
  async setZhiLiaoZuID({ commit }, id) {
    commit('SET_ZHI_LIAO_ZU_ID', id)
  },
  async getDoctorInfo({ commit }, yongHuID) {
    const res = await getDoctorInfoByUserID({
      yongHuID
    })
    if (res.hasError === 0) {
      commit('SET_DOCTOR_INFO', res.data)
    }
    return res
  },
  async setSideBarRoute({ commit }, route) {
    commit('SET_SIDEBAR_ROUTE', route)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
