/**
 * <AUTHOR>
 * @Date 2024-02-19
 * @LastEditors hybtalented
 * @LastEditTime 2024-02-29
 * @FilePath /base-wyyy-template/src/store/modules/permission.js
 * @Description
 * @Copyright (c) 2024 by 附一医信息处, All Rights Reserved.
 */
import { getRouters } from '@/api/user'
import EmptyRouteView from '@/layout/components/AttachSidebar/components/EmptyRouteView.vue'
import ExternalLayout from '@/layout/components/External/index.vue'
import Layout from '@/layout/index'
import ParentView from '@/layout/components/ParentView/index.vue'
import { constantRoutes } from '@/router'
import { getQueryObject } from '@/utils'
import { isExternal } from '@/utils/validate'
/**
 * Use meta.role to determine if the current user has permission
 * @param roles
 * @param route
 */
function hasPermission(roles, route) {
  if (route.meta && route.meta.roles) {
    return roles.some((role) => route.meta.roles.includes(role))
  } else {
    return true
  }
}

/**
 * Filter asynchronous routing tables by recursion
 * @param routes asyncRoutes
 * @param roles
 */
export function filterAsyncRoutes(routes, roles) {
  const res = []
  routes.forEach((route) => {
    const tmp = { ...route }
    if (hasPermission(roles, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, roles)
      }
      res.push(tmp)
    }
  })

  return res
}

const state = {
  routes: [],
  addRoutes: [],
  sidebarRouters: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  SET_SIDEBAR_ROUTERS: (state, routes) => {
    state.sidebarRouters = routes
  }
}

const actions = {
  // 生成路由
  GenerateRoutes({ commit, rootState }) {
    return new Promise((resolve, reject) => {
      // 向后端请求路由数据
      getRouters({
        currUserId: rootState.user.yongHuID,
        yingYongDM: rootState.user.systemID
      })
        .then((res) => {
          const rdata = JSON.parse(JSON.stringify(res.data))
          const rewriteRoutes = filterAsyncRouter(rdata, false, false)
          rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })
          console.log('🚀 ~ file: permission.js:66 ~ .then ~ rewriteRoutes', rewriteRoutes)
          commit('SET_ROUTES', rewriteRoutes)
          commit('SET_SIDEBAR_ROUTERS', constantRoutes.concat(rewriteRoutes))
          // commit('SET_SIDEBAR_ROUTERS', constantRoutes) // 侧边栏路由暂用router.js写死
          resolve(rewriteRoutes)
        })
        .catch((err) => {
          reject(err)
        })
    })
  }
}

// 遍历后台传来的路由字符串，转换为组件对象
function filterAsyncRouter(asyncRouterMap, lastRouter = false, type = false) {
  return asyncRouterMap.filter((route) => {
    if (!route.path) {
      return false
    }
    if (type && route.children) {
      route.children = filterChildren(route.children)
    }
    if (route.component && route.component !== '/') {
      // Layout ParentView 组件特殊处理
      if (route.component === 'Layout') {
        route.component = Layout
      } else if (route.component === 'ParentView') {
        route.component = ParentView
      } else {
        route.component = loadView(route.component)
      }
    } else {
      route.component = EmptyRouteView
    }

    if (isExternal(route.path)) {
      const params = getQueryObject(route.path)
      if (params.layoutless) {
        // 如果时外部地址，并且地址中包含 layoutless 表示使用嵌入的 iframe
        route.component = ExternalLayout
        const routePath = `/external/${encodeURIComponent(route.path)}`
        route.props = {
          url: route.path
        }
        route.path = routePath
        route.meta.activeMenu = routePath
      }
    }

    if (route.children != null && route.children && route.children.length) {
      route.children = filterAsyncRouter(route.children, route, type)
    } else {
      delete route['children']
      delete route['redirect']
    }
    return true
  })
}

function filterChildren(childrenMap, lastRouter = false) {
  var children = []
  childrenMap.forEach((el, index) => {
    if (lastRouter) {
      el.path = lastRouter.path + '/' + el.path
    }
    children = children.concat(el)
  })
  return children
}

// 拼接组件
export const loadView = (view) => {
  return (resolve) => require([`@/views/${view}`], resolve)
}

// 导出 filterAsyncRouter 函数供测试使用
export { filterAsyncRouter }

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
