import { getAppDetail } from '@/api/user'
import store from '@/store'

const state = {
  yingYongJC: '',
  yingYongMC: ''
}

const getters = {
  title(state) {
    if (!state.yingYongJC && !state.yingYongMC) {
      store.dispatch('appInfo/getTitle').then(() => {
        return state.yingYongJC || state.yingYongMC
      })
    }
    return state.yingYongJC || state.yingYongMC
  }
}

const mutations = {
  SET_JIANCHENG(state, data) {
    state.yingYongJC = data
  },
  SET_MINGCHENG(state, data) {
    state.yingYongMC = data
  }
}

const actions = {
  async getTitle({ commit }) {
    const {
      data: { yingYongJC, yingYongMC }
    } = await getAppDetail()
    commit('SET_JIANCHENG', yingYongJC)
    commit('SET_MINGCHENG', yingYongMC)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
