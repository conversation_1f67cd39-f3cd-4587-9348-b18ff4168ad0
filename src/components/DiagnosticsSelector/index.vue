<template>
  <el-dialog
    title="感染诊断选择"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :visible="dialogVisible"
    v-bind="$attrs"
    :custom-class="`dialog dialog-${size}`"
    width="700px"
  >
    <div class="input-group">
      <el-select
        v-model="qianZhui"
        placeholder="请选择前缀"
        size="small"
        class="input-group-prefix"
      >
        <el-option v-for="item in qianSuffix" :key="item" :label="item" :value="item" />
      </el-select>
      <el-input
        v-model="input"
        placeholder="请输入内容"
        clearable
        size="small"
        class="input-group-content"
        @change="handleSearch"
      />
      <el-select v-model="houZhui" placeholder="请选择后缀" size="small" class="input-group-suffix">
        <el-option v-for="item in houSuffix" :key="item" :label="item" :value="item" />
      </el-select>
    </div>
    <div style="height: 350px">
      <public-table
        class="table noScroll"
        :loading="loading"
        :table-data="tableData"
        :table-column="tablesetting.columns"
        :events="tablesetting.events"
        :btn-button="tablesetting.operations"
        operation-width="40px"
        :has-operation="false"
        :is-need-pagination="true"
        :total="total"
        :current-page="currentIndex"
        :page-size="currentSize"
        @sizeChange="sizeChange"
        @currentChange="currentChange"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" @click="dialogVisible = false">取 消</el-button>
      <el-button size="small" class="el-button-color" @click="handleConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  getPrefixOrSuffix,
  getDiagnoseAllByBingAnBHAndMingCheng
} from '@/api/diagnostication-selector'
import { PublicTable } from 'wyyy-component'
export default {
  name: 'DiagnosticsSelector',
  components: {
    PublicTable
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    showPrefix: {
      type: Boolean,
      default: false
    },
    showSuffix: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'small'
    },
    columns: {
      type: Array,
      default: () => [
        {
          prop: 'mingCheng',
          label: '诊断名称',
          minWidth: '80px'
        },
        {
          prop: 'icdm',
          label: 'ICD编码',
          minWidth: '80px'
        }
      ]
    }
  },
  data() {
    return {
      loading: false,
      input: '',
      qianZhui: '', // 前缀
      houZhui: '', // 后缀
      qianSuffix: [], // 前缀数组
      houSuffix: [], // 后缀数组
      tableData: [],
      tablesetting: {
        columns: this.columns,
        events: {
          'row-click': (row) => {
            this.handleTableRow(row)
          },
          'row-dblclick': (row) => {
            this.handleDblclick(row)
          }
        },
        operations: []
      }, // 显示表格列表
      total: null,
      totalIndex: 1,
      currentIndex: 1,
      currentSize: 20,
      selectedRow: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.input = ''
        this.totalIndex = 1
        this.currentIndex = 1
        this.currentSize = 20
        this.getPrefixOrSuffix()
      }
    }
  },
  mounted() {
    this.getPrefixOrSuffix()
  },
  methods: {
    getPrefixOrSuffix() {
      getPrefixOrSuffix(this.request).then((res) => {
        this.houSuffix = res.data.houZhui
        this.qianSuffix = res.data.qianZhui
      })
    },
    getDiagnoseTable() {
      this.loading = true
      const acquisitionData = {
        currUserId: null, //	currUserId	header	true	integer
        isPage: true, //	是否分页标志	query	true	integer
        name: this.input, // 搜索参数	query	false	string
        pageNum: this.currentIndex, // 分页页数	query	true	integer
        pageSize: this.currentSize // 每页记录数	query	true	integer
      }
      getDiagnoseAllByBingAnBHAndMingCheng(this.request, acquisitionData)
        .then((res) => {
          if (res.data) {
            this.tableData = res.data.diagnoseBaseTemplateVoList
            this.total = res.data.total
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleSearch() {
      this.currentIndex = 1
      this.getDiagnoseTable()
    },
    // 分页
    sizeChange(e) {
      this.currentSize = e
      this.getDiagnoseTable()
    },
    // 上下页
    currentChange(e) {
      this.currentIndex = e
      this.getDiagnoseTable()
    },
    // 表格双击事件
    handleDblclick(row) {
      this.dialogVisible = false
      this.$emit('handleConfirm', {
        prefix: this.qianZhui,
        content: row.mingCheng,
        suffix: this.houZhui,
        _row: row
      })
    },
    // 表格点击事件
    handleTableRow(row, event, column) {
      this.input = row.mingCheng
      this.selectedRow = row
    },
    handleConfirm() {
      this.$emit('handleConfirm', {
        prefix: this.qianZhui,
        content: row.mingCheng,
        suffix: this.houZhui,
        _row: row
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep {
  .el-dialog {
    &.dialog {
      &-medium {
        width: var(--dialog-width-medium);
      }

      &-small {
        width: var(--dialog-width-small);
      }

      &-large {
        width: var(--dialog-width-large);
      }

      &-mini {
        width: var(--dialog-width-mini);
      }
    }

    &__header {
      border-bottom: 1px solid #dadee6;
      padding: 10px 14px;
      font: var(--font-medium);
      font-size: var(--font-size-medium);
      color: #171c28;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 3px;
        height: 16px;
        background: #356ac5;
        margin-right: 6px;
      }

      .el-dialog__headerbtn {
        font-size: 19px;
        right: 14px;
        top: auto;

        .el-dialog__close {
          color: #8590b3;
        }
      }
    }
  }
}

.input-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;

  .input-group-content {
    margin: auto 10px;
    min-width: 45%;
  }
}
</style>
