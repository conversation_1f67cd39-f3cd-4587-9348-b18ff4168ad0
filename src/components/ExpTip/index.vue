<template>
  <div v-if="isShowTips" class="notice-tis">
    <div class="link-text">
      {{ '为了更好的体验，请使用Chrome浏览器，版本号需大于等于84，分辨率需设置为1440*900及以上' }}
    </div>
    <div class="el-icon-close el-icon close-btn" @click="onClose"></div>
  </div>
</template>

<script>
export default {
  name: 'AppMain',
  data() {
    return {
      timer: null,
      closeTimeNum: 10,
      isShow: false
    }
  },
  computed: {
    isChromeVersion() {
      const sys = this.getExplore()
      console.log('🚀 ~ isChromeVersion ~ browser:', sys)
      if (sys.browser === 'Chrome' && sys.version.split('.')[0] >= 84) return true
      else return false
    },
    isProperResolution() {
      const screenWidth = window.screen.width
      const screenHeight = window.screen.height
      return screenWidth >= 1440 && screenHeight >= 900
    },
    isShowTips() {
      return this.isShow && (!this.isChromeVersion || !this.isProperResolution)
    },
    closeStatus() {
      return sessionStorage.getItem('isCloseNoticeTip') ? false : true
    }
  },
  mounted() {
    var channel = new BroadcastChannel('fy_notice_tips')
    this.channel = channel
    channel.onmessage = (event) => {
      console.log('Received message:', event.data)
      const { type, data } = event.data
      if (type == 'close') {
        this.onClose('origni')
      } else if (type == 'asyncTime') {
        this.closeTimeNum = data
      }
    }

    this.isShow = sessionStorage.getItem('isCloseNoticeTip') ? false : true
  },

  methods: {
    getExplore() {
      const Sys = {}
      const ua = navigator.userAgent.toLowerCase()
      let s
      ;(s = ua.match(/rv:([\d.]+)\) like gecko/))
        ? (Sys.ie = s[1])
        : (s = ua.match(/msie ([\d\.]+)/))
        ? (Sys.ie = s[1])
        : (s = ua.match(/edge\/([\d\.]+)/))
        ? (Sys.edge = s[1])
        : (s = ua.match(/firefox\/([\d\.]+)/))
        ? (Sys.firefox = s[1])
        : (s = ua.match(/(?:opera|opr).([\d\.]+)/))
        ? (Sys.opera = s[1])
        : (s = ua.match(/chrome\/([\d\.]+)/))
        ? (Sys.chrome = s[1])
        : (s = ua.match(/version\/([\d\.]+).*safari/))
        ? (Sys.safari = s[1])
        : 0
      // 根据关系进行判断
      if (Sys.ie) return { browser: 'IE', version: Sys.ie }
      if (Sys.edge) return { browser: 'EDGE', version: Sys.edge }
      if (Sys.firefox) return { browser: 'Firefox', version: Sys.firefox }
      if (Sys.chrome) return { browser: 'Chrome', version: Sys.chrome }
      if (Sys.opera) return { browser: 'Opera', version: Sys.opera }
      if (Sys.safari) return { browser: 'Safari', version: Sys.safari }
      return { browser: 'Unkonwn', version: 'Unkonwn' }
    },
    // handleClose() {
    //   this.channel.postMessage({ type: 'asyncTime', data: this.closeTimeNum })
    //   if (this.closeTimeNum > 0) {
    //     setTimeout(() => {
    //       this.closeTimeNum--
    //       this.handleClose()
    //     }, 1000)
    //   } else {
    //     this.onClose()
    //   }
    // },
    onClose(type) {
      console.log('close', type)
      this.isShow = false
      sessionStorage.setItem('isCloseNoticeTip', 1)
      if (type != 'origni') {
        this.channel.postMessage({ type: 'close' })
      }
    }
    // isChrome() {
    //   let userAgent = window.navigator.userAgent
    //   const text = userAgent.includes('Chrome') ? '' : '请使用chrome浏览器'
    //   return text
    // },
    // getScreenWidth() {
    //   const screenWidth = window.screen.width
    //   const screenHeight = window.screen.height
    //   console.log('screenWidth', screenWidth, screenHeight, window.screen.width)
    //   const text = screenWidth < 1440 || screenHeight < 900 ? '最小分辨率为1440*900' : ''
    //   return text
    // }
  }
}
</script>

<style lang="scss" scoped>
.notice-tis {
  position: relative;
  z-index: 9;
  width: 100%;
  margin: 0 auto;
  font-size: 14px;
  line-height: 40px;
  color: #555;
  text-align: center;
  background-color: #fee3cc;

  .link-text {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 15px;
    color: #fda333;
  }

  .close-btn {
    position: absolute;
    top: 0;
    right: 0;
    width: 38px;
    font-size: 15px;
    line-height: 38px;
    color: #fda333;
    cursor: pointer;
  }
}
</style>
