<script setup>
import DetailTitle from '@/components/DetailTitle/index.vue'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  backgroundColor: {
    type: String,
    default: '#eff3fb'
  },
  padding: {
    type: String,
    default: '10px'
  },
  fulfill: {
    type: Boolean,
    default: false
  }
})
</script>

<template>
  <div
    :class="fulfill ? 'normal-card-fulfill' : ''"
    :style="{ '--background-color': backgroundColor, '--padding': padding }"
    class="normal-card-container"
  >
    <div class="title">
      <detail-title :title="title">
        <template #extra>
          <slot name="extra"></slot>
        </template>
      </detail-title>
    </div>
    <div class="content">
      <slot name="content"></slot>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.normal-card-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
  padding: var(--padding);

  .content {
    flex: 1;
    margin-top: 10px;
    width: 100%;
  }
}

.normal-card-fulfill {
  height: 100%;

  .content {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
  }
}
</style>
