<template>
  <div>
    <el-dialog
      :title="zhuanKeMC ? `${titleStr} - ${zhuanKeMC}` : titleStr"
      :close-on-press-escape="false"
      :visible="dialogVisible"
      :destroy-on-close="true"
      width="800px"
      @close="closeDialog"
    >
      <div class="top">
        <div class="block-left">
          <el-select v-model="guanJianZiMode" placeholder="请选择" size="small" class="item">
            <el-option
              v-for="item in guanJianZiModeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-input
            ref="guanJianZi"
            v-model="guanJianZi"
            size="small"
            :autofocus="true"
            placeholder="请输入查找关键字"
            clearable
            class="item"
            @clear="inputClear()"
            @keyup.enter.native="searchByGuanJianZiByNet"
          />
          <el-button
            size="small"
            icon="el-icon-search"
            class="el-button-color item"
            type="primary"
            @click="initLB()"
          >
            查找
          </el-button>
        </div>
        <el-checkbox v-model="useZhuanKeProps" class="checkbox" @change="initLB">
          本专科
        </el-checkbox>
      </div>
      <div style="height: 600px">
        <public-table
          class="table noScroll"
          :loading="loading"
          :table-column="tablesetting.columns"
          :table-data="tableData"
          :events="tablesetting.events"
          :btn-button="tablesetting.operations"
          operation-width="40px"
          :has-operation="false"
          :is-need-pagination="true"
          :total="jiLuZS"
          :current-page="currentPage"
          :page-size="pageCount"
          @sizeChange="sizeChange"
          @currentChange="currentChange"
        />
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { PublicTable } from 'wyyy-component'
import { searchByGuanJianZi } from 'wyyy-component/src/apis/doctor-selector'
export default {
  name: 'DoctorSelector',
  components: { PublicTable },
  props: {
    titleStr: {
      type: String,
      default: '医生选择',
      required: false
    },
    // axios实例
    request: {
      type: Function,
      default: null,
      required: false
    },
    // 业务参数 : 是否本专科
    isBenZhuanke: {
      type: Boolean,
      default: false
    },
    benZhuankeID: {
      type: String,
      default: '',
      required: false
    },
    zhuanKeID: {
      type: Number,
      default: null,
      required: false
    },
    zhuanKeMC: {
      type: String,
      default: '',
      required: false
    },
    visible: {
      type: Boolean,
      default: false
    },
    // 外部传入数据
    extraData: {
      type: Array,
      default: () => []
    }
  },
  emits: ['select', 'closeDialog'],
  data() {
    return {
      guanJianZiModeOptions: [
        {
          value: '1',
          label: '首字母'
        },
        {
          value: '2',
          label: '五笔'
        },
        {
          value: '3',
          label: '中文名'
        }
      ],
      guanJianZiMode: '1',
      guanJianZi: '',
      useZhuanKeProps: this.isBenZhuanke || '',
      tablesetting: {
        columns: [
          {
            prop: 'xingMing',
            label: '姓名',
            minWidth: '80px'
          },
          {
            prop: 'zhongShenDM',
            label: '终身码',
            minWidth: '80px'
          },
          {
            prop: 'xianZhuanKeMC',
            label: '所在专科',
            minWidth: '60px'
          },
          {
            prop: 'renYuanLBMC',
            label: '人员类别',
            minWidth: '120px'
          }
        ],
        events: {
          'row-dblclick': (row, column, event) => {
            this.handleTableRow(row)
          }
        },
        operations: []
      },
      tableData: [],
      jiLuZS: null,
      loading: false,
      currentPage: 1,
      pageCount: 20,
      currentRow: null
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('handleChangeVisible', val)
      }
    }
  },
  watch: {
    dialogVisible: {
      immediate: true,
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.$refs.guanJianZi.focus()
            this.initLB()
            // this.initLocalData()
          })
        }
      }
    }
  },
  methods: {
    // 获取接口数据
    searchByGuanJianZiByNet() {
      // 是本专科的话 zhuanKeID传本专科ID
      this.loading = true
      if (this?.extraData?.length) {
        this.tableData = this.extraData
        this.loading = false
        return
      }
      if (this.useZhuanKeProps && !this.benZhuankeID) {
        return Promise.reject('本专科ID不能为空')
      }
      const params = {
        guanJianZi: this.guanJianZi, //关键字
        mode: this.guanJianZiMode, // 关键字类型
        zhuanKeID: this.useZhuanKeProps ? this.benZhuankeID : this.zhuanKeID ? this.zhuanKeID : '', // 专科ID
        pageCount: this.pageCount, // 一页多少
        currentPage: this.currentPage // 第几页(页数)
      }
      searchByGuanJianZi(this.request, params)
        .then((res) => {
          if (res.data) {
            this.tableData = res.data.jiLuLB
            this.jiLuZS = res.data.jiLuZS
            this.loading = false
          }
        })
        .catch((error) => {
          this.$message.error(error)
          this.loading = false
        })
    },

    // 查找按钮
    initLB() {
      this.tableData = []
      this.currentPage = 1
      this.searchByGuanJianZiByNet()
    },
    inputClear() {
      this.tableData = []
      this.currentPage = 1
      this.guanJianZi = ''
      this.searchByGuanJianZiByNet()
    },
    // 取消按钮
    closeDialog() {
      this.tableData = []
      this.currentPage = 1
      this.guanJianZi = ''
      this.dialogVisible = false
    },

    currentChange(e) {
      console.log('currentChange:', e)
      this.currentPage = e
      this.searchByGuanJianZiByNet()
    },
    sizeChange(e) {
      console.log('sizeChange:', e)
      this.pageCount = e
      this.searchByGuanJianZiByNet()
    },
    // 表格点击行内事件
    handleTableRow(row, event, column) {
      console.log('handleTableRow', row)
      this.dialogVisible = false
      this.$emit('handleConfim', row)
    }
  }
}
</script>
<style lang="scss" scoped>
@import 'wyyy-component/src/styles/common.scss';

.dialogTitle {
  color: #888888;
  background-color: #f5f7fa;
  text-align: left;
  padding: 0;
}

.top {
  display: flex;
  align-items: center;

  .block-left {
    flex: 1;
    display: flex;
    justify-content: space-around;

    .item {
      margin: auto 5px;
    }
  }

  .checkbox {
    width: 100px;
  }
}

.table {
  margin: 10px auto 0;
  ::v-deep .el-pagination {
    padding: 0;
  }
}

.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 8px;
}

.dialog-footer {
  padding-top: 0;
}

.top-flex {
  display: flex;
  align-items: center;
}
</style>
