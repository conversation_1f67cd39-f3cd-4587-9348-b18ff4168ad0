<template>
  <div class="medical-document-container">
    <div class="medical-document-layout">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <div class="tabs-container">
          <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabClick">
            <!-- 记录列表 -->
            <el-tab-pane label="记录列表" name="saved">
              <div class="saved-records-list">
                <el-table
                  v-loading="savedRecordsLoading"
                  :data="savedRecordsList"
                  highlight-current-row
                  height="100%"
                  element-loading-text="加载中..."
                  border
                  stripe
                  size="mini"
                  @row-click="handleSavedRecordClick"
                >
                  <el-table-column prop="jilluSJ" label="记录时间" width="150">
                    <template #default="{ row }">
                      <span class="record-time">{{ row.jilluSJ }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="wenShuMC" :label="documentNameLabel" show-overflow-tooltip>
                    <template #default="{ row }">
                      <span class="record-name" :class="{ 'red-text': shouldShowRedText(row) }">
                        {{ row.wenShuMC }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 文书列表 -->
            <el-tab-pane :label="documentTabLabel" name="authorization">
              <div class="document-content">
                <!-- 文书列表 -->
                <div class="document-list">
                  <el-table
                    v-loading="documentListLoading"
                    :data="documentList"
                    highlight-current-row
                    height="100%"
                    element-loading-text="加载中..."
                    border
                    stripe
                    size="mini"
                    @row-click="handleDocumentClick"
                  >
                    <el-table-column
                      prop="wenShuMC"
                      :label="documentNameLabel"
                      show-overflow-tooltip
                    >
                      <template #default="{ row }">
                        <span>【新增】</span>
                        <span class="record-name" :class="{ 'red-text': shouldShowRedText(row) }">
                          {{ row.wenShuMC }}
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 右侧详情面板 -->
      <div class="right-panel">
        <div class="right-header">
          <div class="header-content">
            <div class="record-title">
              <span v-if="currentRecord">
                {{ currentRecord.wenShuMC }}
              </span>
              <span v-else>{{ emptySelectionText }}</span>
            </div>
            <div v-if="currentRecord" class="action-buttons">
              <el-button
                v-for="button in actionButtons"
                :key="button.action"
                type="primary"
                size="mini"
                @click="handleAction(button.action)"
              >
                {{ button.label }}
              </el-button>
            </div>
          </div>
        </div>
        <div class="iframe-container">
          <iframe
            v-if="iframeUrl"
            ref="contentIframe"
            :src="iframeUrl"
            frameborder="0"
            @load="handleIframeLoad"
          ></iframe>
          <div v-else class="no-content">
            <i class="el-icon-document"></i>
            <p>{{ noContentText }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getTeShuZhuanKeYiShuXieWS, getTeShuZhuanKeGeShiDM } from '@/api/informed-consent'
import iframeCommunication from '@/utils/iframe-communication'

export default {
  name: 'SpecialtyDocumentBase',
  props: {
    // 专科ID
    zhuanKeID: {
      type: String,
      required: true
    },
    // 专科文书标签
    documentTabLabel: {
      type: String,
      default: '专科文书列表'
    },
    // 文书名称标签
    documentNameLabel: {
      type: String,
      default: '文书名称'
    },
    // 未选择文书时的提示文字
    emptySelectionText: {
      type: String,
      default: '请选择文书'
    },
    // 无内容时的提示文字
    noContentText: {
      type: String,
      default: '请从左侧选择文书'
    },
    // 操作按钮配置
    actionButtons: {
      type: Array,
      default: () => [
        { action: 'save', label: '保存' },
        { action: 'delete', label: '删除' },
        { action: 'print', label: '打印' }
      ]
    },
    // 基础URL
    baseUrl: {
      type: String,
      default: 'http://10.41.220.39/ehr'
    }
  },
  data() {
    return {
      activeTab: 'saved',
      savedRecordsList: [],
      savedRecordsLoading: false,
      documentList: [],
      documentListLoading: false,
      currentRecord: null,
      iframeUrl: ''
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      patientInfo: ({ patient }) => patient.patientInit,
      gongZhongDM: ({ patient }) => patient.doctorInfo.gongZhongDM_DZ,
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  mounted() {
    this.initData()
    // 初始化iframe通信
    iframeCommunication.init()
  },

  beforeDestroy() {
    // 销毁iframe通信
    iframeCommunication.destroy()
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.fetchSavedRecords()
      await this.fetchDocumentList()
    },

    // 获取已保存记录列表
    async fetchSavedRecords() {
      this.savedRecordsLoading = true
      try {
        const res = await getTeShuZhuanKeYiShuXieWS({
          bingLiID: this.bingLiID,
          zhuanKeID: this.zhuanKeID
        })

        if (res.hasError === 0) {
          this.savedRecordsList = res.data.sort((a, b) => {
            return new Date(a.jilluSJ || 0) - new Date(b.jilluSJ || 0)
          })
        } else {
          this.$message.error(res.errorMessage || '获取已保存记录失败')
        }
      } catch (error) {
        console.error('获取已保存记录失败', error)
        this.$message.error('获取已保存记录失败')
      } finally {
        this.savedRecordsLoading = false
      }
    },

    // 获取文书列表
    async fetchDocumentList() {
      this.documentListLoading = true
      try {
        const res = await getTeShuZhuanKeGeShiDM({
          zhuanKeID: this.zhuanKeID
        })

        if (res.hasError === 0) {
          this.documentList = res.data.map((item) => ({
            ...item,
            wenShuMC: item.mingChen
          }))
        } else {
          this.$message.error(res.errorMessage || '获取文书列表失败')
        }
      } catch (error) {
        console.error('获取文书列表失败', error)
        this.$message.error('获取文书列表失败')
      } finally {
        this.documentListLoading = false
      }
    },

    // 判断是否应该显示红色文字
    shouldShowRedText(row) {
      return row.guidanBZ && row.guidanBZ === '0'
    },

    // 标签页切换
    handleTabClick(tab) {
      this.activeTab = tab.name
    },

    // 已保存记录点击
    handleSavedRecordClick(record) {
      this.currentRecord = record
      this.setIframeUrl(record)
      this.$emit('record-selected', record)
    },

    // 文书点击
    handleDocumentClick(record) {
      this.currentRecord = record
      this.setIframeUrl(record)
      this.$emit('document-selected', record)
    },

    // 设置iframe URL
    setIframeUrl(record) {
      if (!record) {
        this.iframeUrl = ''
        return
      }

      // 根据guidanBZ字段判断URL生成逻辑
      let params = {}
      let pageName = 'blwsdetail.aspx' // 默认页面

      if (record.guidanBZ === '1') {
        params = {
          as_wsid: record.id || 0,
          as_blid: record.bingLiID || this.bingLiID
        }
        pageName = 'zyblwsPdf.aspx'
      } else {
        params = {
          as_blid: this.bingLiID,
          as_gsdm: record.geshiDM || record.geShiDM,
          as_zyid: this.patientInfo.zhuYuanID,
          as_yhid: this.yongHuID,
          as_wsid: record.id || 0,
          as_wslx: record.wenshuLX || record.wenShuLX,
          tmpid: Math.random()
        }
        pageName = 'blwsdetail.aspx'
      }

      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .filter(([, value]) => value !== null && value !== undefined && value !== '')
        .map(([key, value]) => `${key}=${value}`)
        .join('&')

      // 返回完整URL
      this.iframeUrl = `${this.baseUrl}/zyblws/${pageName}?${queryString}`
    },

    // iframe加载完成
    handleIframeLoad() {
      console.log('文书iframe加载完成')
      this.$emit('iframe-loaded')
    },

    // 处理操作按钮点击
    handleAction(action) {
      const iframe = this.$refs.contentIframe
      if (!iframe || !iframe.contentWindow) {
        this.$message.error('iframe未加载完成')
        return
      }

      switch (action) {
        case 'save':
          this.handleSave(iframe)
          break
        case 'delete':
          this.handleDelete(iframe)
          break
        case 'print':
          this.handlePrint(iframe)
          break
        default:
          this.$emit('custom-action', { action, iframe })
      }
    },

    // 保存操作
    async handleSave(iframe) {
      if (!this.currentRecord) {
        this.$message.error('没有可保存的记录')
        return
      }

      try {
        // 使用简化的iframe通信
        const message = await iframeCommunication.save(this.currentRecord.id || 'default', iframe)
        this.$message.success(message)

        // 保存成功后刷新已保存记录列表
        setTimeout(async () => {
          await this.fetchSavedRecords()
        }, 1000)
        this.$emit('save-success')
      } catch (error) {
        console.error('保存失败', error)
        this.$message.error('保存出错: ' + (error.message || '未知错误'))
      }
    },

    // 删除操作
    async handleDelete(iframe) {
      if (!this.currentRecord) {
        this.$message.error('没有可删除的记录')
        return
      }

      try {
        // 确认删除操作
        await this.$confirm(`确定要删除这条${this.documentTabLabel}吗？`, '删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 使用简化的iframe通信
        const message = await iframeCommunication.delete(this.currentRecord.id || 'default', iframe)
        this.$message.success(message)

        // 删除成功后刷新已保存记录列表
        setTimeout(async () => {
          await this.fetchSavedRecords()
        }, 1000)
        // 清空当前选中记录
        this.currentRecord = null
        this.iframeUrl = ''
        this.$emit('delete-success')
      } catch (error) {
        if (error === 'cancel') {
          // 用户取消删除，不做任何操作
          return
        }
        console.error('删除失败', error)
        this.$message.error('删除出错: ' + (error.message || '未知错误'))
      }
    },

    // 打印操作
    async handlePrint(iframe) {
      if (!this.currentRecord) {
        this.$message.error('没有可打印的记录')
        return
      }

      try {
        // 使用简化的iframe通信
        const message = await iframeCommunication.print(this.currentRecord.id || 'default', iframe)
        this.$message.success(message)
        this.$emit('print-success')
      } catch (error) {
        console.error('打印失败', error)
        this.$message.error('打印出错: ' + (error.message || '未知错误'))
      }
    }
  }
}
</script>

<style scoped lang="scss">
.medical-document-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .medical-document-layout {
    display: flex;
    height: 100%;
    gap: 8px;
    padding: 8px;

    .left-panel {
      width: 380px;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .tabs-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        ::v-deep .el-tabs {
          display: flex;
          flex-direction: column;
          height: 100%;

          .el-tabs__content {
            flex: 1;
            overflow: hidden;
            padding: 10px 0 0;
          }

          .el-tab-pane {
            height: 100%;
          }
        }

        .saved-records-list,
        .document-content {
          height: 100%;
          display: flex;
          flex-direction: column;
        }

        .document-list {
          flex: 1;
          overflow: hidden;
        }

        .red-text {
          color: #f56c6c;
        }
      }
    }

    .right-panel {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

      .right-header {
        padding: 12px;
        border-bottom: 1px solid #ebeef5;

        .header-content {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .record-title {
            font-size: 16px;
            font-weight: bold;
          }

          .action-buttons {
            display: flex;
          }
        }
      }

      .iframe-container {
        flex: 1;
        position: relative;
        overflow: hidden;

        iframe {
          width: 100%;
          height: 100%;
          border: none;
        }

        .no-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #909399;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #c0c4cc;
          }

          p {
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }
  }
}
</style>
