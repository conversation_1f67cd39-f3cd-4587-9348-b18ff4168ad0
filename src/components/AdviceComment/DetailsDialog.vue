<template>
  <default-dialog
    :visible="visible"
    width="1600px"
    pop-type="custom"
    @cancel="updateVisible(false)"
    @open="initDialog()"
  >
    <template #title>病案号：{{ bingAnHao }}, 当前病人：{{ bingRenXM }}</template>
    <span>
      <div class="dialog-component" style="height: 560px; overflow-y: auto">
        <el-table
          border
          size="mini"
          :data="dataList"
          height="100%"
          max-height="560px"
          :span-method="objectSpanMethod"
        >
          <el-table-column prop="yiZhuLXMC" width="120" label="医嘱类型"></el-table-column>
          <el-table-column prop="yiZhuSJ" width="150" label="开医嘱时间"></el-table-column>
          <el-table-column prop="yiZhuNR" width="300" label="医嘱内容">
            <template #default="scope">
              <el-button v-if="scope.row.yiZhuLX === '5' || scope.row.yiZhuLX === '10'" type="text">
                {{ scope.row.yiZhuNR }}
              </el-button>
              <div v-else v-html="scope.row.yiZhuNR"></div>
            </template>
          </el-table-column>
          <el-table-column prop="tianShu" label="已用天数"></el-table-column>
          <el-table-column prop="yiShengQM" label="医师签名"></el-table-column>
          <el-table-column prop="jiHuaKSSJ" width="150" label="预计执行时间"></el-table-column>
          <el-table-column prop="chiXuTS" label="持续天数"></el-table-column>
          <el-table-column prop="daoChuRY" label="导出护士"></el-table-column>
          <el-table-column prop="daoChuSJ" width="150" label="导出时间"></el-table-column>
          <el-table-column prop="yiZhuZT" label="医嘱状态">
            <template #default="scope">
              <el-tag v-if="scope.row.yiZhuZT === '1'">正常</el-tag>
              <el-tag v-else-if="scope.row.yiZhuZT === '0'" type="danger">停止</el-tag>
              <el-tag v-else>其他</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="chaKan" width="80" label="查看限制范围" align="center">
            <template #default="scope">
              <el-button type="text" size="medium" @click="viewLimits(scope.row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="dianPing" width="160" label="点评" align="center">
            <template #default="scope">
              <el-button type="text" size="medium" @click="adviceComment(scope.row)">
                <span v-if="getCommentName(scope.row)" style="color: #f35656">
                  {{ getCommentName(scope.row) }}
                </span>
                <span v-else-if="isEdit">点评</span>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <comment-dialog
        :visible.sync="commentVisible"
        :yi-zhu-x-x="currentYiZhuXX"
        :scmxmx="currentScmxmx"
        :shai-cha-i-d="shaiChaID"
        :identity-type="isEdit ? 0 : 3"
      />
    </span>
    <template #footer>
      <el-button v-if="isEdit" style="color: #f35656">提 交(不合理)</el-button>
      <el-button v-if="isEdit" style="color: #008000">提 交(合理)</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </template>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import CommentDialog from './CommentDialog.vue'
import { getAdmissionList } from '@/api/inpatient-order'
import { drugLimitsDo, getScmxmxByScidBlid } from '@/api/medical-quality'

export default {
  name: 'DetailsDialog',
  components: {
    DefaultDialog,
    CommentDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    //是否可编辑 仅筛查者页面可编辑
    isEdit: {
      type: Boolean,
      default: false
    },
    advice: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      commentVisible: false,
      dataList: [],
      currentYiZhuXX: null, //当前医嘱信息
      currentScmxmx: null, //当前筛查明细
      scmxmxList: [], //明细列表
      searchValue: '',
      currentPage: 1
    }
  },

  computed: {
    bingLiID() {
      return this.advice?.bingLiID
    },
    shaiChaID() {
      return this.advice?.shaiChaID
    },
    ruYuanRQ() {
      return this.advice?.bingRenXX?.ruYuanRQ
    },
    bingAnHao() {
      return this.advice?.bingRenXX?.empi
    },
    bingRenXM() {
      return this.advice?.bingRenXX?.xingMing
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    async viewLimits(row) {
      console.log(row)

      return

      const res = await drugLimitsDo({
        yaoPinID: this.bingLiID
      })
      console.log()
      if (res.hasError === 0) {
      }
    },
    adviceComment(row) {
      const scmxmx = this.scmxmxList.find((item) => {
        return Number(item.yiZhuID) === row.yiZhuID
      })
      this.currentYiZhuXX = row
      this.currentScmxmx = scmxmx
      this.commentVisible = true
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        if (row.rowspan !== 0) {
          return {
            rowspan: row.rowspan,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    async initDialog() {
      if (this.shaiChaID) {
        const res = await getScmxmxByScidBlid({
          bingLiID: this.bingLiID,
          shaiChaID: this.shaiChaID,
          yiZhuLX: '2'
        })
        if (res.hasError === 0) {
          this.scmxmxList = res.data
        }
      }
      this.getDataList()
    },
    getCommentName(row) {
      const scmxmx = this.scmxmxList.find((item) => {
        return Number(item.yiZhuID) === row.yiZhuID
      })
      let buttonName = ''
      if (scmxmx && scmxmx.yaoXueBuNR) {
        buttonName += '已点评'
      } else {
        return buttonName
      }

      if (scmxmx && scmxmx.yiShengNR) {
        buttonName += '→反对'
      } else {
        return buttonName
      }

      if (scmxmx && scmxmx.zhuanJiaNR) {
        buttonName += '→不合理'
      } else {
        return buttonName
      }
    },
    async getDataList() {
      const res = await getAdmissionList({
        bingLiID: this.bingLiID,
        chaXunMS: '0',
        kaiShiSJ: this.ruYuanRQ,
        jieShuSJ: this.dateFormat(),
        paiXuMS: '1',
        yiZhuLBs: '1'
      })

      if (res.hasError === 0) {
        for (let i = 0; i < res.data.length; i++) {
          res.data[i]['rowspan'] = 1
          for (let j = i + 1; j < res.data.length; j++) {
            if (res.data[i]['yiZhuLXMC'] === res.data[j]['yiZhuLXMC']) {
              res.data[i]['rowspan'] += 1
              res.data[j]['rowspan'] = 0
              if (j === res.data.length - 1) {
                i = j
              }
            } else {
              i = j - 1
              break
            }
          }
        }
        this.dataList = res.data
      }
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hours = date.getHours()
      const minutes = date.getMinutes()
      const seconds = date.getSeconds()
      return (
        year +
        '-' +
        (month < 10 ? '0' + month : month) +
        '-' +
        (day < 10 ? '0' + day : day) +
        ' ' +
        (hours < 10 ? '0' + hours : hours) +
        ':' +
        (minutes < 10 ? '0' + minutes : minutes) +
        ':' +
        (seconds < 10 ? '0' + seconds : seconds)
      )
    },
    confirmClick() {
      this.updateVisible(false)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-component {
  padding: 0 10px;
  .search-head {
    button {
      --color-primary: #a66dd4;
    }
    :deep(.el-button--primary:hover),
    :deep(.el-button--primary:focus) {
      background: #ce8be0;
      border-color: #ce8be0;
    }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
}
</style>
