<template>
  <default-dialog
    :visible="visible"
    title="点评单"
    width="800px"
    append-to-body
    pop-type="custom"
    @cancel="updateVisible(false)"
    @open="initDialog()"
  >
    <div class="dialog-component" style="min-height: 250px; overflow-y: auto">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="筛查者点评内容" name="0"></el-tab-pane>
        <el-tab-pane v-if="identityType >= 1" label="医生反馈" name="1"></el-tab-pane>
        <el-tab-pane v-if="identityType >= 2" label="专家点评内容" name="2"></el-tab-pane>
      </el-tabs>

      <table style="margin-top: 10px">
        <tbody>
          <tr>
            <td class="info-label"><div>医嘱内容:</div></td>
            <td class="info-value">
              <div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 5px">
                  <span>
                    <el-tag v-if="yiZhuXX?.yiZhuZT === '1'">正常</el-tag>
                    <el-tag v-else-if="yiZhuXX?.yiZhuZT === '0'" type="danger">停止</el-tag>
                    <el-tag v-else>其他</el-tag>
                  </span>
                  <span>
                    <el-button
                      v-if="yiZhuXX?.yiZhuLX === '5' || yiZhuXX?.yiZhuLX === '10'"
                      type="text"
                    >
                      {{ yiZhuXX?.yiZhuNR }}
                    </el-button>
                    <div v-else v-html="yiZhuXX?.yiZhuNR"></div>
                  </span>
                  <span>
                    {{ yiZhuXX?.tianShu }}
                  </span>
                </div>
                <div style="display: flex; justify-content: space-between; align-items: center">
                  <span>
                    {{ yiZhuXX?.yiShengQM }}
                  </span>
                  <span>
                    {{ yiZhuXX?.jiHuaKSSJ }}
                  </span>
                  <span>
                    {{ yiZhuXX?.daoChuRY }}
                  </span>
                  <span>
                    {{ yiZhuXX?.daoChuSJ }}
                  </span>
                </div>
              </div>
            </td>
          </tr>
          <tr v-if="activeTab === '0'">
            <td class="info-label"><div>不合理原因:</div></td>
            <td class="info-value">
              <el-select v-model="yaoXueBuXX" style="width: 100%" placeholder="">
                <el-option
                  v-for="item in XXOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </td>
          </tr>
          <tr v-if="activeTab === '0'">
            <td class="info-label"><div>点评建议:</div></td>
            <td class="info-value">
              <el-input v-model="yaoXueBuNR" type="textarea" :rows="4" />
            </td>
          </tr>
          <tr v-if="activeTab === '1'">
            <td class="info-label"><div>是否同意:</div></td>
            <td class="info-value">
              <el-radio v-model="yiShengXX" label="1">同意</el-radio>
              <el-radio v-model="yiShengXX" label="0">反对</el-radio>
            </td>
          </tr>
          <tr v-if="activeTab === '1'">
            <td class="info-label"><div>备注理由:</div></td>
            <td class="info-value">
              <el-input v-model="yiShengNR" type="textarea" :rows="4" />
            </td>
          </tr>
          <tr v-if="activeTab === '2'">
            <td class="info-label"><div>是否合理:</div></td>
            <td class="info-value">
              <el-radio v-model="zhuanJiaXX" label="1">医嘱合理</el-radio>
              <el-radio v-model="zhuanJiaXX" label="0">医嘱不合理</el-radio>
            </td>
          </tr>
          <tr v-if="activeTab === '2'">
            <td class="info-label"><div>备注理由:</div></td>
            <td class="info-value">
              <el-input v-model="zhuanJiaNR" type="textarea" :rows="4" />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <template #footer>
      <el-button type="primary" @click="confirmClick">保 存</el-button>
      <el-button v-if="stateConfig.editType === '1'" style="color: #f35656" @click="() => {}">
        撤回点评
      </el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </template>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { mapState } from 'vuex'
import { saveSingleDp } from '@/api/medical-quality'

export default {
  name: 'CommentDialog',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    shaiChaID: {
      type: Number,
      default: null
    },
    yiZhuXX: {
      type: Object,
      default: null
    },
    scmxmx: {
      type: Object,
      default: null
    },
    identityType: {
      type: Number,
      default: 0 //身份类型 0筛查者 1医生 2专家 3其他仅查看
    }
  },
  data() {
    return {
      activeTab: '0',

      yaoXueBuXX: '9999', //提交选项
      yaoXueBuNR: '', //提交内容
      yiShengXX: null,
      yiShengNR: null,
      zhuanJiaXX: null,
      zhuanJiaNR: null,

      stateConfig: {
        editType: '0', //操作状态 0未点评 1已点评
        identityType: '0' //身份类型 0筛查者 1医生 2专家 3其他仅查看
      },
      XXOptions: [
        {
          label: '用药与临床诊断不符',
          value: '0001'
        },
        {
          label: '用法用量不适宜',
          value: '0002'
        },
        {
          label: '选用剂型与给药途径不适宜',
          value: '0003'
        },
        {
          label: '联合用药不适宜',
          value: '0004'
        },
        {
          label: '存在配伍禁忌',
          value: '0005'
        },
        {
          label: '存在禁忌症',
          value: '0006'
        },
        {
          label: '其他',
          value: '9999'
        }
      ]
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo
    })
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    async initDialog() {
      if (this.scmxmx && this.scmxmx.yaoXueBuNR) {
        this.yaoXueBuXX = this.scmxmx.yaoXueBuXX
        this.yaoXueBuNR = this.scmxmx.yaoXueBuNR
        if (this.scmxmx.yiShengNR) {
          this.yiShengXX = this.scmxmx.yiShengXX
          this.yiShengNR = this.scmxmx.yiShengNR
          if (this.scmxmx.zhuanJiaNR) {
            this.zhuanJiaXX = this.scmxmx.zhuanJiaXX
            this.zhuanJiaNR = this.scmxmx.zhuanJiaNR
          }
        }
        this.stateConfig.editType = '1'
      } else {
        this.yaoXueBuXX = '9999'
        this.yaoXueBuNR = ''
        this.stateConfig.editType = '0'
      }
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hours = date.getHours()
      const minutes = date.getMinutes()
      const seconds = date.getSeconds()
      return (
        year +
        '-' +
        (month < 10 ? '0' + month : month) +
        '-' +
        (day < 10 ? '0' + day : day) +
        ' ' +
        (hours < 10 ? '0' + hours : hours) +
        ':' +
        (minutes < 10 ? '0' + minutes : minutes) +
        ':' +
        (seconds < 10 ? '0' + seconds : seconds)
      )
    },
    async confirmClick() {
      if (!this.yaoXueBuNR) {
        this.$message.error('未填写点评单')
        return
      }
      const res = await saveSingleDp({
        bingLiID: this.yiZhuXX?.bingLiID,
        shaiChaID: this.shaiChaID,
        yiZhuID: this.yiZhuXX?.yiZhuID,
        yiZhuLX: this.yiZhuXX?.yiZhuLX,
        yaoXueBuNR: this.yaoXueBuNR,
        yaoXueBuXX: this.yaoXueBuXX,
        yaoXueBuYHID: this.userInfo.yongHuID
      })

      if (res.hasError === 0) {
        this.$message.success('保存成功')
        this.updateVisible(false)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-component {
  padding: 0 10px;
  .search-head {
    button {
      --color-primary: #a66dd4;
    }
    :deep(.el-button--primary:hover),
    :deep(.el-button--primary:focus) {
      background: #ce8be0;
      border-color: #ce8be0;
    }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 6px;
  }
  .info-label {
    text-align: right;
    width: 150px;
    background-color: #eaf0f9;
  }
  .info-value {
    background-color: #ffffff;
  }
}
</style>
