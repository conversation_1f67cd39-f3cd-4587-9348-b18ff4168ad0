<template>
  <div>
    <el-popover placement="bottom" width="270" trigger="click">
      <popover-window
        ref="socketWindowRef"
        :user-state="userState"
        :msg-num.sync="msgNum"
        @message-dialog="
          (content) => {
            dialogVisible = true
            messageContent = content
          }
        "
      ></popover-window>
      <template #reference>
        <el-badge
          :value="msgNum > 0 ? msgNum : ''"
          :max="maxTip"
          class="msg-badge"
          style="user-select: none"
          @click.stop
        >
          <div class="right-menu-item hover-effect">
            <slot name="icon">
              <svg-icon icon-class="lingdang" class="icon-test" />
            </slot>
          </div>
        </el-badge>
      </template>
    </el-popover>

    <el-dialog :visible.sync="dialogVisible">
      <span slot="title">
        <span class="dialog-title">
          <span class="bar" />
          公告信息
        </span>
      </span>
      <span>
        <div style="padding: 10px">
          {{ messageContent }}
        </div>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">确 认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import PopoverWindow from './components/PopoverWindow.vue'
export default {
  name: 'BellPendant',
  components: { PopoverWindow },
  props: {
    userState: {
      type: Object,
      default: () => ({})
    },
    maxTip: {
      type: Number,
      default: 99
    },
    getToken: {
      type: Function,
      default: () => ''
    }
  },
  data() {
    return {
      msgNum: 0,
      dialogVisible: false,
      messageContent: ''
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
::v-deep .el-popover__reference-wrapper {
  .right-menu-item {
    display: flex;
    align-items: center;
    padding: 0 7px;
    font-size: 14px;
    color: rgb(23 28 40 / 45%);

    &.hover-effect {
      cursor: pointer;
      transition: background 0.3s;

      &:hover {
        background: rgb(0 0 0 / 2.5%);
      }
    }
    img {
      width: 1em;
      height: 1em;
      overflow: hidden;
      vertical-align: -0.15em;
      fill: currentcolor;
      border: none;
    }
  }
}

.msg-badge {
  cursor: pointer;

  ::v-deep .el-badge__content {
    right: 14px;
    min-width: 17px;
    max-width: 25px;
    height: 17px;
    padding: 0 2px;
    line-height: 14px;
    background-color: #d54941;
  }
}

.dialog-title {
  font-size: 16px;
  .bar {
    border-left: 3px solid #356ac5;
    padding-left: 5px;
  }
}
:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
