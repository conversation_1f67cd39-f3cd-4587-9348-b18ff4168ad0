<template>
  <div>
    <default-dialog
      :visible="visible"
      title="二级密码"
      width="400px"
      confirm-button-text="保存"
      cancel-button-text="关闭"
      @confirm="submitForm"
      @cancel="cancelDialog"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="ruleForm.username"></el-input>
        </el-form-item>
        <el-form-item label="二级密码" prop="password">
          <el-input v-model="ruleForm.password" type="password"></el-input>
        </el-form-item>
      </el-form>
    </default-dialog>
  </div>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import JSEncrypt from 'jsencrypt/bin/jsencrypt'
import CryptoJS from 'crypto-js'
import { checkSecendPassword, getDZQMBYhid } from '@/api/inpatient-order'
import { mapState } from 'vuex'
export default {
  name: 'SecondaryPassword',
  components: {
    DefaultDialog
  },
  props: {
    //(必传)
    visible: {
      type: Boolean,
      default: false
    },
    //类别 1:个人签名 2:个人电子印章 3:部门电子印章 默认：1 ,示例值(1)
    leibie: {
      type: String,
      default: '1'
    },
    //图片类型 1：原图 2：缩略图 默认：1,示例值(1)
    tupianlx: {
      type: String,
      default: '1'
    },
    //是否获取base64数据
    getbase64: {
      type: Boolean,
      default: false
    },
    //是否获取电子签名
    qmtype: {
      type: Boolean,
      default: false
    }
  },
  emits: ['confirm', 'update:visible'],
  data() {
    return {
      DialogValue: false,
      ruleForm: {
        username: '',
        password: ''
      },
      rules: {
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapState({
      patientInfo: ({ patient }) => patient.patientInit
    })
  },
  mounted() {
    console.log(this.patientInfo)
  },
  methods: {
    async submitForm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          const pwd = this.ruleForm.password
          const account = this.ruleForm.username.toUpperCase()
          if (pwd && account) {
            // MD5 加密
            const md5_pwd = CryptoJS.MD5(pwd).toString().toUpperCase() // md5(pwd).toString().toUpperCase()
            const queryStr = `erJiMM=${md5_pwd}&yongHuZH=${account}`
            // RSA 加密
            const publicKeystr =
              'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDJKmw0cPKMy1jGi5zQd7T9fPwaI6NYSZakQkHXBHObqRQwhfeAsKLkHn1cKfyf3WxSZq791GoQRMfKrS9SVi7fpDLvGQwLyCa7W46II4+cC0fPYXdHZSaXT59Ox/YSZsXr+Al2NEXPBPOBfL8zfeRKyqhS76Ind7C2nSRckWXmKwIDAQAB'
            const jsRsa = new JSEncrypt()
            jsRsa.setPublicKey(publicKeystr)
            const RSApassword = jsRsa.encrypt(queryStr)

            // 十位时间戳
            const timestamp = Math.round(new Date().getTime() / 1000).toString()

            //HmacSHA1 签名
            const secret = 'wzhospital@^&*()' // 密钥
            // const hash = CryptoJS.HmacSHA1(RSApassword, secret);
            const signStr = CryptoJS.HmacSHA1(RSApassword, secret).toString(CryptoJS.enc.Base64) // 将加密结果转换为Base64字符串

            //十位时间戳 + HmacSHA1 签名
            // let ms = timestamp + signStr.replaceAll('+','-').replaceAll('/','_')
            let ms = timestamp + signStr.replace(/\+/g, '-').replace(/\//g, '_')
            const res = await checkSecendPassword({
              param: RSApassword,
              'ms-sign': ms
            })
            if (res.data) {
              if (this.qmtype) {
                const params = {
                  getBase64: this.getBase64 || false,
                  leiBie: this.leiBie || 1,
                  tuPianLX: this.tuPianLX || 1,
                  yongHuID: res.data.yongHuID
                }
                //是否获取电子签名
                const msg = await getDZQMBYhid(params)
                if (msg.hasError === 0) {
                  res.data.QMdata = msg.data[0]
                  this.$emit('confirm', res.data)
                  this.$emit('update:visible', false)
                  this.ruleForm = { username: '', password: '' }
                } else {
                  this.$message.error('签名失败！')
                }
              } else {
                this.$emit('confirm', res.data)
                this.$emit('update:visible', false)
                this.ruleForm = { username: '', password: '' }
              }
            } else {
              this.$message.error('登录失败！')
            }
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    // 取消
    cancelDialog() {
      this.$emit('update:visible', false)
      this.$emit('cancel')
      this.ruleForm = { username: '', password: '' }
    }
  }
}
</script>

<style scoped>
.screenfull-svg {
  display: inline-block;
  cursor: pointer;
  fill: #5a5e66;
  width: 20px;
  height: 20px;
  vertical-align: 10px;
}
</style>
