<template>
  <default-dialog
    :visible="visible"
    width="25%"
    title="科室列表"
    @confirm="confirmClick"
    @cancel="updateVisible(false)"
    @open="initDialog()"
  >
    <span>
      <div class="dialog-component">
        <div class="search-head">
          <el-input
            ref="searchInput"
            v-model="searchValue"
            style="width: 80%"
            placeholder="请输入科室名称"
          ></el-input>
          <el-button type="primary" @click="searchData()">查询</el-button>
        </div>
        <el-table
          :data="currentList"
          style="width: 100%; margin-top: 10px"
          stripe
          border
          highlight-current-row
          @current-change="
            (row) => {
              selectItem = row
            }
          "
        >
          <el-table-column
            v-for="(column, index) in [
              { value: 'mingCheng', label: '科室名称', props: { fixed: true } }
            ]"
            :key="index"
            :prop="column.value"
            :label="column.label"
            v-bind="column.props"
          ></el-table-column>
        </el-table>
        <el-pagination
          background
          layout="total, prev, pager, next"
          :current-page.sync="currentPage"
          :page-size="11"
          :pager-count="5"
          :total="dataList.length"
        ></el-pagination>
      </div>
    </span>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { getZhuanKeList } from '@/api/medical-quality'

export default {
  name: 'SelectDrawer',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataList: [],
      searchValue: '',
      currentPage: 1,
      selectItem: null,
      benZhuanKe: true
    }
  },
  computed: {
    currentList() {
      const start = (this.currentPage - 1) * 11
      return this.dataList.slice(start, start + 11)
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    async initDialog() {
      this.searchValue = ''
      this.searchData()
    },
    async searchData() {
      const res = await getZhuanKeList()
      if (res.hasError === 0) {
        this.$refs.searchInput.focus()
        this.dataList = [
          {
            daiMa: '0',
            mingCheng: '所有专科'
          },
          ...res.data
        ].filter((item) => {
          return item.mingCheng?.indexOf(this.searchValue) > -1
        })
      }
    },
    confirmClick() {
      this.updateVisible(false)
      this.$emit('confirm', {
        buMenMC: this.selectItem.mingCheng,
        buMenID: Number(this.selectItem.daiMa)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-component {
  padding: 0 10px;
  .search-head {
    button {
      --color-primary: #a66dd4;
    }
    :deep(.el-button--primary:hover),
    :deep(.el-button--primary:focus) {
      background: #ce8be0;
      border-color: #ce8be0;
    }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
  // table {
  //   margin-top: 10px;
  //   width: 100%;
  // }
  // th,
  // td {
  //   border: 1px solid #ddd;
  //   border-collapse: collapse; /* 移除表格内边框间的间隙 */
  //   height: 35px;
  //   padding: 10px;
  // }
  // th,
  // .tr-two {
  //   background-color: #eaf0f9;
  // }
  // .select-tr {
  //   background-color: #6787cc;
  //   color: #ffffff;
  // }
}
</style>
