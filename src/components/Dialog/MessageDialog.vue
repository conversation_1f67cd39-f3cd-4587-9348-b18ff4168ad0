<template>
  <default-dialog
    :class="['info-dialog', type]"
    :visible="visible"
    :size="size"
    :width="width"
    v-bind="$attrs"
    @confirm="confirm"
    @cancel="$emit('cancel')"
    @update:visible="$emit('update:visible', $event)"
    @close="$emit('close')"
  >
    <template #title>
      <slot name="title">{{ title }}</slot>
    </template>
    <slot></slot>
  </default-dialog>
</template>

<script>
import DefaultDialog from './DefaultDialog.vue'
export default {
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: 'mini'
    },
    title: {
      type: String,
      default: '信息提示'
    },
    width: {
      type: String,
      default: '50%'
    },
    type: {
      type: String,
      default: 'warning'
    }
  },
  emits: {
    /**
     * 点击取消按钮
     */
    cancel() {},
    /**
     * 点击确认按钮
     */
    confirm() {},
    /**
     * 更新状态
     * @param visible
     */
    'update:visible'(visible) {}
  },
  methods: {
    confirm() {
      this.$emit('confirm')
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.info-dialog {
  ::v-deep {
    .el-dialog {
      &__header {
        padding: 15px 10px 13px;
        border-bottom: 0;

        &::before {
          content: '';
          display: inline-block;
          width: 20px;
          height: 20px;
          margin-top: -2px;
          background: url('../../assets/images/warning.png') 0 0/100% 100% no-repeat;
        }
      }

      &__body {
        font-size: 14px;
        color: var(--color-text-placeholder);
        padding: 0 var(--common-margin) 6px calc(var(--common-margin) * 2.6);
        padding: 20px;
      }
      &__footer {
        border: none;
      }
    }
  }
}
.success {
  ::v-deep .el-dialog {
    &__header {
      &::before {
        background: url('../../assets/images/success.png') 0 0/100% 100% no-repeat;
      }
    }
  }
}

.error {
  ::v-deep .el-dialog {
    &__header {
      &::before {
        background: url('../../assets/images/error.png') 0 0/100% 100% no-repeat;
      }
    }
  }
}
</style>
