<template>
  <default-dialog
    :visible="visible"
    width="25%"
    title="医生列表"
    @confirm="confirmClick"
    @cancel="updateVisible(false)"
    @open="initDialog()"
  >
    <span>
      <div class="dialog-component">
        <div class="search-head">
          <el-input
            ref="searchInput"
            v-model="searchValue"
            style="width: 80%"
            placeholder="请输入人员拼音/五笔码"
            @change="searchData"
          ></el-input>
          <el-button type="primary" @click="searchData">查询</el-button>
        </div>
        <el-table
          :data="currentList"
          style="width: 100%; margin-top: 10px"
          stripe
          border
          highlight-current-row
          @current-change="
            (row) => {
              selectItem = row
            }
          "
        >
          <el-table-column prop="xingMing" label="姓名"></el-table-column>
          <el-table-column prop="zhongShenDM" label="终身码"></el-table-column>
        </el-table>
        <el-pagination
          background
          layout="total, prev, pager, next"
          :current-page.sync="currentPage"
          :page-size="11"
          :pager-count="5"
          :total="dataList.length"
        ></el-pagination>
      </div>
    </span>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { getShouShuRyk } from '@/api/surgical-notice'

export default {
  name: 'DoctorDrawer',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      listData: [],
      dataList: [],
      searchValue: '',
      currentPage: 1,
      selectItem: null,
      benZhuanKe: true
    }
  },
  computed: {
    currentList() {
      const start = (this.currentPage - 1) * 11
      return this.dataList.slice(start, start + 11)
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          this.$refs.searchInput.focus()
        })
      }
    }
  },
  mounted() {
    this.initDialog()
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    async initDialog() {
      this.searchValue = ''
      const res = await getShouShuRyk()
      if (res.hasError === 0) {
        this.listData = res.data
        this.dataList = res.data
      }
    },
    async searchData() {
      if (this.searchValue) {
        console.log(this.searchValue.toUpperCase())
        this.dataList = []
        this.listData.forEach((item) => {
          if (
            item.pinYin.indexOf(this.searchValue.toUpperCase()) >= 0 ||
            item.wuBi.indexOf(this.searchValue) >= 0
          ) {
            this.dataList.push(item)
          }
        })
      }
    },
    confirmClick() {
      this.updateVisible(false)
      this.$emit('confirm', this.selectItem)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-component {
  padding: 0 10px;
  .search-head {
    button {
      --color-primary: #a66dd4;
    }
    :deep(.el-button--primary:hover) {
      background: #ce8be0;
      border-color: #ce8be0;
    }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
  // table {
  //   margin-top: 10px;
  //   width: 100%;
  // }
  // th,
  // td {
  //   border: 1px solid #ddd;
  //   border-collapse: collapse; /* 移除表格内边框间的间隙 */
  //   height: 35px;
  //   padding: 10px;
  // }
  // th,
  // .tr-two {
  //   background-color: #eaf0f9;
  // }
  // .select-tr {
  //   background-color: #6787cc;
  //   color: #ffffff;
  // }
}
</style>
