<template>
  <transition :name="isDlideUp ? 'slide-up' : ''">
    <lock-screen-page v-if="isLock && isMouted" />
  </transition>
</template>

<script>
import LockScreenPage from './lockscreen.vue'
import { initTime } from '@/store/modules/lockscreen'
import { mapGetters } from 'vuex'
export default {
  name: 'LockScreen',
  components: {
    LockScreenPage
  },
  data() {
    return {
      isMouted: false,
      timer: undefined, // 定时器
      isDlideUp: false // 启用锁屏动画
    }
  },
  computed: {
    ...mapGetters({
      isLock: 'lockscreen/isLock',
      lockTime: 'lockscreen/lockTime'
    })
  },
  watch: {
    isLock(e) {
      console.log('锁屏 isLock' + this.isLock + ' isMouted:' + this.isMouted)
      if (e === false) {
        this.timekeeping()
      }
    }
  },
  created() {
    console.log('锁屏created')
  },
  mounted() {
    console.log('锁屏mounted')
    setTimeout(() => {
      this.isMouted = true
    })
    // 关闭锁屏初始化时的动画
    setTimeout(() => {
      this.isDlideUp = true
    }, 16)
    console.log('锁屏 isLock' + this.isLock + ' isMouted:' + this.isMouted)
    this.timekeeping()
    document.addEventListener('mousedown', this.timekeeping)
  },
  destroyed() {
    console.log('锁屏销毁')
    document.removeEventListener('mousedown', this.timekeeping)
    clearInterval(this.timer)
  },
  methods: {
    timekeeping() {
      clearInterval(this.timer)
      if (this.isLock) {
        return
      } else {
        // 设置不锁屏
        this.$store.dispatch('lockscreen/setLock', false)
        // 重置锁屏时间
        this.$store.dispatch('lockscreen/setLockTime', initTime)
        this.timer = setInterval(() => {
          // 锁屏倒计时递减
          if (this.isLock) {
            return clearInterval(this.timer)
          } else {
            this.$store.dispatch('lockscreen/setLockTime', this.lockTime - 1)
            //  console.log('倒计时：' + this.lockTime)
            if (this.lockTime <= 0) {
              // 设置锁屏
              this.$store.dispatch('lockscreen/setLock', true)
              return clearInterval(this.timer)
            }
          }
        }, 1000)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.slide-up-enter-active {
  animation: slide-up 0.5s;
}

.slide-up-leave-active {
  animation: slide-up 0.5s reverse;
}

@keyframes slide-up {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0);
  }
}
</style>
