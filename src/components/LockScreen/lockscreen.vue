<template>
  <div
    :class="{ unLockLogin: isShowLogin }"
    class="lockscreen"
    @keyup="unLockLogin(true)"
    @mousedown.stop
    @contextmenu.prevent
  >
    <template v-if="!isShowLogin">
      <div class="lock-box">
        <div class="lock">
          <span class="lock-icon" title="解锁屏幕" @click="unLockLogin(true)">
            <div class="anticon-lock">
              <i class="el-icon-lock" />
            </div>

            <div class="anticon-unlock">
              <i class="el-icon-unlock" />
            </div>
          </span>
        </div>
        <h6 class="tips">点击解锁</h6>
      </div>
      <!-- 小米 / 华为 充电-->
      <!-- <component
        :is="randomCompName"
        :battery="battery"
        :battery-status="batteryStatus"
        :calc-discharging-time="calcDischargingTime"
      /> -->
    </template>
    <template v-if="isShowLogin">
      <div class="login-box">
        <el-avatar icon="el-icon-user-solid" :size="50" />
        <div class="username">
          {{ userName }}
        </div>

        <el-input
          v-model="loginForm.password"
          type="password"
          autofocus
          placeholder="请输入登录密码"
          size="large"
          @keyup.enter.native="throttleLogin"
        >
          <!-- <template #enterButton>
            <LoadingOutlined v-if="state.loginLoading" />
            <arrow-right-outlined v-else />
          </template> -->
          <el-button slot="append" icon="el-icon-right" @click="onLogin" />
        </el-input>
        <a style="margin-top: 10px" @click="nav2login">重新登录</a>
      </div>
    </template>

    <template v-if="!isShowLogin && timeShowObj.month">
      <div class="local-time">
        <div class="time">{{ timeShowObj.hour }}:{{ timeShowObj.minutes }}</div>
        <div class="date">
          {{ timeShowObj.month }}月{{ timeShowObj.day }}号，星期{{ timeShowObj.week }}
        </div>
      </div>
      <div class="computer-status">
        <!-- <span :class="{ offline: !online }" class="network">
          <WifiOutlined class="network" />
        </span>
         <ApiOutlined /> -->
      </div>
    </template>
  </div>
</template>

<script>
import md5 from 'js-md5'
import { mapGetters } from 'vuex'
import { initTime } from '@/store/modules/lockscreen'
import { throttle } from 'lodash'
export default {
  name: 'LockScreenPage',
  data() {
    return {
      timer: undefined, // 定时器
      isShowLogin: false,
      loginLoading: false, // 正在登录
      loginForm: {
        username: '',
        password: ''
      },
      timeShowObj: {}
    }
  },
  computed: {
    ...mapGetters({
      userPwd: 'user/pwd',
      userName: 'user/userName'
    })
  },
  mounted() {
    this.updateTime()
    clearInterval(this.timer)
    this.timer = setInterval(() => this.updateTime(), 1000)
  },
  destroyed() {
    console.log('destroyed 销毁定时器')
    clearInterval(this.timer)
  },
  methods: {
    // 解锁登录
    unLockLogin(value) {
      this.isShowLogin = value
    },
    throttleLogin: throttle(function () {
      this.onLogin()
    }, 1000),
    onLogin() {
      console.log('点击登录')
      if (this.loginForm.password.trim() === '') {
        var el = this.$message({
          message: '请填写密码',
          type: 'warning',
          duration: 2000
        }).$el
        // var el = document.querySelector('.el-message')
        if (el) el.style.cssText = 'z-index:99999'
        return
      }
      this.loginLoading = true
      // 异步登录

      if (this.userPwd === md5(this.loginForm.password).toString().toUpperCase()) {
        console.log('密码正确!!')
        this.unLockLogin(false)
        this.$store.dispatch('lockscreen/setLock', false)
        this.$store.dispatch('lockscreen/setLockTime', initTime)
      } else {
        var el2 = this.$message({
          message: '密码错误!!!',
          type: 'warning',
          duration: 2000
        }).$el
        // var el = document.querySelector('.el-message')
        if (el2) el2.style.cssText = 'z-index:99999'
      }
      this.loginLoading = false
    },
    // 重新登录
    nav2login() {
      this.unLockLogin(false)
      this.$store.dispatch('lockscreen/setLock', false)
      this.$store.dispatch('user/logOut')
    },
    updateTime() {
      // console.log('更新时间', this.timeShowObj)
      const date = new Date()
      this.$set(this.timeShowObj, 'year', date.getFullYear())
      this.$set(this.timeShowObj, 'month', date.getMonth() + 1)
      this.$set(this.timeShowObj, 'week', '日一二三四五六'.charAt(date.getDay()))
      this.$set(this.timeShowObj, 'hour', this.addZero(date.getHours()))
      this.$set(this.timeShowObj, 'minutes', this.addZero(date.getMinutes()))
      this.$set(this.timeShowObj, 'day', date.getDate())

      // this.timeShowObj.year = date.getFullYear()
      // this.timeShowObj.month = date.getMonth() + 1
      // this.timeShowObj.week = '日一二三四五六'.charAt(date.getDay())
      // this.timeShowObj.getDay = date.getDate()
      // this.timeShowObj.hour = date.getHours()
      // this.timeShowObj.minutes = date.getMinutes()
    },
    addZero(i) {
      if (i < 10) {
        i = '0' + i
      }
      return i
    }
  }
}
</script>

<style lang="scss" scoped>
.lockscreen {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 9999;
  display: flex;
  overflow: hidden;
  color: white;
  background: #000;

  &.unLockLogin {
    background-color: rgba(25, 28, 34, 0.78);
    backdrop-filter: blur(7px);
  }

  .login-box {
    position: absolute;
    top: 45%;
    left: 50%;
    display: flex;
    transform: translate(-50%, -50%);
    flex-direction: column;
    justify-content: center;
    align-items: center;

    > * {
      margin-bottom: 14px;
    }

    .username {
      font-size: 30px;
    }
  }

  .lock-box {
    position: absolute;
    top: 12vh;
    left: 50%;
    font-size: 34px;
    transform: translateX(-50%);

    .tips {
      color: white;
      cursor: text;
    }

    .lock {
      display: flex;
      justify-content: center;

      .lock-icon {
        cursor: pointer;

        .anticon-unlock {
          display: none;
        }

        &:hover .anticon-unlock {
          display: initial;
        }

        &:hover .anticon-lock {
          display: none;
        }
      }
    }
  }

  .local-time {
    position: absolute;
    bottom: 60px;
    left: 60px;
    font-family: helvetica;

    .time {
      font-size: 70px;
    }

    .date {
      font-size: 40px;
    }
  }

  .computer-status {
    position: absolute;
    right: 60px;
    bottom: 60px;
    font-size: 24px;

    > * {
      margin-left: 14px;
    }

    .network {
      position: relative;

      &.offline::before {
        position: absolute;
        top: 50%;
        left: 50%;
        z-index: 10;
        width: 2px;
        height: 28px;
        background-color: red;
        content: '';
        transform: translate(-50%, -50%) rotate(45deg);
      }
    }
  }
}
</style>
