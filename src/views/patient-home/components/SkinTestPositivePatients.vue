<template>
  <el-table :data="tableData" border stripe size="mini">
    <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
    <el-table-column prop="shouShuName" label="药品名称"></el-table-column>
  </el-table>
</template>

<script>
import { getPositiveSkinTestRecords } from '@/api/patient'
import fetchData from '@/views/patient-home/components/mixin/fetchData'

export default {
  name: 'SkinTestPositivePatients',
  mixins: [fetchData],
  data() {
    return {
      tableData: []
    }
  },
  methods: {
    fetchData() {
      getPositiveSkinTestRecords({
        zhuanKeID: this.zhuanKeID,
        zhiLiaoZuID: this.zhiLiaoZuID
        // bingQuID: this.bingQuID
      }).then((res) => {
        if (res.hasError === 0) {
          this.tableData = []
          this.$emit('update:count', this.tableData.length)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
