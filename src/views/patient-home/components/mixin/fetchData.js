import { mapState } from 'vuex'

export default {
  props: {
    zhiLiaoZuID: {
      type: Number,
      default: 0
    },
    bingQuID: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  watch: {
    // zhuanKeID: {
    //   handler() {
    //     if (this.zhuanKeID) {
    //       this.fetchData()
    //     }
    //   },
    //   immediate: true
    // }
    // zhiLiaoZuID: {
    //   handler(val, oldVal) {
    //     if (val !== oldVal) {
    //       this.fetchData()
    //     }
    //   },
    //   immediate: true
    // },
    // bingQuID: {
    //   handler(val, oldVal) {
    //     if (val !== oldVal) {
    //       this.fetchData()
    //     }
    //   },
    //   immediate: true
    // }
  },
  methods: {}
}
