<template>
  <el-dialog
    title="修改医师备注"
    :visible="visible"
    width="600px"
    :close-on-click-modal="false"
    destroy-on-close
    @open="openDialog"
    @close="closeDialog"
  >
    <el-descriptions class="custom-descriptions" :column="1" border size="mini">
      <el-descriptions-item label="备注">
        <el-input
          ref="inputRef"
          :value="doctorRemark"
          type="textarea"
          :autosize="{ minRows: 2, maxRows: 4 }"
          placeholder="请输入备注"
          @input="(value) => $emit('update:doctorRemark', value)"
        />
      </el-descriptions-item>
    </el-descriptions>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="handleSubmit">保存</el-button>
      <el-button size="small" @click="closeDialog">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    doctorRemark: {
      type: String,
      default: ''
    }
  },
  emits: ['update:visible', 'update:doctor<PERSON>emark', 'confirm'],
  data() {
    return {}
  },
  methods: {
    handleSubmit() {
      this.$emit('confirm', this.doctorRemark)
    },
    openDialog() {
      this.$nextTick(() => {
        this.$refs.inputRef.focus()
      })
    },
    closeDialog() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped></style>
