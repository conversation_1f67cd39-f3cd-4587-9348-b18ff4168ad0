<template>
  <el-dialog
    :visible="visible"
    width="30%"
    @close="updateVisible(false)"
    @open="initBatchDischarge()"
  >
    <span slot="title">
      <span class="dialog-title">
        <span class="bar" />
        预出院列表
      </span>
    </span>
    <div class="dialog-component">
      <div class="search-head">
        <div style="width: 80px">请选择组</div>
        <el-select
          :value="zhiLiaoZuID"
          placeholder="选择治疗组"
          size="small"
          @change="handleChangeZhiLiaoZu"
        >
          <el-option
            v-for="item in zhiLiaoZuList"
            :key="item.zhiLiaoZuID"
            :label="item.zhiLiaoZuMC"
            :value="item.zhiLiaoZuID"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="batchChuYuan()">批量预出院</el-button>
      </div>
      <div style="max-height: 600px; overflow-y: auto; margin-top: 10px">
        <el-table
          :data="patientList"
          style="width: 100%"
          stripe
          border
          @selection-change="handleSelectionChange"
        >
          >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column
            v-for="(column, index) in columns"
            :key="index"
            :prop="column.value"
            :label="column.label"
            v-bind="column.props"
          >
            <template v-if="column.label === '操作'" #default="{ row }">
              <el-button type="text" @click="openChuYuan(row)">填写</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- <el-pagination
            background
            layout="total, prev, pager, next"
            :current-page.sync="zhuGuanYSPage"
            :page-size="11"
            :pager-count="5"
            :total="zhuGuanYSList.length"
          ></el-pagination> -->
    </div>

    <yu-chu-yuan-dialog
      :visible.sync="chuYuanVisible"
      :zhu-yuan-i-d-list="zhuYuanIDList"
      :is-batch="isBatch"
    />
    <span slot="footer" class="dialog-footer">
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getPatientListByZhiLiaoZu } from '@/api/patient'
import { mapState } from 'vuex'
import YuChuYuanDialog from '@/components/Dialog/yuChuYuanDialog.vue'

export default {
  name: 'BatchDischarge',
  components: {
    YuChuYuanDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    zhiLiaoZuList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chuYuanVisible: false,
      zhuYuanIDList: [], //住院id列表
      isBatch: false, //是否批量

      batchZhuYuanID: [], //勾选批量住院id
      zhiLiaoZuID: null,
      patientList: [],
      columns: [
        { value: 'bingQuMC', label: '病区', props: { fixed: true } },
        { value: 'bingRenXM', label: '患者', props: { fixed: true } },
        { value: 'bingAnHao', label: '病案号', props: { fixed: true } },
        { value: 'chuangWeiHao', label: '床位', props: { fixed: true } },
        { label: '操作', props: { align: 'center' } }
      ]
    }
  },

  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    openChuYuan(row) {
      this.zhuYuanIDList = [row.zhuYuanID]
      this.isBatch = false
      this.chuYuanVisible = true
    },
    batchChuYuan() {
      this.zhuYuanIDList = this.batchZhuYuanID
      this.isBatch = true
      this.chuYuanVisible = true
    },
    async getPatientList() {
      const res = await getPatientListByZhiLiaoZu({
        zhiLiaoZID: this.zhiLiaoZuID,
        zhuanKeID: this.zhuanKeID
      })
      if (res.hasError === 0) {
        this.patientList = res.data
        console.log(this.patientList)
      }
    },
    handleSelectionChange(rows) {
      this.batchZhuYuanID = rows.map((row) => {
        return row.zhuYuanID
      })
    },

    async handleChangeZhiLiaoZu(value) {
      this.zhiLiaoZuID = value
      await this.getPatientList()
    },
    async initBatchDischarge() {
      if (zhiLiaoZuList[0]) {
        this.zhiLiaoZuID = this.zhiLiaoZuList[0]?.zhiLiaoZuID
        await this.getPatientList()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
  .bar {
    border-left: 3px solid #356ac5;
    padding-left: 5px;
  }
}

.dialog-component {
  padding: 0 10px;
  .search-head {
    // button {
    //   --color-primary: #a66dd4;
    // }
    // :deep(.el-button--primary:hover),
    // :deep(.el-button--primary:focus) {
    //   background: #ce8be0;
    //   border-color: #ce8be0;
    // }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
  table {
    margin-top: 10px;
    width: 100%;
  }
  th,
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 10px;
  }
  th,
  .tr-two {
    background-color: #eaf0f9;
  }
  .select-tr {
    background-color: #6787cc;
    color: #ffffff;
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
