<template>
  <el-table :data="tableData" border stripe size="mini">
    <el-table-column prop="bingRenXM" label="病人名称"></el-table-column>
    <el-table-column prop="bingQuMC" label="病区"></el-table-column>
    <el-table-column prop="chuang<PERSON>ei<PERSON>ao" label="床位号"></el-table-column>
    <el-table-column prop="guiZeMC" label="内容"></el-table-column>
  </el-table>
</template>

<script>
import { getCDSSForEHR } from '@/api/patient'
import fetchData from '@/views/patient-home/components/mixin/fetchData'

export default {
  name: 'CDSSAlert',
  mixins: [fetchData],
  data() {
    return {
      tableData: []
    }
  },
  methods: {
    fetchData() {
      getCDSSForEHR({
        zhuanKeID: !this.zhiLiaoZuID && !this.bingQuID ? this.zhuanKeID : null,
        zhiLiaoZuID: this.zhiLiaoZuID,
        bingQuID: this.bingQuID
      }).then((res) => {
        if (res.hasError === 0) {
          console.log('cdss', res.data)
          this.tableData = res.data
          this.$emit('update:count', this.tableData.length)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
