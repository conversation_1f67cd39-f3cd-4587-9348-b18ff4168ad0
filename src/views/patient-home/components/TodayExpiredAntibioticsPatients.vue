<template>
  <el-table :data="tableData" border stripe size="mini">
    <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
    <el-table-column prop="yaoPinMC" label="药品名称"></el-table-column>
    <el-table-column prop="jieShuSJ" label="到期时间"></el-table-column>
  </el-table>
</template>

<script>
import { getDaoQiKjypListByZhuanKeID } from '@/api/patient'
import fetchData from '@/views/patient-home/components/mixin/fetchData'

export default {
  name: 'TodayExpiredAntibioticsPatients',
  mixins: [fetchData],
  data() {
    return {
      tableData: []
    }
  },
  methods: {
    fetchData() {
      getDaoQiKjypListByZhuanKeID({
        zhuanKeID: this.zhuanKeID
      }).then((res) => {
        if (res.hasError === 0) {
          this.tableData = res.data
          this.$emit('update:count', this.tableData.length)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped></style>
