<template>
  <div class="icon-description">
    <el-button type="primary" size="mini" @click="showImage">查看大图</el-button>
    <img src="@/assets/images/patient-images/病人卡片说明1x.jpg" alt="图标说明" />
    <el-dialog
      title="图标说明"
      :visible="dialog"
      width="500px"
      :close-on-click-modal="false"
      @close="dialog = false"
    >
      <img src="@/assets/images/patient-images/病人卡片说明2x.jpg" alt="图标说明大" />
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'IconDescription',
  data() {
    return {
      dialog: false
    }
  },
  methods: {
    showImage() {
      this.dialog = true
    }
  }
}
</script>

<style lang="scss" scoped>
.icon-description {
  padding: 20px;
  img {
    width: 100%;
  }
}
</style>
