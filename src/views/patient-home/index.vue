<template>
  <el-container class="inner-container">
    <el-aside>
      <patient-aside />
    </el-aside>
    <el-main>
      <patient-view />
    </el-main>
  </el-container>
</template>

<script>
import PatientView from '@/views/patient-home/PatientView.vue'
import PatientAside from '@/views/patient-home/PatientAside.vue'

export default {
  name: 'PatientHome',
  components: {
    PatientView,
    PatientAside
  },
  data() {
    return {}
  },
  methods: {}
}
</script>

<style scoped lang="scss">
.inner-container {
  display: flex;
  flex-direction: row-reverse;

  .el-aside {
    flex-basis: 288px;
  }

  .el-main {
    display: flex;
    height: 100%;
    padding: 0 10px;

    .app-main {
      height: 100%;
      width: 100%;
      overflow: auto;
    }
  }
}
</style>
