<template>
  <div class="patient-view">
    <el-card class="patient-head" shadow="never">
      <div class="patient-head-row">
        <span class="bing-ren">{{ patientDetail.bingRenXM }}</span>
        <span class="bing-ren">{{ patientDetail.bingAnHao }}</span>
        <span>{{ patientDetail.xingBieMC }}</span>
        <span>{{ patientDetail.nianLing }}</span>
        <span>{{ patientDetail.zhuanKeMC }}</span>
        <span>
          {{ formatChuangWeiHao(patientDetail.bingQuMC, patientDetail.chuangWeiHao) }}
        </span>
        <button class="tag-button" type="text" @click.stop="zhiLiaoZuVisible = true">
          <el-tag effect="dark">
            {{ patientDetail.zhiLiaoZuMC }}
          </el-tag>
        </button>
        <button class="tag-button" type="text" @click.stop="zhuGuanYSVisible = true">
          <el-tag effect="dark">主管医师: {{ patientDetail.zhuGuanYSXM }}</el-tag>
        </button>
        <el-tag>{{ patientDetail.linChuangZD }}</el-tag>
        <button
          v-if="patientDetail.huLiJBMC"
          class="tag-button"
          type="text"
          @click.stop="huLiVisible = true"
        >
          <el-tag effect="dark">{{ patientDetail.huLiJBMC }}</el-tag>
        </button>
        <el-tag v-if="patientDetail.bingRenYS">{{ patientDetail.bingRenYS }}</el-tag>
        <span style="height: 22px">
          <el-popover popper-class="popper-button" placement="bottom" trigger="click">
            <div>
              <el-button style="color: #000" type="text" @click.stop="openBingRenZY('edit')">
                修改
              </el-button>
            </div>
            <div v-if="patientDetail.bingRenZT === '在院'">
              <el-button style="color: #000" type="text" @click.stop="openBingRenZY('one-touch')">
                一键下转
              </el-button>
            </div>
            <button slot="reference" type="text" @click.stop="() => {}">
              <el-tag effect="dark">{{ patientDetail.bingRenZT }}</el-tag>
            </button>
          </el-popover>
        </span>
        <el-tag>{{ patientDetail.jieSuanLXMC }}</el-tag>
        <el-tag type="danger">{{ patientDetail.zhuYuanSC }}</el-tag>
        <el-tag type="danger">质控</el-tag>
        <button
          class="tag-button"
          type="text"
          @click.stop="
            () => {
              liquidFormData = liquidValue
              liquidVisible = true
            }
          "
        >
          <el-tag effect="dark">液体出入量平衡设置值:L-G={{ liquidValue }}ml</el-tag>
        </button>
        <button class="tag-button tags_btn" type="text" @click.stop="openDetails()">
          <span>详情</span>
        </button>
        <!-- 日间手术-->
        <img
          v-if="patientDetail.riJianSS && patientDetail.riJianSS === '1'"
          :src="require(`@/assets/images/patient-images/patient-card/rjss2.png`)"
          alt="日间手术"
        />
        <!-- <img
          v-else
          :src="require(`@/assets/images/patient-images/patient-card/rjss1.png`)"
          alt="日间手术"
        /> -->
        <!-- 黑名单-->
        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['黑名单']"
          :src="require(`@/assets/images/patient-images/patient-card/黑名单.png`)"
          alt="黑名单"
        />
        <!-- 放射性粒子术后辐射防护-->
        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['放射性粒子术后辐射防护']"
          :src="require(`@/assets/images/patient-images/patient-card/放射性粒子术后辐射防护.png`)"
          alt="放射性粒子术后辐射防护"
        />
        <!-- GCP标志-->
        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['GCP']"
          :src="require(`@/assets/images/patient-images/patient-card/GCP.png`)"
          alt="GCP标志"
        />
        <!-- 安宁疗护-->
        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['安宁疗护']"
          :src="require(`@/assets/images/patient-images/patient-card/安宁疗护.png`)"
          alt="安宁疗护"
        />
        <!-- 安宁疗护-->
        <img
          v-if="patientDetail.specialSign && patientDetail.specialSign['病情保密标志']"
          :src="require(`@/assets/images/patient-images/patient-card/P.png`)"
          alt="安宁疗护"
        />
        <!-- DRG: 0绿色，1黄色，2红色，-1非DRG病例不显示-->
        <img
          v-if="patientDetail.specialSign?.DRG && patientDetail.specialSign?.DRG !== '-1'"
          :src="
            require(`@/assets/images/patient-images/patient-card/drg${patientDetail.specialSign?.DRG}.png`)
          "
          alt="DRG"
        />
      </div>
      <div class="patient-head-row">
        <el-tag
          v-if="getAssessment('PHQ_4')?.score"
          :type="tagTypeMap[getAssessment('PHQ_4')?.riskLevel]"
        >
          PHQ_4评分:
          {{ getAssessment('PHQ_4')?.score || '-' }}分
        </el-tag>
        <el-tag
          v-if="getAssessment('caprini')?.score"
          :type="tagTypeMap[getAssessment('caprini')?.riskLevel]"
        >
          caprini评分:
          {{ getAssessment('caprini')?.score || '-' }}分
        </el-tag>
        <el-tag
          v-if="getAssessment('NRS')?.score"
          :type="tagTypeMap[getAssessment('NRS')?.riskLevel]"
        >
          NRS评分:
          {{ getAssessment('NRS')?.score || '-' }}分
        </el-tag>
        <el-tag
          v-if="getAssessment('产科VTE')?.score"
          :type="tagTypeMap[getAssessment('产科VTE')?.riskLevel]"
        >
          产科VTE评分:
          {{ getAssessment('产科VTE')?.score }}分
        </el-tag>
        <el-tag
          v-if="getAssessment('营养风险筛查评分')?.score"
          :type="tagTypeMap[getAssessment('营养风险筛查评分')?.riskLevel]"
          @click.stop="openNutritional(getAssessment('营养风险筛查评分'))"
        >
          营养风险筛查评分:
          {{ getAssessment('营养风险筛查评分')?.score || '-' }}分
        </el-tag>
        <el-tag
          v-if="getAssessment('padua')?.score"
          :type="tagTypeMap[getAssessment('padua')?.riskLevel]"
        >
          padua评分:
          {{ getAssessment('padua')?.score || '-' }}分
        </el-tag>
        <el-tag
          v-if="getAssessment('ADL_BI评分')?.score"
          :type="tagTypeMap[getAssessment('ADL_BI评分')?.riskLevel]"
        >
          ADL_BI评分:
          {{ getAssessment('ADL_BI评分')?.score || '-' }}分
        </el-tag>
        <el-tag
          v-if="getAssessment('APACHE_II')?.score"
          :type="tagTypeMap[getAssessment('APACHE_II')?.riskLevel]"
        >
          APACHE_II评分:
          {{ getAssessment('APACHE_II')?.score || '-' }}分
        </el-tag>
        <el-tag
          v-if="getAssessment('SOFA')?.score"
          :type="tagTypeMap[getAssessment('SOFA')?.riskLevel]"
        >
          SOFA评分:
          {{ getAssessment('SOFA')?.score || '-' }}分
        </el-tag>
        <el-tag
          v-if="getAssessment('death_rate')?.score"
          :type="tagTypeMap[getAssessment('death_rate')?.riskLevel]"
        >
          预计死亡率:
          {{ getAssessment('death_rate')?.score || '-' }}
        </el-tag>
        <el-tag
          v-if="getAssessment('呕吐分级')?.score"
          :type="tagTypeMap[getAssessment('呕吐分级')?.riskLevel]"
        >
          呕吐分级:
          {{ getAssessment('呕吐分级')?.score || '-' }}级
        </el-tag>
        <el-tag
          v-if="getAssessment('认知功能')?.score"
          :type="tagTypeMap[getAssessment('认知功能')?.riskLevel]"
        >
          认知功能:
          {{ getAssessment('认知功能')?.score || '-' }}分
        </el-tag>
        <el-tag
          v-if="patientDetail.specialSign && patientDetail.specialSign['易致低钾药物使用中']"
          :type="tagTypeMap['MIDDLE']"
        >
          易致低钾药物使用中:
          {{ patientDetail.specialSign['易致低钾药物使用中'] }}
        </el-tag>
      </div>
    </el-card>
    <tags-view class="patient-detail-tabs" />
    <el-drawer
      ref="alertDrawer"
      :visible.sync="drawerVisible"
      size="62%"
      direction="rtl"
      :with-header="false"
      :before-close="urgentLimit"
    >
      <div class="patient-taskbar-box">
        <div class="patient-taskbar-title">
          <span />
          代办/提醒任务栏
        </div>
        <span>
          <!-- {{alertMessages}} -->
          <table>
            <thead>
              <tr style="text-align: left">
                <th>类型</th>
                <th>内容</th>
                <th>处理状态</th>
              </tr>
            </thead>
            <tbody>
              <tr
                v-for="(item, index) in alertMessages"
                :key="'tr-' + index"
                :class="[index % 2 === 1 ? 'tr-two' : 'tr-one']"
              >
                <td
                  :style="item.urgent ? { backgroundColor: '#F35656' } : {}"
                  style="text-align: center; width: 70px"
                >
                  <el-tag v-if="item.tag" :type="tagTypeMap[item.level]">{{ item.tag }}</el-tag>
                  <el-checkbox
                    v-else
                    disabled
                    :value="
                      !alertUpdateMessages.find((upItem) => {
                        return upItem.alertKey === item.alertKey
                      })
                    "
                  />
                </td>
                <td :colspan="item.tag ? 2 : 1">
                  {{ item.content }}
                </td>
                <td v-if="!item.tag" style="width: 200px">
                  <template v-if="item.alertKey === 'GLIM'">
                    <el-button
                      type="primary"
                      :disabled="
                        !alertUpdateMessages.find((upItem) => {
                          return upItem.alertKey === item.alertKey
                        })
                      "
                      @click="alertHandle(item)"
                    >
                      GLIM评估
                    </el-button>
                    <el-button
                      type="primary"
                      :disabled="
                        !alertUpdateMessages.find((upItem) => {
                          return upItem.alertKey === item.alertKey
                        })
                      "
                      @click="alertHandle(item)"
                    >
                      自行处理
                    </el-button>
                  </template>
                  <template v-else>
                    <el-button
                      type="primary"
                      :disabled="
                        !alertUpdateMessages.find((upItem) => {
                          return upItem.alertKey === item.alertKey
                        })
                      "
                      @click="alertHandle(item)"
                    >
                      处理
                    </el-button>
                  </template>
                </td>
              </tr>
            </tbody>
          </table>
        </span>
      </div>
      {{ zhiLiaoZuList }}
      <button
        type="text"
        class="open-drawer-button"
        style="left: -30px"
        @click="$refs.alertDrawer.closeDrawer()"
      >
        <el-badge :value="alertMessages.length" class="item" />
        <i class="el-icon-news"></i>
      </button>
    </el-drawer>

    <el-dialog :visible="alertVisible">
      <span slot="title">
        <span class="drawer-title">
          <span class="bar" />
          消息处理
        </span>
      </span>
      <span>
        <iframe width="100%" :src="alertIframe"></iframe>
      </span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSuccess">确 认</el-button>
        <el-button @click="alertVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <zhi-liao-zu-drawer
      :visible.sync="zhiLiaoZuVisible"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :zhi-liao-zu-i-d="patientDetail.zhiLiaoZuID"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />
    <hu-li-drawer
      :visible.sync="huLiVisible"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :hu-li-j-b-m-c="patientDetail.huLiJBMC"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />
    <zhu-guan-y-s-drawer
      :visible.sync="zhuGuanYSVisible"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :zhuan-ke-i-d="patientDetail.zhuanKeID"
      :zhu-guan-y-s-i-d="patientDetail.zhuGuanYSID"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />
    <bing-ren-z-y-drawer
      :yi-jian-x-z.sync="yiJianXZ"
      :zhu-yuan-i-d="patientDetail.zhuYuanID"
      :zhuan-ke-i-d="patientDetail.zhuanKeID"
      :visible.sync="bingRenZYVisible"
      @upload-success="$store.dispatch('patient/getPatientInit', bingLiID)"
    />

    <el-dialog :visible.sync="liquidVisible" width="300px">
      <span slot="title">
        <span style="font-size: 16px">
          <span class="bar" />
          修改液体出入量平衡
        </span>
      </span>
      <span class="liquid-input">
        目标值:
        <el-input v-model="liquidFormData"></el-input>
      </span>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="setFluidBalance">保 存</el-button>
        <el-button @click="liquidVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <button
      v-if="!drawerVisible && alertMessages.length"
      type="text"
      class="open-drawer-button"
      style="right: 1px"
      @click="drawerVisible = true"
    >
      <el-badge :value="alertMessages.length" class="item" />
      <i class="el-icon-news"></i>
    </button>
  </div>
</template>

<script>
import {
  PatientInit,
  getAssessmentRecords,
  getFluidBalanceInfoByCalculate,
  setFluidBalance,
  getSyDRGByJiBingIDs
} from '@/api/patient-info'
import store from '@/store'
import { mapState } from 'vuex'
import { getAlertMessages } from '@/api/patient'
import { EventBus } from '@/utils/event-bus'
import ZhiLiaoZuDrawer from '@/views/patient-detail/components/ZhiLiaoZuDrawer.vue'
import HuLiDrawer from '@/views/patient-detail/components/HuLiDrawer.vue'
import ZhuGuanYSDrawer from '@/views/patient-detail/components/ZhuGuanYSDrawer.vue'
import BingRenZYDrawer from '@/views/patient-detail/components/BingRenZYDrawer.vue'
import TagsView from './components/TagsView'

export default {
  name: 'PatientDetailView',
  components: {
    ZhiLiaoZuDrawer,
    HuLiDrawer,
    ZhuGuanYSDrawer,
    BingRenZYDrawer,
    TagsView
  },
  data() {
    return {
      assessmentRecords: [], //评分结果汇总
      alertMessages: [], //消息列表
      alertUpdateMessages: [], //暂存更新消息用于判断
      alertVisible: false,
      alertIframe: null,
      yiJianXZ: '0', //一键下转

      //各类弹窗控制
      zhiLiaoZuVisible: false,
      huLiVisible: false,
      zhuGuanYSVisible: false,
      bingRenZYVisible: false,

      //液体出入量相关
      liquidVisible: false,
      liquidValue: 0,
      liquidFormData: 0,

      tagTypeMap: {
        LOW: 'success',
        MIDDLE: 'warning',
        HIGH: 'danger',
        EXTREMELY_HIGH: 'danger',
        info: 'info'
      },

      drawerVisible: false //消息抽屉控制
    }
  },
  computed: {
    ...mapState({
      zhiLiaoZuList: ({ patient }) => patient.zhiLiaoZuList,
      patientDetail: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  activated() {
    this.getPatientInit()
  },
  methods: {
    urgentLimit(done) {
      if (
        !this.alertMessages.find((item) => {
          return item.urgent
        })
      ) {
        done()
      }
    },
    async handleSuccess() {
      this.alertVisible = false

      const res = await PatientInit(this.bingLiID)
      if (res.hasError === 0) {
        this.alertUpdateMessages = res.data.data.alertMessages || []
      }
    },
    //消息处理
    alertHandle(alert) {
      console.log(alert)
      if (typeof alert.fuJiaSX === 'string' && alert.fuJiaSX.indexOf('http') > -1) {
        this.alertVisible = true
        this.alertIframe = alert.fuJiaSX
      }
      switch (alert) {
        case 'HCV':
          break
        case 'NIHSS':
          break
        case 'SHSCTX':
          break
        case 'XBHZD':
          break
        case 'NUTRIC':
          break
        case 'VTEGZS':
          break
        case 'VTEBLSJ':
          break
        case '24HSUICIDEASSESS':
          break
        case 'ICUQuality':
          break
        case 'ZYC30D':
          break
        case 'YYHZD':
          break
        case 'ZRJWCL':
          break
      }
    },
    openBingRenZY(type) {
      if (type === 'edit') {
        this.yiJianXZ = this.patientDetail.bingRenZT === '在院' ? '0' : 'C'
      } else if (type === 'one-touch') {
        this.yiJianXZ = '1'
      }
      this.bingRenZYVisible = true
    },
    openDetails() {
      EventBus.$emit(`sidebarClick_${this.bingLiID}`, {
        name: 'PatientBasicInfo',
        component: () => import('./PatientBasicInfo'),
        meta: {
          title: '病人详情'
        }
      })
    },
    openNutritional(e) {
      if (e.name.indexOf('营养风险筛查') > -1) {
        EventBus.$emit(`sidebarClick_${this.bingLiID}`, {
          name: 'NutritionScreening',
          component: () =>
            import('../patient-inside/nutrition-screening-menu/NutritionScreening.vue'),
          meta: {
            title: '营养筛查'
          }
        })
      }
    },
    async getPatientInit() {
      await this.$store.dispatch('patient/getPatientInit', this.bingLiID)

      this.alertMessages = this.patientDetail.alertMessages || []
      this.alertUpdateMessages = this.alertMessages
      if (this.alertMessages.length > 0) this.drawerVisible = true

      getAssessmentRecords(this.bingLiID).then((res) => {
        if (res.hasError === 0) {
          this.assessmentRecords = res.data
        }
      })

      // 根据BLID获取病人当前8小时内出入量平衡信息
      getFluidBalanceInfoByCalculate(this.bingLiID).then((res) => {
        if (res.hasError === 0) {
          this.liquidValue = res.data.fluidBalanceSet
        }
      })

      //新版评分信息接口测试

      // getSyDRGByJiBingIDs({ bingLiIDList: [this.bingLiID] }).then((res) => {
      //   console.log('根据blid列表获取对应的DRG状态列表', res)
      //   if (res.hasError === 0) {
      //   }
      // })
    },
    getAssessment(name) {
      return this.assessmentRecords.find((item) => {
        return item.name === name
      })
    },
    setFluidBalance() {
      setFluidBalance({
        bingLiID: this.bingLiID,
        fluidBalanceSetValue: this.liquidFormData
      }).then((res) => {
        if (res.hasError === 0) {
          this.liquidVisible = false
          this.liquidValue = this.liquidFormData
          this.$message({
            message: '保存成功',
            type: 'success',
            duration: 700
          })
        }
      })
    },
    formatChuangWeiHao(bingQuMC, chuangWeiHao) {
      return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
    },
    isYYFXOrVTE(type) {
      if (!this.assessmentRecords) {
        return false
      }
      // 判断是否为营养风险或VTE 找不到返回false
      const obj = this.assessmentRecords.find((i) => i.type === type)
      return obj?.riskLevel ?? false
    }
  }
}
</script>

<style scoped lang="scss">
.patient-view {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .patient-detail-tabs {
    flex: 1;
    min-height: 0;
  }
  .patient-head {
    font-size: 15px;
    border-top: none;
    border-left: none;
    :deep(.el-card__body) {
      padding: 10px 15px;
    }
    .patient-head-row {
      display: flex;
      align-items: center; /* 将子元素垂直居中 */
      // min-height: 30px;
      margin-bottom: 5px;
      .tag-button {
        height: 22px;
      }
    }
    .bing-ren {
      font-weight: 600;
    }
    .patient-head-row > span,
    .patient-head-row > button {
      margin-right: 7px;
    }
    img {
      margin-right: 4px;
    }
    .el-tag--dark {
      max-width: 250px;
      height: 22px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .el-tag {
      border-radius: 2px;
    }
  }
  .patient-taskbar-box {
    position: absolute;
    height: 100%;
    width: 100%;
    z-index: 2;
    background-color: #eff3fb;
    padding: 10px;
    .patient-taskbar-title {
      font-weight: 600;
      span {
        border-left: 3px solid #356ac5;
        padding-left: 5px;
      }
    }
    table {
      margin-top: 10px;
      width: 100%;
    }
    th,
    td {
      border: 1px solid #ddd;
      border-collapse: collapse; /* 移除表格内边框间的间隙 */
      height: 35px;
      padding: 7px;
    }
    .tr-one {
      background-color: #f6f6f6;
    }
  }
  .open-drawer-button {
    position: absolute;
    z-index: 1;
    width: 30px;
    height: 60px;
    background-color: #eff3fb;
    top: 55%;
    text-align: left;
    border-radius: 30px 0 0 30px;
    box-shadow: 0px 0px 5px 5px rgba(0, 0, 0, 0.2);
    .el-icon-news {
      line-height: 60px;
      font-size: 28px;
      color: #356ac5;
    }
    .item {
      position: absolute;
      top: 17px;
      left: 10px;
    }
  }
  .liquid-input {
    display: flex;
    align-items: center;
    padding-left: 30px;
    .el-input {
      width: 65%;
    }
  }

  :deep(.el-drawer) {
    overflow: visible;
  }

  :deep(.el-dialog__footer) {
    border-top: none;
  }
}
.bar {
  border-left: 3px solid #356ac5;
  padding-left: 5px;
}
.tags_btn {
  background: none;
}
.tags_btn span {
  color: #356ac5;
}
</style>

<style lang="scss">
.popper-button {
  min-width: 100px;
}
</style>
