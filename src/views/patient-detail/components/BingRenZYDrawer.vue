<template>
  <el-dialog
    :visible="visible"
    :width="['1', '2', '9'].includes(yiJianXZ) && referralType === 2 ? '950px' : '450px'"
    @opened="
      () => {
        if (yiJianXZ === '1') {
          $nextTick(() => {
            referralType = 2
            referralVisible = true
          })
        }
      }
    "
    @close="updateVisible(false)"
  >
    <span slot="title">
      <span class="dialog-title">
        <span class="bar" />
        修改病人住院状态
      </span>
    </span>
    <span>
      <div class="dialog-component">
        <div style="width: 400px">
          <table>
            <tbody>
              <tr>
                <td class="info-label">病人当前状态:</td>
                <td class="info-value">
                  <div style="display: flex; justify-content: space-between; align-items: center">
                    {{ bingRenLYZTTOOLTIP }}
                    <!-- <el-button type="primary" @click="openZhongJing">重精报告出院信息单</el-button> -->
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">修改病人状态为:</td>
                <td class="info-value">
                  <el-select
                    :value="yiJianXZ"
                    style="width: 100%"
                    placeholder="请选择病人状态"
                    @change="changeLiYuanZT"
                  >
                    <el-option
                      v-for="item in liYuanZTList"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="info-label">确认病人主管医师:</td>
                <td class="info-value">
                  <el-select v-model="zhuGuanYS" style="width: 100%" placeholder="请选择主管医师">
                    <el-option
                      v-for="item in zhuGuanYSList"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="info-label">医嘱选择:</td>
                <td class="info-value">
                  <span>
                    <el-checkbox v-model="tingZhiYZ" />
                    停止长期医嘱
                  </span>
                  <span>
                    <el-checkbox v-model="xiaDaCYYZ" />
                    下达出院医嘱
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
          <span class="hint-info">
            <i class="el-icon-warning" />
            <span>
              温馨提示：
              <br />
              1.【停止长期医嘱】不选择，则不停止长期医嘱;
              <br />
              2.【下达出院医嘱】勾选，则根据出院情况下达【今日出统】或者【宣布死亡】，不勾选，则不下达任何医
              。若患者以日间手术收治，但住晚时长超过24小时或整个住统时间中，未进行任何手术或操作（首页），下达医寝出院时，强制退出日间手术流程;
              <br />
              3.选择【转院】则必须填写转院单位，及转院理由;
              <br />
              4.【死亡病人】请准确的填写死亡事件，该时间作为【宣布死亡】医嘱的时间。
            </span>
          </span>
        </div>
        <div v-if="yiJianXZ === '1' && referralType === 2" style="margin-left: 10px">
          <table class="right-table">
            <tbody>
              <tr>
                <td class="info-label">转诊机构:</td>
                <td class="info-value" colspan="3">
                  <el-select v-model="zhuanZhenJg" style="width: 100%" @change="getZhuanZhenJgInfo">
                    <el-option
                      v-for="item in zhuanZhenJgList"
                      :key="item.yiLiaoJGDM"
                      :label="item.yiLiaoJGMC"
                      :value="item.yiLiaoJGDM"
                    ></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="info-label">专科:</td>
                <td class="info-value">
                  <el-select v-model="zhuanZhenJgZk" style="width: 100%">
                    <el-option
                      v-for="item in zhuanZhenJgZkList"
                      :key="item.zhuanKeID"
                      :label="item.zhuanKeMC"
                      :value="item.zhuanKeID"
                    ></el-option>
                  </el-select>
                </td>
                <td class="info-label">紧急程度:</td>
                <td class="info-value">
                  <el-select v-model="jinJiCD" style="width: 100%">
                    <el-option
                      v-for="item in [
                        { label: '一般', value: '1' },
                        { label: '急', value: '2' },
                        { label: '危急', value: '3' }
                      ]"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </td>
              </tr>
              <tr>
                <td class="info-label">联络医生:</td>
                <td class="info-value">{{ zhuanZhenJgLxrXx?.yongHuXM }}</td>
                <td class="info-label">联系电话:</td>
                <td class="info-value">{{ zhuanZhenJgLxrXx?.yiDongHM }}</td>
              </tr>
              <tr>
                <td class="info-label">*主要诊断:</td>
                <td class="info-value">
                  <el-input v-model="zhuYaoZD" :rows="3" type="textarea" />
                </td>
                <td class="info-label">*临床拟诊:</td>
                <td class="info-value">
                  <el-input v-model="linChuangNZ" :rows="3" type="textarea" />
                </td>
              </tr>
              <tr>
                <td class="info-label">转诊嘱托:</td>
                <td class="info-value" colspan="3">
                  <el-input v-model="zhiLiaoFA" :rows="2" type="textarea" />
                </td>
              </tr>
              <tr>
                <td class="info-label">病史摘要:</td>
                <td class="info-value" colspan="3">
                  <el-input v-model="bingShiZY" :rows="2" type="textarea" />
                </td>
              </tr>
              <tr>
                <td class="info-label">化验指标:</td>
                <td class="info-value" colspan="3">
                  <el-input v-model="huaYanZB" :rows="2" type="textarea" />
                </td>
              </tr>
              <tr>
                <td class="info-label">体检:</td>
                <td class="info-value" colspan="3">
                  <el-input v-model="tiJian" :rows="2" type="textarea" />
                </td>
              </tr>
              <tr>
                <td class="info-label">转诊目的:</td>
                <td class="info-value" colspan="3">
                  <el-input v-model="zhuanZhenMD" :rows="2" type="textarea" />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else-if="yiJianXZ === '2'" style="margin-left: 10px">
          <table class="right-table2">
            <tbody>
              <tr>
                <td class="info-label">转归单位:</td>
                <td class="info-value">
                  <el-input v-model="zhuanGuiDW" />
                </td>
              </tr>
              <tr>
                <td class="info-label">转归理由:</td>
                <td class="info-value">
                  <el-input v-model="zhuanGuiLY" :rows="4" type="textarea" />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div v-else-if="yiJianXZ === '9'" style="margin-left: 10px">
          <table class="right-table2">
            <tbody>
              <tr>
                <td class="info-label">死亡时间:</td>
                <td class="info-value">
                  <el-date-picker
                    v-model="siWangSJ"
                    type="datetime"
                    placeholder="选择死亡时间"
                    value-format="yyyy-MM-dd hh:mm:ss"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <el-dialog
        :visible="referralVisible"
        :append-to-body="true"
        :show-close="false"
        width="450px"
      >
        <span>
          <div class="referral-component">
            <el-button
              type="primary"
              @click="
                referralType = 1
                referralVisible = false
              "
            >
              省平台转诊
            </el-button>
            <el-button
              type="primary"
              @click="
                referralType = 2
                referralVisible = false
              "
            >
              院级转诊
            </el-button>
            <el-button
              type="primary"
              @click="
                referralType = 3
                referralVisible = false
              "
            >
              已完成省平台转诊
            </el-button>
          </div>
        </span>
      </el-dialog>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="saveBrCyCz()">保 存</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  saveBrCyCz,
  initXiuGaiBrZyZt,
  getZhuanZhenJgList,
  getZhuanZhenJgZkList,
  getZhuanZhenJgLxrXxList,
  getZhongJingBBCYXXD
} from '@/api/patient'
export default {
  name: 'BingRenZYdialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    zhuYuanID: {
      type: Number,
      default: null
    },
    zhuanKeID: {
      type: Number,
      default: null
    },
    yiJianXZ: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      referralVisible: false, //省平台转诊选择弹窗
      referralType: 2, //平台转诊类型 1省平台转诊 2院级转诊 3已完成省平台转诊

      tingZhiYZ: true,
      xiaDaCYYZ: true,
      liYuanZTList: [],
      zhuGuanYSList: [],
      zhuGuanYS: null,
      zhuanZhenJgList: [],
      zhuanZhenJg: null,
      zhuanZhenJgZkList: [],
      zhuanZhenJgZk: null,
      zhuanZhenJgLxrXxList: [],
      zhuanZhenJgLxrXx: {},
      bingRenLYZTTOOLTIP: null,
      bingShiZY: '',
      linChuangNZ: '',
      zhuanZhenMD: '',
      zhiLiaoFA: '',
      tiJian: '',
      huaYanZB: '',
      jinJiCD: '1',
      zhuYaoZD: '',
      zhuanGuiDW: '',
      zhuanGuiLY: '',
      siWangSJ: null
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.initDialog()

    const res2 = await getZhuanZhenJgLxrXxList()

    console.log('查询_获取转诊机构联系人信息', res2)
    if (res2.hasError === 0) {
      this.zhuanZhenJgLxrXxList = res2.data
    }

    const res1 = await getZhuanZhenJgList()

    console.log('查询_获取转诊机构列表', res1)
    if (res1.hasError === 0) {
      this.zhuanZhenJgList = res1.data
      this.zhuanZhenJg = res1.data[0].yiLiaoJGDM
      this.getZhuanZhenJgInfo(this.zhuanZhenJg)
    }
  },
  methods: {
    async openZhongJing() {
      const res = await getZhongJingBBCYXXD({
        bingLiID: this.bingLiID
      })

      console.log('查询_重精报告出院信息单', res)
      if (res.hasError === 0) {
      }
    },
    changeLiYuanZT(yiJianXZ) {
      this.$emit('update:yiJianXZ', yiJianXZ)
      if (yiJianXZ === '1') {
        this.referralVisible = true
      }
    },
    async initDialog() {
      const res = await initXiuGaiBrZyZt({
        bingLiID: this.bingLiID,

        zhuYuanID: this.zhuYuanID, //住院ID
        zhuanKeID: this.zhuanKeID, //专科ID
        yiJianXZ: this.yiJianXZ //一键下转，1=是
      })

      console.log('查询_初始化修改病人住院状态', res)
      if (res.hasError === 0) {
        this.liYuanZTList = res.data.liYuanZTList
        this.zhuGuanYSList = res.data.zhuGuanYSList
        this.bingRenLYZTTOOLTIP = res.data.bingRenLYZTTOOLTIP
        this.zhuGuanYS = String(res.data.inPatientVo.zhuGuanYSID)
        this.zhuYaoZD = res.data.zyReferralDetailVos?.zhuYaoZD || ''
        this.linChuangNZ = res.data.zyReferralDetailVos?.linChuangNZ || ''
        this.zhiLiaoFA = res.data.zyReferralDetailVos?.zhiLiaoFA || ''
        this.bingShiZY = res.data.zyReferralDetailVos?.bingShiZY || ''
        this.zhiLiaoFA = res.data.zyReferralDetailVos?.zhiLiaoFA || ''
        this.huaYanZB = res.data.zyReferralDetailVos?.huaYanZB || ''
        this.tiJian = res.data.zyReferralDetailVos?.tiJian || ''
        this.zhuanZhenMD = res.data.zyReferralDetailVos?.zhuanZhenMD || ''
      }
    },
    async getZhuanZhenJgInfo(jiGouDM) {
      this.zhuanZhenJgLxrXx = this.zhuanZhenJgLxrXxList.find((item) => {
        return jiGouDM === item.yiLiaoJGDM
      })
      const res = await getZhuanZhenJgZkList({ jiGouDM })

      console.log('查询_获取转诊机构专科列表', res)
      if (res.hasError === 0) {
        this.zhuanZhenJgZkList = res.data
        if (res.data[0]) {
          this.zhuanZhenJgZk = res.data[0].zhuanKeID
        }
      }
    },
    async saveBrCyCz() {
      let data = {}
      if (this.yiJianXZ === '1' && this.referralType === 2) {
        data = {
          yiJianXiaZhuan: 1, //一键下转，1=是
          yiJianXiaZhuanVo: {
            shenQingDanID: 0, //申请单id
            zhuanZhenYLJGDM: this.zhuanZhenJg, //转诊医疗机构代码
            zhuanZhenYLJGMC: this.zhuanZhenJgList.find((item) => {
              return item.yiLiaoJGDM === this.zhuanZhenJg
            })?.yiLiaoJGMC, //转诊医疗机构名称
            zhuanZhenZKID: this.zhuanZhenJgZk, //转诊专科id
            zhuanZhenZKMC: Array.isArray(this.zhuanZhenJgZkList)
              ? this.zhuanZhenJgZkList.find((item) => {
                  return item.zhuanKeID === this.zhuanZhenJgZk
                })?.zhuanKeMC
              : '', //转诊专科名称
            zhuanZhenYSID: this.zhuanZhenJgLxrXx.yongHuID, //转诊医生id
            zhuanZhenYSLXDH: this.zhuanZhenJgLxrXx.yiDongHM, //转诊医生联系电话
            zhuanZhenYSXM: this.zhuanZhenJgLxrXx.yongHuXM, //转诊医生姓名
            bingShiZY: this.bingShiZY, //病史摘要
            linChuangNZ: this.linChuangNZ, //临床拟诊
            zhuanZhenMD: this.zhuanZhenMD, //转诊目的
            zhiLiaoFA: this.zhiLiaoFA, //转诊嘱托
            tiJian: this.tiJian, //体检
            huaYanZB: this.huaYanZB, //化验指标
            jinJiCD: this.jinJiCD, //紧急程度(1:一般 2:急 3:危急)
            zhuYaoZD: this.zhuYaoZD //主要诊断
          }
        }
      } else if (this.yiJianXZ === '2') {
        data = {
          zhuanGuiDW: this.zhuanGuiDW,
          zhuanGuiLY: this.zhuanGuiLY
        }
      } else if (this.yiJianXZ === '9') {
        data = {
          siWangSJ: this.siWangSJ
        }
      }

      const res = await saveBrCyCz({
        bingLiID: this.bingLiID,
        liYuanZTDM: this.yiJianXZ,
        tingZhiYZ: this.tingZhiYZ ? 1 : 0, //停止医嘱：1=是，0=否
        xiaDaCYYZ: this.xiaDaCYYZ ? 1 : 0, //下达出院医嘱：1=是，0=否
        zhuGuanYSID: this.zhuGuanYS, //主管医生ID（用户ID）
        ...data
      })

      console.log('查询_保存病人出院操作', res)
      if (res.hasError === 0) {
        this.initDialog()
        this.updateVisible(false)
        this.$emit('upload-success')
        this.$message({
          message: '保存成功',
          type: 'success',
          duration: 700
        })
      }
    },
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
  .bar {
    border-left: 3px solid #356ac5;
    padding-left: 5px;
  }
}

.dialog-component {
  display: flex;
  padding: 0px 16px;
  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 6px;
  }
  .info-label {
    text-align: right;
    width: 120px;
    background-color: #eaf0f9;
  }
  .info-value {
    width: 230px;
    background-color: #ffffff;
  }
  .right-table {
    .info-label {
      width: 80px;
    }
    .info-value {
      width: 150px;
    }
  }
  .right-table2 {
    .info-value {
      width: 350px;
    }
  }
  .el-checkbox {
    margin: 0 7px;
  }
  .hint-info {
    margin-top: 10px;
    display: flex;
    .el-icon-warning {
      margin: 3px 3px 0 0;
      color: #356ac5;
    }
  }
}

.referral-component {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;
}
:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
