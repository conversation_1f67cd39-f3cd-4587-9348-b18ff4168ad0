<template>
  <div ref="tagsViewContainer" class="tags-view-container">
    <el-tabs
      v-model="activeRoute"
      @tab-remove="closeSelectedTag"
      @contextmenu.prevent.native="openMenu($event)"
    >
      <el-tab-pane
        v-for="tag in visitedViews"
        :key="tag.name"
        :name="tag.name"
        :label="tag.title"
        :closable="!tag.meta?.affix"
      >
        <component :is="tag.component" :key="`${tag.name}-${refreshKey}`" />
      </el-tab-pane>
    </el-tabs>
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag">刷新</li>
      <li @click="closeOthersTags">关闭其它</li>
      <li @click="closeAllTags">关闭所有</li>
    </ul>
  </div>
</template>

<script>
import { loadView } from '@/store/modules/permission'
import { EventBus } from '@/utils/event-bus'
import { isNumberStr } from 'wyyy-component/src/utils'

export default {
  name: 'PatientTagView',
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      affixTags: [],
      activeRoute: '',
      visitedViews: [],
      refreshKey: 0
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  watch: {
    visible(value) {
      if (value) {
        document.body.addEventListener('click', this.closeMenu)
      } else {
        document.body.removeEventListener('click', this.closeMenu)
      }
    },
    activeRoute(val) {
      EventBus.$emit(`activeRoute_${this.bingLiID}`, val)
    }
  },
  mounted() {
    this.initTags()
    EventBus.$on(`sidebarClick_${this.bingLiID}`, (menu) => {
      this.addTags(menu)
    })
  },
  destroyed() {
    EventBus.$off(`sidebarClick_${this.bingLiID}`)
    this.visitedViews = []
    this.initTags()
  },
  methods: {
    initTags() {
      const tag = {
        name: 'InPatientOrders',
        component: loadView('patient-inside/inpatient-orders-menu/InPatientOrders'),
        meta: {
          title: '住院医嘱',
          affix: true
        },
        caiDanLX: 'C'
      }
      this.visitedViews.push(
        Object.assign({}, tag, {
          title: tag.meta.title || 'no-name'
        })
      )
      this.activeRoute = tag.name
    },
    addTags(menu) {
      if (!this.visitedViews.some((v) => v.name === menu.name)) {
        this.visitedViews.push(
          Object.assign({}, menu, {
            title: menu.meta.title || 'no-name'
          })
        )
      }
      this.activeRoute = menu.name
    },
    refreshSelectedTag() {
      this.refreshKey += 1
      this.closeMenu()
    },
    closeSelectedTag(name) {
      const index = this.visitedViews.findIndex((v) => v.name === name)
      this.visitedViews.splice(index, 1)
      if (this.activeRoute === name) {
        this.toLastView(this.visitedViews)
      }
    },
    closeOthersTags() {
      if (!this.selectedTag || !this.selectedTag.name) {
        this.closeMenu()
        return
      }

      const currentTagName = this.selectedTag.name
      this.visitedViews = this.visitedViews.filter(
        (v) => v.meta?.affix || v.name === currentTagName
      )

      // 确保当前标签仍然是激活状态
      if (this.visitedViews.some(v => v.name === currentTagName)) {
        this.activeRoute = currentTagName
      } else {
        // 如果当前标签被意外关闭，切换到默认标签
        this.activeRoute = 'InPatientOrders'
      }

      this.closeMenu()
    },
    closeAllTags() {
      // 只保留固定标签（住院医嘱）
      this.visitedViews = this.visitedViews.filter(v => v.meta?.affix)

      // 切换到默认的住院医嘱标签
      this.activeRoute = 'InPatientOrders'

      this.closeMenu()
    },
    // 跳转到最后一个标签
    toLastView(visitedViews) {
      const latestView = visitedViews.slice(-1)[0]
      if (latestView) {
        this.activeRoute = latestView.name
      } else {
        this.activeRoute = 'InPatientOrders'
      }
    },
    openMenu(e) {
      // 更可靠的方式获取被右键点击的标签
      let targetElement = e.target
      let tabName = null

      // 向上查找包含标签信息的元素
      while (targetElement && !tabName) {
        if (targetElement.id && targetElement.id.includes('tab-')) {
          const idParts = targetElement.id.split('tab-')
          if (idParts.length > 1 && !isNumberStr(idParts[1])) {
            tabName = idParts[1]
          }
        }

        // 也可以通过 aria-controls 属性获取标签名
        if (!tabName && targetElement.getAttribute && targetElement.getAttribute('aria-controls')) {
          const ariaControls = targetElement.getAttribute('aria-controls')
          if (ariaControls && ariaControls.startsWith('pane-')) {
            tabName = ariaControls.replace('pane-', '')
          }
        }

        targetElement = targetElement.parentElement
      }

      if (tabName) {
        this.left = e.clientX
        this.top = e.clientY
        this.visible = true
        this.selectedTag = this.visitedViews.find((v) => v.name === tabName)
        console.log('右键点击的标签:', tabName, this.selectedTag)
      }
    },
    closeMenu() {
      this.visible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  ::v-deep .el-tabs {
    height: 100%;
    display: flex;
    flex-direction: column;
    .el-tabs__header {
      background: #fff;
      flex-shrink: 0;
      .el-tabs__nav {
        .el-tabs__item {
          height: 32px;
          line-height: 32px;
          padding: 0 10px;
        }
      }
    }
    .el-tabs__content {
      flex: 1;
      min-height: 0;
      .el-tab-pane {
        height: 100%;
        overflow: hidden;
      }
    }
  }

  .contextmenu {
    position: fixed;
    z-index: 3000;
    padding: 5px 0;
    margin: 0;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    list-style-type: none;
    background: #fff;
    border-radius: 4px;
    box-shadow: 2px 2px 3px 0 rgb(0 0 0 / 30%);

    li {
      padding: 7px 16px;
      margin: 0;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }

  // }
}
</style>
