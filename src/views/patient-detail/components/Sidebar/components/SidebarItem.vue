<template>
  <div v-if="!item.hidden && lowChildNum < 2" :class="[lowChildNum === 1 && 'level-two']">
    <div v-if="ifRenderOnlyChild(item)" class="menu-item-single">
      <app-link
        v-if="onlyOneChild.meta"
        :to="
          lowChildNum === 1 && item.children?.length > 0 ? basePath : resolvePath(onlyOneChild.path)
        "
        @otherClick="itemClickNative(onlyOneChild)"
      >
        <el-menu-item
          :index="item.name"
          :class="{
            'submenu-title-noDropdown': !isNest
          }"
        >
          <item :icon-visible="!isNest" :x-meta="item.meta || onlyOneChild.meta" />
        </el-menu-item>
      </app-link>
    </div>
    <div v-else>
      <el-submenu
        ref="subMenu"
        :index="item.name"
        :popper-class="nightMode ? 'el-menu--vertical__nightMode' : ''"
      >
        <template slot="title">
          <item :x-meta="item.meta || onlyOneChild.meta" />
        </template>
        <sidebar-item
          v-for="(child, index) in item.children"
          :key="index"
          :is-nest="true"
          :item="child"
          :low-child-num="lowChildNum + 1"
          :item-click-native-val="itemClickNativeVal"
          @itemClickNative="$emit('itemClickNative', ...arguments)"
        />
      </el-submenu>
    </div>
  </div>
</template>

<script>
import path from 'path'
import { isExternal } from '@/utils/validate'
import Item from './Item.vue'
import AppLink from './Link.vue'
import FixiOSBug from './fix.js'
import { mapState } from 'vuex'
export default {
  name: 'SidebarItem',
  components: { Item, AppLink },
  mixins: [FixiOSBug],
  props: {
    // route object
    item: {
      type: Object,
      required: true
    },
    isNest: {
      type: Boolean,
      default: false
    },
    basePath: {
      type: String,
      default: ''
    },
    // 层级
    lowChildNum: {
      type: Number,
      default: 0
    },
    itemClickNativeVal: {
      type: String,
      default: ''
    }
  },
  data() {
    // To fix https://github.com/PanJiaChen/vue-admin-template/issues/237
    // TODO: refactor with render function
    return {
      currentLowChildNum: 0
    }
  },
  computed: {
    ...mapState({
      nightMode: ({ theme }) => theme?.nightMode
    }),
    onlyOneChild() {
      const { caiDanLX } = this?.item
      // 菜单类型
      if (caiDanLX === 'C') {
        return this?.item?.children?.length === 1
          ? this?.item?.children[0]
          : { ...this.item, path: '' }
      }
      // 目录类型
      if (caiDanLX === 'M') {
        // 获取item.children最后一个元素
        return this.item?.children?.[this.item?.children?.length - 1]

        // return (
        //   this.item?.children.filter((f) => {
        //     this.$route.path.includes(f.path)
        //   })?.[0] || this.item?.children[0]
        // )
      }
      // 按钮、功能类型
      if (caiDanLX === 'F') {
        return this?.item
      }
      return {}
    },
    metaIcon() {
      return this.item?.meta?.icon || this.onlyOneChild?.meta?.icon
    },
    metaPicUrl() {
      return this.item?.meta?.url || this.onlyOneChild?.meta?.url
    }
  },
  mounted() {},
  methods: {
    ifRenderOnlyChild(item) {
      const { caiDanLX, hidden } = item
      if (hidden) return false
      // 一级且含子项时渲染
      if (this.lowChildNum === 1 && item.children?.length > 0) {
        return true
      }
      // 菜单类型
      if (caiDanLX === 'C') {
        return true
      }
      // 目录类型
      if (caiDanLX === 'M') {
        return false
      }
      // 按钮、功能类型
      if (caiDanLX === 'F') {
        return true
      }
      return true
    },
    resolvePath(_path) {
      if (isExternal(_path)) {
        return _path
      }
      return path.resolve(this.basePath, _path)
    },
    itemClickNative(to) {
      this.$emit('itemClickNative', to)
    }
  }
}
</script>
<style lang="scss" scoped>
.level-two {
  ::v-deep .el-submenu__icon-arrow {
    display: none !important;
  }

  ::v-deep .el-submenu .el-menu {
    padding: 0 !important;
  }
}

::v-deep.is-attachSidebar-parent {
  &.is-active {
    background: #5888dc !important;
    border-radius: 10px 0 0 10px !important;

    .menu-item-title {
      color: rgb(255 255 255 / 80%) !important;
    }
  }

  &.is-attachSidebar-parent-active {
    position: relative;
    background: #f2f4f5 !important;
    border-radius: 10px 0 0 10px !important;

    .menu-item-title {
      color: #356ac5 !important;
    }
  }
}
</style>
