<script lang="jsx">
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    iconVisible: {
      type: Boolean,
      default: true
    },
    metaPicUrl: {
      type: String,
      default: ''
    },
    xMeta: {
      type: Object,
      default: undefined
    }
  },
  render(h, context) {
    const {
      iconVisible,
      xMeta: { icon, title, url, caiDanTB }
    } = context.props

    const vnodes = []

    if (iconVisible && caiDanTB) {
      vnodes.push(
        <span class="el-img-box">
          <i class={['el-img', 'iconfont', 'sub-el-icon', caiDanTB]}></i>
        </span>
      )
    } else {
      vnodes.push(
        <span class="el-img-box">
          <div class="el-img" />
        </span>
      )
    }
    if (title) {
      vnodes.push(
        <span
          slot="title"
          class="menu-item-title"
          title={title}
          style="overflow:hidden;white-space:nowrap;text-overflow:ellipsis;"
        >
          {title}
        </span>
      )
    }
    return vnodes
  }
}
</script>

<style scoped>
.sub-el-icon {
  width: 1em;
  height: 1em;
  color: currentcolor;
}
</style>
