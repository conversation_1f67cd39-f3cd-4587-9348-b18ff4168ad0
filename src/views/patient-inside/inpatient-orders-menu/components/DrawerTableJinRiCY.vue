<template>
  <el-select
    v-if="shouldShowSelect"
    ref="selectComponent"
    v-model="selectValue"
    style="width: 100%"
    size="mini"
    popper-class="drawer-table-select-dropdown"
    @focus="handleFocus"
    @change="handleChange"
  >
    <el-option
      v-for="item in selectOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
  <el-input
    v-else
    v-model="inputValue"
    class="row-input"
    size="mini"
    disabled
    @focus="handleFocus"
  />
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'DrawerTableJinRiCY',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    },
    inpatientInit: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    }),
    // 判断是否应该显示下拉选择框
    shouldShowSelect() {
      const { yiZhuLX } = this.row

      // 草药带药(zcydy) + 中医科(专科ID=33)
      if (yiZhuLX === 'zcydy' && this.zhuanKeID === '33') {
        return true
      }

      // 出院带药(cydy)
      if (yiZhuLX === 'cydy') {
        return true
      }

      return false
    },
    // 下拉选项
    selectOptions() {
      const { yiZhuLX } = this.row

      // 草药带药(zcydy) + 中医科(专科ID=33)
      if (yiZhuLX === 'zcydy' && this.zhuanKeID === '33') {
        return [
          { value: '1', label: '患者自取' },
          { value: '2', label: '医院配送' },
          { value: '3', label: ' 医院快递' }
        ]
      }

      // 出院带药(cydy)
      if (yiZhuLX === 'cydy') {
        return [
          { value: '1', label: '患者自取' },
          { value: '2', label: '医院配送' }
        ]
      }

      return []
    },
    // 下拉框值
    selectValue: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    },
    // 输入框值
    inputValue: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    }
  },
  watch: {
    // 监听医嘱类型变化，设置默认值
    'row.yiZhuLX': {
      handler(newValue) {
        if (this.shouldShowSelect) {
          // 设置默认值
          if (newValue === 'zcydy' && this.zhuanKeID === '33') {
            // 草药带药 + 中医科：默认"患者自取"
            if (!this.selectValue) {
              this.selectValue = '1'
            }
          } else if (newValue === 'cydy') {
            // 出院带药：默认"医院配送"
            if (!this.selectValue) {
              this.selectValue = '2'
            }
          }
        } else {
          // 其他情况清空值
          this.selectValue = ''
        }
      },
      immediate: true
    }
  },
  methods: {
    handleChange(value) {
      // 处理下拉选择变化
      this.$emit('updateRow', { prop: this.column.value, updateValue: value })
    },
    handleFocus() {
      // 使用延迟确保不干扰el-select的内部逻辑
      this.$nextTick(() => {
        // 只有在不是正在选择状态时才触发焦点事件
        if (!this.isSelecting) {
          this.$emit('inputFocus', {
            prop: this.column.value,
            inputValue: this.shouldShowSelect ? this.selectValue : this.inputValue
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input__inner {
  padding: 0 20px 0 5px;
}
</style>
