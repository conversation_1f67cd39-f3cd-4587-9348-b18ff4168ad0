<template>
  <el-tag size="small" :type="type">{{ row.zhuangTaiBZMC }}</el-tag>
</template>

<script>
export default {
  name: 'TableLink',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    type() {
      switch (this.row.zhuangTaiBZMC) {
        case '已导出':
          return 'success'
        case '停止':
        case '到期':
          return 'danger'
        case '未导出':
          return 'primary'
        default:
          return 'info'
      }
    }
  },
  mounted() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
a {
  color: #409eff;
}
</style>
