<template>
  <el-drawer
    :visible="visible"
    destroy-on-close
    :wrapper-closable="false"
    :append-to-body="false"
    :modal="false"
    :show-close="false"
    :size="drawerFullScreen ? '100%' : '75%'"
    direction="btt"
  >
    <template #title>
      <div class="drawer-header">
        <span class="full-button" @click="drawerFullScreen = !drawerFullScreen">
          <i :class="drawerFullScreen ? 'el-icon-caret-bottom' : 'el-icon-caret-top'"></i>
        </span>
        <div class="title">新增医嘱</div>
        <span>药品检索方式：</span>
        <el-radio-group v-model="yaoPinJianSuo" size="small">
          <el-radio label="0">精确</el-radio>
          <el-radio label="1">模糊</el-radio>
        </el-radio-group>
        <div v-if="tabActive != 'cq'" style="margin-left: 10px">
          <span>草药总价:</span>
          &nbsp;
          <span>{{ cyTotalPrice }}元</span>
        </div>
        <div v-if="muRuKCL" style="margin-left: 10px">
          <span>当前母乳库存量：{{ muRuKCL }}</span>
        </div>
      </div>
    </template>
    <el-table
      ref="drawerListTable"
      :data="drawerYiZhuList"
      border
      stripe
      height="100%"
      size="mini"
      @row-click="handleRowClick"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="30"></el-table-column>
      <el-table-column
        v-for="column in drawerColumns"
        :key="column.value"
        :prop="column.value"
        :label="column.label"
        align="center"
        v-bind="column.props"
      >
        <template v-if="column.component" #default="{ row, $index }">
          <!-- 动态加载组件 -->
          <component
            :is="column.component"
            :row="row"
            :column="getDynamicColumn(column, row)"
            :tab-active="tabActive"
            :inpatient-init="inpatientInit"
            :data-row-index="$index"
            :data-field="column.value"
            @updateRow="({ prop, updateValue }) => handleUpdateRow(row, prop, updateValue)"
            @updateColumns="handleUpdateColumns"
            @inputBlur="({ prop, inputValue }) => handleInputBlur(row, prop, inputValue)"
            @inputFocus="({ prop, inputValue }) => handleInputFocus(row, prop, inputValue)"
            @keyboardNext="() => handleKeyboardNext($index, column.value)"
          />
        </template>
      </el-table-column>
    </el-table>
  </el-drawer>
</template>
<script>
import { checkZhongYaoYiCiYL, getYYTS } from '@/api/inpatient-order'

export default {
  name: 'DrawerYiZhuList',
  components: {
    DrawerTableDatePicker: () => import('./DrawerTableDatePicker.vue'),
    DrawerTableSelect: () => import('./DrawerTableSelect.vue'),
    DrawerTableDrugList: () => import('./DrawerTableDrugList.vue'),
    DrawerTableInput: () => import('./DrawerTableInput.vue'),
    DrawerTableCheckbox: () => import('./DrawerTableCheckbox.vue'),
    DrawerTableJinRiCY: () => import('./DrawerTableJinRiCY.vue'),
    DrawerTableZanShiBQ: () => import('./DrawerTableZanShiBQ.vue'),
    DrawerTableTeShuYF: () => import('./DrawerTableTeShuYF.vue')
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => []
    },
    inpatientInit: {
      type: Object,
      default: () => {}
    },
    tabActive: {
      type: String,
      default: 'cq'
    }
  },
  data() {
    return {
      drawerFullScreen: false,
      yaoPinJianSuo: '0',
      focusSuccessful: false, // 添加焦点成功标志
      // 键盘导航相关
      keyboardNavigation: {
        enabled: true,
        currentRowIndex: 0,
        currentFieldIndex: 0
      },
      // 结束时间字段状态跟踪
      jieShuSJState: {},
      baseDrawerColumns: [
        { value: 'xuNiZH', label: '组号', props: { width: '40' } },
        {
          value: 'yiZhuLX',
          label: '医嘱类型',
          props: { width: '80' },
          component: 'DrawerTableSelect',
          options: [{ value: 'cq', label: '长期医嘱' }]
        },
        {
          value: 'kaiShiSJ',
          label: '计划开始日期',
          props: { width: '150' },
          component: 'DrawerTableDatePicker'
        },
        {
          value: 'mingCheng',
          label: '医嘱名称',
          props: { width: '170' },
          component: 'DrawerTableDrugList',
          moHuSS: this.yaoPinJianSuo,
          inpatientInit: this.inpatientInit
        },
        { value: 'zuHaoTXT', label: '组', props: { width: '25' } },
        {
          value: 'yiCiYL',
          label: '一次用量',
          component: 'DrawerTableInput',
          props: { width: '65' }
        },
        {
          value: 'jiLiangDW',
          label: '剂量单位',
          props: { width: '70' },
          component: 'DrawerTableSelect',
          options: []
        },
        {
          value: 'zhiXingFF',
          label: '用法',
          props: { width: '80' },
          component: 'DrawerTableSelect',
          options: []
        },
        {
          value: 'zhiXingPL',
          label: '频率',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: []
        },
        {
          value: 'geiYaoSJ',
          label: '用药/执行时间',
          props: { width: '100' },
          component: 'DrawerTableSelect',
          options: []
        },
        {
          value: 'guiGe',
          label: '规格/单价',
          props: { width: '100' },
          component: 'DrawerTableInput'
        },
        { value: 'yiShengXM', label: '医师姓名', props: { width: '80' } },
        {
          value: 'leiBie',
          label: '类别',
          props: { width: '60' },
          component: 'DrawerTableSelect',
          options: [
            { value: '3', label: '药品' },
            { value: '1', label: '治疗' },
            { value: '2', label: '饮食' },
            { value: '4', label: '嘱托' }
          ]
        },
        { value: 'feiYongLXMC', label: '费用类型' },
        {
          value: 'shiFouZB',
          label: '自备',
          component: 'DrawerTableCheckbox',
          props: { width: '50' }
        },
        {
          value: 'jinRiLT',
          label: '今日临停',
          component: 'DrawerTableCheckbox',
          props: { width: '50' }
        },
        {
          value: 'teShuYF',
          label: '特殊用法/备注说明',
          props: { width: '150' },
          component: 'DrawerTableTeShuYF'
        },
        {
          value: 'bingQuID',
          label: '执行病区',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: []
        },
        {
          value: 'shouFeiSL',
          label: '数量/剂数',
          props: { width: '75' },
          component: 'DrawerTableInput'
        }, // 收费数量
        {
          value: 'chiXuTS',
          label: '持续天数',
          props: { width: '70' },
          component: 'DrawerTableInput'
        },
        {
          value: 'yongYaoTS',
          label: '用药天数',
          props: { width: '70' },
          component: 'DrawerTableInput'
        },
        { value: 'danWei', label: '单位', props: { width: '70' } },
        { value: 'shouFeiCS', label: '收费次数', props: { width: '70' } },
        {
          value: 'jinRiCY',
          label: '预计出院',
          props: { width: '100' },
          component: 'DrawerTableJinRiCY'
        },
        {
          value: 'zanShiBQ',
          label: '是否代煎',
          props: { width: '100' },
          component: 'DrawerTableZanShiBQ'
        },
        {
          value: 'yaoPinDSBZ',
          label: '滴速',
          props: { width: '120' },
          component: 'DrawerTableInput'
        }, // 药品滴速备注
        {
          value: 'jieShuSJ',
          label: '结束时间',
          props: { width: '150' },
          component: 'DrawerTableDatePicker'
        },
        {
          value: 'tongZhiDanID',
          label: '手术通知单',
          props: { width: '130' },
          component: 'DrawerTableSelect',
          options: []
        },
        { value: 'luRuSJ', label: '医嘱时间', props: { width: '90' } }, // 录入时间
        { value: 'daoRuRYXM', label: '导入人员' },
        { value: 'daoRuSJ', label: '导入时间', props: { width: '90' } }
      ],
      checkYiCiYLTimer: null,
      cyTotalPrice: 0
    }
  },
  computed: {
    drawerYiZhuList: {
      get() {
        return this.value
      },
      set(newValue) {
        this.$emit('input', [...newValue])
      }
    },
    // 动态生成列配置，根据是否为中医科室决定是否显示煎法列
    drawerColumns() {
      const columns = [...this.baseDrawerColumns]

      // 如果是中医科室，在用法列后面插入煎法列
      if (this.inpatientInit?.yiShengZKZYKS === '1') {
        const zhiXingFFIndex = columns.findIndex((col) => col.value === 'zhiXingFF')
        if (zhiXingFFIndex !== -1) {
          const jianFaColumn = {
            value: 'jianFa',
            label: '煎法',
            props: { width: '80' },
            component: 'DrawerTableSelect',
            options: []
          }
          columns.splice(zhiXingFFIndex + 1, 0, jianFaColumn)
        }
      }

      return columns
    },
    muRuKCL() {
      return this.inpatientInit.muRuKCL || ''
    }
  },
  watch: {
    inpatientInit: {
      handler(newValue) {
        this.baseDrawerColumns.forEach((item) => {
          switch (item.value) {
            case 'geiYaoSJ':
              item.options = this.mapOptions(newValue?.geiYaoSJ, 'geiYaoSJDM', 'geiYaoSJMC')
              break
            case 'bingQuID':
              item.options = this.mapOptions(newValue?.zhiXingBQs, 'daiMa', 'mingCheng')
              break
            case 'tongZhiDanID':
              item.options = this.mapOptions(newValue?.shouShuTZDs, 'daiMa', 'mingCheng')
              break
            default:
              break
          }
        })
      },
      immediate: true
    },
    yaoPinJianSuo(newValue) {
      this.baseDrawerColumns.forEach((item) => {
        if (item.value === 'mingCheng') {
          item.moHuSS = newValue
        }
      })
    },
    tabActive: {
      handler(newValue) {
        this.baseDrawerColumns.forEach((item) => {
          if (item.value === 'yiZhuLX') {
            item.options =
              newValue === 'cq'
                ? [{ value: 'cq', label: '长期医嘱' }]
                : [
                    { value: 'ls', label: '临时医嘱' },
                    { value: 'cydy', label: '出院带药' },
                    { value: 'cy', label: '草药医嘱' },
                    { value: 'zcydy', label: '草药带药' }
                  ]
          }
        })
      },
      immediate: true
    }
  },
  mounted() {
    // 监听键盘事件
    this.$el.addEventListener('keydown', this.handleKeyDown)
    this.calculateCyTotalPrice()
  },
  beforeDestroy() {
    // 移除键盘事件监听
    this.$el.removeEventListener('keydown', this.handleKeyDown)
  },
  methods: {
    // 修改下拉选项
    mapOptions(data, valueKey, labelKey) {
      return (
        data?.map((item) => ({
          value: item[valueKey],
          label: item[labelKey]
        })) || []
      )
    },
    // 根据医嘱类型动态获取选项
    getDynamicOptions(fieldName, yiZhuLX) {
      if (!this.inpatientInit) return []

      switch (fieldName) {
        case 'zhiXingFF':
          // 当 yiZhuLX 为 "cy" 或 "zcydy" 时，使用草药用法
          if (yiZhuLX === 'cy' || yiZhuLX === 'zcydy') {
            return this.mapOptions(this.inpatientInit.caoYaoYF, 'daiMa', 'mingCheng')
          } else {
            return this.mapOptions(this.inpatientInit.feiCaoYaoYYFF, 'fangFaDM', 'fangFaMC')
          }
        case 'zhiXingPL':
          // 当 yiZhuLX 为 "cy" 或 "zcydy" 时，使用草药频率
          if (yiZhuLX === 'cy' || yiZhuLX === 'zcydy') {
            return this.mapOptions(this.inpatientInit.caoYaoPL, 'daiMa', 'mingCheng')
          } else {
            return this.mapOptions(this.inpatientInit.yongYaoPL, 'pinLuDM', 'pinLuMC')
          }
        default:
          return []
      }
    },
    // 获取动态列配置
    getDynamicColumn(column, row) {
      // 对于需要动态选项的字段，返回带有动态选项的新列配置
      if (column.value === 'zhiXingFF' || column.value === 'zhiXingPL') {
        return {
          ...column,
          options: this.getDynamicOptions(column.value, row.yiZhuLX)
        }
      }

      // 处理煎法列
      if (column.value === 'jianFa') {
        const isEditable = row.yiZhuLX === 'cy' || row.yiZhuLX === 'zcydy'
        return {
          ...column,
          options: this.mapOptions(this.inpatientInit?.caoYaoJF, 'fangFaDM', 'fangFaMC'),
          disabled: !isEditable
        }
      }

      // 处理数量/剂数列
      if (column.value === 'shouFeiSL') {
        const isDisabled = row.yiZhuLX === 'cq' || row.yiZhuLX === 'ls'
        return {
          ...column,
          disabled: isDisabled
        }
      }

      // 处理一次用量和剂量单位字段 - 当类别为"1-治疗"时禁用
      if (column.value === 'yiCiYL' || column.value === 'jiLiangDW') {
        const isDisabled = row.leiBie === '1'
        return {
          ...column,
          disabled: isDisabled
        }
      }

      // 其他字段返回原始列配置
      return column
    },
    // 根据医嘱类型和类别动态获取字段跳转顺序
    getFieldOrder(row) {
      if (!row) return ['mingCheng']

      // 优先判断医嘱类型
      if (row.yiZhuLX === 'cydy') {
        // 出院带药
        return [
          'mingCheng',
          'yiCiYL',
          'zhiXingFF',
          'zhiXingPL',
          'geiYaoSJ',
          'leiBie',
          'teShuYF',
          'shouFeiSL'
        ]
      } else if (row.yiZhuLX === 'cy' || row.yiZhuLX === 'zcydy') {
        // 草药类别
        return ['mingCheng', 'yiCiYL']
      }

      // 根据类别判断
      switch (row.leiBie) {
        case '1':
          // 治疗类别
          return ['mingCheng', 'zhiXingPL', 'leiBie', 'teShuYF', 'shouFeiSL']
        case '2':
          // 饮食类别
          return ['mingCheng', 'leiBie', 'teShuYF', 'jieShuSJ']
        case '3':
        default:
          // 药品类别（默认）
          return [
            'mingCheng',
            'yiCiYL',
            'zhiXingFF',
            'zhiXingPL',
            'geiYaoSJ',
            'leiBie',
            'teShuYF',
            'jieShuSJ'
          ]
      }
    },

    // 判断当前字段是否是跳转序列的最后一个字段
    isLastField(row, fieldName) {
      const fieldOrder = this.getFieldOrder(row)
      const currentIndex = fieldOrder.indexOf(fieldName)
      return currentIndex === fieldOrder.length - 1
    },

    // 键盘导航处理
    handleKeyDown(event) {
      if (!this.keyboardNavigation.enabled || !this.visible) {
        return
      }

      const { key, target } = event

      // 检查是否在药品搜索输入框中，如果是则不处理任何键盘事件
      const isInDrugInput = this.isTargetInDrugInput(target)
      if (isInDrugInput) {
        return
      }

      // 回车键处理
      if (key === 'Enter') {
        event.preventDefault()
        this.handleEnterKey(target)
      }
      // 其他键盘导航（Tab、左右方向键等）
      else if (key === 'Tab') {
        event.preventDefault()
        this.focusNextField()
      }
    },

    // 检查目标元素是否在药品搜索输入框中
    isTargetInDrugInput(target) {
      // 检查是否是药品搜索输入框
      const isPopoverReference = target.closest('.el-popover__reference')
      const isMingChengField = target.closest('[data-field="mingCheng"]')
      const isDrugInput =
        target.classList.contains('el-input__inner') && (isPopoverReference || isMingChengField)

      if (isDrugInput) {
        // 进一步检查是否有药品列表显示
        const drugComponents = this.findAllDrugComponents()
        for (let component of drugComponents) {
          if (component.visible && component.drugList.length > 0) {
            return true
          }
        }
      }

      return false
    },

    // 处理回车键逻辑
    handleEnterKey(target) {
      const currentRow = this.getCurrentRow()
      if (!currentRow) return

      const currentField = this.getCurrentField(currentRow)
      const rowKey = `${this.keyboardNavigation.currentRowIndex}_${currentField}`

      // 结束时间特殊处理
      if (currentField === 'jieShuSJ') {
        if (!currentRow.jieShuSJ) {
          // 第一次回车：填入当前时间
          this.fillCurrentTime(currentRow)
          // 标记已经填入过时间
          this.$set(this.jieShuSJState, rowKey, { hasFilledTime: true })
          return
        } else {
          // 第二次回车：新增项
          this.triggerAddItem()
          return
        }
      }

      // 判断是否是最后一个字段
      if (this.isLastField(currentRow, currentField)) {
        // 如果是最后一个字段且不是jieShuSJ，则新增医嘱项目
        if (currentField !== 'jieShuSJ') {
          this.triggerAddItem()
          return
        }
      }

      // 跳转到下一个字段
      this.focusNextField()
    },

    // 获取当前行
    getCurrentRow() {
      return this.drawerYiZhuList[this.keyboardNavigation.currentRowIndex]
    },

    // 获取当前字段
    getCurrentField(row) {
      const currentRow = row || this.getCurrentRow()
      if (!currentRow) return 'mingCheng'

      const fieldOrder = this.getFieldOrder(currentRow)
      return fieldOrder[this.keyboardNavigation.currentFieldIndex] || 'mingCheng'
    },

    // 跳转到下一个字段
    focusNextField() {
      const currentRow = this.getCurrentRow()
      if (!currentRow) return

      const fieldOrder = this.getFieldOrder(currentRow)
      let nextFieldIndex = this.keyboardNavigation.currentFieldIndex + 1

      // 如果超出当前行字段范围，跳转到下一行第一个字段
      if (nextFieldIndex >= fieldOrder.length) {
        nextFieldIndex = 0
        this.keyboardNavigation.currentRowIndex++

        // 如果超出行范围，创建新行
        if (this.keyboardNavigation.currentRowIndex >= this.drawerYiZhuList.length) {
          this.triggerAddItem()
          return
        }
      }

      this.keyboardNavigation.currentFieldIndex = nextFieldIndex
      this.focusField(this.keyboardNavigation.currentRowIndex, nextFieldIndex)
    },

    // 聚焦到指定字段
    focusField(rowIndex, fieldIndex) {
      const currentRow = this.drawerYiZhuList[rowIndex]
      if (!currentRow) return

      const fieldOrder = this.getFieldOrder(currentRow)
      const fieldName = fieldOrder[fieldIndex]
      if (!fieldName) return

      // 确保表格滚动到目标行
      this.scrollToRow(rowIndex)

      // 使用延迟确保DOM完全渲染和滚动完成
      setTimeout(() => {
        // 特殊处理药品搜索组件（DrawerTableDrugList）
        if (fieldName === 'mingCheng') {
          this.focusDrugListComponent(rowIndex)
          return
        }

        // 处理其他类型的输入组件
        this.focusRegularInput(rowIndex, fieldName)
      }, 600)
    },

    // 聚焦药品搜索组件
    focusDrugListComponent(rowIndex) {
      // 方案1: 通过$children递归查找所有DrawerTableDrugList组件
      const drugComponents = this.findAllDrugComponents()

      if (drugComponents.length > rowIndex && drugComponents[rowIndex]) {
        const targetComponent = drugComponents[rowIndex]
        if (typeof targetComponent.focusInput === 'function') {
          targetComponent.focusInput()

          // 验证焦点是否设置成功
          setTimeout(() => {
            const activeElement = document.activeElement
            if (
              activeElement &&
              activeElement.tagName === 'INPUT' &&
              activeElement.type !== 'checkbox'
            ) {
              this.focusSuccessful = true
            }
          }, 200)

          return
        }
      }

      // 方案2: 通过固定列DOM查找
      const drawerTable = this.$refs.drawerListTable
      if (!drawerTable) {
        return
      }

      const tableRows = drawerTable.$el.querySelectorAll('.el-table__fixed tbody tr.el-table__row')

      if (rowIndex < tableRows.length && tableRows[rowIndex]) {
        const targetRowElement = tableRows[rowIndex]

        // 查找mingCheng字段对应的单元格
        const targetCell = this.findMingChengCell(targetRowElement)

        if (targetCell) {
          // 查找Vue组件实例
          const vueComponent = this.findVueComponentInCell(targetCell, 'DrawerTableDrugList')

          if (vueComponent && typeof vueComponent.focusInput === 'function') {
            vueComponent.focusInput()
            return
          }

          // 备用方案：直接查找input元素（排除复选框）
          const input = targetCell.querySelector('input:not([type="checkbox"])')
          if (input && !input.disabled && input.offsetWidth > 0) {
            input.focus()
            if (input.value) {
              input.select()
            }
            return
          }
        }
      }

      // 方案3: 强制查找最后一个药品组件
      if (drugComponents.length > 0) {
        const lastComponent = drugComponents[drugComponents.length - 1]
        if (typeof lastComponent.focusInput === 'function') {
          lastComponent.focusInput()
          return
        }
      }
    },

    // 查找所有药品搜索组件
    findAllDrugComponents() {
      const components = []

      // 递归查找所有子组件
      const findComponents = (children) => {
        children.forEach((child) => {
          if (child.$options.name === 'DrawerTableDrugList') {
            components.push(child)
          }
          if (child.$children && child.$children.length > 0) {
            findComponents(child.$children)
          }
        })
      }

      findComponents(this.$children)
      return components
    },

    // 查找mingCheng字段对应的单元格
    findMingChengCell(rowElement) {
      // 方法1: 通过data-field属性查找
      let targetCell = rowElement.querySelector('[data-field="mingCheng"]')
      if (targetCell) {
        return targetCell
      }

      // 方法2: 通过列索引计算
      const fixedColumns = this.drawerColumns.filter((col) => col.props && col.props.fixed)
      const mingChengIndex = fixedColumns.findIndex((col) => col.value === 'mingCheng')

      if (mingChengIndex !== -1) {
        // +1 是因为有选择列
        const cellIndex = mingChengIndex + 1
        if (rowElement.children[cellIndex]) {
          return rowElement.children[cellIndex]
        }
      }

      // 方法3: 查找包含el-popover的单元格
      const cells = rowElement.querySelectorAll('td')
      for (let cell of cells) {
        if (cell.querySelector('.el-popover') || cell.querySelector('[ref="drugInput"]')) {
          return cell
        }
      }

      return null
    },

    // 聚焦常规输入组件
    focusRegularInput(rowIndex, fieldName) {
      const drawerTable = this.$refs.drawerListTable
      if (!drawerTable) return

      // 检查字段是否在固定列中
      const targetColumn = this.drawerColumns.find((col) => col.value === fieldName)
      const isFixed = targetColumn && targetColumn.props && targetColumn.props.fixed

      const selector = isFixed
        ? '.el-table__fixed tbody tr.el-table__row'
        : '.el-table__body-wrapper tbody tr.el-table__row'

      const tableRows = drawerTable.$el.querySelectorAll(selector)

      if (tableRows[rowIndex]) {
        const targetRow = tableRows[rowIndex]
        const columnIndex = this.drawerColumns.findIndex((col) => col.value === fieldName)
        if (columnIndex !== -1) {
          const targetCell = targetRow.children[columnIndex + 1]
          if (targetCell) {
            // 查找不同类型的输入元素
            let inputElement = targetCell.querySelector('input')
            if (!inputElement) {
              inputElement = targetCell.querySelector('.el-input__inner')
            }
            if (!inputElement) {
              inputElement = targetCell.querySelector('.el-select input')
            }
            if (!inputElement) {
              inputElement = targetCell.querySelector('.el-date-editor input')
            }

            if (inputElement) {
              inputElement.focus()
            }
          }
        }
      }
    },

    // 在单元格中查找Vue组件实例
    findVueComponentInCell(cell, componentName) {
      const elements = cell.querySelectorAll('*')

      for (let i = 0; i < elements.length; i++) {
        const element = elements[i]

        if (element.__vue__) {
          if (element.__vue__.$options.name === componentName) {
            return element.__vue__
          }
        }
      }

      return null
    },

    // 滚动表格到指定行
    scrollToRow(rowIndex) {
      this.$nextTick(() => {
        const drawerTable = this.$refs.drawerListTable

        if (!drawerTable) {
          return
        }

        // 查找表格容器
        const tableContainer = drawerTable.$el.querySelector('.el-table__body-wrapper')
        const tableRows = drawerTable.$el.querySelectorAll('tbody tr.el-table__row')

        if (tableRows[rowIndex]) {
          const targetRow = tableRows[rowIndex]

          // 方式1: 滚动表格容器
          if (tableContainer) {
            const containerRect = tableContainer.getBoundingClientRect()
            const rowRect = targetRow.getBoundingClientRect()
            const scrollTop =
              tableContainer.scrollTop +
              (rowRect.top - containerRect.top) -
              containerRect.height / 2

            tableContainer.scrollTo({
              top: Math.max(0, scrollTop),
              behavior: 'smooth'
            })
          } else {
            // 方式2: 直接滚动行元素
            targetRow.scrollIntoView({
              behavior: 'smooth',
              block: 'center'
            })
          }
        }
      })
    },

    // 填入当前时间
    async fillCurrentTime(row) {
      try {
        // 通过事件向父组件请求当前时间
        this.$emit('requestCurrentTime', (currentTime) => {
          this.$set(row, 'jieShuSJ', currentTime)
        })
      } catch (error) {
        console.error('获取当前时间失败:', error)
      }
    },

    // 触发新增项
    triggerAddItem() {
      // 通过事件向父组件请求新增项
      this.$emit('requestAddItem')
    },

    // 初始化焦点到最后一行名称字段
    initializeFocus() {
      if (this.drawerYiZhuList.length === 0) {
        return
      }

      const lastRowIndex = this.drawerYiZhuList.length - 1

      // 重置焦点成功标志
      this.focusSuccessful = false

      this.keyboardNavigation.currentRowIndex = lastRowIndex
      this.keyboardNavigation.currentFieldIndex = 0

      // 增加延迟，确保DOM已经更新
      this.$nextTick(() => {
        setTimeout(() => {
          this.focusField(lastRowIndex, 0)

          // 备用强制聚焦方案 - 只在主要方案失败时执行
          setTimeout(() => {
            if (!this.focusSuccessful) {
              this.forceFocusToLastRow()
            }
          }, 500)
        }, 200)
      })
    },

    // 强制聚焦到最后一行的药品名称字段（备用方案）
    forceFocusToLastRow() {
      try {
        // 首先检查当前焦点是否已经正确
        const activeElement = document.activeElement
        if (
          activeElement &&
          activeElement.tagName === 'INPUT' &&
          activeElement.type !== 'checkbox'
        ) {
          this.focusSuccessful = true
          return
        }

        // 方案1: 查找所有药品搜索组件，聚焦最后一个
        const drugComponents = this.findAllDrugComponents()
        if (drugComponents.length > 0) {
          const lastComponent = drugComponents[drugComponents.length - 1]
          if (typeof lastComponent.focusInput === 'function') {
            lastComponent.focusInput()
            return
          }
        }

        // 方案2: 直接查找固定列最后一行的非复选框input
        const tableRows = this.$el.querySelectorAll('.el-table__fixed tbody tr.el-table__row')

        if (tableRows.length > 0) {
          const lastRow = tableRows[tableRows.length - 1]
          const input = lastRow.querySelector('input:not([type="checkbox"])')

          if (input) {
            input.focus()
            if (input.value) {
              input.select()
            }
            return
          }
        }
      } catch (error) {
        // 静默处理错误
      }
    },
    // 处理键盘导航下一个字段
    handleKeyboardNext(rowIndex, fieldName) {
      const currentRow = this.drawerYiZhuList[rowIndex]
      if (!currentRow) return

      const fieldOrder = this.getFieldOrder(currentRow)
      const fieldIndex = fieldOrder.indexOf(fieldName)
      if (fieldIndex !== -1) {
        this.keyboardNavigation.currentRowIndex = rowIndex
        this.keyboardNavigation.currentFieldIndex = fieldIndex

        // 结束时间字段特殊处理
        if (fieldName === 'jieShuSJ') {
          const rowKey = `${rowIndex}_${fieldName}`

          if (!currentRow.jieShuSJ) {
            // 第一次回车：填入当前时间
            this.fillCurrentTime(currentRow)
            // 标记已经填入过时间
            this.$set(this.jieShuSJState, rowKey, { hasFilledTime: true })
            return
          } else {
            // 第二次回车：新增项
            this.triggerAddItem()
            return
          }
        }

        // 判断是否是最后一个字段
        if (this.isLastField(currentRow, fieldName)) {
          // 如果是最后一个字段且不是jieShuSJ，则新增医嘱项目
          if (fieldName !== 'jieShuSJ') {
            this.triggerAddItem()
            return
          }
        }

        this.focusNextField()
      }
    },

    async handleUpdateRow(row, prop, updateValue) {
      if (typeof updateValue === 'object' && prop === 'mingCheng') {
        // 更新多个字段
        Object.keys(updateValue).forEach((key) => {
          this.$set(row, key, updateValue[key])
          // 处理医嘱类型、开始时间、执行频率、执行方法、给药时间、结束时间、预计出院、是否代煎的全组同步
          if (
            key === 'yiZhuLX' ||
            key === 'kaiShiSJ' ||
            key === 'zhiXingPL' ||
            key === 'zhiXingFF' ||
            key === 'geiYaoSJ' ||
            key === 'jieShuSJ' ||
            key === 'jinRiCY' ||
            key === 'zanShiBQ'
          ) {
            const currentPrefix = row.xuNiZH.split('-')[0]
            this.drawerYiZhuList.forEach((item) => {
              const itemPrefix = item.xuNiZH.split('-')[0]
              if (itemPrefix === currentPrefix) {
                this.$set(item, key, updateValue[key])
                if (!item.leiBie) {
                  this.$set(item, 'leiBie', '3')
                }
                // 如果是更新医嘱类型，且是草药或草药带药
                if (prop === 'yiZhuLX') {
                  if (updateValue['yiZhuLX'] === 'cy' || updateValue['yiZhuLX'] === 'zcydy') {
                    this.$set(item, 'leiBie', '3')
                    this.$set(item, 'zhiXingFF', '煎服')
                    this.$set(item, 'zhiXingPL', 'bid')
                    this.$set(item, 'geiYaoSJ', 'pc')
                    this.$set(item, 'shouFeiSL', '7')
                    let count = 1
                    this.drawerYiZhuList.forEach((groupItem) => {
                      const groupItemPrefix = groupItem.xuNiZH.split('-')[0]
                      if (groupItemPrefix === currentPrefix) {
                        this.$set(groupItem, 'xuNiZH', `${currentPrefix}-${count}`)
                        count++
                      }
                    })
                  } else {
                    this.$set(item, 'xuNiZH', currentPrefix)
                    this.$set(item, 'jianFa', '')
                    // 如果医嘱类型变为长期或临时医嘱，清空数量/剂数
                    if (updateValue['yiZhuLX'] === 'cq' || updateValue['yiZhuLX'] === 'ls') {
                      this.$set(item, 'shouFeiSL', '')
                    }
                  }
                }
              }
              for (let i in item) {
                if (item[i] === null) {
                  this.$set(item, i, '')
                }
              }
            })
          }
        })
      } else {
        // 更新单个字段
        this.$set(row, prop, updateValue)
        // 处理全组同步
        if (
          prop === 'yiZhuLX' ||
          prop === 'kaiShiSJ' ||
          prop === 'zhiXingPL' ||
          prop === 'zhiXingFF' ||
          prop === 'geiYaoSJ' ||
          prop === 'leiBie' ||
          prop === 'jieShuSJ' ||
          prop === 'jinRiCY' ||
          prop === 'zanShiBQ'
        ) {
          const currentPrefix = row.xuNiZH.split('-')[0]
          this.drawerYiZhuList.forEach((item) => {
            const itemPrefix = item.xuNiZH.split('-')[0]
            if (itemPrefix === currentPrefix) {
              this.$set(item, prop, updateValue)
              if (!item.leiBie) {
                this.$set(item, 'leiBie', '3')
              }
              // 如果是更新医嘱类型，且是草药或草药带药
              if (prop === 'yiZhuLX') {
                if (updateValue === 'cy' || updateValue === 'zcydy') {
                  this.$set(item, 'leiBie', '3')
                  this.$set(item, 'zhiXingFF', '煎服')
                  this.$set(item, 'zhiXingPL', 'bid')
                  this.$set(item, 'geiYaoSJ', 'pc')
                  this.$set(item, 'shouFeiSL', '7')
                  let count = 1
                  this.drawerYiZhuList.forEach((groupItem) => {
                    const groupItemPrefix = groupItem.xuNiZH.split('-')[0]
                    if (groupItemPrefix === currentPrefix) {
                      this.$set(groupItem, 'xuNiZH', `${currentPrefix}-${count}`)
                      count++
                    }
                  })
                } else {
                  this.$set(item, 'xuNiZH', currentPrefix)
                  this.$set(item, 'jianFa', '')
                  // 如果医嘱类型变为长期或临时医嘱，清空数量/剂数
                  if (updateValue === 'cq' || updateValue === 'ls') {
                    this.$set(item, 'shouFeiSL', '')
                  }
                }
              }
            }
          })
        }
      }

      // 切换类别拉下框选项
      let options = []
      switch (row.yiZhuLX) {
        case 'cq':
          options = [
            { value: '3', label: '药品' },
            { value: '1', label: '治疗' },
            { value: '2', label: '饮食' },
            { value: '4', label: '嘱托' }
          ]
          break
        case 'ls':
          options = [
            { value: '3', label: '药品' },
            { value: '1', label: '治疗' },
            { value: '4', label: '嘱托' }
          ]
          break
        default:
          options = [{ value: '3', label: '药品' }]
          break
      }
      this.baseDrawerColumns.forEach((item) => {
        if (item.value === 'leiBie') {
          item.options = options
        }
      })
    },
    handleUpdateColumns({ column, options }) {
      this.baseDrawerColumns.forEach((item) => {
        if (item.value === column) {
          item.options = options
        }
      })
    },
    async handleInputBlur(row, prop, inputValue) {
      switch (prop) {
        case 'yiCiYL':
          // 一次用量校验
          if (row.zhiXingFF && row.zhiXingPL) {
            if (row.changYongLiang && row.yiCiYL > Number(row.changYongLiang) * 2) {
              await this.$alert(
                `【${row.mingCheng}】您的一次用量超过常用量的2倍，请注意！`,
                '提示',
                {
                  confirmButtonText: '确定',
                  type: 'info'
                }
              )
            }

            // 草药一次用量校验
            try {
              if (
                row.yiCiYL &&
                row.yaoPinData.jiXing === 'Ypj' &&
                (row.yiZhuLX === 'cy' || row.yiZhuLX === 'zcydy')
              ) {
                await checkZhongYaoYiCiYL({
                  jiLiangDW: row.jiLiangDW,
                  yaoFangDM: row.yaoPinData.yaoFangDM,
                  yaoPinID: row.yaoPinData.yaoPinID,
                  yaoPinMC: encodeURIComponent(row.mingCheng),
                  yiCiYL: row.yiCiYL
                })
              }
            } catch (e) {
              if (e.errorMessage) {
                await this.$alert(e.errorMessage, '提示', {
                  confirmButtonText: '确定',
                  type: 'info'
                })
              }
            }
          }
          this.calculateCyTotalPrice()
          break
        case 'shouFeiSL':
          if (row.yiZhuLX === 'cydy') {
            if (
              row.yiCiYL &&
              row.jiLiangDW &&
              row.zhiXingFF &&
              row.zhiXingPL &&
              row.shouFeiSL &&
              row?.yaoPinData?.yaoPinID
            ) {
              const res = await getYYTS({
                yiCiYL: row.yiCiYL,
                jiLiangDW: row.jiLiangDW,
                yongYaoFF: row.zhiXingFF,
                yongYaoPL: row.zhiXingPL,
                shuLiang: row.shouFeiSL,
                yaoFangDM: row.yaoPinData.yaoFangDM,
                yaoPinID: row.yaoPinData.yaoPinID
              })
              if (res.hasError === 0) {
                this.$set(row, 'yongYaoTS', res.data)
              }
            }
          }
          await this.validateShouFeiSL(row, inputValue)
          this.calculateCyTotalPrice()
          break
      }
    },
    async handleInputFocus(row, prop, inputValue) {
      // 同步键盘导航状态 - 当用户鼠标点击输入框时
      this.syncKeyboardNavigationState(row, prop)

      switch (prop) {
        case 'shouFeiSL':
          await this.validateShouFeiSL(row, inputValue)
          break
      }
    },

    // 同步键盘导航状态
    syncKeyboardNavigationState(row, fieldName) {
      // 查找当前行在数据列表中的索引
      const rowIndex = this.drawerYiZhuList.findIndex((item) => item === row)
      if (rowIndex === -1) return

      // 查找字段在当前行的fieldOrder中的索引
      const fieldOrder = this.getFieldOrder(row)
      const fieldIndex = fieldOrder.indexOf(fieldName)
      if (fieldIndex === -1) return

      // 更新键盘导航状态
      this.keyboardNavigation.currentRowIndex = rowIndex
      this.keyboardNavigation.currentFieldIndex = fieldIndex
    },
    async handleRowClick(row, column, event) {
      this.$emit('row-click', { row, column, event })
    },
    handleSelectionChange(selection) {
      this.$emit('selection-change', selection)
    },
    // 处理收费数量的校验
    async validateShouFeiSL(row, inputValue) {
      if (row.yiZhuLX === 'cydy') {
        // 出院带药的收费数量校验
        if (row?.yaoPinData?.keShouFeiYL) {
          const keShouFeiYL = row.yaoPinData.keShouFeiYL
          if (inputValue && Number(inputValue) > Number(keShouFeiYL)) {
            await this.$confirm(
              `【${row.mingCheng}】的可收费用量为${keShouFeiYL}， 您所开数量超过限量，系统将默认将其修改为合法值！`,
              '提示',
              {
                showCancelButton: false,
                type: 'info'
              }
            )
            this.$set(row, 'shouFeiSL', keShouFeiYL)
          }
        }
      } else if (row.yiZhuLX === 'cy' || row.yiZhuLX === 'zcydy') {
        // 草药或草药带药的收费数量校验
        if (inputValue && Number(inputValue) > 30) {
          await this.$confirm(`帖数不得超过30帖！`, '提示', {
            confirmButtonText: '确定',
            type: 'info'
          })
          this.$set(row, 'shouFeiSL', '30')
        }
      }
    },
    // 计算草药总价
    calculateCyTotalPrice() {
      if (this.tabActive === 'cq') {
        return
      }
      this.cyTotalPrice = this.drawerYiZhuList
        .filter((row) => row.yiZhuLB === '3')
        .reduce((total, row) => {
          if (!row.shouFeiSL) {
            this.$set(row, 'shouFeiSL', '7')
          }
          if (row.yiCiYL && row.shouJia) {
            return total + Number(row.yiCiYL) * Number(row.shouFeiSL) * Number(row.shouJia) || 0
          }
          return total || 0
        }, 0)
        .toFixed(2)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-table--mini .el-table__cell {
  padding: 2px 4px !important;
  .cell {
    padding: 0;
    .el-input__inner {
      height: 24px;
    }
  }
}
</style>
