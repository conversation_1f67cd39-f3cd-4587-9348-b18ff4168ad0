<!-- 医技检查 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <!-- <div style="width: 250px">
        <el-button type="primary" @click="test">测试</el-button>
      </div> -->
      <div class="table-background" style="flex-grow: 1">
        <div style="display: flex; min-height: 550px">
          <div class="table-component" style="width: 25%; max-height: 620px">
            <div class="table-title">
              <div>
                <span class="bar" />
                医技检查列表
              </div>
            </div>
            <div class="table-select">
              <el-input
                ref="searchInput"
                v-model="searchValue"
                style="width: calc(100% - 60px)"
                placeholder="请输入关键字"
              ></el-input>
              <el-button type="primary" class="search-button" @click="getMuLu">查找</el-button>
            </div>
            <div style="max-height: 520px; overflow-y: auto">
              <!-- 目录树根节点 -->
              <template
                v-for="(treeItem, index) in adviceTree.filter((t) => {
                  return rootMulu.find((r) => {
                    return r.yiZhuMLID === t.yiZhuMLID
                  })
                })"
              >
                <div
                  :key="'tree' + index"
                  class="fold-title"
                  :style="index ? { borderTop: 'none' } : {}"
                  @click="
                    treeItem.foldState = !treeItem.foldState
                    openAssayItem(treeItem)
                  "
                >
                  {{ treeItem.yiZhuMC }}
                  <i
                    v-if="
                      adviceTree.find((t) => {
                        return t.fuYiZMLID === treeItem.yiZhuMLID
                      })
                    "
                    :class="[treeItem.foldState ? 'el-icon-caret-top' : 'el-icon-caret-right']"
                    class="fold-icon"
                  />
                </div>
                <!-- 目录树子节点 -->
                <template v-if="treeItem.foldState">
                  <template
                    v-for="(itemChild, indexChild) in adviceTree.filter((t) => {
                      return t.fuYiZMLID === treeItem.yiZhuMLID
                    })"
                  >
                    <div
                      :key="'child' + index + '-' + indexChild"
                      class="fold-title fold-child"
                      :style="index ? { borderTop: 'none' } : {}"
                      @click="
                        itemChild.foldState = !itemChild.foldState
                        openAssayItem(itemChild)
                      "
                    >
                      {{ itemChild.yiZhuMC }}
                      <i
                        v-if="
                          adviceTree.find((t) => {
                            return t.fuYiZMLID === itemChild.yiZhuMLID
                          })
                        "
                        :class="[itemChild.foldState ? 'el-icon-caret-top' : 'el-icon-caret-right']"
                        class="fold-icon"
                      />
                    </div>
                    <!-- 目录树孙子节点 -->
                    <template v-if="itemChild.foldState">
                      <template
                        v-for="(itemGrandson, indexGrandson) in adviceTree.filter((t) => {
                          return t.fuYiZMLID === itemChild.yiZhuMLID
                        })"
                      >
                        <div
                          :key="'grandson' + index + '-' + indexChild + '-' + indexGrandson"
                          class="fold-title fold-grandson"
                          @click="
                            itemGrandson.foldState = !itemGrandson.foldState
                            openAssayItem(itemGrandson)
                          "
                        >
                          {{ itemGrandson.yiZhuMC }}
                          <i
                            v-if="
                              adviceTree.find((t) => {
                                return t.fuYiZMLID === itemGrandson.yiZhuMLID
                              })
                            "
                            :class="[
                              itemGrandson.foldState ? 'el-icon-arrow-up' : 'el-icon-arrow-right'
                            ]"
                            class="fold-icon"
                          />
                        </div>
                        <!-- 目录树曾孙子节点 -->
                        <template v-if="itemGrandson.foldState">
                          <div
                            v-for="(itemGG, indexGG) in adviceTree.filter((t) => {
                              return t.fuYiZMLID === itemGrandson.yiZhuMLID
                            })"
                            :key="
                              'gg' + index + '-' + indexChild + '-' + indexGrandson + '-' + indexGG
                            "
                            class="fold-title fold-gg"
                            @click="openAssayItem(itemGG)"
                          >
                            {{ itemGG.yiZhuMC }}
                          </div>
                        </template>
                      </template>
                    </template>
                  </template>
                </template>
              </template>
            </div>
          </div>
          <div class="table-component" style="margin-left: 12px; width: 45%; max-height: 620px">
            <template v-if="selectMuLu && selectMuLu.yiZhuMC">
              <div class="head-search">
                <div>
                  <el-button type="primary" @click="getRequisitionToday">
                    查看该患者今日所开检查单
                  </el-button>
                  <el-button
                    type="primary"
                    :disabled="
                      !(formType === 'add' || (editRequest && editRequest.zhuangTai === '未收费'))
                    "
                    @click="requisitionDataImport"
                  >
                    导入末次门诊检查申请单数据
                  </el-button>
                </div>
                <div>
                  <span class="search-label">级别:</span>
                  <el-select
                    v-model="formRequestData.jinJiCD"
                    placeholder="请选择"
                    style="width: 130px"
                  >
                    <el-option
                      v-for="item in [
                        { label: '普通', value: '0' },
                        { label: '紧急', value: '1' }
                      ]"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </div>
              </div>
              <div class="table-title">
                <div>
                  <span class="bar" />
                  {{ selectMuLu.yiZhuMC }}
                </div>
                <div>
                  <el-button
                    :disabled="
                      !(formType === 'add' || (editRequest && editRequest.zhuangTai === '未收费'))
                    "
                    type="primary"
                    @click="saveRequest"
                  >
                    保存
                  </el-button>
                  <el-button
                    v-if="editRequest && editRequest.zhuangTai === '未收费'"
                    type="primary"
                    @click="modifyStatus(1)"
                  >
                    暂停
                  </el-button>
                  <el-button
                    v-if="editRequest && editRequest.zhuangTai === '暂停'"
                    type="primary"
                    @click="modifyStatus(2)"
                  >
                    立即启用
                  </el-button>
                  <el-button type="primary" @click="refresh()">刷新</el-button>
                  <!-- {{ formType }} -->
                </div>
              </div>
              <div style="max-height: 500px; overflow-y: auto">
                <div class="form-component">
                  <div v-if="docAdviceZhuYiSX" class="filter-box-right">
                    <el-alert type="primary" :closable="false" show-icon plain size="small">
                      {{ docAdviceZhuYiSX }}
                    </el-alert>
                  </div>
                  <table>
                    <tbody>
                      <tr v-if="docAdviceCunChuFSXS">
                        <td class="info-label">存储方式:</td>
                        <td class="info-value" colspan="3">
                          <el-select v-model="selectRadio" placeholder="请选择" style="width: 100%">
                            <el-option
                              v-for="item in [
                                {
                                  label: '数字影像',
                                  value: 'yx'
                                },
                                {
                                  label: '数字影像+胶片',
                                  value: 'yxjp'
                                },
                                {
                                  label: '胶片',
                                  value: 'jp'
                                }
                              ]"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label">执行地点:</td>
                        <td class="info-value" colspan="3">
                          <el-select
                            v-model="selectBingQu"
                            placeholder="请选择"
                            style="width: 100%"
                          >
                            <el-option
                              v-for="item in bingQuOptions"
                              :key="item.buMenDM"
                              :label="item.buMenMC"
                              :value="item.buMenDM"
                            ></el-option>
                          </el-select>
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label">临床诊断:</td>
                        <td class="info-value" colspan="3">
                          <el-input
                            v-model="formRequestData.linChuangZD"
                            placeholder="请输入名称"
                          />
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label" style="vertical-align: top; padding-top: 10px">
                          个人病史:
                        </td>
                        <td class="info-value" colspan="3">
                          <el-input
                            v-model="formRequestData.geRenBS"
                            type="textarea"
                            maxlength="200"
                            clearable
                            show-word-limit
                            :autosize="{ minRows: 3 }"
                          />
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label" style="vertical-align: top; padding-top: 10px">
                          体检数据:
                        </td>
                        <td class="info-value" colspan="3">
                          <el-input
                            v-model="formRequestData.tiJianSJ"
                            type="textarea"
                            maxlength="200"
                            clearable
                            show-word-limit
                            :autosize="{ minRows: 3 }"
                          />
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label" style="vertical-align: top; padding-top: 10px">
                          化验检查:
                        </td>
                        <td class="info-value" colspan="3">
                          <el-input
                            v-model="formRequestData.huaYanTJ"
                            type="textarea"
                            maxlength="200"
                            clearable
                            show-word-limit
                            :autosize="{ minRows: 3 }"
                          />
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div class="table-title" style="margin-top: 10px">
                  <div>检查项目</div>
                </div>
                <div class="form-component">
                  <div style="display: flex; align-items: flex-start">
                    <table
                      v-for="(list, listIndex) in [
                        docAdviceCatalogueList.filter((item, index) => {
                          return index % 2 === 0
                        }),
                        docAdviceCatalogueList.filter((item, index) => {
                          return index % 2 === 1
                        })
                      ]"
                      :key="'table' + listIndex"
                    >
                      <tbody>
                        <tr v-for="(item, index) in list" :key="'tr' + index">
                          <td class="info-label" style="width: 40px; text-align: center">
                            <el-checkbox
                              v-model="item.isSelect"
                              @change="getCheckedAllInfo(item.yiZhuMLMXID, $event)"
                            />
                          </td>
                          <td class="info-value">
                            <div
                              style="
                                width: 100%;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                              "
                            >
                              {{ item.yiZhuMLMXMC }}
                            </div>
                          </td>
                          <td class="info-value">{{ kongZhiJBDict[item.kongZhiJB] }}</td>
                          <td class="info-value">￥{{ item.danJia }} * {{ item.shuLiang }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div class="form-component">
                  <table>
                    <tbody>
                      <template v-if="selectMuLu.yiZhuLBDM === '0203'">
                        <tr>
                          <td class="info-label">传染性标本:</td>
                          <td class="info-value">
                            <el-select
                              v-model="bingLiShenQingDan.shiFouCRXBB"
                              placeholder="请选择"
                              style="width: 100%"
                            >
                              <el-option
                                v-for="item in [
                                  { label: '是', value: '1' },
                                  { label: '否', value: '0' }
                                ]"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              ></el-option>
                            </el-select>
                          </td>
                          <td class="info-label">标本内容:</td>
                          <td class="info-value">
                            <el-select
                              v-if="bingLiShenQingDan.shiFouCRXBB === '1'"
                              v-model="bingLiShenQingDan.chuanRanXingBBNR"
                              placeholder="请选择"
                              style="width: 100%"
                            >
                              <el-option
                                v-for="item in [
                                  { label: '未选择', value: null },
                                  { label: '结核', value: 'jh' },
                                  { label: '乙肝', value: 'yg' },
                                  { label: 'HIV', value: 'hiv' },
                                  { label: '梅毒', value: 'md' },
                                  { label: '其他', value: 'qt' }
                                ]"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              ></el-option>
                            </el-select>
                          </td>
                        </tr>

                        <tr>
                          <td class="info-label">标本离体时间:</td>
                          <td class="info-value">
                            <el-date-picker
                              v-model="bingLiShenQingDan.biaoBenLTSJ"
                              type="datetime"
                              placeholder="选择日期"
                              value-format="yyyy-MM-dd hh:mm:ss"
                            />
                          </td>
                          <td class="info-label">标本固定时间:</td>
                          <td class="info-value">
                            <el-date-picker
                              v-model="bingLiShenQingDan.biaoBenGDSJ"
                              type="datetime"
                              placeholder="选择日期"
                              value-format="yyyy-MM-dd hh:mm:ss"
                            />
                          </td>
                        </tr>
                        <tr>
                          <td class="info-label" rowspan="4">取材部位:</td>
                          <td class="info-value" rowspan="4">
                            <div style="max-height: 160px; overflow-y: auto">
                              <table class="biao-ben-table">
                                <tr>
                                  <th>序号</th>
                                  <th>标本名称及取材部位</th>
                                </tr>
                                <tr
                                  v-for="xuhao in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]"
                                  :key="'xuhao ' + xuhao"
                                >
                                  <td class="td-xuhao">{{ xuhao }}</td>
                                  <td>
                                    <el-input placeholder="" />
                                  </td>
                                </tr>
                              </table>
                            </div>
                          </td>
                          <td class="info-label">婚姻:</td>
                          <td class="info-value">
                            <el-select
                              v-model="bingLiShenQingDan.hunYinZK"
                              placeholder="请选择"
                              style="width: 100%"
                            >
                              <el-option
                                v-for="item in [
                                  { label: '未婚', value: '1' },
                                  { label: '已婚', value: '2' },
                                  { label: '离婚', value: '3' },
                                  { label: '再婚', value: '4' },
                                  { label: '丧偶', value: '5' },
                                  { label: '其他', value: '6' }
                                ]"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              ></el-option>
                            </el-select>
                          </td>
                        </tr>
                        <tr>
                          <td class="info-label">联系电话:</td>
                          <td class="info-value">
                            <el-input v-model="bingLiShenQingDan.lianXiDH" placeholder="请输入" />
                          </td>
                        </tr>
                        <tr>
                          <td class="info-label">联系地址:</td>
                          <td class="info-value">
                            <el-input v-model="bingLiShenQingDan.lianXiDZ" placeholder="请输入" />
                          </td>
                        </tr>
                        <tr>
                          <td class="info-label">原切片号:</td>
                          <td class="info-value">
                            <el-input
                              v-model="bingLiShenQingDan.yuanQiePianH"
                              placeholder="请输入"
                            />
                          </td>
                        </tr>
                      </template>
                      <tr v-if="selectMuLu.yiZhuLBDM.slice(0, 4) === '0218'">
                        <td class="info-label">血液样本类型:</td>
                        <td class="info-value">
                          <el-select
                            v-model="xueYeShenQingDan.xueYeYBLX"
                            placeholder="请选择"
                            style="width: 100%"
                          >
                            <el-option
                              v-for="item in [
                                { label: '骨髓', value: 'N' },
                                { label: '血液', value: 'C' },
                                { label: '脑脊液', value: 'G' },
                                { label: '穿刺液', value: 'W' },
                                { label: '组织', value: '97' },
                                { label: '其他', value: 'QT' }
                              ]"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </td>
                        <td class="info-label">试管类型:</td>
                        <td class="info-value">
                          <el-select
                            v-model="xueYeShenQingDan.shiGuanLX"
                            placeholder="请选择"
                            style="width: 100%"
                          >
                            <el-option
                              v-for="item in bloodRequisition_list"
                              :key="item.daiMa"
                              :label="item.mingCheng"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </td>
                      </tr>

                      <tr>
                        <td class="info-label">检查目的:</td>
                        <td class="info-value" colspan="3">
                          <el-select
                            v-model="formRequestData.jianChaMD"
                            placeholder="请选择"
                            style="width: 100%"
                          >
                            <el-option
                              v-for="item in [
                                { label: '辅助检查', value: '0001' },
                                { label: '手术前', value: '0002' },
                                { label: '手术后', value: '0003' },
                                { label: '除外肿瘤', value: '0004' },
                                { label: '门诊复诊前', value: '0005' },
                                { label: '复查后出院', value: '0006' }
                              ]"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label">手术时间:</td>
                        <td class="info-value" colspan="3">
                          <el-date-picker
                            v-model="formRequestData.shouShuSJ"
                            type="date"
                            placeholder="选择日期"
                            value-format="yyyy-MM-dd hh:mm:ss"
                          />
                          *可填写手术时间以方便安排预约
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label">备注说明:</td>
                        <td class="info-value" colspan="3">
                          <el-input
                            v-model="formRequestData.teShuSM"
                            placeholder="请输入名称"
                            style="width: 270px"
                          />
                          参考总价：￥{{ totalPrice }}元
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label">
                          <el-select v-model="selectTest3" placeholder="请选择" style="width: 100%">
                            <el-option
                              v-for="item in [
                                { label: '期望检查日期', value: '00' },
                                { label: '手术日期', value: '01' },
                                { label: '讨论日期', value: '02' },
                                { label: '出院日期', value: '03' }
                              ]"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            ></el-option>
                          </el-select>
                        </td>
                        <td class="info-value" colspan="3">
                          <el-date-picker
                            v-model="formRequestData.qiWangJCSJ"
                            type="date"
                            placeholder="选择日期"
                            value-format="yyyy-MM-dd hh:mm:ss"
                          />
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label">症状开始 ~ 结束时间:</td>
                        <td class="info-value" colspan="3">
                          <el-date-picker
                            v-model="formRequestData.zhengZhuangSJ"
                            type="daterange"
                            range-separator="至"
                            start-placeholder="开始日期"
                            end-placeholder="结束日期"
                            value-format="yyyy-MM-dd hh:mm:ss"
                          />
                          （选填）
                        </td>
                      </tr>
                      <tr>
                        <td class="info-label">症状描述:</td>
                        <td class="info-value" colspan="3">
                          <el-input
                            v-model="formRequestData.zhengZhuangMS"
                            placeholder="请输入名称"
                            style="width: 270px"
                          />
                          （选填）
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </template>
          </div>
          <div class="table-component" style="margin-left: 12px; width: 30%">
            <div class="table-title">
              <div>
                <span class="bar" />
                申请单列表
              </div>
              <div>
                状态：
                <el-select v-model="listType" placeholder="请选择">
                  <el-option
                    v-for="item in [
                      { label: '未收费', value: '未收费' },
                      { label: '已收费', value: '已收费' },
                      { label: '全部', value: '全部' }
                    ]"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>
            </div>
            <div style="max-height: 570px; overflow: auto">
              <table>
                <thead>
                  <tr style="text-align: left">
                    <th>申请单名称</th>
                    <th>申请日期</th>
                    <th>类型</th>
                    <th>状态</th>
                    <th style="width: 70px; text-align: center">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <template
                    v-for="(item, index) in requisitionList.filter((t) => {
                      if (listType !== '全部') {
                        return t.zhuangTai === listType
                      } else {
                        return true
                      }
                    })"
                  >
                    <tr :key="'advice' + index" :class="[index % 2 === 1 ? 'tr-two' : 'tr-one']">
                      <td>
                        <a
                          @click="
                            () => {
                              refresh = () => {
                                getRequestDetails(item)
                              }
                              refresh()
                            }
                          "
                        >
                          {{ item.shenQingDanMC }}
                        </a>
                      </td>
                      <td>
                        {{ item.shouCiLRSJ.split(' ')[0] }}
                      </td>
                      <td>{{ getZhenLiaoLX(item.zhenLiaoLX) }}</td>
                      <td>
                        <el-tag :type="item.zhuangTai === '已收费' ? 'success' : 'danger'">
                          {{ item.zhuangTai }}
                        </el-tag>
                      </td>
                      <td style="text-align: center">
                        <div style="display: flex; align-items: center">
                          <el-button type="text" @click="deleteRequest(item)">删除</el-button>
                          <el-divider direction="vertical" />
                          <el-button type="text" @click="closedLoop(item)">闭环</el-button>
                        </div>
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="dialogStore['message'].visible" :append-to-body="true" width="350px">
      <span slot="title">
        <span style="font-size: 16px">
          <i class="el-icon-question"></i>
          信息窗口
        </span>
      </span>
      <div class="dialog-component">
        <div v-html="dialogStore['message'].data"></div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogStore['message'].resolve(true)">确 认</el-button>
        <el-button @click="dialogStore['message'].resolve(false)">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  searchDocAdviceCatalogue,
  getHistoryInpatientRequisitions,
  getCatalogueDetailed,
  getAssayInpatientExecuteDeptList,
  getLatestOutPatientRequisitionDataImport,
  addRequisitionInpatient,
  deleteRequisitionInpatient,
  getRequisitionData,
  modifyRequisitionInpatient,
  getAllInpatientRequisitionToday,
  getCheckRequisitionBiHuanUrl,
  modifyRequisitionStatus,
  modifyRequisitionControl,
  getRepeatItem
} from '@/api/doctors-advice'
import module from '@/store/modules/theme'
import { deepClone } from '@/utils'
import { mapState } from 'vuex'

class DialogRef {
  constructor() {
    this.visible = false
    this.data = null
    this.resolve = () => {}
    this.reject = () => {}
  }
  async run(data = '') {
    const result = await new Promise((resolve, reject) => {
      this.data = data
      this.visible = true
      this.resolve = resolve
      this.reject = reject
    })
    this.visible = false
    return result
  }
} //弹窗同步控制器类

export default {
  name: 'MedicalTechnology',
  props: {
    patientInit: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      searchValue: '',
      selectRadio: 'yx', //选择存储方式
      selectBingQu: '', //选择病区
      bingQuOptions: [], //病区选项列表
      // bingQuID: '3615', //当前病人病区
      // bingLiID: '2616474', //当前病人
      foldState: null, //控制打开目录
      adviceTree: [], //医嘱目录树
      selectMuLu: {}, //当前医嘱项目列表目录
      requisitionList: [],
      kongZhiJBDict: ['自', '甲', '乙'],
      rootMulu: [], //根节点列表
      dialogStore: {
        message: new DialogRef()
      },
      docAdviceCunChuFSXS: false, //是否有存储方式
      docAdviceZhuYiSX: '',
      docAdviceCatalogueList: [],
      docAdviceControlList: [],
      bloodRequisition_list: [],
      formRequestData: {
        geRenBS: '',
        linChuangZD: '',
        huaYanTJ: '',
        tiJianSJ: '',
        jianChaMD: null,
        shouShuSJ: null,
        teShuSM: '',
        qiWangJCSJ: null,
        zhengZhuangSJ: [],
        zhengZhuangMS: '',
        jinJiCD: null
      },
      bingLiShenQingDan: {},
      xueYeShenQingDan: {},
      listType: '未收费',
      formType: 'add',
      editRequest: null, //修改中的申请单
      selectTest3: null,
      refresh: () => {} //缓存刷新函数
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo,
      initInfo: ({ patient }) => patient.initInfo
      // zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,

      // bingQuID: ({ patient }) => patient.bingQuID,
      // patientInit: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.patientInit.bingLiID
    },
    zhiLiaoZuID() {
      return this.patientInit.zhiLiaoZuID
    },
    bingQuID() {
      return this.patientInit.bingQuID
    },
    totalPrice() {
      let sum = 0
      this.docAdviceCatalogueList.forEach((item) => {
        if (item.isSelect === true) {
          sum += item.danJia * item.shuLiang
        }
      })
      return sum
    }
  },
  // mounted() {
  //   this.initPage()
  // },
  methods: {
    async initPage() {
      // console.log('测试初始数据', this.zhiLiaoZuID)

      await this.getMuLu()

      const res3 = await getAssayInpatientExecuteDeptList()

      console.log('获取执行病区', res3)
      if (res3.hasError === 0) {
        this.selectBingQu = this.bingQuID
        this.bingQuOptions = [{ buMenDM: this.bingQuID, buMenMC: '病人当前病区' }, ...res3.data]
      }

      this.getHistoryRequest()
      this.inputFocus()
    },
    inputFocus() {
      this.$nextTick(() => {
        this.$refs.searchInput.focus()
      })
    },
    getZhenLiaoLX(zhenLiaoLX) {
      switch (zhenLiaoLX) {
        case '11':
          return '住院'
        case '01':
          return '急诊'
        case '02':
          return '门诊'
        case '15':
          return '移动'
        case '16':
          return '网络'
        default:
          return '门诊'
      }
    },
    async openAssayItem(data) {
      console.log('点击目录', data)
      if (
        !this.adviceTree.find((t) => {
          return t.fuYiZMLID === data.yiZhuMLID
        })
      ) {
        console.log('add')
        this.formType = 'add'
        this.refresh = async () => {
          await this.openForm(data)
          await this.getHistoryRequest()
          await this.requisitionDataImport()
        }
        await this.refresh()
      }
    },
    async getMuLu() {
      const res = await searchDocAdviceCatalogue({
        buMenID: this.initInfo.buMenID,
        guanJianZi: this.searchValue,
        kaiDanMS: 1,
        zhenLiaoLX: 11
      })
      console.log('获取医嘱目录', res)
      if (res.hasError === 0) {
        this.rootMulu = []
        this.adviceTree = res.data.map((item) => {
          return { ...item, foldState: false }
        })
        this.adviceTree.forEach((item) => {
          if (
            this.searchValue
              ? !this.adviceTree.find((t) => {
                  return t.yiZhuMLID === item.fuYiZMLID && t.yiZhuMLID !== item.yiZhuMLID
                })
              : 2 === item.fuYiZMLID && 2 !== item.yiZhuMLID
          ) {
            this.rootMulu.push(item)
          }
        })
        console.log('根目录', this.rootMulu)
      }
    },

    async requisitionDataImport() {
      const res2 = await getLatestOutPatientRequisitionDataImport({
        bingLiID: this.bingLiID
      })

      console.log('导入末次门诊检查申请单数据', res2)
      if (res2.hasError === 0) {
        if (res2.data) {
          Object.keys(this.formRequestData).forEach((key) => {
            if (this.formRequestData[key] !== undefined && res2.data[key] !== undefined) {
              this.formRequestData[key] = res2.data[key]
            }
          })
        } else {
          await this.dialogStore['message'].run(`没有门诊特检申请单数据`)
        }
      }
    },
    async openForm(data) {
      console.log(this.patientInit)
      this.selectMuLu = data

      const res = await getCatalogueDetailed({
        bingRenBH: this.patientInit.bingRenBH,
        jieSuanDM: this.patientInit.jieSuanDM,
        kaiDanMS: 1,
        yiZhuMLID: data.yiZhuMLID,
        zhuanKeID: this.initInfo.zhuanKeID
      })

      console.log('申请单详情', res)
      if (res.hasError === 0) {
        if (res.data.yiDongZJNR) {
          this.dialogStore['message'].run(res.data.yiDongZJNR)
        }
        this.docAdviceCunChuFSXS = res.data.cunChuFSXS
        this.docAdviceZhuYiSX = res.data.zhuYiSX
        this.docAdviceCatalogueList = res.data.docAdviceCatalogueDetailListChargeVo_list.map(
          (item) => {
            const newItem = deepClone(item)
            newItem.isSelect = false
            newItem.shuLiang = item.moRenSL
            return newItem
          }
        )
        this.docAdviceControlList = res.data.docAdviceCatalogueDetailControlVo_list
        this.bloodRequisition_list = res.data.bloodRequisition_list
        this.bingLiShenQingDan =
          data.yiZhuLBDM === '0203'
            ? {
                shiFouCRXBB: '0',
                chuanRanXingBBNR: null,
                biaoBenGDSJ: '', //标本固定时间
                biaoBenLTSJ: '', //标本离体时间
                hunYinZK: this.patientInit.shouShuTZD?.hunYinZK,
                lianXiDH: this.patientInit.shouShuTZD?.lianXiDH, //联系电话
                lianXiDZ: this.patientInit.shouShuTZD?.lianXiDZ, //联系地址
                yuanQiePianH: ''
              }
            : {}
        this.xueYeShenQingDan =
          data.yiZhuLBDM.slice(0, 4) === '0218'
            ? {
                shiGuanLX: res.data.shiGuanLX, //试管类型
                xueYeYBLX: res.data.yangBenLX //血液样本类型
              }
            : {}
      }
    },
    async getHistoryRequest() {
      const res1 = await getHistoryInpatientRequisitions({
        bingLiID: this.bingLiID,
        zhenLiaoLX: 11
      })

      console.log('历史申请单', res1)
      if (res1.hasError === 0) {
        this.requisitionList = res1.data
      }
    },

    async getRequestDetails(request) {
      const res = await getRequisitionData({
        shenQingDanID: request.shenQingDanID
      })

      console.log('获取申请单数据集合', this.formRequestData, res, this.docAdviceCatalogueList)
      if (res.hasError === 0) {
        this.formType = 'edit'
        const mulu = this.adviceTree.find((item) => {
          return item.yiZhuMLID === res.data.muLuID
        })
        if (mulu) {
          await this.openForm(mulu)
        } else {
          return
        }

        this.editRequest = res.data
        this.formRequestData = {
          geRenBS: this.editRequest.bingShi,
          huaYanTJ: this.editRequest.huaYanTJ,
          jianChaMD: this.editRequest.jianChaMD,
          jinJiCD: this.editRequest.jinJiCD,
          linChuangZD: this.editRequest.linChuangZD,
          qiWangJCSJ: this.editRequest.qiWangJCSJ,
          shouShuSJ: this.editRequest.shouShuSJ,
          teShuSM: this.editRequest.teShuSM,
          tiJianSJ: this.editRequest.tiJian,
          zhengZhuangMS: this.editRequest.additionalInfoVo.zhengZhuangMS,
          zhengZhuangSJ: [
            this.editRequest.additionalInfoVo.zhengZhuangKSSJ || '',
            this.editRequest.additionalInfoVo.zhengZhuangJSSJ || ''
          ]
        }
        this.docAdviceCatalogueList.forEach((item) => {
          this.editRequest.shenQingDXMMX.forEach((selectItem) => {
            if (item.yiZhuMLMXID === selectItem.yiZhuMLMXID) {
              item.isSelect = true
            }
          })
        })
        console.log(this.docAdviceCatalogueList)
      }
    },

    getCheckedAllInfo(yiZhuMLMXID, isCheck) {
      const ckList = []
      this.docAdviceControlList.forEach((ck) => {
        if (ck.yiZhuMLMXID === yiZhuMLMXID && ck.kongZhi != 5) {
          ckList.push(ck)
        }
      })
      const list = []

      ckList.forEach((ck) => {
        this.docAdviceControlList.forEach((ck1) => {
          if (
            ck1.zuHao === ck.zuHao &&
            (ck1.kongZhi === 1 || ck1.kongZhi === 5 || ck1.kongZhi === 9)
          ) {
            list.push(ck1)
          }
        })
      })

      list.forEach((ck1) => {
        this.docAdviceCatalogueList.forEach((obj) => {
          if (ck1.yiZhuMLMXID === obj.yiZhuMLMXID) {
            obj.isSelect = isCheck
          }
        })
      })
      return
    },

    async saveRequest() {
      console.log(this.patientInit)
      console.log(this.formRequestData)
      console.log(this.editRequest)
      const currentTime = new Date()
      const year = currentTime.getFullYear()
      const month = currentTime.getMonth() + 1
      const day = currentTime.getDate()
      const hours = currentTime.getHours()
      const minutes = currentTime.getMinutes()
      const seconds = currentTime.getSeconds()
      const currDate =
        year +
        '-' +
        (month < 10 ? '0' + month : month) +
        '-' +
        (day < 10 ? '0' + day : day) +
        ' ' +
        (hours < 10 ? '0' + hours : hours) +
        ':' +
        (minutes < 10 ? '0' + minutes : minutes) +
        ':' +
        (seconds < 10 ? '0' + seconds : seconds)
      const data = {
        bingLiID: this.bingLiID,
        bingQuID: this.bingQuID,
        bingRenBH: this.patientInit.bingRenBH,
        bingShi: this.formRequestData.geRenBS,
        chuShengRQ: this.patientInit.chuShengRQ + ' 00:00:00',
        chuangWeiHao: this.patientInit.chuangWeiHao,
        huaYanTJ: this.formRequestData.huaYanTJ,
        hunYinZK: this.bingLiShenQingDan?.hunYinZK || this.patientInit.shouShuTZD?.hunYinZK,
        jianChaMD: this.formRequestData.jianChaMD,
        jinJiCD: this.formRequestData.jinJiCD,
        kaiFangZKID: this.initInfo.zhuanKeID,
        lianXiDH: this.bingLiShenQingDan?.lianXiDH || this.patientInit.shouShuTZD?.lianXiDH,
        lianXiDZ: this.bingLiShenQingDan?.lianXiDZ || this.patientInit.shouShuTZD?.lianXiDZ,
        yuanQiePianH: this.bingLiShenQingDan?.lianXiDZ,
        linChuangZD: this.formRequestData.linChuangZD,
        qiWangJCSJ: this.formRequestData.qiWangJCSJ,
        teShuSM: this.formRequestData.teShuSM,
        tiJian: this.formRequestData.tiJianSJ,
        xingBie: this.patientInit.xingBie,
        xingMing: this.patientInit.bingRenXM,
        xiuGaiYHID: this.initInfo.yiShiYHID,
        muLuID: this.selectMuLu.yiZhuMLID,
        yiZhuLBDM: this.selectMuLu.yiZhuLBDM,
        yiZhuYHID: this.initInfo.yiShiYHID,
        zhenLiaoLX: 11,
        zhuYuanHao: this.patientInit.zhuYuanHao,
        zhuYuanID: this.patientInit.zhuYuanID,
        infoQO: {
          shenQingDanID: this.formType === 'edit' ? this.editRequest.shenQingDanID : null,
          shouShuSJ: this.formRequestData.shouShuSJ, //手术时间
          zhengZhuangJSSJ: this.formRequestData.zhengZhuangJSSJ, //症状结束时间
          zhengZhuangKSSJ: this.formRequestData.zhengZhuangKSSJ, //症状开始时间
          zhengZhuangMS: this.formRequestData.zhengZhuangMS, //症状描述
          shiFouCRXBB: this.bingLiShenQingDan?.shiFouCRXBB,
          chuanRanXingBBNR: this.bingLiShenQingDan?.chuanRanXingBBNR,
          biaoBenGDSJ: this.bingLiShenQingDan?.biaoBenGDSJ,
          biaoBenLTSJ: this.bingLiShenQingDan?.biaoBenLTSJ,
          shiGuanLX: this.xueYeShenQingDan?.shiGuanLX,
          xueYeYBLX: this.xueYeShenQingDan?.xueYeYBLX
        },
        shenQingDMXTMXX: this.docAdviceCatalogueList
          .filter((item) => {
            return item.isSelect === true
          })
          .map((item) => {
            return {
              danJia: item.danJia,
              shenQingDanID: this.formType === 'edit' ? this.editRequest.shenQingDanID : null,
              shouFeiXMID: item.shouFeiXMID,
              shouFeiZHID: item.shouFeiZHID,
              shuLiang: item.shuLiang,
              xiangMuMC: item.yiZhuMLMXMC,
              xiuGaiYHID: this.initInfo.yiShiYHID,
              yiZhuID: this.formType === 'edit' ? this.editRequest.shenQingDXMMX[0].yiZhuID : null,
              yiZhuMLMXID: item.yiZhuMLMXID,
              yiZhuSJ: currDate,
              yiZhuYHID: this.initInfo.yiShiYHID
            }
          }),
        zhuYuanSQDYZXX: this.docAdviceCatalogueList
          .filter((item) => {
            return item.isSelect === true
          })
          .map((item) => {
            return {
              bingLiID: this.bingLiID,
              bingQuID: this.bingQuID,
              buMenID: this.initInfo.buMenID,
              danJia: item.danJia,
              mingCheng: item.yiZhuMLMXMC,
              shenQingDanID: this.formType === 'edit' ? this.editRequest.shenQingDanID : null,
              shouCiLRSJ: currDate,
              shouFeiXMID: item.shouFeiXMID,
              shouFeiZHID: item.shouFeiZHID,
              shuLiang: item.shuLiang,
              xiuGaiYHID: this.initInfo.yiShiYHID,
              yiZhuID: this.formType === 'edit' ? this.editRequest.shenQingDXMMX[0].yiZhuID : null,
              yiZhuLBDM: item.yiZhuLBDM,
              yiZhuMLMXID: item.yiZhuMLMXID,
              yiZhuSJ: currDate,
              yiZhuYHID: this.initInfo.yiShiYHID,
              zhiLiaoZuID: this.zhiLiaoZuID,
              zhuYuanID: this.patientInit.zhuYuanID,
              zhuanKeID: this.initInfo.zhuanKeID,
              ziFei: 0
            }
          })
      }
      console.log('保存 data:', data)
      // return
      if (await this.dialogStore['message'].run(`确定保存该检查医嘱吗？`)) {
        if (this.formRequestData.shouShuSJ < currDate) {
          await this.dialogStore['message'].run(`手术时间不得早于当前时间!`)
          return
        }
        if (this.totalPrice > 0) {
          let res = null
          if (this.formType === 'edit') {
            // const res0 = await modifyRequisitionControl(
            //   {
            //     shenQingDanID: this.editRequest.shenQingDanID,
            //     zhuanKeID: this.initInfo.zhuanKeID
            //   },
            //   data
            // )
            // console.log('门诊医师站_更新_修改申请单控制', res0)
            // if (res0.hasError === 0) {
            // }
            // return

            res = await modifyRequisitionInpatient(
              {
                yinXiangJP: this.docAdviceCunChuFSXS ? this.selectRadio : null,
                shenQingDanSJID: this.editRequest.shenQingDanID,
                zhuanKeID: this.initInfo.zhuanKeID
              },
              data
            )
          } else {
            // const res0 = await getRepeatItem({
            //   bingRenBH: this.patientInit.bingRenBH
            // })
            // console.log('门诊医师站_查询_获取重复项目提醒消息', res0)
            // if (res0.hasError === 0) {
            // }
            // return

            res = await addRequisitionInpatient(
              {
                yinXiangJP: this.docAdviceCunChuFSXS ? this.selectRadio : null,
                zhuanKeID: this.initInfo.zhuanKeID
              },
              data
            )
          }
          if (res.hasError === 0) {
            this.$message({
              message: '保存成功',
              type: 'success'
            })
            this.getHistoryRequest()
          }
        } else {
          await this.dialogStore['message'].run(`总金额不能为0元!`)
        }
      }
    },

    async modifyStatus(status) {
      const res = await modifyRequisitionStatus({
        zhuangTai: status,
        shenQingDanID: this.editRequest.shenQingDanID
      })

      if (res.hasError === 0) {
        this.$message({
          message: (status === 1 ? '暂停' : '启用') + '成功',
          type: 'success'
        })
        this.getHistoryRequest()
        this.selectMuLu = {}
      }
    },
    //闭环
    async closedLoop(item) {
      const res = await getCheckRequisitionBiHuanUrl({ shenQingDanSJID: item.shenQingDanID })
      console.log('闭环地址', res)
      if (res.hasError === 0) {
        window.open(res.data)
      }
    },

    async deleteRequest(item) {
      console.log(item)
      if (item.zhuangTai === '已收费') {
        await this.dialogStore['message'].run(`该医嘱项目已经收过费，你不能删除本医嘱！`)
      } else {
        if (await this.dialogStore['message'].run(`确定要删除这条检查申请单医嘱吗？`)) {
          const res = await deleteRequisitionInpatient({ shenQingDanID: item.shenQingDanID })
          console.log('删除申请单', res)
          if (res.hasError === 0) {
            this.$message({
              message: '删除成功',
              type: 'success'
            })
            this.getHistoryRequest()
            this.selectMuLu = {}
          }
        }
      }
    },

    //获取今日申请单
    async getRequisitionToday() {
      const res = await getAllInpatientRequisitionToday({
        bingRenBH: this.patientInit.bingRenBH
      })
      console.log('电子病历_查询_获取当日住院检查申请单', res)
      if (res.hasError === 0) {
        await this.dialogStore['message'].run(
          res.data.length ? res.data.join('<br/>') : '该患者今日暂未开检查单'
        )
      }
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  overflow-y: auto;
  .system-page {
    .table-component {
      padding: 12px;
      background-color: #eaf0f9;
      border-radius: 4px;
      border: 1px solid #ddd;
      .head-search {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        background-color: #eaf0f9;
        border-radius: 4px;
        border: 1px solid #ddd;
        margin-bottom: 10px;
        .search-label {
          margin: 0 10px;
          font-weight: 600;
        }
      }
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        margin-bottom: 10px;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
        .sub-title {
          font-weight: 400;
        }
      }
      .table-select {
        margin-bottom: 12px;
        .el-select {
          width: calc(100% - 60px);
        }
      }
      .form-component {
        table {
          width: 100%;
        }
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 35px;
          padding: 5px;
        }
        .info-label {
          text-align: right;
          width: 110px;
          background-color: #ebf0f8;
          span {
            color: #f35656;
            position: relative;
            top: 3px;
            right: 3px;
          }
        }
        .info-value {
          background-color: #ffffff;
          a {
            text-decoration: underline;
            color: #356ac5;
          }
        }
        .filter-box-right {
          .el-alert {
            width: auto;
            padding: 6px 10px;
            margin-bottom: 10px;
            font-size: 12px;
            border: 1px solid rgba(21, 91, 212, 0.45);
            background-color: rgba(21, 91, 212, 0.05);
          }
        }
        .biao-ben-table {
          th,
          .td-xuhao {
            background-color: #ebf0f8;
          }
        }
      }
      .round-div {
        width: 14px;
        height: 14px;
        border-radius: 7px;
        margin-left: 6px;
      }
      button.search-button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover).search-button {
        background: #a66dd4;
        border-color: #a66dd4;
      }
      :deep(.el-button--primary:focus).search-button {
        background: #ce8be0;
        border-color: #ce8be0;
      }

      .fold-title {
        display: flex;
        font-weight: 600;
        justify-content: space-between;
        padding: 10px;
        background-color: #eaf0f9;
        border: 1px solid #ddd;
        i {
          display: flex;
          align-items: center;
          background: none;
        }
      }
      .fold-child {
        font-weight: 400;
        background-color: #f6f6f6;
        border-top: none;
      }
      .fold-grandson {
        font-weight: 400;
        background-color: #eff3fb;
        border-top: none;
      }
      .fold-gg {
        font-weight: 400;
        background-color: #f6f6f6;
        border-top: none;
      }
      .fold-icon {
        margin-right: 3px;
        color: #8590b3;
        font-size: 12px;
      }
    }
    .table-background {
      background-color: #eff3fb;
      border-radius: 2px;
    }

    table {
      margin-bottom: 10px;
      width: 100%;
    }
    th,
    td {
      border: 1px solid #ddd;
      border-collapse: collapse; /* 移除表格内边框间的间隙 */
      height: 35px;
      padding: 5px 8px;
    }
    .tr-one {
      background-color: #f6f6f6;
    }
    th,
    .tr-two {
      background-color: #eaf0f9;
    }
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}

.el-icon-question {
  font-size: 14px;
  color: #356ac5;
}
.dialog-component {
  padding-left: 30px;
  color: #96999e;
  a {
    color: #356ac5;
  }
}
</style>
