<template>
  <el-select
    ref="selectComponent"
    v-model="select"
    :disabled="isDisabled"
    filterable
    class="select-component"
    popper-class="drawer-table-select-dropdown"
    @keydown.native="handleKeyDown"
    @focus="handleFocus"
    @visible-change="handleVisibleChange"
    @change="handleChange"
  >
    <el-option
      v-for="item in columnOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
</template>

<script>
export default {
  name: 'DrawerTableSelect',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      columnOptions: [],
      dropdownVisible: false, // 跟踪下拉框状态
      isSelecting: false // 跟踪是否正在选择选项
    }
  },
  computed: {
    select: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    },
    isDisabled() {
      // 支持通过 column.disabled 属性禁用
      if (this.column.disabled !== undefined) {
        return this.column.disabled
      }
      // 原有的禁用逻辑
      return this.column.value === 'yiZhuLX' && !!this.row.mingCheng
    }
  },
  watch: {
    'column.options': {
      handler(newValue) {
        this.columnOptions = newValue
      },
      immediate: true
    }
  },
  mounted() {},
  methods: {
    handleKeyDown(event) {
      if (event.key === 'Enter') {
        event.preventDefault()

        // 检查当前是否有选中值
        const hasSelectedValue =
          this.select !== null && this.select !== undefined && this.select !== ''

        if (hasSelectedValue) {
          // 如果已有选中值，保持当前值不变，直接跳转到下一个字段
          // 如果下拉框是打开的，先关闭它
          if (this.dropdownVisible) {
            this.$refs.selectComponent.blur()
          }

          // 跳转到下一个字段
          setTimeout(() => {
            this.$emit('keyboardNext')
          }, 200)
        } else {
          // 如果没有选中值，自动选择第一个可用选项
          if (this.columnOptions.length > 0) {
            this.select = this.columnOptions[0].value

            // 关闭下拉框（如果打开的话）
            if (this.dropdownVisible) {
              this.$refs.selectComponent.blur()
            }

            // 选择第一项后跳转到下一个字段
            setTimeout(() => {
              this.$emit('keyboardNext')
            }, 200)
          } else {
            // 如果没有可用选项，直接跳转到下一个字段
            setTimeout(() => {
              this.$emit('keyboardNext')
            }, 200)
          }
        }
      }
    },
    handleVisibleChange(visible) {
      // 跟踪下拉框状态
      this.dropdownVisible = visible

      // 当下拉框关闭时，重置选择状态
      if (!visible) {
        this.isSelecting = false
      }
    },
    handleChange(value) {
      // 标记正在选择，避免blur事件干扰
      this.isSelecting = true

      // 延迟重置选择状态，确保选择流程完成
      this.$nextTick(() => {
        setTimeout(() => {
          this.isSelecting = false
        }, 100)
      })
    },
    handleFocus() {
      // 使用延迟确保不干扰el-select的内部逻辑
      this.$nextTick(() => {
        // 只有在不是正在选择状态时才触发焦点事件
        if (!this.isSelecting) {
          this.$emit('inputFocus', { prop: this.column.value, inputValue: this.select })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.select-component {
  width: 100%;
  ::v-deep .el-input__inner {
    padding: 0 5px;
  }
  ::v-deep .el-input__suffix {
    display: none;
  }
}
</style>
