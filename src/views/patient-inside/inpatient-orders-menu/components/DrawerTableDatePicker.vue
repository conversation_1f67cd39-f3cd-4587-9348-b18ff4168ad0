<template>
  <el-date-picker
    v-model="dateTime"
    class="custom-date-picker"
    popper-class="compact-datetime-picker"
    value-format="yyyy-MM-dd HH:mm:ss"
    format="yyyy-MM-dd HH:mm"
    size="mini"
    type="datetime"
    @change="handleChange"
    @keydown.native="handleKeyDown"
    @focus="handleFocus"
  ></el-date-picker>
</template>

<script>
export default {
  name: 'DrawerTableDatePicker',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      pickerVisible: false
    }
  },
  computed: {
    dateTime: {
      get() {
        return this.row[this.column.value]
      },
      set(val) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: val })
      }
    }
  },
  methods: {
    handleChange(value) {
      this.$emit('updateRow', { prop: this.column.value, updateValue: value })
    },
    handleKeyDown(event) {
      if (event.key === 'Enter') {
        event.preventDefault()
        // 结束时间字段的特殊处理在DrawerTable中处理
        this.$emit('keyboardNext')
      }
    },
    handleFocus() {
      // 触发焦点事件，用于同步键盘导航状态
      this.$emit('inputFocus', { prop: this.column.value, inputValue: this.dateTime })
    }
  }
}
</script>

<style lang="scss" scoped>
.custom-date-picker {
  width: 100%;
  ::v-deep .el-input__inner {
    padding: 0 20px 0 5px;
  }
  ::v-deep .el-input__prefix {
    display: none;
  }
  ::v-deep .el-input__icon {
    line-height: 24px;
    right: 0;
  }
}
</style>
