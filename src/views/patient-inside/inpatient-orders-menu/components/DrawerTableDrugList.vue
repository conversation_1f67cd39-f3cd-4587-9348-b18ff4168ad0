<template>
  <el-popover
    v-model="visible"
    popper-class="drug-popover-class"
    width="700"
    trigger="manual"
    placement="bottom"
    :append-to-body="true"
  >
    <!--<el-radio-group v-model="yaoPinLeiXing" style="margin-bottom: 10px">-->
    <!--  <el-radio-button label="全院用药">全院用药</el-radio-button>-->
    <!--  <el-radio-button label="个人常用药">个人常用药</el-radio-button>-->
    <!--  <el-radio-button label="科室常用药">科室常用药</el-radio-button>-->
    <!--</el-radio-group>-->
    <el-table
      ref="drugListTable"
      v-loading="loading"
      height="250"
      :data="drugList"
      highlight-current-row
      border
      stripe
      size="mini"
      :row-style="{ cursor: 'pointer' }"
      @row-click="handleRowClick"
      @current-change="handleCurrentChange"
    >
      <el-table-column prop="mingCheng" label="药品名称/治疗模板名称/膳食名称" width="320px">
        <template #default="{ row: drugRow }">
          <div>{{ drugRow.leiXingMC }} {{ drugRow.mingCheng }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="jiCai" label="集采" width="70px"></el-table-column>
      <el-table-column prop="guiGe" label="规格" width="120px"></el-table-column>
      <el-table-column prop="shouJia" label="售价"></el-table-column>
      <el-table-column prop="biaoZhun" label="标准"></el-table-column>
    </el-table>
    <el-input
      ref="drugInput"
      slot="reference"
      v-model="query"
      class="drug-input"
      @input="debouncedHandleInput"
      @focus="handleFocus"
      @blur="handleBlur"
    />
  </el-popover>
</template>

<script>
import { debounce } from 'lodash'
import { EventBus } from '@/utils/event-bus'
import { searchYiZhu } from '@/api/inpatient-order'
export default {
  name: 'DrawerTableDrugList',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    },
    tabActive: {
      type: String,
      default: ''
    },
    inpatientInit: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      moHuSS: '0',
      visible: false,
      yaoPinLeiXing: '全院用药',
      drugList: [],
      tempValue: '', // 添加临时存储值
      hasSelected: false, // 添加选中标记
      loading: false,
      currentHighlightIndex: -1, // 当前高亮的药品索引，-1表示没有高亮
      hasHighlighted: false // 是否已经高亮过（用于区分第一次和第二次回车）
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    query: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    }
  },
  watch: {
    'column.moHuSS': {
      handler(newValue) {
        this.moHuSS = newValue
      },
      immediate: true
    }
  },
  mounted() {
    // 监听全局键盘事件，使用capture模式确保优先处理
    document.addEventListener('keydown', this.handleGlobalKeyDown, true)
  },
  beforeDestroy() {
    // 移除全局键盘事件监听
    document.removeEventListener('keydown', this.handleGlobalKeyDown, true)
  },
  methods: {
    // 判断查询医嘱时医嘱种类
    getYaoPinLeiXing() {
      const { zhuanKeID } = this.inpatientInit.ezyblbrVo || {}
      const { leiBie, yiZhuLX } = this.row
      if (zhuanKeID === 49 && yiZhuLX === 'cq') {
        return '0011'
      }
      switch (leiBie) {
        case '1':
          return '0010'
        case '2':
          return yiZhuLX === 'cq' ? '0001' : '0000'
        case '3':
        case '4':
          if (yiZhuLX === 'cq') {
            return '1000'
          } else if (yiZhuLX === 'cy' || yiZhuLX === 'zcydy') {
            return '0100'
          } else if (yiZhuLX === 'ls' || yiZhuLX === 'cydy') {
            return '1000'
          }
          break
        default:
          return yiZhuLX === 'cq' ? '1011' : '1010'
      }
    },
    handleInput(value) {
      this.drugList = []
      this.currentHighlightIndex = -1 // 重置高亮索引为-1（无高亮）
      this.hasHighlighted = false // 重置高亮状态
      if (value.trim() === '') {
        this.visible = false
        return
      }
      this.visible = true
      const key = value.trim().toUpperCase()
      /**
       * 搜索医嘱信息
       *
       * @param {Object} searchParams - 搜索参数对象
       * @param {string} searchParams.leiXing - 类型：1111 第1位=药品，第2位=草药，第3位=治疗，第4位=饮食
       * @param {string} searchParams.key - 搜索关键词
       * @param {string} searchParams.moHuSS - 模糊搜索（1=是，0=否）
       * @param {string} searchParams.jiXing - 剂型
       * @param {string} searchParams.yiZhuLB - 医嘱类别（1=西药，2=中成药，3=草药）
       * @param {string} searchParams.yiZhuLX - 医嘱类型（cq=长期，ls=临时）
       * @param {string} searchParams.bingLiID - 病历ID
       * @param {Array} searchParams.drugTakePharmacyVos - 药房代码列表
       */
      this.loading = true
      searchYiZhu({
        leiXing: this.getYaoPinLeiXing(),
        key,
        moHuSS: this.moHuSS,
        yiZhuLB: '1', // 长期医嘱默认西药，中成药模板导入为中成药，临时医嘱草药医嘱为草药
        yiZhuLX: this.row.yiZhuLX,
        bingLiID: this.bingLiID,
        drugTakePharmacyVos: this.inpatientInit.yaoFangDMs
      })
        .then((res) => {
          if (res.hasError === 0) {
            this.drugList = res.data
            // 重置高亮状态，不自动选中任何结果
            if (this.drugList.length > 0) {
              this.currentHighlightIndex = -1 // 无高亮状态
              this.hasHighlighted = false
              // 清除表格的当前行选中状态
              this.$nextTick(() => {
                if (this.$refs.drugListTable) {
                  this.$refs.drugListTable.setCurrentRow(null)
                }
              })
            }
          }
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleFocus(event) {
      this.tempValue = this.query
      this.hasSelected = false
      this.query = ''
      // 触发焦点事件，用于同步键盘导航状态
      this.$emit('inputFocus', { prop: this.column.value, inputValue: this.query })
    },
    handleBlur(event) {
      if (!this.hasSelected) {
        // 如果没有选择药品，恢复原始值
        this.query = this.tempValue
      }
      this.visible = false
    },
    // 搜索防抖
    debouncedHandleInput: debounce(function (value) {
      this.handleInput(value)
    }, 300),
    // 选中某条搜索医嘱
    setCurrent(row) {
      this.$refs.drugListTable.setCurrentRow(row)
      this.hasSelected = true
    },
    // 点击医嘱
    async handleRowClick(row) {
      this.visible = false
      this.query = row.mingCheng
      this.hasSelected = true
      // 执行开药判断逻辑
      EventBus.$emit(`startYZLogic_${this.bingLiID}`, { row: this.row, drugRow: row })
      // 选择药品后自动跳转到下一个字段
      this.$nextTick(() => {
        this.$emit('keyboardNext')
      })
    },
    handleCurrentChange() {
      // 表格行选择变化处理
    },

    // 全局键盘事件处理
    handleGlobalKeyDown(event) {
      // 只有当前组件的输入框获得焦点时才处理键盘事件
      const activeElement = document.activeElement
      const drugInput = this.$refs.drugInput

      if (!drugInput || !activeElement) {
        return
      }

      // 检查焦点是否在当前组件的输入框中
      const isInputFocused =
        activeElement === drugInput.$el.querySelector('input') ||
        activeElement.closest('.el-popover__reference') === drugInput.$el

      if (!isInputFocused) {
        return
      }

      // 对于回车键和方向键，在药品列表显示时完全接管处理
      if (
        (event.key === 'Enter' || event.key === 'ArrowUp' || event.key === 'ArrowDown') &&
        this.visible &&
        this.drugList.length > 0
      ) {
        event.stopPropagation()
        event.stopImmediatePropagation()
        event.preventDefault()
        this.handleKeyDown(event)
        return false
      }

      this.handleKeyDown(event)
    },

    // 键盘事件处理
    handleKeyDown(event) {
      const { key } = event

      // 立即阻止事件冒泡和默认行为
      event.stopPropagation()
      event.stopImmediatePropagation()

      if (key === 'Enter') {
        event.preventDefault()
        if (this.visible && this.drugList.length > 0) {
          if (!this.hasHighlighted || this.currentHighlightIndex === -1) {
            // 第一次按回车：高亮第一条医嘱，但不跳转字段
            this.currentHighlightIndex = 0
            this.hasHighlighted = true
            this.$nextTick(() => {
              if (this.$refs.drugListTable) {
                this.$refs.drugListTable.setCurrentRow(this.drugList[0])
              }
            })
            return false
          } else {
            // 第二次按回车：选择当前高亮的药品
            this.selectDrugByIndex(this.currentHighlightIndex)
            return false
          }
        } else {
          // 检查条件：当query和tempValue都为空时，阻止字段跳转
          const isQueryEmpty = !this.query || this.query.trim() === ''
          const isTempValueEmpty = !this.tempValue || this.tempValue.trim() === ''

          if (isQueryEmpty && isTempValueEmpty) {
            // 当两个字段都为空时，阻止默认行为和事件传播，光标保持在当前输入框
            event.preventDefault()
            event.stopPropagation()
            return false
          }

          // 没有搜索结果且不满足阻止条件时，触发下一个字段
          this.$emit('keyboardNext')
          return false
        }
      } else if (key === 'ArrowUp' || key === 'ArrowDown') {
        if (this.visible && this.drugList.length > 0) {
          event.preventDefault()
          this.handleArrowKey(key)
          return false
        }
      } else if (key === 'Escape') {
        event.preventDefault()
        this.visible = false
        return false
      }
    },

    // 处理上下方向键
    handleArrowKey(key) {
      // 如果还没有高亮任何项，先设置为第一项
      if (this.currentHighlightIndex === -1) {
        this.currentHighlightIndex = 0
        this.hasHighlighted = true
      } else {
        if (key === 'ArrowUp') {
          this.currentHighlightIndex = Math.max(0, this.currentHighlightIndex - 1)
        } else if (key === 'ArrowDown') {
          this.currentHighlightIndex = Math.min(
            this.drugList.length - 1,
            this.currentHighlightIndex + 1
          )
        }
      }

      // 设置表格当前行
      if (this.currentHighlightIndex >= 0 && this.drugList[this.currentHighlightIndex]) {
        if (this.$refs.drugListTable) {
          // 使用$nextTick确保DOM更新
          this.$nextTick(() => {
            this.$refs.drugListTable.setCurrentRow(this.drugList[this.currentHighlightIndex])

            // 确保高亮行滚动到可见区域
            setTimeout(() => {
              const tableBody =
                this.$refs.drugListTable.$el.querySelector('.el-table__body-wrapper')
              if (tableBody) {
                const currentRow = tableBody.querySelector('.el-table__row.current-row')
                if (currentRow) {
                  currentRow.scrollIntoView({
                    behavior: 'smooth',
                    block: 'nearest'
                  })
                }
              }
            }, 50)
          })
        }
      }
    },

    // 选择第一个药品
    selectFirstDrug() {
      if (this.drugList.length > 0) {
        this.selectDrugByIndex(0)
      }
    },

    // 根据索引选择药品
    selectDrugByIndex(index) {
      if (index >= 0 && index < this.drugList.length && this.drugList[index]) {
        this.handleRowClick(this.drugList[index])
      }
    },

    // 公开的聚焦方法，供父组件调用
    focusInput() {
      // 使用延迟确保组件完全渲染
      this.$nextTick(() => {
        setTimeout(() => {
          if (this.$refs.drugInput) {
            // 尝试多种方式获取input元素
            let inputElement = null

            // 方式1: 通过$el查找
            if (this.$refs.drugInput.$el) {
              inputElement = this.$refs.drugInput.$el.querySelector('input')
            }

            // 方式2: 直接使用ref（如果是原生input）
            if (!inputElement && this.$refs.drugInput.tagName === 'INPUT') {
              inputElement = this.$refs.drugInput
            }

            // 方式3: 通过$refs.input（el-input的内部结构）
            if (!inputElement && this.$refs.drugInput.$refs && this.$refs.drugInput.$refs.input) {
              inputElement = this.$refs.drugInput.$refs.input
            }

            // 方式4: 查找所有input元素
            if (!inputElement && this.$refs.drugInput.$el) {
              const allInputs = this.$refs.drugInput.$el.querySelectorAll('input')
              inputElement = allInputs[0]
            }

            if (inputElement && inputElement.focus) {
              // 强制聚焦
              inputElement.focus()

              // 如果有内容，选中以便重新输入
              if (this.query) {
                inputElement.select()
              }

              // 验证焦点是否成功
              setTimeout(() => {
                // 如果焦点设置失败，再次尝试
                if (document.activeElement !== inputElement) {
                  inputElement.focus()

                  // 滚动到元素位置
                  inputElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'center'
                  })
                }
              }, 100)
            }
          }
        }, 100)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .drug-input {
  .el-input__inner {
    padding: 0 5px;
  }
}
:deep(.drug-popover-class) {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 5px !important;
}
</style>
