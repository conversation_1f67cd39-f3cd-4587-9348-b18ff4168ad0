<template>
  <el-select
    v-if="shouldShowSelect"
    ref="selectComponent"
    v-model="selectValue"
    style="width: 100%"
    size="mini"
    popper-class="drawer-table-select-dropdown"
    @focus="handleFocus"
    @change="handleChange"
  >
    <el-option
      v-for="item in selectOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    ></el-option>
  </el-select>
  <el-input v-else v-model="inputValue" class="row-input" size="mini" @focus="handleFocus" />
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'DrawerTableTeShuYF',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    },
    inpatientInit: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {}
  },
  computed: {
    // 判断是否应该显示下拉选择框
    shouldShowSelect() {
      const { yiZhuLX } = this.row
      // 草药医嘱
      return yiZhuLX === 'cy' || yiZhuLX === 'zcydy'
    },
    // 下拉选项
    selectOptions() {
      const { yiZhuLX } = this.row

      // 草药医嘱
      if (yiZhuLX === 'cy' || yiZhuLX === 'zcydy') {
        return this.inpatientInit.caoYaoTSJF.map((item) => ({
          value: item.fangFaDM,
          label: item.fangFaMC
        }))
      }
      return []
    },
    // 下拉框值
    selectValue: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    },
    // 输入框值
    inputValue: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    }
  },
  methods: {
    handleChange(value) {
      // 处理下拉选择变化
      this.$emit('updateRow', { prop: this.column.value, updateValue: value })
    },
    handleFocus() {
      // 使用延迟确保不干扰el-select的内部逻辑
      this.$nextTick(() => {
        // 只有在不是正在选择状态时才触发焦点事件
        if (!this.isSelecting) {
          this.$emit('inputFocus', {
            prop: this.column.value,
            inputValue: this.shouldShowSelect ? this.selectValue : this.inputValue
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-input__inner {
  padding: 0 20px 0 5px;
}
</style>
