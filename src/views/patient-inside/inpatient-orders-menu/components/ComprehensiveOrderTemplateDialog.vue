<template>
  <default-dialog :visible.sync="isVisible" title="综合医嘱模板" width="1500px" pop-type="tip">
    <div class="comprehensive-template-dialog-content">
      <div class="left-panel">
        <el-menu :default-openeds="openedMenus" class="template-menu" @open="handleMenuOpen">
          <!-- 个人模板 -->
          <el-submenu index="personal">
            <template #title>
              <span>个人模板</span>
            </template>
            <!-- 个人模板下的目录 -->
            <template v-if="personalCategories.length > 0">
              <el-menu-item
                v-for="category in personalCategories"
                :key="category.moBanID"
                :index="category.moBanID + ''"
                @click="handleCategoryClick(category)"
              >
                {{ category.mingCheng }}
              </el-menu-item>
            </template>
            <el-menu-item v-else class="empty-item">暂无个人模板</el-menu-item>
          </el-submenu>

          <!-- 专科模板 -->
          <el-submenu index="department">
            <template #title>
              <span>专科模板</span>
            </template>
            <!-- 专科模板分类(第二级) -->
            <template v-if="departmentCategories.length > 0">
              <el-menu-item
                v-for="category in departmentCategories"
                :key="category.moBanID"
                :index="category.moBanID + ''"
                @click="handleCategoryClick(category)"
              >
                {{ category.mingCheng }}
              </el-menu-item>
            </template>
            <el-menu-item v-else class="empty-item">暂无专科模板</el-menu-item>
          </el-submenu>

          <!-- 全院模板 -->
          <el-submenu index="hospital">
            <template #title>
              <span>全院模板</span>
            </template>
            <!-- 全院模板分类(第二级) -->
            <template v-if="hospitalCategories.length > 0">
              <el-menu-item
                v-for="category in hospitalCategories"
                :key="category.moBanID"
                :index="category.moBanID + ''"
                @click="handleCategoryClick(category)"
              >
                {{ category.mingCheng }}
              </el-menu-item>
            </template>
            <el-menu-item v-else class="empty-item">暂无全院模板</el-menu-item>
          </el-submenu>
        </el-menu>
      </div>
      <div class="right-panel">
        <div class="table-title">
          <span>已选择的医嘱模板</span>
          <div class="table-actions">
            <el-button type="primary" size="mini" @click="handleImport">导入</el-button>
            <el-button type="primary" size="mini" @click="handleSelectAll">全选</el-button>
            <el-button type="primary" size="mini" @click="handleUnselectAll">全不选</el-button>
            <el-button type="primary" size="mini" @click="handleInvertSelection">反选</el-button>
          </div>
        </div>
        <div class="table-wrapper">
          <el-table
            ref="orderTable"
            :data="templateDetails"
            border
            stripe
            row-key="moBanMXID"
            size="mini"
            height="440px"
            :span-method="handleSpanMethod"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              type="selection"
              width="55"
              :reserve-selection="true"
              fixed="left"
            ></el-table-column>
            <el-table-column prop="muBanMC" label="模板名称" fixed="left"></el-table-column>
            <el-table-column prop="leiBie" label="类别" width="80" fixed="left">
              <template #default="{ row }">
                {{
                  row.leiBie === '1'
                    ? '治疗'
                    : row.leiBie === '2'
                    ? '饮食'
                    : row.leiBie === '3'
                    ? '药品'
                    : '嘱托'
                }}
              </template>
            </el-table-column>
            <el-table-column
              prop="mingCheng"
              label="医嘱名称"
              width="150"
              fixed="left"
            ></el-table-column>
            <el-table-column prop="zuHaoTXT" label="组" width="30" fixed="left"></el-table-column>
            <el-table-column prop="guiGe" label="规格/单价" width="130"></el-table-column>
            <el-table-column prop="yiCiYL" label="一次用量" width="100"></el-table-column>
            <el-table-column prop="jiLiangDW" label="剂量单位" width="100"></el-table-column>
            <el-table-column prop="zhiXingFF" label="用法" width="90"></el-table-column>
            <el-table-column prop="zhiXingPL" label="频率" width="90"></el-table-column>
            <el-table-column prop="geiYaoSJ" label="用药/执行时间" width="100"></el-table-column>
            <el-table-column prop="teShuYF" label="医嘱说明/备注说明" width="100"></el-table-column>
            <el-table-column prop="chiXuTS" label="持续天数" width="50"></el-table-column>
            <el-table-column prop="shouFeiSL" label="数量/剂数" width="50"></el-table-column>
            <el-table-column prop="danWei" label="单位" width="50"></el-table-column>
            <el-table-column prop="shouFeiCS" label="次数" width="50"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { getZongHeYzMbInit, getZongHeYzMbMX } from '@/api/inpatient-order'

export default {
  name: 'ComprehensiveOrderTemplateDialog',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    inpatientInit: {
      type: Object,
      default: () => ({})
    },
    tabActive: {
      type: String,
      default: 'cq'
    }
  },
  data() {
    return {
      personalCategories: [], // 个人模板目录
      departmentCategories: [], // 专科模板目录
      hospitalCategories: [], // 全院模板目录
      templateDetails: [], // 医嘱模板明细
      selectedRows: [], // 已选择的行
      openedMenus: ['personal', 'department', 'hospital'], // 当前展开的菜单
      currentCategory: null // 当前选中的目录
    }
  },
  computed: {
    isVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    bingLiID() {
      return this.inpatientInit?.ezyblbrVo?.bingLiID || '' // 从初始化数据获取病历ID
    }
  },
  watch: {
    isVisible(val) {
      if (val) {
        this.resetData()
        this.fetchTemplateTree()
      }
    }
  },
  methods: {
    // 重置数据
    resetData() {
      this.personalCategories = []
      this.departmentCategories = []
      this.hospitalCategories = []
      this.templateDetails = []
      this.selectedRows = []
      this.openedMenus = ['personal', 'department', 'hospital']
      this.currentCategory = null
    },

    // 获取模板目录(一、二级)
    async fetchTemplateTree() {
      try {
        const res = await getZongHeYzMbInit()
        if (res.hasError === 0 && res.data) {
          // 处理个人模板
          if (res.data.geRenMBs && Array.isArray(res.data.geRenMBs)) {
            this.personalCategories = res.data.geRenMBs
          }

          // 处理专科模板
          if (res.data.zhuanKeMBs && Array.isArray(res.data.zhuanKeMBs)) {
            this.departmentCategories = res.data.zhuanKeMBs
          }

          // 处理全院模板
          if (res.data.quanYuanMBs && Array.isArray(res.data.quanYuanMBs)) {
            this.hospitalCategories = res.data.quanYuanMBs
          }
        }
      } catch (error) {
        console.error('获取综合医嘱模板目录失败:', error)
      }
    },

    // 处理菜单打开事件
    handleMenuOpen(index) {
      // 确保菜单保持打开状态
      if (!this.openedMenus.includes(index)) {
        this.openedMenus.push(index)
      }
    },

    // 处理点击医嘱目录
    async handleCategoryClick(category) {
      if (!category || !category.moBanID) return

      this.currentCategory = category

      try {
        const res = await getZongHeYzMbMX({
          moBanID: category.moBanID
        })

        if (res.hasError === 0 && res.data) {
          // 处理返回的模板明细数据
          this.templateDetails = Array.isArray(res.data) ? res.data : []
          // 根据组号设置zuHaoTXT
          this.templateDetails.forEach((item, index) => {
            const currentPrefix = item.zuHao
            const nextItem = this.templateDetails[index + 1]
            const prevItem = this.templateDetails[index - 1]
            const nextPrefix = nextItem?.zuHao
            const prevPrefix = prevItem?.zuHao
            let zuHaoTXT = ''
            if (currentPrefix === prevPrefix && currentPrefix === nextPrefix) {
              // 当前项在组的中间
              zuHaoTXT = '|'
            } else if (currentPrefix === nextPrefix && currentPrefix !== prevPrefix) {
              // 当前项是组的第一项
              zuHaoTXT = '┐'
            } else if (currentPrefix !== nextPrefix && currentPrefix === prevPrefix) {
              // 当前项是组的最后一项
              zuHaoTXT = '┘'
            }
            this.$set(item, 'zuHaoTXT', zuHaoTXT)
            // 设置模板名称
            this.$set(item, 'muBanMC', category.mingCheng)
          })
        }
      } catch (error) {
        console.error('获取综合医嘱模板明细失败:', error)
        this.$message.error(`获取综合医嘱模板明细失败: ${error.message || error}`)
        this.templateDetails = []
      }
    },

    // 处理选择变化
    handleSelectionChange(selection) {
      const selectedRows = selection
      // 选中组号相同的行
      const sameZuHaoRows = this.templateDetails.filter((row) => {
        return selectedRows.some((selectedRow) => selectedRow.zuHao === row.zuHao)
      })
      this.selectedRows = sameZuHaoRows
      console.log('sameZuHaoRows', sameZuHaoRows)
    },

    // 导入操作
    handleImport() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条医嘱')
        return
      }

      this.$emit('confirm', this.selectedRows)
      this.isVisible = false
    },

    // 全选
    handleSelectAll() {
      const table = this.$refs.orderTable
      this.templateDetails.forEach((row) => {
        table.toggleRowSelection(row, true)
      })
    },

    // 全不选
    handleUnselectAll() {
      const table = this.$refs.orderTable
      table.clearSelection()
    },

    // 反选
    handleInvertSelection() {
      const table = this.$refs.orderTable
      this.templateDetails.forEach((row) => {
        const isSelected = this.selectedRows.includes(row)
        table.toggleRowSelection(row, !isSelected)
      })
    },

    // 处理跨行合并
    handleSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (column.type === 'selection') {
        if (!row.zuHao) {
          return [1, 1]
        }
        let rowspan = 1
        // 计算后续行中相同组号的行数
        for (let i = rowIndex + 1; i < this.templateDetails.length; i++) {
          if (this.templateDetails[i].zuHao === row.zuHao) {
            rowspan++
          } else {
            break
          }
        }
        // 如果是该组的第一行，合并后续行
        if (rowIndex === 0 || this.templateDetails[rowIndex - 1].zuHao !== row.zuHao) {
          return [rowspan, 1]
        } else {
          // 非第一行隐藏单元格
          return [0, 0]
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.comprehensive-template-dialog-content {
  display: flex;
  height: 500px;

  .left-panel {
    width: 220px;
    margin-right: 10px;
    overflow-y: auto;
    height: 100%;

    ::v-deep .el-submenu__title {
      height: 40px;
      line-height: 40px;
      font-size: 14px !important;
      font-weight: 600;
      background-color: transparent;
      padding: 0 20px !important;
      border-bottom: 1px solid #dcdfe6;

      &:hover {
        background-color: #ecf5ff;
      }
    }

    .template-menu {
      height: 100%;
      border: 1px solid #dcdfe6;
      background-color: transparent;

      & > .el-submenu > ::v-deep .el-submenu__title {
        border-bottom: 1px solid #dcdfe6;
        background-color: #eaf0f9;
      }

      // 空列表提示
      .empty-item {
        color: #909399;
        font-style: italic;
      }

      ::v-deep .el-menu-item {
        height: 36px;
        line-height: 36px;
        background-color: transparent;
        border-bottom: 1px solid #dcdfe6;
        padding: 0 20px !important;

        &:hover {
          background-color: #ecf5ff;
        }

        &.is-active {
          color: #409eff;
          background-color: #ecf5ff;
        }
      }
    }
  }

  .right-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;

    .table-title {
      font-weight: bold;
      padding: 0 12px;
      height: 40px;
      background-color: #fafbfc;
      border: 1px solid #dcdfe6;
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        display: inline-block;
        padding-left: 5px;
        border-left: 3px solid #409eff;
      }

      .table-actions {
        display: flex;
        gap: 5px;
      }
    }

    .table-wrapper {
      flex-grow: 1;
      border: 1px solid #dcdfe6;
      border-top: none;
      padding: 12px;
    }

    ::v-deep .el-table {
      width: 100%;
      .el-date-editor.el-input,
      .el-date-editor.el-input__inner,
      .el-select {
        width: 100%;
      }
      td,
      th {
        padding: 4px 0;
      }
      .cell {
        padding-left: 5px;
        padding-right: 5px;
      }
    }
  }
}
</style>
