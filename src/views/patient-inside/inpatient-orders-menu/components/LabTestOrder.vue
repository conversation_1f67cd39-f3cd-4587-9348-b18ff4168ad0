<template>
  <div class="lab-test-order">
    <el-row :gutter="10">
      <el-col :span="6">
        <div class="items">
          <div class="title">化验医嘱列表</div>
          <div class="filter-container">
            <el-input v-model="filterValue" placeholder="请输入拼音首字母查找"></el-input>
            <el-button type="primary" class="purple-button">查找</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="items">
          <div v-if="TestItemTitle" class="title">{{ TestItemTitle }}</div>
        </div>
      </el-col>
      <el-col :span="10">
        <div class="items">
          <div class="title">已选择医嘱</div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'LabTestOrder',
  data() {
    return {
      filterValue: '',
      TestItemTitle: ''
    }
  },
  methods: {}
}
</script>

<style lang="scss" scoped>
.lab-test-order {
  height: 100%;
  overflow-y: auto;
  background: #eff3fb;
  padding-right: 5px;

  .items {
    height: 580px;
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 10px;
    .title {
      font: var(--font-medium);
      font-size: var(--font-size-regular);
      color: #171c28;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 3px;
        height: 16px;
        background: #356ac5;
        margin-right: 6px;
      }
    }
    .filter-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
      .purple-button {
        background: var(--color-purple);
        border: 1px solid var(--color-purple);
        &:hover,
        &:focus {
          background: #ce8be0;
          border-color: #ce8be0;
        }
      }
    }
  }
}
</style>
