<template>
  <default-dialog :visible.sync="dialogVisible" title="肠外营养套餐" width="1200px" pop-type="tip">
    <div class="chang-wai-container">
      <!-- 左侧套餐列表 -->
      <div class="left-menu">
        <div class="left-menu-header">
          <div class="menu-title">
            <span>营养套餐模版</span>
          </div>
          <div class="menu-items">
            <div
              v-for="(item, index) in taoCanList"
              :key="index"
              :class="['menu-item', { active: activeMenuIndex === index, stripe: index % 2 === 0 }]"
              @click="selectMenu(index)"
            >
              {{ item.taoCanMC }}
            </div>
          </div>
        </div>
        <div class="menu-tips">
          <div class="menu-title">
            <span>温馨提示</span>
          </div>
          <div class="tips-content">
            <p>一、肠外营养套餐使用注意事项：</p>
            <p>必需完成营养风险筛查，NRS-2002≥3分，且经口或肠内营养不能满足需要。</p>
            <p>
              依据25-30kcal/kg计算患者能力需求，当经口或肠内营养满足需要量60%时，考虑停用肠外营养。
            </p>
            <p>脂肪乳根据患者病情选择最适合品种，胰岛素根据患者血糖情况选择最合适剂量。</p>
            <p>
              套餐仅适用于病情稳定患者。有严重肝肾功能不全、电解质紊乱，高血糖、高血脂，或肥胖、重度营养不良、妊娠等特殊情况，需要个体化肠外营养者，请发起临床营养中心会诊-肠外营养会诊。
            </p>
            <p>二、监测参数：</p>
            <p>目标能量和蛋白质。</p>
            <p>液体需要量，血电解质、血糖浓度，肝功能、肾功能，血甘油三酯浓度，导管并发症。</p>
            <p>三、监测频率：</p>
            <p>新开患者（尤其电解质紊乱、RFS风险大者）每日监护直到稳定。</p>
            <p>稳定使用1周患者2-7天1次监护。</p>
            <p>稳定使用1周以上患者1-4周1次监护。</p>
          </div>
        </div>
      </div>

      <!-- 右侧内容区 -->
      <div class="right-content">
        <div class="header-title">
          <span>{{ activeTaoCan ? activeTaoCan.taoCanMC : '' }}</span>
          <el-button type="primary" class="import-btn" @click="importOrder">导入</el-button>
        </div>
        <div class="info-container">
          <div class="header-info">
            <div class="date-info">
              <span class="label">开始日期：</span>
              <el-date-picker
                v-model="kaiShiSJ"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期"
                size="small"
              ></el-date-picker>
            </div>
            <div class="duration-info">
              <span class="label">持续天数：</span>
              <el-input v-model="chiXuTS" size="small" style="width: 80px"></el-input>
            </div>
            <div class="execute-info">
              <span class="label">执行频率：</span>
              <el-select v-model="zhiXingPL" size="small" placeholder="请选择执行频率">
                <el-option
                  v-for="item in zhiXingPLList"
                  :key="item.pinLuDM"
                  :label="item.pinLuMC"
                  :value="item.pinLuDM"
                ></el-option>
              </el-select>
            </div>
          </div>

          <div class="medicine-table">
            <el-table
              v-loading="loading"
              :data="yaoPinList"
              style="width: 100%"
              max-height="600px"
              border
              stripe
              size="small"
            >
              <el-table-column label="选择" width="80" align="center">
                <template #default="scope">
                  <!-- 必选项显示为已选中的复选框 -->
                  <el-checkbox v-if="scope.row.zuHeHao === 0" :value="true" disabled></el-checkbox>

                  <!-- 非必选项显示为复选框 -->
                  <el-checkbox
                    v-else
                    :value="selectedYaoPinIds.includes(scope.row.id)"
                    @change="() => handleRadioClick(scope.row)"
                  ></el-checkbox>
                </template>
              </el-table-column>
              <el-table-column prop="yaoPinMC" label="名称"></el-table-column>
              <el-table-column label="用量" width="100" align="center">
                <template #default="{ row }">
                  {{ row.yiCiYL + (row.jiLiangDW || 'ml') }}
                </template>
              </el-table-column>
              <el-table-column label="类型" width="150" align="center">
                <template #default="{ row }">
                  <span :class="getTypeClass(row.zuHeHao)">
                    {{
                      row.zuHeHao === 0
                        ? '必选'
                        : row.zuHeHao === 1
                        ? '组合1'
                        : row.zuHeHao === 2
                        ? '组合2'
                        : row.zuHeHao === 3
                        ? '组合3'
                        : row.zuHeHao
                    }}
                  </span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import {
  getChangWaiYyInit,
  getChangWaiYyTcmc,
  getChangWaiYyByTcMc,
  saveChangWaiYyTc
} from '@/api/inpatient-order'
export default {
  name: 'ChangWaiDialog',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeMenuIndex: 0,
      kaiShiSJ: '',
      chiXuTS: '',
      zhiXingPL: '',
      taoCanList: [],
      zhiXingPLList: [],
      yaoPinList: [],
      selectedYaoPinIds: [],
      loading: false
    }
  },
  computed: {
    activeTaoCan() {
      return this.taoCanList[this.activeMenuIndex] || {}
    },
    bingLiID() {
      return this.$route.params.id
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        // 对话框打开时初始化数据
        this.initData()
      }
    },
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  methods: {
    selectMenu(index) {
      this.activeMenuIndex = index
      // 重置已选择的药品
      this.selectedYaoPinIds = []
      // 获取选中套餐的药品列表
      this.getMedicinesByTcMc(this.taoCanList[index].taoCanMC)
    },
    getTypeClass(zuHeHao) {
      if (zuHeHao === 0) {
        return 'type-required'
      } else if ([1, 2, 3].includes(zuHeHao)) {
        return 'type-combination'
      }
      return ''
    },
    // 初始化数据
    initData() {
      this.loading = true
      this.resetData()

      // 获取初始化数据
      getChangWaiYyInit()
        .then((res) => {
          if (res.hasError === 0) {
            // 设置开始日期
            this.kaiShiSJ = res.data.kaiShiSJ || this.$moment().format('YYYY-MM-DD')

            // 设置执行频率选项
            if (res.data.zhiXingPLList && res.data.zhiXingPLList.length > 0) {
              this.zhiXingPLList = res.data.zhiXingPLList

              // 默认选择第一个频率
              if (this.zhiXingPLList.length > 0) {
                this.zhiXingPL = this.zhiXingPLList[0].pinLuDM
              }
            }

            // 获取套餐列表
            this.getTcmcList()
          }
        })
        .catch((err) => {
          console.error('初始化肠外营养套餐失败', err)
          this.$message.error('初始化肠外营养套餐失败')
          this.loading = false
        })
    },
    // 重置数据
    resetData() {
      this.kaiShiSJ = ''
      this.chiXuTS = ''
      this.zhiXingPL = ''
      this.taoCanList = []
      this.yaoPinList = []
      this.selectedYaoPinIds = []
      this.activeMenuIndex = 0
    },
    // 获取套餐名称列表
    getTcmcList() {
      getChangWaiYyTcmc()
        .then((res) => {
          if (res.hasError === 0 && res.data && res.data.length > 0) {
            this.taoCanList = res.data

            // 默认选中第一个套餐
            if (this.taoCanList.length > 0) {
              this.activeMenuIndex = 0
              this.getMedicinesByTcMc(this.taoCanList[0].taoCanMC)
            }
          }
        })
        .catch((err) => {
          console.error('获取肠外营养套餐名称列表失败', err)
          this.$message.error('获取肠外营养套餐名称列表失败')
          this.loading = false
        })
    },
    // 根据套餐名称获取药品列表
    getMedicinesByTcMc(taoCanMC) {
      if (!taoCanMC) return

      this.loading = true
      getChangWaiYyByTcMc({ taoCanMC })
        .then((res) => {
          if (res.hasError === 0 && res.data && res.data.length > 0) {
            // 保存药品列表
            this.yaoPinList = res.data

            // 自动选中必选项
            this.autoSelectRequiredItems()
          }
          this.loading = false
        })
        .catch((err) => {
          console.error('获取肠外营养套餐药品列表失败', err)
          this.$message.error('获取肠外营养套餐药品列表失败')
          this.loading = false
        })
    },
    // 自动选中必选项
    autoSelectRequiredItems() {
      // 清空已选择的药品
      this.selectedYaoPinIds = []

      // 添加所有必选项
      const requiredItems = this.yaoPinList.filter((item) => item.zuHeHao === 0)
      requiredItems.forEach((item) => {
        if (item.id) {
          this.selectedYaoPinIds.push(item.id)
        }
      })
    },
    // 处理单选按钮点击
    handleRadioClick(row) {
      // 如果是必选项，不做处理
      if (row.zuHeHao === 0) return

      // 获取当前组合号
      const group = row.zuHeHao

      // 检查当前项是否已经被选中
      const isSelected = this.selectedYaoPinIds.includes(row.id)

      // 获取该组合的所有药品ID
      const groupItemIds = this.yaoPinList
        .filter((item) => item.zuHeHao === group)
        .map((item) => item.id)

      // 从已选择列表中移除同组的所有项
      this.selectedYaoPinIds = this.selectedYaoPinIds.filter((id) => !groupItemIds.includes(id))

      // 如果当前项之前未被选中，则添加到选中列表
      // 如果当前项之前已被选中，则不添加（即取消选择）
      if (!isSelected) {
        this.selectedYaoPinIds.push(row.id)
      }
    },

    // 导入医嘱
    importOrder() {
      if (!this.kaiShiSJ) {
        this.$message.warning('请选择开始日期')
        return
      }

      if (!this.chiXuTS) {
        this.$message.warning('请输入持续天数')
        return
      }

      if (!this.zhiXingPL) {
        this.$message.warning('请选择执行频率')
        return
      }

      // 检查每个组合是否至少选择了一项
      const groups = [
        ...new Set(this.yaoPinList.filter((item) => item.zuHeHao !== 0).map((item) => item.zuHeHao))
      ]

      // 获取已选择的非必选项
      const selectedNonRequiredItems = this.yaoPinList.filter(
        (item) => item.zuHeHao !== 0 && this.selectedYaoPinIds.includes(item.id)
      )

      // 检查每个组合是否有选中的项
      for (const group of groups) {
        const hasSelectedInGroup = selectedNonRequiredItems.some((item) => item.zuHeHao === group)
        if (!hasSelectedInGroup) {
          this.$message.warning(`组合${group}项目必须选择一项`)
          return
        }
      }

      // 获取选中的药品数据
      const selectedItems = this.yaoPinList.filter((item) =>
        this.selectedYaoPinIds.includes(item.id)
      )

      const params = {
        bingLiID: this.bingLiID,
        chiXuTS: this.chiXuTS,
        kaiShiSJ: this.kaiShiSJ,
        taoCangMC: this.activeTaoCan.taoCanMC,
        zhiXingPL: this.zhiXingPL,
        ids: this.selectedYaoPinIds,
        selectedItems // 添加选中的药品数据
      }

      // 调用保存接口
      saveChangWaiYyTc(params)
        .then((res) => {
          if (res.hasError === 0) {
            this.$emit('changWaiImportSuccess')
          }
        })
        .catch((err) => {
          console.error('保存肠外营养套餐失败', err)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep(.el-dialog__body) {
  border-bottom: 1px solid #dcdfe6;
}
.chang-wai-container {
  display: flex;

  .left-menu {
    width: 400px;

    .left-menu-header {
      border: 1px solid #dcdfe6;

      .menu-items {
        max-height: 304px;
        overflow-y: auto;
      }

      .menu-item {
        padding: 12px;
        cursor: pointer;
        border-bottom: 1px solid #dcdfe6;

        &:last-child {
          border-bottom: none;
        }

        &:hover {
          background-color: #f5f7fa;
        }

        &.stripe {
          background-color: #eff3f8;
        }

        &.active {
          background-color: #6787cc;
          color: #fff;
        }
      }
    }

    .menu-title {
      font-weight: bold;
      height: 40px;
      padding: 0 12px;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #dcdfe6;
      background-color: #fafbfc;

      span {
        font-size: 14px;
        border-left: 3px solid #356ac5;
        padding-left: 8px;
      }
    }

    .menu-tips {
      border: 1px solid #dcdfe6;
      margin-top: 16px;

      .tips-content {
        padding: 12px;
        font-size: 14px;
        color: rgba(23, 28, 40, 0.65);
        line-height: 20px;
        max-height: 304px;
        overflow-y: auto;

        p {
          margin-bottom: 8px;
        }
      }
    }
  }

  .right-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    border: 1px solid #dcdfe6;
    margin-left: 16px;

    .header-title {
      font-weight: bold;
      height: 40px;
      padding: 0 12px;
      border-bottom: 1px solid #dcdfe6;
      background-color: #fafbfc;
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        font-size: 14px;
        border-left: 3px solid #356ac5;
        padding-left: 8px;
      }

      .import-btn {
        width: 60px;
      }
    }

    .info-container {
      padding: 10px;
    }

    .header-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-bottom: 15px;

      .date-info,
      .duration-info,
      .execute-info {
        display: flex;
        align-items: center;
        margin-right: 10px;

        .label {
          margin-right: 5px;
          white-space: nowrap;
        }
      }
      ::v-deep(.el-date-editor--date input) {
        width: 220px;
      }
      .execute-info {
        margin-right: 0;
      }

      .import-btn {
        margin-left: auto;
      }
    }

    .medicine-table {
      flex: 1;
      overflow-y: auto;
    }
  }
}

.type-required {
  color: #333;
}

.type-combination {
  color: #409eff;
}
</style>
