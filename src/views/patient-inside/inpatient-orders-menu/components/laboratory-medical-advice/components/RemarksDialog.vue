<template>
  <el-dialog
    :visible="dialogRef.visible"
    :append-to-body="true"
    width="500px"
    @open="initFormData()"
    @close="dialogRef.resolve(false)"
  >
    <span slot="title">
      <span class="dialog-title">
        <span class="bar" />
        化验备注
      </span>
    </span>
    <span class="dialog-input">
      备注信息:
      <el-input
        ref="remarksInput"
        v-model="formData"
        type="textarea"
        :autosize="{ minRows: 2, maxRows: 4 }"
        placeholder="请输入内容"
      />
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="dialogRef.resolve(formData)">保 存</el-button>
      <el-button @click="dialogRef.resolve(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  name: 'RemarksDialog',
  props: {
    dialogRef: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: null
    }
  },
  methods: {
    initFormData() {
      this.formData = this.dialogRef.data
      this.$nextTick(() => {
        this.$refs.remarksInput.focus()
      })
    },
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
  .bar {
    border-left: 3px solid #356ac5;
    padding-left: 5px;
  }
}

.dialog-input {
  display: flex;
  align-items: center;
  padding-left: 30px;
  .el-input {
    width: 65%;
    margin-left: 10px;
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}
</style>
