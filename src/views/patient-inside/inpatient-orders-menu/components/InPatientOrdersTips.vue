<template>
  <el-alert class="tips" type="primary" show-icon plain center size="small" :closable="false">
    <div>
      <span>长药输液量：</span>
      <span class="red-font">{{ dangRiJmSyl }}</span>
    </div>
    <a class="red-font">您有0条质控信息需要处理</a>
  </el-alert>
</template>

<script>
import { getDangRiJmSyl } from '@/api/inpatient-order'

export default {
  name: 'InPatientOrdersTips',
  data() {
    return {
      dangRiJmSyl: 0
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  mounted() {
    this.getDangRiJmSyl()
  },
  methods: {
    async getDangRiJmSyl() {
      const res = await getDangRiJmSyl({
        bingLiID: this.bingLiID
      })
      if (res.hasError === 0) {
        this.dangRiJmSyl = res.data
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.tips {
  position: absolute;
  top: 11px;
  right: 21px;
  padding: 2px 12px;
  z-index: 99;
  border: 1px solid rgb(53, 106, 197, 0.45);
  background-color: rgba(53, 106, 197, 0.05);
  width: auto;
  max-width: 400px;
  white-space: nowrap;

  ::v-deep .el-alert__description {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 16px;
  }

  .red-font {
    color: #f35656;
  }
}
</style>
