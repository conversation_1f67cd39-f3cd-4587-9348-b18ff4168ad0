<template>
  <el-input
    v-model="inputValue"
    :disabled="isDisabled"
    class="row-input"
    size="mini"
    @blur="handleBlur"
    @focus="handleFocus"
    @keydown="handleKeyDown"
  />
</template>

<script>
export default {
  name: 'DrawerTableInput',
  props: {
    row: {
      type: Object,
      default: () => {
        return {}
      }
    },
    column: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.row[this.column.value]
      },
      set(value) {
        this.$emit('updateRow', { prop: this.column.value, updateValue: value })
      }
    },
    isDisabled() {
      // 支持通过 column.disabled 属性禁用
      if (this.column.disabled !== undefined) {
        return this.column.disabled
      }
      return false
    }
  },
  methods: {
    handleBlur() {
      this.$emit('inputBlur', { prop: this.column.value, inputValue: this.inputValue })
    },
    handleFocus() {
      this.$emit('inputFocus', { prop: this.column.value, inputValue: this.inputValue })
    },
    handleKeyDown(event) {
      if (event.key === 'Enter') {
        event.preventDefault()
        this.$emit('keyboardNext')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.row-input {
  ::v-deep .el-input__inner {
    padding: 0 5px;
  }
}
</style>
