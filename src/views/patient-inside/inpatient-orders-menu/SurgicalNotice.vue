<template>
  <div class="surgical-notice-view">
    <el-container>
      <el-aside class="left-aside">
        <div class="left">
          <div class="left-header">
            <span class="title">手术通知单列表</span>
            <el-button type="primary" @click="handleAdd">新增</el-button>
          </div>
          <div class="surgical-list">
            <table v-for="item in shouShuTZDList" :key="item.kaiDanSJ" @click="handleTZDInit(item)">
              <thead>
                <template v-if="item.shouShuXM.length">
                  <th v-if="item.shouShuXM[0].shouShuBW" class="table-header" colspan="3">
                    {{ item.shouShuXM[0].shouShuMC }}({{ item.shouShuXM[0].shouShuBW }})
                  </th>
                  <th v-else class="table-header" colspan="3">
                    {{ item.shouShuXM[0].shouShuMC }}
                  </th>
                </template>
                <template v-else>
                  <th class="table-header" colspan="3" style="height: 46px"></th>
                </template>
              </thead>
              <tbody>
                <tr>
                  <td class="table-title">开单时间:</td>
                  <td class="text-center" colspan="2">{{ item.kaiDanSJ }}</td>
                </tr>
                <tr>
                  <td class="table-title">主刀医师:</td>
                  <td class="text-center">{{ item.zhuDaoYSXM }}</td>
                  <td class="text-center" style="color: #356ac5">{{ item.shenPiYJMC }}</td>
                </tr>
                <tr>
                  <td class="table-title">状态:</td>
                  <td class="text-center" style="border-right: none">
                    <el-tag v-if="item.zhuangTaiBZ != 9" hit color="#E2E8F4">
                      {{ zhuangTaiMC(item.zhuangTaiBZ) }}
                    </el-tag>
                    <el-tag v-else hit type="danger">
                      {{ zhuangTaiMC(item.zhuangTaiBZ) }}
                    </el-tag>
                  </td>
                  <td class="text-center" style="border-left: none">
                    <el-tag hit color="#E2E8F4">{{ item.shiFouJZMC }}</el-tag>
                  </td>
                </tr>
                <tr class="last-tr">
                  <td class="table-title last-td">房间:</td>
                  <td class="last-td text-center" style="color: #f35656" colspan="2">
                    <span v-if="item.shouShuJianMC != null && item.taiXu != null">
                      {{ item.shouShuJianMC }}-{{ item.taiXu }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </el-aside>
      <el-main>
        <div v-show="yiPaiHuPaiView" class="right">
          <div class="one-block">
            <div>修改项目：</div>
            <el-select v-model="xiuGaiXM" placeholder="请选择" @change="xiuGaiXMChange">
              <el-option
                v-for="item in xiuGaiXMList"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.mingCheng"
              ></el-option>
            </el-select>
            <!-- <el-button type="danger" size="medium">选择</el-button> -->
            <el-button type="primary" size="mini" @click="yiPaiHuPaiView = false">
              返回上一页
            </el-button>
          </div>
          <div class="two-block">
            <div style="width: 32%">
              <div v-show="xiuGaiXM != '手术部位'" class="flex" style="margin: 10px">
                <div style="width: 70px; text-align: right">{{ xiuGaiXM }}：</div>
                <el-input v-model="currentContent" style="width: 200px" readonly></el-input>
              </div>
              <div class="flex" style="margin: 10px">
                <div v-show="xiuGaiXM != '手术部位'" style="width: 70px; text-align: right">
                  修改为：
                </div>
                <div
                  v-show="xiuGaiXM == '主刀医生' || xiuGaiXM == '台上指导'"
                  @dblclick="handleUpdateContent"
                >
                  <el-input v-model="updateContent" style="width: 200px" readonly></el-input>
                </div>
                <div v-show="xiuGaiXM == '参观者'">
                  <el-input v-model="updateContent" style="width: 200px"></el-input>
                </div>
                <div v-show="xiuGaiXM == '手术部位'">
                  <el-table :data="shouShuTZD.shouShuXM">
                    <el-table-column
                      prop="shouShuMC"
                      label="手术名称"
                      width="240"
                    ></el-table-column>
                    <el-table-column
                      prop="shouShuBW"
                      width="240"
                      label="手术部位(修改前)"
                    ></el-table-column>
                    <el-table-column label="手术部位(修改后)" width="240">
                      <template #default="scope">
                        <el-select
                          v-model="scope.row.newShouShuBW"
                          multiple
                          placeholder="请选择"
                          @change="multipleSelectShouShuBW(scope.row)"
                        >
                          <el-option
                            v-for="item in shouShuBWList"
                            :key="item.buWeiMC"
                            :label="item.buWeiMC"
                            :value="item.buWeiMC"
                          ></el-option>
                        </el-select>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
                <div v-show="xiuGaiXM == '医疗小组'">
                  <el-select v-model="updateContent" style="width: 200px" placeholder="请选择">
                    <el-option
                      v-for="item in initTZD.zhuanKeXZList"
                      :key="item.id.toString()"
                      :label="item.mingCheng"
                      :value="item.id.toString()"
                    ></el-option>
                  </el-select>
                </div>
                <div v-show="xiuGaiXM == '麻醉会诊'">
                  <el-checkbox v-model="updateContent"></el-checkbox>
                </div>
                <div v-show="xiuGaiXM == '特殊要求'">
                  <el-input v-model="updateContent" style="width: 200px"></el-input>
                </div>
              </div>
              <div style="margin: 6px">
                <el-button type="primary" size="mini" @click="handleChangeShouShuTZD">
                  提交
                </el-button>
              </div>
            </div>
          </div>

          <div class="three-block">
            <div class="three-header">
              <div class="title">该手术单的修改记录</div>
              <div>
                <el-button type="primary" @click="refreshXiuGaiJL">刷新</el-button>
                <el-button type="primary" @click="deleteOperationXiuGaiJL">删除记录</el-button>
              </div>
            </div>
            <el-table
              :data="shouShuDanXGJLList"
              :row-key="getRowKeys2"
              style="margin-top: 20px"
              @selection-change="shouShuDanXGJLListChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="id" label="ID" width="80"></el-table-column>
              <el-table-column prop="ziDuanMC" label="修改项目"></el-table-column>
              <el-table-column prop="shouShuMC" label="拟施手术名称" width="180"></el-table-column>
              <el-table-column prop="jiuMingCheng" label="修改前" width="240"></el-table-column>
              <el-table-column prop="xinMingCheng" label="修改后" width="240"></el-table-column>
              <el-table-column prop="shenQingYHXM" label="申请人" width="80"></el-table-column>
              <el-table-column prop="shenQingSJ" label="申请时间" width="160"></el-table-column>
              <el-table-column prop="keShiSPYJ" label="科室审批意见" width="160"></el-table-column>
              <el-table-column prop="keShiSPR" label="科室审批人" width="90"></el-table-column>
              <el-table-column prop="keShiSPSJ" label="科室审批时间" width="160"></el-table-column>
              <el-table-column
                prop="shouShuSSPYJMC"
                label="手术室审批意见"
                width="160"
              ></el-table-column>
              <el-table-column
                prop="shouShuSSPYHXM"
                label="手术室审批人"
                width="100"
              ></el-table-column>
              <el-table-column
                prop="shouShuSSPSJ"
                label="手术室审批时间"
                width="160"
              ></el-table-column>
            </el-table>
          </div>
        </div>
        <div v-show="!yiPaiHuPaiView" class="right">
          <div class="right-header">
            <div class="title">手术通知单</div>
            <div class="button-group">
              <el-button type="primary" :disabled="saveDisabled" @click="handleSave">
                保存
              </el-button>
              <el-button type="primary" @click="handleDelete">删除</el-button>
              <el-popover
                v-model="visible"
                popper-class="right-header-poperover"
                placement="bottom"
                width="240"
              >
                <div class="popover">
                  <div class="popover-item">打印</div>
                  <div class="popover-item">一次性手术器械相关信息查询</div>
                  <div class="popover-item" @click="handleBiHuan">手术闭环</div>
                  <div class="popover-item" @click="handleShenQingTT">申请停台</div>
                  <div class="popover-item" @click="handleShenQingSSHC">申请手术耗材</div>
                </div>

                <el-button slot="reference"><i class="el-icon-more"></i></el-button>
              </el-popover>
            </div>
          </div>
          <div class="right-content">
            <el-container>
              <el-main>
                <table class="right-table">
                  <tr>
                    <td class="right-table-title">姓名:</td>
                    <td class="right-table-content">{{ initTZD.bingRenXM }}</td>
                    <td class="right-table-title">性别:</td>
                    <td class="right-table-content">
                      {{ initTZD.bingRenXB == '1' ? '男' : '女' }}
                    </td>
                    <td class="right-table-title">出生日期:</td>
                    <td class="right-table-content">
                      {{ formatDate(initTZD.chuShengRQ, 'yyyy-MM-dd') }}
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">职业:</td>
                    <td class="right-table-content">{{ initTZD.zhiYeMC }}</td>
                    <td class="right-table-title">病区:</td>
                    <td class="right-table-content">
                      {{ initTZD.bingQuMC }}
                    </td>
                    <td class="right-table-title">床位:</td>
                    <td class="right-table-content">{{ initTZD.chuangWeiHao }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">病案号:</td>
                    <td class="right-table-content">{{ initTZD.bingAnHao }}</td>
                    <td class="right-table-title">入院日期:</td>
                    <td class="right-table-content">
                      {{ formatDate(initTZD.ruYuanRQ, 'yyyy-MM-dd') }}
                    </td>
                    <td class="right-table-title">临床诊断:</td>
                    <td class="right-table-content">{{ initTZD.linChuangZD }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">主刀医生:</td>
                    <td class="right-table-content" @dblclick="selectYS('zhuDaoYSXM')">
                      <el-input
                        v-model="shouShuTZD.zhuDaoYSXM"
                        readonly
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">台上指导:</td>
                    <td class="right-table-content" @dblclick="selectYS('taiShangZDXM')">
                      <el-input
                        v-model="shouShuTZD.taiShangZDXM"
                        readonly
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">参观者:</td>
                    <td class="right-table-content">
                      <el-input v-model="shouShuTZD.canGuanZhe"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">第一助手:</td>
                    <td class="right-table-content" @dblclick="selectYS('diYiZSXM')">
                      <el-input
                        v-model="shouShuTZD.diYiZSXM"
                        readonly
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">第二助手:</td>
                    <td class="right-table-content" @dblclick="selectYS('diErZSXM')">
                      <el-input
                        v-model="shouShuTZD.diErZSXM"
                        readonly
                        class="select-input"
                      ></el-input>
                    </td>
                    <td class="right-table-title">开单者:</td>
                    <td class="right-table-content">{{ shouShuTZD.kaiDanYSXM }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">第三助手:</td>
                    <td class="right-table-content" @dblclick="selectYS('diSanZSXM')">
                      <el-input
                        v-model="shouShuTZD.diSanZSXM"
                        readonly
                        class="select-input"
                      ></el-input>
                    </td>
                    <td v-if="noticeName != 'CatheterSurgeryNotice'" class="right-table-title">
                      第四助手:
                    </td>
                    <td
                      v-if="noticeName != 'CatheterSurgeryNotice'"
                      class="right-table-content"
                      @dblclick="selectYS('diSiZSXM')"
                    >
                      <el-input
                        v-model="shouShuTZD.diSiZSXM"
                        readonly
                        class="select-input"
                      ></el-input>
                    </td>
                    <td
                      v-if="noticeName == 'CatheterSurgeryNotice'"
                      colspan="2"
                      class="right-table-content"
                    ></td>
                    <td
                      colspan="2"
                      style="
                        text-align: center;
                        text-decoration: underline;
                        color: #356ac5;
                        cursor: pointer;
                        background-color: #fff;
                      "
                    >
                      {{ shouShuJBFilter }}
                    </td>
                  </tr>
                  <tr
                    v-for="row in shuZhongHUiZhenRowNum(shouShuTZD.shuZhongHZ.length + 1)"
                    :key="row"
                  >
                    <template
                      v-for="col in shuZhongHUiZhenColNum(row, shouShuTZD.shuZhongHZ.length + 1)"
                    >
                      <td
                        v-if="row == 1 && col == 1"
                        :key="col"
                        style="text-align: center"
                        colspan="2"
                      >
                        <el-button style="color: #356ac5" @click="handleAddShuZhongHZ">
                          新增术中会诊
                        </el-button>
                      </td>
                      <template v-else>
                        <td :key="col + '1'" class="right-table-title">会诊医生:</td>
                        <td
                          :key="col + '2'"
                          class="right-table-content"
                          @dblclick="selectYS('shuZhongHZ', (row - 1) * 3 + col - 2)"
                        >
                          <el-input
                            v-model="shouShuTZD.shuZhongHZ[(row - 1) * 3 + col - 2].xingMing"
                            class="select-input"
                            readonly
                          ></el-input>
                        </td>
                      </template>
                    </template>
                  </tr>
                  <tr>
                    <td class="right-table-title">拟施手术:</td>
                    <td class="right-table-content" colspan="5" @dblclick="selectShouShu">
                      <div class="niShiShouShuList">
                        <span @dblclick.stop>
                          <el-tag
                            v-for="tag in shouShuTZD.shouShuXM"
                            :key="tag.shouShuDM"
                            closable
                            @close="handleNiShiSSTagClose(tag)"
                          >
                            {{ tag.shouShuMC }}
                            <span v-if="tag.shouShuBW && tag.shouShuBW != ' '">
                              ({{ tag.shouShuBW }})
                            </span>
                          </el-tag>
                        </span>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">手术室:</td>
                    <td class="right-table-content">
                      <el-select
                        v-if="noticeName == 'SurgicalNotice'"
                        v-model="shouShuTZD.shouShuShiDM"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in initTZD.shouShuShiList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                      <el-select
                        v-else-if="noticeName == 'InterventionalSurgeryNotice'"
                        v-model="shouShuTZD.shouShuShiDM"
                        placeholder="请选择"
                        @change="shouShuShiChange"
                      >
                        <el-option
                          v-for="item in initTZD.xinZangJRSSS"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                      <el-select
                        v-else-if="noticeName == 'CatheterSurgeryNotice'"
                        v-model="shouShuTZD.shouShuShiDM"
                        placeholder="请选择"
                        @change="shouShuShiChange"
                      >
                        <el-option
                          v-for="item in initTZD.waiZhouDGSSS"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td v-if="noticeName == 'SurgicalNotice'" class="right-table-title">
                      专科小组:
                    </td>
                    <td v-else class="right-table-title">手术间:</td>
                    <td v-if="noticeName == 'SurgicalNotice'" class="right-table-content">
                      <el-select v-model="shouShuTZD.youXianJB" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.youXianJBList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td v-else class="right-table-content">
                      <el-select v-model="shouShuTZD.shouShuJianDM" placeholder="请选择">
                        <el-option
                          v-for="item in shouShuJianList"
                          :key="item.shouShuJian"
                          :label="item.shouShuJianMC"
                          :value="item.shouShuJian"
                        ></el-option>
                      </el-select>
                    </td>
                    <td
                      class="right-table-title"
                      style="
                        text-align: left;
                        display: flex;
                        width: 100%;
                        flex-direction: row-reverse;
                      "
                    >
                      <span>:</span>
                      <span>预计手术时间(分钟)</span>
                    </td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.yuJiSSSC" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.yuJiSSSCList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">医疗小组:</td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.zhuanKeXZ" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.zhuanKeXZList"
                          :key="item.id.toString()"
                          :label="item.mingCheng"
                          :value="item.id.toString()"
                        ></el-option>
                      </el-select>
                    </td>
                    <td
                      v-if="noticeName == 'InterventionalSurgeryNotice'"
                      class="right-table-title"
                    >
                      台序:
                    </td>
                    <td v-else class="right-table-title">小组台序:</td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.xiaoZuTX" placeholder="请选择">
                        <el-option
                          v-for="item in xiaoZuTaiXuList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td
                      class="right-table-title"
                      style="
                        text-align: left;
                        display: flex;
                        width: 100%;
                        flex-direction: row-reverse;
                        align-items: center;
                      "
                    >
                      <span>:</span>
                      <span>拟施手术日期</span>
                    </td>
                    <td class="right-table-content">
                      <el-date-picker
                        v-model="shouShuTZD.niShouShuSJ"
                        type="date"
                        placeholder="选择日期时间"
                        class="niShiShouShuSJ"
                        format="yyyy-MM-dd"
                        value-format="yyyy-MM-dd HH:mm:ss"
                      ></el-date-picker>
                      <!-- {{ formatDate(initTZD.niShouShuSJ, 'yyyy-MM-dd') }} -->
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">隔离种类:</td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.geLiZL" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.geLiZL"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <!-- 缺失字段 -->
                    <td
                      class="right-table-title"
                      style="
                        text-align: left;
                        display: flex;
                        width: 100%;
                        flex-direction: row-reverse;
                      "
                    >
                      <span>:</span>
                      <span>传染病阳性指标</span>
                    </td>
                    <td colspan="3" class="right-table-content">
                      <el-input v-model="shouShuTZD.chuanRanBingYXZB"></el-input>
                    </td>
                  </tr>
                  <tr>
                    <!-- 缺失字段 -->
                    <td class="right-table-title">预约冰冻切片:</td>
                    <td class="right-table-content">
                      <el-radio v-model="shouShuTZD.bingDongQP" label="0">否</el-radio>
                      <el-radio v-model="shouShuTZD.bingDongQP" label="1">是</el-radio>
                    </td>
                    <td class="right-table-title">麻醉会诊:</td>
                    <td class="right-table-content">
                      <el-checkbox v-model="maZuiHZMapper"></el-checkbox>
                    </td>
                    <td class="right-table-title">急诊:</td>
                    <td class="right-table-content">
                      <el-checkbox v-model="shiFouJZMapper"></el-checkbox>
                    </td>
                  </tr>

                  <tr>
                    <td class="right-table-title">日间手术:</td>
                    <td class="right-table-content">
                      <el-checkbox v-model="riJianSSMapper"></el-checkbox>
                    </td>
                    <td v-if="noticeName == 'SurgicalNotice'" class="right-table-title">
                      需要技师:
                    </td>
                    <td v-if="noticeName == 'SurgicalNotice'" class="right-table-content">
                      <el-checkbox v-model="xuYaoJSMapper"></el-checkbox>
                    </td>
                    <td v-if="noticeName != 'SurgicalNotice'" class="right-table-title">
                      其它要求:
                    </td>
                    <td v-if="noticeName != 'SurgicalNotice'" class="right-table-content">
                      <el-checkbox v-model="qiYaZL">气压治疗</el-checkbox>
                      <el-checkbox v-model="zhongDaSS">重大手术</el-checkbox>
                    </td>
                    <td class="right-table-title">开单时间:</td>
                    <td class="right-table-content">{{ shouShuTZD.kaiDanSJ }}</td>
                  </tr>
                  <tr>
                    <!-- <td colspan="2" class="right-table-title"></td> -->
                    <td class="right-table-title">特殊手术体位:</td>
                    <td class="right-table-content">
                      <el-select v-model="shouShuTZD.teShuSSTW" placeholder="请选择">
                        <el-option
                          v-for="item in initTZD.teShuSSTWList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                    <td class="right-table-title">新技术新项目:</td>
                    <td colspan="3" class="right-table-content">
                      <el-select
                        v-model="shouShuTZD.xinJiShuXXM"
                        style="width: 100%"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in xinJiShuXXMList"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </td>
                  </tr>
                  <tr v-if="noticeName == 'SurgicalNotice'">
                    <td class="right-table-title">其它要求:</td>
                    <td class="right-table-content">
                      <el-checkbox v-model="qiYaZL">气压治疗</el-checkbox>
                      <el-checkbox v-model="zhongDaSS">重大手术</el-checkbox>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">术前讨论:</td>
                    <td colspan="5" class="right-table-content">
                      <div
                        style="display: flex; align-items: center; justify-content: space-between"
                      >
                        <span v-if="shouShuTZD.shuQianTLWSID">
                          {{ shouShuTZD.shuQianTLWSMC }}
                        </span>
                        <span v-else>未关联</span>
                        <div>
                          <el-button @click="openShuQianTLDialog">选择</el-button>
                          <el-button @click="cancelShuQianTL">取消关联</el-button>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">知情同意书:</td>
                    <td colspan="5" class="right-table-content">
                      <div
                        style="display: flex; align-items: center; justify-content: space-between"
                      >
                        <span v-if="shouShuTZD.zhiQingTYSWSID">
                          {{ shouShuTZD.zhiQingTYSWSMC }}
                        </span>
                        <span v-else>未关联</span>
                        <div>
                          <el-button @click="openzhiQingTYSDialog">选择</el-button>
                          <el-button @click="cancelZhiQingTYS">取消关联</el-button>
                        </div>
                      </div>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title" style="vertical-align: top">
                      <div>特殊要求:</div>
                      <div
                        style="color: #155bd4; text-decoration: underline; cursor: pointer"
                        @click="openWenZiMB"
                      >
                        文字模板
                      </div>
                    </td>
                    <td class="right-table-content" colspan="5">
                      <el-input
                        v-model="shouShuTZD.kaiDanBZ"
                        type="textarea"
                        :autosize="{ minRows: 6, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                        class="kaiDanBZ"
                      ></el-input>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">审批医师:</td>
                    <td class="right-table-content">{{ shouShuTZD.shenPiYSXM }}</td>
                    <td class="right-table-title">审批意见:</td>
                    <td class="right-table-content">{{ shouShuTZD.shenPiYJMC }}</td>
                    <td class="right-table-title">审批时间:</td>
                    <td class="right-table-content">
                      <!-- {{ formatDate(shouShuTZD.shenPiSJ, 'yyyy-MM-dd') }} -->
                      {{ shouShuTZD.shenPiSJ }}
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">手术房间:</td>
                    <td class="right-table-content">
                      {{ shouShuTZD.shouShuJianMC }}-{{ shouShuTZD.taiXu }}
                    </td>
                    <td class="right-table-title">手术时间:</td>
                    <td class="right-table-content">
                      <!-- {{ formatDate(shouShuTZD.niShouShuSJ, 'yyyy-MM-dd') }} -->
                      {{ shouShuTZD.niShouShuSJ }}
                    </td>
                    <td class="right-table-title">实施手术:</td>
                    <td class="right-table-content">
                      {{ shiShiShouShuMapper(shouShuTZD.shiShiSS) }}
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-title">麻醉医师1:</td>
                    <td class="right-table-content">{{ shouShuTZD.maZuiYS1XM }}</td>
                    <td class="right-table-title">麻醉医师2:</td>
                    <td class="right-table-content">{{ shouShuTZD.maZuiYS2XM }}</td>
                    <td class="right-table-title">麻醉医师3:</td>
                    <td class="right-table-content">{{ shouShuTZD.maZuiYS3XM }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">器械护士1:</td>
                    <td class="right-table-content">{{ shouShuTZD.qiXieHS1XM }}</td>
                    <td class="right-table-title">巡回护士1:</td>
                    <td class="right-table-content">{{ shouShuTZD.xunHuiHS1XM }}</td>
                    <td class="right-table-title">感染处置:</td>
                    <td class="right-table-content">{{ shouShuTZD.ganRanCZ1XM }}</td>
                  </tr>
                  <tr>
                    <td class="right-table-title">器械护士2:</td>
                    <td class="right-table-content">{{ shouShuTZD.qiXieHS2XM }}</td>
                    <td class="right-table-title">巡回护士2:</td>
                    <td class="right-table-content">{{ shouShuTZD.xunHuiHS2XM }}</td>
                    <td colspan="2" class="right-table-title"></td>
                  </tr>
                  <tr>
                    <td colspan="6" class="right-table-title">
                      <div class="title">说明</div>
                    </td>
                  </tr>
                  <tr>
                    <td class="right-table-footer-tip" colspan="6">
                      <div style="background-color: #ffffff; padding: 8px">
                        <div>
                          1.医师在开通知单时请仔细核对
                          <span style="color: #ed6a0c">自己和主刀医师当前的专科。</span>
                        </div>
                        <div>
                          2.通知单
                          <span style="color: #ed6a0c">审批时的专科</span>
                          由
                          <span style="color: #ed6a0c">主刀医师</span>
                          的当前专科决定。比如：如果主刀医师是A专科，则也需要A专科医师审批。
                        </div>
                        <div>
                          3.平诊手术通知单接收时间为
                          <span style="color: #ed6a0c">05：00</span>
                          至
                          <span style="color: #ed6a0c">11：00</span>
                          ，如有特殊情况请与手术室联系。
                        </div>
                        <div>
                          4.日间手术开单时间在
                          <span style="color: #ed6a0c">15：00</span>
                          后，需电话联系手术室。
                        </div>
                        <div>
                          ——》
                          <span style="color: #155bd4; text-decoration: underline">
                            术前手术室材料使用教程
                          </span>
                          <span v-show="yiPaiHuPaiShow">
                            《—— ——》
                            <span
                              style="color: #155bd4; text-decoration: underline"
                              @click="openYiPaiHuPai"
                            >
                              医排护排手术修改
                            </span>
                            《——
                          </span>
                        </div>
                      </div>
                    </td>
                  </tr>
                </table>
              </el-main>
              <el-aside class="right-aside">
                <div class="right-aside-header">
                  <el-button size="mini" @click="getBrHuaYanJg">导入化验结果</el-button>
                </div>
                <table class="right-aside-table">
                  <thead>
                    <tr>
                      <td>化验名称</td>
                      <td>结果</td>
                      <td>化验时间</td>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in huaYanJGList" :key="item.huaYanDM">
                      <td>{{ item.huaYanMC }}</td>
                      <td>{{ item.huaYanJG }}</td>
                      <td>{{ item.huaYanSJ }}</td>
                    </tr>
                  </tbody>
                </table>
              </el-aside>
            </el-container>
          </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog width="30%" class="Dialog" :visible.sync="renYuanXZDialog">
      <div slot="title" class="title">人员选择</div>
      <div class="flex">
        <el-input
          v-model="renYuanXM"
          class="renYuanXMInput"
          placeholder="请输入人员终身码或拼音/五笔码"
          @change="queryZhuDaoYS"
        ></el-input>
        <el-checkbox v-model="benZhuanKe">本专科</el-checkbox>
      </div>
      <table class="renYuanList">
        <thead>
          <tr>
            <td>姓名</td>
            <td>终身码</td>
            <td>所在专科</td>
            <td>人员类别</td>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="item in currentPageZhuDaoYSList"
            :key="item.yongHuID"
            @dblclick="handleSelectRY(item)"
          >
            <td>{{ item.xingMing }}</td>
            <td>{{ item.zhongShenDM }}</td>
            <td>{{ item.xianZhuanKeMC }}</td>
            <td>{{ item.renYuanLBMC }}</td>
          </tr>
        </tbody>
      </table>
      <el-pagination
        class="pagination"
        small
        background
        :current-page.sync="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="zhuDaoYSListFilter.length"
      ></el-pagination>
    </el-dialog>
    <el-dialog
      width="50%"
      class="Dialog"
      :visible.sync="niShiShouShuDialog"
      @close="handleShouShuListClose"
    >
      <div slot="title" class="title">手术选择窗口</div>
      <div class="flex shouShuXZHeader shouShuHeader">
        <span class="title">手术列表</span>
        <div class="flex" style="flex-grow: 0.1">
          <span style="width: 63%; font-weight: 600">输入拼音五笔(回撤结束):</span>
          <el-input v-model="shouShuQueryMC" @change="queryShouShuList"></el-input>
        </div>
      </div>
      <div class="shouShuTable shouShuXZList">
        <el-table
          ref="shouShuXZList"
          :data="currentPageShouShuXZList"
          tooltip-effect="dark"
          :row-key="getRowKeys1"
          style="width: 100%; height: 350px; overflow-y: scroll"
          @select="shouShuListChange"
        >
          <el-table-column type="selection" width="80" :reserve-selection="true"></el-table-column>
          <el-table-column prop="daiMa" label="手术代码" width="120"></el-table-column>
          <el-table-column prop="mingCheng" label="手术名称"></el-table-column>
          <el-table-column prop="shouShuJBMC" label="手术级别" width="120"></el-table-column>
          <el-table-column prop="icddm" label="ICD9" width="120"></el-table-column>
        </el-table>
        <el-pagination
          class="pagination"
          small
          background
          :current-page.sync="shouShuXZCurrentPage"
          :page-size="shouShuXZPageSize"
          layout="total, prev, pager, next"
          :total="shouShuXZList.length"
        ></el-pagination>
      </div>
      <div class="yiXuanZeSSHeader shouShuHeader">
        <span class="title">已选择的手术</span>
      </div>
      <div class="shouShuTable yiXuanZeShouShuList">
        <el-table
          ref="shouShuYXZSelectedList"
          :data="shouShuYXZSelectionList"
          tooltip-effect="dark"
          style="width: 100%; height: 100%; overflow-y: scroll"
          :row-key="getRowKeys"
          @cell-click="handleShouShuBWClick"
          @select="shouShuYXZListChange"
        >
          <el-table-column
            class-name="zhuShouShuSpan"
            type="selection"
            width="80"
            :reserve-selection="true"
          ></el-table-column>
          <el-table-column prop="shouShuDM" label="手术代码" width="120"></el-table-column>
          <el-table-column prop="shouShuMC" label="手术名称"></el-table-column>
          <el-table-column prop="shouShuBW" label="手术部位" width="120"></el-table-column>
          <el-table-column prop="shouShuJBMC" label="手术级别" width="120"></el-table-column>
          <el-table-column prop="icd" label="ICD9" width="120"></el-table-column>
        </el-table>
      </div>
      <div style="margin-top: 20px">
        <i class="el-icon-info" style="color: #356ac5"></i>
        <span style="margin-left: 1px">
          手术选择后点完成按钮返回主界面，如需调整手术准入，请联系医务科。
        </span>
      </div>
      <div style="float: right; padding-bottom: 16px">
        <el-button type="primary" @click="handleNiShiShouShuSubmit">确定</el-button>
        <el-button @click="niShiShouShuDialog = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog width="20%" class="Dialog shouShuBWDialog" :visible.sync="shouShuBWDialog">
      <div slot="title" class="title">选择手术部位</div>
      <el-table :data="shouShuBWList">
        <el-table-column label="选择手术部位">
          <template #default="scope">
            <el-radio
              v-if="scope.row.buWeiMC.includes('侧')"
              v-model="shouShuBWC"
              :label="scope.row.buWeiMC"
            >
              {{ scope.row.buWeiMC }}
            </el-radio>
            <el-radio v-else v-model="shouShuBWD" :label="scope.row.buWeiMC">
              {{ scope.row.buWeiMC }}
            </el-radio>
          </template>
        </el-table-column>
      </el-table>
      <div style="float: right; padding: 20px 0">
        <el-button type="primary" size="mini" @click="handleSelectShouShuBW">确定</el-button>
        <el-button type="primary" size="mini" @click="resetShouShuBWDialog">重置</el-button>
        <el-button size="mini" @click="shouShuBWDialog = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog width="50%" class="Dialog wenZiMBDialog" :visible.sync="wenZiMBDialog">
      <div slot="title" class="title">模板导入及维护</div>
      <div class="flex">
        <div class="wenZiMB-left">
          <div class="wenZiMB-left-header">模板名称</div>
          <div class="wenZiMB-left-content">
            <el-collapse :key="wenZiMBCollapseKey" accordion @change="wenZiMBUpdate">
              <template v-for="(item, index) in wenZiMBList.moBanLX">
                <div :key="index" class="model-item">
                  <el-collapse-item :title="item.mingCheng" :name="index">
                    <template v-for="children in wenZiMBFilter(item.mingCheng)">
                      <div
                        :key="children.id"
                        class="model-item-child"
                        @click="onWZMBClick(children)"
                      >
                        {{ children.moBanMC }}
                      </div>
                    </template>
                  </el-collapse-item>
                </div>
              </template>
            </el-collapse>
          </div>
        </div>
        <div class="wenZiMB-right">
          <div class="wenZiMB-right-header">
            <div class="title">{{ currentMB }}</div>
            <div>
              <el-button type="primary" size="medium" @click="clearWenZiMB">新增</el-button>
              <el-button type="primary" size="medium" @click="saveWenZiMB">保存</el-button>
            </div>
          </div>
          <div class="wenZiMB-right-content">
            <table>
              <tr style="height: 10%">
                <td class="table-title">模板名称：</td>
                <td class="table-content">
                  <el-input v-model="moBanData.moBanMC" placeholder="请输入模板名称"></el-input>
                </td>
              </tr>
              <tr>
                <td class="table-title">模板内容：</td>
                <td class="table-content">
                  <el-input
                    v-model="moBanData.moBanNR"
                    type="textarea"
                    placeholder="请输入模板内容"
                    show-word-limit
                    :clearable="true"
                  ></el-input>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: flex-end; margin-top: 30px">
        <el-button type="primary" @click="writeIntoWenZiMB">导入</el-button>
        <el-button @click="wenZiMBDialog = false">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      width="60%"
      class="Dialog shuQianTLDialog"
      :visible.sync="shuQianTLDialog"
      @close="shuQianTLClose"
    >
      <div slot="title" class="title">绑定术前讨论记录</div>
      <div>
        <el-table ref="shuQianTLTable" :data="shuQianTLData" @select="shuQianTLSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="id" label="文书ID" width="100"></el-table-column>
          <el-table-column prop="geshiDM" label="格式代码" width="80"></el-table-column>
          <!-- <el-table-column prop="bingRenXM" label="病人姓名" width="80"></el-table-column> -->
          <el-table-column prop="wenShuMC" label="文书名称"></el-table-column>
          <el-table-column prop="jilluSJ" label="记录时间" width="150"></el-table-column>
          <el-table-column prop="shouciLRSJ" label="首次录入时间" width="150"></el-table-column>
          <!-- <el-table-column prop="caoZuoZhe" label="操作者" width="80"></el-table-column> -->
        </el-table>
      </div>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-button type="primary" @click="updateShuQianTLData">导入数据</el-button>
      </div>
    </el-dialog>
    <el-dialog
      width="60%"
      class="Dialog zhiQingTYSDialog"
      :visible.sync="zhiQingTYSDialog"
      @close="zhiQingTYSClose"
    >
      <div slot="title" class="title">绑定知情同意书</div>
      <div>
        <el-table ref="zhiQingTYSTable" :data="zhiQingTYSData" @select="zhiQingTYSSelectionChange">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="id" label="文书ID" width="100"></el-table-column>
          <el-table-column prop="geshiDM" label="格式代码" width="80"></el-table-column>
          <!-- <el-table-column prop="bingRenXM" label="病人姓名" width="80"></el-table-column> -->
          <el-table-column prop="wenShuMC" label="文书名称"></el-table-column>
          <el-table-column prop="jilluSJ" label="记录时间" width="150"></el-table-column>
          <el-table-column prop="shouciLRSJ" label="首次录入时间" width="150"></el-table-column>
          <!-- <el-table-column prop="caoZuoZhe" label="操作者" width="80"></el-table-column> -->
        </el-table>
      </div>
      <div style="display: flex; justify-content: flex-end; margin-top: 20px">
        <el-button type="primary" @click="updateZhiQingTYSData">导入数据</el-button>
      </div>
    </el-dialog>
    <el-dialog width="30%" class="Dialog shouShuTTDialog" :visible.sync="shouShuTTDialog">
      <div slot="title" class="title">手术停台</div>
      <table>
        <tr>
          <td class="table-title">停台申请人类型:</td>
          <td class="table-content">
            <el-select
              v-model="stopOperationData.type"
              style="width: 100%"
              placeholder="请选择停台申请人类型"
            >
              <el-option
                v-for="item in stopOperationData.TingTaiSQRLX"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              ></el-option>
            </el-select>
          </td>
        </tr>
        <tr>
          <td class="table-title">停台原因</td>
          <td class="table-content">
            <el-select
              v-model="stopOperationData.reason"
              style="width: 100%"
              placeholder="请选择停台原因"
            >
              <el-option
                v-for="item in stopOperationData.TingTaiYY"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              ></el-option>
            </el-select>
          </td>
        </tr>
        <tr>
          <td class="table-title">停台备注:</td>
          <td class="table-content">
            <el-input v-model="stopOperationData.tingTaiBZ" type="textarea"></el-input>
          </td>
        </tr>
      </table>
      <div class="flex" style="justify-content: flex-end; margin-top: 12px">
        <el-button type="primary" size="mini" @click="handleShouShuTT">确认停台</el-button>
        <el-button size="mini" @click="shouShuTTDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  initShouShuTZDList,
  initShouShuTZD,
  getListByZhuanKeID,
  updateShouShuTZD,
  addShouShuTZD,
  deleteSstzd,
  getShouShuRyk,
  getZhunRuSSXM,
  getBrHuaYanJg,
  getWenShuListByWslx,
  getWenShuListByWSLXList,
  initKaiDanBzMb,
  getKaiDanBzMbById,
  addKaiDanBzMb,
  updateKaiDanBzMb,
  getKaiDanBzMbListByMoBanLx,
  initOperationXiuGaiJL,
  saveOperationXiuGaiJL,
  deleteOperationXiuGaiJL,
  getOperationXiuGaiJL,
  getShouShuJianList,
  stopOperationInit,
  stopOperationByDoctor
} from '@/api/surgical-notice'
import { xiaoZuTaiXuList, shouShuTZD } from './init/surgical-notice-init'
import { format } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      maxV: -1,
      maxKey: '',
      stopOperationData: {}, //停台初始化数据
      shouShuJianList: [], //手术间列表
      noticeName: this.$store.state.patient.sideBarRoute, //当前页面路由名
      zhiQingTYSSelectionCache: {},
      zhiQingTYSSelection: {},
      shuQianTLSelectionCache: {}, //未点确定暂存数据
      wenZiMBList: [],
      wenZiMBCollapseKey: 0,
      huaYanJGList: [],
      selectMode: '', //选择医生模式 (''为选择医生，'update'为修改医生)
      zhuShouShuDM: '',
      shuQianTLData: [],
      zhiQingTYSData: [],
      currentMB: '个人模板',
      moBanData: {
        moBanMC: '',
        moBanNR: ''
      },

      yiPaiHuPaiView: false, //医排护排界面显示
      updateContent: '', //修改后的值
      updateData: {}, //需要修改的数据
      xiuGaiXMList: [],
      xiuGaiXM: '主刀医生',
      saveDisabled: false,
      xinJiShuXXMList: [],
      shouShuTZD: {},
      initTZD: {},
      maZuiHZ: false,
      shiFouJZ: false,
      riJianSS: false,
      xuYaoJS: false,
      canGuanZhe: '',
      teShuYQ: '',
      zhuYuanID: null, //住院ID
      visible: false, //头部右侧三个点按钮是否显示
      shouShuTZDList: [], //通知单列表
      renYuanXZDialog: false, //主刀医生选择窗口
      niShiShouShuDialog: false, //拟施手术窗口
      shouShuXMSubmit: false,
      shouShuBWDialog: false, //手术部位窗口
      wenZiMBDialog: false, //文字模板窗口
      shuQianTLDialog: false, //术前讨论窗口
      zhiQingTYSDialog: false, //知情同意书窗口
      shouShuTTDialog: false, //申请停台窗口
      shouShuBWSelection: {}, //手术部位选择
      shouShuBWC: '',
      shouShuBWD: '',
      shouShuDanXGJLList: [], //手术单修改记录列表
      shouShuDanXGJLSelectionList: [],
      zhuDaoYSList: [], //主刀医生列表
      currentPage: 1,
      pageSize: 13, //每页条数
      shouShuBWList: [
        {
          buWeiMC: '左侧'
        },
        {
          buWeiMC: '右侧'
        },
        {
          buWeiMC: '双侧'
        },
        {
          buWeiMC: '上段'
        },
        {
          buWeiMC: '中段'
        },
        {
          buWeiMC: '下段'
        }
      ],
      shouShuXZList: [], //手术选择列表(接口获取到的所有手术列表)
      cacheShouShuXM: [],
      shouShuYXZSelectionList: [],
      shouShuXZCurrentPage: 1,
      shouShuXZPageSize: 7,
      shouShuQueryMC: '', //手术选择窗口查询手术名称
      benZhuanKe: true, //手术选择窗口本专科
      renYuanXM: '',
      qiYaZL: false,
      zhongDaSS: false
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientInfo: ({ patient }) => patient.patientInit
    }),
    tongZhiDanZTColor() {
      return function (zhuangTaiBZ) {
        switch (zhuangTaiBZ) {
          case '9':
            return 'red'
          default:
            return '#E2E8F4'
        }
      }
    },
    leiBie() {
      switch (this.noticeName) {
        case 'SurgicalNotice':
          return '0'
        case 'InterventionalSurgeryNotice':
          return '1'
        case 'CatheterSurgeryNotice':
          return '2'
        default:
          return '0'
      }
    },
    shouShuJBFilter() {
      let shouShuJBMC = ''
      let max = 0
      if (this.shouShuTZD.shouShuXM) {
        this.shouShuTZD.shouShuXM.map((item) => {
          if (parseInt(item.shouShuJB) > max) {
            shouShuJBMC = item.shouShuJBMC
            max = parseInt(item.shouShuJB)
          }
        })
      }
      return shouShuJBMC
    },
    yiPaiHuPaiShow() {
      return this.shouShuTZD.tongZhiDID && parseInt(this.shouShuTZD.zhuangTaiBZ) >= 3
    },
    yiXuanZeSSCheckBox() {
      return function (row) {
        if (row.shouShuDM == this.zhuShouShuDM) {
          return true
        }
        return false
      }
    },
    currentContent() {
      switch (this.xiuGaiXM) {
        case '主刀医生':
          return this.shouShuTZD.zhuDaoYSXM
        case '台上指导':
          return this.shouShuTZD.taiShangZDXM
        case '参观者':
          return this.shouShuTZD.canGuanZhe
        case '手术部位':
          let teShuSSTW = this.initTZD.teShuSSTWList.find((item) => {
            if (item.daiMa == this.shouShuTZD.teShuSSTW) {
              return item
            }
          })
          if (teShuSSTW) return teShuSSTW.mingCheng
          return ''
        case '医疗小组':
          let zhuanKeXZ = this.initTZD.zhuanKeXZList.find((item) => {
            if (item.id.toString() == this.shouShuTZD.zhuanKeXZ) {
              return item
            }
          })
          if (zhuanKeXZ) return zhuanKeXZ.mingCheng
          return ''
        case '麻醉会诊':
          return this.maZuiHZMapper
        case '特殊要求':
          return this.shouShuTZD.kaiDanBZ
        default:
          return ''
      }
    },
    moBanLXFilter() {
      return function (mingCheng) {
        switch (mingCheng) {
          case '个人模板':
            return 1
          case '专科模板':
            return 2
          case '全院模板':
            return 3
        }
      }
    },
    wenZiMBFilter() {
      return function (mingCheng) {
        switch (mingCheng) {
          case '个人模板':
            return this.wenZiMBList.geRenMB
          case '专科模板':
            return this.wenZiMBList.zhuanKeMB
          case '全院模板':
            return this.wenZiMBList.quanYuanMB
        }
      }
    },
    zhuDaoYSListFilter() {
      if (this.benZhuanKe) {
        return this.zhuDaoYSList.filter((item) => item.xianZhuanKeID == this.zhuanKeID)
      }
      return this.zhuDaoYSList
    },
    currentPageZhuDaoYSList() {
      return this.zhuDaoYSListFilter.slice(
        (this.currentPage - 1) * this.pageSize,
        (this.currentPage - 1) * this.pageSize + this.pageSize
      )
    },
    currentPageShouShuXZList() {
      return this.shouShuXZList.slice(
        (this.shouShuXZCurrentPage - 1) * this.shouShuXZPageSize,
        (this.shouShuXZCurrentPage - 1) * this.shouShuXZPageSize + this.shouShuXZPageSize
      )
    },
    shuZhongHUiZhenRowNum() {
      return function (length) {
        if (length % 3 == 0) {
          return length / 3
        } else {
          return Math.floor(length / 3) + 1
        }
      }
    },
    shuZhongHUiZhenColNum() {
      return function (rowNum, length) {
        if (length % 3 == 0) {
          return 3
        } else {
          if (rowNum < Math.floor(length / 3) + 1) {
            return 3
          } else {
            return length % 3
          }
        }
      }
    },
    shiShiShouShuMapper() {
      return function (shouShuList) {
        let shiShiShouShu = ''
        if (shouShuList != null) {
          shouShuList.map((item) => {
            shiShiShouShu += item.mingCheng
            if (item.shouShuBW != null) {
              shiShiShouShu += '(' + item.shouShuBW + ')'
            }
            shiShiShouShu += ' '
          })
        }
        return shiShiShouShu
      }
    },
    qiYaZLMapper: {
      get() {
        return this.qiYaZL
      },
      set(val) {
        this.qiYaZL = val
      }
    },
    maZuiHZMapper: {
      get() {
        return this.maZuiHZ
      },
      set(val) {
        this.shouShuTZD.maZuiHZ = val ? '1' : '0'
        this.maZuiHZ = val
      }
    },
    shiFouJZMapper: {
      get() {
        return this.shiFouJZ
      },
      set(val) {
        this.shouShuTZD.shiFouJZ = val
        this.shiFouJZ = val
      }
    },
    riJianSSMapper: {
      get() {
        return this.riJianSS
      },
      set(val) {
        this.shouShuTZD.riJianSS = val ? '1' : '0'
        this.riJianSS = val
      }
    },
    xuYaoJSMapper: {
      get() {
        return this.xuYaoJS
      },
      set(val) {
        this.shouShuTZD.xuYaoJS = val ? '1' : '0'
        this.xuYaoJS = val
      }
    },
    xiaoZuTaiXuList() {
      return xiaoZuTaiXuList
    },
    formatDate() {
      return function (date, formatType) {
        if (!date) {
          return ''
        }
        return format(Date.parse(date), formatType)
      }
    },
    bingLiID() {
      return this.$route.params.id
    },
    zhuangTaiMC() {
      return function (zhuangTaiBZ) {
        switch (zhuangTaiBZ) {
          case '0':
            return '暂停'
          case '1':
            return '普通'
          case '2':
            return '锁定'
          case '3':
            return '医生已排班'
          case '4':
            return '护士已排班'
          case '5':
            return '已审核'
          case '9':
            return '删除'
          default:
            return '无'
        }
      }
    }
  },
  watch: {
    'shouShuTZD.maZuiHZ': {
      handler(val) {
        this.maZuiHZ = val == '1' ? true : false
      },
      deep: true
    },
    'shouShuTZD.xuYaoJS': {
      handler(val) {
        this.xuYaoJS = val == '1' ? true : false
      },
      deep: true
    },
    'shouShuTZD.riJianSS': {
      handler(val) {
        this.riJianSS = val == '1' ? true : false
      },
      deep: true
    },
    'shouShuTZD.shiFouJZ': {
      handler(val) {
        this.shiFouJZ = val
      },
      deep: true
    },
    'shouShuTZD.qiTaSXs': {
      handler(val) {
        if (this.shouShuTZD.qiTaSXs != null) {
          this.shouShuTZD.qiTaSXs.map((item) => {
            if (item.shuXingDM == '01') {
              this.qiYaZL = item.shuXingZhi == '1' ? true : false
            }
            if (item.shuXingDM == 'ZD') {
              this.zhongDaSS = item.shuXingZhi == '1' ? true : false
            }
          })
        } else {
          this.qiYaZL = false
          this.zhongDaSS = false
        }
      },
      deep: true
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      try {
        const res = await stopOperationInit()
        this.stopOperationData = res.data
        const res2 = await initShouShuTZD({
          leiBie: this.leiBie,
          zhuYuanID: this.patientInfo.zhuYuanID,
          bingLiID: this.bingLiID,
          tongZhiDanID: 0
        })
        this.initTZD = res2.data
        const res3 = await getListByZhuanKeID({
          zhuanKeID: this.zhuanKeID
        })
        this.xinJiShuXXMList = res3.data
        await this.refreshData()
      } catch (error) {
        console.log(error)
      }
    },
    async refreshData() {
      this.shouShuTZD = JSON.parse(JSON.stringify(shouShuTZD))
      this.zhiQingTYSSelection = {}
      console.log(this.shouShuTZD)
      const res1 = await initShouShuTZDList({
        bingLiID: this.bingLiID,
        leiBie: this.leiBie,
        zhuYuanID: this.patientInfo.zhuYuanID
      })
      if (res1.hasError === 0) {
        this.shouShuTZDList = res1.data.shouShuTZDList
      }
    },
    async handleAdd() {
      this.shouShuTZD = JSON.parse(JSON.stringify(shouShuTZD))
      this.zhiQingTYSSelection = {}
      this.shouShuTZD.shuZhongHZ = []
      this.saveDisabled = false
      this.qiYaZL = false
      this.zhongDaSS = false
    },
    // 新增手术通知单
    async handleSave() {
      let extra = {
        leiBie: this.leiBie,
        zhuYuanID: this.patientInfo.zhuYuanID,
        bingLiID: this.bingLiID,
        shouShuJB: '9',
        qiTaSXs: []
      }
      extra.qiTaSXs.push({
        tongZhiDanID: '0', //通知单ID
        shuXingDM: '01', //属性代码（01=气压治疗，ZD=重大手术，FJ=非计划再次手术，KY=科研标识）
        shuXingZhi: this.qiYaZL ? '1' : '0' //属性值（1=是，0=否）
      })
      extra.qiTaSXs.push({
        tongZhiDanID: '0', //通知单ID
        shuXingDM: 'ZD', //属性代码（01=气压治疗，ZD=重大手术，FJ=非计划再次手术，KY=科研标识）
        shuXingZhi: this.zhongDaSS ? '1' : '0' //属性值（1=是，0=否）
      })
      this.initTZD.shouShuShiList.map((item) => {
        if (item.daiMa == this.shouShuTZD.shouShuShiDM) {
          extra.shouShuShiMC = item.mingCheng
        }
      })
      this.shouShuTZD = { ...this.shouShuTZD, ...extra }
      let res = {}
      console.log(this.shouShuTZD.shouShuXM)
      if (this.shouShuTZD.tongZhiDID) {
        res = await updateShouShuTZD(this.shouShuTZD)
      } else {
        res = await addShouShuTZD(this.shouShuTZD)
        this.refreshData()
      }
      if (res.hasError === 0) {
        this.$message({
          message: '保存成功',
          type: 'success'
        })
      }
    },
    async handleDelete() {
      if (this.shouShuTZD.tongZhiDID != 0) {
        let res = await deleteSstzd({
          tongZhiDanID: this.shouShuTZD.tongZhiDID
        })
        this.$message({
          message: '删除成功',
          type: 'success'
        })
        await this.refreshData()
      } else {
        this.$message({
          message: '请先选择手术通知单',
          type: 'success'
        })
      }
    },
    // 点击左侧通知单
    async handleTZDInit(item) {
      console.log(item)
      const res = await initShouShuTZD({
        leiBie: this.leiBie,
        zhuYuanID: this.patientInfo.zhuYuanID,
        bingLiID: this.bingLiID,
        tongZhiDanID: item.tongZhiDID
      })
      this.shouShuXZList = []
      this.yiPaiHuPaiView = false
      this.initTZD = res.data
      this.shouShuTZD = res.data.shouShuTZD
      this.xinJiShuXXMList.map((item) => {
        if (item.mingCheng == this.shouShuTZD.xinJiShuXXM) {
          this.shouShuTZD.xinJiShuXXM = item.daiMa
        }
      })
      if (this.shouShuTZD.shenPiYJ != null) this.saveDisabled = true
      console.log(this.shouShuTZD)
    },
    async handleAddShuZhongHZ() {
      if (
        this.shouShuTZD.shuZhongHZ.length &&
        this.shouShuTZD.shuZhongHZ[this.shouShuTZD.shuZhongHZ.length - 1].xingMing == ''
      ) {
        return
      }
      this.shouShuTZD.shuZhongHZ.push({ xingMing: '' })
    },
    async selectYS(selectObj, index) {
      const res = await getShouShuRyk()
      this.zhuDaoYSList = res.data
      this.renYuanXM = ''
      this.renYuanXZDialog = true
      this.selectInput = selectObj
      this.selectSZYZIndex = index
    },
    // 打开拟施手术列表
    async selectShouShu() {
      if (this.shouShuTZD.zhuDaoYSID == null) {
        this.$message({
          message: '请先选择主刀医生',
          type: 'success'
        })
        return
      }
      if (this.shouShuXZList.length == 0) {
        let res = await getZhunRuSSXM({
          mingCheng: '',
          renYuanKuID: this.shouShuTZD.zhuDaoYSID
        })
        this.shouShuXZList = res.data
      }
      this.shouShuYXZSelectionList = JSON.parse(JSON.stringify(this.shouShuTZD.shouShuXM))
      this.niShiShouShuDialog = true

      let setMaxType = true
      this.maxV = -1
      this.$nextTick(() => {
        this.niShiShouShuListInit = false
        const span = document.createElement('span')
        span.className = 'zhuShouShuSpanContent'
        span.textContent = '主手术' // 设置文本（可选）

        // 添加到 div.test 下
        if (!this.$el.querySelector('.zhuShouShuSpan .cell .zhuShouShuSpanContent')) {
          this.$el.querySelector('.zhuShouShuSpan .cell').appendChild(span)
        }

        this.shouShuYXZSelectionList.map((item) => {
          let hasItem = this.shouShuXZList.find((childItem) => childItem.daiMa == item.shouShuDM)
          if (hasItem) {
            this.$refs.shouShuXZList.toggleRowSelection(hasItem)
          }
          if (item.zhuShouShu === '1') {
            this.$refs.shouShuYXZSelectedList.toggleRowSelection(item, true)
            this.maxKey = item.shouShuDM
            setMaxType = false
          } else {
            this.$refs.shouShuYXZSelectedList.toggleRowSelection(item, false)
          }
        })
        this.niShiShouShuListInit = true
        if (setMaxType) {
          this.setMaxShouShuJB()
        }
      })

      this.cacheShouShuXM = JSON.parse(JSON.stringify(this.shouShuTZD.shouShuXM))
    },
    handleShouShuListClose() {
      if (!this.shouShuXMSubmit) {
        this.shouShuTZD.shouShuXM = this.cacheShouShuXM
        console.log(this.cacheShouShuXM)
      }
      this.shouShuXMSubmit = false
      this.$refs.shouShuXZList.clearSelection()
    },
    setMaxShouShuJB() {
      let maxIndex = -1
      console.log('shouShuYXZSelectionList:', this.shouShuYXZSelectionList)
      for (let i = 0; i < this.shouShuYXZSelectionList.length; i++) {
        if (this.shouShuYXZSelectionList[i].shouShuJB > this.maxV) {
          this.maxV = this.shouShuYXZSelectionList[i].shouShuJB
          this.maxKey = this.shouShuYXZSelectionList[i].shouShuDM
          maxIndex = i
        }
      }
      if (maxIndex !== -1) {
        this.$refs.shouShuYXZSelectedList.clearSelection()
        this.$refs.shouShuYXZSelectedList.toggleRowSelection(this.shouShuYXZSelectionList[maxIndex])
      }
    },
    shouShuListChange(selection, row) {
      if (!this.niShiShouShuListInit) {
        return
      }

      const fData = this.shouShuYXZSelectionList.find((item) => item.shouShuDM === row.daiMa)
      let setMaxTypye = false
      console.log('setMaxTypye:', setMaxTypye)
      if (!fData) {
        //添加
        setMaxTypye = true
      } else {
        if (row.daiMa === this.maxKey) {
          setMaxTypye = true
          this.maxV = -1
        }
      }

      let array = this.shouShuYXZSelectionList.filter(
        (item) => !this.shouShuXZList.some((childItem) => childItem.daiMa == item.shouShuDM)
      )
      selection = selection.map((item) => {
        let hasItem = this.shouShuTZD.shouShuXM.find((aItem) => aItem.shouShuDM == item.daiMa)
        if (hasItem) {
          item.shouShuBW = hasItem.shouShuBW
        }
        return item
      })
      this.shouShuYXZSelectionList = selection.map((item) => {
        item.shouShuDM = item.daiMa
        item.shouShuMC = item.mingCheng
        item.icd = item.icddm

        return item
      })
      if (array.length > 0) {
        this.shouShuYXZSelectionList = array.concat(this.shouShuYXZSelectionList)
      }
      if (setMaxTypye) {
        this.setMaxShouShuJB()
      }
    },
    shouShuYXZListChange(selection, row) {
      this.$refs.shouShuYXZSelectedList.clearSelection()
      this.$refs.shouShuYXZSelectedList.toggleRowSelection(row, true)
      this.shouShuYXZSelectionList.forEach((item) => {
        if (item.shouShuDM !== row.shouShuDM) {
          item.zhuShouShu = '0'
        } else {
          item.zhuShouShu = '1'
        }
      })
      this.maxV = row.shouShuJB
      this.maxKey = row.shouShuDM
      console.log(this.shouShuYXZSelectionList)
    },
    //手术部位修改
    handleSelectShouShuBW() {
      if (this.selectMode == 'update') {
        this.updateContent = this.shouShuBWC + ' ' + this.shouShuBWD
      } else {
        this.shouShuYXZSelectionList.forEach((item) => {
          if (item.shouShuDM == this.shouShuBWSelection.shouShuDM) {
            this.$set(item, 'shouShuBW', this.shouShuBWC + ' ' + this.shouShuBWD)
          }
        })
        this.shouShuTZD.shouShuXM = JSON.parse(JSON.stringify(this.shouShuYXZSelectionList))
      }
      this.shouShuBWDialog = false
    },
    resetShouShuBWDialog() {
      this.shouShuBWD = ''
      this.shouShuBWC = ''
    },
    handleShouShuBWClick(row, col, cell, event) {
      if (row.shouShuBW) {
        let shouShuBWArr = row.shouShuBW.split(' ')
        this.shouShuBWC = shouShuBWArr[0]
        this.shouShuBWD = shouShuBWArr[1]
      } else {
        this.resetShouShuBWDialog()
      }
      if (col.label == '手术部位') {
        this.shouShuBWDialog = true
        this.shouShuBWSelection = row
      }
    },
    handleNiShiShouShuSubmit() {
      console.log('this.shouShuYXZSelectionList:', this.shouShuYXZSelectionList)
      for (const d of this.shouShuYXZSelectionList) {
        if (d.shouShuDM === this.maxKey) {
          d['zhuShouShu'] = '1'
        } else {
          d['zhuShouShu'] = '0'
        }
      }
      this.shouShuTZD.shouShuXM = this.shouShuYXZSelectionList
      this.niShiShouShuDialog = false
      this.shouShuXMSubmit = true
    },
    async queryZhuDaoYS() {
      let res = await getShouShuRyk({
        key: this.renYuanXM
      })
      this.zhuDaoYSList = res.data
    },
    queryShouShuList() {},
    // 选择人员
    async handleSelectRY(item) {
      let idList = [
        this.shouShuTZD.zhuDaoYSID,
        this.shouShuTZD.diYiZSID,
        this.shouShuTZD.diErZSID,
        this.shouShuTZD.diSanZSID,
        this.shouShuTZD.diSiZSID,
        this.shouShuTZD.taiShangZDID
      ]
      this.shouShuTZD.shuZhongHZ.map((item) => {
        idList.push(item.renYuanKuID)
      })
      if (idList.includes(item.renYuanKuID)) {
        this.$message.error('该医生已担任其他角色，请重新选择')
        return
      }
      if (this.selectMode == 'update') {
        this.updateContent = item.xingMing
        this.updateData = {
          zhuDaoZKID: item.xianZhuanKeID,
          xingMing: item.xingMing,
          renYuanKuID: item.renYuanKuID
        }
      } else {
        if (this.selectInput != 'shuZhongHZ')
          this.$set(this.shouShuTZD, this.selectInput, item.xingMing)
        this.$set(this.shouShuTZD, 'zhuDaoZKID', item.xianZhuanKeID)
        switch (this.selectInput) {
          case 'zhuDaoYSXM':
            this.$set(this.shouShuTZD, 'zhuDaoYSID', item.renYuanKuID)
            let res = await getZhunRuSSXM({
              mingCheng: '',
              renYuanKuID: this.shouShuTZD.zhuDaoYSID
            })
            this.shouShuXZList = res.data
            break
          case 'diYiZSXM':
            this.$set(this.shouShuTZD, 'diYiZSID', item.renYuanKuID)
            break
          case 'diErZSXM':
            this.$set(this.shouShuTZD, 'diErZSID', item.renYuanKuID)
            break
          case 'diSanZSXM':
            this.$set(this.shouShuTZD, 'diSanZSID', item.renYuanKuID)
            break
          case 'diSiZSXM':
            this.$set(this.shouShuTZD, 'diSiZSID', item.renYuanKuID)
            break
          case 'taiShangZDXM':
            this.$set(this.shouShuTZD, 'taiShangZDID', item.renYuanKuID)
            break
          case 'shuZhongHZ':
            this.$set(this.shouShuTZD.shuZhongHZ, this.selectSZYZIndex, {
              xingMing: item.xingMing,
              renYuanKuID: item.renYuanKuID,
              tongZhiDanID: this.shouShuTZD.tongZhiDID
            })
            break
        }
      }
      this.renYuanXZDialog = false
      this.selectMode = ''
      console.log(this.shouShuTZD)
    },
    getRowKeys(row) {
      return row.shouShuDM
    },
    getRowKeys1(row) {
      return row.daiMa
    },
    getRowKeys2(row) {
      return row.id
    },
    handleNiShiSSTagClose(tag) {
      this.shouShuTZD.shouShuXM = this.shouShuTZD.shouShuXM.filter(
        (item) => item.shouShuDM != tag.shouShuDM
      )
      if (tag.zhuShouShu == '1') {
        let max = { shouShuJB: -1 }
        this.shouShuTZD.shouShuXM.forEach((item) => {
          if (item.shouShuJB > max.shouShuJB) {
            max = item
          }
        })
        max.zhuShouShu = '1'
      }
    },
    async refreshWenZiMBData() {
      let res = await initKaiDanBzMb()
      this.wenZiMBList = res.data
    },
    async openWenZiMB() {
      await this.refreshWenZiMBData()
      this.clearWenZiMB()
      this.wenZiMBDialog = true
      this.wenZiMBCollapseKey++
    },
    async clearWenZiMB() {
      this.moBanData = {
        moBanMC: '',
        moBanNR: ''
      }
    },
    async saveWenZiMB() {
      let caoZuoZID = this.$store.state.user.userInfo.caoZuoZheID
      if (this.moBanData.id) {
        let res = await updateKaiDanBzMb({
          id: this.moBanData.id,
          moBanDM: 'kdbz', //模板代码，模板代码，kdbz=特殊要求
          moBanLX: this.moBanLXFilter(this.currentMB), //模板类型，1=个人，2=专科，3=全院
          moBanMC: this.moBanData.moBanMC, //模板名称
          // pinYin: '', //拼音
          // wuBi: '', //五笔
          moBanNR: this.moBanData.moBanNR, //模板内容
          zhuanKeID: this.zhuanKeID, //专科ID
          caoZuoZID: caoZuoZID //操作者ID
          // xiuGaiSJ: '' //修改时间
        })
        if (res.hasError === 0) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          await this.refreshWenZiMBData()
        } else {
          this.$message.error('保存失败')
        }
      } else {
        let res1 = await addKaiDanBzMb({
          moBanDM: 'kdbz', //模板代码，模板代码，kdbz=特殊要求
          moBanLX: this.moBanLXFilter(this.currentMB), //模板类型，1=个人，2=专科，3=全院
          moBanMC: this.moBanData.moBanMC, //模板名称
          // pinYin: '', //拼音
          // wuBi: '', //五笔
          moBanNR: this.moBanData.moBanNR, //模板内容
          zhuanKeID: this.zhuanKeID, //专科ID
          caoZuoZID: caoZuoZID //操作者ID
          // xiuGaiSJ: '' //修改时间
        })
        if (res1.hasError === 0) {
          this.$message({
            message: '新增成功',
            type: 'success'
          })
          await this.refreshWenZiMBData()
        } else {
          this.$message.error('新增失败')
        }
      }
    },
    onWZMBClick(item) {
      this.moBanData = JSON.parse(JSON.stringify(item))
    },
    wenZiMBUpdate(index) {
      if (index == '0') {
        this.currentMB = '个人模板'
      } else if (index == '1') {
        this.currentMB = '专科模板'
      } else if (index == '2') {
        this.currentMB = '全院模板'
      }
      delete this.moBanData.id
      console.log(this.moBanData)
    },
    handleUpdateContent() {
      this.selectMode = 'update'
      switch (this.xiuGaiXM) {
        case '主刀医生':
          this.selectYS('zhuDaoYSXM')
          console.log('主刀医生')
          break
        case '台上指导':
          this.selectYS('taiShangZDXM')
          console.log('台上指导')
          break
        case '参观者':
          console.log('参观者')
          break
        case '手术部位':
          this.shouShuBWDialog = true
          console.log('手术部位')
          break
        case '医疗小组':
          console.log('医疗小组')
          break
        case '麻醉会诊':
          console.log('麻醉会诊')
          break
        case '特殊要求':
          console.log('特殊要求')
          break
      }
    },
    async openYiPaiHuPai() {
      let res = await initOperationXiuGaiJL({
        bingLiID: this.bingLiID,
        leiBie: this.leiBie,
        tongZhiDanID: this.shouShuTZD.tongZhiDID,
        zhuYuanID: this.patientInfo.zhuYuanID
      })
      this.xiuGaiXMList = res.data.ziDuanList
      await this.refreshXiuGaiJL()
      this.yiPaiHuPaiView = true
    },
    shouShuDanXGJLListChange(selection) {
      this.shouShuDanXGJLSelectionList = selection
    },
    async deleteOperationXiuGaiJL() {
      if (this.shouShuDanXGJLSelectionList.length > 0) {
        this.shouShuDanXGJLSelectionList.map(async (item, index) => {
          await deleteOperationXiuGaiJL([
            {
              id: item.id,
              tongZhiDID: item.tongZhiDID
            }
          ])
          if (index == this.shouShuDanXGJLSelectionList.length - 1) {
            await this.refreshXiuGaiJL()
          }
        })
      }
    },
    async refreshXiuGaiJL() {
      let res = await getOperationXiuGaiJL({
        bingLiID: this.bingLiID,
        leiBie: this.leiBie,
        tongZhiDanID: this.shouShuTZD.tongZhiDID,
        zhuYuanID: this.bingLiID
      })
      this.shouShuDanXGJLList = res.data
      console.log(this.shouShuDanXGJLList)
    },
    //提交修改
    async handleChangeShouShuTZD() {
      let ziDuanMing = ''
      this.xiuGaiXMList.map((item) => {
        if (item.mingCheng == this.xiuGaiXM) {
          ziDuanMing = item.daiMa
        }
      })
      let data = {
        leiBie: this.leiBie,
        bingLiID: this.bingLiID,
        tongZhiDID: this.shouShuTZD.tongZhiDID,
        ziDuanMC: this.xiuGaiXM,
        ziDuanMing: ziDuanMing
      }
      let saveData = []
      switch (this.xiuGaiXM) {
        case '主刀医生':
          data.jiuMingCheng = this.shouShuTZD.zhuDaoYSXM
          data.xinMingCheng = this.updateData.xingMing
          data.jiuDaiMa = this.shouShuTZD.zhuDaoYSID
          data.xinDaiMa = this.updateData.renYuanKuID
          saveData.push(data)
          break
        case '台上指导':
          data.jiuMingCheng = this.shouShuTZD.taiShangZDXM
          data.xinMingCheng = this.updateData.xingMing
          data.jiuDaiMa = this.shouShuTZD.taiShangZDID
          data.xinDaiMa = this.updateData.renYuanKuID
          saveData.push(data)
          break
        case '参观者':
          data.jiuMingCheng = this.shouShuTZD.canGuanZhe
          data.xinMingCheng = this.updateContent
          saveData.push(data)
          break
        case '手术部位':
          this.shouShuTZD.shouShuXM.map((item) => {
            if (item.newShouShuBW) {
              data.shouShuDM = item.shouShuDM
              data.shouShuMC = item.shouShuMC
              data.jiuMingCheng = item.shouShuBW || ''
              data.xinMingCheng = item.newShouShuBW
              saveData.push(JSON.parse(JSON.stringify(data)))
            }
          })
          break
        case '医疗小组':
          let jiuZhuanKeXZ = this.initTZD.zhuanKeXZList.find((item) => {
            if (item.id.toString() == this.shouShuTZD.zhuanKeXZ) {
              return item
            }
          })
          let xinZhuanKeXZ = this.initTZD.zhuanKeXZList.find((item) => {
            if (item.id.toString() == this.updateContent) {
              return item
            }
          })
          data.jiuMingCheng = jiuZhuanKeXZ.mingCheng
          data.xinMingCheng = xinZhuanKeXZ.mingCheng
          data.jiuDaiMa = this.shouShuTZD.zhuanKeXZ
          data.xinDaiMa = this.updateContent
          saveData.push(data)
          break
        case '麻醉会诊':
          data.jiuMingCheng = this.shouShuTZD.maZuiHZ ? '否' : '是'
          data.xinMingCheng = this.updateContent ? '否' : '是'
          data.jiuDaiMa = this.shouShuTZD.maZuiHZ ? '0' : '1'
          data.xinDaiMa = this.updateContent ? '0' : '1'
          saveData.push(data)
          break
        case '特殊要求':
          data.jiuMingCheng = this.shouShuTZD.kaiDanBZ
          data.xinMingCheng = this.updateContent
          saveData.push(data)
          break
      }
      await saveOperationXiuGaiJL(saveData)
      await this.refreshXiuGaiJL()
    },

    xiuGaiXMChange() {
      this.updateContent = ''
    },
    async getBrHuaYanJg() {
      let res = await getBrHuaYanJg({
        bingLiID: this.bingLiID
      })
      this.huaYanJGList = res.data
    },
    async openShuQianTLDialog() {
      let res = await getWenShuListByWslx({
        bingLiID: this.bingLiID,
        wenShuLX: '231'
      })
      this.shuQianTLData = res.data
      this.shuQianTLDialog = true
    },
    async openzhiQingTYSDialog() {
      let res1 = await getWenShuListByWslx({
        bingLiID: this.bingLiID,
        wenShuLX: '234'
      })
      let res2 = await getWenShuListByWslx({
        bingLiID: this.bingLiID,
        wenShuLX: '26'
      })
      this.zhiQingTYSData = res1.data.concat(res2.data)
      this.zhiQingTYSDialog = true
    },
    shuQianTLSelectionChange(selection, row) {
      if (selection.length > 1) {
        // 如果尝试选择多个项目，则取消之前的所有选择并重新选择当前项
        this.$refs.shuQianTLTable.clearSelection() // 清除所有选择
        this.$refs.shuQianTLTable.toggleRowSelection(row, true) // 重新选择当前项
      }
      if (row) {
        this.shuQianTLSelectionCache = { shuQianTLWSID: row.id, shuQianTLWSMC: row.wenShuMC }
      } else {
        this.shuQianTLSelectionCache = {}
      }
    },
    updateShuQianTLData() {
      this.shouShuTZD = { ...this.shouShuTZD, ...this.shuQianTLSelectionCache }
      this.shuQianTLDialog = false
    },
    shuQianTLClose() {
      this.shuQianTLSelectionCache = {
        shuQianTLWSID: this.shouShuTZD.shuQianTLWSID,
        shuQianTLWSMC: this.shouShuTZD.shuQianTLWSMC
      }
    },
    zhiQingTYSSelectionChange(selection, row) {
      if (selection.length > 1) {
        // 如果尝试选择多个项目，则取消之前的所有选择并重新选择当前项
        this.$refs.zhiQingTYSTable.clearSelection() // 清除所有选择
        this.$refs.zhiQingTYSTable.toggleRowSelection(row, true) // 重新选择当前项
      }
      if (row) {
        this.zhiQingTYSSelectionCache = { zhiQingTYSWSID: row.id, zhiQingTYSWSMC: row.wenShuMC }
      } else {
        this.zhiQingTYSSelectionCache = {}
      }
    },
    updateZhiQingTYSData() {
      this.shouShuTZD = { ...this.shouShuTZD, ...this.zhiQingTYSSelectionCache }
      this.zhiQingTYSDialog = false
    },
    zhiQingTYSClose() {
      this.zhiQingTYSSelectionCache = {
        zhiQingTYSWSID: this.shouShuTZD.zhiQingTYSWSID,
        zhiQingTYSWSMC: this.shouShuTZD.zhiQingTYSWSMC
      }
    },
    cancelShuQianTL() {
      this.shouShuTZD.shuQianTLWSID = 0
      this.shouShuTZD.shuQianTLWSMC = ''
    },
    cancelZhiQingTYS() {
      this.shouShuTZD.zhiQingTYSWSID = 0
      this.shouShuTZD.zhiQingTYSWSMC = ''
    },
    wenZiMBItemClick(item) {
      console.log(item)
    },

    async shouShuShiChange(val) {
      let res = await getShouShuJianList({
        leiBie: this.leiBie,
        shouShuShiDM: val
      })
      this.shouShuJianList = res.data
      console.log(val)
    },
    handleBiHuan() {
      if (this.shouShuTZD.biHuanDZ) {
        this.visible = false
        window.open(this.shouShuTZD.biHuanDZ)
      }
    },
    handleShenQingTT() {
      this.shouShuTTDialog = true
      this.visible = false
    },
    async handleShouShuTT() {
      let res = await stopOperationByDoctor({
        bingLiID: this.bingLiID,
        tongZhiDID: this.shouShuTZD.tongZhiDID,
        tingTaiSQRLX: this.stopOperationData.type,
        tingTaiYYDM: this.stopOperationData.reason,
        beiZhu: this.stopOperationData.tingTaiBZ
      })
      if (res.hasError === 0) {
        this.$message({
          message: '停台成功',
          type: 'success'
        })
        this.shouShuTTDialog = false
        this.refreshData()
      }
    },
    handleShenQingSSHC() {
      if (this.shouShuTZD.shouShuHCURL) {
        window.open(this.shouShuTZD.shouShuHCURL)
        this.visible = false
      }
    },
    writeIntoWenZiMB() {
      if (this.shouShuTZD.kaiDanBZ == null) {
        this.shouShuTZD.kaiDanBZ = ''
      }
      this.shouShuTZD.kaiDanBZ += this.moBanData.moBanNR
      this.wenZiMBDialog = false
    },
    multipleSelectShouShuBW(val) {
      let newShouShuBWArr = val.newShouShuBW
      if (newShouShuBWArr.length < 2) {
        return
      }
      let ceFirstIndex = newShouShuBWArr.findIndex((item) => {
        return item.includes('侧')
      })

      let ceLastIndex = newShouShuBWArr.findLastIndex((item) => {
        return item.includes('侧')
      })
      let duanFirstIndex = newShouShuBWArr.findIndex((item) => {
        return item.includes('段')
      })
      let duanLastIndex = newShouShuBWArr.findLastIndex((item) => {
        return item.includes('段')
      })
      if (ceFirstIndex != ceLastIndex) {
        newShouShuBWArr.splice(ceFirstIndex, 1)
      }
      if (duanFirstIndex != duanLastIndex) {
        newShouShuBWArr.splice(duanFirstIndex, 1)
      }
      if (newShouShuBWArr.length == 2) {
        if (newShouShuBWArr[0].includes('段')) {
          let oldData = newShouShuBWArr[1]
          newShouShuBWArr[1] = newShouShuBWArr[0]
          newShouShuBWArr[0] = oldData
        }
      }
      console.log(val)
    }
  }
}
</script>
<style>
.right-header-poperover {
  padding: 8px;
}
</style>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  // .el-input {
  //   width: auto;
  // }
}
.Dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
    .renYuanXMInput {
      width: 43%;
      margin-right: 10px;
      .el-input__inner {
        padding: 0 6px;
        height: 24px;
        font-size: 13px;
      }
    }
    .renYuanList {
      margin-top: 20px;
      width: 100%;
      thead {
        background-color: #eaf0f9;
      }
      td {
        border: 1px solid #dcdfe6;
        border-collapse: collapse; /* 移除表格内边框间的间隙 */
        padding: 6px;
        font-size: 13px;
      }
      tbody {
        tr:nth-child(even) {
          background-color: #eaf0f9;
        }
        tr:nth-child(odd) {
          background-color: #f6f6f6;
        }
      }
    }
    .bottom-btn {
      margin-top: 30px;
      flex-direction: row-reverse;
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .shouShuHeader {
    padding: 6px 10px;
    border: 1px solid #dcdfe6;
  }
  .shouShuTable {
    padding: 12px;
    border: 1px solid #dcdfe6;
    border-top: none;
    position: relative;
    .pagination {
      position: absolute;
      bottom: 10px;
      right: 10px;
    }
    ::v-deep tbody .el-table__row:nth-child(even) {
      background: #f6f6f6;
    }
    ::v-deep .el-table__header-wrapper {
      tr {
        background: rgba(59, 118, 239, 0.6) !important;
      }
    }
  }
  .shouShuXZList {
    height: 400px;
    tr {
      height: 50px;
    }
    ::v-deep th {
      .el-checkbox {
        display: none;
      }
    }
  }
  .yiXuanZeShouShuList {
    height: 242px;
    ::v-deep th {
      .el-checkbox {
        display: none;
      }
    }
  }
  .shouShuXZHeader {
    justify-content: space-between;
  }
  .yiXuanZeSSHeader {
    margin-top: 10px;
  }
}
.shouShuBWDialog {
  ::v-deep th {
    padding: 6px;
  }
  ::v-deep td {
    padding: 6px;
  }
  ::v-deep tr:nth-child(even) {
    background-color: #eff3fb !important;
  }
  ::v-deep tr:nth-child(odd) {
    background-color: #f6f6f6 !important;
  }
}
.wenZiMBDialog {
  ::v-deep .el-collapse-item__header::before {
    display: none;
  }
  ::v-deep .el-collapse-item__header {
    font-size: 12px;
    padding: 6px;
  }
  ::v-deep .el-collapse-item__content {
    padding: 0;
    .model-item-child {
      padding: 3px 10px;
      cursor: pointer;
    }
    .model-item-child:hover {
      background-color: #6787cc;
      color: #fff;
    }
  }
  ::v-deep .el-collapse-item__wrap {
    border: none;
  }
  .wenZiMB-left {
    height: 445px;
    width: 25%;
    .wenZiMB-left-header {
      padding: 6px;
      background-color: #eaf0f9;
      height: 40px;
      font-weight: 600;
      line-height: 30px;
    }
    .wenZiMB-left-content {
      height: 420px;
      background-color: #fafbfc;
      .wenZiMB-left-content-item {
        padding: 6px;
        font-size: 14px;
        border: 1px solid #ecedf2;
      }
      .wenZiMB-left-content-item:hover {
        background-color: #6787cc;
        color: #fff;
        cursor: pointer;
      }
    }
  }
  .wenZiMB-right {
    height: 445px;
    width: 75%;
    padding: 0px 10px;
    .wenZiMB-right-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #eaf0f9;
      height: 40px;
      padding: 10px;
      .el-button {
        padding: 5px 8px;
      }
    }
    .wenZiMB-right-content {
      padding: 10px;
      border: 1px solid #ecedf2;
      height: 420px;
      table {
        width: 100%;
        height: 100%;
        td {
          border: 1px solid #dcdfe6;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
        }
        .table-title {
          padding: 10px;
          text-align: right;
          width: 20%;
          background-color: #eaf0f9;
        }
        .table-content {
          width: 80%;
          padding: 6px;
          .el-textarea {
            height: 100%;
            ::v-deep .el-textarea__inner {
              height: 100%;
            }
          }
        }
      }
    }
  }
}
.shuQianTLDialog,
.zhiQingTYSDialog {
  .el-table {
    height: 300px;
    overflow-y: scroll;
  }
  ::v-deep th {
    .el-checkbox {
      display: none;
    }
  }
}
.shouShuTTDialog {
  table {
    width: 100%;
    height: 100%;
    td {
      border: 1px solid #dcdfe6;
      border-collapse: collapse; /* 移除表格内边框间的间隙 */
    }
    .table-title {
      padding: 10px;
      text-align: right;
      width: 32%;
      background-color: #eaf0f9;
    }
    .table-content {
      width: 68%;
      padding: 6px;
      .el-textarea {
        height: 100%;
        ::v-deep .el-textarea__inner {
          min-height: 150px !important;
        }
      }
    }
  }
}
.text-center {
  text-align: center;
  .el-tag {
    font-size: 14px;
  }
}
.title {
  font-weight: 600;
  border-left: 4px solid #356ac5;
  padding-left: 8px;
  text-align: left;
}

.popover {
  .popover-item {
    padding: 3px 0;
    cursor: pointer;
  }
  .popover-item:hover {
    background-color: #dedede;
  }
}
.surgical-notice-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;
  .left-aside {
    width: 300px !important;
    font-size: 14px;
  }
  .right-aside {
    width: 252px !important;
  }
  .el-main {
    padding: 0 0 0 6px !important;
  }
  .left {
    background-color: #eff3fb;
    border-radius: 4px;
    padding: 8px;
    height: 100%;
    .left-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;
      height: 4%;
      ::v-deep .el-button {
        padding: 6px 10px;
        background-color: #3b76ef;
      }
    }
    .surgical-list::-webkit-scrollbar {
      display: none;
    }
    .surgical-list {
      height: 94%;
      overflow-y: scroll;
      border: 1px solid #dcdfe6;
      background-color: #eaf0f9;
      border-radius: 4px;
      padding: 7px;
      table {
        width: 100%;
        margin-bottom: 10px;
        td {
          border: 1.5px solid #dcdfe6;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          border-right: none;
        }
        tbody {
          border: 1px solid #3b76ef;
        }
        .table-title {
          color: #fff;
          background-color: rgba($color: #3b76ef, $alpha: 0.6);
          padding: 10px 13px;
          text-align: right;
        }
        .last-td {
          border-bottom: none;
          border-radius: 0 0 4px 4px;
        }
      }
      .table-header {
        text-align: center;
        background-color: #6787cc;
        color: #fff;
        padding: 12px 20px;
        border-radius: 4px 4px 0 0;
      }
    }
  }
  .right {
    background-color: #eff3fb;
    border-radius: 4px;
    padding: 5px;
    height: 100%;
    .one-block {
      display: flex;
      border: 2px solid #dee1e8;
      padding: 12px;
      width: 100%;
      align-items: center;
    }
    .two-block {
      border: 2px solid #dee1e8;
      margin: 10px 0;
    }
    .three-block {
      border: 2px solid #dee1e8;
      padding: 10px;
      height: 420px;
      .three-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
    .right-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 5%;
      .button-group {
        display: flex;
        align-items: center;
        ::v-deep .el-popover__reference {
          background-color: #eff3fb;
          border: none;
        }
        .el-icon-more {
          transform: rotateZ(90deg);
          color: #356ac5;
          font-size: 18px;
        }
      }
    }
    .right-content {
      .niShiShouShuList {
        height: 100px;
        border: 1px solid #000;
        background-color: rgba(59, 118, 239, 0.1);
        border-radius: 4px;
        padding: 6px;
        .el-tag {
          border: 1px solid rgba($color: #155bd4, $alpha: 0.45) !important;
          margin: 2px;
        }
      }
      font-size: 12px;
      padding: 16px 8px;
      height: 95%;
      .el-main::-webkit-scrollbar {
        display: none;
      }
      .el-main {
        padding: 0px !important;
        .right-table {
          text-align: left;
          height: 100%;
          width: 100%;
          td {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 6px;
          }
          .right-table-title {
            text-align: right;
            background-color: #eaf0f9;
            width: 9%;
            height: 100%;
          }
          .right-table-content {
            background-color: #ffffff;
            width: 20%;
            .el-checkbox {
              margin-right: 8px;
            }
            .niShiShouShuSJ {
              width: 100%;
              ::v-deep input {
                width: 100%;
              }
            }
          }
          .right-table-footer-tip {
            padding: 0 0 10px 0;
            background-color: #eaf0f9;
          }
          .select-input {
            ::v-deep .el-input__inner {
              background-color: #e4ecfb;
              height: 24px;
            }
          }
        }
      }
      .el-aside {
        margin: 0 0 0 6px !important;
        padding: 6px !important;
        border: 1px solid #dcdfe6;
        .right-aside-header {
          text-align: right;
          ::v-deep .el-button {
            background-color: #a66dd4;
            color: #fff;
          }
        }
        .right-aside-table {
          width: 100%;
          margin-top: 20px;
          td {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 2px 6px;
          }
          thead {
            background-color: #eaf0f9;
          }
          tbody {
            td {
              background-color: #f6f6f6;
            }
          }
        }
      }
    }
  }
}
</style>
