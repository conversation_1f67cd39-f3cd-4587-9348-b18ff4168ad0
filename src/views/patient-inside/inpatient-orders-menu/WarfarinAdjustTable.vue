<template>
  <div class="container">
    <div class="content-top">
      <div class="top-left">
        <div class="content-header">
          <div class="title">出凝血INR内容(最近7日)</div>
        </div>
        <div class="table">
          <el-table border stripe size="mini" :data="tableData">
            <el-table-column type="index" width="50"></el-table-column>
            <el-table-column prop="bingAnHao" width="120" label="病案号"></el-table-column>
            <el-table-column prop="huaYanJGXMMC" width="250" label="名称"></el-table-column>
            <el-table-column width="250" label="英文姓名">INR</el-table-column>
            <el-table-column prop="huaYanJG" width="140" label="结果"></el-table-column>
            <el-table-column prop="ceShiSJ" width="160" label="检查时间"></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="top-right">
        <div class="content-header">
          <div class="title">出凝血INR内容(最近7日)</div>
        </div>
        <div class="chart">
          <ve-line :data="chartData" :settings="chartSettings" height="100%" width="100%"></ve-line>
        </div>
      </div>
    </div>
    <div class="content-bottom">
      <div class="content-header">
        <div class="title">出凝血INR内容(最近7日)</div>
      </div>
      <div class="table">
        <el-table border stripe size="mini" :data="tableData2">
          <el-table-column type="index" width="50"></el-table-column>
          <el-table-column prop="bingAnHao" width="140" label="病案号"></el-table-column>
          <el-table-column prop="kaiShiSJ" width="160" label="计划开始时间"></el-table-column>
          <el-table-column prop="jieShuSJ" width="160" label="结束时间"></el-table-column>
          <el-table-column prop="mingCheng" label="药品名称"></el-table-column>
          <el-table-column prop="yiCiYL" width="100" label="一次用量"></el-table-column>
          <el-table-column prop="jiLiangDW" width="100" label="剂量单位"></el-table-column>
          <el-table-column prop="zhiXingPL" width="100" label="执行频率"></el-table-column>
          <el-table-column prop="zhiXingFF" width="100" label="执行方法"></el-table-column>
          <el-table-column prop="geiYaoSJ" width="160" label="给药时间"></el-table-column>
          <el-table-column width="100" label="操作" align="center">
            <template #default="scope">
              <el-button type="text" size="medium" @click="gotoBh(scope.row)">执行记录</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getHuaFaLingTzb,
  getKangFuZLJL,
  getKangFuZLYZ,
  getUnFinishedExamDataAll
} from '@/api/inpatient-order'
import VeLine from 'v-charts/lib/line.common'
import Vue from 'vue'
Vue.component(VeLine.name, VeLine)

export default {
  name: 'WarfarinAdjustTable',
  data() {
    return {
      tableData: [],
      tableData2: [],
      chartData: {
        columns: ['时间', '结果'],
        rows: []
      },
      chartSettings: {}
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const res = await getHuaFaLingTzb({
        bingLiID: this.bingLiID
      })
      this.chartData.rows = []
      for (const d of res.data.huaYanJG) {
        d['bingAnHao'] = res.data.inPatientVo.bingAnHao
        const chart = {
          时间: d.ceShiSJ,
          结果: d.huaYanJG
        }
        this.chartData.rows.push(chart)
      }
      for (const d of res.data.ylZyypyzVos) {
        d['bingAnHao'] = res.data.inPatientVo.bingAnHao
      }
      this.tableData = res.data.huaYanJG
      this.tableData2 = res.data.ylZyypyzVos
    },
    async gotoBh(row) {
      const url = `${row.yaoPinBHDZ}zhuYuanID=${row.zhuYuanID}&yiZhuZH=${row.zuHao}&pageNum=1&pageSize=10`
      const newWindow = window.open(url)
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}
.content-top {
  display: flex;
  background-color: #eaf0f9;
  padding: 14px;
  height: 400px;
  .top-left {
    max-width: 1000px;
  }
  .top-right {
    margin-left: 10px;
    flex: 1;
    border: #dadee5 2px solid;
    padding: 14px;
    display: flex;
    flex-direction: column;
  }
}
.content-bottom {
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-height: 162px;

  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
  .title {
    position: relative;
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 9px;
  }
  .title::before {
    position: absolute;
    left: -9px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }
}
.table {
  max-width: 1400px;
  flex: 1;
  overflow: auto;
  //width: 702px;
  //max-width: 800px;
  ::v-deep .el-tag {
    border-color: #356ac5;
  }
}
.chart {
  flex: 1;
  background-color: #ffffff;
  max-width: 611px;
  max-height: 312px;
}
::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
</style>
