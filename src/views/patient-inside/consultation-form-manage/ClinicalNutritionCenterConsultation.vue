<template>
  <div class="clinical-nutrition-center-view">
    <div class="title">请选择发起营养会诊：</div>
    <div>
      <el-radio v-model="type" label="1">
        发起肠外营养会诊（预计经口或管饲摄入不能满足机体50%热卡和蛋白需求量达3-7天；肠缺血、肠梗阻、穿孔等肠功能障碍）
      </el-radio>
    </div>
    <div>
      <el-radio v-model="type" label="2">发起营养科会诊（特医食品和肠内营养制剂）</el-radio>
    </div>
    <el-button type="primary" size="medium" @click="openDetails">确定</el-button>
  </div>
</template>
<script>
import { EventBus } from '@/utils/event-bus'
import { loadView } from '@/store/modules/permission'
export default {
  data() {
    return {
      type: 0
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    }
  },
  methods: {
    async openDetails() {
      if (this.type != 0) {
        EventBus.$emit(`sidebarClick_${this.bingLiID}`, {
          name: 'ParenteralNutritionConsultation',
          component: loadView(
            'patient-inside/consultation-form-manage/ParenteralNutritionConsultation'
          ),
          // component: () => import('./ParenteralNutritionConsultation'),
          meta: {
            title: this.type == '1' ? '发起肠外营养会诊' : '发起营养会诊'
          }
        })
        await this.$store.dispatch(
          'patient/setSideBarRoute',
          'ParenteralNutritionConsultation?type=' + this.type
        )
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.clinical-nutrition-center-view {
  padding: 30px 40px;
  .title {
    font-size: 16px;
    margin-bottom: 6px;
    font-weight: 600;
  }
  .el-button {
    margin: 10px 0 0;
  }
}
</style>
