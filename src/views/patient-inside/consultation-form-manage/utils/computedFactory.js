export function mapComputedTSKJ(keys) {
  const computed = {}
  keys.forEach((key) => {
    computed[key] = {
      get() {
        return this.huiZhenDanData.ylTskjywFzjcVo?.[key] || ''
      },
      set(value) {
        if (!this.huiZhenDanData.ylTskjywFzjcVo) {
          this.$set(this.huiZhenDanData, 'ylTskjywFzjcVo', { key: value })
        } else {
          this.$set(this.huiZhenDanData.ylTskjywFzjcVo, key, value)
        }
      }
    }
  })
  return computed
}

export function mapComputedSLKJ(keys) {
  const computed = {}
  keys.forEach((key) => {
    computed[key] = {
      get() {
        return this.huiZhenDanData.ylSlkjywFzjcVo?.[key] || ''
      },
      set(value) {
        if (!this.huiZhenDanData.ylSlkjywFzjcVo) {
          this.$set(this.huiZhenDanData, 'ylSlkjywFzjcVo', { key: value })
        } else {
          this.$set(this.huiZhenDanData.ylSlkjywFzjcVo, key, value)
        }
      }
    }
  })
  return computed
}
