<template>
  <div class="clinical-pharmacy-view">
    <el-container>
      <el-aside class="left-aside" width="320px !important">
        <div style="background-color: #eff3fb; height: 100%; padding: 10px">
          <el-descriptions
            class="information"
            title="三联抗菌药物会诊单"
            :column="3"
            size="medium"
            border
          >
            <template slot="extra">
              <el-button type="primary" size="mini" @click="clear">新增</el-button>
            </template>
          </el-descriptions>
          <el-table
            :data="huiZhenDanList"
            border
            style="width: 100%; margin-top: 20px"
            @row-click="huiZhenDanListClick"
          >
            <el-table-column prop="shenQingSJ" label="日期" width="160">
              <template #default="{ row }">
                <span>{{ formatDate(row.shenQingSJ, 'yyyy-MM-dd HH:mm:ss') }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="shenQingYHXM" label="申请医师姓名"></el-table-column>
          </el-table>
        </div>
      </el-aside>
      <el-main class="right-main">
        <div style="background-color: #eff3fb; height: 100%">
          <div class="box">
            <div style="height: 6%">
              <el-descriptions
                class="information"
                title="三联抗菌药物会诊单"
                :column="3"
                size="medium"
                border
              >
                <template slot="extra">
                  <el-button type="primary" size="mini" @click="saveHuiZhenDan">暂存</el-button>
                  <el-button
                    type="primary"
                    :disabled="huiZhenDanData.zhuangTaiBZ != '0'"
                    size="mini"
                    @click="jiJiaoSQ"
                  >
                    提交申请
                  </el-button>
                  <el-button
                    type="primary"
                    :disabled="huiZhenDanData.zhuangTaiBZ != '1'"
                    size="mini"
                    @click="cheXiaoSQ"
                  >
                    撤销申请
                  </el-button>
                  <el-button type="primary" size="mini">打印</el-button>
                </template>
              </el-descriptions>
            </div>
            <div class="content-box">
              <div
                style="
                  font-weight: 600;
                  height: 5%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                "
              >
                温州医科大学附属第一医院三联抗菌药物会诊单
              </div>
              <div class="content">
                <el-descriptions class="information" :column="4" size="mini" border>
                  <el-descriptions-item>
                    <template slot="label">当前状态:</template>
                    <el-tag>新增</el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">紧急状态:</template>
                    <el-checkbox v-model="jinJiZT" disabled></el-checkbox>
                    <span
                      class="underline-content"
                      style="margin-left: 5px"
                      @click="jinJiQKDialog = true"
                    >
                      紧急情况
                    </span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">选择会诊医师:</template>
                    <div @dblclick="selectJinRiHZZJ">
                      <el-input v-model="huiZhenDanData.huiZhenYSXM"></el-input>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">申请医师:</template>
                    王华晓
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">申请时间:</template>
                    2024-07-17 16:11
                  </el-descriptions-item>
                  <el-descriptions-item
                    label-class-name="hide"
                    content-class-name="hide"
                  ></el-descriptions-item>
                  <el-descriptions-item
                    label-class-name="hide"
                    content-class-name="hide"
                  ></el-descriptions-item>
                  <el-descriptions-item
                    label-class-name="hide"
                    content-class-name="hide"
                  ></el-descriptions-item>
                </el-descriptions>
                <el-descriptions
                  class="patient-basic-information information"
                  title="病人基本信息"
                  :column="4"
                  size="medium"
                  border
                >
                  <el-descriptions-item>
                    <template slot="label">姓名:</template>
                    {{ initData.inPatientVo?.bingRenXM }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">性别:</template>
                    {{
                      initData.inPatientVo?.xingBie == '1'
                        ? '男'
                        : initData.inPatientVo?.xingBie == '0'
                        ? '女'
                        : ''
                    }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">年龄:</template>
                    {{ initData.nianLing }}岁
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">病区-床位:</template>
                    4病区-033床
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">病案号:</template>
                    {{ initData.inPatientVo?.bingAnHao }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">体重:</template>
                    <el-input v-model="huiZhenDanData.bingRenTZ"></el-input>
                  </el-descriptions-item>
                  <el-descriptions-item :span="2">
                    <template slot="label">临床诊断:</template>
                    {{ initData.linChuangZDMC }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">体温(最近三次):</template>
                    <div class="flex">
                      <el-input v-model="huiZhenDanData.tiWen"></el-input>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">血压:</template>
                    <el-input v-model="huiZhenDanData.xueYa"></el-input>
                  </el-descriptions-item>
                  <el-descriptions-item
                    label-class-name="hide"
                    content-class-name="hide"
                  ></el-descriptions-item>
                  <el-descriptions-item
                    label-class-name="hide"
                    content-class-name="hide"
                  ></el-descriptions-item>
                </el-descriptions>
                <el-descriptions
                  class="patient-basic-information information"
                  title="病诊断依据(与感染相关)"
                  :column="4"
                  size="medium"
                  border
                >
                  <el-descriptions-item
                    :span="4"
                    :label-style="{ width: '12.5%' }"
                    :content-style="{ width: '87.5%' }"
                  >
                    <template slot="label">1.主诉:</template>
                    <el-input v-model="huiZhenDanData.zhuSu"></el-input>
                  </el-descriptions-item>
                  <el-descriptions-item
                    :span="4"
                    :label-style="{ width: '12.5%' }"
                    :content-style="{ width: '87.5%' }"
                  >
                    <template slot="label">2.申请理由:</template>
                    <el-checkbox-group v-model="shenQingLYListMapper">
                      <el-checkbox
                        v-for="shenQingLY in shenQingLYS"
                        :key="shenQingLY"
                        :label="shenQingLY"
                      ></el-checkbox>
                    </el-checkbox-group>
                  </el-descriptions-item>
                  <el-descriptions-item
                    :span="4"
                    :label-style="{ width: '12.5%' }"
                    :content-style="{ width: '87.5%' }"
                  >
                    <template slot="label">3.辅助检查:</template>
                    <div style="padding: 8px">
                      <el-descriptions
                        class="patient-basic-information information"
                        :column="4"
                        size="medium"
                        border
                      >
                        <el-descriptions-item
                          :label-style="{ width: '12.5%' }"
                          :content-style="{ width: '87.5%', padding: '0 !important' }"
                          label="实验室检查"
                          :span="4"
                        >
                          <el-descriptions
                            class="patient-basic-information"
                            :column="3"
                            size="medium"
                            border
                          >
                            <el-descriptions-item>
                              <template slot="label">白细胞计数:</template>
                              <el-input v-model="baiXiBJS"></el-input>
                            </el-descriptions-item>
                            <el-descriptions-item>
                              <template slot="label">中性细胞粒百分数:</template>
                              <el-input v-model="zhongXingLiBFS"></el-input>
                            </el-descriptions-item>
                            <el-descriptions-item>
                              <template slot="label">C反应蛋白:</template>
                              <el-input v-model="cfanYingDB"></el-input>
                            </el-descriptions-item>
                            <el-descriptions-item>
                              <template slot="label">降钙素原:</template>
                              <el-input v-model="jiangGaiSY"></el-input>
                            </el-descriptions-item>
                            <el-descriptions-item>
                              <template slot="label">丙氨酸氨基转移酶:</template>
                              <el-input v-model="bingAnJSZYM"></el-input>
                            </el-descriptions-item>
                            <el-descriptions-item>
                              <template slot="label">肌酐:</template>
                              <el-input v-model="jiGan"></el-input>
                            </el-descriptions-item>
                            <el-descriptions-item>
                              <template slot="label">结核分枝杆菌复合群DNA:</template>
                              <el-input v-model="jieHeFZGJFHQDNA"></el-input>
                            </el-descriptions-item>
                            <el-descriptions-item
                              label-class-name="hide"
                              content-class-name="hide"
                            ></el-descriptions-item>
                            <el-descriptions-item
                              label-class-name="hide"
                              content-class-name="hide"
                            ></el-descriptions-item>
                          </el-descriptions>
                        </el-descriptions-item>
                        <el-descriptions-item
                          :label-style="{ width: '12.5%' }"
                          :content-style="{ width: '87.5%', padding: '0 !important' }"
                          label="病理学检查"
                          :span="4"
                          content-class-name="bingLiXueJC"
                        >
                          <el-descriptions
                            class="patient-basic-information"
                            :column="2"
                            title="微生物送检"
                            size="medium"
                            border
                          >
                            <el-descriptions-item
                              :label-style="{ width: '12.5%' }"
                              :content-style="{ width: '87.5%' }"
                              :span="2"
                            >
                              <template slot="label">是否送检:</template>
                              <el-radio v-model="huiZhenDanData.shiFouWSWSJ" label="1">
                                已送检
                              </el-radio>
                              <el-radio v-model="huiZhenDanData.shiFouWSWSJ" label="0">
                                未送检
                              </el-radio>
                            </el-descriptions-item>
                            <el-descriptions-item
                              :label-style="{ width: '12.5%' }"
                              :content-style="{ width: '87.5%' }"
                              :span="2"
                            >
                              <template slot="label">标本类型(可多选):</template>
                              <el-checkbox-group v-model="biaoBenLXListMapper">
                                <el-checkbox
                                  v-for="biaoBenLX in biaoBenLXS"
                                  :key="biaoBenLX"
                                  :label="biaoBenLX"
                                ></el-checkbox>
                                <el-input
                                  v-model="biaoBenLXQTMapper"
                                  :disabled="!biaoBenLXListMapper.includes('其他培养')"
                                  class="qiTaPeiYangInput"
                                ></el-input>
                              </el-checkbox-group>
                              <!-- <span style="margin-left: 30px">
                                  <el-checkbox v-model="qiTaPY">其他培养</el-checkbox>
                                  <el-input
                                    v-model="biaoBenLXQTMapper"
                                    :disabled="!qiTaPY"
                                    class="qiTaPeiYangInput"
                                  ></el-input>
                                </span> -->
                            </el-descriptions-item>
                          </el-descriptions>
                          <el-descriptions
                            class="patient-basic-information"
                            :column="3"
                            title="微生物送检结果"
                            size="medium"
                            border
                          >
                            <el-descriptions-item>
                              <template slot="label">送检结果:</template>
                              <el-radio v-model="youWuWSWSJJG" label="1" @change="youWuSJJGChange">
                                有
                              </el-radio>
                              <el-radio v-model="youWuWSWSJJG" label="0">无</el-radio>
                            </el-descriptions-item>
                            <el-descriptions-item
                              label-class-name="hide1"
                              content-class-name="hide1"
                            ></el-descriptions-item>
                            <el-descriptions-item
                              label-class-name="hide1"
                              content-class-name="hide1"
                            ></el-descriptions-item>
                            <el-descriptions-item
                              v-for="item in huiZhenDanData.ylSlkjywWswsjjgVos"
                              :key="item.xuHao"
                              span="3"
                              :label="'标本' + item.xuHao + ':'"
                            >
                              <el-descriptions :column="3" size="medium" border>
                                <el-descriptions-item>
                                  <template slot="label">标本:</template>
                                  {{ item.bingYuanXueYBBH }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                  <template slot="label">标本类型:</template>
                                  {{ item.biaoBenLXMC }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                  <template slot="label">结果:</template>
                                  {{ item.bingYuanXueJG }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                  <template slot="label">药敏:</template>
                                  {{ item.bingYuanXueYM }}
                                </el-descriptions-item>
                              </el-descriptions>
                            </el-descriptions-item>
                          </el-descriptions>
                          <el-descriptions
                            class="patient-basic-information"
                            :column="2"
                            title="影像学检查"
                            size="medium"
                            border
                          >
                            <el-descriptions-item>
                              <template slot="label">
                                <el-button
                                  type="primary"
                                  size="mini"
                                  @click="openYingXiangXueJCDialog('CR')"
                                >
                                  选择X线
                                </el-button>
                              </template>
                              <el-checkbox
                                v-model="xuanZeXX"
                                label="X线"
                                @change="yingXiangXueJCChange('puFang')"
                              ></el-checkbox>
                            </el-descriptions-item>

                            <el-descriptions-item :content-style="{ width: '35%' }">
                              <template slot="label">结果:</template>
                              <el-input v-model="puFang"></el-input>
                            </el-descriptions-item>
                            <el-descriptions-item>
                              <template slot="label">
                                <el-button
                                  type="primary"
                                  size="mini"
                                  @click="openYingXiangXueJCDialog('CT')"
                                >
                                  选择CT
                                </el-button>
                              </template>
                              <el-checkbox
                                v-model="xuanZeCT"
                                label="CT"
                                @change="yingXiangXueJCChange('ct')"
                              ></el-checkbox>
                            </el-descriptions-item>
                            <el-descriptions-item :content-style="{ width: '35%' }">
                              <template slot="label">结果:</template>
                              <el-input v-model="ct"></el-input>
                            </el-descriptions-item>
                            <el-descriptions-item>
                              <template slot="label">
                                <el-button
                                  type="primary"
                                  size="mini"
                                  @click="openYingXiangXueJCDialog('MR')"
                                >
                                  选择磁共振
                                </el-button>
                              </template>
                              <el-checkbox
                                v-model="xuanZeCGZ"
                                label="磁共振"
                                @change="yingXiangXueJCChange('mri')"
                              ></el-checkbox>
                            </el-descriptions-item>
                            <el-descriptions-item :content-style="{ width: '35%' }">
                              <template slot="label">结果:</template>
                              <el-input v-model="mri"></el-input>
                            </el-descriptions-item>
                          </el-descriptions>
                        </el-descriptions-item>
                      </el-descriptions>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
                <el-descriptions
                  class="patient-basic-information information"
                  title="申请用药"
                  :column="4"
                  size="medium"
                  border
                >
                  <el-descriptions-item>
                    <template slot="label">药物1名称:</template>
                    <div @click="shenQingYongYaoMCClick('0')">
                      <el-input v-model="yaoPinMC1" readonly></el-input>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">药物2名称:</template>
                    <div @click="shenQingYongYaoMCClick('1')">
                      <el-input v-model="yaoPinMC2" readonly></el-input>
                    </div>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">药物3名称:</template>
                    <div @click="shenQingYongYaoMCClick('2')">
                      <el-input v-model="yaoPinMC3" readonly></el-input>
                    </div>
                  </el-descriptions-item>
                </el-descriptions>
                <el-descriptions
                  class="jiWangYYQK information"
                  title="既往用药情况"
                  :column="4"
                  size="medium"
                  border
                >
                  <el-descriptions-item
                    :label-style="{ width: '0%', padding: '0px !important' }"
                    :content-style="{ width: '100%', backgroundColor: '#EAF0F9' }"
                  >
                    <el-table :data="huiZhenDanData.ylSlkjywYyqkVos" class="jiWangYYQKTable">
                      <el-table-column
                        width="58"
                        prop="xuHao"
                        label="序号"
                        align="center"
                      ></el-table-column>
                      <el-table-column prop="yaoPinMC" label="药品名称">
                        <template #default="{ row }">
                          <div @click="yaoPinMCClick(row)">
                            <el-input v-model="row.yaoPinMC" readonly></el-input>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column width="130" prop="jiLiang" label="计量">
                        <template #default="{ row }">
                          <el-input v-model="row.jiLiang"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column width="100" prop="yiCiYL" label="一次用量">
                        <template #default="{ row }">
                          <el-input v-model="row.yiCiYL"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column width="100" prop="jiLiangDW" label="单位">
                        <template #default="{ row }">
                          <el-input v-model="row.jiLiangDW"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column width="100" prop="zhiLiaoPL" label="执行频率">
                        <template #default="{ row }">
                          <el-input v-model="row.zhiLiaoPL"></el-input>
                        </template>
                      </el-table-column>
                      <el-table-column width="208" prop="kaiShiSJ" label="开始时间">
                        <template #default="{ row }">
                          <el-date-picker
                            v-model="row.kaiShiSJ"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                          ></el-date-picker>
                        </template>
                      </el-table-column>
                      <el-table-column width="208" prop="jieShuSJ" label="结束时间">
                        <template #default="{ row }">
                          <el-date-picker
                            v-model="row.jieShuSJ"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                          ></el-date-picker>
                        </template>
                      </el-table-column>
                      <el-table-column width="70" header-align="center" align="center" label="操作">
                        <template #default="{ row }">
                          <span style="color: #356ac5; cursor: pointer" @click="delJWYYRow(row)">
                            删除
                          </span>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-descriptions-item>
                  <template slot="extra">
                    <el-button type="primary" size="mini" @click="addJWYYRow">新增</el-button>
                  </template>
                </el-descriptions>
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog width="30%" class="Dialog biaoBenDialog" :visible.sync="biaoBenDialog">
      <div slot="title" class="title">病原学检查</div>
      <div style="margin-bottom: 10px">
        <i class="el-icon-question" style="color: rgb(53, 106, 197)"></i>
        请打勾选择标本(可多选)
      </div>
      <el-table ref="biaoBenList" :data="biaoBenList" @select="biaoBenSelect">
        <el-table-column type="selection"></el-table-column>
        <el-table-column prop="yangBenHao" label="样本号"></el-table-column>
        <el-table-column prop="yangBenLXMC" label="样本类型"></el-table-column>
        <el-table-column prop="jieGuo" label="结果"></el-table-column>
        <el-table-column prop="yaoMin" label="药敏"></el-table-column>
      </el-table>
      <div style="margin: 30px 0 10px 0; text-align: right">
        <el-button type="primary" size="mini" @click="writeBianBen">导入</el-button>
        <el-button size="mini">关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog width="22%" class="Dialog" :visible.sync="jinJiQKDialog">
      <div slot="title" class="title">紧急情况说明</div>
      <div style="padding-left: 16px; padding-bottom: 4px">请填写紧急情况理由:</div>
      <el-input
        v-model="huiZhenDanData.jiZhenLY"
        type="textarea"
        :autosize="{ minRows: 6, maxRows: 15 }"
        placeholder="请输入内容"
        maxlength="1000"
        show-word-limit
        :rows="10"
      ></el-input>
      <div style="text-align: right; margin-top: 30px">
        <el-button type="primary">确认</el-button>
        <el-button>关闭</el-button>
      </div>
    </el-dialog>
    <el-dialog width="35%" class="Dialog yingXiangXueJCDialog" :visible.sync="yingXiangXueJCDialog">
      <div slot="title" class="title">选择特殊检查</div>
      <el-table
        ref="yingXiangXueJCTable"
        :data="yingXiangXueJCData"
        @row-dblclick="selectYingXiangXueJC"
      >
        <el-table-column width="200" prop="baoGaoSJ" label="报告时间"></el-table-column>
        <el-table-column prop="baoGaoJG" label="报告结果"></el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog width="30%" class="Dialog" :visible.sync="renYuanXZDialog">
      <div slot="title" class="title">会诊专家选择</div>
      <el-input
        v-model="renYuanXM"
        class="renYuanXMInput"
        placeholder="请输入人员终身码或拼音/五笔码"
        @change="queryRY"
      ></el-input>
      <table class="renYuanList">
        <thead>
          <tr>
            <td>姓名</td>
            <td>终身码</td>
            <td>所在专科</td>
            <td>人员类别</td>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="item in currentPageHuiZhenYSList"
            :key="item.yongHuID"
            @dblclick="handleSelectRY(item)"
          >
            <td>{{ item.xingMing }}</td>
            <td>{{ item.zhongShenDM }}</td>
            <td>{{ item.xianZhuanKeMC }}</td>
            <td>{{ item.renYuanLBMC }}</td>
          </tr>
        </tbody>
      </table>
      <el-pagination
        class="pagination"
        small
        background
        :current-page.sync="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="huiZhenYSList.length"
      ></el-pagination>
    </el-dialog>
    <el-dialog width="20%" class="Dialog" :visible.sync="jinRiHZZJDialog">
      <div slot="title" class="title">会诊医生选择</div>
      <div>今日会诊专家：</div>
      <div style="max-height: 200px; overflow-y: auto">
        <div
          v-for="item in jinRiHuiZhenZZList"
          :key="item.huiZhenYSID"
          style="margin: 5px 0; display: flex; align-items: center"
        >
          <span style="width: 70px; display: inline-block">{{ item.huiZhenYSXM }}</span>
          <el-button style="margin-left: 20px" @click="handleSelectRY(item)">选择</el-button>
        </div>
      </div>
      <el-button style="margin: 20px 0 0" @click="ziXingXZHZZJ">自行选择会诊专家</el-button>
    </el-dialog>
    <el-dialog width="30%" class="Dialog kangJunYWDialog" :visible.sync="kangJunYWDialog">
      <div slot="title" class="title">选择抗菌药物</div>
      <div class="flex">
        <el-input
          v-model="yaoWuMC"
          class="renYuanXMInput"
          placeholder="请输入药品拼音码/五笔码/中文"
          @change="queryKangJunYW"
        ></el-input>
      </div>

      <table class="renYuanList">
        <thead>
          <tr>
            <td>药物名称</td>
            <td>计量</td>
            <td>库存量</td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in currentPageYaoWuList" :key="item.id" @dblclick="selectYaoWu(item)">
            <td>{{ item.mingCheng }}</td>
            <td>{{ item.jiLiang }}</td>
            <td>{{ item.keShouFeiYL > 0 ? '有库存' : '【缺货】' }}</td>
          </tr>
        </tbody>
      </table>
      <el-pagination
        class="pagination"
        small
        background
        :current-page.sync="currentYaoWuPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="yaoWuList.length"
      ></el-pagination>
    </el-dialog>
  </div>
</template>
<script>
import {
  initSlkjywhzd,
  initSlkjywhzdList,
  addSanLianKJYWHZD,
  updateSanLianKJYWHZD,
  getSanLianKJYWHZDListByBlid,
  getSanLianKJYWHZD,
  getWeiShengWuSJ,
  getJinRiHuiZhenZJ,
  getFuZhuJC,
  searchKangJunYP,
  tiJiaoSLKJYWHZD,
  quXiaoTJSLKJYWHZD,
  searchHuiZhenYS
} from '@/api/consultation-form-manage'
import { mapState } from 'vuex'
import { mapComputedSLKJ } from './utils/computedFactory'
import { format } from 'date-fns'
export default {
  data() {
    return {
      initData: {},
      huiZhenDanData: {},
      huiZhenDanList: [],
      xuanZeXX: false,
      xuanZeCT: false,
      xuanZeCGZ: false,
      kangJunYWDialog: false,
      kangJunYWRow: null,
      shenQingYYIndex: null,
      biaoBenList: [],
      biaoBenSelection: [],
      yaoWuList: [],
      yaoWuMC: '', // 搜索抗菌药物内容
      yingXiangXueJCDialog: false,
      yingXiangXueJCData: [], // X线、CT、核磁共振数据
      biaoBenDialog: false,
      jinJiQKDialog: false,
      jinJiQKLY: '',
      jinJiZT: true,
      shenQingLYS: [
        '革兰阴性杆菌+耐热革兰阳性球菌+真菌',
        '泛耐药革兰阴性杆菌+耐药革兰阳性球菌',
        '泛耐药革兰阴性che杆菌+真菌',
        '泛耐药革兰阴性杆菌(CRE,CRAB)',
        '非典型结核分枝杆菌(NTM)',
        '丝状真菌+细菌',
        '结核杆菌+其他细菌或真菌',
        '结核+真菌',
        '耶氏肺孢子菌+细菌',
        '诺卡菌+真菌',
        '难治性真菌感染，常规治疗效果欠佳'
      ],
      biaoBenLXS: [
        '血培养',
        '痰培养',
        '尿培养',
        '粪便培养',
        '胸水培养',
        '腹水培养',
        '脑脊液培养',
        '导管培养',
        '创面培养',
        '其他培养'
      ],
      qiTaPY: false,
      jinRiHZZJDialog: false, // 今日会诊专家窗口
      renYuanXZDialog: false, //申请会诊医生弹出框 是否显示
      renYuanXM: '', //申请会诊医生弹出框内 搜索人员姓名
      huiZhenYSList: [], //申请会诊医生弹出框 医生列表
      jinRiHuiZhenZZList: [], // 今日会诊专家列表
      currentPage: 1, //申请会诊医生弹出框 当前页号
      currentYaoWuPage: 1,
      pageSize: 13 //申请会诊医生弹出框 页面行数
    }
  },
  computed: {
    ...mapComputedSLKJ([
      'baiXiBJS',
      'zhongXingLiBFS',
      'cfanYingDB',
      'jiangGaiSY',
      'bingAnJSZYM',
      'jiGan',
      'jieHeFZGJFHQDNA',
      'youWuWSWSJJG',
      'puFang',
      'ct',
      'mri'
    ]),
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    }),
    bingLiID() {
      return this.$route.params.id
    },
    formatDate() {
      return function (date, formatType) {
        if (!date) {
          return ''
        }
        return format(Date.parse(date), formatType)
      }
    },
    currentPageHuiZhenYSList() {
      return this.huiZhenYSList.slice(
        (this.currentPage - 1) * this.pageSize,
        (this.currentPage - 1) * this.pageSize + this.pageSize
      )
    },
    currentPageYaoWuList() {
      return this.yaoWuList.slice(
        (this.currentYaoWuPage - 1) * this.pageSize,
        (this.currentYaoWuPage - 1) * this.pageSize + this.pageSize
      )
    },
    shenQingLYListMapper: {
      get() {
        return (
          this.huiZhenDanData.shenQingLY?.split(',').map((item) => {
            return this.shenQingLYS[item]
          }) || []
        )
      },
      set(value) {
        let arr = []
        value.map((item) => {
          arr.push(this.shenQingLYS.indexOf(item))
        })
        this.$set(this.huiZhenDanData, 'shenQingLY', arr.sort().join(','))
      }
    },
    biaoBenLXListMapper: {
      get() {
        return (
          this.huiZhenDanData.ylSlkjywFzjcVo?.biaoBenLX?.split('^').map((item) => {
            return this.biaoBenLXS[item]
          }) || []
        )
      },
      set(value) {
        let arr = []
        value.map((item) => {
          arr.push(this.biaoBenLXS.indexOf(item))
        })

        if (!this.huiZhenDanData.ylSlkjywFzjcVo) {
          this.$set(this.huiZhenDanData, 'ylSlkjywFzjcVo', { biaoBenLX: arr.sort().join('^') })
        } else {
          this.$set(this.huiZhenDanData.ylSlkjywFzjcVo, 'biaoBenLX', arr.sort().join('^'))
        }
      }
    },
    biaoBenLXQTMapper: {
      get() {
        return this.huiZhenDanData.ylSlkjywFzjcVo?.biaoBenLXQTPY || ''
      },
      set(val) {
        if (!this.huiZhenDanData.ylSlkjywFzjcVo) {
          this.$set(this.huiZhenDanData, 'ylSlkjywFzjcVo', { biaoBenLXQTPY: val })
        } else {
          this.$set(this.huiZhenDanData.ylSlkjywFzjcVo, 'biaoBenLXQTPY', val)
        }
      }
    },
    yaoPinMC1: {
      get() {
        if (this.huiZhenDanData.ylSlkjywSqywVos?.length > 0) {
          return this.huiZhenDanData.ylSlkjywSqywVos[0].yaoPinMC
        }
        return ''
      },
      set(val) {
        if (!this.huiZhenDanData.ylSlkjywSqywVos) {
          this.huiZhenDanData.ylSlkjywSqywVos = [{}, {}, {}]
        }
        this.huiZhenDanData.ylSlkjywSqywVos[0].yaoPinMC = val
      }
    },
    yaoPinMC2: {
      get() {
        if (this.huiZhenDanData.ylSlkjywSqywVos?.length > 1) {
          return this.huiZhenDanData.ylSlkjywSqywVos[1].yaoPinMC
        }
        return ''
      },
      set(val) {
        this.huiZhenDanData.ylSlkjywSqywVos[1].yaoPinMC = val
      }
    },
    yaoPinMC3: {
      get() {
        if (this.huiZhenDanData.ylSlkjywSqywVos?.length > 2) {
          return this.huiZhenDanData.ylSlkjywSqywVos[2].yaoPinMC
        }
        return ''
      },
      set(val) {
        this.huiZhenDanData.ylSlkjywSqywVos[2].yaoPinMC = val
      }
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      await this.initHuiZhenDanData()
      await this.initHuiZhenDanList()
      await this.initWeiShengWUSJ()
    },
    async initHuiZhenDanData() {
      let res = await initSlkjywhzd({
        bingLiID: this.bingLiID
      })
      this.initData = res.data
      this.$set(this.huiZhenDanData, 'bingRenTZ', this.initData.tiZhong)
      this.$set(this.huiZhenDanData, 'zhuSu', this.initData.zhuSu)
      this.$set(this.huiZhenDanData, 'tiWen', this.initData.tiWen)
      this.$set(this.huiZhenDanData, 'xueYa', this.initData.xueYa)
    },
    async initHuiZhenDanList() {
      let res = await getSanLianKJYWHZDListByBlid({
        bingLiID: this.bingLiID
      })
      this.huiZhenDanList = res.data
    },
    async initKangJunYP() {
      this.yaoWuMC = ''
      let res = await searchKangJunYP({
        key: '',
        yaoFangDM: '2A3'
      })
      this.yaoWuList = res.data
    },
    async initWeiShengWUSJ() {
      let res = await getWeiShengWuSJ({ bingLiID: this.bingLiID })
      this.biaoBenList = [
        ...res.data,
        ...[
          {
            yangBenHao: '20250705XJD061',
            yangBenLXMC: '引流液',
            jieGuo: '普通培养',
            yaoMin: '123'
          },
          { yangBenHao: 'test', yangBenLXMC: '引流液1', jieGuo: 'test培养', yaoMin: '456' },
          { yangBenHao: 'test1', yangBenLXMC: '引流液2', jieGuo: '培养', yaoMin: '789' }
        ]
      ]
    },
    async huiZhenDanListClick(row, col, event) {
      let res = await getSanLianKJYWHZD({
        huiZhenDanID: row.huiZhenDanID
      })

      this.huiZhenDanData = res.data
      if (this.huiZhenDanData.ylSlkjywFzjcVo?.biaoBenLXQTPY != null) {
        this.huiZhenDanData.ylSlkjywFzjcVo.biaoBenLX += '^9'
      }
      this.xuanZeXX = this.huiZhenDanData.ylSlkjywFzjcVo?.puFang != null || false
      this.xuanZeCT = this.huiZhenDanData.ylSlkjywFzjcVo?.ct != null || false
      this.xuanZeCGZ = this.huiZhenDanData.ylSlkjywFzjcVo?.mri != null || false
    },
    async saveHuiZhenDan() {
      let index = this.huiZhenDanData.ylSlkjywFzjcVo.biaoBenLX?.indexOf('9') || -1
      let oldObj = {
        biaoBenLX: this.huiZhenDanData.ylSlkjywFzjcVo.biaoBenLX,
        biaoBenLXQTPY: this.huiZhenDanData.ylSlkjywFzjcVo.biaoBenLXQTPY
      }
      if (index != -1) {
        this.huiZhenDanData.ylSlkjywFzjcVo.biaoBenLX =
          this.huiZhenDanData.ylSlkjywFzjcVo.biaoBenLX.substring(0, index - 1)
      } else {
        this.huiZhenDanData.ylSlkjywFzjcVo.biaoBenLXQTPY = ''
      }
      if (this.huiZhenDanData.huiZhenDanID != 0) {
        let res = await updateSanLianKJYWHZD(this.huiZhenDanData)
        if (res.hasError === 0) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          await this.initHuiZhenDanList()
        }
      } else {
        let res1 = await addSanLianKJYWHZD(this.huiZhenDanData)
        if (res1.hasError === 0) {
          this.$message({
            message: '新增成功',
            type: 'success'
          })
          await this.initHuiZhenDanList()
        }
      }
      this.huiZhenDanData.ylSlkjywFzjcVo.biaoBenLX = oldObj.biaoBenLX
      this.huiZhenDanData.ylSlkjywFzjcVo.biaoBenLXQTPY = oldObj.biaoBenLXQTPY
    },
    async jiJiaoSQ() {
      let res = await tiJiaoSLKJYWHZD({
        huiZhenDanID: this.huiZhenDanData.huiZhenDanID
      })
      if (res.hasError === 0) {
        this.$message({
          message: '提交申请成功',
          type: 'success'
        })
        this.huiZhenDanData.zhuangTaiBZ = '1'
      }
    },
    async cheXiaoSQ() {
      let res = await quXiaoTJSLKJYWHZD({
        huiZhenDanID: this.huiZhenDanData.huiZhenDanID
      })
      if (res.hasError === 0) {
        this.$message({
          message: '撤销申请成功',
          type: 'success'
        })
        this.huiZhenDanData.zhuangTaiBZ = '0'
      }
    },
    selectYingXiangXueJC(row, col, event) {
      switch (this.fuZhuJCType) {
        case 'CR':
          this.xuanZeXX = true
          this.$set(this.huiZhenDanData.ylSlkjywFzjcVo, 'puFang', row.baoGaoJG)
          break
        case 'CT':
          this.xuanZeCT = true
          this.$set(this.huiZhenDanData.ylSlkjywFzjcVo, 'ct', row.baoGaoJG)
          break
        case 'MR':
          this.xuanZeCGZ = true
          this.$set(this.huiZhenDanData.ylSlkjywFzjcVo, 'mri', row.baoGaoJG)
          break
      }
      this.yingXiangXueJCDialog = false
    },
    async openYingXiangXueJCDialog(type) {
      this.fuZhuJCType = type
      let res = {}
      switch (this.fuZhuJCType) {
        case 'CR':
          res = await getFuZhuJC({ bingLiID: this.bingLiID, jianChaLX: 'CR' })
          res.data = [
            {
              baoGaoSJ: '2025/7/9 14:39:33',
              baoGaoJG: '脊柱退行性改变 T11锥体轻度压缩性改变 腰椎过伸过屈位活动度受限'
            },
            {
              baoGaoSJ: '2025/7/7 16:59:14',
              baoGaoJG: '提示骨质疏松'
            }
          ]
          break
        case 'CT':
          res = await getFuZhuJC({ bingLiID: this.bingLiID, jianChaLX: 'CT' })
          res.data = [
            {
              baoGaoSJ: '2025/7/7 22:39:11',
              baoGaoJG:
                '腰椎退行性变伴L5-S1层面右侧隐窝及椎管狭窄，右侧神经根受压 腰椎欠稳、生理曲度变直 QCT提示骨质疏松，请结合图文报告'
            },
            {
              baoGaoSJ: '2025/7/5 15:15:09',
              baoGaoJG: '两肺结节，炎性优先考虑，建议年度随访 冠脉少许钙化 两侧胸膜轻增厚'
            }
          ]
          break
        case 'MR':
          res = await getFuZhuJC({ bingLiID: this.bingLiID, jianChaLX: 'MR' })
          res.data = [
            {
              baoGaoSJ: '2025/7/9 19:51:55',
              baoGaoJG:
                '腰椎退变伴L4/5，L5/S1椎间盘水平椎管狭窄 L4/5水平两侧及L5/S1水平右侧神经根受压 L2锥体轻度前滑移'
            }
          ]
          break
      }
      this.yingXiangXueJCData = res.data
      this.yingXiangXueJCDialog = true
    },
    yaoPinMCClick(row) {
      this.kangJunYWDialog = true
      this.kangJunYWRow = JSON.parse(JSON.stringify(row))
    },
    selectYaoWu(item) {
      console.log(item)
      if (this.kangJunYWRow) {
        let jwyyItem = this.huiZhenDanData.ylSlkjywYyqkVos[this.kangJunYWRow.xuHao - 1]
        this.$set(jwyyItem, 'yaoPinID', item.yaoPinID)
        this.$set(jwyyItem, 'yaoPinMC', item.mingCheng)
        this.$set(jwyyItem, 'jiLiang', item.jiLiang)
      } else {
        if (!this.huiZhenDanData.ylSlkjywSqywVos) {
          this.$set(this.huiZhenDanData, 'ylSlkjywSqywVos', [
            { yaoPinMC: item.mingCheng, yaoPinID: item.yaoPinID }
          ])
        } else {
          this.$set(this.huiZhenDanData.ylSlkjywSqywVos, this.shenQingYYIndex, {
            yaoPinMC: item.mingCheng,
            yaoPinID: item.yaoPinID
          })
        }
      }
      this.kangJunYWDialog = false
    },
    shenQingYongYaoMCClick(index) {
      this.kangJunYWDialog = true
      this.shenQingYYIndex = index
      this.kangJunYWRow = null
    },
    async queryKangJunYW() {
      let res = await searchKangJunYP({
        key: this.yaoWuMC,
        yaoFangDM: '2A3'
      })
      this.yaoWuList = res.data
    },
    async queryRY() {
      let res = await searchHuiZhenYS({ key: this.renYuanXM })
      this.huiZhenYSList = res.data
    },
    handleSelectRY(item) {
      if (this.renYuanXZDialog) {
        this.$set(this.huiZhenDanData, 'huiZhenYSXM', item.xingMing)
        this.$set(this.huiZhenDanData, 'huiZhenYSID', item.yongHuID)
        this.renYuanXZDialog = false
      }
      if (this.jinRiHZZJDialog) {
        this.$set(this.huiZhenDanData, 'huiZhenYSXM', item.huiZhenYSXM)
        this.$set(this.huiZhenDanData, 'huiZhenYSID', item.huiZhenYSID)
        this.jinRiHZZJDialog = false
      }
    },
    async ziXingXZHZZJ() {
      let res = await searchHuiZhenYS()
      this.huiZhenYSList = res.data
      this.jinRiHZZJDialog = false
      this.renYuanXZDialog = true
      this.renYuanXM = ''
    },
    addJWYYRow() {
      if (!this.huiZhenDanData.ylSlkjywYyqkVos) {
        this.$set(this.huiZhenDanData, 'ylSlkjywYyqkVos', [])
      }
      this.huiZhenDanData.ylSlkjywYyqkVos.push({
        xuHao: this.huiZhenDanData.ylSlkjywYyqkVos.length + 1
      })
    },
    delJWYYRow(row) {
      this.huiZhenDanData.ylSlkjywYyqkVos.splice(
        this.huiZhenDanData.ylSlkjywYyqkVos.findIndex((item) => {
          return item.xuHao == row.xuHao
        }),
        1
      )
    },
    yingXiangXueJCChange(name) {
      switch (name) {
        case 'puFang':
          if (!this.xuanZeXX) {
            this.puFang = ''
          }
          break
        case 'ct':
          if (!this.xuanZeCT) {
            this.ct = ''
          }
          break
        case 'mri':
          if (!this.xuanZeCGZ) {
            this.mri = ''
          }
          break
      }
    },
    youWuSJJGChange(val) {
      this.biaoBenDialog = true
      this.biaoBenSelection = []
      this.$refs.biaoBenList.clearSelection()
    },
    clear() {
      this.xuanZeCGZ = false
      this.xuanZeCT = false
      this.xuanZeXX = false
      this.$set(this.huiZhenDanData, 'bingRenTZ', this.initData.tiZhong)
      this.$set(this.huiZhenDanData, 'zhuSu', this.initData.zhuSu)
      this.$set(this.huiZhenDanData, 'tiWen', this.initData.tiWen)
      this.$set(this.huiZhenDanData, 'xueYa', this.initData.xueYa)
      this.huiZhenDanData = {
        bingRenTZ: this.initData.tiZhong,
        zhuSu: this.initData.zhuSu,
        tiWen: this.initData.tiWen,
        xueYa: this.initData.xueYa,
        huiZhenDanID: '0',
        ylSlkjywFzjcVo: {},
        ylSlkjywWswsjjgVos: [],
        ylSlkjywYyqkVos: [],
        ylSlkjywYzVo: {},
        yuFangYYZZ: '',
        yuFangYYZZF: '',
        zhiLiaoYYZZ: '',
        ganRanZD: '',
        bingAnHao: this.initData.inPatientVo.bingAnHao, //病案号
        zhuYuanHao: this.initData.inPatientVo.zhuYuanHao, //住院号
        bingLiID: this.bingLiID,
        chuangWeiHao: this.initData.inPatientVo.chuangWeiHao, //床位号
        zhuanKeID: this.zhuanKeID,
        bingQuID: this.initData.inPatientVo.bingQuID, //病区ID
        yaoPinID: '0' //药品ID
      }
    },
    biaoBenSelect(selection, row) {
      this.biaoBenSelection = selection
    },
    writeBianBen() {
      if (this.biaoBenSelection.length != 0) {
        let arr = []
        this.biaoBenSelection.map((item, index) => {
          arr.push({
            biaoBenLXMC: null,
            bingYuanXueYBBH: item.yangBenHao,
            bingYuanXueJG: item.jieGuo,
            bingYuanXueYM: item.yaoMin,
            xuHao: index + 1
          })
        })
        this.huiZhenDanData.ylSlkjywWswsjjgVos = arr
      }

      this.biaoBenDialog = false
    },
    async selectJinRiHZZJ() {
      this.jinRiHZZJDialog = true
      let res = await getJinRiHuiZhenZJ()
      if (res.hasError === 0) this.jinRiHuiZhenZZList = res.data
    }
  }
}
</script>
<style lang="scss" scoped>
.hide-label {
  ::v-deep .el-radio__label {
    display: none;
  }
}
.underline-content {
  text-decoration: underline;
  text-underline-offset: 2px;
  color: #155bd4;
  cursor: pointer;
}
::v-deep .hide {
  background: rgb(239, 243, 251) !important;
  border-color: rgb(239, 243, 251) !important;
}
::v-deep .hide1 {
  background: rgb(239, 243, 251) !important;
  border: none !important;
}
.flex {
  display: flex;
  align-items: center;
}
.select-input {
  ::v-deep .el-input__inner {
    background-color: #e4ecfb;
    height: 24px;
  }
}
.Dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
    .renYuanXMInput {
      width: 43%;
      margin-right: 10px;
      .el-input__inner {
        padding: 0 6px;
        height: 24px;
        font-size: 13px;
      }
    }
    .renYuanList {
      margin-top: 20px;
      width: 100%;
      thead {
        background-color: #eaf0f9;
      }
      td {
        border: 1px solid #dcdfe6;
        border-collapse: collapse; /* 移除表格内边框间的间隙 */
        padding: 6px;
        font-size: 13px;
      }
      tbody {
        tr:nth-child(even) {
          background-color: #eaf0f9;
        }
        tr:nth-child(odd) {
          background-color: #f6f6f6;
        }
      }
    }
    .bottom-btn {
      margin-top: 30px;
      flex-direction: row-reverse;
      .el-button {
        margin-left: 10px;
      }
    }
  }
}

.biaoBenDialog {
  ::v-deep th {
    .el-checkbox {
      display: none;
    }
  }
}
.clinical-pharmacy-view {
  background-color: #fff;
  height: 100%;
  ::v-deep .el-input__inner {
    padding: 0 8px;
  }
  ::v-deep .el-descriptions__title {
    font-size: 13px;
  }
  ::v-deep .el-descriptions__title::before {
    font-weight: 600;
    border-left: 3px solid #356ac5;
    display: inline;
  }
  .title {
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    text-align: left;
  }
  .left-aside {
    padding: 10px;
    ::v-deep .el-input__inner {
      height: 26px !important;
      width: 180px !important;
    }
    ::v-deep .el-table .el-table__cell {
      padding: 6px 0;
      font-size: 12px;
    }
  }
  .right-main {
    padding: 10px 10px 10px 0;
    .box {
      // border: 1px solid #dcdfe6;
      padding: 10px 10px 0 10px;
      height: 100%;

      .content::-webkit-scrollbar {
        display: none;
      }
      .content-box {
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        padding: 0 10px;
        height: calc(95% - 20px);
        ::v-deep .content {
          overflow: scroll;
          height: 95%;
          border: 1px solid #dcdfe6;
          .el-descriptions-row {
            td,
            th {
              border-collapse: collapse;
              border: 1px solid #dcdfe6;
              padding: 8px !important;
              font-size: 12px;
            }
            th {
              text-align: right;
              color: #000;
              background-color: #eaf0f9;
              width: 9%;
            }
            td {
              background-color: #f5f8fc;
              width: 13%;
            }
          }

          .jiWangYYQK {
            th {
              text-align: center;
            }
            .jiWangYYQKTable {
              td {
                padding: 6px 0 !important;
                .el-date-editor {
                  width: 100%;
                  .el-input__inner {
                    padding: 0 30px !important;
                  }
                }
              }
            }
          }
          .el-descriptions__header {
            margin: 8px;
          }
        }
        .qiTaPeiYangInput {
          width: 200px;
          margin-left: 10px;
          ::v-deep .el-input__inner {
            height: 24px;
          }
        }
      }
    }
  }
}
</style>
