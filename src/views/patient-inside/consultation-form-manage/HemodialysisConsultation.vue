<template>
  <div class="clinical-pharmacy-view">
    <el-container>
      <el-aside class="left-aside" width="400px !important">
        <div style="background-color: #eff3fb; height: 100%; padding: 10px">
          <el-descriptions
            class="information"
            title="病人会诊单列表"
            :column="3"
            size="medium"
            border
          >
            <template slot="extra">
              <el-select v-model="huiZhenMC" placeholder="请选择">
                <el-option
                  v-for="item in huiZhenList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-button type="primary" size="mini" @click="clear">新增</el-button>
            </template>
          </el-descriptions>
          <el-table
            :data="huiZhenTable"
            border
            style="width: 100%; margin-top: 20px"
            @row-click="huiZhenTableClick"
          >
            <el-table-column prop="shenQingSJ" label="会诊时间" width="180"></el-table-column>
            <el-table-column prop="urlText" label="会诊单名称"></el-table-column>
          </el-table>
        </div>
      </el-aside>
      <el-main class="right-main">
        <div style="background-color: #eff3fb; height: 100%">
          <div class="box">
            <div style="height: 6%">
              <el-descriptions
                class="information"
                title="血透会诊单"
                :column="3"
                size="medium"
                border
              >
                <template slot="extra">
                  <el-button type="primary" size="mini" @click="handleSave">保存</el-button>
                  <el-button type="primary" size="mini" @click="resetHuiZhenDan">重置</el-button>
                  <el-button
                    type="info"
                    plain
                    :disabled="huiZhenDanID == '0'"
                    size="mini"
                    @click="sendMessage"
                  >
                    发送短信通知
                  </el-button>
                </template>
              </el-descriptions>
            </div>
            <div class="content-box">
              <div class="content">
                <div>
                  <el-descriptions
                    class="patient-basic-information information"
                    title="病人基本信息"
                    :column="3"
                    size="medium"
                    border
                  >
                    <el-descriptions-item>
                      <template slot="label">姓名:</template>
                      {{ patientData.bingRenXM }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">性别:</template>
                      {{
                        patientData.bingRenXB == '1'
                          ? '男'
                          : patientData.bingRenXB == '0'
                          ? '女'
                          : ''
                      }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">出生日期:</template>
                      {{ formatDate(patientData.chuShengRQ, 'yyyy-MM-dd') }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">病区:</template>
                      {{ patientData.bingQuMC }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">床位:</template>
                      {{ patientData.chuangWeiHao }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">病案号:</template>
                      {{ patientData.bingAnHao }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <div>
                  <el-descriptions
                    class="Applicant-information information"
                    title="申请人信息"
                    :column="3"
                    size="medium"
                    border
                  >
                    <el-descriptions-item>
                      <template slot="label">申请医师:</template>
                      {{ initData.shenQingYSXM }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">联系电话:</template>
                      <el-input v-model="patientData.shenQingYSLXDH"></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">申请专科:</template>
                      {{ initData.shenQingZKMC }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <div>
                  <el-descriptions
                    class="Application-information information"
                    title="申请信息"
                    :column="3"
                    size="medium"
                    border
                  >
                    <el-descriptions-item>
                      <template slot="label">
                        <i
                          class="el-icon-question"
                          style="color: rgb(53, 106, 197)"
                          @click="showBeiZhu = !showBeiZhu"
                        ></i>
                        会诊选择:
                        <div v-show="showBeiZhu">{{ beiZhu }}</div>
                      </template>
                      <el-radio
                        v-for="item in initData.huiZhenBZList"
                        :key="item.daiMa"
                        v-model="patientData.huiZhenBZ"
                        :label="item.daiMa"
                      >
                        {{ item.mingCheng }}
                      </el-radio>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">会诊专科:</template>
                      <el-select
                        v-model="patientData.huiZhenZKMC"
                        placeholder="请选择"
                        @change="changeHZZK"
                      >
                        <el-option
                          v-for="item in initData.zhuanKeList"
                          :key="item.buMenID"
                          :label="item.buMenMC"
                          :value="item.buMenID"
                        ></el-option>
                      </el-select>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">邀请会诊医生:</template>
                      <div @dblclick="shenQingHZYS">
                        <el-input
                          v-model="patientData.shenQingHZYSXM"
                          class="select-input"
                        ></el-input>
                      </div>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">术前四项:</template>
                      <el-radio v-model="patientData.xueTouFJXX.shuQianSX" label="1">是</el-radio>
                      <el-radio v-model="patientData.xueTouFJXX.shuQianSX" label="0">否</el-radio>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">血透知情同意书:</template>
                      <el-radio v-model="patientData.xueTouFJXX.tongYiShu" label="1">是</el-radio>
                      <el-radio v-model="patientData.xueTouFJXX.tongYiShu" label="0">否</el-radio>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">可否使用抗凝剂:</template>
                      <el-radio v-model="patientData.xueTouFJXX.kangNingJi" label="1">是</el-radio>
                      <el-radio v-model="patientData.xueTouFJXX.kangNingJi" label="0">否</el-radio>
                    </el-descriptions-item>
                    <el-descriptions-item span="4">
                      <template slot="label">最后一次血透时间及频率:</template>
                      <el-input v-model="patientData.xueTouFJXX.xueTouSJPLLatest"></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>患者病情及诊疗经过:</div>
                        <div class="underline-content">获取检查数据</div>
                      </template>
                      <el-input
                        v-model="patientData.bingQingZY"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>初步诊断:</div>
                      </template>
                      <el-input
                        v-model="patientData.chuBuZD"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item span="4">
                      <template slot="label">备注:</template>
                      <el-input v-model="patientData.xueTouFJXX.shenQingBZ"></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>申请血透会诊:</div>
                        <div>使用理由</div>
                        <div class="underline-content">获取检查数据</div>
                        <div class="underline-content">历史诊疗</div>
                      </template>
                      <el-input
                        v-model="patientData.shenQingLY"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>短信通知:</div>
                      </template>
                      <el-table :data="patientData.messageList">
                        <el-table-column prop="zhuangTai" label="状态"></el-table-column>
                        <el-table-column prop="shouJiHM" label="手机号码"></el-table-column>
                        <el-table-column prop="xiaoXiNR" label="消息内容"></el-table-column>
                        <el-table-column prop="faSongSJ" label="发送时间"></el-table-column>
                        <el-table-column prop="huiZhiSJ" label="回执时间"></el-table-column>
                      </el-table>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <div v-if="initData.additionalTip" class="tips">
                <i class="el-icon-warning" style="color: rgb(53, 106, 197)"></i>
                {{ initData.additionalTip }}
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog width="30%" class="Dialog" :visible.sync="renYuanXZDialog">
      <div slot="title" class="title">人员选择</div>
      <el-input
        v-model="renYuanXM"
        class="renYuanXMInput"
        placeholder="请输入人员终身码或拼音/五笔码"
        @change="queryZhuDaoYS"
      ></el-input>
      <table class="renYuanList">
        <thead>
          <tr>
            <td>姓名</td>
            <td>终身码</td>
            <td>现部门</td>
            <td>固定部门</td>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="item in currentPageHuiZhenYSList"
            :key="item.yongHuID"
            @dblclick="handleSelectRY(item)"
          >
            <td>{{ item.yiShengXM }}</td>
            <td>{{ item.zhongShenDM }}</td>
            <td>{{ item.xianZhuanKe }}</td>
            <td>{{ item.guDingZK }}</td>
          </tr>
        </tbody>
      </table>
      <el-pagination
        class="pagination"
        small
        background
        :current-page.sync="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="huiZhenYSList.length"
      ></el-pagination>
    </el-dialog>
  </div>
</template>
<script>
import {
  getAddConsulationInitData,
  getConsulationData,
  getPatientofConsulation,
  getConsulationByBLID,
  newConsulation,
  getDoctorOfConsulation,
  sendConsulationMessageSP
} from '@/api/consultation-form-manage'
import { mapState } from 'vuex'
import { format } from 'date-fns'
export default {
  data() {
    return {
      showBeiZhu: false,
      patientData: {},
      initData: {},
      huiZhenDanID: '0', //会诊单id  0为新增
      huiZhenBZ: '13', //会诊标志
      huiZhenList: [
        {
          label: '血透会诊',
          value: '1'
        }
      ], //左侧会诊选择列表
      huiZhenMC: '血透会诊', //会诊名称
      huiZhenTable: [], //左侧会诊单表格
      zhuanKeList: [], //会诊专科列表
      renYuanXZDialog: false, //申请会诊医生弹出框 是否显示
      renYuanXM: '', //申请会诊医生弹出框内 搜索人员姓名
      huiZhenYSList: [], //申请会诊医生弹出框 医生列表
      currentHuiZhenYSList: [],
      currentPage: 1, //申请会诊医生弹出框 当前页号
      pageSize: 13 //申请会诊医生弹出框 页面行数
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    }),
    bingLiID() {
      return this.$route.params.id
    },
    formatDate() {
      return function (date, formatType) {
        if (!date) {
          return ''
        }
        return format(Date.parse(date), formatType)
      }
    },
    currentPageHuiZhenYSList() {
      return this.currentHuiZhenYSList.slice(
        (this.currentPage - 1) * this.pageSize,
        (this.currentPage - 1) * this.pageSize + this.pageSize
      )
    },
    beiZhu() {
      if (this.patientData.huiZhenBZ) {
        return this.initData.huiZhenBZList.find((item) => item.daiMa == this.patientData.huiZhenBZ)
          .beiZhu
      }
      return ''
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    clear() {
      this.huiZhenDanID = 0
      let clearItem = [
        'shenQingYSLXDH',
        'huiZhenZKID',
        'huiZhenZKMC',
        'shenQingHZYSID',
        'shenQingHZYSXM',
        'bingQingZY',
        'chuBuZD',
        'shenQingLY'
      ]
      clearItem.map((item) => {
        this.patientData[item] = null
      })
      this.$set(this.patientData, 'huiZhenBZ', this.initData.huiZhenBZList[0].daiMa)
      this.$set(this.patientData, 'xueTouFJXX', {
        huiZhenBZ: this.patientData.huiZhenBZ,
        kangNingJi: '',
        niXueTouSJ: '',
        shenQingBZ: '',
        shiFouXT: '1',
        shuQianSX: '',
        tongYiShu: '',
        xueTouSJPLLatest: ''
      })
      this.patientData.messageList = []
    },
    async resetHuiZhenDan() {
      if (this.huiZhenDanID == 0) {
        await this.initPatientData()
      } else {
        let res = await getConsulationData({
          huiZhenDanID: this.huiZhenDanID
        })
        this.patientData = res.data
      }
    },
    async initPatientData() {
      let res = await getAddConsulationInitData({
        zhuanKeID: this.zhuanKeID,
        tiJianBH: '',
        huiZhenBZ: this.huiZhenBZ
      })
      this.initData = res.data
      let res1 = await getPatientofConsulation({
        bingLiID: this.bingLiID,
        huiZhenBZ: this.huiZhenBZ,
        moRenZKID: 0
      })
      this.patientData = res1.data
      this.$set(this.patientData, 'huiZhenZKMC', res.data.zhuanKeList[0].buMenMC)
      this.$set(this.patientData, 'huiZhenZKID', res.data.zhuanKeList[0].buMenID)
      this.$set(this.patientData, 'huiZhenBZ', res.data.huiZhenBZList[0].daiMa)
      this.$set(this.patientData, 'shenQingYSLXDH', res.data.shenQingYSLXDH)
      this.$set(this.patientData, 'messageList', [])
      this.$set(this.patientData, 'xueTouFJXX', {
        huiZhenBZ: this.patientData.huiZhenBZ,
        kangNingJi: '',
        niXueTouSJ: '',
        shenQingBZ: '',
        shiFouXT: '1',
        shuQianSX: '',
        tongYiShu: '',
        xueTouSJPLLatest: ''
      })
      console.log(this.patientData.xueTouFJXX)
    },
    async initHuiZhenTable() {
      let res = await getConsulationByBLID({
        bingLiID: this.bingLiID
      })
      this.huiZhenTable = res.data.filter((item) => {
        return item.huiZhenBZ == this.huiZhenBZ
      })
    },
    async init() {
      await this.initHuiZhenTable()
      await this.initPatientData()
      console.log(this.patientData.xueTouFJXX)
    },
    async handleSave() {
      let param = {
        bingLiID: this.bingLiID,
        bingQingZY: this.patientData.bingQingZY,
        chuBuZD: this.patientData.chuBuZD,
        huiZhenBZ: this.huiZhenBZ,
        huiZhenDanID: this.huiZhenDanID,
        moRenZKID: 0,
        shenQingLY: this.patientData.shenQingLY,
        shenQingYSLXDH: this.patientData.shenQingYSLXDH,
        shenQingZKID: this.zhuanKeID,
        tiJianBH: '',
        tiJianKHLX: '',
        xueTouFJXX: this.patientData.xueTouFJXX,
        zhuanKeYSXXList: [
          {
            huiZhenZKID: this.patientData.huiZhenZKID,
            id: 0,
            shenQingHZYSID: this.patientData.shenQingHZYSID
          }
        ]
      }
      // console.log(param)
      // let param = {
      //   ...this.patientData,
      //   ...{
      //     bingLiID: this.bingLiID,
      //     huiZhenBZ: this.huiZhenBZ,
      //     huiZhenDanID: this.huiZhenDanID,
      //     niNanBingTL: this.patientData.niNanBingTL || '0',
      //     shenQingZKID: this.patientData.shenQingZKID || this.zhuanKeID,
      //     suoZaiDW: this.patientData.suoZaiDW || '',
      //     tiJianBH: this.patientData.tiJianBH || '',
      //     tiJianKHLX: this.patientData.tiJianKHLX || '',
      //     xueTouFJXX: this.patientData.xueTouFJXX || {
      //       huiZhenBZ: '',
      //       kangNingJi: '',
      //       niXueTouSJ: '',
      //       shenQingBZ: '',
      //       shiFouXT: '',
      //       shuQianSX: '',
      //       tongYiShu: '',
      //       xueTouSJPLLatest: ''
      //     },
      //     moRenZKID: 0,
      //     zhuanKeYSXXList: [
      //       {
      //         huiZhenZKID: this.patientData.huiZhenZKID,
      //         id: 0,
      //         shenQingHZYSID: this.patientData.shenQingHZYSID
      //       }
      //     ]
      //   }
      // }
      // for (const key in param) {
      //   if (!needItem.includes(key)) {
      //     delete param[key]
      //   }
      // }
      let res = await newConsulation(param)
      if (res.hasError === 0) {
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        await this.initHuiZhenTable()
      }
    },
    async huiZhenTableClick(row, column, event) {
      this.huiZhenDanID = row.huiZhenDanID
      let res = await getConsulationData({
        huiZhenDanID: row.huiZhenDanID
      })
      this.patientData = res.data
      if (this.patientData.xueTouFJXX == null) {
        this.$set(this.patientData, 'xueTouFJXX', {
          huiZhenBZ: this.patientData.huiZhenBZ,
          kangNingJi: '',
          niXueTouSJ: '',
          shenQingBZ: '',
          shiFouXT: '1',
          shuQianSX: '',
          tongYiShu: '',
          xueTouSJPLLatest: ''
        })
      }
    },
    async queryZhuDaoYS() {
      this.currentHuiZhenYSList = this.huiZhenYSList.filter((item) => {
        if (
          (item.pinYin != null && item.pinYin.indexOf(this.renYuanXM.toUpperCase()) === 0) ||
          (item.wuBi != null && item.wuBi.indexOf(this.renYuanXM.toUpperCase()) === 0) ||
          (item.zhongShenDM != null && item.zhongShenDM?.indexOf(this.renYuanXM) === 0)
        ) {
          return true
        }
      })
    },
    async handleSelectRY(item) {
      this.patientData.shenQingHZYSXM = item.yiShengXM
      this.patientData.shenQingHZYSID = item.yongHuID
      this.renYuanXZDialog = false
    },
    async shenQingHZYS() {
      if (this.patientData.huiZhenZKID == null) {
        return
      }
      this.renYuanXZDialog = true
      let res = await getDoctorOfConsulation({
        huiZhenBZ: this.huiZhenBZ,
        huiZhenZKID: this.patientData.huiZhenZKID
      })
      this.huiZhenYSList = this.currentHuiZhenYSList = res.data.consulationDoctorList
    },
    changeHZZK(val) {
      if (this.patientData.huiZhenZKID != val) {
        this.patientData.huiZhenZKID = val
        this.patientData.shenQingHZYSXM = ''
        delete this.patientData['shenQingHZYSID']
      }
    },
    async sendMessage() {
      await sendConsulationMessageSP({
        huiZhenDanID: this.huiZhenDanID
      })
      let res = await getConsulationData({
        huiZhenDanID: this.huiZhenDanID
      })
      this.patientData = res.data
    },
    huiZhenXZDetail() {
      console.log(123)
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
}
.select-input {
  ::v-deep .el-input__inner {
    background-color: #e4ecfb;
    height: 24px;
  }
}
.Dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
    .renYuanXMInput {
      width: 43%;
      margin-right: 10px;
      .el-input__inner {
        padding: 0 6px;
        height: 24px;
        font-size: 13px;
      }
    }
    .renYuanList {
      margin-top: 20px;
      width: 100%;
      thead {
        background-color: #eaf0f9;
      }
      td {
        border: 1px solid #dcdfe6;
        border-collapse: collapse; /* 移除表格内边框间的间隙 */
        padding: 6px;
        font-size: 13px;
      }
      tbody {
        tr:nth-child(even) {
          background-color: #eaf0f9;
        }
        tr:nth-child(odd) {
          background-color: #f6f6f6;
        }
      }
    }
    .bottom-btn {
      margin-top: 30px;
      flex-direction: row-reverse;
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .shouShuHeader {
    padding: 6px 10px;
    border: 1px solid #dcdfe6;
  }
  .shouShuTable {
    padding: 12px;
    border: 1px solid #dcdfe6;
    border-top: none;
    position: relative;
    .pagination {
      position: absolute;
      bottom: 10px;
      right: 10px;
    }
  }
  .shouShuXZList {
    height: 400px;
    tr {
      height: 50px;
    }
  }
  .yiXuanZeShouShuList {
    height: 242px;
    ::v-deep th {
      .el-checkbox {
        display: none;
      }
    }
  }
  .shouShuXZHeader {
    justify-content: space-between;
  }
  .yiXuanZeSSHeader {
    margin-top: 10px;
  }
}
.clinical-pharmacy-view {
  background-color: #fff;
  height: 100%;
  ::v-deep .el-input__inner {
    padding: 0 8px;
  }
  ::v-deep .el-descriptions__title {
    font-size: 13px;
  }
  ::v-deep .el-descriptions__title::before {
    font-weight: 600;
    border-left: 3px solid #356ac5;
    display: inline;
  }
  .title {
    font-weight: 600;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    text-align: left;
  }
  .left-aside {
    padding: 10px;
    ::v-deep .el-input__inner {
      height: 26px !important;
      width: 180px !important;
    }
    ::v-deep .el-table .el-table__cell {
      padding: 6px 0;
      font-size: 12px;
    }
  }
  .right-main {
    padding: 10px 10px 10px 0;
    .box {
      // border: 1px solid #dcdfe6;
      padding: 10px 10px 0 10px;
      height: 94%;

      .content::-webkit-scrollbar {
        display: none;
      }
      .content-box {
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        padding: 10px 10px 0 10px;
        height: 100%;
        ::v-deep .content {
          overflow: scroll;
          height: 95%;
          td,
          th {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 8px !important;
            font-size: 12px;
          }
          th {
            text-align: right;
            color: #000;
            background-color: #eaf0f9;
            width: 12%;
          }
          td {
            background-color: #f5f8fc;
            width: 18%;
          }
          .information {
            margin-bottom: 8px;
          }
          .Application-information {
            margin-bottom: 0px;
            .patient-label {
              vertical-align: top;
              .underline-content {
                text-decoration: underline;
                text-underline-offset: 2px;
                color: #155bd4;
                cursor: pointer;
              }
            }
            .patient-content {
              padding: 5px 6px !important;
            }
          }
          .right-aside {
            width: 252px !important;
          }
        }
        .tips {
          display: flex;
          align-items: center;
          height: 5%;
          font-size: 12px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
