<template>
  <div class="clinical-pharmacy-view">
    <el-container>
      <el-aside class="left-aside" width="400px !important">
        <div style="background-color: #eff3fb; height: 100%; padding: 10px">
          <el-descriptions
            class="information"
            title="病人会诊单列表"
            :column="3"
            size="medium"
            border
          >
            <template slot="extra">
              <el-select v-model="huiZhenMC" placeholder="请选择">
                <el-option
                  v-for="item in huiZhenList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
              <el-button type="primary" size="mini" @click="clear">新增</el-button>
            </template>
          </el-descriptions>
          <el-table
            :data="huiZhenTable"
            border
            style="width: 100%; margin-top: 20px"
            @row-click="huiZhenTableClick"
          >
            <el-table-column prop="shenQingSJ" label="会诊时间" width="180"></el-table-column>
            <el-table-column prop="urlText" label="会诊单名称"></el-table-column>
          </el-table>
        </div>
      </el-aside>
      <el-main class="right-main">
        <div style="background-color: #eff3fb; height: 100%">
          <div class="box">
            <div style="height: 6%">
              <el-descriptions
                class="information"
                title="疑难病历多学科会诊单"
                :column="3"
                size="medium"
                border
              >
                <template slot="extra">
                  <el-button type="primary" size="mini" @click="handleSave">保存</el-button>
                  <el-button type="primary" size="mini" @click="resetHuiZhenDan">重置</el-button>
                  <el-button type="info" plain disabled size="mini">发送短信通知</el-button>
                </template>
              </el-descriptions>
            </div>
            <div class="content-box">
              <div class="content">
                <div>
                  <el-descriptions
                    class="patient-basic-information information"
                    title="病人基本信息"
                    :column="3"
                    size="medium"
                    border
                  >
                    <el-descriptions-item>
                      <template slot="label">姓名:</template>
                      {{ patientData.bingRenXM }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">性别:</template>
                      {{
                        patientData.bingRenXB == '1'
                          ? '男'
                          : patientData.bingRenXB == '0'
                          ? '女'
                          : ''
                      }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">出生日期:</template>
                      {{ formatDate(patientData.chuShengRQ, 'yyyy-MM-dd') }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">病区:</template>
                      {{ patientData.bingQuMC }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">床位:</template>
                      {{ patientData.chuangWeiHao }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">病案号:</template>
                      {{ patientData.bingAnHao }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <div>
                  <el-descriptions
                    class="Applicant-information information"
                    title="申请人信息"
                    :column="3"
                    size="medium"
                    border
                  >
                    <el-descriptions-item>
                      <template slot="label">申请医师:</template>
                      {{ initData.shenQingYSXM }}
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">联系电话:</template>
                      <el-input v-model="patientData.shenQingYSLXDH"></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">申请专科:</template>
                      {{ initData.shenQingZKMC }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
                <div>
                  <el-descriptions
                    class="Application-information information"
                    title="申请信息"
                    :column="3"
                    size="medium"
                    border
                  >
                    <el-descriptions-item>
                      <template slot="label">
                        <i
                          class="el-icon-question"
                          style="color: rgb(53, 106, 197)"
                          @click="showBeiZhu = !showBeiZhu"
                        ></i>
                        会诊选择:
                        <div v-show="showBeiZhu">{{ beiZhu }}</div>
                      </template>
                      <el-radio
                        v-for="item in initData.huiZhenBZList"
                        :key="item.daiMa"
                        v-model="patientData.huiZhenBZ"
                        :label="item.daiMa"
                      >
                        {{ item.mingCheng }}
                      </el-radio>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">会诊专科:</template>
                      <template v-for="(pItem, index) in patientData.zhuanKeYSXXList">
                        <el-select
                          :key="pItem.id"
                          v-model="pItem.huiZhenZKID"
                          placeholder="请选择"
                          @change="changeHZZK(index)"
                        >
                          <el-option
                            v-for="item in initData.zhuanKeList"
                            :key="item.buMenID"
                            :label="item.buMenMC"
                            :value="item.buMenID"
                          ></el-option>
                        </el-select>
                      </template>
                    </el-descriptions-item>
                    <el-descriptions-item>
                      <template slot="label">邀请会诊医生:</template>
                      <div>
                        <template v-for="aItem in patientData.zhuanKeYSXXList">
                          <div :key="aItem.id" @dblclick="shenQingHZYS(aItem)">
                            <el-input
                              v-model="aItem.shenQingHZYSXM"
                              class="select-input"
                            ></el-input>
                          </div>
                        </template>
                      </div>
                    </el-descriptions-item>
                    <el-descriptions-item span="3">
                      <template slot="label">预约会诊地点:</template>
                      <div class="flex">
                        <el-input
                          v-model="patientData.yuYueHZDD"
                          style="width: 300px; margin-right: 10px"
                        ></el-input>
                        <el-checkbox-group
                          v-model="yuYueHZSelect"
                          @change="handleCheckedYuYueHZChange"
                        >
                          <el-checkbox
                            v-for="yuYueHZ in yuYueHZList"
                            :key="yuYueHZ"
                            :label="yuYueHZ"
                          >
                            {{ yuYueHZ }}
                          </el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </el-descriptions-item>
                    <el-descriptions-item span="3">
                      <template slot="label">预约会诊时间:</template>
                      <el-date-picker
                        v-model="patientData.yuYueHZSJ"
                        type="datetime"
                        placeholder="请选择预约会诊日期"
                        value-format="yyyy-MM-dd HH:mm:ss"
                      ></el-date-picker>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>患者病情及诊疗经过:</div>
                        <div class="underline-content">获取检查数据</div>
                      </template>
                      <el-input
                        v-model="patientData.bingQingZY"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>初步诊断:</div>
                      </template>
                      <el-input
                        v-model="patientData.chuBuZD"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>申请疑难病历会诊:</div>
                        <div>使用理由</div>
                        <div class="underline-content">获取检查数据</div>
                        <div class="underline-content">历史诊疗</div>
                      </template>
                      <el-input
                        v-model="patientData.shenQingLY"
                        type="textarea"
                        :autosize="{ minRows: 3, maxRows: 15 }"
                        placeholder="请输入内容"
                        maxlength="1000"
                        show-word-limit
                        :rows="10"
                      ></el-input>
                    </el-descriptions-item>
                    <el-descriptions-item
                      label-class-name="patient-label"
                      content-class-name="patient-content"
                      span="4"
                    >
                      <template slot="label">
                        <div>短信通知:</div>
                      </template>
                      <el-table :data="patientData.messageList">
                        <el-table-column prop="zhuangTai" label="状态"></el-table-column>
                        <el-table-column prop="shouJiHM" label="手机号码"></el-table-column>
                        <el-table-column prop="xiaoXiNR" label="消息内容"></el-table-column>
                        <el-table-column prop="faSongSJ" label="发送时间"></el-table-column>
                        <el-table-column prop="huiZhiSJ" label="回执时间"></el-table-column>
                      </el-table>
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
              <div v-if="initData.additionalTip" class="tips">
                <i class="el-icon-warning" style="color: rgb(53, 106, 197)"></i>
                {{ initData.additionalTip }}
              </div>
            </div>
          </div>
        </div>
      </el-main>
    </el-container>
    <el-dialog width="30%" class="Dialog" :visible.sync="renYuanXZDialog">
      <div slot="title" class="title">人员选择</div>
      <el-input
        v-model="renYuanXM"
        class="renYuanXMInput"
        placeholder="请输入人员终身码或拼音/五笔码"
        @change="queryZhuDaoYS"
      ></el-input>
      <table class="renYuanList">
        <thead>
          <tr>
            <td>姓名</td>
            <td>终身码</td>
            <td>现部门</td>
            <td>固定部门</td>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="item in currentPageHuiZhenYSList"
            :key="item.yongHuID"
            @dblclick="handleSelectRY(item)"
          >
            <td>{{ item.yiShengXM }}</td>
            <td>{{ item.zhongShenDM }}</td>
            <td>{{ item.xianZhuanKe }}</td>
            <td>{{ item.guDingZK }}</td>
          </tr>
        </tbody>
      </table>
      <el-pagination
        class="pagination"
        small
        background
        :current-page.sync="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="huiZhenYSList.length"
      ></el-pagination>
    </el-dialog>
  </div>
</template>
<script>
import {
  getAddConsulationInitData,
  getConsulationData,
  getPatientofConsulation,
  getConsulationByBLID,
  newConsulation,
  getDoctorOfConsulation,
  sendConsulationMessageSP
} from '@/api/consultation-form-manage'
import { mapState } from 'vuex'
import { format } from 'date-fns'
export default {
  data() {
    return {
      showBeiZhu: false,
      yuYueHZList: ['属于疑难病历讨论', '邀请医务处参加', '邀请医患办参加'],
      yuYueHZSelect: [],
      patientData: {},
      initData: {},
      huiZhenDanID: '0', //会诊单id  0为新增
      huiZhenBZ: ['4', '15'], //会诊标志 全院疑难病例讨论申请 术前多学科讨论
      huiZhenList: [
        {
          label: '疑难病例多学科会诊',
          value: '1'
        }
      ], //左侧会诊选择列表
      huiZhenMC: '疑难病例多学科会诊', //会诊名称
      huiZhenTable: [], //左侧会诊单表格
      zhuanKeList: [], //会诊专科列表
      renYuanXZDialog: false, //申请会诊医生弹出框 是否显示
      renYuanXM: '', //申请会诊医生弹出框内 搜索人员姓名
      huiZhenYSList: [], //申请会诊医生弹出框 医生列表
      currentHuiZhenYSList: [],
      currentPage: 1, //申请会诊医生弹出框 当前页号
      pageSize: 13 //申请会诊医生弹出框 页面行数
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    }),
    bingLiID() {
      return this.$route.params.id
    },
    formatDate() {
      return function (date, formatType) {
        if (!date) {
          return ''
        }
        return format(Date.parse(date), formatType)
      }
    },
    currentPageHuiZhenYSList() {
      return this.currentHuiZhenYSList.slice(
        (this.currentPage - 1) * this.pageSize,
        (this.currentPage - 1) * this.pageSize + this.pageSize
      )
    },
    beiZhu() {
      if (this.patientData.huiZhenBZ) {
        return this.initData.huiZhenBZList.find((item) => item.daiMa == this.patientData.huiZhenBZ)
          .beiZhu
      }
      return ''
    }
  },
  watch: {
    yuYueHZSelect: {
      handler(val) {
        if (val.includes('属于疑难病历讨论')) {
          this.patientData.niNanBingTL = '1'
        } else {
          this.patientData.niNanBingTL = '0'
        }
        if (val.includes('邀请医务处参加')) {
          this.patientData.yiWuCJ = '1'
        } else {
          this.patientData.yiWuCJ = '0'
        }
        if (val.includes('邀请医患办参加')) {
          this.patientData.yiHuanCJ = '1'
        } else {
          this.patientData.yiHuanCJ = '0'
        }
      },
      deep: true
    },
    'patientData.zhuanKeYSXXList': {
      handler(val) {
        if (val[val.length - 1].huiZhenZKID != null) {
          val.push({
            huiZhenZKID: null
          })
        }
      },
      deep: true
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    clear() {
      this.huiZhenDanID = 0
      let clearItem = [
        'shenQingYSLXDH',
        'shenQingHZYSID',
        'shenQingHZYSXM',
        'bingQingZY',
        'chuBuZD',
        'shenQingLY',
        'niNanBingTL',
        'yiHuanCJ',
        'yiWuCJ',
        'yuYueHZDD',
        'yuYueHZSJ'
      ]
      clearItem.map((item) => {
        this.patientData[item] = null
      })
      this.$set(this.patientData, 'huiZhenBZ', this.initData.huiZhenBZList[0].daiMa)
      this.$set(this.patientData, 'zhuanKeYSXXList', [
        {
          huiZhenZKID: null
        }
      ])
      this.patientData.messageList = []
      this.yuYueHZSelect = []
    },
    async resetHuiZhenDan() {
      if (this.huiZhenDanID == 0) {
        await this.initPatientData()
      } else {
        let res = await getConsulationData({
          huiZhenDanID: this.huiZhenDanID
        })
        this.patientData = res.data
        if (res.data.consulationDetailLists) {
          this.$set(
            this.patientData,
            'zhuanKeYSXXList',
            res.data.consulationDetailLists.map((item) => {
              return {
                shenQingHZYSID: item.shenQingHZYSID,
                id: item.id,
                huiZhenZKID: item.huiZhenZKID,
                shenQingHZYSXM: item.shenQingHZYSXM
              }
            })
          )
        } else {
          this.$set(this.patientData, 'zhuanKeYSXXList', {
            huiZhenZKID: this.initData.zhuanKeList[0].buMenID,
            id: 0
          })
        }
        this.yuYueHZSelect = []
        if (this.patientData.yiHuanCJ == '1') {
          this.yuYueHZSelect.push('邀请医患办参加')
        }
        if (this.patientData.yiWuCJ == '1') {
          this.yuYueHZSelect.push('邀请医务处参加')
        }
        if (this.patientData.niNanBingTL == '1') {
          this.yuYueHZSelect.push('属于疑难病历讨论')
        }
      }
    },
    async initPatientData() {
      let res = await getAddConsulationInitData({
        zhuanKeID: this.zhuanKeID,
        tiJianBH: '',
        huiZhenBZ: this.huiZhenBZ[0]
      })
      this.initData = res.data
      let res1 = await getPatientofConsulation({
        bingLiID: this.bingLiID,
        huiZhenBZ: this.huiZhenBZ[0],
        moRenZKID: 0
      })
      this.patientData = res1.data
      this.$set(this.patientData, 'huiZhenBZ', this.initData.huiZhenBZList[0].daiMa)
      this.$set(this.patientData, 'shenQingYSLXDH', this.initData.shenQingYSLXDH)
      this.$set(this.patientData, 'messageList', [])
      this.$set(this.patientData, 'zhuanKeYSXXList', [
        {
          huiZhenZKID: this.initData.zhuanKeList[0].buMenID,
          id: 0
        }
      ])
      this.$set(this.yuYueHZSelect, [])
    },
    async initHuiZhenTable() {
      let res = await getConsulationByBLID({
        bingLiID: this.bingLiID
      })
      this.huiZhenTable = res.data.filter((item) => {
        return this.huiZhenBZ.includes(item.huiZhenBZ)
      })
    },
    async init() {
      await this.initHuiZhenTable()
      await this.initPatientData()
    },
    async handleSave() {
      let param = {
        bingLiID: this.bingLiID,
        bingQingZY: this.patientData.bingQingZY,
        chuBuZD: this.patientData.chuBuZD,
        huiZhenBZ: this.patientData.huiZhenBZ,
        huiZhenDanID: this.huiZhenDanID,
        moRenZKID: 0,
        shenQingLY: this.patientData.shenQingLY,
        shenQingYSLXDH: this.patientData.shenQingYSLXDH,
        shenQingZKID: this.zhuanKeID,
        tiJianBH: '',
        tiJianKHLX: '',
        niNanBingTL: this.patientData.niNanBingTL,
        yiHuanCJ: this.patientData.yiHuanCJ,
        yiWuCJ: this.patientData.yiWuCJ,
        yuYueHZDD: this.patientData.yuYueHZDD,
        yuYueHZSJ: this.patientData.yuYueHZSJ,
        zhuanKeYSXXList: this.patientData.zhuanKeYSXXList.slice(
          0,
          this.patientData.zhuanKeYSXXList.length - 1
        )
      }
      console.log(param)
      let res = await newConsulation(param)
      if (res.hasError === 0) {
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        await this.initHuiZhenTable()
      }
    },
    async huiZhenTableClick(row, column, event) {
      this.huiZhenDanID = row.huiZhenDanID
      let res = await getConsulationData({
        huiZhenDanID: row.huiZhenDanID
      })
      this.patientData = res.data
      if (res.data.consulationDetailLists) {
        this.$set(
          this.patientData,
          'zhuanKeYSXXList',
          res.data.consulationDetailLists.map((item) => {
            return {
              shenQingHZYSID: item.shenQingHZYSID,
              id: item.id,
              huiZhenZKID: item.huiZhenZKID,
              shenQingHZYSXM: item.shenQingHZYSXM
            }
          })
        )
      } else {
        this.$set(this.patientData, 'zhuanKeYSXXList', {
          huiZhenZKID: this.initData.zhuanKeList[0].buMenID,
          id: 0
        })
      }
      this.yuYueHZSelect = []
      if (this.patientData.yiHuanCJ == '1') {
        this.yuYueHZSelect.push('邀请医患办参加')
      }
      if (this.patientData.yiWuCJ == '1') {
        this.yuYueHZSelect.push('邀请医务处参加')
      }
      if (this.patientData.niNanBingTL == '1') {
        this.yuYueHZSelect.push('属于疑难病历讨论')
      }
    },
    async queryZhuDaoYS() {
      this.currentHuiZhenYSList = this.huiZhenYSList.filter((item) => {
        if (
          (item.pinYin != null && item.pinYin.indexOf(this.renYuanXM.toUpperCase()) === 0) ||
          (item.wuBi != null && item.wuBi.indexOf(this.renYuanXM.toUpperCase()) === 0) ||
          (item.zhongShenDM != null && item.zhongShenDM?.indexOf(this.renYuanXM) === 0)
        ) {
          return true
        }
      })
    },
    async handleSelectRY(item) {
      this.currentHZItem.shenQingHZYSXM = item.yiShengXM
      this.currentHZItem.shenQingHZYSID = item.yongHuID
      this.renYuanXZDialog = false
    },
    async shenQingHZYS(item) {
      if (item.huiZhenZKID == null) {
        return
      }
      this.renYuanXZDialog = true
      let res = await getDoctorOfConsulation({
        huiZhenBZ: this.patientData.huiZhenBZ,
        huiZhenZKID: item.huiZhenZKID
      })
      this.huiZhenYSList = this.currentHuiZhenYSList = res.data.consulationDoctorList
      this.currentHZItem = item
    },
    changeHZZK(index) {
      this.patientData.zhuanKeYSXXList[index].shenQingHZYSXM = null
      this.patientData.zhuanKeYSXXList[index].shenQingHZYSID = null
    },
    async sendMessage() {
      await sendConsulationMessageSP({
        huiZhenDanID: this.huiZhenDanID
      })
      let res = await getConsulationData({
        huiZhenDanID: this.huiZhenDanID
      })
      this.patientData = res.data
    },
    huiZhenXZDetail() {
      console.log(123)
    },
    handleCheckedYuYueHZChange(val) {
      console.log(val)
      console.log(this.yuYueHZSelect)
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
}
.el-checkbox {
  margin-right: 8px;
}
.select-input {
  ::v-deep .el-input__inner {
    background-color: #e4ecfb;
  }
}
.Dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
    .renYuanXMInput {
      width: 43%;
      margin-right: 10px;
      .el-input__inner {
        padding: 0 6px;
        height: 24px;
        font-size: 13px;
      }
    }
    .renYuanList {
      margin-top: 20px;
      width: 100%;
      thead {
        background-color: #eaf0f9;
      }
      td {
        border: 1px solid #dcdfe6;
        border-collapse: collapse; /* 移除表格内边框间的间隙 */
        padding: 6px;
        font-size: 13px;
      }
      tbody {
        tr:nth-child(even) {
          background-color: #eaf0f9;
        }
        tr:nth-child(odd) {
          background-color: #f6f6f6;
        }
      }
    }
    .bottom-btn {
      margin-top: 30px;
      flex-direction: row-reverse;
      .el-button {
        margin-left: 10px;
      }
    }
  }
  .shouShuHeader {
    padding: 6px 10px;
    border: 1px solid #dcdfe6;
  }
  .shouShuTable {
    padding: 12px;
    border: 1px solid #dcdfe6;
    border-top: none;
    position: relative;
    .pagination {
      position: absolute;
      bottom: 10px;
      right: 10px;
    }
  }
  .shouShuXZList {
    height: 400px;
    tr {
      height: 50px;
    }
  }
  .yiXuanZeShouShuList {
    height: 242px;
    ::v-deep th {
      .el-checkbox {
        display: none;
      }
    }
  }
  .shouShuXZHeader {
    justify-content: space-between;
  }
  .yiXuanZeSSHeader {
    margin-top: 10px;
  }
}
.clinical-pharmacy-view {
  background-color: #fff;
  height: 100%;
  ::v-deep .el-input__inner {
    padding: 0 8px;
  }
  ::v-deep .el-date-editor .el-input__inner {
    padding: 0 0 0 30px;
  }
  ::v-deep .el-descriptions__title {
    font-size: 13px;
  }
  ::v-deep .el-descriptions__title::before {
    font-weight: 600;
    border-left: 3px solid #356ac5;
    display: inline;
  }
  .title {
    font-weight: 600;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    text-align: left;
  }
  .left-aside {
    padding: 10px;
    ::v-deep .el-input__inner {
      height: 26px !important;
      width: 180px !important;
    }
    ::v-deep .el-table .el-table__cell {
      padding: 6px 0;
      font-size: 12px;
    }
  }
  .right-main {
    padding: 10px 10px 10px 0;
    .box {
      // border: 1px solid #dcdfe6;
      padding: 10px 10px 0 10px;
      height: 94%;

      .content::-webkit-scrollbar {
        display: none;
      }
      .content-box {
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        padding: 10px 10px 0 10px;
        height: 100%;
        ::v-deep .content {
          overflow: scroll;
          height: 95%;
          td,
          th {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 8px !important;
            font-size: 12px;
          }
          th {
            text-align: right;
            color: #000;
            background-color: #eaf0f9;
            width: 12%;
          }
          td {
            background-color: #f5f8fc;
            width: 18%;
          }
          .information {
            margin-bottom: 8px;
          }
          .Application-information {
            margin-bottom: 0px;
            .patient-label {
              vertical-align: top;
              .underline-content {
                text-decoration: underline;
                text-underline-offset: 2px;
                color: #155bd4;
                cursor: pointer;
              }
            }
            .patient-content {
              padding: 5px 6px !important;
            }
          }
          .right-aside {
            width: 252px !important;
          }
        }
        .tips {
          display: flex;
          align-items: center;
          height: 5%;
          font-size: 12px;
          font-weight: 600;
        }
      }
    }
  }
}
</style>
