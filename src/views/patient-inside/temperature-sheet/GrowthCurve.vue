<template>
  <div class="temperature-sheet-view">
    <el-container>
      <el-aside class="left-aside">
        <div class="left">
          <div class="left-header">
            <span class="title">生长发育曲线列表</span>
          </div>
          <div style="height: 96%">
            <el-table style="height: 100%" :data="growthRecords" stripe border size="mini">
              <el-table-column prop="chuShengTL" label="出生胎龄" width="80"></el-table-column>
              <el-table-column prop="dangQianTL" label="当前胎龄" width="80"></el-table-column>
              <el-table-column prop="tiZhong" label="体重" width="60"></el-table-column>
              <el-table-column prop="xiuGaiSJ" label="操作时间" width="135"></el-table-column>
              <el-table-column label="操作" align="center" width="120">
                <template #default="{ row, $index }">
                  <el-button
                    type="text"
                    class="button-with-divider"
                    @click="handleWenkClick(row, $index)"
                  >
                    编辑
                  </el-button>
                  <el-button
                    type="text"
                    class="button-with-divider"
                    @click="deleteGrowthPop(row, $index)"
                  >
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-aside>
      <el-aside class="right-aside">
        <div class="right">
          <div class="right-top">
            <div class="right-header">
              <span class="title">生长发育曲线</span>
            </div>
            <div>
              <el-button
                type="primary"
                style="padding: 10px 20px"
                @click="insertGrowthDevelopmentRecord"
              >
                保存
              </el-button>
            </div>
          </div>
          <div class="right-middle">
            <table class="right-table">
              <tr>
                <td class="right-table-title">出生胎龄(周):</td>
                <td class="right-table-content">
                  <el-select v-model="addItem.chuShengTL" filterable placeholder="请选择">
                    <el-option
                      v-for="item in chuShengDM"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </td>
                <td class="right-table-title">当前胎龄(周):</td>
                <td class="right-table-content">
                  <el-input
                    type="number"
                    v-model="addItem.dangQianTL"
                    class="select-input"
                    maxlength="5"
                  ></el-input>
                </td>
                <td class="right-table-title">体重(克):</td>
                <td class="right-table-content">
                  <el-input v-model="addItem.tiZhong" class="select-input" maxlength="5"></el-input>
                </td>
              </tr>
            </table>
            <div ref="chart" style="width: 100%; height: 550px; margin-top: 30px"></div>
          </div>
        </div>
      </el-aside>
    </el-container>

    <!-- 删除生长曲线弹框 -->
    <div class="pop-class">
      <el-dialog :visible.sync="delVisible" width="350px" :close-on-click-modal="false">
        <span slot="title">
          <span style="font-size: 16px">
            <i class="el-icon-warning"></i>
            <span style="font-weight: bold">提示信息</span>
          </span>
        </span>
        <div class="delete-component">是否确认删除？</div>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="deleteGrowthDevelopmentRecord">确认</el-button>
          <el-button @click="delVisible = false">取消</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 生长发育曲线 -->
    <div class="growth-class">
      <el-dialog
        :visible.sync="editVisible"
        title="生长发育曲线"
        width="420px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <table class="right-table">
          <tr>
            <td class="right-table-title">出生胎龄(周):</td>
            <td class="right-table-content">
              <el-select v-model="weekItem.chuShengTL" filterable placeholder="请选择">
                <el-option
                  v-for="item in chuShengDM"
                  :key="item.daiMa"
                  :label="item.mingCheng"
                  :value="item.daiMa"
                ></el-option>
              </el-select>
            </td>
          </tr>
          <tr>
            <td class="right-table-title">当前胎龄(周):</td>
            <td class="right-table-content">
              <el-input
                type="number"
                v-model="weekItem.dangQianTL"
                class="select-input"
                maxlength="5"
              ></el-input>
            </td>
          </tr>

          <tr>
            <td class="right-table-title">体重(克):</td>
            <td class="right-table-content">
              <el-input v-model="weekItem.tiZhong" class="select-input" maxlength="5"></el-input>
            </td>
          </tr>
        </table>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="updateGrowthDevelopmentRecord">保存</el-button>
          <el-button @click="editVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getGrowthDevelopmentRecordListByBingLiID,
  insertGrowthDevelopmentRecord,
  deleteGrowthDevelopmentRecord,
  updateGrowthDevelopmentRecord
} from '@/api/growth-curve'
import { format } from 'date-fns'
import { mapState } from 'vuex'
import * as echarts from 'echarts'
export default {
  data() {
    return {
      sex: 1, //患者性别
      addItem: {}, //新增生长曲线编辑框
      editVisible: false, //编辑判断值
      weekItem: {}, //修改生长曲线编辑框
      delVisible: false, //删除判断值
      growthRecords: [], // 表格数据
      dqtlOptions: [
        18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
        41, 42, 43, 44
      ], // 胎龄选项
      chuShengDM: [
        { daiMa: 18, mingCheng: 18 },
        { daiMa: 19, mingCheng: 19 },
        { daiMa: 20, mingCheng: 20 },
        { daiMa: 21, mingCheng: 21 },
        { daiMa: 22, mingCheng: 22 },
        { daiMa: 23, mingCheng: 23 },
        { daiMa: 24, mingCheng: 24 },
        { daiMa: 25, mingCheng: 25 },
        { daiMa: 26, mingCheng: 26 },
        { daiMa: 27, mingCheng: 27 },
        { daiMa: 28, mingCheng: 28 },
        { daiMa: 29, mingCheng: 29 },
        { daiMa: 30, mingCheng: 30 },
        { daiMa: 31, mingCheng: 31 },
        { daiMa: 32, mingCheng: 32 },
        { daiMa: 33, mingCheng: 33 },
        { daiMa: 34, mingCheng: 34 },
        { daiMa: 35, mingCheng: 35 },
        { daiMa: 36, mingCheng: 36 },
        { daiMa: 37, mingCheng: 37 },
        { daiMa: 38, mingCheng: 38 },
        { daiMa: 39, mingCheng: 39 },
        { daiMa: 40, mingCheng: 40 },
        { daiMa: 41, mingCheng: 41 },
        { daiMa: 42, mingCheng: 42 },
        { daiMa: 43, mingCheng: 43 },
        { daiMa: 44, mingCheng: 44 }
      ] //出生胎龄选择范围
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
      // return 2613948
    }
  },
  async mounted() {
    await this.getGrowthDevelopmentRecordListByBingLiID()
    this.sex = this.patientDetail.xingBie
  },
  methods: {
    // 渲染图表曲线
    async initChart() {
      const chart = echarts.init(this.$refs.chart)
      const option = {
        title: {
          text: '生长发育曲线',
          left: 'center',
          top: '0%',
          textStyle: {
            fontSize: 16
          }
        },
        tooltip: { trigger: 'axis' },
        color: [
          '#5470C6',
          '#91CC75',
          '#FAC858',
          '#EE6666',
          '#73C0DE',
          '#3BA272',
          '#FC8452',
          '#9A60B4'
        ],
        legend: {
          data:
            this.sex == 1
              ? ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97', '实际体重']
              : ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', '实际体重'],
          top: '6%',
          left: 'center'
        },
        dataZoom: [
          {
            type: 'slider',
            show: false,
            handleSize: 8,
            filterMode: 'filter'
          },
          {
            type: 'inside',
            start: 94,
            end: 100
          },
          {
            type: 'slider',
            show: true,
            yAxisIndex: 0,
            filterMode: 'empty',
            handleSize: 8,
            showDataShadow: true
          }
        ],
        xAxis: {
          type: 'category',
          data: this.dqtlOptions,
          name: '出生胎龄(周)',
          boundaryGap: false,
          axisLabel: {
            textStyle: {
              color: '#707070'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '胎儿体重(克)',
          min: 0,
          axisLine: { show: false },
          axisLabel: {
            textStyle: {
              color: '#707070'
            }
          }
        },
        series: this.generateChartSeries()
      }
      chart.setOption(option)
    },
    // 生成图表数据系列
    generateChartSeries() {
      const manData = [
        {
          name: 'P3',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            356,
            444,
            534,
            628,
            724,
            825,
            935,
            1059,
            1205,
            1376,
            1576,
            1803,
            2053,
            2308,
            2515,
            2643,
            2723,
            2784,
            2839,
            '',
            ''
          ]
        },
        {
          name: 'P10',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            434,
            538,
            645,
            753,
            865,
            980,
            1105,
            1244,
            1404,
            1590,
            1801,
            2035,
            2289,
            2543,
            2749,
            2877,
            2959,
            3021,
            3077,
            '',
            ''
          ]
        },
        {
          name: 'P25',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            520,
            642,
            765,
            890,
            1017,
            1147,
            1286,
            1440,
            1614,
            1814,
            2036,
            2279,
            2536,
            2790,
            2877,
            2993,
            3203,
            3266,
            3323,
            '',
            ''
          ]
        },
        {
          name: 'P50',
          type: 'line',
          stack: 'Total',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            624,
            766,
            909,
            1053,
            1196,
            1343,
            1497,
            1666,
            1857,
            2071,
            2306,
            2558,
            2820,
            3073,
            3273,
            3399,
            3482,
            3545,
            3602,
            '',
            ''
          ]
        },
        {
          name: 'P75',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            737,
            901,
            1064,
            1226,
            1387,
            1549,
            1718,
            1902,
            2108,
            2337,
            2585,
            2847,
            3114,
            3366,
            3562,
            3685,
            3767,
            3830,
            3887,
            '',
            ''
          ]
        },
        {
          name: 'P90',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            846,
            1031,
            1212,
            1390,
            1566,
            1742,
            1925,
            2122,
            2341,
            2584,
            2843,
            3114,
            3386,
            3637,
            3828,
            3949,
            4030,
            4092,
            4148,
            '',
            ''
          ]
        },
        {
          name: 'P97',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            962,
            1166,
            1366,
            1561,
            1752,
            1941,
            2136,
            2346,
            2578,
            2830,
            3104,
            3384,
            3662,
            3912,
            4098,
            4215,
            4294,
            4355,
            4410,
            '',
            ''
          ]
        }
      ]
      const womanData = [
        {
          name: 'P3',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            304,
            395,
            487,
            582,
            680,
            781,
            890,
            1012,
            1152,
            1314,
            1503,
            1719,
            1960,
            2204,
            2409,
            2543,
            2623,
            2681,
            2731,
            '',
            ''
          ]
        },
        {
          name: 'P10',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            359,
            466,
            575,
            686,
            799,
            917,
            1042,
            1181,
            1338,
            1518,
            1722,
            1951,
            2197,
            2439,
            2640,
            2770,
            2849,
            2905,
            2945,
            '',
            ''
          ]
        },
        {
          name: 'P25',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            425,
            550,
            677,
            806,
            936,
            1070,
            1212,
            1367,
            1541,
            1737,
            1955,
            2193,
            2445,
            2685,
            2879,
            3006,
            3083,
            3138,
            3185,
            '',
            ''
          ]
        },
        {
          name: 'P50',
          type: 'line',
          stack: 'Total',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            513,
            662,
            811,
            960,
            1109,
            1261,
            1419,
            1591,
            1782,
            1993,
            2225,
            2472,
            2727,
            2964,
            3153,
            3275,
            3349,
            3402,
            3448,
            '',
            ''
          ]
        },
        {
          name: 'P75',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            622,
            796,
            968,
            1138,
            1306,
            1474,
            1648,
            1835,
            2039,
            2264,
            2506,
            2760,
            3018,
            3251,
            3433,
            3550,
            3621,
            3673,
            3717,
            '',
            ''
          ]
        },
        {
          name: 'P90',
          type: 'line',
          data: [
            '',
            '',
            '',
            '',
            '',
            '',
            740,
            939,
            1132,
            1321,
            1504,
            1686,
            1872,
            2071,
            2285,
            2519,
            2768,
            3028,
            3286,
            3515,
            3691,
            3803,
            3872,
            3921,
            3963,
            '',
            ''
          ]
        }
      ]
      let standardData = this.sex == 1 ? manData : womanData

      // 实际数据
      let chart_list = new Array(27).fill('')
      this.growthRecords.forEach((item) => {
        const index = parseInt(item.dangQianTL) - 18
        if (index >= 0 && index < 27) {
          chart_list[index] = item.tiZhong
        }
      })
      const actualData = {
        name: '实际体重',
        type: 'scatter',
        symbolSize: 10,
        data: chart_list
      }
      return [...standardData, actualData]
    },
    // 根据病例ID获取基础信息
    async getGrowthDevelopmentRecordListByBingLiID() {
      try {
        const res = await getGrowthDevelopmentRecordListByBingLiID(this.bingLiID)
        if (res.hasError === 0) {
          this.growthRecords = res.data.map((item) => ({
            ...item,
            xiuGaiSJ: format(new Date(item.xiuGaiSJ), 'yyyy-MM-dd HH:mm')
          }))
          this.initChart()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 新增生长发育曲线记录
    async insertGrowthDevelopmentRecord() {
      try {
        const res = await insertGrowthDevelopmentRecord({
          bingLiID: this.bingLiID,
          chuShengTL: this.addItem.chuShengTL,
          dangQianTL: this.addItem.dangQianTL,
          tiZhong: this.addItem.tiZhong
        })
        if (res.hasError === 0) {
          this.$message({
            message: '新增成功',
            type: 'success'
          })
          this.addItem = {}
          this.getGrowthDevelopmentRecordListByBingLiID()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 删除生长曲线弹框
    deleteGrowthPop(row) {
      this.weekItem = Object.assign({}, row)
      this.delVisible = true
    },
    // 删除生长发育曲线记录
    async deleteGrowthDevelopmentRecord() {
      try {
        const res = await deleteGrowthDevelopmentRecord(this.weekItem.id)
        if (res.hasError === 0) {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.delVisible = false
          this.getGrowthDevelopmentRecordListByBingLiID()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 点击生长曲线某一出生胎龄
    handleWenkClick(row) {
      this.weekItem = Object.assign({}, row)
      this.editVisible = true
    },

    // 更新生长发育曲线记录
    async updateGrowthDevelopmentRecord() {
      try {
        const res = await updateGrowthDevelopmentRecord(this.weekItem.id, {
          chuShengTL: this.weekItem.chuShengTL,
          dangQianTL: this.weekItem.dangQianTL,
          tiZhong: this.weekItem.tiZhong
        })
        if (res.hasError === 0) {
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.editVisible = false
          this.getGrowthDevelopmentRecordListByBingLiID()
        }
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.temperature-sheet-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;

  .left-aside {
    width: 30% !important;
    margin-right: 10px;
  }

  .left {
    background-color: #eff3fb;
    border-radius: 4px;
    padding: 12px;
    position: relative;
    height: 100%;
    overflow: hidden;

    .left-tips {
      display: flex;
      font-size: 14px;
      padding: 6px 10px;
      border: 1px solid #a2bae5;
      background-color: #e9f1ff;

      position: sticky; // 使用 sticky 定位
      top: 0; // 距离容器顶部0px
      z-index: 10; // 适当层级
      background-color: #e9f1ff; // 保持背景色
      margin-bottom: 10px; // 保持原有间距

      .el-icon-warning {
        font-size: 16px;
        color: #356ac5;
        margin-right: 4px;
        margin-top: -2px;
      }

      .el-icon-title {
        width: 60px;
      }

      .el-icon-content {
        width: 130%;
        line-height: 20px;
        margin-top: -3px;
      }
    }

    .left-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      .title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }

      .title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }

      ::v-deep .el-button {
        padding: 6px 10px;
        background-color: #3b76ef;
      }
    }
  }
}

.right-aside {
  width: 69.3% !important;

  .right {
    background-color: #eff3fb;
    border-radius: 4px;
    padding: 12px;
    position: relative;
    height: 100%;

    .right-top {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .right-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 10px 0;

        .title {
          position: relative;
          color: #171c28;
          font-size: 14px;
          line-height: 14px;
          font-weight: bold;
          margin-left: 9px;
        }

        .title::before {
          position: absolute;
          left: -9px;
          width: 3px;
          height: 14px;
          content: '';
          background-color: #356ac5;
        }

        ::v-deep .el-button {
          padding: 6px 10px;
          background-color: #3b76ef;
        }
      }
    }

    .right-middle {
      font-size: 14px;
      margin: 15px 0;
    }

    .right-table {
      text-align: left;
      table-layout: fixed;
      // height: 100%;
      td {
        border: 1px solid #dcdfe6;
        padding: 4px 6px;
        // width: 13%;
        overflow: hidden;
        box-sizing: border-box;
      }
      .right-table-title {
        text-align: right;
        background-color: #eff3fb;
        width: 12%;
        height: 100%;
        line-height: 16px;
      }
      .right-table-content {
        background-color: #ffffff;
        // width: 13%;
        height: 40px;
        ::v-deep .el-input__inner {
          width: 100%;
          min-width: 0;
          // background-color: #e4ecfb;
          // height: 24px;
        }
        .el-checkbox {
          margin-right: 8px;
        }
      }
      .right-table-footer-tip {
        padding: 0 0 10px 0;
        background-color: #eaf0f9;
      }
      .select-input {
        ::v-deep .el-input__inner {
          // background-color: #e4ecfb;
          // height: 24px;
        }
      }
    }
  }

  .temperature-sheet-box {
    background: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .twd-data-info {
      zoom: 1.94;
      .hospital-title {
        text-align: center;
        padding: 30px 0 16px;

        .hospital-name {
          font-size: 26px;
          font-weight: bold;
          color: black;
          font-family: '楷体';
          text-align: center;
        }
      }

      .record-type {
        font-size: 16px;
      }

      .patient-info-row {
        font-size: 16px;
      }

      .info-value {
        font-family: '楷体';
      }
    }
  }
}

// 删除生长曲线弹框
.pop-class {
  .el-icon-warning {
    font-size: 20px;
    color: #ed6a0c;
  }

  .delete-component {
    padding-left: 35px;
    color: #96999e;

    a {
      color: #356ac5;
      font-weight: bold;
    }
  }

  ::v-deep .el-dialog__footer {
    padding: 15px;
    padding-top: 10px;
  }
}

// 生长发育曲线
.growth-class {
  .right-table {
    text-align: left;
    table-layout: fixed;
    td {
      border: 1px solid #dcdfe6;
      padding: 4px 6px;
      // width: 13%;
      overflow: hidden;
      box-sizing: border-box;
    }
    .right-table-title {
      text-align: right;
      background-color: #eff3fb;
      width: 37%;
      height: 100%;
      line-height: 16px;
    }
    .right-table-content {
      background-color: #ffffff;
      // width: 13%;
      height: 40px;
      ::v-deep .el-input__inner {
        width: 100%;
        min-width: 0;
        // background-color: #e4ecfb;
        // height: 24px;
      }
    }
    .right-table-footer-tip {
      padding: 0 0 10px 0;
      background-color: #eaf0f9;
    }
  }

  ::v-deep .el-dialog {
    padding: 6px 10px;
  }

  ::v-deep .el-dialog__header {
    padding: 10px 14px;
    font: var(--font-medium);
    font-size: var(--font-size-medium);
    color: #171c28;
    display: flex;
    align-items: center;
  }

  ::v-deep .el-dialog__header::before {
    // content: url('~@/assets/images/info.png');
    // // width: 3px;
    // // height: 16px;
    // // background: #356ac5;
    // // margin-right: 6px;
    // position: absolute;
    // top: 7px;
    // left: 6px;
    // transform: scale(0.55);
    position: absolute;
    left: 22px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }

  ::v-deep .el-dialog__title {
    font-weight: bold;
    margin-left: 10px;
    opacity: 0.8;
  }
}

::v-deep .el-table__cell {
  padding-left: 2px;
}
</style>
