<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">新增食源性报告卡</div>
        <div>
          <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
          <el-button v-if="patient" type="primary" @click="sendData">返回上一层</el-button>
        </div>
      </div>
      <div class="content" style="border: #dadee5 1px solid; overflow: auto">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <div class="content-header">
            <div class="title">基础信息</div>
          </div>
          <div class="el-form">
            <table>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>姓名：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="huanZheXM">
                    <el-input v-model="ruleForm.huanZheXM"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>性别：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="huanZheXB">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.huanZheXB">
                        <el-radio
                          v-for="data in baseInfo.xingBie"
                          :key="data.daiMa"
                          :label="data.daiMa"
                        >
                          {{ data.neiRong }}
                        </el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>出生日期：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="chuShengRQ">
                    <el-date-picker
                      v-model="ruleForm.chuShengRQ"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>发病时间：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="faBingSJ">
                    <el-date-picker
                      v-model="ruleForm.faBingSJ"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>门诊号：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="menZhenHao">
                    <el-input v-model="ruleForm.menZhenHao"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>就诊时间：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="jiuZhenSJ">
                    <el-date-picker
                      v-model="ruleForm.jiuZhenSJ"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>是否住院：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="shiFouZY">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.shiFouZY">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span>家属姓名：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="jianHuRXM">
                    <el-input v-model="ruleForm.jianHuRXM"></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>职业：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="huanZheZY">
                    <el-select v-model="ruleForm.huanZheZY">
                      <el-option
                        v-for="item in baseInfo.zhiYe"
                        :key="item.mingCheng"
                        :label="item.mingCheng"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>联系电话：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="lianXiDH">
                    <el-input v-model="ruleForm.lianXiDH"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>工作单位：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="gongZuoDW">
                    <el-input v-model="ruleForm.gongZuoDW"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span>死亡时间：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="siWangSJ">
                    <el-date-picker
                      v-model="ruleForm.siWangSJ"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>所属地区：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value2">
                    <el-form-item prop="huanZheSY">
                      <div class="value">
                        <el-radio-group v-model="ruleForm.huanZheSY">
                          <el-radio
                            v-for="data in baseInfo.diZhiLX"
                            :key="data.daiMa"
                            :label="data.daiMa"
                          >
                            {{ data.neiRong }}
                          </el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>目前居住地址：</span>
                </td>
                <td class="info-value" colspan="7">
                  <el-form-item prop="xianZhuSF" class="cz-select">
                    <el-form-item prop="xianZhuSF">
                      <el-select
                        v-model="ruleForm.xianZhuSF"
                        placeholder="-省份-"
                        @change="setAddress($event, 1, 'xianZhu')"
                      >
                        <el-option
                          v-for="item in addressMapList['xianZhu'].SF"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="xianZhuSJ">
                      <el-select
                        v-model="ruleForm.xianZhuSJ"
                        placeholder="-市级-"
                        @change="setAddress($event, 2, 'xianZhu')"
                      >
                        <el-option
                          v-for="item in addressMapList['xianZhu'].SJ"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="xianZhuQX">
                      <el-select
                        v-model="ruleForm.xianZhuQX"
                        placeholder="-区县-"
                        @change="setAddress($event, 3, 'xianZhu')"
                      >
                        <el-option
                          v-for="item in addressMapList['xianZhu'].QX"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="xianZhuJD">
                      <el-select v-model="ruleForm.xianZhuJD" placeholder="-街道-">
                        <el-option
                          v-for="item in addressMapList['xianZhu'].JD"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="xianZhuXXDZ">
                      <el-input v-model="ruleForm.xianZhuXXDZ" placeholder="-详细地址-"></el-input>
                    </el-form-item>
                  </el-form-item>
                </td>
              </tr>
            </table>
          </div>
          <div class="content-header" style="margin-top: 14px">
            <div class="title">暴露信息</div>
          </div>
          <div class="el-form">
            <el-form ref="formData" :model="formData" :rules="rules2">
              <table>
                <tr>
                  <td class="info-label" colspan="8">
                    <el-table :data="tableData" border size="medium" style="width: 100%">
                      <el-table-column type="index" label="序号" width="50"></el-table-column>
                      <el-table-column prop="shiPinMC" label="食品名称"></el-table-column>
                      <el-table-column label="食品分类">
                        <template #default="scope">
                          {{ getMapData('shipinFL', scope.row.shiPinFL) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="加工或包装方式">
                        <template #default="scope">
                          {{ getMapData('jiaGongFS', scope.row.jiaGongHBZFS) }}
                        </template>
                      </el-table-column>
                      <el-table-column label="进食场所类型">
                        <template #default="scope">
                          {{ getMapData('changSuoLX', scope.row.jinShiCSLX) }}
                        </template>
                      </el-table-column>
                      <el-table-column prop="jinShiCS" label="进食场所"></el-table-column>
                      <el-table-column prop="shiPinPP" label="食品品牌"></el-table-column>
                      <el-table-column prop="jinShiSJ" label="进食时间"></el-table-column>
                      <el-table-column prop="jinShiRS" label="进食人数"></el-table-column>
                      <el-table-column label="操作" align="center">
                        <template #default="scope">
                          <el-button
                            type="text"
                            size="mini"
                            @click="handleDelete(scope.$index, scope.row)"
                          >
                            删除
                          </el-button>
                          <el-divider direction="vertical"></el-divider>
                          <el-button
                            type="text"
                            size="mini"
                            @click="handleEdit(scope.$index, scope.row)"
                          >
                            编辑
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span style="color: red">*</span>
                    <span>食品名称：</span>
                  </td>
                  <td class="info-value">
                    <el-form-item prop="shiPinMC">
                      <el-input v-model="formData.shiPinMC"></el-input>
                    </el-form-item>
                  </td>
                  <td class="info-label">
                    <span style="color: red">*</span>
                    <span>食品分类：</span>
                  </td>
                  <td class="info-value">
                    <el-form-item prop="shiPinFL">
                      <el-select v-model="formData.shiPinFL">
                        <el-option
                          v-for="item in baseInfo.shipinFL"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </td>
                  <td class="info-label">
                    <span style="color: red">*</span>
                    <span>加工及包装方式：</span>
                  </td>
                  <td class="info-value">
                    <el-form-item prop="jiaGongHBZFS">
                      <el-select v-model="formData.jiaGongHBZFS">
                        <el-option
                          v-for="item in baseInfo.jiaGongFS"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </td>
                  <td class="info-label">
                    <span>食品品牌：</span>
                  </td>
                  <td class="info-value">
                    <el-form-item prop="shiPinPP">
                      <el-input v-model="formData.shiPinPP"></el-input>
                    </el-form-item>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span style="color: red">*</span>
                    <span>进食时间：</span>
                  </td>
                  <td class="info-value">
                    <el-form-item prop="jinShiSJ">
                      <el-date-picker
                        v-model="formData.jinShiSJ"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        type="datetime"
                        placeholder="选择日期"
                      ></el-date-picker>
                    </el-form-item>
                  </td>
                  <td class="info-label">
                    <span>生产厂家：</span>
                  </td>
                  <td class="info-value">
                    <el-form-item prop="shengChanCJ">
                      <el-input v-model="formData.shengChanCJ"></el-input>
                    </el-form-item>
                  </td>
                  <td class="info-label">
                    <span style="color: red">*</span>
                    <span>进食人数：</span>
                  </td>
                  <td class="info-value">
                    <el-form-item prop="jinShiRS">
                      <el-input v-model="formData.jinShiRS"></el-input>
                    </el-form-item>
                  </td>
                  <td class="info-label">
                    <span style="color: red">*</span>
                    <span>其他人是否发病：</span>
                  </td>
                  <td class="info-value">
                    <div class="value">
                      <el-radio-group v-model="formData.qiTaRSFFB">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                        <el-radio label="2">未知</el-radio>
                      </el-radio-group>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span style="color: red">*</span>
                    <span>购买地点：</span>
                  </td>
                  <td class="info-value" colspan="5">
                    <div class="cz-select">
                      <el-form-item prop="gouMaiSF">
                        <el-select
                          v-model="formData.gouMaiSF"
                          placeholder="-省份-"
                          @change="setAddress($event, 1, 'gouMai')"
                        >
                          <el-option
                            v-for="item in addressMapList['gouMai'].SF"
                            :key="item.daiMa"
                            :label="item.mingCheng"
                            :value="item.daiMa"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item prop="gouMaiSJ">
                        <el-select
                          v-model="formData.gouMaiSJ"
                          placeholder="-市级-"
                          @change="setAddress($event, 2, 'gouMai')"
                        >
                          <el-option
                            v-for="item in addressMapList['gouMai'].SJ"
                            :key="item.daiMa"
                            :label="item.mingCheng"
                            :value="item.daiMa"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item prop="gouMaiQX">
                        <el-select
                          v-model="formData.gouMaiQX"
                          placeholder="-区县-"
                          @change="setAddress($event, 3, 'gouMai')"
                        >
                          <el-option
                            v-for="item in addressMapList['gouMai'].QX"
                            :key="item.daiMa"
                            :label="item.mingCheng"
                            :value="item.daiMa"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item prop="gouMaiXXDZ">
                        <el-input v-model="formData.gouMaiXXDZ" placeholder="-详细地址-"></el-input>
                      </el-form-item>
                      <span>类型：</span>
                      <el-select v-model="formData.gouMaiCSLX" style="flex: 1; margin-right: 0">
                        <el-option
                          v-for="item in baseInfo.changSuoLX"
                          :key="item.daiMa"
                          :label="item.neiRong"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                  </td>
                  <td class="info-label">
                    <span>是否采集食物样品：</span>
                  </td>
                  <td class="info-value">
                    <div class="value">
                      <el-radio-group v-model="formData.shiFouCY">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                      </el-radio-group>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td class="info-label">
                    <span style="color: red">*</span>
                    <span>进食场所：</span>
                  </td>
                  <td class="info-value" colspan="5">
                    <div class="cz-select">
                      <el-form-item prop="jinSSF">
                        <el-select
                          v-model="formData.jinSSF"
                          placeholder="-省份-"
                          @change="setAddress($event, 1, 'jinS')"
                        >
                          <el-option
                            v-for="item in addressMapList['jinS'].SF"
                            :key="item.daiMa"
                            :label="item.mingCheng"
                            :value="item.daiMa"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item prop="jinSSJ">
                        <el-select
                          v-model="formData.jinSSJ"
                          placeholder="-市级-"
                          @change="setAddress($event, 2, 'jinS')"
                        >
                          <el-option
                            v-for="item in addressMapList['jinS'].SJ"
                            :key="item.daiMa"
                            :label="item.mingCheng"
                            :value="item.daiMa"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item prop="jinSQX">
                        <el-select
                          v-model="formData.jinSQX"
                          placeholder="-区县-"
                          @change="setAddress($event, 3, 'jinS')"
                        >
                          <el-option
                            v-for="item in addressMapList['jinS'].QX"
                            :key="item.daiMa"
                            :label="item.mingCheng"
                            :value="item.daiMa"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                      <el-form-item prop="jinShiXXDZ">
                        <el-input v-model="formData.jinShiXXDZ" placeholder="-详细地址-"></el-input>
                      </el-form-item>
                      <span>类型：</span>
                      <el-select v-model="formData.jinShiCSLX" style="flex: 1; margin-right: 0">
                        <el-option
                          v-for="item in baseInfo.changSuoLX"
                          :key="item.daiMa"
                          :label="item.neiRong"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </div>
                  </td>
                  <td class="info-label" colspan="2"></td>
                </tr>
                <tr>
                  <td class="info-label" colspan="8">
                    <div style="float: right">
                      <span>
                        其他食品，混合食品，多种食品具体分类可将鼠标放到“食品分类”上即可查看
                      </span>
                      <el-button v-if="xgIndex === -1" style="color: #356ac5" @click="setBLxx">
                        添加
                      </el-button>
                      <el-button v-else style="color: #356ac5" @click="setBLxx">保存修改</el-button>
                    </div>
                  </td>
                </tr>
              </table>
            </el-form>
          </div>
          <div class="content-header" style="margin-top: 14px">
            <div class="title">病例检查信息</div>
          </div>
          <div class="el-form">
            <table>
              <tr>
                <td class="info-label" rowspan="4">
                  <span style="color: red">*</span>
                  <span>全身症状与体征：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.quanShenZZYTZ">
                      <el-checkbox
                        v-for="data in baseInfo.quanShenZZ.slice(0, 7)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '1'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.faReDS"
                            style="width: 50px"
                            :disabled="!ruleForm.quanShenZZYTZ.includes('1')"
                          ></el-input>
                          °C
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.quanShenZZYTZ">
                      <el-checkbox
                        v-for="data in baseInfo.quanShenZZ.slice(7, 14)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        {{ data.mingCheng }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.quanShenZZYTZ">
                      <el-checkbox
                        v-for="data in baseInfo.quanShenZZ.slice(14, 21)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        {{ data.mingCheng }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.quanShenZZYTZ">
                      <el-checkbox
                        v-for="data in baseInfo.quanShenZZ.slice(21)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '23'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.zhengZhuangYTZQT"
                            style="width: 200px"
                            :disabled="!ruleForm.quanShenZZYTZ.includes('23')"
                          ></el-input>
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label" rowspan="3">
                  <span style="color: red">*</span>
                  <span>消化系统：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.xiaoHuaXT">
                      <el-checkbox
                        v-for="data in baseInfo.xiaoHuaXT.slice(0, 3)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '2'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.ouTuCS"
                            style="width: 50px"
                            :disabled="!ruleForm.xiaoHuaXT.includes('2')"
                          ></el-input>
                          次/天
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.xiaoHuaXT">
                      <el-checkbox
                        v-for="data in baseInfo.xiaoHuaXT.slice(3, 4)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '4'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.fuXieCS"
                            style="width: 50px"
                            :disabled="!ruleForm.xiaoHuaXT.includes('4')"
                          ></el-input>
                          次/天(性状)：
                          <el-radio-group
                            v-model="ruleForm.xiaoHuaXTXZ"
                            :disabled="!ruleForm.xiaoHuaXT.includes('4')"
                          >
                            <el-radio
                              v-for="item in baseInfo.daBian"
                              :key="item.daiMa"
                              :label="item.daiMa"
                            >
                              {{ item.mingCheng }}
                            </el-radio>
                          </el-radio-group>
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.xiaoHuaXT">
                      <el-checkbox
                        v-for="data in baseInfo.xiaoHuaXT.slice(4)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '7'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.xiaoHuaXTQT"
                            style="width: 200px"
                            :disabled="!ruleForm.xiaoHuaXT.includes('7')"
                          ></el-input>
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span>呼吸系统：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.huXiXT">
                      <el-checkbox
                        v-for="data in baseInfo.huXiXT"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '4'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.huXiXTQT"
                            style="width: 200px"
                            :disabled="!ruleForm.huXiXT.includes('4')"
                          ></el-input>
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span>心脑血管系统：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.xinNaoXGXT">
                      <el-checkbox
                        v-for="data in baseInfo.xinXuanGuan"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '5'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.xinNaoXGQT"
                            style="width: 200px"
                            :disabled="!ruleForm.xinNaoXGXT.includes('5')"
                          ></el-input>
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span>泌尿系统：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.miNiaoXT">
                      <el-checkbox
                        v-for="data in baseInfo.miNiaoXT"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '5'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.miNiaoXTQT"
                            style="width: 200px"
                            :disabled="!ruleForm.miNiaoXT.includes('5')"
                          ></el-input>
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label" rowspan="3">
                  <span>神经系统：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.shenJingXT">
                      <el-checkbox
                        v-for="data in baseInfo.shenJingXT.slice(0, 9)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        {{ data.mingCheng }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.shenJingXT">
                      <el-checkbox
                        v-for="data in baseInfo.shenJingXT.slice(9, 13)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        {{ data.mingCheng }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.shenJingXT">
                      <el-checkbox
                        v-for="data in baseInfo.shenJingXT.slice(13)"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '16'">
                          {{ data.mingCheng }}
                          :(
                          <el-radio-group
                            v-model="ruleForm.tongKongYC"
                            :disabled="!ruleForm.shenJingXT.includes('16')"
                          >
                            <el-radio label="1">扩大</el-radio>
                            <el-radio label="2">固定</el-radio>
                            <el-radio label="3">收缩</el-radio>
                          </el-radio-group>
                          )
                        </div>
                        <div v-else-if="data.daiMa === '19'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.shenJingXTQT"
                            style="width: 200px"
                            :disabled="!ruleForm.shenJingXT.includes('19')"
                          ></el-input>
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span>皮肤和皮下组织：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.piFuHPXZZ">
                      <el-checkbox
                        v-for="data in baseInfo.piFuPXZZ"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '6'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.piFuHPXQT"
                            style="width: 200px"
                            :disabled="!ruleForm.piFuHPXZZ.includes('6')"
                          ></el-input>
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <p style="line-height: 14px">
                    <span style="color: red">*</span>
                    <span>是否在就诊前服用抗生素治疗：</span>
                  </p>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-radio-group v-model="ruleForm.shiFouYKSS">
                      <el-radio label="1">是</el-radio>
                      <el-radio label="2">否</el-radio>
                    </el-radio-group>
                    <el-input
                      v-model="ruleForm.kangShengSMC"
                      style="width: 200px; margin-left: 5px"
                      :disabled="ruleForm.shiFouYKSS !== '1'"
                      placeholder="请输入抗生素名称"
                    ></el-input>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>既往病史：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <el-checkbox-group v-model="ruleForm.jiWangBS">
                      <el-checkbox
                        v-for="data in baseInfo.jiWanBS"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        <div v-if="data.daiMa === '8'">
                          {{ data.mingCheng }}
                          <el-input
                            v-model="ruleForm.jiWangBSQT"
                            style="width: 200px"
                            :disabled="!ruleForm.jiWangBS.includes('8')"
                          ></el-input>
                        </div>
                        <div v-else>{{ data.mingCheng }}</div>
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div class="content-header" style="margin-top: 14px">
            <div class="title">检测结果</div>
          </div>
          <div class="el-form">
            <table>
              <tr>
                <td class="info-label">
                  <p style="line-height: 14px">
                    <span>监测结果为阳性时填写：</span>
                  </p>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value2">
                    <el-input v-model="ruleForm.jianCeJG"></el-input>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>初步诊断：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value">
                    <div style="display: flex; align-items: center">
                      <el-checkbox-group v-model="ruleForm.chuBuZD">
                        <el-checkbox label="1-1">急性肠胃炎</el-checkbox>
                        <el-checkbox label="1-2">感染性腹泻</el-checkbox>
                      </el-checkbox-group>
                      <el-button
                        type="text"
                        class="el-button"
                        @click="ruleForm.chuBuZDType = 'xiJun'"
                      >
                        细菌性
                      </el-button>
                      <el-button
                        type="text"
                        class="el-button"
                        @click="ruleForm.chuBuZDType = 'bingDu'"
                      >
                        病毒性
                      </el-button>
                      <el-button
                        type="text"
                        class="el-button"
                        @click="ruleForm.chuBuZDType = 'jiShengChong'"
                      >
                        寄生虫性
                      </el-button>
                      <el-button
                        type="text"
                        class="el-button"
                        @click="ruleForm.chuBuZDType = 'huaXueXing'"
                      >
                        化学性
                      </el-button>
                      <el-button
                        type="text"
                        class="el-button"
                        @click="ruleForm.chuBuZDType = 'youDuDZW'"
                      >
                        有毒动植物性
                      </el-button>
                      <el-button
                        type="text"
                        class="el-button"
                        @click="ruleForm.chuBuZDType = 'zhenJunXing'"
                      >
                        真菌性
                      </el-button>
                      <el-checkbox-group v-model="ruleForm.chuBuZD" style="margin-left: 30px">
                        <el-checkbox label="1-0">
                          其他
                          <el-input
                            v-model="ruleForm.chuBuZDQT"
                            style="width: 200px"
                            :disabled="!ruleForm.chuBuZD.includes('1-0')"
                          ></el-input>
                        </el-checkbox>
                      </el-checkbox-group>
                    </div>
                    <el-checkbox-group v-model="ruleForm.chuBuZD">
                      <el-checkbox
                        v-for="data in baseInfo[ruleForm.chuBuZDType]"
                        :key="data.daiMa"
                        :label="data.daiMa"
                        style="margin-top: 6px; margin-bottom: 6px"
                      >
                        {{ data.mingCheng }}
                      </el-checkbox>
                    </el-checkbox-group>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import {
  addOrUpdateSYBGK,
  getAddressListByAddress,
  getAddressListSy,
  getBaseInfoSY
} from '@/api/report-card'
import { mapState } from 'vuex'

export default {
  name: 'FoodborneReportCard',
  props: {
    patient: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {
        // bingLiWYBS: '', //病例唯一标识
        // zhuJianGUID: '', //主键guid
        shiPinMC: '', //食品名称
        shiPinFL: '', //食品分类
        jiaGongHBZFS: '', //加工或包装方式
        shiPinPP: '', //食品品牌
        shengChanCJ: '', //生产厂家
        // jinShiCS: '', //进食场所
        // gouMaiCS: '', //购买场所
        jinShiDDSSX: '', //进食地点省市县
        jinShiXXDZ: '', //进食详细地址
        gouMaiDDSSX: '', //购买地点省市县
        gouMaiXXDZ: '', //购买详细地址
        jinShiRS: 0, //进食人数
        jinShiSJ: '', //进食时间
        qiTaRSFFB: '1', //其他人是否发病
        shiFouCY: '0', //是否采样
        xuHao: 0, //序号

        gouMaiSF: '',
        gouMaiSJ: '',
        gouMaiQX: '',
        jinSSF: '',
        jinSSJ: '',
        jinSQX: '',

        gouMaiCSLX: '23021005',
        jinShiCSLX: '23021005'
      },
      tableData: [],
      ruleForm: {
        bingLiID: '',
        yiLiaoJG: '01',
        huanZheXM: '',
        huanZheXB: '',
        chuShengRQ: '',
        faBingSJ: '',
        menZhenHao: '',
        jiuZhenSJ: '',
        shiFouZY: '0',
        jianHuRXM: '',
        huanZheZY: '',
        lianXiDH: '',
        gongZuoDW: '',
        siWangSJ: '',
        huanZheSY: '',
        xianZhuSF: '',
        xianZhuSJ: '',
        xianZhuQX: '',
        xianZhuJD: '',
        xianZhuSSX: '',
        xianZhuXXDZ: '',

        blxxQo: [],

        quanShenZZYTZ: [],
        faReDS: '',
        zhengZhuangYTZQT: '',
        xiaoHuaXT: [],
        ouTuCS: '',
        fuXieCS: '',
        xiaoHuaXTXZ: '',
        xiaoHuaXTQT: '',
        huXiXT: [],
        huXiXTQT: '',
        xinNaoXGXT: [],
        xinNaoXGQT: '',
        miNiaoXT: [],
        miNiaoXTQT: '',
        shenJingXT: [],
        tongKongYC: '',
        shenJingXTQT: '',
        piFuHPXZZ: [],
        piFuHPXQT: '',
        shiFouYKSS: '',
        kangShengSMC: '',
        // kangShengSZL:'',
        jiWangBS: [],
        jiWangBSQT: '',
        jianCeJG: '',
        chuBuZD: [],
        chuBuZDQT: '',

        chuBuZDType: ''
      },
      rules: {
        huanZheXM: [{ required: true, message: '请输入', trigger: 'blur' }],
        huanZheXB: [{ required: true, message: '请选择', trigger: 'change' }],
        chuShengRQ: [{ required: true, message: '请选择', trigger: 'change' }],
        faBingSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        menZhenHao: [{ required: true, message: '请选择', trigger: 'change' }],
        jiuZhenSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        shiFouZY: [{ required: true, message: '请选择', trigger: 'change' }],
        huanZheZY: [{ required: true, message: '请选择', trigger: 'change' }],
        lianXiDH: [{ required: true, message: '请选择', trigger: 'change' }],
        gongZuoDW: [{ required: true, message: '请选择', trigger: 'change' }],
        huanZheSY: [{ required: true, message: '请选择', trigger: 'change' }],
        xianZhuSF: [{ required: true, message: '请选择', trigger: 'change' }],
        xianZhuSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        xianZhuQX: [{ required: true, message: '请选择', trigger: 'change' }],
        xianZhuJD: [{ required: true, message: '请选择', trigger: 'change' }],
        xianZhuXXDZ: [{ required: true, message: '请选择', trigger: 'change' }],
        quanShenZZYTZ: [{ required: true, message: '请选择', trigger: 'change' }],
        xiaoHuaXT: [{ required: true, message: '请选择', trigger: 'change' }],
        shiFouYKSS: [{ required: true, message: '请选择', trigger: 'change' }],
        jiWangBS: [{ required: true, message: '请选择', trigger: 'change' }],
        chuBuZD: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      rules2: {
        shiPinMC: [{ required: true, message: '请输入', trigger: 'blur' }],
        shiPinFL: [{ required: true, message: '请选择', trigger: 'change' }],
        jiaGongHBZFS: [{ required: true, message: '请选择', trigger: 'change' }],
        jinShiSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        jinShiRS: [{ required: true, message: '请选择', trigger: 'change' }],
        qiTaRSFFB: [{ required: true, message: '请选择', trigger: 'change' }],
        // gouMaiSF: [{ required: true, message: '请选择', trigger: 'change' }],
        // gouMaiSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        // gouMaiQX: [{ required: true, message: '请选择', trigger: 'change' }],
        gouMaiXXDZ: [{ required: true, message: '请输入', trigger: 'blur' }],
        // jinSSF: [{ required: true, message: '请选择', trigger: 'change' }],
        // jinSSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        // jinSQX: [{ required: true, message: '请选择', trigger: 'change' }],
        jinShiXXDZ: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      bingLiID: '',
      baseInfo: {},
      addressMapList: {
        xianZhu: { SF: [], SJ: [], QX: [], JD: [] },
        gouMai: { SF: [], SJ: [], QX: [], JD: [] },
        jinS: { SF: [], SJ: [], QX: [], JD: [] }
      },
      addressMap: { 0: 'SF', 1: 'SJ', 2: 'QX', 3: 'JD' },
      xgIndex: -1
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    })
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      //获取报告卡数据
      await this.getReportCard()
      //获取地址信息
      const resAddress = await this.getAddress(0, 0)
      this.addressMapList['xianZhu']['SF'] = resAddress
      this.addressMapList['gouMai']['SF'] = resAddress
      this.addressMapList['jinS']['SF'] = resAddress
      if (this.patient) {
        this.ruleForm = this.patient
        this.bingLiID = this.patient.bingLiID
        await this.dataHandle(this.ruleForm)
      } else {
        this.bingLiID = this.$route.params.id
        //获取报告卡数据
        await this.getPatient()
      }
    },
    async getPatient() {
      const ruleForm = this.ruleForm

      // const resData = await getPatientInfoByEmrID({ bingLiID: this.bingLiID })
      // const patientDetail = resData['data']
      // console.log('patientDetail:', patientDetail)
      // console.log('this.patientDetail:', this.patientDetail)
      ruleForm.bingLiID = this.bingLiID
      ruleForm.menZhenHao = this.patientDetail.bingRenBH

      ruleForm.huanZheXM = this.patientDetail.bingRenXM
      ruleForm.huanZheXB = this.patientDetail.xingBie
      ruleForm.chuShengRQ = this.patientDetail.chuShengRQ + ' 00:00:00'
      ruleForm.lianXiDH = this.patientDetail.lianXiDH

      const resAddress = await getAddressListByAddress({ diZhi: this.patientDetail.lianXiDZ })
      ruleForm.huanZheSY = resAddress.data.duiZhaoLB
      // const addressList = resAddress.data.diZhiBM.split('-')
      // ruleForm.xianZhuSF = addressList[0]
      // for (let i = 1; i < 4; i++) {
      //   await this.setAddress(ruleForm['xianZhu' + this.addressMap[i - 1]], i, 'xianZhu')
      //   ruleForm['xianZhu' + this.addressMap[i]] = addressList[i]
      // }
      ruleForm.xianZhuXXDZ = this.patientDetail.lianXiDZ
    },
    async getReportCard() {
      const res = await getBaseInfoSY({})
      this.baseInfo = res.data
    },
    async getAddress(id, code) {
      const res = await getAddressListSy({ leiXing: id, shangJiDM: code })
      return res.data
    },
    async setAddress(code, id, type) {
      for (let i = id; i < 4; i++) {
        this.addressMapList[type][this.addressMap[i]] = []
        this.ruleForm[type + this.addressMap[i]] = ''
      }
      this.addressMapList[type][this.addressMap[id]] = await this.getAddress(id, code)
    },
    submitForm(formName) {
      //保存
      this.$refs[formName].validate(async (valid) => {
        const listToStr = [
          'quanShenZZYTZ',
          'xiaoHuaXT',
          'huXiXT',
          'xinNaoXGXT',
          'miNiaoXT',
          'shenJingXT',
          'piFuHPXZZ',
          'jiWangBS',
          'chuBuZD'
        ]
        if (valid) {
          let reqData = JSON.parse(JSON.stringify(this.ruleForm))
          for (const key of listToStr) {
            if (reqData[key]) {
              reqData[key] = reqData[key].join(',')
            } else {
              reqData[key] = ''
            }
          }
          reqData['blxxQo'] = this.tableData
          reqData['xianZhuSSX'] =
            reqData.xianZhuSF +
            '-' +
            reqData.xianZhuSJ +
            '-' +
            reqData.xianZhuQX +
            '-' +
            reqData.xianZhuJD

          console.log('reqData:', reqData)
          const res = await addOrUpdateSYBGK(reqData)
          console.log(res)
          if (res.hasError === -1) {
            this.$message.error(res.errorMessage)
          } else {
            this.$message({
              message: res.errorMessage,
              type: 'success'
            })
          }
        } else {
          this.$message({
            message: '请补充必填内容！',
            type: 'warning'
          })
          return false
        }
      })
    },
    getMapData(type, key) {
      for (const d of this.baseInfo[type]) {
        if (d.daiMa === key) {
          if (d.mingCheng) {
            return d.mingCheng
          } else {
            return d.neiRong
          }
        }
      }
    },
    setBLxx() {
      this.$refs['formData'].validate(async (valid) => {
        if (valid) {
          const data = { ...this.formData }
          data['jinShiDDSSX'] = data.jinSSF + '-' + data.jinSSJ + '-' + data.jinSQX
          data['gouMaiDDSSX'] = data.gouMaiSF + '-' + data.gouMaiSJ + '-' + data.gouMaiQX
          data['gouMaiCS'] = data.gouMaiXXDZ
          data['jinShiCS'] = data.jinShiXXDZ
          if (this.xgIndex === -1) {
            this.tableData.push(data)
            this.$message({
              message: '添加成功！',
              type: 'success'
            })
          } else {
            this.$set(this.tableData, this.xgIndex, { ...data })
            this.xgIndex = -1
            this.$message({
              message: '修改成功！',
              type: 'success'
            })
          }
          console.log(this.tableData)
        }
      })
    },
    handleDelete(index, row) {
      this.tableData.splice(index, 1)
    },
    async handleEdit(index, row) {
      this.formData = { ...row }
      this.xgIndex = index
      const keyList = ['gouMai', 'jinS']
      for (const key of keyList) {
        let dataList = []
        if (key === 'jinS') {
          dataList = this.formData['jinShiDDSSX'].split('-')
        } else {
          dataList = this.formData[key + 'DDSSX'].split('-')
        }
        for (let i = 0; i < 4; i++) {
          this.formData[key + this.addressMap[i]] = dataList[i]
        }
        for (let i = 1; i < 4; i++) {
          const k = this.addressMap[i - 1]
          this.addressMapList[key][this.addressMap[i]] = await this.getAddress(
            i,
            this.formData[key + k]
          )
        }
      }
    },
    async dataHandle(reqData) {
      const listToStr = [
        'quanShenZZYTZ',
        'xiaoHuaXT',
        'huXiXT',
        'xinNaoXGXT',
        'miNiaoXT',
        'shenJingXT',
        'piFuHPXZZ',
        'jiWangBS',
        'chuBuZD'
      ]
      for (const key of listToStr) {
        if (reqData[key]) {
          reqData[key] = reqData[key].split(',')
        } else {
          reqData[key] = []
        }
      }
      if (reqData['xianZhuSSX']) {
        const key = 'xianZhu'
        const dataList = reqData['xianZhuSSX'].split('-')
        for (let i = 0; i < 4; i++) {
          reqData[key + this.addressMap[i]] = dataList[i]
        }
        for (let i = 1; i < 4; i++) {
          const k = this.addressMap[i - 1]
          this.addressMapList[key][this.addressMap[i]] = await this.getAddress(i, reqData[key + k])
        }
      }
      this.tableData = reqData['blxxvos']
    },
    sendData() {
      const data = { dialogVisible: false }
      this.$emit('childEvent', data) // 触发事件并传递数据
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;
    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }
  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }
  .info-value {
    background-color: #f7f9fd;
    .value {
      margin-left: 10px;
      .risk {
        margin-right: 15px;
      }
    }
    a {
      text-decoration: underline;
      color: #356ac5;
    }
    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }
    .cz-select {
      display: flex;
      align-items: center;
      ::v-deep .el-form-item {
        width: auto;
      }
      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }
      ::v-deep .el-input {
        margin-right: 5px;
      }
    }
    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }
      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }
      ::v-deep .el-select {
        width: 100%;
      }
      ::v-deep .el-input {
        width: 100%;
      }
    }
    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }
    .el-button {
      margin-top: -3px;
      margin-left: 30px;
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
</style>
