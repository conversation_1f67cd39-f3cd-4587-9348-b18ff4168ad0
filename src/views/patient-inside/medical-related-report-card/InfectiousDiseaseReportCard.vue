<template>
  <div class="container">
    <div v-if="viewType === 1" class="content">
      <div class="content-header">
        <el-input v-model="select" style="width: 200px" @change="getInfectious"></el-input>
      </div>
      <div class="content" style="border: #dadee5 1px solid">
        <el-table
          :data="tableData"
          style="width: 100%; overflow: auto"
          size="mini"
          highlight-current-row
          @current-change="handleCurrentChange"
        >
          <el-table-column prop="mingCheng" label="诊断名称"></el-table-column>
          <el-table-column prop="icd" label="ICD编码" width="180"></el-table-column>
        </el-table>
        <el-pagination
          :current-page.sync="pageIndex"
          :page-size="pageSize"
          layout="total, prev, pager, next"
          :total="total"
          @current-change="setPage"
        ></el-pagination>
      </div>
    </div>
    <div v-else class="content">
      <div class="content-header">
        <div class="title">新增传染病报告卡</div>
        <div>
          <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
          <el-button v-if="patient" type="primary" @click="sendData">返回上一层</el-button>
        </div>
      </div>
      <div class="content" style="border: #dadee5 1px solid; overflow: auto">
        <div class="content-header">
          <div class="title">信息填写</div>
        </div>
        <div class="el-form">
          <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="250px">
            <el-form-item label="姓名：" prop="xingMing">
              <el-input v-model="ruleForm.xingMing"></el-input>
            </el-form-item>
            <el-form-item label="有效证件号：" prop="zhengJianHM">
              <el-select v-model="ruleForm.zhengJianLX" placeholder="请选择证件类型">
                <el-option
                  v-for="data in baseInfo.zhengJianLX"
                  :key="data.daiMa"
                  :label="data.neiRong"
                  :value="data.daiMa"
                ></el-option>
              </el-select>
              <el-input v-model="ruleForm.zhengJianHM"></el-input>
            </el-form-item>
            <el-form-item label="性别：" prop="xingBie">
              <div class="value">
                <el-radio-group v-model="ruleForm.xingBie">
                  <el-radio v-for="data in baseInfo.xingBie" :key="data.daiMa" :label="data.daiMa">
                    {{ data.neiRong }}
                  </el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
            <el-form-item label="出生日期：" prop="chuShengRQ">
              <el-date-picker
                v-model="ruleForm.chuShengRQ"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="联系电话：" prop="lianXiDH">
              <el-input v-model="ruleForm.lianXiDH"></el-input>
            </el-form-item>
            <el-form-item label="职业：" prop="zhiYeLB">
              <el-select v-model="ruleForm.zhiYeLB">
                <el-option
                  v-for="item in baseInfo.zhiYeList"
                  :key="item.daiMa"
                  :label="item.mingCheng"
                  :value="item.daiMa"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="患者工作单位：" prop="gongZuoDW">
              <el-input v-model="ruleForm.gongZuoDW" placeholder="学生或幼托需填写"></el-input>
            </el-form-item>
            <el-form-item label="现住地址类型：" prop="changZhuDQLX">
              <div class="value">
                <el-radio-group v-model="ruleForm.changZhuDQLX">
                  <el-radio v-for="data in baseInfo.diZhiLX" :key="data.daiMa" :label="data.daiMa">
                    {{ data.neiRong }}
                  </el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
            <el-form-item prop="xianZhuZhiSF" label="常住户口地址：" class="cz-select">
              <el-form-item prop="xianZhuZhiSF">
                <el-select
                  v-model="ruleForm.xianZhuZhiSF"
                  placeholder="-省份-"
                  @change="setAddress($event, 1, 'xianZhuZhi')"
                >
                  <el-option
                    v-for="item in addressMapList['xianZhuZhi'].SF"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="xianZhuZhiSJ">
                <el-select
                  v-model="ruleForm.xianZhuZhiSJ"
                  placeholder="-市级-"
                  @change="setAddress($event, 2, 'xianZhuZhi')"
                >
                  <el-option
                    v-for="item in addressMapList['xianZhuZhi'].SJ"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="xianZhuZhiQX">
                <el-select
                  v-model="ruleForm.xianZhuZhiQX"
                  placeholder="-区县-"
                  @change="setAddress($event, 3, 'xianZhuZhi')"
                >
                  <el-option
                    v-for="item in addressMapList['xianZhuZhi'].QX"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="xianZhuZhiJD">
                <el-select v-model="ruleForm.xianZhuZhiJD" placeholder="-街道-">
                  <el-option
                    v-for="item in addressMapList['xianZhuZhi'].JD"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item prop="xianZhuXXDZ">
                <el-input v-model="ruleForm.xianZhuXXDZ" placeholder="详细地址"></el-input>
              </el-form-item>
            </el-form-item>
            <el-form-item label="诊断类型：" prop="zhenDuanLX">
              <el-select v-model="ruleForm.zhenDuanLX" placeholder="请选择">
                <el-option
                  v-for="data in baseInfo.zhenDuanLX"
                  :key="data.daiMa"
                  :label="data.mingCheng"
                  :value="data.daiMa"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="病历分类：" prop="bingLiFL">
              <el-select v-model="ruleForm.bingLiFL" placeholder="请选择">
                <el-option
                  v-for="data in baseInfo.bingLiFL"
                  :key="data.daiMa"
                  :label="data.mingCheng"
                  :value="data.daiMa"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="病发日期：" prop="faBingRQ">
              <el-date-picker
                v-model="ruleForm.faBingRQ"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="诊断时间：" prop="zhenDuanRQ">
              <el-date-picker
                v-model="ruleForm.zhenDuanRQ"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
                placeholder="选择时间"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="死亡时间：" prop="siWangRQ">
              <el-date-picker
                v-model="ruleForm.siWangRQ"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="datetime"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="疾病名称：" prop="icd10">
              <el-select
                v-model="ruleForm.icd10"
                filterable
                remote
                placeholder="请输入首拼或中文名"
                :remote-method="getDiagnose"
                :loading="loading"
                @change="changeView"
              >
                <el-option
                  v-for="item in diagnoseList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <div v-if="view === '4000'">
              <el-form-item label="重症患者：" prop="zhongZhengBS">
                <div class="value">
                  <el-radio-group v-model="ruleForm.zhongZhengBS">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                </div>
              </el-form-item>
            </div>
            <div v-if="view === '9825'">
              <el-form-item label="病人所属：" prop="suoShuDiLXBM">
                <div class="value">
                  <el-radio-group v-model="ruleForm.suoShuDiLXBM">
                    <el-radio label="0">本地</el-radio>
                    <el-radio label="1">异地</el-radio>
                  </el-radio-group>
                </div>
              </el-form-item>
              <el-form-item label="来现就诊地日期：" prop="laiRiQi">
                <el-date-picker
                  v-model="ruleForm.laiRiQi"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="现就诊地址类型：" prop="xianJiuZhenDZZLXBM">
                <div class="value">
                  <el-radio-group v-model="ruleForm.xianJiuZhenDZZLXBM">
                    <el-radio
                      v-for="data in baseInfo.diZhiLX"
                      :key="data.daiMa"
                      :label="data.daiMa"
                    >
                      {{ data.neiRong }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </el-form-item>
              <el-form-item
                label="现就诊地详细地址："
                prop="xianJiuZhenDZZGJBMSF"
                class="cz-select"
              >
                <el-form-item prop="xianJiuZhenDZZGJBMSF">
                  <el-select
                    v-model="ruleForm.xianJiuZhenDZZGJBMSF"
                    placeholder="-省份-"
                    @change="setAddress($event, 1, 'xianJiuZhenDZZGJBM')"
                  >
                    <el-option
                      v-for="item in addressMapList['xianJiuZhenDZZGJBM'].SF"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="xianJiuZhenDZZGJBMSJ">
                  <el-select
                    v-model="ruleForm.xianJiuZhenDZZGJBMSJ"
                    placeholder="-市级-"
                    @change="setAddress($event, 2, 'xianJiuZhenDZZGJBM')"
                  >
                    <el-option
                      v-for="item in addressMapList['xianJiuZhenDZZGJBM'].SJ"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="xianJiuZhenDZZGJBMQX">
                  <el-select
                    v-model="ruleForm.xianJiuZhenDZZGJBMQX"
                    placeholder="-区县-"
                    @change="setAddress($event, 3, 'xianJiuZhenDZZGJBM')"
                  >
                    <el-option
                      v-for="item in addressMapList['xianJiuZhenDZZGJBM'].QX"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="xianJiuZhenDZZGJBMJD">
                  <el-select v-model="ruleForm.xianJiuZhenDZZGJBMJD" placeholder="-街道-">
                    <el-option
                      v-for="item in addressMapList['xianJiuZhenDZZGJBM'].JD"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="xianJiuZhenDiXXZZ">
                  <el-input v-model="ruleForm.xianJiuZhenDiXXZZ" placeholder="详细地址"></el-input>
                </el-form-item>
              </el-form-item>
              <el-form-item label="麻痹日期：" prop="maBiRQ">
                <el-date-picker
                  v-model="ruleForm.maBiRQ"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="麻痹症状：" prop="maBiZZ">
                <el-input v-model="ruleForm.maBiZZ"></el-input>
              </el-form-item>
            </div>
            <div v-if="xbList.includes(view)">
              <el-form-item label="婚姻状况：" prop="hunYinZK">
                <div class="value">
                  <el-radio-group v-model="ruleForm.hunYinZK">
                    <el-radio
                      v-for="data in baseInfo.hunYinZK"
                      :key="data.daiMa"
                      :label="data.daiMa"
                    >
                      {{ data.neiRong }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </el-form-item>
              <el-form-item label="民族：" prop="minZu">
                <el-select v-model="ruleForm.minZu" placeholder="请选择">
                  <el-option
                    v-for="data in baseInfo.minZu"
                    :key="data.daiMa"
                    :label="data.mingCheng"
                    :value="data.daiMa"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="文化程度：" prop="wenHuaCD">
                <el-select v-model="ruleForm.wenHuaCD" placeholder="请选择">
                  <el-option
                    v-for="data in baseInfo.wenHua"
                    :key="data.daiMa"
                    :label="data.mingCheng"
                    :value="data.daiMa"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="户籍地址类型：" prop="huJiDLX">
                <div class="value">
                  <el-radio-group v-model="ruleForm.huJiDLX">
                    <el-radio
                      v-for="data in baseInfo.diZhiLX"
                      :key="data.daiMa"
                      :label="data.daiMa"
                    >
                      {{ data.neiRong }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </el-form-item>
              <el-form-item label="户籍详细地址：" prop="huJiSF" class="cz-select">
                <el-form-item prop="huJiSF">
                  <el-select
                    v-model="ruleForm.huJiSF"
                    placeholder="-省份-"
                    @change="setAddress($event, 1, 'huJi')"
                  >
                    <el-option
                      v-for="item in addressMapList['huJi'].SF"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="huJiSJ">
                  <el-select
                    v-model="ruleForm.huJiSJ"
                    placeholder="-市级-"
                    @change="setAddress($event, 2, 'huJi')"
                  >
                    <el-option
                      v-for="item in addressMapList['huJi'].SJ"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="huJiQX">
                  <el-select
                    v-model="ruleForm.huJiQX"
                    placeholder="-区县-"
                    @change="setAddress($event, 3, 'huJi')"
                  >
                    <el-option
                      v-for="item in addressMapList['huJi'].QX"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="huJiJD">
                  <el-select v-model="ruleForm.huJiJD" placeholder="-街道-">
                    <el-option
                      v-for="item in addressMapList['huJi'].JD"
                      :key="item.daiMa"
                      :label="item.mingCheng"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="xianZhuXXDZ">
                  <el-input v-model="ruleForm.huJiXXDZ" placeholder="详细地址"></el-input>
                </el-form-item>
              </el-form-item>
              <el-form-item label="接触史：" prop="jieChuShiXX" class="jcs">
                <div class="value">
                  <el-checkbox-group v-model="ruleForm.jieChuShiXX">
                    <el-checkbox
                      v-for="data in baseInfo.jieChuShi"
                      :key="data.daiMa"
                      :label="data.daiMa"
                    >
                      {{ data.mingCheng }}
                    </el-checkbox>
                  </el-checkbox-group>
                </div>
              </el-form-item>
              <el-form-item
                v-if="ruleForm.jieChuShiXX.includes('11')"
                label="其他接触史："
                prop="jieChuShiQT"
              >
                <el-input v-model="ruleForm.jieChuShiQT"></el-input>
              </el-form-item>
              <el-form-item label="性病史：" prop="xingBingShi">
                <div class="value">
                  <el-radio-group v-model="ruleForm.xingBingShi">
                    <el-radio
                      v-for="data in baseInfo.xingBingShi"
                      :key="data.daiMa"
                      :label="data.daiMa"
                    >
                      {{ data.neiRong }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </el-form-item>
              <el-form-item label="最有可能传染途径：" prop="ganRanTJ">
                <div class="value">
                  <el-radio-group v-model="ruleForm.ganRanTJ">
                    <el-radio
                      v-for="data in baseInfo.ganRanTJ"
                      :key="data.daiMa"
                      :label="data.daiMa"
                    >
                      {{ data.mingCheng }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </el-form-item>
              <el-form-item
                v-if="ruleForm.ganRanTJ === '10'"
                label="其他最有可能传染途径："
                prop="ganRanTJQT"
              >
                <el-input v-model="ruleForm.ganRanTJQT"></el-input>
              </el-form-item>
              <el-form-item label="样本来源：" prop="yangBenLY" class="yb">
                <el-radio-group v-model="ruleForm.yangBenLY" style="width: 100%">
                  <div class="ybz">
                    <el-radio v-for="data in baseInfoYbz[0]" :key="data.daiMa" :label="data.daiMa">
                      {{ data.mingCheng }}
                    </el-radio>
                  </div>
                  <div class="ybz">
                    <el-radio v-for="data in baseInfoYbz[1]" :key="data.daiMa" :label="data.daiMa">
                      {{ data.mingCheng }}
                    </el-radio>
                  </div>
                  <div class="ybz">
                    <el-radio v-for="data in baseInfoYbz[2]" :key="data.daiMa" :label="data.daiMa">
                      {{ data.mingCheng }}
                    </el-radio>
                  </div>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-if="ruleForm.yangBenLY === '20'"
                label="其他样本来源："
                prop="yangBenLYQT"
              >
                <el-input v-model="ruleForm.yangBenLYQT"></el-input>
              </el-form-item>
              <el-form-item label="实验室检测结论：" prop="shiYanShiJCJG">
                <div class="value">
                  <el-radio-group v-model="ruleForm.shiYanShiJCJG">
                    <el-radio
                      v-for="data in baseInfo.shiYanShiJCJL"
                      :key="data.daiMa"
                      :label="data.daiMa"
                    >
                      {{ data.neiRong }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </el-form-item>
              <el-form-item label="确认(替代策略、核酸)检测阳性日期：" prop="queRenYXRQ">
                <el-date-picker
                  v-model="ruleForm.queRenYXRQ"
                  value-format="yyyy-MM-dd"
                  type="date"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
              <el-form-item label="确认(替代策略、核酸)检测单位：" prop="queRenYXDWMC">
                <el-input v-model="ruleForm.queRenYXDWMC"></el-input>
              </el-form-item>
              <el-form-item label="生殖道沙眼衣原体感染：" prop="yiYuanTiGRBZ">
                <el-select v-model="ruleForm.yiYuanTiGRBZ" placeholder="请选择">
                  <el-option label="确诊病例" value="1"></el-option>
                  <el-option label="无症状感染" value="2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="azList.includes(view)" label="艾滋病确诊日期：" prop="aiZiZDRQ">
                <el-date-picker
                  v-model="ruleForm.aiZiZDRQ"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  type="datetime"
                  placeholder="选择日期"
                ></el-date-picker>
              </el-form-item>
            </div>
            <el-form-item label="填卡医生：" prop="baoKaYS">
              <el-input v-model="ruleForm.baoKaYS"></el-input>
            </el-form-item>
            <el-form-item label="填卡日期：" prop="baoKaRQ">
              <el-date-picker
                v-model="ruleForm.baoKaRQ"
                value-format="yyyy-MM-dd"
                type="date"
                placeholder="选择日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="密切接触者有无相同症状：" prop="qingMiJCTZZBZ">
              <div class="value">
                <el-radio-group v-model="ruleForm.qingMiJCTZZBZ">
                  <el-radio label="0">无</el-radio>
                  <el-radio label="1">有</el-radio>
                </el-radio-group>
              </div>
            </el-form-item>
            <el-form-item label="备注：" prop="beiZhu">
              <el-input v-model="ruleForm.beiZhu"></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getAddressList,
  getAddressListByAddress,
  getBaseInfo,
  getDiagnoseList,
  getInfectiousDiseaseDiagnosis,
  getPatientInfoByEmrID,
  saveReport
} from '@/api/report-card'
import { mapState } from 'vuex'

export default {
  name: 'InfectiousDiseaseReportCard',
  props: {
    patient: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      viewType: 1,
      xbList: ['0600', '0601', '0801', '0802', '0803', '0804', '0805'],
      azList: ['0600', '0601'],
      select: '',
      tableData: [],
      pageIndex: 1,
      pageSize: 17,
      total: 0,

      ruleForm: {
        bingRenBH: '',
        bingLiID: '',
        zhuYuanHao: '',
        icd10: '',
        xingMing: '',
        zhengJianLX: '',
        zhengJianHM: '',
        xingBie: '1',
        chuShengRQ: '',
        lianXiDH: '',
        zhiYeLB: '',
        gongZuoDW: '',
        changZhuDQLX: '1',

        xianZhuZhiSF: '',
        xianZhuZhiSJ: '',
        xianZhuZhiQX: '',
        xianZhuZhiJD: '',
        xianZhuZhi: '',
        xianZhuXXDZ: '',

        zhenDuanLX: '',
        bingLiFL: '',
        faBingRQ: '',
        zhenDuanRQ: '',
        siWangRQ: '',
        jiBingMC: '',
        jiBingID: '',

        suoShuDiLXBM: '',
        laiRiQi: '',
        xianJiuZhenDZZLXBM: '',
        xianJiuZhenDZZGJBM: '',
        xianJiuZhenDZZGJBMSF: '',
        xianJiuZhenDZZGJBMSJ: '',
        xianJiuZhenDZZGJBMQX: '',
        xianJiuZhenDZZGJBMJD: '',
        xianJiuZhenDiXXZZ: '',
        maBiRQ: '',
        maBiZZ: '',

        hunYinZK: '',
        minZu: '',
        wenHuaCD: '',
        huJiDLX: '',
        huJiSF: '',
        huJiSJ: '',
        huJiQX: '',
        huJiJD: '',
        huJiDiBM: '',
        huJiXXDZ: '',
        jieChuShiXX: [],
        jieChuShiQT: '',
        xingBingShi: '',
        ganRanTJ: '',
        ganRanTJQT: '',
        yangBenLY: '',
        yangBenLYQT: '',
        shiYanShiJCJG: '',
        queRenYXRQ: '',
        queRenYXDWMC: '',
        yiYuanTiGRBZ: '',
        aiZiZDRQ: '',

        baoKaYS: '',
        baoKaRQ: '',
        qingMiJCTZZBZ: '0',
        beiZhu: '',
        zhongZhengBS: ''
      },
      rules: {
        xingMing: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        zhengJianLX: [{ required: true, message: '请选择证件', trigger: 'change' }],
        zhengJianHM: [{ required: true, message: '请输入证件号', trigger: 'blur' }],
        xingBie: [{ required: true, message: '请选择性别', trigger: 'change' }],
        chuShengRQ: [{ required: true, message: '请输入日期', trigger: 'change' }],
        zhiYeLB: [{ required: true, message: '请输入职业', trigger: 'blur' }],
        // xianZhuZhi: [{ required: true, message: '请输入地址', trigger: 'change' }],
        xianZhuZhiSF: [{ required: true, message: '请输入地址', trigger: 'change' }],
        xianZhuZhiSJ: [{ required: true, message: '请输入地址', trigger: 'change' }],
        xianZhuZhiQX: [{ required: true, message: '请输入地址', trigger: 'change' }],
        xianZhuZhiJD: [{ required: true, message: '请输入地址', trigger: 'change' }],
        xianZhuXXDZ: [{ required: true, message: '请输入地址', trigger: 'blur' }],
        zhenDuanLX: [{ required: true, message: '请输入类型', trigger: 'blur' }],
        bingLiFL: [{ required: true, message: '请输入分类', trigger: 'change' }],
        faBingRQ: [{ required: true, message: '请输入日期', trigger: 'change' }],
        zhenDuanRQ: [{ required: true, message: '请输入时间', trigger: 'change' }],
        icd10: [{ required: true, message: '请输入名称', trigger: 'blur' }],

        suoShuDiLXBM: [{ required: true, message: '请输入', trigger: 'change' }],
        maBiRQ: [{ required: true, message: '请输入', trigger: 'change' }],

        hunYinZK: [{ required: true, message: '请输入', trigger: 'change' }],
        minZu: [{ required: true, message: '请输入', trigger: 'change' }],
        wenHuaCD: [{ required: true, message: '请输入', trigger: 'change' }],
        huJiDLX: [{ required: true, message: '请输入', trigger: 'change' }],
        jieChuShiXX: [{ required: true, message: '请输入', trigger: 'change' }],
        jieChuShiQT: [{ required: true, message: '请输入', trigger: 'change' }],
        xingBingShi: [{ required: true, message: '请输入', trigger: 'change' }],
        ganRanTJ: [{ required: true, message: '请输入', trigger: 'change' }],
        ganRanTJQT: [{ required: true, message: '请输入', trigger: 'change' }],
        yangBenLY: [{ required: true, message: '请输入', trigger: 'change' }],
        yangBenLYQT: [{ required: true, message: '请输入', trigger: 'change' }],
        shiYanShiJCJG: [{ required: true, message: '请输入', trigger: 'change' }],
        aiZiZDRQ: [{ required: true, message: '请输入', trigger: 'change' }],

        baoKaYS: [{ required: true, message: '请输入名称', trigger: 'blur' }],
        baoKaRQ: [{ required: true, message: '请输入日期', trigger: 'change' }],
        qingMiJCTZZBZ: [{ required: true, message: '请选择症状', trigger: 'change' }],

        zhongZhengBS: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      bingLiID: '',
      baseInfo: {},
      baseInfoYbz: [],
      addressMapList: {
        xianZhuZhi: { SF: [], SJ: [], QX: [], JD: [] },
        huJi: { SF: [], SJ: [], QX: [], JD: [] },
        xianJiuZhenDZZGJBM: { SF: [], SJ: [], QX: [], JD: [] }
      },
      addressMap: { 0: 'SF', 1: 'SJ', 2: 'QX', 3: 'JD' },
      diagnoseList: [],
      diagnoseMap: {},
      loading: false,
      view: ''
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    })
  },
  async mounted() {
    await this.getInfectious(this.select)
    await this.init()
  },
  methods: {
    async setPage(pageIndex) {
      this.pageIndex = pageIndex
      const res = await getInfectiousDiseaseDiagnosis({
        key: this.select,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize
      })
      this.tableData = res.data.records
    },
    async getInfectious(key) {
      this.pageIndex = 1
      const res = await getInfectiousDiseaseDiagnosis({
        key: key,
        pageIndex: this.pageIndex,
        pageSize: this.pageSize
      })
      this.total = res.data.total
      this.tableData = res.data.records
    },
    async handleCurrentChange(val) {
      this.ruleForm.jiBingMC = val.jiBingID + '|' + val.leiBie + '|' + val.mc_crb
      this.ruleForm.jiBingID = val.jiBingID
      const res = await this.getDiagnose(val.mc_crb)
      for (const d of res.data) {
        if (d.mingCheng === val.mc_crb) {
          this.ruleForm.icd10 = d.daiMa
          await this.changeView(d.daiMa)
        }
      }
      this.viewType = 2
    },
    async init() {
      //获取地址信息
      const resAddress = await this.getAddress(0, 0)
      this.addressMapList['xianZhuZhi']['SF'] = resAddress
      this.addressMapList['huJi']['SF'] = resAddress
      this.addressMapList['xianJiuZhenDZZGJBM']['SF'] = resAddress
      //获取报告卡数据
      await this.getReportCard()
      if (this.patient) {
        console.log(this.patient)
        this.viewType = 2
        this.ruleForm = this.patient
        this.bingLiID = this.patient.bingLiID

        await this.dataHandle(this.ruleForm, false)

        const res = await getInfectiousDiseaseDiagnosis({
          key: this.patient.jiBingMC,
          pageIndex: 1,
          pageSize: 100
        })
        for (const d of res.data.records) {
          if (d.icd === this.patient.icd) {
            await this.handleCurrentChange(d)
            break
          }
        }
      } else {
        this.bingLiID = this.$route.params.id
        //获取报告卡数据
        await this.getPatient()
      }
    },
    async getPatient() {
      const now = new Date()
      const ruleForm = this.ruleForm

      const resData = await getPatientInfoByEmrID({ bingLiID: this.bingLiID })
      const patientDetail = resData['data']

      ruleForm.bingLiID = this.bingLiID
      ruleForm.zhuYuanHao = this.patientDetail.zhuYuanHao
      ruleForm.bingRenBH = this.patientDetail.bingRenBH
      ruleForm.xingMing = this.patientDetail.bingRenXM
      ruleForm.zhengJianLX = '01'
      ruleForm.zhengJianHM = patientDetail.shenFenZH
      ruleForm.xingBie = this.patientDetail.xingBie
      ruleForm.lianXiDH = this.patientDetail.lianXiDH

      const resAddress = await getAddressListByAddress({ diZhi: this.patientDetail.lianXiDZ })
      ruleForm.changZhuDQLX = resAddress.data.duiZhaoLB
      const addressList = resAddress.data.diZhiBM.split('-')
      ruleForm.xianZhuZhiSF = addressList[0]
      for (let i = 1; i < 4; i++) {
        await this.setAddress(ruleForm['xianZhuZhi' + this.addressMap[i - 1]], i, 'xianZhuZhi')
        ruleForm['xianZhuZhi' + this.addressMap[i]] = addressList[i]
      }
      ruleForm.xianZhuXXDZ = this.patientDetail.lianXiDZ
      ruleForm.gongZuoDW = this.patientDetail.lianXiDZ

      ruleForm.bingLiFL = '0'
      ruleForm.chuShengRQ = this.patientDetail.chuShengRQ
      ruleForm.faBingRQ = now
      ruleForm.zhenDuanRQ = now
      ruleForm.baoKaRQ = now
      ruleForm.baoKaYS = this.patientInfo.yongHuXM
      ruleForm.qingMiJCTZZBZ = '1'
    },
    async getReportCard() {
      const res = await getBaseInfo({})
      this.baseInfo = res.data
      this.baseInfoYbz[0] = res.data.yangBenLY.slice(0, 8)
      this.baseInfoYbz[1] = res.data.yangBenLY.slice(8, 15)
      this.baseInfoYbz[2] = res.data.yangBenLY.slice(15)
    },
    async getAddress(id, code) {
      const res = await getAddressList({ leiXing: id, shangJiDM: code })
      return res.data
    },
    async setAddress(code, id, type) {
      for (let i = id; i < 4; i++) {
        this.addressMapList[type][this.addressMap[i]] = []
        this.ruleForm[type + this.addressMap[i]] = ''
      }
      this.addressMapList[type][this.addressMap[id]] = await this.getAddress(id, code)
    },
    submitForm(formName) {
      //保存
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          await this.dataHandle(this.ruleForm, true)
          // this.ruleForm['icd10'] = this.diagnoseMap[this.ruleForm['jiBingMC']].icd
          const res = await saveReport(this.ruleForm)
          if (res.hasError === -1) {
            this.$message.error(res.errorMessage)
          } else {
            this.$message({
              message: res.errorMessage,
              type: 'success'
            })
          }
        } else {
          this.$message({
            message: '请补充必填内容！',
            type: 'warning'
          })
          return false
        }
      })
    },
    async dataHandle(reqData, type) {
      if (type) {
        for (const key in this.addressMapList) {
          for (const k in this.addressMapList[key]) {
            reqData[key] = reqData[key + k] + '-'
          }
          reqData[key] = reqData[key].slice(0, -1)
        }
      } else {
        console.log('进入')
        console.log(reqData)
      }
    },
    sendData() {
      const data = { dialogVisible: false }
      this.$emit('childEvent', data) // 触发事件并传递数据
    },
    async getDiagnose(query) {
      //查询疾病
      const res = await getDiagnoseList({ key: query.toUpperCase() })
      let resList = []
      for (const item of res.data) {
        const data = { value: item.daiMa, label: item.mingCheng, icd: item.icd }
        this.diagnoseMap[item.daiMa] = data
        resList.push(data)
      }
      this.diagnoseList = resList
      return res
    },
    async changeView(code) {
      this.view = code
      if (this.xbList.includes(code)) {
        console.log('艾滋')
        const resAddress = await getAddressListByAddress({ diZhi: this.patientDetail.lianXiDZ })
        const addressList = resAddress.data.diZhiBM.split('-')
        this.ruleForm.huJiDLX = resAddress.data.duiZhaoLB
        this.ruleForm.huJiSF = addressList[0]
        for (let i = 1; i < 4; i++) {
          await this.setAddress(this.ruleForm['huJi' + this.addressMap[i - 1]], i, 'huJi')
          this.ruleForm['huJi' + this.addressMap[i]] = addressList[i]
        }
        this.ruleForm.huJiXXDZ = this.patientDetail.lianXiDZ
      } else if (code === '9825') {
        console.log('AFP')
        this.ruleForm.suoShuDiLXBM = '1'
        const resAddress = await getAddressListByAddress({ diZhi: '南白象街道' })
        const addressList = resAddress.data.diZhiBM.split('-')
        this.ruleForm.xianJiuZhenDZZGJBMSF = addressList[0]
        for (let i = 1; i < 4; i++) {
          await this.setAddress(
            this.ruleForm['xianJiuZhenDZZGJBM' + this.addressMap[i - 1]],
            i,
            'xianJiuZhenDZZGJBM'
          )
          this.ruleForm['xianJiuZhenDZZGJBM' + this.addressMap[i]] = addressList[i]
        }
        this.ruleForm.xianJiuZhenDiXXZZ = '温州医科大学附属第一医院'
        if (this.ruleForm['xianJiuZhenDZZGJBMSF'] === this.ruleForm['xianZhuZhiSF']) {
          this.ruleForm.xianJiuZhenDZZLXBM = '3'
        } else {
          this.ruleForm.xianJiuZhenDZZLXBM = '4'
        }
        if (this.ruleForm['xianJiuZhenDZZGJBMSJ'] === this.ruleForm['xianZhuZhiSJ']) {
          this.ruleForm.xianJiuZhenDZZLXBM = '2'
        }
        if (this.ruleForm['xianJiuZhenDZZGJBMQX'] === this.ruleForm['xianZhuZhiQX']) {
          this.ruleForm.xianJiuZhenDZZLXBM = '1'
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .el-form {
    .value {
      margin-left: 10px;
    }

    ::v-deep .el-select {
      margin-right: 4px;
    }

    .cz-select {
      ::v-deep .el-select {
        width: 160px;
      }

      ::v-deep .el-form-item__content {
        border: none;
      }
    }

    .jcs {
      ::v-deep .el-checkbox {
        margin-right: 20px;
      }
    }

    .yb {
      ::v-deep .el-form-item__label {
        height: 120px;
        display: flex;
        align-items: center; /* 垂直居中 */
        justify-content: right; /* 水平居中 */
      }

      ::v-deep .el-form-item__content {
        height: 120px;
        display: flex;
        flex-direction: column;
        padding: 0;
      }

      .ybz {
        padding-left: 10px;
        border: 1px solid #ddd;
        height: 40px;
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 262px;
      }
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
    }

    ::v-deep .el-form-item__label {
      height: 40px;
      border: 1px solid #ddd;
      padding: 2px;
      text-align: right;
      background-color: #eaf0f9;
    }

    ::v-deep .el-form-item__content {
      height: 40px;
      border: 1px solid #ddd;
      padding: 2px;
      max-width: 100%;
      background-color: #f7f9fd;
    }
  }
}

::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
</style>
