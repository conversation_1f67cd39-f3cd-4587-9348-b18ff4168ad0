<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">新增心脑报告卡</div>
        <div>
          <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
          <el-button v-if="patient" type="primary" @click="sendData">返回上一层</el-button>
        </div>
      </div>
      <div class="content" style="border: #dadee5 1px solid; overflow: auto">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <div class="el-form">
            <table>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>报告卡类别：</span>
                </td>
                <td class="info-value" colspan="3">
                  <div class="value2">
                    <el-form-item prop="baoGaoKLX">
                      <div class="value">
                        <el-radio-group v-model="ruleForm.baoGaoKLX">
                          <el-radio
                            v-for="data in baseInfo.baoGaoKaLB"
                            :key="data.daiMa"
                            :label="data.daiMa"
                          >
                            {{ data.mingCheng }}
                          </el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </div>
                </td>
                <td class="info-label">
                  <span>病案号：</span>
                </td>
                <td class="info-value">
                  <div class="value">
                    <span>{{ patientDetail.bingAnHao }}</span>
                  </div>
                </td>
                <td class="info-label">
                  <span>报告单位：</span>
                </td>
                <td class="info-value">
                  <div class="value">
                    <span>温州医科大学第一附属医院</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>ICD编码(ICD-10)：</span>
                </td>
                <td class="info-value" colspan="3">
                  <el-form-item prop="icd10">
                    <el-select v-model="ruleForm.icd10">
                      <el-option
                        v-for="item in baseInfo.zhenDuan"
                        :key="item.daiMa"
                        :label="item.xianShiMC"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label" colspan="4"></td>
              </tr>
              <tr>
                <td style="padding: 0; border: none">
                  <div class="content-header" style="margin-top: 14px">
                    <div class="title">基础信息</div>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>姓名：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="xingMing">
                    <el-input v-model="ruleForm.xingMing" disabled></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>性别：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="xingBie">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.xingBie" disabled>
                        <el-radio
                          v-for="data in baseInfo.xingBie"
                          :key="data.daiMa"
                          :label="data.daiMa"
                        >
                          {{ data.neiRong }}
                        </el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>出生日期：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="chuShengRQ">
                    <el-date-picker
                      v-model="ruleForm.chuShengRQ"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span>实足年龄：</span>
                </td>
                <td class="info-value">
                  <el-input></el-input>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>职业：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="hangYeDM">
                    <el-select v-model="ruleForm.hangYeDM">
                      <el-option
                        v-for="item in baseInfo.zhiYe"
                        :key="item.daiMa"
                        :label="item.xianShiMC"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>具体工种：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="zhiYeDM">
                    <el-select v-model="ruleForm.zhiYeDM">
                      <el-option
                        v-for="item in baseInfo.gongZhong"
                        :key="item.daiMa"
                        :label="item.xianShiMC"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>身份证号：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="shenFenZH">
                    <el-input v-model="ruleForm.shenFenZH"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span>民族：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="mingZu">
                    <el-select v-model="ruleForm.mingZu" placeholder="请选择">
                      <el-option
                        v-for="data in baseInfo.minZu"
                        :key="data.daiMa"
                        :label="data.xianShiMC"
                        :value="data.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span>工作单位：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="gongZuoDW">
                    <el-input v-model="ruleForm.gongZuoDW"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>联系电话：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="lianXiDH">
                    <el-input v-model="ruleForm.lianXiDH"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>文化程度：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="wenHuaCD">
                    <el-select v-model="ruleForm.wenHuaCD" placeholder="请选择">
                      <el-option
                        v-for="data in baseInfo.wenHua"
                        :key="data.daiMa"
                        :label="data.xianShiMC"
                        :value="data.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>婚姻：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="hunYinZK">
                    <el-select v-model="ruleForm.hunYinZK" placeholder="请选择">
                      <el-option
                        v-for="data in baseInfo.hunYinZK"
                        :key="data.daiMa"
                        :label="data.xianShiMC"
                        :value="data.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>常住户口地址：</span>
                </td>
                <td class="info-value" colspan="7">
                  <el-form-item prop="huKouSF" class="cz-select">
                    <el-form-item prop="huKouSF">
                      <el-select
                        v-model="ruleForm.huKouSF"
                        placeholder="-省份-"
                        @change="setAddress($event, 1, 'huKou')"
                      >
                        <el-option
                          v-for="item in addressMapList['huKou'].SF"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="huKouSJ">
                      <el-select
                        v-model="ruleForm.huKouSJ"
                        placeholder="-市级-"
                        @change="setAddress($event, 2, 'huKou')"
                      >
                        <el-option
                          v-for="item in addressMapList['huKou'].SJ"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="huKouQX">
                      <el-select
                        v-model="ruleForm.huKouQX"
                        placeholder="-区县-"
                        @change="setAddress($event, 3, 'huKou')"
                      >
                        <el-option
                          v-for="item in addressMapList['huKou'].QX"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="huKouJD">
                      <el-select v-model="ruleForm.huKouJD" placeholder="-街道-">
                        <el-option
                          v-for="item in addressMapList['huKou'].JD"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="huKouJWDM">
                      <el-input v-model="ruleForm.huKouJWDM" placeholder="-居委会(村)-"></el-input>
                    </el-form-item>
                    <el-form-item prop="huKouXXDZ">
                      <el-input v-model="ruleForm.huKouXXDZ" placeholder="-详细地址-"></el-input>
                    </el-form-item>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>目前居住地址：</span>
                </td>
                <td class="info-value" colspan="7">
                  <el-form-item prop="shiJiSF" class="cz-select">
                    <el-form-item prop="shiJiSF">
                      <el-select
                        v-model="ruleForm.shiJiSF"
                        placeholder="-省份-"
                        @change="setAddress($event, 1, 'shiJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['shiJi'].SF"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="shiJiSJ">
                      <el-select
                        v-model="ruleForm.shiJiSJ"
                        placeholder="-市级-"
                        @change="setAddress($event, 2, 'shiJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['shiJi'].SJ"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="shiJiQX">
                      <el-select
                        v-model="ruleForm.shiJiQX"
                        placeholder="-区县-"
                        @change="setAddress($event, 3, 'shiJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['shiJi'].QX"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="shiJiJD">
                      <el-select v-model="ruleForm.shiJiJD" placeholder="-街道-">
                        <el-option
                          v-for="item in addressMapList['shiJi'].JD"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="shiJiJWDM">
                      <el-input v-model="ruleForm.shiJiJWDM" placeholder="-居委会(村)-"></el-input>
                    </el-form-item>
                    <el-form-item prop="shiJiXXDZ">
                      <el-input v-model="ruleForm.shiJiXXDZ" placeholder="-详细地址-"></el-input>
                    </el-form-item>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <p style="line-height: 14px">
                    <span class="red-star">*</span>
                    <span>本辖区连续居住6月以上：</span>
                  </p>
                </td>
                <td class="info-value">
                  <el-form-item prop="lianXuJZ">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.lianXuJZ">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td style="padding: 0; border: none">
                  <div class="content-header" style="margin-top: 14px">
                    <div class="title">诊断信息</div>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label" colspan="2" rowspan="2" style="text-align: center">
                  <span>诊断</span>
                </td>
                <td class="info-label">
                  <p style="line-height: 14px">
                    <span>冠心病：</span>
                  </p>
                </td>
                <td class="info-value" colspan="6">
                  <div class="value">
                    <el-radio-group v-model="ruleForm.guanXinBZD">
                      <el-radio
                        v-for="data in baseInfo.guanXinBings"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        {{ data.neiRong }}
                      </el-radio>
                    </el-radio-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <p style="line-height: 14px">
                    <span>脑卒中：</span>
                  </p>
                </td>
                <td class="info-value" colspan="6">
                  <div class="value">
                    <el-radio-group v-model="ruleForm.naoZuZZD">
                      <el-radio
                        v-for="data in baseInfo.naoZuZhongs"
                        :key="data.daiMa"
                        :label="data.daiMa"
                      >
                        {{ data.neiRong }}
                      </el-radio>
                    </el-radio-group>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label" colspan="2" style="text-align: center">
                  <span class="red-star">*</span>
                  <span>诊断</span>
                </td>
                <td colspan="6" class="value3">
                  <table>
                    <tr>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>临床症状：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="linChuangZZ">
                          <el-select v-model="ruleForm.linChuangZZ">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>血管造影：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="xueGuanZY">
                          <el-select v-model="ruleForm.xueGuanZY">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>心电图：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="xinDianTu">
                          <el-select v-model="ruleForm.xinDianTu">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>CT：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="ct">
                          <el-select v-model="ruleForm.ct">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                    </tr>
                    <tr>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>血清酶：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="xueQingMei">
                          <el-select v-model="ruleForm.xueQingMei">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>磁共振：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="ciGongZhen">
                          <el-select v-model="ruleForm.ciGongZhen">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>脑髓液：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="naoJiYe">
                          <el-select v-model="ruleForm.naoJiYe">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>尸检：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="shiJian">
                          <el-select v-model="ruleForm.shiJian">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                    </tr>
                    <tr>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>脑电图：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="naoDianTu">
                          <el-select v-model="ruleForm.naoDianTu">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>神经科医生检查：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="shenJingKYSJC">
                          <el-select v-model="ruleForm.shenJingKYSJC">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>腰穿：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="yaoChuanZD">
                          <el-select v-model="ruleForm.yaoChuanZD">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>超声心动图：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="chaoShengZD">
                          <el-select v-model="ruleForm.chaoShengZD">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                    </tr>
                    <tr>
                      <td class="info-label">
                        <p style="line-height: 14px">
                          <span>手术：</span>
                        </p>
                      </td>
                      <td class="info-value">
                        <el-form-item prop="shouShuYJZD">
                          <el-select v-model="ruleForm.shouShuYJZD">
                            <el-option
                              v-for="item in baseInfo.jianChaJG"
                              :key="item.daiMa"
                              :label="item.xianShiMC"
                              :value="item.daiMa"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                      </td>
                      <td class="info-label" colspan="6"></td>
                    </tr>
                  </table>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>病史：</span>
                </td>
                <td class="info-value" colspan="7">
                  <div class="value2">
                    <el-form-item>
                      <div class="value">
                        <el-checkbox-group v-model="ruleForm.bingShi">
                          <el-checkbox
                            v-for="data in baseInfo.bingShi"
                            :key="data.daiMa"
                            :label="data.daiMa"
                          >
                            {{ data.mingCheng }}
                          </el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </el-form-item>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>死后推断：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="siHouTD">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.siHouTD">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="2">否</el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <p style="line-height: 14px">
                    <span>本次卒中发病时间与CT/核磁共振检查时间间隔：</span>
                  </p>
                </td>
                <td class="info-value">
                  <el-form-item prop="ciGongZSJJG">
                    <el-select v-model="ruleForm.ciGongZSJJG" placeholder="请选择">
                      <el-option
                        v-for="data in baseInfo.naoZuZhongJGSJS"
                        :key="data.daiMa"
                        :label="data.neiRong"
                        :value="data.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>首要症状(脑卒中)：</span>
                </td>
                <td class="info-value" colspan="3">
                  <div class="value2">
                    <el-form-item prop="shouYaoZZ">
                      <div class="value">
                        <el-radio-group v-model="ruleForm.shouYaoZZ">
                          <el-radio
                            v-for="data in baseInfo.naoZuZhongZZs"
                            :key="data.daiMa"
                            :label="data.daiMa"
                          >
                            {{ data.neiRong }}
                          </el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>发病日期：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="faBingRQ">
                    <el-date-picker
                      v-model="ruleForm.faBingRQ"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>确诊日期：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="zhenDuanRQ">
                    <el-date-picker
                      v-model="ruleForm.zhenDuanRQ"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>确诊单位：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="queZhenDW">
                    <el-select v-model="ruleForm.queZhenDW">
                      <el-option
                        v-for="item in baseInfo.zhenDuanDW"
                        :key="item.daiMa"
                        :label="item.xianShiMC"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>是否首次发病：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="shouCiFB">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.shouCiFB">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="2">否</el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span>死亡日期：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="siWangRQ">
                    <el-date-picker
                      v-model="ruleForm.siWangRQ"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span>死亡原因：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="siWangYY">
                    <el-select v-model="ruleForm.siWangYY">
                      <el-option label="心脑" value="1"></el-option>
                      <el-option label="非心脑" value="2"></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span>死亡ICD-10：</span>
                </td>
                <td class="info-value">
                  <el-input v-model="ruleForm.siWangICD10"></el-input>
                </td>
                <td class="info-label">
                  <span>死亡ICD名称：</span>
                </td>
                <td class="info-value">
                  <el-input v-model="ruleForm.siWangICDMC"></el-input>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>报卡医师：</span>
                </td>
                <td class="info-value">
                  <el-input v-model="ruleForm.baoGaoYS"></el-input>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>报卡日期：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="baoGaoRQ">
                    <el-date-picker
                      v-model="ruleForm.baoGaoRQ"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span class="red-star">*</span>
                  <span>转归：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="zhuanGui">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.zhuanGui">
                        <el-radio label="0">生存</el-radio>
                        <el-radio label="1">死亡</el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td style="padding: 0; border: none">
                  <div class="content-header" style="margin-top: 14px">
                    <div class="title">病史摘要</div>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span>病史摘要：</span>
                </td>
                <td class="info-value" colspan="7">
                  <el-input v-model="ruleForm.bingShiZY"></el-input>
                </td>
              </tr>
            </table>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import {
  addOrUpdateXNBGK,
  getAddressListByAddressMXB,
  getAddressListMxb,
  getBaseInfoXN,
  getPatientInfoByEmrID
} from '@/api/report-card'
import { mapState } from 'vuex'

export default {
  name: 'HeartBrainReportCard',
  props: {
    patient: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      ruleForm: {
        bingLiID: '',
        menZhenHao: '',
        bingRenBH: '',
        jiGouDM: '01',
        baoGaoKLX: '1',
        icd10: '',
        xingMing: '',
        xingBie: '1',
        chuShengRQ: '',
        hangYeDM: '', //职业
        zhiYeDM: '', //具体工种
        shenFenZH: '',
        mingZu: '1',
        gongZuoDW: '',
        lianXiDH: '',
        wenHuaCD: '',
        hunYinZK: '9',
        lianXuJZ: '1',
        huKouSF: '',
        huKouSJ: '',
        huKouQX: '',
        huKouJD: '',
        huKouJWDM: '',
        huKouXXDZ: '',
        // huKouSFDM: '',
        // huKouSDM: '',
        // huKouQXDM: '',
        // huKouJDDM: '',
        shiJiSF: '',
        shiJiSJ: '',
        shiJiQX: '',
        shiJiJD: '',
        shiJiJWDM: '',
        shiJiXXDZ: '',
        // shiJiSFDM: '',
        // shiJiSDM: '',
        // shiJiQXDM: '',
        // shiJiJDDM: '',
        guanXinBZD: '',
        naoZuZZD: '',
        linChuangZZ: '',
        xueGuanZY: '',
        xinDianTu: '',
        ct: '',
        xueQingMei: '',
        ciGongZhen: '',
        naoJiYe: '',
        shiJian: '',
        naoDianTu: '',
        shenJingKYSJC: '',
        yaoChuanZD: '',
        chaoShengZD: '',
        shouShuYJZD: '',
        bingShi: [],
        siHouTD: '',
        ciGongZSJJG: '',
        shouYaoZZ: '',
        faBingRQ: '',
        zhenDuanRQ: '',
        queZhenDW: '',
        shouCiFB: '',
        siWangRQ: '',
        siWangYY: '',
        siWangICD10: '',
        siWangICDMC: '',
        baoGaoYS: '',
        baoGaoRQ: '',
        zhuanGui: '0',
        bingShiZY: ''
      },
      rules: {
        baoGaoKLX: [{ required: true, message: '请选择', trigger: 'change' }],
        xingMing: [{ required: true, message: '请输入', trigger: 'blur' }],
        xingBie: [{ required: true, message: '请选择', trigger: 'change' }],
        chuShengRQ: [{ required: true, message: '请选择', trigger: 'change' }],
        hangYeDM: [{ required: true, message: '请选择', trigger: 'change' }],
        zhiYeDM: [{ required: true, message: '请选择', trigger: 'change' }],
        shenFenZH: [{ required: true, message: '请输入', trigger: 'blur' }],
        lianXiDH: [{ required: true, message: '请输入', trigger: 'blur' }],
        wenHuaCD: [{ required: true, message: '请选择', trigger: 'change' }],
        hunYinZK: [{ required: true, message: '请选择', trigger: 'change' }],
        lianXuJZ: [{ required: true, message: '请选择', trigger: 'change' }],
        huKouSF: [{ required: true, message: '请选择', trigger: 'change' }],
        // huKouSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        // huKouQX: [{ required: true, message: '请选择', trigger: 'change' }],
        // huKouJD: [{ required: true, message: '请选择', trigger: 'change' }],
        // huKouJWDM: [{ required: true, message: '请输入', trigger: 'blur' }],
        huKouXXDZ: [{ required: true, message: '请输入', trigger: 'blur' }],
        shiJiSF: [{ required: true, message: '请选择', trigger: 'change' }],
        // shiJiSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        // shiJiQX: [{ required: true, message: '请选择', trigger: 'change' }],
        // shiJiJD: [{ required: true, message: '请选择', trigger: 'change' }],
        // shiJiJWDM: [{ required: true, message: '请输入', trigger: 'blur' }],
        shiJiXXDZ: [{ required: true, message: '请输入', trigger: 'blur' }],
        icd10: [{ required: true, message: '请选择', trigger: 'change' }],
        bingShi: [{ required: true, message: '请选择', trigger: 'change' }],
        siHouTD: [{ required: true, message: '请选择', trigger: 'change' }],
        shouYaoZZ: [{ required: true, message: '请选择', trigger: 'change' }],
        faBingRQ: [{ required: true, message: '请选择', trigger: 'change' }],
        zhenDuanRQ: [{ required: true, message: '请选择', trigger: 'change' }],
        queZhenDW: [{ required: true, message: '请选择', trigger: 'change' }],
        shouCiFB: [{ required: true, message: '请选择', trigger: 'change' }],
        baoGaoRQ: [{ required: true, message: '请选择', trigger: 'change' }],
        zhuanGui: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      bingLiID: '',
      baseInfo: {},
      addressMapList: {
        huKou: { SF: [], SJ: [], QX: [], JD: [] },
        shiJi: { SF: [], SJ: [], QX: [], JD: [] }
      },
      addressMap: { 0: 'SF', 1: 'SJ', 2: 'QX', 3: 'JD' }
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    })
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      //获取报告卡数据
      await this.getReportCard()
      //获取地址信息
      const resAddress = await this.getAddress(0, 0)
      this.addressMapList['huKou']['SF'] = resAddress
      this.addressMapList['shiJi']['SF'] = resAddress
      if (this.patient) {
        this.ruleForm = this.patient
        this.bingLiID = this.patient.bingLiID
        await this.dataHandle(this.ruleForm, false)
      } else {
        this.bingLiID = this.$route.params.id
        //获取报告卡数据
        await this.getPatient()
      }
    },
    async getPatient() {
      const now = new Date()
      const ruleForm = this.ruleForm

      const resData = await getPatientInfoByEmrID({ bingLiID: this.bingLiID })
      const patientDetail = resData['data']

      console.log('patientDetail:', patientDetail)
      console.log('this.patientDetail:', this.patientDetail)

      ruleForm.xingMing = this.patientDetail.bingRenXM
      ruleForm.xingBie = this.patientDetail.xingBie
      ruleForm.chuShengRQ = this.patientDetail.chuShengRQ
      ruleForm.shenFenZH = patientDetail.shenFenZH
      ruleForm.lianXiDH = this.patientDetail.lianXiDH
      ruleForm.bingRenBH = this.patientDetail.bingRenBH
      ruleForm.menZhenHao = this.patientDetail.bingRenBH
      ruleForm.bingLiID = this.bingLiID

      const resAddress = await getAddressListByAddressMXB({
        diZhi: this.patientDetail.lianXiDZ,
        yuanQuDM: patientDetail.ruYuanYQ
      })
      const addressList = resAddress.data.diZhiBM.split('-')
      ruleForm.huKouSF = addressList[0]
      ruleForm.shiJiSF = addressList[0]
      for (let i = 1; i < 4; i++) {
        await this.setAddress(ruleForm['huKou' + this.addressMap[i - 1]], i, 'huKou')
        ruleForm['huKou' + this.addressMap[i]] = addressList[i]
      }
      for (let i = 1; i < 4; i++) {
        await this.setAddress(ruleForm['shiJi' + this.addressMap[i - 1]], i, 'shiJi')
        ruleForm['shiJi' + this.addressMap[i]] = addressList[i]
      }
      ruleForm.huKouXXDZ = this.patientDetail.lianXiDZ
      ruleForm.shiJiXXDZ = this.patientDetail.lianXiDZ

      ruleForm.queZhenDW = '1'
      ruleForm.baoGaoYS = this.patientInfo.yongHuXM
      ruleForm.baoGaoRQ = now
    },
    async getReportCard() {
      const res = await getBaseInfoXN({})
      this.baseInfo = res.data
    },
    async getAddress(id, code) {
      const res = await getAddressListMxb({ leiXing: id, shangJiDM: code })
      return res.data
    },
    async setAddress(code, id, type) {
      for (let i = id; i < 4; i++) {
        this.addressMapList[type][this.addressMap[i]] = []
        this.ruleForm[type + this.addressMap[i]] = ''
      }
      this.addressMapList[type][this.addressMap[id]] = await this.getAddress(id, code)
    },
    submitForm(formName) {
      //保存
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let reqData = JSON.parse(JSON.stringify(this.ruleForm))
          await this.dataHandle(reqData, true)
          console.log('ruleForm:', this.ruleForm)
          const res = await addOrUpdateXNBGK(reqData)
          console.log(res)
          if (res.hasError === -1) {
            this.$message.error(res.errorMessage)
          } else {
            this.$message({
              message: res.errorMessage,
              type: 'success'
            })
          }
        } else {
          this.$message({
            message: '请补充必填内容！',
            type: 'warning'
          })
          return false
        }
      })
    },
    async dataHandle(reqData, type) {
      const keyMap = { SF: 'SFDM', SJ: 'SDM', QX: 'QXDM', JD: 'JDDM' }
      const listToStr = ['bingShi']
      const hzXXMap = {
        zhuYuanID: 0, //住院ID
        xingMing: '', //姓名
        xingBie: '', //性别
        mingZu: '', //名族
        gongZuoDW: '', //工作单位
        wenHuaCD: '', //文化程度
        chuShengRQ: '', //出生日期
        shenFenZH: '', //身份证号
        lianXiDH: '', //联系电话
        hunYinZK: '', //婚姻状况
        hangYeDM: '', //行业代码
        zhiYeDM: '', //职业代码
        lianXuJZ: '', //连续居住
        huKouSFDM: '', //户口省份代码
        huKouSDM: '', //户口市代码
        huKouQXDM: '', //户口区县代码
        huKouJDDM: '', //户口街道代码
        huKouJWDM: '', //户口居委代码
        huKouXXDZ: '', //户口详细地址
        shiJiSFDM: '', //实际省份代码
        shiJiSDM: '', //实际市代码
        shiJiQXDM: '', //实际区县代码
        shiJiJDDM: '', //实际街道代码
        shiJiJWDM: '', //实际居委代码
        shiJiXXDZ: '', //实际详细地址
        shenHeBZ: '', //审核标志
        shenHeSJ: '', //审核时间
        shenHeRY: 0, //审核人员
        leiXing: '', //类型
        bingRenBH: '', //病人编号
        banBen: '', //版本
        bingRenXH: '', //病人序号
        deZhiTBLX: '', //地址同步类型
        jiGouDM: '' //机构代码
      }
      if (type) {
        for (const key in keyMap) {
          reqData['huKou' + keyMap[key]] = reqData['huKou' + key]
        }
        for (const key in keyMap) {
          reqData['shiJi' + keyMap[key]] = reqData['shiJi' + key]
        }
        for (const key of listToStr) {
          if (reqData[key]) {
            reqData[key] = reqData[key].join(',')
          } else {
            reqData[key] = ''
          }
        }
        reqData['ezjjkHzxxQo'] = {}
        for (const key in hzXXMap) {
          if (reqData[key]) {
            reqData['ezjjkHzxxQo'][key] = reqData[key]
          }
        }
      } else {
        for (const key of listToStr) {
          if (reqData[key]) {
            reqData[key] = reqData[key].split(',')
          } else {
            reqData[key] = []
          }
        }
        for (const key in hzXXMap) {
          if (reqData['zjjkHzxx'][key]) {
            reqData[key] = reqData['zjjkHzxx'][key]
          }
        }
        for (const key in keyMap) {
          reqData['huKou' + key] = reqData['huKou' + keyMap[key]]
        }
        for (const key in keyMap) {
          reqData['shiJi' + key] = reqData['shiJi' + keyMap[key]]
        }
        for (const key in this.addressMapList) {
          for (let i = 1; i < 4; i++) {
            const k = this.addressMap[i - 1]
            this.addressMapList[key][this.addressMap[i]] = await this.getAddress(
              i,
              reqData[key + k]
            )
          }
        }
      }
    },
    sendData() {
      const data = { dialogVisible: false }
      this.$emit('childEvent', data) // 触发事件并传递数据
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;
    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }
  .value3 {
    border: none;
    padding: 0;
    ::v-deep .el-form-item {
      width: 140px !important;
    }
  }
  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }
  .info-value {
    background-color: #f7f9fd;
    .value {
      margin-left: 10px;
      .risk {
        margin-right: 15px;
      }
    }
    a {
      text-decoration: underline;
      color: #356ac5;
    }
    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }
    .cz-select {
      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }
      ::v-deep .el-input {
        margin-right: 5px;
      }
    }
    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }
      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }
      ::v-deep .el-select {
        width: 100%;
      }
      ::v-deep .el-input {
        width: 100%;
      }
    }
    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }
  }
}

.red-star {
  color: red;
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
</style>
