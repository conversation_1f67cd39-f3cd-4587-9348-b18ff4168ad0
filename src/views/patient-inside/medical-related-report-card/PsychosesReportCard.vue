<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">重精报告卡</div>
        <div>
          <el-button type="primary" @click="submitForm('ruleForm')">保存</el-button>
          <el-button v-if="patient" type="primary" @click="sendData">返回上一层</el-button>
        </div>
      </div>
      <div class="content" style="border: #dadee5 1px solid; overflow: auto">
        <el-form ref="ruleForm" :model="ruleForm" :rules="rules">
          <div class="content-header">
            <div class="title">基础信息</div>
          </div>
          <div class="el-form">
            <table>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>患者信息完整度：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="huanZheXXWZX">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.huanZheXXWZX">
                        <el-radio label="1">完整</el-radio>
                        <el-radio label="2">不完整</el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>姓名：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="xingMing">
                    <el-input v-model="ruleForm.xingMing"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>证件类型：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="zhengjianLX">
                    <el-select v-model="ruleForm.zhengjianLX">
                      <el-option label="身份证" value="1"></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>证件号码：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="shenFenZH">
                    <el-input v-model="ruleForm.shenFenZH"></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>性别：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="xingBie">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.xingBie">
                        <el-radio
                          v-for="data in baseInfo.xingBie"
                          :key="data.daiMa"
                          :label="data.daiMa"
                        >
                          {{ data.neiRong }}
                        </el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>出生日期：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="chuShengRQ">
                    <el-date-picker
                      v-model="ruleForm.chuShengRQ"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>就业情况：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="zhiYe">
                    <el-select v-model="ruleForm.zhiYe">
                      <el-option
                        v-for="item in baseInfo.zhiYeList"
                        :key="item.daiMa"
                        :label="item.mingCheng"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>患者来源：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="huanZheLY">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.huanZheLY">
                        <el-radio label="1">门诊</el-radio>
                        <el-radio label="2">住院</el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>监护人姓名：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="lianXiRXM">
                    <el-input v-model="ruleForm.lianXiRXM"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>监护人电话：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="lianXiRDH">
                    <el-input v-model="ruleForm.lianXiRDH"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>与患者关系：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="yuHuanZGX">
                    <el-select v-model="ruleForm.yuHuanZGX">
                      <el-option
                        v-for="item in baseInfo.huanZheGX"
                        :key="item.daiMa"
                        :label="item.mingCheng"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>民族：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="minZuDM">
                    <el-select v-model="ruleForm.minZuDM">
                      <el-option
                        v-for="item in baseInfo.minZu"
                        :key="item.daiMa"
                        :label="item.mingCheng"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>户籍地址：</span>
                </td>
                <td class="info-value" colspan="7">
                  <el-form-item prop="huJiSF" class="cz-select">
                    <el-form-item prop="huJiSF">
                      <el-select
                        v-model="ruleForm.huJiSF"
                        placeholder="-省份-"
                        @change="setAddress($event, 1, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].SF"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="huJiSJ">
                      <el-select
                        v-model="ruleForm.huJiSJ"
                        placeholder="-市级-"
                        @change="setAddress($event, 2, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].SJ"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="huJiQX">
                      <el-select
                        v-model="ruleForm.huJiQX"
                        placeholder="-区县-"
                        @change="setAddress($event, 3, 'huJi')"
                      >
                        <el-option
                          v-for="item in addressMapList['huJi'].QX"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="huJiJD">
                      <el-select v-model="ruleForm.huJiJD" placeholder="-街道-">
                        <el-option
                          v-for="item in addressMapList['huJi'].JD"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="huJiDZ">
                      <el-input v-model="ruleForm.huJiDZ" placeholder="-居委会(村)-"></el-input>
                    </el-form-item>
                    <el-form-item prop="huJiXXDZ">
                      <el-input v-model="ruleForm.huJiXXDZ" placeholder="-详细地址-"></el-input>
                    </el-form-item>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>现住址详细地址：</span>
                </td>
                <td class="info-value" colspan="7">
                  <el-form-item prop="juZhuSF" class="cz-select">
                    <el-form-item prop="juZhuSF">
                      <el-select
                        v-model="ruleForm.juZhuSF"
                        placeholder="-省份-"
                        @change="setAddress($event, 1, 'juZhu')"
                      >
                        <el-option
                          v-for="item in addressMapList['juZhu'].SF"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="juZhuSJ">
                      <el-select
                        v-model="ruleForm.juZhuSJ"
                        placeholder="-市级-"
                        @change="setAddress($event, 2, 'juZhu')"
                      >
                        <el-option
                          v-for="item in addressMapList['juZhu'].SJ"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="juZhuQX">
                      <el-select
                        v-model="ruleForm.juZhuQX"
                        placeholder="-区县-"
                        @change="setAddress($event, 3, 'juZhu')"
                      >
                        <el-option
                          v-for="item in addressMapList['juZhu'].QX"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="juZhuJD">
                      <el-select v-model="ruleForm.juZhuJD" placeholder="-街道-">
                        <el-option
                          v-for="item in addressMapList['juZhu'].JD"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                    <el-form-item prop="juZhuDZ">
                      <el-input v-model="ruleForm.juZhuDZ" placeholder="-居委会(村)-"></el-input>
                    </el-form-item>
                    <el-form-item prop="juZhuXXDZ">
                      <el-input v-model="ruleForm.juZhuXXDZ" placeholder="-详细地址-"></el-input>
                    </el-form-item>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>户籍地址类型：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="huJiDZLX">
                    <el-select v-model="ruleForm.huJiDZLX">
                      <el-option
                        v-for="item in baseInfo.diZhiLX"
                        :key="item.daiMa"
                        :label="item.neiRong"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>现住地址类型：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="juZhuDZLX">
                    <el-select v-model="ruleForm.juZhuDZLX">
                      <el-option
                        v-for="item in baseInfo.diZhiLX"
                        :key="item.daiMa"
                        :label="item.neiRong"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <p style="line-height: 14px">
                    <span style="color: red">*</span>
                    <span>两系三代严重精神障碍家族史：</span>
                  </p>
                </td>
                <td class="info-value">
                  <el-form-item prop="jingShenJBJZS">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.jingShenJBJZS">
                        <el-radio label="1">有</el-radio>
                        <el-radio label="0">无</el-radio>
                        <el-radio label="2">不详</el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>婚姻状况：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="hunYinDM">
                    <el-select v-model="ruleForm.hunYinDM">
                      <el-option
                        v-for="item in baseInfo.hunYinZK"
                        :key="item.daiMa"
                        :label="item.mingCheng"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>文化程度：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="wenHuaCD">
                    <el-select v-model="ruleForm.wenHuaCD">
                      <el-option
                        v-for="item in baseInfo.wenHuaCD"
                        :key="item.daiMa"
                        :label="item.mingCheng"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>户别：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="huKouSB">
                    <el-select v-model="ruleForm.huKouSB">
                      <el-option
                        v-for="item in baseInfo.huBie"
                        :key="item.daiMa"
                        :label="item.mingCheng"
                        :value="item.daiMa"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
              </tr>
            </table>
          </div>
          <div class="content-header" style="margin-top: 14px">
            <div class="title">病情信息</div>
          </div>
          <div class="el-form">
            <table>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>初次发病时间：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="chuCiFBSJ">
                    <el-date-picker
                      v-model="ruleForm.chuCiFBSJ"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span>既往住院情况：</span>
                </td>
                <td class="info-value">
                  <div style="display: flex; align-items: center; margin-left: 5px">
                    <p style="line-height: 14px; width: 120px">
                      <span>曾住精神专科医院/综合医院精神科</span>
                    </p>
                    <el-input
                      v-model="ruleForm.jiWangZYCS"
                      style="width: 50px; margin: 0 5px 0 5px"
                    ></el-input>
                    <span>次</span>
                  </div>
                </td>
                <td class="info-label">
                  <span>既往关锁情况：</span>
                </td>
                <td class="info-value">
                  <div class="value2 radio">
                    <el-form-item prop="jiWangGSQK">
                      <div class="value">
                        <el-radio-group v-model="ruleForm.jiWangGSQK">
                          <el-radio label="1">无关锁</el-radio>
                          <el-radio label="2">关锁</el-radio>
                          <el-radio label="3">关锁已解除</el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </div>
                </td>
                <td class="info-value" colspan="2"></td>
              </tr>
              <tr>
                <td class="info-label">
                  <p style="line-height: 14px">
                    <span style="color: red">*</span>
                    <span>是否已进行抗精神疾病药物治疗：</span>
                  </p>
                </td>
                <td class="info-value">
                  <el-form-item prop="shiFouYWZL">
                    <div class="value">
                      <el-radio-group v-model="ruleForm.shiFouYWZL">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                      </el-radio-group>
                    </div>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <p style="line-height: 14px">
                    <span style="color: red">*</span>
                    <span>首次抗精神疾病药物治疗时间：</span>
                  </p>
                </td>
                <td class="info-value">
                  <el-form-item prop="yaoWuZLSJ">
                    <el-date-picker
                      v-model="ruleForm.yaoWuZLSJ"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>送诊主体：</span>
                </td>
                <td class="info-value" colspan="3">
                  <div class="value2 radio">
                    <el-form-item prop="songZhenZT">
                      <div class="value">
                        <el-radio-group v-model="ruleForm.songZhenZT">
                          <el-radio
                            v-for="data in baseInfo.songZhenZT"
                            :key="data.daiMa"
                            :label="data.daiMa"
                          >
                            {{ data.mingCheng }}
                          </el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>确诊医院：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="queZhenYY">
                    <el-input v-model="ruleForm.queZhenYY"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>确诊时间：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="queZhenRQ">
                    <el-date-picker
                      v-model="ruleForm.queZhenRQ"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>疾病名称：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="jiBingMC">
                    <el-select v-model="ruleForm.jiBingMC" @change="getICD">
                      <el-option
                        v-for="item in baseInfo.zhenDuan"
                        :key="item.daiMa"
                        :label="item.mingCheng"
                        :value="item.mingCheng"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>ICD-10编码：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="jiBingDM">
                    <el-input v-model="ruleForm.jiBingDM" disabled></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>目前用药情况：</span>
                  <div style="text-align: center">
                    <el-button style="color: #356ac5; margin-top: 5px" @click="tcView = true">
                      新增
                    </el-button>
                  </div>
                </td>
                <td class="info-value" colspan="7">
                  <el-table :data="tableData" border size="medium" style="width: 100%">
                    <el-table-column prop="yaoPinID" label="药物ID"></el-table-column>
                    <el-table-column prop="yaoPinMC" label="药物名称"></el-table-column>
                    <el-table-column prop="changXiaoZJ" label="长效"></el-table-column>
                    <el-table-column prop="fuYaoJG" label="服药间隔">
                      <template #default="scope">
                        <span>{{ scope.row.fuYaoJG === '1' ? '日' : '月' }}</span>
                      </template>
                    </el-table-column>
                    <el-table-column prop="fuYaoCS" label="服药次数"></el-table-column>
                    <el-table-column prop="fuYaoJL" label="服药剂量"></el-table-column>
                    <el-table-column prop="shiPinMC" label="操作" align="center">
                      <template #default="scope">
                        <el-button type="text" @click="tcDel(scope.$index)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>

                  <el-dialog title="重精报告-用药情况" :visible.sync="tcView" width="300px">
                    <el-form :model="formData">
                      <table>
                        <tr>
                          <td class="info-label2">
                            <span>长效制剂：</span>
                          </td>
                          <td class="info-value">
                            <div class="value">
                              <el-radio-group v-model="formData.changXiaoZJ">
                                <el-radio label="1">是</el-radio>
                                <el-radio label="0">否</el-radio>
                              </el-radio-group>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="info-label2">
                            <span>药物名称：</span>
                          </td>
                          <td class="info-value">
                            <el-select v-model="formData.yaoPinMC" @change="getYaoPin">
                              <el-option
                                v-for="item in baseInfo.yaoPin"
                                :key="item.daiMa"
                                :label="item.mingCheng"
                                :value="item.mingCheng"
                              ></el-option>
                            </el-select>
                          </td>
                        </tr>
                        <tr>
                          <td class="info-label2">
                            <span>服药间隔：</span>
                          </td>
                          <td class="info-value">
                            <div class="value">
                              <el-radio-group v-model="formData.fuYaoJG">
                                <el-radio label="1">日</el-radio>
                                <el-radio label="0">月</el-radio>
                              </el-radio-group>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="info-label2">
                            <span>服药次数：</span>
                          </td>
                          <td class="info-value">
                            <el-input v-model="formData.fuYaoCS"></el-input>
                          </td>
                        </tr>
                        <tr>
                          <td class="info-label2">
                            <span>服药剂量：</span>
                          </td>
                          <td class="info-value">
                            <el-input v-model="formData.fuYaoJL"></el-input>
                          </td>
                        </tr>
                        <tr>
                          <td class="info-label2" rowspan="3">
                            <span>用量用法：</span>
                          </td>
                          <td class="info-value">
                            <div style="display: flex; align-items: center">
                              <span>早</span>
                              <el-input
                                v-model="formData.jiLiangZao"
                                style="width: 50px; margin: 0 5px"
                              ></el-input>
                              <span>mg</span>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="info-value">
                            <div style="display: flex; align-items: center">
                              <span>中</span>
                              <el-input
                                v-model="formData.jiLiangZhong"
                                style="width: 50px; margin: 0 5px"
                              ></el-input>
                              <span>mg</span>
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td class="info-value">
                            <div style="display: flex; align-items: center">
                              <span>晚</span>
                              <el-input
                                v-model="formData.jiLiangWan"
                                style="width: 50px; margin: 0 5px"
                              ></el-input>
                              <span>mg</span>
                            </div>
                          </td>
                        </tr>
                      </table>
                    </el-form>
                    <div slot="footer" style="margin: 12px">
                      <el-button size="small" type="primary" @click="tcSave">保存</el-button>
                      <el-button size="small" style="margin-left: 10px" @click="tcView = false">
                        关闭
                      </el-button>
                    </div>
                  </el-dialog>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>既往危险行为：</span>
                </td>
                <td class="info-value" colspan="3">
                  <div class="value2">
                    <el-form-item prop="jiWangWXXW">
                      <el-select v-model="ruleForm.jiWangWXXW">
                        <el-option
                          v-for="item in baseInfo.weiXianXW"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>既往危险性评估：</span>
                </td>
                <td class="info-value" colspan="3">
                  <div class="value2">
                    <el-form-item prop="weiXianXPG">
                      <div class="value">
                        <el-radio-group v-model="ruleForm.weiXianXPG">
                          <el-radio
                            v-for="data in baseInfo.weiXianFJ"
                            :key="data.daiMa"
                            :label="data.daiMa"
                          >
                            {{ data.mingCheng }}
                          </el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </div>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>知情同意：</span>
                </td>
                <td class="info-value" colspan="3">
                  <div class="value2">
                    <el-form-item prop="zhiQingTY">
                      <div class="value">
                        <el-radio-group v-model="ruleForm.zhiQingTY">
                          <el-radio
                            v-for="data in baseInfo.zhiQingTY"
                            :key="data.daiMa"
                            :label="data.daiMa"
                          >
                            {{ data.mingCheng }}
                          </el-radio>
                        </el-radio-group>
                      </div>
                    </el-form-item>
                  </div>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>知情同意签字时间：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="qianZiSJ">
                    <el-date-picker
                      v-model="ruleForm.qianZiSJ"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span>备注：</span>
                </td>
                <td class="info-value" colspan="5">
                  <div class="value2">
                    <el-form-item prop="beiZhu">
                      <el-input v-model="ruleForm.beiZhu"></el-input>
                    </el-form-item>
                  </div>
                </td>
              </tr>
            </table>
          </div>
          <div class="content-header" style="margin-top: 14px">
            <div class="title">报告者信息</div>
          </div>
          <div class="el-form">
            <table>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>填卡医生：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="tiankaYS">
                    <el-input v-model="ruleForm.tiankaYS"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span>医生电话：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="yishengDH">
                    <el-input v-model="ruleForm.yishengDH"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>填卡时间：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="tiankaSJ">
                    <el-date-picker
                      v-model="ruleForm.tiankaSJ"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>报告地区：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="baogaoDQ">
                    <el-input v-model="ruleForm.baogaoDQ"></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>报告单位：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="baogaoDW">
                    <el-input v-model="ruleForm.baogaoDW"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>报告科室：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="baogaoKS">
                    <el-input v-model="ruleForm.baogaoKS"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>联系电话：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="lianxiDH">
                    <el-input v-model="ruleForm.lianxiDH"></el-input>
                  </el-form-item>
                </td>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>录入人：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="luruR">
                    <el-input v-model="ruleForm.luruR"></el-input>
                  </el-form-item>
                </td>
              </tr>
              <tr>
                <td class="info-label">
                  <span style="color: red">*</span>
                  <span>录入日期：</span>
                </td>
                <td class="info-value">
                  <el-form-item prop="luruRQ">
                    <el-date-picker
                      v-model="ruleForm.luruRQ"
                      value-format="yyyy-MM-dd"
                      type="date"
                      placeholder="选择日期"
                    ></el-date-picker>
                  </el-form-item>
                </td>
              </tr>
            </table>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import {
  addOrUpdateZJBGK,
  getAddressList,
  getAddressListByAddress,
  getBaseInfoZJ,
  getPatientInfoByEmrID
} from '@/api/report-card'
import { mapState } from 'vuex'

export default {
  name: 'PsychosesReportCard',
  props: {
    patient: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      formData: {
        yaoPinID: 0, //药品ID
        yaoPinMC: '', //药品名称
        fuYaoJG: '', //服药间隔
        changXiaoZJ: '0', //长效制剂
        fuYaoCS: '', //服药次数
        fuYaoJL: '', //服药剂量
        jiLiangWan: '0', //剂量-晚
        jiLiangZao: '0', //剂量-早
        jiLiangZhong: '0', //剂量-中
        yongYaoLX: '' //用药类型
      },
      tableData: [],
      ruleForm: {
        jiGouDM: '01',
        bingLiID: '',
        huanZheXXWZX: '1',
        xingMing: '',
        zhengjianLX: '1',
        shenFenZH: '',
        xingBie: '',
        chuShengRQ: '',
        zhiYe: '9',
        huanZheLY: '1',
        lianXiRXM: '',
        lianXiRDH: '',
        yuHuanZGX: '1',
        minZuDM: '01',
        huJiSF: '',
        huJiSJ: '',
        huJiQX: '',
        huJiJD: '',
        huJiDZBM: '',
        huJiDZ: '',
        huJiXXDZ: '',
        juZhuSF: '',
        juZhuSJ: '',
        juZhuQX: '',
        juZhuJD: '',
        juZhuDZBM: '',
        juZhuDZ: '',
        juZhuXXDZ: '',
        huJiDZLX: '',
        juZhuDZLX: '',
        jingShenJBJZS: '0',
        hunYinDM: '1',
        wenHuaCD: '9',
        huKouSB: '9',

        chuCiFBSJ: '',
        jiWangZYCS: '',
        jiWangGSQK: '1',
        shiFouYWZL: '0',
        yaoWuZLSJ: '',
        songZhenZT: '1',
        queZhenYY: '温州医科大学附属第一医院',
        queZhenRQ: '',
        jiBingMC: '',
        jiBingDM: '',
        jiBingICD10: '',

        ypxxQos: [],

        jiWangWXXW: '',
        weiXianXPG: '6',
        zhiQingTY: '1',
        qianZiSJ: '',
        beiZhu: '',

        tiankaYS: '',
        yishengDH: '',
        tiankaSJ: '',
        baogaoDQ: '瓯海区',
        baogaoDW: '温州医科大学附属第一医院',
        baogaoKS: '精神科',
        lianxiDH: '',
        luruR: '',
        luruRQ: ''
      },
      rules: {
        huanZheXXWZX: [{ required: true, message: '请选择', trigger: 'change' }],
        xingMing: [{ required: true, message: '请输入', trigger: 'blur' }],
        shenFenZH: [{ required: true, message: '请输入', trigger: 'blur' }],
        xingBie: [{ required: true, message: '请选择', trigger: 'change' }],
        chuShengRQ: [{ required: true, message: '请选择', trigger: 'change' }],
        zhiYe: [{ required: true, message: '请选择', trigger: 'change' }],
        huanZheLY: [{ required: true, message: '请选择', trigger: 'change' }],
        lianXiRXM: [{ required: true, message: '请选择', trigger: 'change' }],
        lianXiRDH: [{ required: true, message: '请选择', trigger: 'change' }],
        yuHuanZGX: [{ required: true, message: '请选择', trigger: 'change' }],
        minZuDM: [{ required: true, message: '请选择', trigger: 'change' }],
        huJiDZ: [{ required: true, message: '请输入', trigger: 'blur' }],
        huJiXXDZ: [{ required: true, message: '请输入', trigger: 'blur' }],
        juZhuDZ: [{ required: true, message: '请输入', trigger: 'blur' }],
        juZhuXXDZ: [{ required: true, message: '请输入', trigger: 'blur' }],
        huJiDZLX: [{ required: true, message: '请选择', trigger: 'change' }],
        juZhuDZLX: [{ required: true, message: '请选择', trigger: 'change' }],
        jingShenJBJZS: [{ required: true, message: '请选择', trigger: 'change' }],
        hunYinDM: [{ required: true, message: '请选择', trigger: 'change' }],
        wenHuaCD: [{ required: true, message: '请选择', trigger: 'change' }],
        huKouSB: [{ required: true, message: '请选择', trigger: 'change' }],
        chuCiFBSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        shiFouYWZL: [{ required: true, message: '请选择', trigger: 'change' }],
        yaoWuZLSJ: [{ required: true, message: '请选择', trigger: 'change' }],
        songZhenZT: [{ required: true, message: '请选择', trigger: 'change' }],
        queZhenYY: [{ required: true, message: '请输入', trigger: 'blur' }],
        queZhenRQ: [{ required: true, message: '请选择', trigger: 'change' }],
        jiBingMC: [{ required: true, message: '请选择', trigger: 'change' }],
        jiBingDM: [{ required: true, message: '请输入', trigger: 'blur' }],
        jiWangWXXW: [{ required: true, message: '请选择', trigger: 'change' }],
        weiXianXPG: [{ required: true, message: '请选择', trigger: 'change' }],
        zhiQingTY: [{ required: true, message: '请选择', trigger: 'change' }],
        qianZiSJ: [{ required: true, message: '请选择', trigger: 'change' }]
      },
      bingLiID: '',
      baseInfo: {},
      addressMapList: {
        huJi: { SF: [], SJ: [], QX: [], JD: [] },
        juZhu: { SF: [], SJ: [], QX: [], JD: [] }
      },
      addressMap: { 0: 'SF', 1: 'SJ', 2: 'QX', 3: 'JD' },
      tcView: false
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    })
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      //获取报告卡数据
      await this.getReportCard()
      //获取地址信息
      const address = await this.getAddress(0, 0)
      this.addressMapList['huJi']['SF'] = address
      this.addressMapList['juZhu']['SF'] = address
      if (this.patient) {
        this.ruleForm = this.patient
        this.bingLiID = this.patient.bingLiID
        await this.dataHandle(this.ruleForm)
      } else {
        this.bingLiID = this.$route.params.id
        //获取报告卡数据
        await this.getPatient()
      }
    },
    async getPatient() {
      const now = new Date()
      const ruleForm = this.ruleForm

      const resData = await getPatientInfoByEmrID({ bingLiID: this.bingLiID })
      const patientDetail = resData['data']
      // console.log('patientDetail:', patientDetail)
      // console.log('this.patientDetail:', this.patientDetail)

      console.log('this.patientInfo:', this.patientInfo)
      ruleForm.tiankaYS = this.patientInfo.yongHuXM
      ruleForm.luruR = this.patientInfo.yongHuXM
      ruleForm.tiankaSJ = now
      ruleForm.luruRQ = now

      ruleForm.bingLiID = this.bingLiID

      ruleForm.shenFenZH = patientDetail.shenFenZH
      ruleForm.xingMing = this.patientDetail.bingRenXM
      ruleForm.xingBie = this.patientDetail.xingBie
      ruleForm.chuShengRQ = this.patientDetail.chuShengRQ + ' 00:00:00'
      ruleForm.lianXiRDH = this.patientDetail.lianXiDH

      const resAddress = await getAddressListByAddress({ diZhi: this.patientDetail.lianXiDZ })
      ruleForm.huJiDZLX = resAddress.data.duiZhaoLB
      ruleForm.juZhuDZLX = resAddress.data.duiZhaoLB
      ruleForm.huJiXXDZ = this.patientDetail.lianXiDZ
      ruleForm.juZhuXXDZ = this.patientDetail.lianXiDZ
      const addressList = resAddress.data.diZhiBM.split('-')
      ruleForm.huJiSF = addressList[0]
      for (let i = 1; i < 4; i++) {
        await this.setAddress(ruleForm['huJi' + this.addressMap[i - 1]], i, 'huJi')
        ruleForm['huJi' + this.addressMap[i]] = addressList[i]
      }
      ruleForm.juZhuSF = addressList[0]
      for (let i = 1; i < 4; i++) {
        await this.setAddress(ruleForm['juZhu' + this.addressMap[i - 1]], i, 'juZhu')
        ruleForm['juZhu' + this.addressMap[i]] = addressList[i]
      }
    },
    async getReportCard() {
      const res = await getBaseInfoZJ({})
      this.baseInfo = res.data
    },
    async getAddress(id, code) {
      const res = await getAddressList({ leiXing: id, shangJiDM: code })
      return res.data
    },
    async setAddress(code, id, type) {
      for (let i = id; i < 4; i++) {
        this.addressMapList[type][this.addressMap[i]] = []
        this.ruleForm[type + this.addressMap[i]] = ''
      }
      this.addressMapList[type][this.addressMap[id]] = await this.getAddress(id, code)
    },
    submitForm(formName) {
      //保存
      this.$refs[formName].validate(async (valid) => {
        if (valid) {
          let reqData = JSON.parse(JSON.stringify(this.ruleForm))
          reqData['jiuYeQK'] = reqData['zhiYe']
          reqData['ypxxQos'] = this.tableData
          console.log('reqData:', reqData)
          const res = await addOrUpdateZJBGK(reqData)
          console.log(res)
          if (res.hasError === -1) {
            this.$message.error(res.errorMessage)
          } else {
            this.$message({
              message: res.errorMessage,
              type: 'success'
            })
          }
        } else {
          this.$message({
            message: '请补充必填内容！',
            type: 'warning'
          })
          return false
        }
      })
    },
    tcSave() {
      const data = { ...this.formData }
      console.log(data)
      this.tableData.push(data)
      this.tcView = false
    },
    tcDel(index) {
      this.tableData.splice(index, 1)
    },
    getYaoPin(v) {
      const data = this.baseInfo.yaoPin.find((item) => item.mingCheng === v)
      this.formData.yaoPinID = data.daiMa
    },
    getICD(v) {
      const data = this.baseInfo.zhenDuan.find((item) => item.mingCheng === v)
      this.ruleForm.jiBingDM = data.daiMa
      this.ruleForm.jiBingICD10 = data.daiMa
    },
    async dataHandle(reqData) {
      for (const key in this.addressMapList) {
        let resAddress = await getAddressListByAddress({ diZhi: reqData[key + 'XXDZ'] })
        let dataList = resAddress.data.diZhiBM.split('-')
        for (let i = 0; i < 4; i++) {
          reqData[key + this.addressMap[i]] = dataList[i]
        }
        for (let i = 1; i < 4; i++) {
          const k = this.addressMap[i - 1]
          this.addressMapList[key][this.addressMap[i]] = await this.getAddress(i, reqData[key + k])
        }
      }
      reqData['zhengjianLX'] = '1'
      this.tableData = reqData['ypxxQos']
    },
    sendData() {
      const data = { dialogVisible: false }
      this.$emit('childEvent', data) // 触发事件并传递数据
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;
    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }
  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }
  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }
  .info-value {
    background-color: #f7f9fd;
    .value {
      margin-left: 10px;
      .risk {
        margin-right: 15px;
      }
    }
    a {
      text-decoration: underline;
      color: #356ac5;
    }
    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }
    .cz-select {
      display: flex;
      align-items: center;
      ::v-deep .el-form-item {
        width: auto;
      }
      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }
      ::v-deep .el-input {
        margin-right: 5px;
      }
    }
    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }
      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }
      ::v-deep .el-select {
        width: 100%;
      }
      ::v-deep .el-input {
        width: 100%;
      }
    }
    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }
    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }
      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
