<template>
  <specialty-document-base
    :zhuan-ke-i-d="zhuanKeID"
    document-tab-label="专科相关文书（超声）"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import SpecialtyDocumentBase from '@/components/medicalRecord/SpecialtyDocumentBase.vue'

export default {
  name: 'UltrasoundDocuments',
  components: {
    SpecialtyDocumentBase
  },
  data() {
    return {
      zhuanKeID: '3549' // 超声科ID
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('专科相关文书（超声）保存成功')
    },
    handleDeleteSuccess() {
      console.log('专科相关文书（超声）删除成功')
    },
    handlePrintSuccess() {
      console.log('专科相关文书（超声）打印成功')
    }
  }
}
</script>
