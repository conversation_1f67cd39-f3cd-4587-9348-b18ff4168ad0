<template>
  <specialty-document-base
    :zhuan-ke-i-d="zhuanKeID"
    document-tab-label="专科相关文书（康复试用）"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import SpecialtyDocumentBase from '@/components/medicalRecord/SpecialtyDocumentBase.vue'

export default {
  name: 'RehabilitationDocuments',
  components: {
    SpecialtyDocumentBase
  },
  data() {
    return {
      zhuanKeID: '19' // 康复科ID
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('专科相关文书（康复试用）保存成功')
    },
    handleDeleteSuccess() {
      console.log('专科相关文书（康复试用）删除成功')
    },
    handlePrintSuccess() {
      console.log('专科相关文书（康复试用）打印成功')
    }
  }
}
</script>
