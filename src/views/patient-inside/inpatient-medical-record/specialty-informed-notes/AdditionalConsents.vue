<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="其他由医生签署的知情同意书"
    document-name-label="其他由医生签署的知情同意书名称"
    empty-selection-text="请选择其他由医生签署的知情同意书"
    no-content-text="请从左侧选择其他由医生签署的知情同意书"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'AdditionalConsents',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '28' // 其他由医生签署的知情同意书的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('其他由医生签署的知情同意书保存成功')
    },
    handleDeleteSuccess() {
      console.log('其他由医生签署的知情同意书删除成功')
    },
    handlePrintSuccess() {
      console.log('其他由医生签署的知情同意书打印成功')
    }
  }
}
</script>
