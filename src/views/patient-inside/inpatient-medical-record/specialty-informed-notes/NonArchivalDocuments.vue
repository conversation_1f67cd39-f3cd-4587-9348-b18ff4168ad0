<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="无需归档类文书"
    document-name-label="无需归档类文书名称"
    empty-selection-text="请选择无需归档类文书"
    no-content-text="请从左侧选择无需归档类文书"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'NonArchivalDocuments',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '91' // 无需归档类文书的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('无需归档类文书保存成功')
    },
    handleDeleteSuccess() {
      console.log('无需归档类文书删除成功')
    },
    handlePrintSuccess() {
      console.log('无需归档类文书打印成功')
    }
  }
}
</script>
