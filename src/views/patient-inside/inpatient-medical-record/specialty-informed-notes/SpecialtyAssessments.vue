<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="相关专科记录评估单"
    document-name-label="相关专科记录评估单名称"
    empty-selection-text="请选择相关专科记录评估单"
    no-content-text="请从左侧选择相关专科记录评估单"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'SpecialtyAssessments',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '2A' // 相关专科记录评估单的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('相关专科记录评估单保存成功')
    },
    handleDeleteSuccess() {
      console.log('相关专科记录评估单删除成功')
    },
    handlePrintSuccess() {
      console.log('相关专科记录评估单打印成功')
    }
  }
}
</script>
