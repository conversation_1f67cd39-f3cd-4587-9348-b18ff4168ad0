<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="本院其他纸质文书"
    document-name-label="本院其他纸质文书名称"
    empty-selection-text="请选择本院其他纸质文书"
    no-content-text="请从左侧选择本院其他纸质文书"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'HospitalPaperDocuments',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '55' // 本院其他纸质文书的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('本院其他纸质文书保存成功')
    },
    handleDeleteSuccess() {
      console.log('本院其他纸质文书删除成功')
    },
    handlePrintSuccess() {
      console.log('本院其他纸质文书打印成功')
    }
  }
}
</script>
