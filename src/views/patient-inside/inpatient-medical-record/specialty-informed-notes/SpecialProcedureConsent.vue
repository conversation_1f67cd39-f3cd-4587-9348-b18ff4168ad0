<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="特殊检查、特殊治疗同意书"
    document-name-label="特殊检查、特殊治疗同意书名称"
    empty-selection-text="请选择特殊检查、特殊治疗同意书"
    no-content-text="请从左侧选择特殊检查、特殊治疗同意书"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'SpecialProcedureConsent',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '26' // 特殊检查、特殊治疗同意书的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('特殊检查、特殊治疗同意书保存成功')
    },
    handleDeleteSuccess() {
      console.log('特殊检查、特殊治疗同意书删除成功')
    },
    handlePrintSuccess() {
      console.log('特殊检查、特殊治疗同意书打印成功')
    }
  }
}
</script>
