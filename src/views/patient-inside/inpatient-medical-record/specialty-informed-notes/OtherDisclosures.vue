<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="其他告知书"
    document-name-label="其他告知书名称"
    empty-selection-text="请选择其他告知书"
    no-content-text="请从左侧选择其他告知书"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'OtherDisclosures',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '2F' // 其他告知书的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('其他告知书保存成功')
    },
    handleDeleteSuccess() {
      console.log('其他告知书删除成功')
    },
    handlePrintSuccess() {
      console.log('其他告知书打印成功')
    }
  }
}
</script>
