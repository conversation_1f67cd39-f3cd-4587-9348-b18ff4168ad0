<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="其他评估、记录单及交接"
    document-name-label="其他评估、记录单及交接名称"
    empty-selection-text="请选择其他评估、记录单及交接"
    no-content-text="请从左侧选择其他评估、记录单及交接"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'AssessmentHandovers',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '44' // 其他评估、记录单及交接的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('其他评估、记录单及交接保存成功')
    },
    handleDeleteSuccess() {
      console.log('其他评估、记录单及交接删除成功')
    },
    handlePrintSuccess() {
      console.log('其他评估、记录单及交接打印成功')
    }
  }
}
</script>
