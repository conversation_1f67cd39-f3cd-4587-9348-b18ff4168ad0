<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="患者授权书"
    document-name-label="授权书名称"
    empty-selection-text="请选择患者授权书"
    no-content-text="请从左侧选择患者授权书"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'PatientAuthorization',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '24' // 患者授权书的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('患者授权书保存成功')
    },
    handleDeleteSuccess() {
      console.log('患者授权书删除成功')
    },
    handlePrintSuccess() {
      console.log('患者授权书打印成功')
    }
  }
}
</script>
