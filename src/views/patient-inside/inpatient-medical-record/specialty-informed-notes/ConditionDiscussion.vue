<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="病情谈话记录"
    document-name-label="病情谈话记录名称"
    empty-selection-text="请选择病情谈话记录"
    no-content-text="请从左侧选择病情谈话记录"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'ConditionDiscussion',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '27' // 病情谈话记录的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('病情谈话记录保存成功')
    },
    handleDeleteSuccess() {
      console.log('病情谈话记录删除成功')
    },
    handlePrintSuccess() {
      console.log('病情谈话记录打印成功')
    }
  }
}
</script>
