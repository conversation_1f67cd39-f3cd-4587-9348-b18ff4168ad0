<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="病情告知书"
    document-name-label="病情告知书名称"
    empty-selection-text="请选择病情告知书"
    no-content-text="请从左侧选择病情告知书"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'ConditionDisclosure',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '2E' // 病情告知书的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('病情告知书保存成功')
    },
    handleDeleteSuccess() {
      console.log('病情告知书删除成功')
    },
    handlePrintSuccess() {
      console.log('病情告知书打印成功')
    }
  }
}
</script>
