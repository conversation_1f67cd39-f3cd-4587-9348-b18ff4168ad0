<template>
  <medical-document-base
    :wen-shu-l-x="wenShuLX"
    document-tab-label="输血知情同意书"
    document-name-label="输血知情同意书名称"
    empty-selection-text="请选择输血知情同意书"
    no-content-text="请从左侧选择输血知情同意书"
    @save-success="handleSaveSuccess"
    @delete-success="handleDeleteSuccess"
    @print-success="handlePrintSuccess"
  />
</template>

<script>
import MedicalDocumentBase from '@/components/medicalRecord/MedicalDocumentBase.vue'

export default {
  name: 'BloodTransfusionConsent',
  components: {
    MedicalDocumentBase
  },
  data() {
    return {
      wenShuLX: '25' // 输血知情同意书的文书类型代码
    }
  },
  methods: {
    handleSaveSuccess() {
      console.log('输血知情同意书保存成功')
    },
    handleDeleteSuccess() {
      console.log('输血知情同意书删除成功')
    },
    handlePrintSuccess() {
      console.log('输血知情同意书打印成功')
    }
  }
}
</script>
