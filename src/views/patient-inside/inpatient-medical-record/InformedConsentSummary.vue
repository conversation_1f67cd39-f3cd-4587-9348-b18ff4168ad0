<template>
  <!-- 知情记录汇总 -->
  <div class="informed-consent-container">
    <!-- 左右布局 -->
    <div class="informed-consent-layout">
      <!-- 左侧面板 -->
      <div class="left-panel">
        <!-- 标签页区域 -->
        <div class="tabs-container">
          <el-tabs v-model="activeTab" type="border-card" @tab-click="handleTabClick">
            <!-- 已保存记录列表 -->
            <el-tab-pane label="已保存记录列表" name="saved">
              <div class="saved-records-list">
                <el-table
                  v-loading="savedRecordsLoading"
                  :data="savedRecordsList"
                  highlight-current-row
                  height="100%"
                  element-loading-text="加载中..."
                  border
                  stripe
                  size="mini"
                  @row-click="handleSavedRecordClick"
                >
                  <el-table-column prop="jilluSJ" label="记录时间" width="150">
                    <template #default="{ row }">
                      <span class="record-time">{{ row.jilluSJ }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column prop="wenShuMC" label="文书名称">
                    <template #default="{ row }">
                      <span
                        class="record-name"
                        :style="{ color: shouldShowRedText(row) ? 'red' : '' }"
                      >
                        {{ row.wenShuMC }}
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 新增记录(常用) -->
            <el-tab-pane label="新增记录(常用)" name="common">
              <div class="common-records-list">
                <el-table
                  v-loading="commonRecordsLoading"
                  :data="commonRecordsList"
                  highlight-current-row
                  height="100%"
                  element-loading-text="加载中..."
                  border
                  stripe
                  size="mini"
                  @row-click="handleCommonRecordClick"
                >
                  <el-table-column prop="wenShuMC" label="文书名称">
                    <template #default="{ row }">
                      <span class="record-name">
                        {{ row.wenShuMC }}
                      </span>
                      <span v-if="isWenShuWritten(row)" style="font-size: 16px">
                        (
                        <i class="el-icon-check"></i>
                        )
                      </span>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-tab-pane>

            <!-- 搜索 -->
            <el-tab-pane label="搜索" name="search">
              <div class="search-section">
                <div class="search-input-area">
                  <el-input
                    v-model="searchKeyword"
                    placeholder="请输入拼音码或者中文检索"
                    size="mini"
                    clearable
                    @keyup.enter.native="handleSearch"
                  ></el-input>
                  <el-button type="primary" class="purple-button" @click="handleSearch">
                    查询
                  </el-button>
                </div>
                <div class="search-results-list">
                  <el-table
                    v-loading="searchResultsLoading"
                    :data="searchResultsList"
                    highlight-current-row
                    height="100%"
                    element-loading-text="搜索中..."
                    border
                    stripe
                    size="mini"
                    @row-click="handleSearchResultClick"
                  >
                    <el-table-column prop="mingChen" label="文书名称">
                      <template #default="{ row }">
                        <span>【新增】</span>
                        <span class="record-name">{{ row.mingChen }}</span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>

      <!-- 右侧详情面板 -->
      <div class="right-panel">
        <div class="right-header">
          <div class="header-content">
            <div class="record-title">
              <span v-if="currentRecord">{{ currentRecord.wenShuMC }}</span>
              <span v-else>请选择知情记录</span>
            </div>
            <div v-if="currentRecord" class="action-buttons">
              <el-button type="primary" size="mini" @click="handleSave">保存</el-button>
              <el-button type="primary" size="mini" @click="handleDelete">删除</el-button>
              <el-button type="primary" size="mini" @click="handlePrint">打印</el-button>
            </div>
          </div>
        </div>
        <div class="iframe-container">
          <iframe
            v-if="iframeUrl"
            ref="contentIframe"
            :src="iframeUrl"
            frameborder="0"
            @load="handleIframeLoad"
          ></iframe>
          <div v-else class="no-content">
            <i class="el-icon-document"></i>
            <p>请从左侧选择知情记录</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getAllYiShuXieWenShuByBLID,
  getZqjlListInit,
  getZqjlListForSearch
} from '@/api/informed-consent'
import iframeCommunication from '@/utils/iframe-communication'

export default {
  name: 'InformedConsentSummary',
  data() {
    return {
      // 当前激活的标签页
      activeTab: 'saved',
      // 已保存记录列表
      savedRecordsList: [],
      savedRecordsLoading: false,
      // 常用记录列表
      commonRecordsList: [],
      commonRecordsLoading: false,
      // 搜索相关
      searchKeyword: '',
      searchResultsList: [],
      searchResultsLoading: false,
      // 当前选中的记录
      currentRecord: null,
      // iframe URL
      iframeUrl: '',
      // 基础URL
      baseUrl: 'http://************/ehr',
      // 知情记录文书类型列表（用于获取已保存记录）
      wenShuLXList: [
        '24',
        '25',
        '26',
        '27',
        '28',
        '29',
        '55',
        '2A',
        '2B',
        '2C',
        '2D',
        '2E',
        '2F',
        '30'
      ].toString()
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientInfo: ({ patient }) => patient.patientInit,
      gongZhongDM: ({ patient }) => patient.doctorInfo.gongZhongDM_DZ, // 工种代码对照
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  mounted() {
    this.initData()
    // 初始化iframe通信
    iframeCommunication.init()
  },

  beforeDestroy() {
    // 销毁iframe通信
    iframeCommunication.destroy()
  },
  methods: {
    // 初始化数据
    async initData() {
      await this.fetchSavedRecords()
      await this.fetchCommonRecords()
    },

    // 获取已保存记录列表
    async fetchSavedRecords() {
      this.savedRecordsLoading = true
      try {
        const res = await getAllYiShuXieWenShuByBLID({
          bingLiID: this.bingLiID,
          wenShuLXList: this.wenShuLXList
        })

        if (res.hasError === 0) {
          this.savedRecordsList = res.data.sort((a, b) => {
            // 按记录时间排序
            return new Date(a.jilluSJ || 0) - new Date(b.jilluSJ || 0)
          })
        } else {
          this.$message.error(res.errorMessage || '获取已保存记录失败')
        }
      } catch (error) {
        console.error('获取已保存记录失败', error)
        this.$message.error('获取已保存记录失败')
      } finally {
        this.savedRecordsLoading = false
      }
    },

    // 获取常用记录列表
    async fetchCommonRecords() {
      this.commonRecordsLoading = true
      try {
        const res = await getZqjlListInit({
          bingLiID: this.bingLiID,
          zhuanKeID: this.zhuanKeID
        })

        if (res.hasError === 0) {
          this.commonRecordsList = res.data || []
        } else {
          this.$message.error(res.errorMessage || '获取常用记录失败')
        }
      } catch (error) {
        console.error('获取常用记录失败', error)
        this.$message.error('获取常用记录失败')
      } finally {
        this.commonRecordsLoading = false
      }
    },

    // 判断文书是否已经书写
    isWenShuWritten(row) {
      return this.savedRecordsList.some((item) => item.geshiDM === row.geShiDM)
    },

    // 判断是否应该显示红色文字
    shouldShowRedText(row) {
      // 原始JS逻辑：SPBZ=='0'未找到对应字段
      return row.guidanBZ && row.guidanBZ === '0'
    },

    // 标签页切换
    handleTabClick(tab) {
      this.activeTab = tab.name
      if (tab.name === 'search') {
        this.searchResultsList = []
        this.searchKeyword = ''
      }
    },

    // 已保存记录点击
    handleSavedRecordClick(record) {
      this.currentRecord = record
      this.setIframeUrl(record)
    },

    // 常用记录点击
    handleCommonRecordClick(record) {
      this.currentRecord = record
      this.setIframeUrl(record)
    },

    // 搜索结果点击
    handleSearchResultClick(record) {
      this.currentRecord = record
      this.setIframeUrl(record)
    },

    // 搜索处理
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.$message.warning('请输入拼音码或者中文检索')
        return
      }

      this.searchResultsLoading = true
      try {
        const res = await getZqjlListForSearch({
          key: this.searchKeyword,
          wenShuLX: '00'
        })

        if (res.hasError === 0) {
          this.searchResultsList = res.data || []
          if (this.searchResultsList.length === 0) {
            this.$message.info('未找到相关记录')
          }
        } else {
          this.$message.error(res.errorMessage || '搜索失败')
        }
      } catch (error) {
        console.error('搜索失败', error)
        this.$message.error('搜索失败')
      } finally {
        this.searchResultsLoading = false
      }
    },

    // 设置iframe URL
    setIframeUrl(record) {
      if (!record) {
        this.iframeUrl = ''
        return
      }

      // 根据guidanBZ字段判断URL生成逻辑
      let params = {}
      let pageName = 'blwsdetail.aspx' // 默认页面

      if (record.guidanBZ === '1') {
        params = {
          as_wsid: record.id || 0,
          as_blid: record.bingLiID || this.bingLiID
        }
        pageName = 'zyblwsPdf.aspx'
      } else {
        params = {
          as_blid: this.bingLiID,
          as_gsdm: record.geshiDM || record.geShiDM,
          as_zyid: this.patientInfo.zhuYuanID,
          as_yhid: this.yongHuID,
          as_wsid: record.id || 0,
          as_wslx: record.wenshuLX || record.wenShuLX,
          // as_tmpid: 't1',
          tmpid: Math.random()
        }
        pageName = 'blwsdetail.aspx'
      }

      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .filter(([, value]) => value !== null && value !== undefined && value !== '')
        .map(([key, value]) => `${key}=${value}`)
        .join('&')

      // 返回完整URL
      this.iframeUrl = `${this.baseUrl}/zyblws/${pageName}?${queryString}`
    },

    // iframe加载完成
    handleIframeLoad() {
      console.log('知情记录iframe加载完成')
    },

    // 保存记录
    async handleSave() {
      if (!this.currentRecord) {
        this.$message.error('没有可保存的记录')
        return
      }

      // 检查病历状态，如果已封存则不允许保存
      if (this.patientInfo.bingLiZT === '1') {
        this.$message({
          type: 'warning',
          message: '已封存病历不能进行该操作!'
        })
        return
      }

      // 获取iframe元素
      const iframe = this.$refs.contentIframe
      if (!iframe) {
        this.$message.error('iframe未加载完成')
        return
      }

      // 检查iframe URL是否包含blwsdetail（PDF页面不支持保存）
      if (iframe.src.indexOf('blwsdetail') < 0) {
        this.$message.warning('无保存功能')
        return
      }

      try {
        // 使用简化的iframe通信
        const message = await iframeCommunication.save(this.currentRecord.id || 'default', iframe)
        this.$message.success(message)

        // 保存成功后刷新已保存记录列表
        setTimeout(async () => {
          await this.fetchSavedRecords()
        }, 1000)
      } catch (error) {
        console.error('保存失败', error)
        this.$message.error('保存出错: ' + (error.message || '未知错误'))
      }
    },

    // 删除记录
    async handleDelete() {
      if (!this.currentRecord) {
        this.$message.error('没有可删除的记录')
        return
      }

      // 检查病历状态，如果已封存则不允许删除
      if (this.patientInfo.bingLiZT === '1') {
        this.$message({
          type: 'warning',
          message: '已封存病历不能进行该操作!'
        })
        return
      }

      // 获取iframe元素
      const iframe = this.$refs.contentIframe
      if (!iframe) {
        this.$message.error('iframe未加载完成')
        return
      }

      // 检查iframe URL是否包含blwsdetail（PDF页面不支持删除）
      if (iframe.src.indexOf('blwsdetail') < 0) {
        this.$message.warning('无删除功能')
        return
      }

      try {
        // 确认删除操作
        await this.$confirm('确定要删除这条知情记录吗？', '删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 使用简化的iframe通信
        const message = await iframeCommunication.delete(this.currentRecord.id || 'default', iframe)
        this.$message.success(message)

        // 删除成功后刷新已保存记录列表
        setTimeout(async () => {
          await this.fetchSavedRecords()
        }, 1000)
        // 清空当前选中记录
        this.currentRecord = null
        this.iframeUrl = ''
      } catch (error) {
        if (error === 'cancel') {
          // 用户取消删除，不做任何操作
          return
        }
        console.error('删除失败', error)
        this.$message.error('删除出错: ' + (error.message || '未知错误'))
      }
    },

    // 打印记录
    async handlePrint() {
      if (!this.currentRecord) {
        this.$message.error('没有可打印的记录')
        return
      }

      // 获取iframe元素
      const iframe = this.$refs.contentIframe
      if (!iframe) {
        this.$message.error('iframe未加载完成')
        return
      }

      try {
        // 检查iframe URL类型来决定打印方式
        if (iframe.src.indexOf('blwsdetail') >= 0) {
          // blwsdetail页面：使用简化的iframe通信
          const message = await iframeCommunication.print(
            this.currentRecord.id || 'default',
            iframe
          )
          this.$message.success(message)
        } else {
          // PDF页面或其他页面：使用浏览器默认打印
          iframe.contentWindow.print()
          this.$message.success('打印完成')
        }
      } catch (error) {
        console.error('打印失败', error)
        this.$message.error('打印出错: ' + (error.message || '未知错误'))
      }
    }
  }
}
</script>

<style scoped lang="scss">
.informed-consent-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;

  .informed-consent-layout {
    display: flex;
    height: 100%;
    gap: 8px;
    padding: 8px;

    .left-panel {
      width: 380px;
      background: white;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;

      .tabs-container {
        flex: 1;
        overflow: hidden;

        ::v-deep .el-tabs {
          height: 100%;
          display: flex;
          flex-direction: column;

          .el-tabs__header {
            margin: 0;
            border-bottom: 1px solid #e4e7ed;
          }

          .el-tabs__content {
            flex: 1;
            overflow: hidden;
            padding: 10px 0 0;

            .el-tab-pane {
              height: 100%;
            }
          }
        }

        .saved-records-list,
        .common-records-list {
          height: 100%;
          overflow: hidden;

          ::v-deep .el-table {
            border: none;

            .record-name {
              cursor: pointer;

              &:hover {
                text-decoration: underline;
              }
            }

            .record-time {
              font-size: 14px;
            }
          }
        }

        .search-section {
          height: 100%;
          display: flex;
          flex-direction: column;
          padding: 0px 10px 10px;

          .search-input-area {
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;

            .purple-button {
              background: var(--color-purple);
              border: 1px solid var(--color-purple);

              &:hover,
              &:focus {
                background: #ce8be0;
                border-color: #ce8be0;
              }
            }
          }

          .search-results-list {
            flex: 1;
            overflow: hidden;
          }
        }
      }
    }

    .right-panel {
      flex: 1;
      background: white;
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      display: flex;
      flex-direction: column;

      .right-header {
        height: 40px;
        padding: 0 16px;
        border-bottom: 1px solid #e4e7ed;
        background: #fafbfc;

        .header-content {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .record-title {
            font-size: 14px;
            font-weight: 600;
            color: #303133;
            display: flex;
            align-items: center;

            &::before {
              content: '';
              width: 3px;
              height: 16px;
              background: #356ac5;
              margin-right: 6px;
            }
          }

          .action-buttons {
            display: flex;

            .el-button {
              padding: 5px 12px;
            }
          }
        }
      }

      .iframe-container {
        flex: 1;
        position: relative;
        overflow: hidden;

        iframe {
          width: 100%;
          height: 100%;
          border: none;
        }

        .no-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          height: 100%;
          color: #909399;

          i {
            font-size: 48px;
            margin-bottom: 16px;
            color: #c0c4cc;
          }

          p {
            font-size: 14px;
            margin: 0;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .informed-consent-container {
    .informed-consent-layout {
      .left-panel {
        width: 350px;
      }
    }
  }
}

@media (max-width: 768px) {
  .informed-consent-container {
    .informed-consent-layout {
      flex-direction: column;

      .left-panel {
        width: 100%;
        height: 300px;
      }

      .right-panel {
        flex: 1;
      }
    }
  }
}
</style>
