<template>
  <!-- 量表评分表 -->
  <div class="rating-scale-container">
    <div class="rating-scale-header">
      <div class="title">{{ bingRenXM }}评分表记录</div>
      <div class="button-group">
        <el-button type="primary" @click="handleAdd">新增</el-button>
        <el-button type="primary" @click="handleViewNewRating">查看/新增 新版评分表</el-button>
      </div>
    </div>

    <div class="rating-scale-content">
      <el-table :data="ratingScaleList" border stripe size="mini" style="width: 860px">
        <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
        <el-table-column
          prop="xiuGaiSJ"
          label="最后评分修改时间"
          width="150"
          align="center"
        ></el-table-column>
        <el-table-column prop="yiSheng" label="医生" width="70" align="center"></el-table-column>
        <el-table-column
          prop="mingCheng"
          label="评分表名称"
          min-width="400"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="pingFenZFZ"
          label="总分值"
          width="70"
          align="center"
        ></el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template #default="{ row }">
            <el-button type="text" size="small" @click="handleView(row)">查看</el-button>
            <el-divider direction="vertical" />
            <el-button type="text" size="small" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 新增评分表弹窗 -->
    <default-dialog
      :visible.sync="addDialog.visible"
      title="量表评分表"
      width="450px"
      confirm-button-text="确认"
      cancel-button-text="关闭"
      @confirm="handleAddConfirm"
      @cancel="handleAddCancel"
    >
      <div class="add-scale-dialog">
        <!-- 搜索框 -->
        <div class="search-section">
          <el-input
            ref="searchInput"
            v-model="addDialog.searchKeyword"
            placeholder="请输入拼音码或者中文检索"
            size="mini"
            clearable
            @input="filterScaleList"
            @clear="filterScaleList"
          ></el-input>
        </div>
        <div class="table-section">
          <el-table
            :data="getCurrentPageData()"
            border
            stripe
            size="mini"
            min-height="300px"
            highlight-current-row
            :row-class-name="({ row }) => (addDialog.selectedRow === row ? 'selected-row' : '')"
            @row-click="handleAddRowClick"
          >
            <el-table-column prop="pingFenBiao" label="中文名"></el-table-column>
          </el-table>
        </div>
        <el-pagination
          :current-page="addDialog.currentPage"
          :page-size="addDialog.pageSize"
          :total="addDialog.total"
          layout="total, prev, pager, next"
          background
          @current-change="handlePageChange"
          @size-change="handlePageSizeChange"
        ></el-pagination>
      </div>
    </default-dialog>

    <!-- 评分量表详情弹窗 -->
    <default-dialog
      :visible.sync="detailDialog.visible"
      title="评分量表详情"
      width="50%"
      pop-type="custom"
      @cancel="handleDetailCancel"
    >
      <iframe
        v-if="detailDialog.visible"
        :src="detailDialog.iframeUrl"
        width="100%"
        height="700px"
        frameborder="0"
        style="border: none"
      ></iframe>
    </default-dialog>
  </div>
</template>

<script>
import { getInPatientPFB, getPingFenBiaoIDMC, deletePingFenBiao } from '@/api/rating-scale'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { mapState } from 'vuex'

export default {
  name: 'RatingScale',
  components: {
    DefaultDialog
  },
  data() {
    return {
      ratingScaleList: [],
      // 新增弹窗相关数据
      addDialog: {
        visible: false,
        searchKeyword: '',
        scaleList: [],
        filteredScaleList: [],
        selectedRow: null,
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      // 详情弹窗相关数据
      detailDialog: {
        visible: false,
        iframeUrl: ''
      }
    }
  },
  computed: {
    ...mapState({
      bingRenXM: ({ patient }) => patient.patientInit.bingRenXM,
      bingRenBH: ({ patient }) => patient.patientInit.bingRenBH,
      bingLiID: ({ patient }) => patient.patientInit.bingLiID,
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  watch: {
    // 监听弹窗显示状态，自动聚焦搜索框
    'addDialog.visible'(newVal) {
      if (newVal) {
        // 弹窗打开时，等待DOM更新后聚焦搜索框
        this.$nextTick(() => {
          if (this.$refs.searchInput) {
            this.$refs.searchInput.focus()
          }
        })
      }
    }
  },
  async mounted() {
    await this.getInPatientPFB()
  },
  methods: {
    // 获取住院病人的评分表数据
    async getInPatientPFB() {
      try {
        const res = await getInPatientPFB({
          bingLiID: this.$route.params.id
        })

        if (res.hasError === 0 && res.data) {
          this.ratingScaleList = res.data || []
        }
      } catch (error) {
        console.error('获取评分表数据失败:', error)
        this.$message.error('获取评分表数据失败')
        this.ratingScaleList = []
      }
    },

    // 新增评分表
    async handleAdd() {
      try {
        const res = await getPingFenBiaoIDMC({
          zhuanKeID: this.zhuanKeID
        })

        if (res.hasError === 0 && res.data) {
          this.addDialog.scaleList = res.data || []
          this.filterScaleList()
          this.addDialog.visible = true
        }
      } catch (error) {
        this.$message.error('获取评分表列表失败')
        this.addDialog.scaleList = []
      }
    },

    // 查看/新增 新版评分表
    handleViewNewRating() {
      this.$message.info('查看/新增 新版评分表功能待开发')
    },

    // 查看评分表详情
    handleView(row) {
      // 构建iframe URL，使用row.pingFenZHID
      const baseUrl = 'http://10.41.220.39/ehr/pfbgl/pfbym.aspx'
      const params = new URLSearchParams({
        as_brbh: this.bingRenBH || '',
        as_blid: this.bingLiID || this.$route.params.id,
        as_pfid: row.pingFenID || '', // 返回的是pingFenID
        as_pfzhid: row.pingFenZHID || ''
      })

      this.detailDialog.iframeUrl = `${baseUrl}?${params.toString()}`
      this.detailDialog.visible = true
    },

    // 删除评分表
    handleDelete(row) {
      this.$confirm(`确认删除"${row.mingCheng}"吗？`, '删除提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          try {
            const res = await deletePingFenBiao({
              pingFenZHID: row.pingFenZHID
            })

            if (res.hasError === 0) {
              this.$message.success('删除成功')
              await this.getInPatientPFB()
            } else {
              this.$message.error(res.errorMessage || '删除失败')
            }
          } catch (error) {
            console.error('删除评分表失败:', error)
            this.$message.error('删除失败，请稍后重试')
          }
        })
        .catch(() => {
          this.$message.info('已取消删除')
        })
    },

    // 搜索过滤
    filterScaleList() {
      const keyword = this.addDialog.searchKeyword.trim().toLowerCase()
      if (!keyword) {
        this.addDialog.filteredScaleList = [...this.addDialog.scaleList]
      } else {
        this.addDialog.filteredScaleList = this.addDialog.scaleList.filter((item) => {
          // 支持中文名称搜索
          const chineseMatch = item.pingFenBiao && item.pingFenBiao.toLowerCase().includes(keyword)

          // 支持拼音码搜索
          const pinyinMatch =
            item.pingFenBiaoPY && item.pingFenBiaoPY.toLowerCase().includes(keyword)

          return chineseMatch || pinyinMatch
        })
      }
      this.addDialog.total = this.addDialog.filteredScaleList.length
      this.addDialog.currentPage = 1 // 搜索后重置到第一页
    },

    // 新增弹窗表格行点击
    handleAddRowClick(row) {
      this.addDialog.selectedRow = row
    },

    // 关闭详情弹窗
    handleDetailCancel() {
      this.detailDialog.visible = false
      this.detailDialog.iframeUrl = ''
    },

    // 分页处理
    handlePageChange(page) {
      this.addDialog.currentPage = page
    },

    handlePageSizeChange(size) {
      this.addDialog.pageSize = size
      this.addDialog.currentPage = 1
    },

    // 获取当前页数据
    getCurrentPageData() {
      const start = (this.addDialog.currentPage - 1) * this.addDialog.pageSize
      const end = start + this.addDialog.pageSize
      return this.addDialog.filteredScaleList.slice(start, end)
    },

    // 确认新增
    handleAddConfirm() {
      if (!this.addDialog.selectedRow) {
        this.$message.warning('请选择一个评分表')
        return
      }

      // 构建iframe URL，as_pfzhid使用固定值0
      const baseUrl = 'http://10.41.220.39/ehr/pfbgl/pfbym.aspx'
      const params = new URLSearchParams({
        as_brbh: this.bingRenBH || '',
        as_blid: this.bingLiID || this.$route.params.id,
        as_pfid: this.addDialog.selectedRow.pingFenBiaoID || '',
        as_pfzhid: '0'
      })

      this.detailDialog.iframeUrl = `${baseUrl}?${params.toString()}`
      this.detailDialog.visible = true

      // 关闭新增弹窗
      this.handleAddCancel()
    },

    // 关闭新增
    handleAddCancel() {
      this.addDialog.visible = false
      this.addDialog.selectedRow = null
      this.addDialog.searchKeyword = ''
      this.addDialog.currentPage = 1
    }
  }
}
</script>

<style scoped lang="scss">
.rating-scale-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f6f6f6;
  padding: 8px;
}

.rating-scale-header {
  width: 860px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0;
  padding: 12px 0;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;

  .title {
    font-weight: bold;
    display: inline-block;
    font-size: 14px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    vertical-align: middle;
  }

  .button-group {
    display: flex;

    .el-button {
      font-size: 12px;
      padding: 6px 12px;
      border-radius: 4px;
    }
  }
}

.rating-scale-content {
  flex: 1;
  ::v-deep .el-table--mini {
    td.el-table__cell {
      padding: 3px 0;
    }
  }
}

// 弹窗样式
.add-scale-dialog {
  .search-section {
    margin-bottom: 8px;
  }

  .table-section {
    ::v-deep .el-table {
      .el-table__row {
        cursor: pointer;
        height: 40px;
      }

      .selected-row {
        background-color: #6787cc !important;
        color: white !important;

        &:hover > td {
          background-color: #6787cc !important;
        }

        td {
          background-color: #6787cc !important;
          color: white !important;
        }
      }
    }
  }
}
</style>
