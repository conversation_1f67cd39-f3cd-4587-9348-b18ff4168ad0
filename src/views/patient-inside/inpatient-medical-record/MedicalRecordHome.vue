<template>
  <div class="surgical-notice-view">
    <div class="right">
      <div class="fixed-header">
        <!-- 头部 -->
        <div class="right-header">
          <div class="title">病案首页</div>
          <div class="button-group">
            <el-button v-if="!submitShow" type="primary" @click="handleSaveRecord(0)">
              保存
            </el-button>
            <el-button v-if="!submitShow" type="primary" @click="getShouYeCDSSNR(1)">
              提交
            </el-button>
            <el-button v-if="submitShow" type="primary" @click="GetPrintContent">打印</el-button>
            <el-button v-if="submitShow" type="primary" @click="WithdrawSubmit">撤销提交</el-button>
            <el-button type="primary" @click="getCommonSurgeriesByZhuanKe()">常用手术</el-button>
            <el-button type="primary" @click="getCommonDiagnosesByZhuanKe()">常用诊断</el-button>
          </div>
        </div>
        <!-- 标题 -->
        <div class="title-group">
          <h2 class="title-describe">温州医科大学附属第一医院</h2>
          <div>
            <span>医疗付费情况：</span>
            <span style="display: inline-block; width: 200px; margin: 0 5px">
              <el-select
                filterable
                v-model="patientRecord.bingRenJBXX.yiLiaoFFFS"
                :disabled="sumbitDisabled"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in medicalRecord.yiLiaoZFFSDM"
                  :key="item.daiMa"
                  :label="item.mingCheng"
                  :value="item.daiMa"
                ></el-option>
              </el-select>
            </span>
            <span>病案号：</span>
            <span class="" style="display: inline-block; width: 120px; margin: 0 5px">
              <el-input
                v-model="patientRecord.bingRenJBXX.empi"
                :disabled="defaultDisabled"
                class="select-input"
              ></el-input>
            </span>
            <span>第</span>
            <span class="" style="display: inline-block; width: 60px; margin: 0 5px">
              <el-input
                v-model="patientRecord.bingRenJBXX.zhuYuanCS"
                :disabled="defaultDisabled"
                class="select-input"
              ></el-input>
            </span>
            <span>次</span>
          </div>
        </div>
      </div>
      <!-- 主体内容 -->
      <div class="right-content scrollable-content">
        <div class="el-main">
          <!-- 主体内容头部 -->
          <div class="right-header" style="margin: 11px 0">
            <div class="title">病人基本信息</div>
          </div>
          <table class="right-table">
            <tr>
              <td class="right-table-title">姓名:</td>
              <td class="right-table-content">{{ patientRecord.bingRenJBXX.bingRenXM }}</td>
              <td class="right-table-title">性别:</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenJBXX.bingRenXB == 1 ? '男' : '女' }}
              </td>
              <td class="right-table-title">出生日期:</td>
              <td class="right-table-content">
                <el-date-picker
                  v-model="patientRecord.bingRenJBXX.chuShengRQ"
                  :disabled="sumbitDisabled"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                ></el-date-picker>
              </td>
              <td class="right-table-title">年龄：</td>
              <td class="right-table-content">
                {{
                  calculateAge(
                    patientRecord.bingRenJBXX.ruYuanSJ,
                    patientRecord.bingRenJBXX.chuShengRQ
                  )
                }}岁
              </td>
              <td class="right-table-title">国籍:</td>
              <td class="right-table-content">
                <el-select
                  filterable
                  v-model="patientRecord.bingRenJBXX.guoJi"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in medicalRecord.guoJiDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr v-if="keshi1 == '妇科' || keshi2 == '儿科'">
              <!-- <td class="right-table-title" colspan="2">年龄不足一周岁</td> -->
              <td class="right-table-title">年龄:</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenJBXX.xinShengErYL }}月-{{
                  patientRecord.bingRenJBXX.xinShengErTS
                }}/30天
              </td>
              <td class="right-table-title">新生儿出生体重(克):</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.xinShengErCSTZ"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">新生儿入院体重(克):</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.xinShengErRYTZ"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">父母姓名:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.fuMuXM"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">长期居住地：</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.zhangQiJZD"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">出生地:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.chuShengDe"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">籍贯:</td>
              <td class="right-table-content">
                <el-select
                  filterable
                  v-model="patientRecord.bingRenJBXX.jiGuanDM"
                  :disabled="sumbitDisabled"
                  filterable
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in getJiGuanDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">民族:</td>
              <td class="right-table-content">
                <el-select
                  filterable
                  v-model="patientRecord.bingRenJBXX.minZuDM"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in medicalRecord.mingZuDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">职业:</td>
              <td class="right-table-content">
                <el-select
                  filterable
                  v-model="patientRecord.bingRenJBXX.zhiYeDM"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in medicalRecord.zhiYeDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">婚姻:</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenJBXX.hunYinQK"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in medicalRecord.hunYinDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td class="right-table-title zjlx" colspan="2">
                <el-select
                  v-model="patientRecord.bingRenJBXX.zhengJianLX"
                  :disabled="sumbitDisabled"
                  @change="handleOptionlick"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in zhengJianLXDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-content" colspan="2">
                <el-input
                  v-model="patientRecord.bingRenJBXX.shenFenZH"
                  :disabled="sfzDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">电话:</td>
              <td class="right-table-content" colspan="5">
                <el-input
                  v-model="patientRecord.bingRenJBXX.xianZhuZhiDH"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">现住地:</td>
              <td class="right-table-content" colspan="7">
                <el-input
                  v-model="patientRecord.bingRenJBXX.xianJuZhuDZ"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">邮编:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.xianZhuZhiYB"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">户口地址:</td>
              <td class="right-table-content" colspan="7">
                <el-input
                  v-model="patientRecord.bingRenJBXX.huKouDZ"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">邮编:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.huKouYB"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">工作单位及地址:</td>
              <td class="right-table-content" colspan="5">
                <el-input
                  v-model="patientRecord.bingRenJBXX.gongZuoDWJDZ"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">单位电话:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.danWeiDH"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">邮编:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.danWeiYB"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">联系人姓名:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.lianXiRenXM"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">关系:</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenJBXX.lianXiRenGX"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in medicalRecord.lianXiRenGXDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">地址:</td>
              <td class="right-table-content" colspan="3">
                <el-input
                  v-model="patientRecord.bingRenJBXX.lianXiRenDZ"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">电话:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.lianXiRenDH"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr v-if="keshi1 == '妇科'">
              <td class="right-table-title">丈夫姓名:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.zhangFuXM"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">丈夫身份证:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQKSZ.zhangFuSFZH"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr v-if="keshi1 == '妇科'">
              <td class="right-table-title">产前检查:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenJBXX.chanQianJC"
                  :disabled="sumbitDisabled"
                  label="0"
                >
                  无
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenJBXX.chanQianJC"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  有
                </el-radio>
              </td>
              <td class="right-table-title">末次月经:</td>
              <td class="right-table-content">
                <el-date-picker
                  v-model="patientRecord.bingRenJBXX.moCiYJ"
                  :disabled="sumbitDisabled"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                ></el-date-picker>
              </td>
              <td class="right-table-title">预产期:</td>
              <td class="right-table-content">
                <el-date-picker
                  v-model="patientRecord.bingRenJBXX.yuChanQiSJ"
                  :disabled="sumbitDisabled"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                ></el-date-picker>
              </td>
              <td class="right-table-title">孕次:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.huaiYunCS"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">产次:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenJBXX.chanCi"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">病历陈述者:</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenJBXX.bingLiCSZ"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in bingLiCSZDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">入院途径:</td>
              <td class="right-table-content" colspan="7">
                <el-radio
                  v-for="(item, index) in ruYuanTJDM"
                  :key="index"
                  v-model="patientRecord.bingRenJBXX.ruYuanTJ"
                  :disabled="sumbitDisabled"
                  :label="item.daiMa"
                >
                  {{ item.mingCheng }}
                </el-radio>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">入院时间:</td>
              <td class="right-table-content">{{ patientRecord.bingRenJBXX.ruYuanSJ }}</td>
              <td class="right-table-title">入院科别:</td>
              <td class="right-table-content">{{ patientRecord.bingRenJBXX.ruYuanZKMC }}</td>
              <td class="right-table-title">病房:</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenJBXX.ruYuanBQMC }}-{{ patientRecord.bingRenJBXX.ruYuanCWH }}
              </td>
              <td class="right-table-title">转科级别</td>
              <td class="right-table-content">{{ patientRecord.bingRenJBXX.zhuanKeKB }}</td>
            </tr>
            <tr>
              <td class="right-table-title">出院时间:</td>
              <td class="right-table-content">{{ patientRecord.bingRenJBXX.chuYuanSJ }}</td>
              <td class="right-table-title">出院科别:</td>
              <td class="right-table-content">{{ patientRecord.bingRenJBXX.chuYuanZKMC }}</td>
              <td class="right-table-title">病房:</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenJBXX.chuYuanBQMC }}-{{
                  patientRecord.bingRenJBXX.chuYuanCWH
                }}
              </td>
              <td class="right-table-title">实际住院：</td>
              <td class="right-table-content">{{ patientRecord.bingRenJBXX.zhuYuanTS }}天</td>
            </tr>
          </table>
          <div class="right-header" style="margin: 11px 0">
            <div class="title">病人医疗情况</div>
          </div>
          <table class="right-table">
            <tr>
              <td class="right-table-title">门(急)诊诊断:</td>
              <td class="right-table-content" colspan="5">
                <el-input
                  v-model="patientRecord.bingRenYLQK.menZhenZD"
                  :disabled="defaultDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">疾病编码:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.menZhenZDDM"
                  :readonly="!sumbitDisabled"
                  :disabled="sumbitDisabled"
                  style="cursor: pointer"
                  class="select-input"
                  placeholder="请选择疾病编码"
                  @click.native="handleInputClick('jb')"
                ></el-input>
              </td>
              <td class="right-table-title">确诊日期:</td>
              <td class="right-table-content">
                <el-date-picker
                  v-model="patientRecord.bingRenYLQK.queZhenRQ"
                  :disabled="sumbitDisabled"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                ></el-date-picker>
              </td>
            </tr>
            <tr>
              <td colspan="10" class="cryuan-details" style="padding: 12px 6px">
                <span class="cryuan-left" style="padding: 12px; font-weight: bold">
                  出入院诊断详情
                  <i class="el-icon-question"></i>
                </span>
                <el-button v-if="!sumbitDisabled" type="primary" @click="getWenShuZDByBingLiID">
                  同步诊断
                </el-button>
                <span
                  v-if="!sumbitDisabled"
                  class="cryuan-right"
                  style="position: absolute; right: 6px; margin-top: 0"
                >
                  <el-button type="primary" plain @click="handleAdd(bingRenZDQKXY, 'cryzd')">
                    添加
                  </el-button>
                  <el-button type="primary" plain @click="handleDelete(bingRenZDQKXY, 'cryzd')">
                    删除
                  </el-button>
                </span>
              </td>
            </tr>
            <tr>
              <td colspan="10">
                <el-table :data="bingRenZDQKXY" border style="width: 100%">
                  <!-- :span-method="objectSpanMethod" -->
                  <el-table-column label="选择" width="50" align="center">
                    <template #default="{ $index }">
                      <el-checkbox
                        :disabled="sumbitDisabled"
                        :value="selectedIds1 === $index"
                        @change="() => handleRadioClick('cryzd', $index)"
                      ></el-checkbox>
                    </template>
                  </el-table-column>
                  <el-table-column label="诊断类别" width="120">
                    <template #default="{ row }">
                      <!-- <span v-if="row.shunXuHao == 1 && row.shiFouZYZD == 1">主要诊断</span> -->
                      <span v-if="row.shunXuHao == 1">主要诊断</span>
                      <span v-else>其它诊断</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="诊断名称">
                    <template #default="{ row, $index }">
                      <el-input
                        v-model="row.zhenDuanMC"
                        :readonly="!sumbitDisabled"
                        :disabled="sumbitDisabled"
                        style="cursor: pointer"
                        class="select-input"
                        placeholder="请选择诊断名称"
                        @click.native="handleInputClick('zdmc_td', $index)"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="疾病编码" width="170">
                    <template #default="{ row }">
                      <span>{{ row.icd }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="入院病情">
                    <template #default="{ row }">
                      <el-radio v-model="row.ruYuanBQ" :disabled="sumbitDisabled" label="1">
                        有
                      </el-radio>
                      <el-radio v-model="row.ruYuanBQ" :disabled="sumbitDisabled" label="2">
                        临床未确认
                      </el-radio>
                      <el-radio v-model="row.ruYuanBQ" :disabled="sumbitDisabled" label="3">
                        情况不明
                      </el-radio>
                      <el-radio v-model="row.ruYuanBQ" :disabled="sumbitDisabled" label="4">
                        无
                      </el-radio>
                    </template>
                  </el-table-column>
                  <el-table-column label="" width="200" align="center">
                    <template #default="{ $index }">
                      <div class="move-buttons">
                        <el-button
                          :disabled="sumbitDisabled == true ? true : $index === 0"
                          @click="handleMoveUp(bingRenZDQKXY, $index)"
                        >
                          <i class="el-icon-top"></i>
                        </el-button>
                        <el-button
                          :disabled="
                            sumbitDisabled == true ? true : $index === bingRenZDQKXY.length - 1
                          "
                          @click="handleMoveDown(bingRenZDQKXY, $index)"
                        >
                          <i class="el-icon-bottom"></i>
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            <tr v-if="keshi3 == '中医'">
              <td colspan="10" class="cryuan-details" style="padding: 12px 6px">
                <span class="cryuan-left" style="padding: 12px; font-weight: bold">
                  中医诊断详情
                  <i class="el-icon-question"></i>
                </span>
                <el-button v-if="!sumbitDisabled" type="primary" @click="getQuanBuWSZDByBingLiID">
                  同步诊断
                </el-button>
                <el-button
                  v-if="patientRecord.bingRenYLQKZJ.yiYuanGR == 2 && !sumbitDisabled"
                  type="primary"
                  @click="getYuanNeiGRByBingLiID"
                >
                  医院感染部位
                </el-button>
                <span
                  v-if="!sumbitDisabled"
                  class="cryuan-right"
                  style="position: absolute; right: 6px; margin-top: 0"
                >
                  <el-button type="primary" plain @click="handleAdd(bingRenZDQKZY, 'zycryzd')">
                    添加
                  </el-button>
                  <el-button type="primary" plain @click="handleDelete(bingRenZDQKZY, 'zycryzd')">
                    删除
                  </el-button>
                </span>
              </td>
            </tr>
            <tr v-if="keshi3 == '中医'">
              <td colspan="10">
                <el-table :data="bingRenZDQKZY" border style="width: 100%">
                  <!-- :span-method="objectSpanMethod" -->
                  <el-table-column label="选择" width="50" align="center">
                    <template #default="{ $index }">
                      <el-checkbox
                        :disabled="sumbitDisabled"
                        :value="selectedIds2 === $index"
                        @change="() => handleRadioClick('zycryzd', $index)"
                      ></el-checkbox>
                    </template>
                  </el-table-column>
                  <el-table-column label="诊断类别" width="120">
                    <template #default="{ row }">
                      <!-- <span v-if="row.shunXuHao == 1 && row.shiFouZYZD == 1">主要诊断</span> -->
                      <span v-if="row.shunXuHao == 1">主要诊断</span>
                      <span v-else>其它诊断</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="诊断名称">
                    <template #default="{ row, $index }">
                      <el-input
                        v-model="row.zhenDuanMC"
                        :readonly="!sumbitDisabled"
                        :disabled="sumbitDisabled"
                        style="cursor: pointer"
                        class="select-input"
                        placeholder="请选择诊断名称"
                        @click.native="handleInputClick('zyzdmc_td', $index)"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="疾病编码" width="170">
                    <template #default="{ row }">
                      <span>{{ row.icd }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="入院病情">
                    <template #default="{ row }">
                      <el-radio v-model="row.ruYuanBQ" :disabled="sumbitDisabled" label="1">
                        有
                      </el-radio>
                      <el-radio v-model="row.ruYuanBQ" :disabled="sumbitDisabled" label="2">
                        临床未确认
                      </el-radio>
                      <el-radio v-model="row.ruYuanBQ" :disabled="sumbitDisabled" label="3">
                        情况不明
                      </el-radio>
                      <el-radio v-model="row.ruYuanBQ" :disabled="sumbitDisabled" label="4">
                        无
                      </el-radio>
                    </template>
                  </el-table-column>
                  <el-table-column label="" width="200" align="center">
                    <template #default="{ $index }">
                      <div class="move-buttons">
                        <el-button
                          :disabled="sumbitDisabled == true ? true : $index === 0"
                          @click="handleMoveUp(bingRenZDQKZY, $index)"
                        >
                          <i class="el-icon-top"></i>
                        </el-button>
                        <el-button
                          :disabled="
                            sumbitDisabled == true ? true : $index === bingRenZDQKZY.length - 1
                          "
                          @click="handleMoveDown(bingRenZDQKZY, $index)"
                        >
                          <i class="el-icon-bottom"></i>
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">损伤、中毒的外部原因:</td>
              <td class="right-table-content" colspan="3">
                <el-input
                  v-model="patientRecord.bingRenYLQK.sunShangZDWBYY"
                  :readonly="!sumbitDisabled"
                  :disabled="sumbitDisabled"
                  style="cursor: pointer"
                  class="select-input"
                  placeholder="请选择损伤、中毒的外部原因"
                  @click.native="handleInputClick('sszd')"
                ></el-input>
              </td>
              <td class="right-table-title">疾病代码:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.sunShangZDJBBM"
                  :disabled="defaultDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">病理诊断:</td>
              <td class="right-table-content" colspan="3">
                <el-input
                  v-model="patientRecord.bingRenYLQK.bingLiZD"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">疾病编码:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.bingLiZDJBBM"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">病理号:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.bingLiHao"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">药物过敏:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQK.yaoWuGM"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  无
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQK.yaoWuGM"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  有
                </el-radio>
              </td>
              <td class="right-table-title">过敏药物:</td>
              <td class="right-table-content" colspan="3">
                <el-input
                  v-model="patientRecord.bingRenYLQK.guoMinYW"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">死亡患者尸检:</td>
              <td class="right-table-content" colspan="3">
                <el-radio
                  v-for="(item, index) in siWangHZSJDM"
                  :key="index"
                  v-model="patientRecord.bingRenYLQK.siWangHZSJ"
                  :disabled="sumbitDisabled"
                  :label="item.daiMa"
                >
                  {{ item.mingCheng }}
                </el-radio>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">血型:</td>
              <td class="right-table-content" colspan="3">
                <el-radio
                  v-for="(item, index) in xieXingDM"
                  :key="index"
                  v-model="patientRecord.bingRenYLQK.xieXing"
                  :disabled="sumbitDisabled"
                  :label="item.daiMa"
                >
                  {{ item.mingCheng }}
                </el-radio>
              </td>
              <td class="right-table-title">RH:</td>
              <td class="right-table-content" colspan="5">
                <el-radio
                  v-for="(item, index) in rhDM"
                  :key="index"
                  v-model="patientRecord.bingRenYLQK.rh"
                  :disabled="sumbitDisabled"
                  :label="item.daiMa"
                >
                  {{ item.mingCheng }}
                </el-radio>
              </td>
            </tr>
            <tr>
              <td rowspan="2" colspan="2" style="text-align: center">诊断符合情况</td>
              <td class="right-table-title">门诊与出院：</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenYLQK.menChuFHBZ"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in zdfhqk"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">入院与出院：</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenYLQK.ruYuanCYFHBZ"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in zdfhqk"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">术前与术后：</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenYLQK.shuQianSHFHBZ"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in zdfhqk"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">临床与病理：</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenYLQK.linBingFHBZ"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in zdfhqk"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">放射与病理：</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenYLQK.fangBingFHBZ"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in zdfhqk"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">主诊组长:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.keZhuRenMC"
                  :disabled="defaultDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">(副)主任医师:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.zhuRenYSMC"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">主治医师:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.zhuZhiYSMC"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">住院医师：</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.zhuYuanYSMC"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">病历监管医师:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.bingLiJGYSMC"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">责任护士:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.zeRenHSMC"
                  :disabled="defaultDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">进修医师:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.jinXiuYSMC"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">实习医师:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.shiXiYSMC"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">编码人员</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.bianMaYuanMC"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">病案质量:</td>
              <td class="right-table-content">
                <el-radio
                  v-for="(item, index) in bingAnZLDM"
                  :key="index"
                  v-model="patientRecord.bingRenYLQK.bingAnZL"
                  :disabled="sumbitDisabled"
                  :label="item.daiMa"
                >
                  {{ item.mingCheng }}
                </el-radio>
              </td>
              <td class="right-table-title">质控医师:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.zhiKongYSMC"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">质控护士:</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQK.zhiKongHSMC"
                  :disabled="defaultDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">质控日期</td>
              <td class="right-table-content">
                <el-date-picker
                  v-model="patientRecord.bingRenYLQK.zhiKongSJ"
                  :disabled="sumbitDisabled"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="选择日期时间"
                ></el-date-picker>
              </td>
            </tr>
            <tr>
              <td colspan="10" class="cryuan-details" style="padding: 12px 6px">
                <span style="padding: 12px">
                  <span style="font-weight: bold; margin-right: 4px">手术情况</span>
                  (手术数据由手术记录数据自动关联产生)
                </span>
                <span v-if="!sumbitDisabled" class="cryuan-right">
                  <el-button
                    type="primary"
                    plain
                    @click="handleAdd(patientRecord.bingRenSSQK, 'ssqk')"
                  >
                    添加
                  </el-button>
                  <el-button
                    type="primary"
                    plain
                    @click="handleDelete(patientRecord.bingRenSSQK, 'ssqk')"
                  >
                    删除
                  </el-button>
                </span>
              </td>
            </tr>
            <tr>
              <td colspan="10">
                <el-table :data="patientRecord.bingRenSSQK" border size="small" style="width: 100%">
                  <el-table-column label="选择" width="50" align="center">
                    <template #default="{ $index }">
                      <el-checkbox
                        :disabled="sumbitDisabled"
                        :value="selectedIds3 === $index"
                        @change="() => handleRadioClick('ssqk', $index)"
                      ></el-checkbox>
                    </template>
                  </el-table-column>
                  <el-table-column label="手术及操作编码" width="130">
                    <template #default="{ row }">
                      <span>{{ row.icddm }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="手术及操作日期" width="170">
                    <template #default="{ row }">
                      <el-date-picker
                        v-model="row.shouShuRQ"
                        :disabled="sumbitDisabled"
                        class="ssjczrq"
                        placeholder="选择日期时间"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        format="yyyy-MM-dd"
                      ></el-date-picker>
                    </template>
                  </el-table-column>
                  <el-table-column label="手术级别" width="114">
                    <template #default="{ row }">
                      <el-select v-model="row.shouShuJB" :disabled="defaultDisabled" placeholder="">
                        <el-option
                          v-for="level in surgeryLevels"
                          :key="level.value"
                          :label="level.label"
                          :value="level.value"
                        ></el-option>
                      </el-select>
                      <!-- {{ formatSurgeryLevel(row.shouShuJB) }} -->
                    </template>
                  </el-table-column>
                  <el-table-column label="手术及操作名称" width="190">
                    <template #default="{ row, $index }">
                      <el-input
                        v-model="row.shouShuMC"
                        :readonly="!sumbitDisabled"
                        :disabled="sumbitDisabled"
                        style="cursor: pointer"
                        class="select-input"
                        placeholder="请选择损手术及操作名称"
                        @click.native="handleInputClick('ssmc', $index)"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="主术者" width="120">
                    <template #default="{ row }">
                      <el-input
                        v-model="row.shuZheXM"
                        :disabled="sumbitDisabled"
                        class="select-input"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="I助" width="120">
                    <template #default="{ row }">
                      <el-input
                        v-model="row.yiZhuXM"
                        :disabled="sumbitDisabled"
                        class="select-input"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="II助" width="120">
                    <template #default="{ row }">
                      <el-input
                        v-model="row.erZhuXM"
                        :disabled="sumbitDisabled"
                        class="select-input"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="切口" width="120">
                    <template #default="{ row }">
                      <el-select
                        v-model="row.qieKou"
                        :disabled="sumbitDisabled"
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in qkDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="愈合" width="120">
                    <template #default="{ row }">
                      <el-select v-model="row.yuHe" :disabled="sumbitDisabled" placeholder="请选择">
                        <el-option
                          v-for="item in yhDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="麻醉方式" width="120">
                    <template #default="{ row }">
                      <el-select
                        v-model="row.maZuiFS"
                        :disabled="sumbitDisabled"
                        filterable
                        placeholder="请选择"
                      >
                        <el-option
                          v-for="item in getMaZuiDM"
                          :key="item.daiMa"
                          :label="item.mingCheng"
                          :value="item.daiMa"
                        ></el-option>
                      </el-select>
                    </template>
                  </el-table-column>
                  <el-table-column label="麻醉医师" width="120">
                    <template #default="{ row }">
                      <el-input
                        v-model="row.maZuiYSXM"
                        :disabled="sumbitDisabled"
                        class="select-input"
                      ></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column label="" align="center" width="100">
                    <template #default="{ $index }">
                      <div class="move-buttons">
                        <el-button
                          :disabled="sumbitDisabled == true ? true : $index === 0"
                          @click="handleMoveUp(patientRecord.bingRenSSQK, $index)"
                        >
                          <i class="el-icon-top"></i>
                        </el-button>
                        <el-button
                          :disabled="
                            sumbitDisabled == true
                              ? true
                              : $index === patientRecord.bingRenSSQK.length - 1
                          "
                          @click="handleMoveDown(patientRecord.bingRenSSQK, $index)"
                        >
                          <i class="el-icon-bottom"></i>
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">主诊断转归情况:</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenYLQKZJ.zhuZhenDuanZGQK"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in zzdzgqk"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">并发症情况:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.bingFaZhengQK"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  无
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.bingFaZhengQK"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  有
                </el-radio>
              </td>
              <td class="right-table-title">手术患者类型:</td>
              <td class="right-table-content" colspan="3">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shouShuHZLX"
                  :disabled="sumbitDisabled"
                  label="0"
                >
                  非手术患者
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shouShuHZLX"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  急诊患者
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shouShuHZLX"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  择期手术
                </el-radio>
              </td>
              <td class="right-table-title">非计划再次手术:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.feiJiHuaZCSS"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  无
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.feiJiHuaZCSS"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  有
                </el-radio>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">医院感染:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.yiYuanGR"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  无
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.yiYuanGR"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  有
                </el-radio>
              </td>
              <td class="right-table-title">单病种管理:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.danBingZhongGL"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  否
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.danBingZhongGL"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  是
                </el-radio>
              </td>
              <td class="right-table-title">临床路径:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shiFouJRLCLJ"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  否
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shiFouJRLCLJ"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  是
                </el-radio>
              </td>
              <td class="right-table-title">是否完成临床路径:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shiFouWCLCLJ"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  否
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shiFouWCLCLJ"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  是
                </el-radio>
              </td>
              <td class="right-table-title">住院期间有无告病危:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJYWGBW"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  无
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJYWGBW"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  有
                </el-radio>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">入住ICU情况:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.ruZhuICUQK"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  无
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.ruZhuICUQK"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  有
                </el-radio>
              </td>
              <td class="right-table-title">抢救次数(次):</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQKZJ.qiangJiuCS"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">成功(次):</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQKZJ.chengGongCS"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">呼吸机使用时间(小时):</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQKZJ.huXiJiSYSJ"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">离院方式:</td>
              <td class="right-table-content">
                <el-select
                  v-model="patientRecord.bingRenYLQK.liYuanFS"
                  :disabled="sumbitDisabled"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in liYuanFSDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </td>
              <td class="right-table-title">拟接收医疗机构名称:</td>
              <td class="right-table-content" colspan="7">
                <el-input
                  v-model="patientRecord.bingRenYLQK.zhuanYuanJSJGMC"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">是否有出院31天再住院计划:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQK.shiFouYZYJH"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  无
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQK.shiFouYZYJH"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  有
                </el-radio>
              </td>
              <td class="right-table-title">目的:</td>
              <td class="right-table-content" colspan="7">
                <el-input
                  v-model="patientRecord.bingRenYLQK.zaiZhuYuanMD"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">颅脑损伤患者昏迷时间:</td>
              <td class="right-table-content" colspan="9">
                <span>入院前</span>
                <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                  <el-input
                    v-model="patientRecord.bingRenYLQK.ruYuanQianHMSJ1"
                    :disabled="sumbitDisabled"
                    class="select-input"
                  ></el-input>
                </span>
                <span>天</span>
                <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                  <el-input
                    v-model="patientRecord.bingRenYLQK.ruYuanQianHMSJ2"
                    :disabled="sumbitDisabled"
                    class="select-input"
                  ></el-input>
                </span>
                <span>小时</span>
                <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                  <el-input
                    v-model="patientRecord.bingRenYLQK.ruYuanQianHMSJ3"
                    :disabled="sumbitDisabled"
                    class="select-input"
                  ></el-input>
                </span>
                <span style="margin-right: 100px">分钟</span>
                <span>入院后</span>
                <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                  <el-input
                    v-model="patientRecord.bingRenYLQK.ruYuanHouHMSJ1"
                    :disabled="sumbitDisabled"
                    class="select-input"
                  ></el-input>
                </span>
                <span>天</span>
                <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                  <el-input
                    v-model="patientRecord.bingRenYLQK.ruYuanHouHMSJ2"
                    :disabled="sumbitDisabled"
                    class="select-input"
                  ></el-input>
                </span>
                <span>小时</span>
                <span class="" style="display: inline-block; width: 50px; margin: 0 5px">
                  <el-input
                    v-model="patientRecord.bingRenYLQK.ruYuanHouHMSJ3"
                    :disabled="sumbitDisabled"
                    class="select-input"
                  ></el-input>
                </span>
                <span>分钟</span>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">发生压疮:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shiFouFSYC"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  否
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shiFouFSYC"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  是
                </el-radio>
              </td>
              <td class="right-table-title">是否住院期间发生:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shiFouZYQJFS"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  否
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.shiFouZYQJFS"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  是
                </el-radio>
              </td>
              <td class="right-table-title">住院期间跌倒或坠床:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJFSDDHZC"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  否
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJFSDDHZC"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  是
                </el-radio>
              </td>
              <td class="right-table-title">住院期间身体约束:</td>
              <td class="right-table-content">
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJSTYS"
                  :disabled="sumbitDisabled"
                  label="1"
                >
                  否
                </el-radio>
                <el-radio
                  v-model="patientRecord.bingRenYLQKZJ.zhuYuanQJSTYS"
                  :disabled="sumbitDisabled"
                  label="2"
                >
                  是
                </el-radio>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">特级护理天数(天):</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQKZJ.teJiHLTS"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">一级护理天数(天):</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQKZJ.yiJiHLTS"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">二级护理天数(天):</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQKZJ.erJiHLTS"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
              <td class="right-table-title">三级护理天数(天):</td>
              <td class="right-table-content">
                <el-input
                  v-model="patientRecord.bingRenYLQKZJ.sanJiHLTS"
                  :disabled="sumbitDisabled"
                  class="select-input"
                ></el-input>
              </td>
            </tr>
          </table>
          <div class="right-header" style="margin: 11px 0">
            <div class="title">病人费用情况(具体费用清单请见医院信息系统提供的住院费用清单。)</div>
          </div>
          <table class="right-table">
            <tr>
              <td class="right-table-title" colspan="2" style="text-align: center">住院费用:</td>
              <td class="right-table-title">总费用：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.zongFeiYong }}</td>
              <td class="right-table-title">(其中自付金额)：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.ziFeiJE }}</td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center">
                1.综合医疗服务类
              </td>
              <td class="right-table-title">(1)一般医疗服务费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.yiBanYLFWF }}</td>
              <td class="right-table-title">(2)一般治疗操作费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.yiBanZLCZF }}</td>
              <td class="right-table-title">(3)护理费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.huLiFei }}</td>
              <td class="right-table-title">(4)其他费用：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.zongGeQTFY }}</td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center">2.诊断类</td>
              <td class="right-table-title">(5)病理诊断费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.bingLiZDF }}</td>
              <td class="right-table-title">(6)实验室诊断费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.shiYanSZDF }}</td>
              <td class="right-table-title">(7)影像学诊断费：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.yingXiangXZDF }}
              </td>
              <td class="right-table-title">(8)临床诊断项目费：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.linChuangZDXMF }}
              </td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center" rowspan="2">
                3.治疗类
              </td>
              <td class="right-table-title">(9)非手术治疗项目费：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.feiShouSZLXMF }}
              </td>
              <td class="right-table-title">(临床物理治疗费)：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.linChuangWLZLF }}
              </td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title">(10)手术治疗费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.shouShuZLF }}</td>
              <td class="right-table-title">(麻醉费)：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.maZuiFei }}</td>
              <td class="right-table-title">(手术费)：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.shouShuFei }}</td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center">4.康复类</td>
              <td class="right-table-title">(11)康复费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.kangFuFei }}</td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center">5.中医类</td>
              <td class="right-table-title">(12)中医治疗费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.zhongYiZLF }}</td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center">6.西药类</td>
              <td class="right-table-title">(13)西医费用：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.xiYaoFei }}</td>
              <td class="right-table-title">(抗菌药物费用)</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.kangJunYWFY }}</td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center">7.中药类</td>
              <td class="right-table-title">(14)中成药费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.zhongYaoFei }}</td>
              <td class="right-table-title">(15)中草药费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.caoYaoFei }}</td>
            </tr>

            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center" rowspan="2">
                8.血液和血液制品类
              </td>
              <td class="right-table-title">(16)血费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.shuXieFei }}</td>
              <td class="right-table-title">(17)蛋白质类制品费：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.baiDanBaiLZPF }}
              </td>
              <td class="right-table-title">(18)球蛋白类制品费：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.qiuDanBaiLZPF }}
              </td>
              <td class="right-table-title">(19)凝血因子类制品费：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.ningXieYZLZPF }}
              </td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title">(20)细胞因子类制品费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.xiBaoYZLZPF }}</td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center">9.耗材类</td>
              <td class="right-table-title">(21)检查用一次性医用材料费：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.jianChaYongYCXYYCLF }}
              </td>
              <td class="right-table-title">(22)治疗用一次性医用材料费：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.zhiLiaoYongYCXYYCLF }}
              </td>
              <td class="right-table-title">(23)手术用一次性医用材料费：</td>
              <td class="right-table-content">
                {{ patientRecord.bingRenSFMX.shouShuYongYCXYYCLF }}
              </td>
            </tr>
            <tr v-if="costShow">
              <td class="right-table-title" colspan="2" style="text-align: center">10.其他类</td>
              <td class="right-table-title">(24)其他费：</td>
              <td class="right-table-content">{{ patientRecord.bingRenSFMX.qiTaFei }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>

    <!-- 常见手术列表弹框 -->
    <div class="surgeries-class">
      <el-dialog
        :visible.sync="surgeriesVisible"
        title="常见手术列表"
        width="620px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <el-table :data="currentPageSurgeries" border stripe size="mini" max-height="480px">
          <el-table-column prop="mingCheng" label="手术名称" width="400"></el-table-column>
          <el-table-column prop="shuLiang" label="数量"></el-table-column>
        </el-table>
        <el-pagination
          :current-page.sync="surgeriesPagination.currentPage"
          :page-size.sync="surgeriesPagination.pageSize"
          layout="total, prev, pager, next"
          :total="SurgeriesData.length"
          @current-change="handleSurgeriesPageChange"
        ></el-pagination>
        <span slot="footer" class="dialog-footer">
          <el-button @click="surgeriesVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 常用诊断列表弹框 -->
    <div class="surgeries-class">
      <el-dialog
        :visible.sync="diagnosesVisible"
        title="常用诊断列表"
        width="620px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <el-table :data="currentPageDiagnoses" border stripe size="mini" max-height="480px">
          <el-table-column prop="mingCheng" label="诊断名称" width="400"></el-table-column>
          <el-table-column prop="shuLiang" label="数量"></el-table-column>
        </el-table>
        <el-pagination
          :current-page.sync="diagnosesPagination.currentPage"
          :page-size.sync="diagnosesPagination.pageSize"
          layout="total, prev, pager, next"
          :total="DiagnosesData.length"
          @current-change="handleDiagnosesPageChange"
        ></el-pagination>
        <span slot="footer" class="dialog-footer">
          <el-button @click="diagnosesVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 医院感染列表弹框 -->
    <div class="surgeries-class">
      <el-dialog
        :visible.sync="infectVisible"
        title="医院感染列表"
        width="620px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <div class="flex" style="margin-bottom: 14px; margin-top: -6px">
          请将院内感染诊断填入出院诊断中
        </div>
        <el-table :data="infectData" border stripe size="mini" max-height="480px">
          <el-table-column prop="ganRanSJ" width="230" label="感染时间"></el-table-column>
          <el-table-column prop="ganRanBW" width="352" label="感染部位"></el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button @click="infectVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>

    <!-- 诊断、中毒、手术 -->
    <div class="surgeries-class">
      <el-dialog
        class="zxs-class"
        :visible.sync="zxsVisible"
        :title="selectType == 'ssmc' ? '手术列表' : '诊断列表'"
        width="620px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <div class="flex">
          <el-input
            v-model="zxsXmInput"
            class="zxsXMInput"
            placeholder="请输入拼音码/名称查询"
            @input="zxsXmSearch"
          ></el-input>
        </div>
        <el-table :data="zxsList" border size="mini" max-height="480px">
          <!-- 疾病 -->
          <el-table-column
            v-if="selectType == 'jb' || selectType == 'zdmc_td' || selectType == 'zyzdmc_td'"
            key="zhenDuanMC"
            prop="zhenDuanMC"
            label="诊断名称"
            width="310"
          ></el-table-column>
          <el-table-column
            v-if="selectType == 'jb' || selectType == 'zdmc_td' || selectType == 'zyzdmc_td'"
            key="icd"
            prop="icd"
            label="ICD编码"
            width="160"
          ></el-table-column>
          <!-- 受伤中毒 -->
          <el-table-column
            v-if="selectType == 'sszd'"
            key="mingChen"
            prop="mingChen"
            label="诊断名称"
            width="310"
          ></el-table-column>
          <el-table-column
            v-if="selectType == 'sszd'"
            key="daiMa"
            prop="daiMa"
            label="ICD编码"
            width="160"
          ></el-table-column>
          <!-- 手术名称 -->
          <el-table-column
            v-if="selectType == 'ssmc'"
            key="shouShuMC"
            prop="shouShuMC"
            label="手术名称"
            width="215"
          ></el-table-column>
          <el-table-column
            v-if="selectType == 'ssmc'"
            key="icddm"
            prop="icddm"
            label="ICD编码"
            width="125"
          ></el-table-column>
          <el-table-column
            v-if="selectType == 'ssmc'"
            key="shouShuJB"
            prop="shouShuJB"
            label="默认手术级别"
          ></el-table-column>
          <el-table-column
            v-if="selectType == 'ssmc'"
            key="shouShuLB"
            prop="shouShuLB"
            label="手术类型"
          ></el-table-column>
          <el-table-column label="操作" :width="selectType == 'ssmc' ? '' : '112'" align="center">
            <template #default="scope">
              <el-button size="mini" @click="zxsXmSelect(scope.row)">选择</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="zxsPagination.currentPage"
          :page-size="zxsPagination.pageSize"
          background
          layout="total, prev, pager, next"
          :total="zhenduanLength"
          @current-change="zxsPageChange"
        ></el-pagination>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="zxsVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {
  InitTheFirstPageOfMedicalRecord,
  saveTheFirstPageOfMedicalRecord,
  getBingAnSYByBingLiID,
  InitTheFirstPageFormOfMedicalRecord,
  getJiGuanDMList,
  getMaZuiFSList,
  getCommonSurgeriesByZhuanKe,
  getCommonDiagnosesByZhuanKe,
  getYlBasySswyByPageAndZhuangTai,
  getZhenDuanByPageAndZhuanTai,
  getYlSsicdByPageAndZhuanTai,
  getShouYeCDSSNR,
  triggerRuleBySceneCodeInGeneralMode,
  getWenShuZDByBingLiID,
  getQuanBuWSZDByBingLiID,
  changeBingAnShouYeTiJiaoZTByBingLiID,
  getYuanNeiGRByBingLiID,
  GetPrintContent
} from '@/api/medical-record-home'
import { format, getYear } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      selectType: '', //弹框选择的类型
      zxsVisible: false, // 诊断、中毒、手术弹框的判断
      defaultDisabled: true, //默认不可编辑
      sumbitDisabled: false, //已提交的不可编辑
      sfzDisabled: false, //判断证件类型是否是身份证不可编辑
      selectedIds1: '', //表格勾选分类
      selectedIds2: '', //
      selectedIds3: '', //
      spanArr: [], // 存储合并信息
      surgeriesVisible: false, //常用手术弹框
      diagnosesVisible: false, //常用诊断弹框
      infectVisible: false, //医院感染弹框
      SurgeriesData: [], //常见手术列表
      DiagnosesData: [], //常用诊断列表
      infectData: [], //医院感染列表
      surgeriesPagination: {
        currentPage: 1,
        pageSize: 10
      }, //常见手术分页
      diagnosesPagination: {
        currentPage: 1,
        pageSize: 10
      }, //常见手术分页

      getJiGuanDM: [], //籍贯列表
      getMaZuiDM: [], //麻醉列表
      zhengJianLXDM: [
        { mingCheng: '身份证', daiMa: '1' },
        { mingCheng: '护照', daiMa: '2' },
        { mingCheng: '中国人民解放军军人身份证件', daiMa: '3' },
        { mingCheng: '中国人民武装警察身份证件', daiMa: '4' },
        { mingCheng: '港澳居民来往内地通行证', daiMa: '5' },
        { mingCheng: '台湾居民来往大陆通行证', daiMa: '6' },
        { mingCheng: '其他', daiMa: '99' }
      ],
      bingLiCSZDM: [
        { mingCheng: '患者', daiMa: '患者' },
        { mingCheng: '患者家属', daiMa: '患者家属' },
        { mingCheng: '患者及其家属', daiMa: '患者及其家属' },
        { mingCheng: '孕妇本人', daiMa: '孕妇本人' },
        { mingCheng: '产科医师', daiMa: '产科医师' },
        { mingCheng: '助产士', daiMa: '助产士' },
        { mingCheng: '儿科医师', daiMa: '儿科医师' },
        { mingCheng: '者家属及产科医师', daiMa: '者家属及产科医师' },
        { mingCheng: '患者家属及助产士', daiMa: '患者家属及助产士' },
        { mingCheng: '患者家属及儿科医师', daiMa: '患者家属及儿科医师' },
        { mingCheng: '其他', daiMa: '其他' }
      ],
      ruYuanTJDM: [
        { mingCheng: '急诊', daiMa: '1' },
        { mingCheng: '门诊', daiMa: '2' },
        { mingCheng: '其他医疗机构转入', daiMa: '3' },
        { mingCheng: '其他', daiMa: '4' }
      ],
      siWangHZSJDM: [
        { mingCheng: '是', daiMa: '1' },
        { mingCheng: '否', daiMa: '2' },
        { mingCheng: '非死亡', daiMa: null }
      ],
      xieXingDM: [
        { mingCheng: 'A', daiMa: '1' },
        { mingCheng: 'B', daiMa: '2' },
        { mingCheng: 'O', daiMa: '3' },
        { mingCheng: 'AB', daiMa: '4' },
        { mingCheng: '不详', daiMa: '5' },
        { mingCheng: '未查', daiMa: '6' }
      ],
      rhDM: [
        { mingCheng: '阴', daiMa: '1' },
        { mingCheng: '阳', daiMa: '2' },
        { mingCheng: '不详', daiMa: '3' },
        { mingCheng: '未查', daiMa: '4' }
      ],
      zdfhqk: [
        { mingCheng: '未做', daiMa: '0' },
        { mingCheng: '符合', daiMa: '1' },
        { mingCheng: '不符合', daiMa: '2' },
        { mingCheng: '不肯定', daiMa: '3' }
      ],
      bingAnZLDM: [
        { mingCheng: '甲', daiMa: '1' },
        { mingCheng: '乙', daiMa: '2' },
        { mingCheng: '丙', daiMa: '3' }
      ],
      zzdzgqk: [
        { mingCheng: '治愈', daiMa: '1' },
        { mingCheng: '好转', daiMa: '2' },
        { mingCheng: '未愈', daiMa: '3' },
        { mingCheng: '死亡', daiMa: '4' },
        { mingCheng: '其他', daiMa: '5' }
      ],
      liYuanFSDM: [
        { mingCheng: '医嘱离院', daiMa: '1' },
        { mingCheng: '医嘱转院', daiMa: '2' },
        { mingCheng: '医嘱转社区卫生服务机构/乡镇卫生院', daiMa: '3' },
        { mingCheng: '非医嘱离院', daiMa: '4' },
        { mingCheng: '死亡', daiMa: '5' },
        { mingCheng: '其他', daiMa: '9' }
      ],
      qkDM: [
        { mingCheng: '0', daiMa: '0' },
        { mingCheng: 'I', daiMa: 'I' },
        { mingCheng: 'II', daiMa: 'II' },
        { mingCheng: 'III', daiMa: 'III' }
      ],
      yhDM: [
        { mingCheng: '甲', daiMa: '甲' },
        { mingCheng: '乙', daiMa: '乙' },
        { mingCheng: '丙', daiMa: '丙' },
        { mingCheng: '其他', daiMa: '其他' }
      ],
      surgeryLevels: [
        { value: '1', label: '一级' },
        { value: '2', label: '二级' },
        { value: '3', label: '三级' },
        { value: '4', label: '四级' },
        { value: '5', label: '五级' },
        { value: '6', label: '六级' },
        { value: '7', label: '七级' }
      ], //手术列表映射
      keshi1: '', //特殊科室
      keshi2: '', //
      keshi3: '', //
      costShow: false, //费用明细展示判断
      submitShow: false, //是否已提交判断
      saveShow: true, //是否未病区入院保存判断
      patientRecord: {
        bingRenJBXX: {
          yiLiaoFFFS: '',
          empi: '',
          zhuYuanCS: '',
          bingRenXM: '',
          bingRenXB: '',
          chuShengRQ: '',
          ruYuanSJ: '',
          chuShengRQ: '',
          guoJi: '',
          xinShengErYL: '',
          xinShengErTS: '',
          xinShengErCSTZ: '',
          xinShengErRYTZ: '',
          fuMuXM: '',
          zhangQiJZD: '',
          chuShengDe: '',
          jiGuanDM: '',
          minZuDM: '',
          zhiYeDM: '',
          hunYinQK: '',
          zhengJianLX: '',
          shenFenZH: '',
          xianZhuZhiDH: '',
          xianJuZhuDZ: '',
          xianZhuZhiYB: '',
          huKouDZ: '',
          huKouYB: '',
          gongZuoDWJDZ: '',
          danWeiDH: '',
          danWeiYB: '',
          lianXiRenXM: '',
          lianXiRenGX: '',
          lianXiRenDZ: '',
          lianXiRenDH: '',
          zhangFuXM: '',
          chanQianJC: '',
          moCiYJ: '',
          yuChanQiSJ: '',
          huaiYunCS: '',
          chanCi: '',
          bingLiCSZ: '',
          ruYuanTJ: '',
          ruYuanSJ: '',
          ruYuanZKMC: '',
          ruYuanBQMC: '',
          ruYuanCWH: '',
          zhuanKeKB: '',
          chuYuanSJ: '',
          chuYuanZKMC: '',
          chuYuanBQMC: '',
          chuYuanCWH: '',
          zhuYuanTS: ''
        },
        bingRenSFMX: {},
        bingRenYLQK: {},
        bingRenYLQKSZ: {},
        bingRenYLQKZJ: {}
      }, //病人基本信息
      medicalRecord: {
        yiLiaoZFFSDM: [],
        guoJiDM: [],
        mingZuDM: [],
        zhiYeDM: [],
        hunYinDM: [],
        lianXiRenGXDM: []
      }, //病人全部信息
      bingRenZDQKXY: [], //诊断西医
      bingRenZDQKZY: [], //诊断中医

      zxsXmInput: '', //诊断、中毒、手术搜索输入框
      zxsPagination: {
        currentPage: 1,
        pageSize: 10
      }, //诊断、中毒、手术分页
      zxsList: [], ////诊断、中毒、手术列表数据
      zhenduanLength: 0 ////诊断、中毒、手术列表长度
    }
  },
  computed: {
    // 手术列表当前页数据
    currentPageSurgeries() {
      const start = (this.surgeriesPagination.currentPage - 1) * this.surgeriesPagination.pageSize
      const end = start + this.surgeriesPagination.pageSize
      return this.SurgeriesData.slice(start, end)
    },

    // 诊断列表当前页数据
    currentPageDiagnoses() {
      const start = (this.diagnosesPagination.currentPage - 1) * this.diagnosesPagination.pageSize
      const end = start + this.diagnosesPagination.pageSize
      return this.DiagnosesData.slice(start, end)
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit,
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  watch: {
    'patientRecord.bingRenZDQK': {
      handler() {
        this.calcSpanMethod() // 数据变化时重新计算合并范围
      },
      deep: true
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    // 计算合并范围
    calcSpanMethod() {
      this.spanArr = []
      let pos = 0 // 记录合并起始的位置
      const data = this.patientRecord.bingRenZDQK

      // 遍历数据，计算相同诊断类别的连续行数
      for (let i = 0; i < data.length; i++) {
        if (i === 0) {
          this.spanArr.push(1)
          pos = 0
        } else {
          // 判断当前行和上一行的诊断类别是否相同
          // const currentIsMain = data[i].shunXuHao == 1 && data[i].shiFouZYZD == 1
          // const prevIsMain = data[i - 1].shunXuHao == 1 && data[i - 1].shiFouZYZD == 1
          const currentIsMain = data[i].shunXuHao == 1
          const prevIsMain = data[i - 1].shunXuHao == 1

          if (currentIsMain === prevIsMain) {
            this.spanArr[pos] += 1 // 合并行数+1
            this.spanArr.push(0) // 当前行被合并，不显示
          } else {
            this.spanArr.push(1) // 新合并组
            pos = i // 更新合并起始位置
          }
        }
      }
    },
    // 合并单元格方法
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 1) {
        // 只对"诊断类别"列处理
        const span = this.spanArr[rowIndex]
        if (span > 0) {
          return {
            rowspan: span, // 合并的行数
            colspan: 1 // 合并的列数（固定1列）
          }
        } else {
          return {
            rowspan: 0, // 不显示（被合并）
            colspan: 0
          }
        }
      }
    },
    // 手术列表分页处理
    handleSurgeriesPageChange(page) {
      this.surgeriesPagination.currentPage = page
    },

    // 诊断列表分页处理
    handleDiagnosesPageChange(page) {
      this.diagnosesPagination.currentPage = page
    },

    // 年龄判断
    calculateAge(rysj, birthYear) {
      return format(new Date(rysj), 'yyyy') - format(new Date(birthYear), 'yyyy')
    },

    // 表格项向上移动
    handleMoveUp(arr, index) {
      if (index > 0) {
        const temp = arr[index]
        this.$set(arr, index, arr[index - 1])
        this.$set(arr, index - 1, temp)
      }
    },

    // 表格项向下移动
    handleMoveDown(arr, index) {
      if (index < arr.length - 1) {
        const temp = arr[index]
        this.$set(arr, index, arr[index + 1])
        this.$set(arr, index + 1, temp)
      }
    },

    // 表格项添加
    handleAdd(arr, type) {
      if (type == 'cryzd') {
        arr.push({
          bingAnBM: '',
          bingLiID: '',
          caoZuoZheID: '',
          fuID: '',
          icd: '',
          jiBingID: '',
          ruYuanBQ: '',
          shiFouZYZD: '',
          shunXuHao: '',
          xiuGaiSJ: '',
          zhenDuanLB: '2',
          zhenDuanMC: '',
          zhuanGuiQK: '',
          zhuanKeID: ''
        })
      } else if (type == 'zycryzd') {
        arr.push({
          bingAnBM: '',
          bingLiID: '',
          caoZuoZheID: '',
          fuID: '',
          icd: '',
          jiBingID: '',
          ruYuanBQ: '',
          shiFouZYZD: '',
          shunXuHao: '',
          xiuGaiSJ: '',
          zhenDuanLB: '11',
          zhenDuanMC: '',
          zhuanGuiQK: '',
          zhuanKeID: ''
        })
      } else if (type == 'ssqk') {
        arr.push({
          asa: '',
          bingLiID: '',
          caoZuoZheID: '',
          diErZSID: '',
          diYiZSID: '',
          erZhuXM: '',
          icddm: '',
          maZuiFS: '',
          maZuiFSMC: '',
          maZuiYSID: '',
          maZuiYSXM: '',
          nnis: '',
          qieKouYHDJ: '/',
          shiFouZQSS: '',
          shouShuBH: '',
          shouShuDM: '',
          shouShuID: '',
          shouShuJB: '',
          shouShuLX: '',
          shouShuMC: '',
          shouShuRQ: '',
          shouShuBH: '0',
          shuZheXM: '',
          shuZheYHID: '',
          shunXuHao: '',
          xiuGaiSJ: '',
          yiZhuXM: '',
          youChuangCZ: '',
          zhuangTaiBZ: ''
        })
      }
    },

    // 表格项删除
    handleDelete(arr) {
      if (this.selectedIds1 != '') {
        arr.splice(this.selectedIds1, 1)
      } else if (this.selectedIds2 != '') {
        arr.splice(this.selectedIds2, 1)
      } else if (this.selectedIds3 != '') {
        arr.splice(this.selectedIds3, 1)
      }
      this.selectedIds1 = ''
      this.selectedIds2 = ''
      this.selectedIds3 = ''
    },

    // 处理表格项单选按钮点击
    handleRadioClick(type, index) {
      if (type == 'cryzd') {
        this.selectedIds1 = index
      } else if (type == 'zycryzd') {
        this.selectedIds2 = index
      } else if (type == 'ssqk') {
        this.selectedIds3 = index
      }
    },

    // 处理证件类型选择按钮点击
    handleOptionlick() {
      if (this.patientRecord.bingRenJBXX.zhengJianLX == '1') {
        this.sfzDisabled = true
      } else {
        this.sfzDisabled = false
      }
    },

    // 页面初始化
    async init() {
      this.InitTheFirstPageOfMedicalRecord()
    },

    // 手术列表映射
    formatSurgeryLevel(value) {
      const map = { 1: '一级', 2: '二级', 3: '三级', 4: '四级', 5: '五级', 6: '六级', 7: '七级' }
      return map[value] || value
    },

    // 住院医生站_查询_病案首页初始化
    async InitTheFirstPageOfMedicalRecord(data) {
      try {
        const res = await InitTheFirstPageOfMedicalRecord({
          bingLiID: this.patientDetail.bingLiID,
          zhuYuanID: this.patientDetail.zhuYuanID
        })
        if (res.hasError === 0) {
          this.getJiGuanDMList()
          this.getMaZuiFSList()
          if (res.data.jianDanZD.财务明细展示 == 1) {
            this.costShow = true
          }
          if (res.data.jianDanZD.特殊专科 == 1) {
            this.keshi1 = '妇科'
            this.keshi2 = '儿科'
          } else if (res.data.jianDanZD.特殊专科 == 2) {
            this.keshi2 = '儿科'
          } else if (res.data.jianDanZD.特殊专科 == 3) {
            this.keshi3 = '中医'
          }
          if (res.data.jianDanZD.未病区入院 == 1) {
            this.saveShow = false
          }

          if (res.data.jianDanZD.提交状态 == 1) {
            this.getBingAnSYByBingLiID()
            this.sumbitDisabled = true
            this.sfzDisabled = true
            this.submitShow = true
          } else {
            this.sumbitDisabled = false
            this.submitShow = false
            this.medicalRecord = res.data
            this.patientRecord = res.data.theFirstPageOfMedicalRecordVo
            // 证件类型判断是否是身份证-不可编辑
            if (this.patientRecord.bingRenJBXX.zhengJianLX == '1') {
              this.sfzDisabled = true
            }
            const [qian1, qian2, qian3] = (
              this.patientRecord.bingRenYLQK.ruYuanQianHMSJ || ''
            ).split('^')
            const [hou1, hou2, hou3] = (this.patientRecord.bingRenYLQK.ruYuanHouHMSJ || '').split(
              '^'
            )

            this.patientRecord.bingRenYLQK = {
              ...this.patientRecord.bingRenYLQK,
              ruYuanQianHMSJ1: qian1,
              ruYuanQianHMSJ2: qian2,
              ruYuanQianHMSJ3: qian3,
              ruYuanHouHMSJ1: hou1,
              ruYuanHouHMSJ2: hou2,
              ruYuanHouHMSJ3: hou3
            }
            this.patientRecord.bingRenSSQK = this.patientRecord.bingRenSSQK.map((item) => ({
              ...item,
              qieKou: item.qieKouYHDJ?.split('/')[0] || '',
              yuHe: item.qieKouYHDJ?.split('/')[1] || ''
            }))

            this.bingRenZDQKXY = res.data.theFirstPageOfMedicalRecordVo.bingRenZDQK.filter(
              (item) => item.zhenDuanLB === '2'
            )
            this.bingRenZDQKZY = res.data.theFirstPageOfMedicalRecordVo.bingRenZDQK.filter(
              (item) => item.zhenDuanLB === '11'
            )
            this.calcSpanMethod()
          }
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 住院医生站_查询_查询一个BingLiID的病案首页
    async getBingAnSYByBingLiID(data) {
      try {
        const res = await getBingAnSYByBingLiID({
          bingLiID: this.patientDetail.bingLiID
        })
        if (res.hasError === 0) {
          this.InitTheFirstPageFormOfMedicalRecord()
          this.patientRecord = res.data
          const [qian1, qian2, qian3] = (this.patientRecord.bingRenYLQK.ruYuanQianHMSJ || '').split(
            '^'
          )
          const [hou1, hou2, hou3] = (this.patientRecord.bingRenYLQK.ruYuanHouHMSJ || '').split('^')

          this.patientRecord.bingRenYLQK = {
            ...this.patientRecord.bingRenYLQK,
            ruYuanQianHMSJ1: qian1,
            ruYuanQianHMSJ2: qian2,
            ruYuanQianHMSJ3: qian3,
            ruYuanHouHMSJ1: hou1,
            ruYuanHouHMSJ2: hou2,
            ruYuanHouHMSJ3: hou3
          }
          this.patientRecord.bingRenSSQK = this.patientRecord.bingRenSSQK.map((item) => ({
            ...item,
            qieKou: item.qieKouYHDJ?.split('/')[0] || '',
            yuHe: item.qieKouYHDJ?.split('/')[1] || ''
          }))

          this.bingRenZDQKXY = res.data.bingRenZDQK.filter((item) => item.zhenDuanLB === '2')
          this.bingRenZDQKZY = res.data.bingRenZDQK.filter((item) => item.zhenDuanLB === '11')
          this.calcSpanMethod()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 已提交状态获取数据
    async InitTheFirstPageFormOfMedicalRecord(data) {
      try {
        const res = await InitTheFirstPageFormOfMedicalRecord({
          bingLiID: this.patientDetail.bingLiID,
          zhuYuanID: this.patientDetail.zhuYuanID
        })
        if (res.hasError === 0) {
          if (res.data.jianDanZD.财务明细展示 == 1) {
            this.costShow = true
          }
          if (res.data.jianDanZD.特殊专科 == 1) {
            this.keshi1 = '妇科'
            this.keshi2 = '儿科'
          } else if (res.data.jianDanZD.特殊专科 == 2) {
            this.keshi2 = '儿科'
          } else if (res.data.jianDanZD.特殊专科 == 3) {
            this.keshi3 = '中医'
          }
          if (res.data.jianDanZD.未病区入院 == 1) {
            this.saveShow = false
          }
          this.medicalRecord = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 电子病历_查询_查询一个专科id的常用手术
    async getCommonSurgeriesByZhuanKe(data) {
      this.surgeriesVisible = true
      try {
        const res = await getCommonSurgeriesByZhuanKe({
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.SurgeriesData = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 电子病历_查询_查询一个科室id的常用诊断
    async getCommonDiagnosesByZhuanKe(data) {
      this.diagnosesVisible = true
      try {
        const res = await getCommonDiagnosesByZhuanKe({
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.DiagnosesData = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 住院医生站_查询_根据病历ID获取院内感染信息
    async getYuanNeiGRByBingLiID(data) {
      this.infectVisible = true
      try {
        const res = await getYuanNeiGRByBingLiID({
          bingLiID: this.patientDetail.bingLiID
        })
        if (res.hasError === 0) {
          this.infectData = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 获取溶栓时间
    async getShouYeCDSSNR(type) {
      try {
        const res = await getShouYeCDSSNR({
          bingLiID: this.patientDetail.bingLiID
        })
        if (res.hasError === 0) {
          this.CDSSNR = res.data
          this.triggerRuleBySceneCodeInGeneralMode(type)
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 调用提交接口前的状态
    async triggerRuleBySceneCodeInGeneralMode(type) {
      try {
        const res = await triggerRuleBySceneCodeInGeneralMode({
          biaoShiHao: this.patientRecord.bingRenJBXX.bingAnHao,
          bingAnHao: this.patientRecord.bingRenJBXX.bingLiID,
          changJingDM: 'basy',
          linShiSJ: {
            YlBasyBrjbxxPo: this.patientRecord.bingRenJBXX,
            YlBasyBrylqkPo: this.patientRecord.bingRenYLQK,
            YlBasyBrylqkSzPo: {
              zhangFuSFZH: this.patientRecord.bingRenYLQKSZ.zhangFuSFZH
            },
            YlBasyBrylqkZjPo: this.patientRecord.bingRenYLQKZJ,
            YlBasyZybasysfmxPo: this.patientRecord.bingRenSFMX,
            YlBasyBrssqkPo: this.patientRecord.bingRenSSQK,
            YlZyblbrzdPo: this.bingRenZDQKXY.concat(this.bingRenZDQKZY)
          },
          linShiZD: {
            rongShuanSJ: this.CDSSNR.rongShuanSJ || '',
            rongShuanYS: this.CDSSNR.rongShuanYS || ''
          },
          shiFouMZ: false,
          shiFouTJ: true
        })
        this.handleSaveRecord(1)
        // if (res.hasError === 0) {
        //   this.handleSaveRecord(1)
        // } else if (res.hasError === 1) {
        //   this.$message({
        //     message: '质控测试发现提示信息',
        //     type: 'error'
        //   })
        // } else if (res.hasError === 2) {
        //   this.$message({
        //     message: '错误信息与提示信息',
        //     type: 'error'
        //   })
        // }
      } catch (error) {
        console.log(error)
      }
    },

    // 撤销提交
    async WithdrawSubmit() {
      try {
        const res = await changeBingAnShouYeTiJiaoZTByBingLiID({
          bingLiID: this.patientDetail.bingLiID,
          zhuangTaiBZ: 0
        })
        if (res.hasError === 0) {
          this.InitTheFirstPageOfMedicalRecord()
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 查询_获取籍贯代码List
    async getJiGuanDMList(data) {
      try {
        const res = await getJiGuanDMList()
        if (res.hasError === 0) {
          this.getJiGuanDM = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 查询_获取麻醉方式List
    async getMaZuiFSList(data) {
      try {
        const res = await getMaZuiFSList()
        if (res.hasError === 0) {
          this.getMaZuiDM = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 常用的西医诊断的同步
    async getWenShuZDByBingLiID() {
      try {
        const res = await getWenShuZDByBingLiID({
          bingLiID: this.patientDetail.bingLiID
        })
        if (res.hasError === 0) {
          this.bingRenZDQKXY = res.data.map((item) => ({
            ...item,
            shunXuHao: item.xuHao,
            zhenDuanMC: item.mingChen,
            ruYuanBQ: '1',
            zhenDuanLB: '2'
          }))
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 常用的中医诊断的同步
    async getQuanBuWSZDByBingLiID() {
      try {
        const res = await getQuanBuWSZDByBingLiID({
          bingLiID: this.patientDetail.bingLiID,
          LeiBie: 11
        })
        if (res.hasError === 0) {
          this.bingRenZDQKZY = res.data.map((item) => ({
            ...item,
            shunXuHao: item.xuHao,
            zhenDuanMC: item.mingChen,
            ruYuanBQ: '1',
            zhenDuanLB: '11'
          }))
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 集中打印-打印
    async GetPrintContent() {
      try {
        const res = await GetPrintContent({
          bingLiID: this.patientDetail.bingLiID,
          lieBiao: 'BASY',
          yongHuID: this.yongHuID
        })
        if (res.hasError === 0) {
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 点击搜索事件
    handleInputClick(type, index) {
      this.selectType = type
      this.selectIndex = index
      this.zxsVisible = true
      this.zxsXmInput = ''
      this.zxsPagination.currentPage = 1
      if (
        this.selectType == 'jb' ||
        this.selectType == 'zdmc_td' ||
        this.selectType == 'zyzdmc_td'
      ) {
        this.getzhenduanbypageandzhuantai()
      } else if (this.selectType == 'sszd') {
        this.getYlBasySswyByPageAndZhuangTai()
      } else if (this.selectType == 'ssmc') {
        this.getYlSsicdByPageAndZhuanTai()
      }
    },

    // 诊中手列表分页处理
    zxsPageChange(page) {
      this.zxsPagination.currentPage = page
      if (
        this.selectType == 'jb' ||
        this.selectType == 'zdmc_td' ||
        this.selectType == 'zyzdmc_td'
      ) {
        this.getzhenduanbypageandzhuantai()
      } else if (this.selectType == 'sszd') {
        this.getYlBasySswyByPageAndZhuangTai()
      } else if (this.selectType == 'ssmc') {
        this.getYlSsicdByPageAndZhuanTai()
      }
    },

    // 诊中手列表查询
    async zxsXmSearch() {
      this.zxsPagination.currentPage = 1
      if (
        this.selectType == 'jb' ||
        this.selectType == 'zdmc_td' ||
        this.selectType == 'zyzdmc_td'
      ) {
        this.getzhenduanbypageandzhuantai()
      } else if (this.selectType == 'sszd') {
        this.getYlBasySswyByPageAndZhuangTai()
      } else if (this.selectType == 'ssmc') {
        this.getYlSsicdByPageAndZhuanTai()
      }
    },

    // 诊中手列表选择某一项
    zxsXmSelect(row) {
      if (this.selectType == 'jb') {
        this.patientRecord.bingRenYLQK.menZhenZD = row.zhenDuanMC
        this.patientRecord.bingRenYLQK.menZhenZDDM = row.icd
      } else if (this.selectType == 'zdmc_td') {
        const originalItem = this.bingRenZDQKXY[this.selectIndex] || {}
        this.$set(this.bingRenZDQKXY, this.selectIndex, {
          ...originalItem, // 保留所有原有字段
          bingAnBM: row.bingAnBM,
          bingLiID: this.patientDetail.bingLiID,
          caoZuoZheID: row.caoZuoZheID,
          icd: row.icd,
          jiBingID: row.jiBingID,
          shunXuHao: originalItem.shunXuHao || this.bingRenZDQKXY.length,
          xiuGaiSJ: row.xiuGaiSJ,
          zhenDuanMC: row.zhenDuanMC
        })
      } else if (this.selectType == 'zyzdmc_td') {
        const originalItem = this.bingRenZDQKZY[this.selectIndex] || {}
        this.$set(this.bingRenZDQKZY, this.selectIndex, {
          ...originalItem, // 保留所有原有字段
          bingAnBM: row.bingAnBM,
          bingLiID: this.patientDetail.bingLiID,
          caoZuoZheID: row.caoZuoZheID,
          icd: row.icd,
          jiBingID: row.jiBingID,
          shunXuHao: originalItem.shunXuHao || this.bingRenZDQKZY.length,
          xiuGaiSJ: row.xiuGaiSJ,
          zhenDuanMC: row.zhenDuanMC
        })
      } else if (this.selectType == 'sszd') {
        this.patientRecord.bingRenYLQK.sunShangZDWBYY = row.mingChen
        this.patientRecord.bingRenYLQK.sunShangZDJBBM = row.daiMa
      } else if (this.selectType == 'ssmc') {
        const originalItem = this.patientRecord.bingRenSSQK[this.selectIndex] || {}
        this.$set(this.patientRecord.bingRenSSQK, this.selectIndex, {
          ...originalItem, // 保留所有原有字段
          bingLiID: this.patientDetail.bingLiID,
          caoZuoZheID: row.caoZuoZheID,
          icddm: row.icddm,
          shouShuID: row.shouShuID,
          shouShuJB: row.shouShuJB,
          shouShuLB: row.shouShuLB,
          shouShuMC: row.shouShuMC,
          shouShuDM: row.icddm,
          shunXuHao: originalItem.shunXuHao || this.patientRecord.bingRenSSQK.length,
          xiuGaiSJ: row.xiuGaiSJ,
          zhuangTaiBZ: row.zhuangTaiBZ,
          shuZheXM: '孙成超',
          shuZheYHID: 2074,
          yiZhuXM: '李叶平',
          diYiZSID: 9520,
          erZhuXM: '陈烁苹 ',
          diErZSID: 8863
        })
      }
      this.zxsVisible = false
    },

    // 分页搜索诊断列表
    async getzhenduanbypageandzhuantai() {
      try {
        const res = await getZhenDuanByPageAndZhuanTai({
          pageIndex: this.zxsPagination.currentPage,
          pageSize: 10,
          wenBen: this.zxsXmInput
        })
        if (res.hasError === 0) {
          this.zxsList = res.data
          this.zhenduanLength = res.extendData.total
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 分页搜索损伤、中毒的外部原因列表
    async getYlBasySswyByPageAndZhuangTai() {
      try {
        const res = await getYlBasySswyByPageAndZhuangTai({
          pageIndex: this.zxsPagination.currentPage,
          pageSize: 10,
          wenBen: this.zxsXmInput
        })
        if (res.hasError === 0) {
          this.zxsList = res.data
          this.zhenduanLength = res.extendData.toal
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 分页搜索手术列表
    async getYlSsicdByPageAndZhuanTai() {
      try {
        const res = await getYlSsicdByPageAndZhuanTai({
          pageIndex: this.zxsPagination.currentPage,
          pageSize: 10,
          wenBen: this.zxsXmInput
        })
        if (res.hasError === 0) {
          this.zxsList = res.data
          this.zhenduanLength = res.extendData.total
        }
      } catch (error) {
        console.log(error)
      }
    },

    validateAllData() {
      const errors = []
      // 校验入院诊断数据
      const invalidZDQK = this.bingRenZDQKXY
        .concat(this.bingRenZDQKZY)
        .filter((item) => !item.zhenDuanMC)
      if (invalidZDQK.length > 0) {
        errors.push('入院诊断名称未填写')
      }

      // 校验手术数据
      const invalidSSQK = this.patientRecord.bingRenSSQK.filter(
        (item) => !item.shouShuRQ || !item.shouShuMC
      )
      if (invalidSSQK.length > 0) {
        errors.push('手术操作日期或手术操作名称未填写')
      }
      // 统一处理错误
      if (errors.length > 0) {
        this.$message.error(errors.join('；'))
        return false
      }
      return true
    },

    // 住院医生站_查询_病案首页保存或提交
    async handleSaveRecord(type) {
      if (!this.validateAllData()) {
        return
      } else {
        if (this.saveShow == false) {
          this.$message({
            message: '未病区入院不能保存',
            type: 'error'
          })
        } else {
          this.patientRecord.bingRenSSQK.forEach((item) => {
            item.qieKouYHDJ = item.qieKou + '/' + item.yuHe
          })
          try {
            const res = await saveTheFirstPageOfMedicalRecord({
              bingRenJBXX: this.patientRecord.bingRenJBXX,
              bingRenSFMX: this.patientRecord.bingRenSFMX,
              bingRenSSQK: this.patientRecord.bingRenSSQK,
              bingRenYLQK: this.patientRecord.bingRenYLQK,
              bingRenYLQKSZ: this.patientRecord.bingRenYLQKSZ,
              bingRenYLQKZJ: this.patientRecord.bingRenYLQKZJ,
              bingRenZDQK: this.bingRenZDQKXY.concat(this.bingRenZDQKZY),
              shiFouTiJiao: type
            })
            if (res.hasError === 0) {
              this.$message({
                message: type == 0 ? '修改成功' : '提交成功',
                type: 'success'
              })
              this.InitTheFirstPageOfMedicalRecord()
            }
          } catch (error) {
            console.log(error)
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
}
.title {
  font-weight: 600;
  border-left: 3px solid #356ac5;
  padding-left: 8px;
  line-height: 15px;
  text-align: left;
}

.surgical-notice-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;
  .left-aside {
    width: 300px !important;
  }
  .right-aside {
    width: 100% !important;
  }
  .el-main {
    padding: 0 0 0 10px !important;
  }
  .left {
    background-color: #eaf0f9;
    border-radius: 4px;
    padding: 8px;
    height: 100%;
    .left-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 10px;

      ::v-deep .el-button {
        padding: 6px 10px;
        background-color: #3b76ef;
      }
    }
  }
  .right {
    background-color: #eaf0f9;
    border-radius: 4px;
    padding: 8px 12px;
    // padding-top: 0;
    height: 100%;

    /* 固定头部和标题 */
    .fixed-header {
      position: sticky;
      top: 0;
      z-index: 999;
      background-color: #eaf0f9;
      padding: 8px 0;
      border-bottom: 1px solid #e0e0e0;
      height: 24%;
    }

    /* 可滚动区域 */
    .scrollable-content {
      flex: 1;
      overflow-y: auto;
      border-top: none !important;
    }
    .right-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // height: 5%;
      .button-group {
        display: flex;
        align-items: center;
        ::v-deep .el-popover__reference {
          background-color: #eff3fb;
          border: none;
        }
        .el-icon-more {
          transform: rotateZ(90deg);
          color: #356ac5;
          font-size: 18px;
        }
      }
    }
    .title-group {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin: 16px 0;
      .title-describe {
        font-size: 22px;
        font-weight: bold;
        margin-bottom: 14px;
      }
    }
    .right-content {
      font-size: 12px;
      padding: 12px 8px;
      height: 95%;
      border: 1px solid #e0e0e0;
      height: 76%;
      .niShiShouShuList {
        height: 100px;
        border: 1px solid #000;
        background-color: rgba(59, 118, 239, 0.1);
        border-radius: 4px;
        padding: 6px;
        .el-tag {
          border: 1px solid rgba($color: #155bd4, $alpha: 0.45) !important;
          margin: 2px;
        }
      }
      .el-main {
        .right-table {
          text-align: left;
          table-layout: fixed;
          // height: 100%;
          width: 100%;
          td {
            border: 1px solid #dcdfe6;
            padding: 4px 6px;
            // width: 13%;
            overflow: hidden;
            box-sizing: border-box;
          }
          .right-table-title {
            text-align: right;
            background-color: #eff3fb;
            width: 8%;
            height: 100%;
            line-height: 16px;
          }
          .right-table-content {
            background-color: #ffffff;
            // width: 13%;
            height: 40px;
            ::v-deep .el-input__inner {
              width: 100%;
              min-width: 0;
              // background-color: #e4ecfb;
              // height: 24px;
            }
            .el-checkbox {
              margin-right: 8px;
            }
          }
          .right-table-footer-tip {
            padding: 0 0 10px 0;
            background-color: #eaf0f9;
          }
          .select-input {
            ::v-deep .el-input__inner {
              // background-color: #e4ecfb;
              // height: 24px;
            }
          }
        }
      }
      .el-aside {
        margin: 0 0 0 10px !important;
        padding: 10px !important;
        border: 1px solid #dcdfe6;
        .right-aside-header {
          text-align: right;
          ::v-deep .el-button {
            background-color: #a66dd4;
            color: #fff;
          }
        }
        .right-aside-table {
          width: 100%;
          margin-top: 20px;
          td {
            border-collapse: collapse;
            border: 1px solid #dcdfe6;
            padding: 2px 6px;
          }
          thead {
            background-color: #eaf0f9;
          }
          tbody {
            td {
              background-color: #f6f6f6;
            }
          }
        }
      }
    }
  }
}
.no-header .cell {
  display: none;
}
::v-deep .el-input .el-input__inner {
  // padding: 4px;
  width: 100%;
}
::v-deep .zjlx .el-input .el-input__inner {
  // width: 310px;
  text-align: right;
}

.ssjczrq {
  width: 100%;
}

// 常见手术、诊断列表弹框
.surgeries-class {
  ::v-deep .el-dialog {
    padding: 6px 10px;
  }

  ::v-deep .el-table--mini .el-table__cell {
    padding: 12px 0;
  }

  .dialog-footer {
    padding-bottom: 15px;
  }

  ::v-deep .el-dialog__header {
    // border-bottom: 1px solid #dadee6;
    padding: 10px 14px;
    font: var(--font-medium);
    font-size: var(--font-size-medium);
    color: #171c28;
    display: flex;
    align-items: center;
  }

  ::v-deep .el-dialog__header::before {
    // content: url('~@/assets/images/info.png');
    // // width: 3px;
    // // height: 16px;
    // // background: #356ac5;
    // // margin-right: 6px;
    // position: absolute;
    // top: 7px;
    // left: 6px;
    // transform: scale(0.55);
    position: absolute;
    left: 22px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }

  ::v-deep .el-dialog__title {
    font-weight: bold;
    margin-left: 10px;
    opacity: 0.8;
  }

  ::v-deep .el-pager li {
    margin: 0 4px;
  }
}

.cryuan-details {
  position: relative;
  padding: 12px 6px;
  .cryuan-left {
    padding: 12px;
    font-weight: bold;
    margin-right: 4px;
    .el-icon-question {
      font-size: 16px;
      color: #356ac5;
    }
  }
  .cryuan-right {
    position: absolute;
    right: 6px;
    margin-top: -7px;
  }
}
::v-deep .right-content .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding: 3px 0;
}
::v-deep .el-input.is-disabled .el-input__inner {
  background: #fff;
}
::v-deep .el-date-editor--date {
  width: 100%;
}

::v-deep .el-radio__original {
  display: none !important;
}

::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background: #356ac5;
  border-color: #356ac5;
}

// 诊断、中毒、手术
.zxs-class {
  ::v-deep .el-table--mini .el-table__cell {
    padding: 5px 0;
  }
  .flex {
    // flex-direction: column;
    .zxsXMInput {
      margin-bottom: 18px;
      width: 60%;
    }
    .zxsXMInit {
    }
  }
}
</style>
