<template>
  <!-- 入院记录 -->
  <div class="admission-note-container">
    <div class="admission-note-layout">
      <!-- 左侧列表 -->
      <div class="left-panel">
        <div class="left-header">
          <div class="title-area">
            <span>{{ isRiJianSS ? '日间手术病历' : '大病历列表' }}</span>
          </div>
        </div>
        <div class="record-list">
          <el-table
            v-loading="loading"
            :data="yiShuXieWenShuList"
            highlight-current-row
            :height="tableHeight"
            element-loading-text="加载中..."
            border
            stripe
            size="mini"
            @row-click="handleRecordClick"
          >
            <el-table-column prop="jilluSJ" label="记录时间" width="150">
              <template #default="{ row }">
                <span class="record-time">{{ row.jilluSJ }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="wenShuMC" label="文书名称">
              <template #default="{ row }">
                <span class="record-name">{{ row.wenShuMC }}</span>
              </template>
            </el-table-column>
            <el-table-column width="50" align="center">
              <template #default="{ row }">
                <i
                  v-if="isWenShuWritten(row)"
                  class="el-icon-check"
                  style="font-size: 18px; pointer-events: none"
                ></i>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 右侧详情 -->
      <div class="right-panel">
        <div class="right-header">
          <div class="button-group">
            <el-button type="primary" size="mini" :loading="loading" @click="refreshPage">
              刷新本页
            </el-button>
            <el-button
              type="primary"
              size="mini"
              :disabled="loading"
              @click="toggleAllRecords(false)"
            >
              折叠
            </el-button>
            <el-button
              type="primary"
              size="mini"
              :disabled="loading"
              @click="toggleAllRecords(true)"
            >
              展开
            </el-button>
          </div>
        </div>
        <div
          ref="recordDetails"
          v-loading="loading"
          class="record-details"
          element-loading-text="加载中..."
        >
          <div
            v-for="(record, index) in recordDetailList"
            :id="'panel_' + record.id"
            :key="record.id"
            class="record-detail-item"
          >
            <div class="record-header">
              <div class="record-title">
                <span>{{ record.wenShuMC }}</span>
              </div>
              <div class="record-actions">
                <template v-if="record.isEditing">
                  <el-button
                    type="primary"
                    size="mini"
                    class="action-button"
                    @click="handleSaveClick(record.id)"
                  >
                    保存
                  </el-button>
                  <el-button
                    type="primary"
                    size="mini"
                    class="action-button"
                    @click="handleDeleteClick(record.id)"
                  >
                    删除
                  </el-button>
                  <el-button
                    type="primary"
                    size="mini"
                    class="action-button"
                    @click="handlePrintClick(record.id)"
                  >
                    打印
                  </el-button>
                  <el-button
                    type="primary"
                    size="mini"
                    class="action-button"
                    @click="handleEditClick(record.id)"
                  >
                    取消
                  </el-button>
                </template>
                <template v-else>
                  <el-button
                    type="primary"
                    size="mini"
                    class="action-button"
                    @click="handleEditClick(record.id)"
                  >
                    编辑
                  </el-button>
                </template>
                <i
                  :class="record.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                  @click="toggleRecord(index)"
                ></i>
              </div>
            </div>
            <div v-show="record.expanded" class="record-content">
              <iframe
                v-if="record.url"
                :id="'iframe_' + record.id"
                :src="record.url"
                frameborder="0"
                @load="handleIframeLoad($event, record.id)"
              ></iframe>
              <div v-else class="no-content">暂无内容</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getAdmissionNoteInit, insertSingleZuanKeGS } from '@/api/admission-note'
import { mapState } from 'vuex'
import iframeCommunication from '@/utils/iframe-communication'

export default {
  name: 'AdmissionNotes',
  data() {
    return {
      // 已书写文书列表（记录列表）
      yiShuXieWenShuList: [],
      // 应书写文书列表（左侧列表最后）
      yingShuXieWenShuList: [],
      // 详情列表
      recordDetailList: [],
      // 全部展开状态
      allExpanded: true,
      // 表格高度
      tableHeight: '100%',
      // 加载状态
      loading: false,
      // 是否正在新增记录
      isAddingRecord: false,
      // 基础URL
      baseUrl: 'http://************/ehr',
      // 用户选择的记录ID
      userSelectedRecordId: null,
      // 是否有iframe加载完成
      anyIframeLoaded: false
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientInfo: ({ patient }) => patient.patientInit,
      gongZhongDM: ({ patient }) => patient.doctorInfo.gongZhongDM_DZ, // 工种代码对照
      // yongHuID: ({ user }) => '30276', // 用于测试
      yongHuID: ({ user }) => user.yongHuID,
      // 判断是否为日间手术病人
      isRiJianSS: ({ patient }) => patient.patientInit?.riJianSS === '1'
    })
  },
  mounted() {
    // 初始化数据
    this.fetchInitData()
    // 初始化iframe通信
    iframeCommunication.init()
  },

  beforeDestroy() {
    // 销毁iframe通信
    iframeCommunication.destroy()
  },

  methods: {
    // 获取初始化数据
    async fetchInitData() {
      this.loading = true
      try {
        // 获取初始化数据
        const initRes = await getAdmissionNoteInit({
          bingLiID: this.bingLiID
        })

        if (initRes.hasError === 0) {
          const data = initRes.data

          // 先检查是否有格式代码字符串，需要弹出选择框
          if (data && data.geShiDMStr) {
            this.handleTemplateSelection(data.geShiDMStr)
            return // 选择模板后会重新调用fetchInitData，所以这里直接返回
          }

          // 处理应书写文书列表
          if (data && data.yingShuXieWenShuList) {
            this.yingShuXieWenShuList = data.yingShuXieWenShuList
          }

          // 处理已书写文书列表
          if (data && data.yiShuXieWenShuList) {
            this.yiShuXieWenShuList = data.yiShuXieWenShuList.sort((a, b) => {
              return new Date(a.jilluSJ || 0) - new Date(b.jilluSJ || 0)
            })

            const yingShuXieWenShuList = this.yingShuXieWenShuList
              .filter((item) => {
                return !this.yiShuXieWenShuList.some((i) => i.geshiDM === item.geShiDM)
              })
              .map((item) => ({
                ...item,
                wenShuMC: item.mingChen,
                jilluSJ: '',
                wenShuYLURL: ''
              }))

            this.yiShuXieWenShuList = this.yiShuXieWenShuList.concat(yingShuXieWenShuList)

            // 初始化详情列表
            this.recordDetailList = data.yiShuXieWenShuList.map((item) => ({
              ...item,
              expanded: true,
              isEditing: false,
              url: item.wenShuYLURL ? item.wenShuYLURL : this.getRecordEditUrl(item)
            }))
          }
        }
      } catch (error) {
        console.error('初始化失败', error)
        this.$message.error('初始化失败')
      } finally {
        this.loading = false
      }
    },

    // 获取记录编辑URL
    getRecordEditUrl(record) {
      if (!record || !record.id) return ''

      const idIsNumber = typeof record.id === 'number'
      // 构建URL参数
      const params = {
        as_blid: record.bingliID || this.bingLiID,
        as_gsdm: record.geshiDM || record.geShiDM,
        as_zyid: this.patientInfo.zhuYuanID,
        as_yhid: this.yongHuID,
        as_wsid: idIsNumber ? record.id : 0, // 新文书ID为0
        as_wslx: record.wenshuLX || record.wenShuLX,
        as_tmpid: 't1',
        tmpid: Math.random()
      }

      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&')

      // 返回完整URL
      return `${this.baseUrl}/zyblws/blwsdetail.aspx?${queryString}`
    },

    // 处理模板选择
    handleTemplateSelection(geShiDMStr) {
      // 解析格式代码字符串，格式为：创伤:7115^0249^6194^0267^;急诊:0493^9587^0053^0259^6194^0267^
      const templates = []
      const templateGroups = geShiDMStr.split(';')

      templateGroups.forEach((group) => {
        if (!group) return

        const [name, code] = group.split(':')
        if (name && code) {
          templates.push({ name, code })
        }
      })

      // 弹出选择框
      this.$confirm('请选择模板', '选择模板', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info',
        showCancelButton: false,
        center: true,
        customClass: 'template-selection-dialog',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            const selectedTemplate = instance.$el.querySelector('select').value
            if (selectedTemplate) {
              this.setTemplate(selectedTemplate)
                .then(() => {
                  done()
                })
                .catch(() => {
                  done()
                })
            } else {
              this.$message.warning('请选择一个模板')
              return false
            }
          } else {
            done()
          }
        }
      })
        .then(() => {
          // 选择模板后重新获取数据
          this.fetchInitData()
        })
        .catch(() => {
          // 取消选择
        })

      // 添加模板选择下拉框到确认框中
      setTimeout(() => {
        const messageEl = document.querySelector(
          '.template-selection-dialog .el-message-box__message'
        )
        if (messageEl) {
          const selectHtml = `
            <select style="width: 100%; margin-top: 10px; padding: 8px; border: 1px solid #dcdfe6; border-radius: 4px;">
              <option value="">请选择模板</option>
              ${templates.map((t) => `<option value="${t.code}">${t.name}</option>`).join('')}
            </select>
          `
          messageEl.innerHTML += selectHtml
        }
      }, 100)
    },

    // 设置模板
    async setTemplate(templateCode) {
      try {
        const res = await insertSingleZuanKeGS({
          bingLiID: this.bingLiID,
          leiBieDM: '01', // 固定值
          geShiDM: templateCode
        })
        if (res.hasError === 0) {
          this.$message.success('设置模板成功')
          return Promise.resolve()
        } else {
          this.$message.error('设置模板失败')
          return Promise.reject()
        }
      } catch (error) {
        console.error('设置模板失败', error)
        this.$message.error('设置模板失败')
        return Promise.reject()
      }
    },

    // 判断文书是否已经书写
    isWenShuWritten(row) {
      return this.yiShuXieWenShuList.some((item) => item.geshiDM === row.geShiDM)
    },

    // 处理记录点击
    async handleRecordClick(row) {
      // 设置用户选择的记录ID
      this.userSelectedRecordId = row.id

      // 检查是否已经在详情列表中
      const found = this.recordDetailList.some((item) => {
        if (
          item.id === row.id ||
          (item.geShiDM === row.geShiDM && !item.id.toString().startsWith('t'))
        ) {
          item.expanded = true
          return true
        }
        return false
      })

      // 检查是否是临时记录（已添加但未保存）
      const isTemporaryAdded = this.recordDetailList.some((item) => {
        return item.geShiDM === row.geShiDM && item.id.toString().startsWith('t')
      })

      let targetId = row.id

      if (!found && !isTemporaryAdded) {
        // 新增记录，并获取新记录的ID
        targetId = await this.handleAddRecord(row)
      } else if (!found && isTemporaryAdded) {
        // 如果是临时记录，找到对应的临时记录ID
        const tempRecord = this.recordDetailList.find(
          (item) => item.geShiDM === row.geShiDM && item.id.toString().startsWith('t')
        )
        if (tempRecord) {
          targetId = tempRecord.id
        }
      }

      // 滚动到对应的详情（延迟执行，确保DOM已更新）
      this.$nextTick(() => {
        const detailElement = document.getElementById(`panel_${targetId}`)
        if (detailElement) {
          // 使用setTimeout确保DOM已完全渲染
          setTimeout(() => {
            this.$refs.recordDetails.scrollTop =
              detailElement.offsetTop - this.$refs.recordDetails.offsetTop
          }, 100)
        }
      })
    },

    // 处理新增记录
    async handleAddRecord(selectedWenShu) {
      try {
        this.isAddingRecord = true

        // 创建新记录对象
        const newRecord = {
          id: 't' + new Date().getTime(),
          wenShuMC: selectedWenShu.wenShuMC,
          geShiDM: selectedWenShu.geShiDM,
          wenShuLX: selectedWenShu.wenShuLX,
          expanded: true,
          isEditing: true // 新增记录默认为编辑状态
        }

        // 设置URL
        newRecord.url = this.getRecordEditUrl(newRecord)

        // 添加到详情列表
        this.recordDetailList.push(newRecord)

        // 返回新记录的ID
        return newRecord.id
      } catch (error) {
        this.$message.error(error.message || '未知错误')
        return null
      } finally {
        this.isAddingRecord = false
      }
    },

    // 切换单个记录的展开状态
    toggleRecord(index) {
      this.recordDetailList[index].expanded = !this.recordDetailList[index].expanded
    },

    // 切换所有记录的展开状态
    toggleAllRecords(expanded) {
      this.allExpanded = expanded
      this.recordDetailList.forEach((item) => {
        item.expanded = this.allExpanded
      })
    },

    // 刷新页面
    refreshPage() {
      // 保存当前展开状态
      const expandedStates = {}
      this.recordDetailList.forEach((record) => {
        expandedStates[record.id] = record.expanded
      })

      // 重新获取数据
      this.fetchInitData().then(() => {
        // 恢复展开状态
        this.recordDetailList.forEach((record) => {
          if (expandedStates[record.id] !== undefined) {
            record.expanded = expandedStates[record.id]
          }
        })
      })
    },

    // 处理编辑按钮点击
    handleEditClick(recordId) {
      // 检查病历状态，如果已封存则不允许编辑
      if (this.patientInfo.bingLiZT === '1') {
        this.$message({
          type: 'warning',
          message: '已封存病历不能进行该操作!'
        })
        return
      }

      // 查找对应的记录
      const record = this.recordDetailList.find((item) => item.id === recordId)
      if (!record) {
        this.$message.error('未找到对应的记录')
        return
      }

      // 切换编辑状态
      if (record.isEditing) {
        record.isEditing = false
        record.url = record.wenShuYLURL
      } else {
        record.isEditing = true
        record.url = this.getRecordEditUrl(record)
      }
    },

    // 刷新已书写文书列表
    async refreshYiShuXieWenShuList() {
      try {
        // 获取初始化数据
        const initRes = await getAdmissionNoteInit({
          bingLiID: this.bingLiID
        })

        if (initRes.hasError === 0) {
          const data = initRes.data

          // 处理应书写文书列表（左侧列表）
          if (data && data.yingShuXieWenShuList) {
            this.yingShuXieWenShuList = data.yingShuXieWenShuList
          }

          // 更新已书写文书列表
          if (data && data.yiShuXieWenShuList) {
            this.yiShuXieWenShuList = data.yiShuXieWenShuList.sort((a, b) => {
              return new Date(a.jilluSJ || 0) - new Date(b.jilluSJ || 0)
            })

            const yingShuXieWenShuList = this.yingShuXieWenShuList
              .filter((item) => {
                return !this.yiShuXieWenShuList.some((i) => i.geshiDM === item.geShiDM)
              })
              .map((item) => ({
                ...item,
                wenShuMC: item.mingChen,
                jilluSJ: '',
                wenShuYLURL: ''
              }))

            this.yiShuXieWenShuList = this.yiShuXieWenShuList.concat(yingShuXieWenShuList)

            // 查找当前正在编辑的记录
            let currentEditingRecord = null

            this.recordDetailList.forEach((record) => {
              // 保存当前正在编辑的记录
              if (record.isEditing) {
                currentEditingRecord = record
              }
            })

            // 重新构建详情列表
            this.recordDetailList = data.yiShuXieWenShuList.map((item) => {
              // 检查是否为当前正在编辑的记录
              const isCurrentEditing = currentEditingRecord && currentEditingRecord.id === item.id

              // 根据编辑状态构建正确的URL
              let url
              if (isCurrentEditing) {
                url = this.getRecordEditUrl(item)
              } else {
                url = item.wenShuYLURL
              }

              return {
                ...item,
                expanded: true, // 默认展开
                isEditing: isCurrentEditing,
                url
              }
            })
          }
        }
      } catch (error) {
        console.error('刷新已书写文书列表失败', error)
        this.$message.error('刷新已书写文书列表失败')
      }
    },

    // 处理打印按钮点击
    async handlePrintClick(recordId) {
      // 获取iframe元素
      const iframe = document.getElementById(`iframe_${recordId}`)
      if (!iframe) {
        this.$message.error('iframe未加载完成')
        return
      }

      try {
        // 使用简化的iframe通信
        const message = await iframeCommunication.print(recordId, iframe)
        this.$message.success(message)
      } catch (error) {
        console.error('打印失败', error)
        this.$message.error('打印失败: ' + (error.message || '未知错误'))
      }
    },

    // 处理iframe加载完成
    handleIframeLoad(_, recordId) {
      console.log('iframe加载完成', recordId)

      // 在iframe加载完成后，滚动到对应的面板
      this.$nextTick(() => {
        // 只有当这是第一个加载的iframe或用户已经点击了某条记录时才滚动
        if (
          this.userSelectedRecordId === recordId ||
          (!this.anyIframeLoaded && recordId === this.recordDetailList[0]?.id)
        ) {
          const detailElement = document.getElementById(`panel_${recordId}`)
          if (detailElement) {
            // 使用setTimeout确保DOM已完全渲染
            setTimeout(() => {
              this.$refs.recordDetails.scrollTop =
                detailElement.offsetTop - this.$refs.recordDetails.offsetTop
            }, 100)
          }

          // 标记已有iframe加载完成
          this.anyIframeLoaded = true
        }
      })
    },

    // 处理保存按钮点击
    async handleSaveClick(recordId) {
      // 查找对应的记录
      const record = this.recordDetailList.find((item) => item.id === recordId)
      if (!record) {
        this.$message.error('未找到对应的记录')
        return
      }

      // 获取iframe元素
      const iframe = document.getElementById(`iframe_${recordId}`)
      if (!iframe) {
        this.$message.error('iframe未加载完成')
        return
      }

      try {
        // 使用简化的iframe通信
        const message = await iframeCommunication.save(recordId, iframe)
        this.$message.success(message)

        // 保存成功后的处理
        // 1. 调用刷新方法重新获取文书列表数据
        // 2. 保持isEditing状态为true，不自动退出编辑状态
        setTimeout(async () => {
          await this.refreshYiShuXieWenShuList()

          // 确保当前记录保持编辑状态
          const updatedRecord = this.recordDetailList.find((item) => item.id === recordId)
          if (updatedRecord) {
            updatedRecord.isEditing = true
            // 更新URL为编辑URL，确保显示最新保存的内容
            updatedRecord.url = this.getRecordEditUrl(updatedRecord)
          }
        }, 1000)
      } catch (error) {
        console.error('保存失败', error)
        this.$message.error('保存失败: ' + (error.message || '未知错误'))
      }
    },

    // 处理删除按钮点击
    handleDeleteClick(recordId) {
      this.$confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // 查找对应的记录
          const record = this.recordDetailList.find((item) => item.id === recordId)
          if (!record) {
            this.$message.error('未找到对应的记录')
            return
          }

          // 获取iframe元素
          const iframe = document.getElementById(`iframe_${recordId}`)
          if (!iframe) {
            this.$message.error('iframe未加载完成')
            return
          }

          try {
            // 使用简化的iframe通信
            const message = await iframeCommunication.delete(recordId, iframe)
            this.$message.success(message)

            // 删除成功后的处理
            const isTemporaryRecord = recordId.toString().startsWith('t')
            if (isTemporaryRecord) {
              // 临时记录直接从列表中移除
              const index = this.recordDetailList.findIndex((item) => item.id === recordId)
              if (index !== -1) {
                this.recordDetailList.splice(index, 1)
              }
            } else {
              // 已保存记录重新获取列表
              setTimeout(async () => {
                await this.refreshYiShuXieWenShuList()
              }, 1000)
            }
          } catch (error) {
            console.error('删除失败', error)
            this.$message.error('删除失败: ' + (error.message || '未知错误'))
          }
        })
        .catch(() => {
          // 取消删除，不做任何操作
        })
    }
  }
}
</script>

<style scoped lang="scss">
.admission-note-container {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #f6f6f6;
  position: relative;
}

.admission-note-layout {
  display: flex;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.left-panel {
  width: 380px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #eff3fb;
  border-radius: 4px 0 0 4px;
}

.left-header {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #eff3fb;
}

.title-area {
  display: flex;
  align-items: center;

  span {
    font-weight: bold;
    margin-right: 10px;
    display: inline-block;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    vertical-align: middle;
  }
}

.record-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;

  ::v-deep .el-table {
    .record-name {
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .record-time {
      font-size: 14px;
    }
  }
}

.right-panel {
  flex: 1;
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  background-color: #eff3fb;
  padding: 10px;
  border-radius: 4px;
}

.right-header {
  padding: 0 12px;
  height: 58px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #eff3fb;
  display: flex;
  align-items: center;
}

.button-group {
  display: flex;
}

.record-details {
  flex: 1;
  overflow-y: auto;
  padding-top: 10px;
  background-color: #eff3fb;
}

.record-detail-item {
  margin-bottom: 10px;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 50px;
  background-color: #eaf0f9;
  padding: 8px 12px;
  border-bottom: 1px solid #dcdfe6;
  transition: background-color 0.3s;

  &:hover {
    background-color: #ecf5ff;
  }
}

.record-title {
  display: flex;
  align-items: center;
  padding-left: 8px;
  border-left: 4px solid #356ac5;
  cursor: pointer;
  flex: 1;

  span {
    font-weight: bold;
    color: #303133;
    font-size: 15px;
  }
}

.record-actions {
  display: flex;
  align-items: center;
  gap: 5px;
  margin-right: 10px;

  i {
    cursor: pointer;
    font-size: 18px;
    color: #606266;
    margin-left: 5px;
  }

  .action-button {
    margin-left: 0;
    margin-right: 0;
    padding: 5px 10px;
  }
}

.record-content {
  padding: 0;
  background-color: #fff;
  transition: height 0.3s;

  iframe {
    width: 100%;
    height: 540px;
    border: none;
    display: block;
  }

  .no-content {
    padding: 30px;
    text-align: center;
    color: #909399;
    font-size: 14px;
    background-color: #fafafa;
  }
}
</style>
