<template>
  <!--病程记录-->
  <div class="progress-note-container">
    <secondary-password
      :visible.sync="qmPhotoType.visible"
      :leibie="qmPhotoType.leiBie"
      :tupianlx="qmPhotoType.tuPianLX"
      :getbase64="qmPhotoType.getBase64"
      :qmtype="qmPhotoType.qmtype"
      @confirm="SaveSecPass"
      @cancel="cancelDialog"
    ></secondary-password>
    <!-- 评分表弹窗 -->
    <default-dialog
      :title="scoreFormDialog.title"
      :visible.sync="scoreFormDialog.visible"
      :width="scoreFormDialog.width"
      pop-type="custom"
      append-to-body
      destroy-on-close
      @cancel="closeScoreFormDialog"
    >
      <div :style="{ height: scoreFormDialog.height, position: 'relative' }">
        <div v-if="scoreFormDialog.loading" class="score-form-loading">
          <div class="loading-spinner">
            <i class="el-icon-loading"></i>
            <p>加载中...</p>
          </div>
        </div>
        <iframe
          v-if="scoreFormDialog.url"
          :src="scoreFormDialog.url"
          frameborder="0"
          style="width: 100%; height: 100%; margin: 0 auto"
          @load="handleScoreFormIframeLoad"
        ></iframe>
      </div>
    </default-dialog>
    <default-dialog
      :title="exportMbDialog.title"
      :visible.sync="exportMbDialog.visible"
      :width="exportMbDialog.width"
      pop-type="custom"
      append-to-body
      destroy-on-close
      @cancel="closeMbExportDialog"
    >
      <div style="height: 400px; overflow-y: auto">
        <el-button type="primary" @click="exportData()">确 定</el-button>
        <el-table
          :data="exportTableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="baoGaoSJ" label="报告时间" width="180"></el-table-column>
          <el-table-column prop="jianChaXM" label="检查项目" width="180"></el-table-column>
          <el-table-column prop="jianChaJG" label="检查结果" width="180"></el-table-column>
        </el-table>
      </div>
    </default-dialog>
    <!-- 模板-->
    <default-dialog
      :title="exportMbDialog.title"
      :visible.sync="exportMbDialog.visible"
      :width="exportMbDialog.width"
      pop-type="custom"
      append-to-body
      destroy-on-close
      @cancel="closeExportDialog"
    >
      <div style="height: 400px; overflow-y: auto">
        <el-row>
          <el-col :span="8">
            <el-select v-model="moBanLX" placeholder="请选择">
              <el-option
                v-for="item in moBanLXOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-table :data="exportTableData" @selection-change="handleSelectionChange">
              <el-table-column prop="baoGaoSJ" label="模板名称" width="180"></el-table-column>
            </el-table>
          </el-col>
          <el-col :span="16">
            <div>
              <el-button type="primary" @click="addMbData()">新增</el-button>
              <el-input v-model="moBanMC" class="renYuanXMInput"></el-input>
              <el-button type="primary" @click="saveMbData()">保存</el-button>
              <el-input
                v-model="moBanMCArea"
                type="textarea"
                :autosize="{ minRows: 2, maxRows: 10 }"
                placeholder="请输入内容"
              ></el-input>
            </div>
          </el-col>
        </el-row>
      </div>
    </default-dialog>
    <!--医生选择-->
    <el-dialog width="30%" class="Dialog" :visible.sync="exportYYDialog">
      <div slot="title" class="title">人员选择</div>
      <div class="flex">
        <el-input
          v-model="renYuanXM"
          class="renYuanXMInput"
          placeholder="请输入人员终身码或拼音/五笔码"
          @change="queryZhuDaoYS"
        ></el-input>
        <el-checkbox v-model="benZhuanKe">本专科</el-checkbox>
      </div>
      <table class="renYuanList">
        <thead>
          <tr>
            <td>姓名</td>
            <td>终身码</td>
            <td>所在专科</td>
            <td>人员类别</td>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="item in exportYYTableData"
            :key="item.yongHuID"
            @dblclick="handleSelectRY(item)"
          >
            <td>{{ item.xingMing }}</td>
            <td>{{ item.zhongShenDM }}</td>
            <td>{{ item.xianZhuanKeMC }}</td>
            <td>{{ item.renYuanLBMC }}</td>
          </tr>
        </tbody>
      </table>
      <el-pagination
        class="pagination"
        small
        background
        :current-page.sync="currentPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="exportYYTableData.length"
      ></el-pagination>
      <div class="flex bottom-btn">
        <el-button size="mini" @click="exportYYDialog = false">关闭</el-button>
        <el-button size="mini" type="primary">确认</el-button>
      </div>
    </el-dialog>
    <!-- 左右布局 -->
    <div class="progress-note-layout">
      <!-- 左侧列表 -->
      <div class="left-panel">
        <div class="left-header">
          <div class="title-area">
            <span>病程记录列表</span>
            <el-select
              v-model="selectedRecordType"
              placeholder="请选择"
              size="mini"
              class="record-type-select"
            >
              <el-option
                v-for="item in keShuXieWenShuList"
                :key="item.geShiDM"
                :label="item.wenShuMC"
                :value="item.geShiDM"
              ></el-option>
            </el-select>
            <el-button
              type="primary"
              size="mini"
              :loading="addingRecord"
              :disabled="addingRecord"
              @click="handleAddRecord"
            >
              新增
            </el-button>
          </div>
        </div>
        <div class="record-list">
          <el-table
            v-loading="loading"
            :data="yiShuXieWenShuList"
            highlight-current-row
            :height="tableHeight"
            element-loading-text="加载中..."
            border
            stripe
            size="mini"
            @row-click="handleRecordClick"
          >
            <el-table-column prop="jilluSJ" label="记录时间" width="150"></el-table-column>
            <el-table-column prop="wenShuMC" label="记录名称">
              <template #default="{ row }">
                <span class="record-name">{{ row.wenShuMC }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- 右侧详情 -->
      <div class="right-panel">
        <div class="right-header">
          <div class="button-group">
            <el-button type="primary" size="mini" :loading="loading" @click="refreshPage">
              刷新本页
            </el-button>
            <el-button
              type="primary"
              size="mini"
              :disabled="loading"
              @click="toggleAllRecords(false)"
            >
              折叠
            </el-button>
            <el-button
              type="primary"
              size="mini"
              :disabled="loading"
              @click="toggleAllRecords(true)"
            >
              展开
            </el-button>
          </div>
        </div>
        <div
          ref="recordDetails"
          v-loading="loading"
          class="record-details"
          element-loading-text="加载中..."
        >
          <!-- 危急值记录提醒 -->
          <div v-if="weiJiZhiList.length > 0" class="record-detail-item">
            <div class="record-header" @click="toggleWeiJiZhi">
              <div class="record-title">
                <span>危急值记录提醒</span>
              </div>
              <i :class="weiJiZhiExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
            </div>
            <div v-show="weiJiZhiExpanded" class="record-content">
              <div v-loading="weiJiZhiLoading">
                <el-table :data="weiJiZhiList" border stripe size="mini">
                  <el-table-column label="检查时间" width="150">
                    <template #default="scope">{{ scope.row.jianChaSJ }}</template>
                  </el-table-column>
                  <el-table-column label="检查项目">
                    <template #default="scope">{{ scope.row.jianChaXM }}</template>
                  </el-table-column>
                  <el-table-column label="检查结果">
                    <template #default="scope">
                      <span
                        :style="{
                          color:
                            scope.row.jianChaJG && scope.row.jianChaJG.includes('危') ? 'red' : ''
                        }"
                      >
                        {{ scope.row.jianChaJG }}
                      </span>
                    </template>
                  </el-table-column>
                  <el-table-column label="检查备注">
                    <template #default="scope">{{ scope.row.jianChaBZ }}</template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
          <!-- 其余列表 -->
          <div
            v-for="(record, index) in recordDetailList"
            :id="'panel_' + record.id"
            :key="record.id"
            class="record-detail-item"
          >
            <div class="record-header">
              <div class="record-title" @click="toggleRecord(index)">
                <span>{{ record.wenShuMC }}</span>
              </div>
              <div class="record-actions">
                <template v-if="record.isEditing">
                  <el-button
                    type="success"
                    size="mini"
                    class="action-button save-button"
                    @click="handleSaveClick(record.id)"
                  >
                    保存
                  </el-button>
                  <el-button
                    type="danger"
                    size="mini"
                    class="action-button delete-button"
                    @click="handleDeleteClick(record.id)"
                  >
                    删除
                  </el-button>
                  <el-button
                    type="info"
                    size="mini"
                    class="action-button print-button"
                    @click="handlePrintClick(record.id)"
                  >
                    打印
                  </el-button>
                </template>
                <el-button
                  type="primary"
                  size="mini"
                  class="action-button edit-button"
                  @click="handleEditClick(record.id)"
                >
                  {{ record.isEditing ? '取消' : '编辑' }}
                </el-button>
                <i
                  :class="record.expanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"
                  @click="toggleRecord(index)"
                ></i>
              </div>
            </div>
            <div v-show="record.expanded" class="record-content">
              <el-row v-if="record.editor === '4'">
                <el-col :span="20">
                  1
                  <iframe
                    :id="'iframe_' + record.id"
                    v-amount="isEditShow"
                    frameborder="0"
                    width="100%"
                    height="100%"
                  ></iframe>
                </el-col>
                <el-col v-if="record.isEditing" :span="4">
                  <el-button v-if="record" @click="_export('weijizhi', record.id)">
                    危急值
                  </el-button>
                  <el-button @click="_export('moban', record.id)">模板</el-button>
                  <el-button @click="_export('YYXZ', record.id)">选择上级医师</el-button>
                  <el-button @click="_export('YYXZ', record.id)">选择跟随查房医师</el-button>
                </el-col>
              </el-row>
              <iframe
                v-else-if="record.url"
                :id="'iframe_' + record.id"
                :src="record.url"
                frameborder="0"
                @load="handleIframeLoad($event, record.id)"
              ></iframe>
              <div v-else class="no-content">暂无内容</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { FcApi } from '@/api/fcForm/fcApi.js'
const fcApi = new FcApi()
import {
  getProgressNoteInit,
  getCriticalValueByBingLiID,
  getGeShiDMFromRedis,
  checkVTEStatus,
  checkICUStatus,
  checkFJZT,
  checkRankinStatus,
  checkPHQStatus,
  checkNIHSSStatus,
  checkPregnancyVTEStatus,
  getWeiDaoRuCriticalValueByBingLiID,
  initZhuYuanBingLiMB,
  getMedicalStaff
} from '@/api/progress-note'
import { getShouShuRyk } from '@/api/surgical-notice'
import {
  getByGeShiDM,
  deleteWenShuJLNewEditor,
  savePFBNewEditor,
  saveWenShuJiluNewEditor
} from '@/api/fc-editor'
import { mapState } from 'vuex'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import SecondaryPassword from '@/components/SecondaryPassword/index.vue'
import iframeCommunication from '@/utils/iframe-communication'

export default {
  name: 'ProgressNotes',
  components: {
    DefaultDialog,
    SecondaryPassword
  },
  directives: {
    amount: {
      inserted(_, binding, vnode) {
        if (_.attributes['id']) {
          const txt = _.attributes['id'].value
          const id = txt.split('_') || []
          binding.value(_, id[1])
        }
      }
    }
  },
  data() {
    return {
      fcApiLists: {},
      // 记录类型选择
      selectedRecordType: '',
      // 可书写文书列表（下拉框选项）
      keShuXieWenShuList: [],
      // 已书写文书列表（记录列表）
      yiShuXieWenShuList: [],
      // 危机值展开状态
      weiJiZhiExpanded: true,
      // 危急值列表数据
      weiJiZhiList: [],
      // 危急值加载状态
      weiJiZhiLoading: false,
      // 详情列表
      recordDetailList: [],
      // 全部展开状态
      allExpanded: true,
      // 表格高度
      tableHeight: '100%',
      // 加载状态
      loading: false,
      // 基础URL
      baseUrl: 'http://10.41.220.39/ehr',
      // 添加记录状态
      addingRecord: false,
      // 评分表弹窗
      scoreFormDialog: {
        visible: false,
        title: '',
        url: '',
        loading: true,
        width: '800px',
        height: '80vh'
      },
      // 导入数据弹窗
      exportDialog: {
        visible: false,
        title: '住院病历模板',
        width: '800px',
        height: '80vh'
      },
      // 模板导入数据弹窗
      exportMbDialog: {
        visible: false,
        title: '',
        width: '800px',
        height: '80vh'
      },
      exportTableData: [],
      moBanLX: 1,
      moBanLXOptions: [
        {
          label: '专科',
          value: 1
        },
        {
          label: '个人',
          value: 2
        },
        {
          label: '全院',
          value: 3
        }
      ],
      moBanMC: '',
      moBanMCArea: '',
      //
      exportYYTableData: [],
      renYuanXM: '',
      exportYYDialog: false,
      currentPage: 1,
      benZhuanKe: true,
      pageSize: 13,
      //二级签名
      qmPhotoType: {
        visible: false, //必传
        leiBie: '1', //可传
        tuPianLX: '1', //可传
        getBase64: true, //可传
        qmtype: true //可传
      },
      FcEditorRule: {},
      FcEditorOption: {},
      FcEditorValue: {},
      CurrentLocation: {},
      qianMingLists: {}
    }
  },
  computed: {
    bingLiID() {
      // return '2618289'
      return this.$route.params.id
    },
    ...mapState({
      // zhuanKeID: () => '42',
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientInfo: ({ patient }) => patient.patientInit,
      gongZhongDM: ({ patient }) => patient.doctorInfo.gongZhongDM_DZ, // 工种代码对照
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  mounted() {
    // 初始化数据
    this.fetchInitData()
    // 初始化iframe通信
    iframeCommunication.init()
  },

  beforeDestroy() {
    // 销毁iframe通信
    iframeCommunication.destroy()
    // fcApi.off('emitSelection')
    // fcApi.off('signatureRequest')
  },
  methods: {
    // 获取初始化数据
    async fetchInitData() {
      this.loading = true
      try {
        // 获取初始化数据
        const initRes = await getProgressNoteInit({
          bingLiID: this.bingLiID,
          zhuanKeID: this.zhuanKeID
        })

        if (initRes.hasError === 0) {
          const data = initRes.data

          // 处理可书写文书列表（下拉框选项）
          if (data && data.keShuXieWenShuList) {
            this.keShuXieWenShuList = data.keShuXieWenShuList

            // 默认选择第一个选项
            if (this.keShuXieWenShuList.length > 0) {
              this.selectedRecordType = this.keShuXieWenShuList[0].geShiDM
            }
          }

          // 处理已书写文书列表（记录列表）
          if (data && data.yiShuXieWenShuList) {
            this.yiShuXieWenShuList = data.yiShuXieWenShuList.sort((a, b) => {
              return new Date(a.jilluSJ || 0) - new Date(b.jilluSJ || 0)
            })

            // 初始化详情列表
            this.recordDetailList = this.yiShuXieWenShuList.map((item) => ({
              ...item,
              expanded: true,
              isEditing: false,
              url: item.wenShuYLURL
            }))
          }
        }
      } catch (error) {
        console.error('初始化失败', error)
        this.$message.error('初始化失败')
      } finally {
        this.loading = false
      }

      // 获取危急值列表
      await this.fetchCriticalValueList()
    },

    // 获取危急值列表
    async fetchCriticalValueList() {
      this.weiJiZhiLoading = true
      try {
        const res = await getCriticalValueByBingLiID({
          bingLiID: this.bingLiID
        })

        if (res.hasError === 0) {
          this.weiJiZhiList = res.data || []
        }
      } catch (error) {
        console.error('获取危急值列表失败', error)
      } finally {
        this.weiJiZhiLoading = false
      }
    },

    // 处理记录点击
    async handleRecordClick(row) {
      // 滚动到对应的详情
      if (row.editor === '4') {
        const value = row.wenShuNR || '{}'
        // await this.getGeShiDaiMa(row)
        if (this.fcApiLists[row.id]) {
          const api = new FcApi()
          this.fcApiLists[row.id] = api
        }
        await this.handleFcIframeLoad(row)
        await fcApi.updateForm({ value: JSON.parse(value) })
        fcApi.setMode('print')
      }
      const detailElement = document.getElementById(`panel_${row.id}`)
      if (detailElement) {
        this.$refs.recordDetails.scrollTop =
          detailElement.offsetTop - this.$refs.recordDetails.offsetTop
      }
    },
    // 切换危急值列表展开
    toggleWeiJiZhi() {
      this.weiJiZhiExpanded = !this.weiJiZhiExpanded
    },

    // 切换单个记录的展开状态
    toggleRecord(index) {
      this.recordDetailList[index].expanded = !this.recordDetailList[index].expanded
    },

    // 切换所有记录的展开状态
    toggleAllRecords(expanded) {
      this.allExpanded = expanded
      this.recordDetailList.forEach((item) => {
        item.expanded = this.allExpanded
      })
    },

    // 刷新页面
    refreshPage() {
      // 保存当前展开状态
      const expandedStates = {}
      this.recordDetailList.forEach((record) => {
        expandedStates[record.id] = record.expanded
      })

      // 重新获取数据
      this.fetchInitData().then(() => {
        // 恢复展开状态
        this.recordDetailList.forEach((record) => {
          if (expandedStates[record.id] !== undefined) {
            record.expanded = expandedStates[record.id]
          }
        })
      })
    },

    // 处理新增记录
    async handleAddRecord() {
      if (!this.selectedRecordType) {
        this.$confirm('请选择记录类型', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning'
        })
        return
      }

      // 获取选中的文书类型
      const selectedWenShu = this.keShuXieWenShuList.find(
        (item) => item.geShiDM === this.selectedRecordType
      )

      if (!selectedWenShu) {
        this.$confirm('未找到选中的文书类型', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false,
          type: 'warning'
        })
        return
      }

      try {
        this.loading = true
        this.addingRecord = true
        console.log(this.patientInfo)
        // 1. 检查用户是否有权限书写文书
        if (this.gongZhongDM === '0000') {
          await this.$confirm('非医生账号无法书写文书，请联系医务处或信息处进行确认修改', '提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'warning'
          })
          return
        }

        // 1.1 检查文书是否可以多份
        const geShiInfoRes = await getGeShiDMFromRedis({
          daiMa: selectedWenShu.geShiDM
        })

        if (geShiInfoRes.hasError !== 0) {
          this.$message.error(geShiInfoRes.errorMsg || '获取文书格式信息失败')
          return
        }

        // 如果返回值中的duoFenBZ为0，表示该文书只能有一份
        if (geShiInfoRes.data && geShiInfoRes.data.duoFenBZ === '0') {
          await this.$confirm('该记录一份病历中只允许存在一份，如页面上未显示，请刷新！', '提示', {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'warning'
          })
          return
        }

        // 1.2 检查病历状态
        if (this.patientInfo.bingLiZT === '1') {
          // 病历已封存，只有特定类型的文书可以添加
          const specialTypes = ['1Z', '1I'] // 辅检记录和危急值记录

          // 如果不是特殊类型，则不允许添加
          if (!specialTypes.includes(selectedWenShu.wenShuLX)) {
            this.$message({
              type: 'warning',
              title: '提示信息',
              message: '已封存病历不能进行该操作!'
            })
            return
          }

          // 对于辅检记录(1Z)，需要检查辅助状态
          if (selectedWenShu.wenShuLX === '1Z') {
            const fjztRes = await checkFJZT({
              bingLiID: this.bingLiID,
              wenShuLX: selectedWenShu.wenShuLX
            })

            if (fjztRes.hasError !== 0) {
              this.$message.error(fjztRes.errorMsg || '检查辅检状态失败')
              return
            }

            if (fjztRes.data.status === '1') {
              this.$confirm(fjztRes.data.message, '提示', {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              })
              return
            }
          }
        }

        // 2. 根据文书类型进行特殊检查

        // 2.1 首次病程记录(11)的特殊检查
        if (selectedWenShu.wenShuLX === '11') {
          // 2.1.1 检查NIHSS评分情况
          const nihssRes = await checkNIHSSStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (nihssRes.hasError !== 0) {
            this.$message.error(nihssRes.errorMsg || '检查NIHSS评分状态失败')
            return
          }

          if (nihssRes.data.status === '1') {
            await this.$confirm(
              nihssRes.data.message ||
                '完成美国国立卫生院神经功能缺损评分表(NIHSS)评分后才可书写首次病程记录',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开NIHSS评分表页面
              this.openScoreForm('NIHSS评分表', 17)
            })
            return
          }

          // 2.1.2 检查mRS评分情况
          const mrsRes = await checkRankinStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (mrsRes.hasError !== 0) {
            this.$message.error(mrsRes.errorMsg || '检查mRS评分状态失败')
            return
          }

          if (mrsRes.data.status === '1') {
            await this.$confirm(
              mrsRes.data.message || '完成改良Rankin量表(mRS)评分后才可书写首次病程记录',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开mRS评分表页面
              this.openScoreForm('改良Rankin量表(mRS)评分', 72)
            })
            return
          }

          // 2.1.3 检查PHQ量表状态
          const phqRes = await checkPHQStatus({
            bingLiID: this.bingLiID
          })

          if (phqRes.hasError !== 0) {
            this.$message.error(phqRes.errorMsg || '检查PHQ量表状态失败')
            return
          }

          if (phqRes.data.status === '1') {
            let message = phqRes.data.message || '完成PHQ-4量表后才可书写首次病程记录'

            await this.$confirm(message, '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            }).then(() => {
              // 打开PHQ量表页面（使用不同的URL格式）
              const pfid = message.includes('PHQ-9') ? 274 : 148
              const title = message.includes('PHQ-9') ? 'PHQ-9量表' : 'PHQ-4量表'

              // PHQ量表使用pfbym.aspx而不是pfblist.aspx
              this.scoreFormDialog.title = title
              this.scoreFormDialog.url = `${this.baseUrl}/pfbgl/pfbym.aspx?as_blid=${this.bingLiID}&as_pfid=${pfid}&as_pfzhid=0`
              this.scoreFormDialog.loading = true
              this.scoreFormDialog.visible = true
            })
            return
          }

          // 2.1.4 检查产科VTE状态（如果年龄大于14岁）
          const nianLing = Number(this.patientInfo.nianLing.split('岁')[0])
          if (nianLing > 14) {
            const ckVteRes = await checkPregnancyVTEStatus({
              bingLiID: this.bingLiID,
              yongHuID: this.yongHuID
            })

            if (ckVteRes.hasError !== 0) {
              this.$message.error(ckVteRes.errorMsg || '检查产科VTE状态失败')
              return
            }

            if (ckVteRes.data.status === '1') {
              await this.$confirm(
                ckVteRes.data.message ||
                  '完成妊娠期及产褥期静脉血栓血塞证(VTE)的危险因素评分后才可书写首次病程记录',
                '提示',
                {
                  confirmButtonText: '确定',
                  showCancelButton: false,
                  type: 'warning'
                }
              ).then(() => {
                // 打开VTE评分表页面
                this.openScoreForm('妊娠期及产褥期VTE评分', 71)
              })
              return
            }
          }

          // 2.1.5 检查VTE状态
          const vteRes = await checkVTEStatus({
            bingLiID: this.bingLiID,
            yongHuID: this.yongHuID
          })

          if (vteRes.hasError !== 0) {
            this.$message.error(vteRes.errorMsg || '检查VTE状态失败')
            return
          }

          if (vteRes.data.status === '1') {
            await this.$confirm(
              vteRes.data.message || '患者需要填写VTE评估表，请先完成评估',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开VTE评分表页面
              this.openScoreForm('VTE评估表', 71)
            })
            return
          }
        }

        // 2.2 上级医师查房记录(13)的特殊检查
        else if (selectedWenShu.wenShuLX === '13') {
          // 检查NIHSS评分情况
          const nihssRes = await checkNIHSSStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (nihssRes.hasError !== 0) {
            this.$message.error(nihssRes.errorMsg || '检查NIHSS评分状态失败')
            return
          }

          if (nihssRes.data.status === '1') {
            await this.$confirm(
              nihssRes.data.message ||
                '该患者住院第五天需要完成美国国立卫生院神经功能缺损评分表(NIHSS)评分',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开NIHSS评分表页面
              this.openScoreForm('NIHSS评分表', 17)
            })
            return
          }
        }

        // 2.3 出院记录(1E)的特殊检查
        else if (selectedWenShu.wenShuLX === '1E') {
          // 检查mRS评分情况
          const mrsRes = await checkRankinStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (mrsRes.hasError !== 0) {
            this.$message.error(mrsRes.errorMsg || '检查mRS评分状态失败')
            return
          }

          if (mrsRes.data.status === '1') {
            await this.$confirm(
              mrsRes.data.message || '完成改良Rankin量表(mRS)评分后才可书写出院记录',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开mRS评分表页面
              this.openScoreForm('改良Rankin量表(mRS)评分', 72)
            })
            return
          }
        }

        // 2.4 其他文书类型的VTE检查
        else if (selectedWenShu.wenShuLX === '15') {
          // 检查VTE状态
          const vteRes = await checkVTEStatus({
            bingLiID: this.bingLiID,
            yongHuID: this.yongHuID
          })

          if (vteRes.hasError !== 0) {
            this.$message.error(vteRes.errorMsg || '检查VTE状态失败')
            return
          }

          if (vteRes.data.status === '1') {
            await this.$confirm(
              vteRes.data.message || '患者需要填写VTE评估表，请先完成评估',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开VTE评分表页面
              this.openScoreForm('VTE评估表', 71)
            })
            return
          }
          // 检查ICU状态
          const icuRes = await checkICUStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (icuRes.hasError !== 0) {
            this.$message.error(icuRes.errorMsg || '检查ICU状态失败')
            return
          }

          if (icuRes.data.status === '1') {
            await this.$confirm(
              icuRes.data.message || 'ICU患者填写出院记录需填ICU质量控制登记表！',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开ICU质量控制登记表页面
              this.openScoreForm('ICU质量控制登记表', 71)
            })
            return
          }
        }

        // 2.5 转科记录(14)和24小时入出院记录(08)的特殊检查
        if (selectedWenShu.wenShuLX === '14' || selectedWenShu.wenShuLX === '08') {
          const icuRes = await checkICUStatus({
            bingLiID: this.bingLiID,
            wenShuLX: selectedWenShu.wenShuLX
          })

          if (icuRes.hasError !== 0) {
            this.$message.error(icuRes.errorMsg || '检查ICU状态失败')
            return
          }

          if (icuRes.data.status === '1') {
            await this.$confirm(
              icuRes.data.message || 'ICU患者填写出院记录需填ICU质量控制登记表！',
              '提示',
              {
                confirmButtonText: '确定',
                showCancelButton: false,
                type: 'warning'
              }
            ).then(() => {
              // 打开ICU质量控制登记表页面
              this.openScoreForm('ICU质量控制登记表', 71)
            })
            return
          }
        }

        // 3. 创建新记录对象
        const newRecord = {
          id: 't' + new Date().getTime(),
          wenShuMC: selectedWenShu.wenShuMC,
          geShiDM: selectedWenShu.geShiDM,
          wenShuLX: selectedWenShu.wenShuLX,
          expanded: true,
          isEditing: true // 新增记录默认为编辑状态
        }
        // 设置URL
        newRecord.url = this.getRecordEditUrl(newRecord)
        // 5. 添加到详情列表
        this.recordDetailList.push(newRecord)
        if (this.fcApiLists[newRecord.id]) {
          const api = new FcApi()
          this.fcApiLists[newRecord.id] = api
        }
        const msg = await this.getGeShiDaiMa(selectedWenShu)
        if (msg) {
          newRecord.editor = msg.editor
          newRecord.wenShuMBNR = msg.wenShuFaBuNR_Real
          newRecord.wenShuNR = msg.wenShuNR
        }
        // 6. 滚动到新增记录位置
        this.$nextTick(() => {
          const detailElement = document.getElementById(`panel_${newRecord.id}`)
          if (detailElement) {
            // 使用setTimeout确保DOM已完全渲染
            setTimeout(async () => {
              this.$refs.recordDetails.scrollTop =
                detailElement.offsetTop - this.$refs.recordDetails.offsetTop
              this.handleFcIframeLoad(newRecord)
            }, 50)
          }
        })
      } catch (error) {
        this.$message.error('新增记录失败：' + (error.message || '未知错误'))
      } finally {
        this.loading = false
        this.addingRecord = false
      }
    },
    //获取格式代码
    async getGeShiDaiMa(selectedWenShu) {
      let row = {}
      const msg = await getByGeShiDM({
        geShiDM: selectedWenShu.geShiDM || selectedWenShu.geshiDM,
        banBenHao: this.patientInfo.bingAnHao,
        moBanLX: 1
      })
      if (msg.data?.wenShuFaBuNR_Real) {
        row = msg.data
        row.editor = '4'
        row.wenShuMBNR = msg.data.wenShuFaBuNR_Real
        row.wenShuNR = msg.data.wenShuNR
        this.recordDetailList.map((item) => {
          if (item.id === selectedWenShu.id) {
            item.wenShuMBNR = msg.data.wenShuFaBuNR_Real
            item.wenShuNR = msg.data.wenShuNR
          }
        })
      }
      return row
    },
    // 获取记录编辑URL
    getRecordEditUrl(record) {
      if (!record || !record.id) return ''

      const idIsNumber = typeof record.id === 'number'
      // 构建URL参数
      const params = {
        as_blid: record.bingliID || this.bingLiID,
        as_gsdm: record.geshiDM || record.geShiDM,
        as_zyid: this.patientInfo.zhuYuanID,
        as_yhid: this.yongHuID,
        as_wsid: idIsNumber ? record.id : 0, // 新文书ID为0
        as_wslx: record.wenshuLX || record.wenShuLX,
        as_tmpid: 't1',
        tmpid: Math.random()
      }

      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&')

      // 返回完整URL
      return `${this.baseUrl}/zyblws/blwsdetail.aspx?${queryString}`
    },
    // 处理iframe加载完成
    handleIframeLoad(_, recordId) {
      console.log('iframe加载完成', recordId)
    },

    // 处理编辑按钮点击
    async handleEditClick(recordId) {
      // 检查病历状态，如果已封存则不允许编辑
      if (this.patientInfo.bingLiZT === '1') {
        this.$message({
          type: 'warning',
          message: '已封存病历不能进行该操作!'
        })
        return
      }

      // 查找对应的记录
      const record = this.recordDetailList.find((item) => item.id === recordId)
      if (!record) {
        this.$message.error('未找到对应的记录')
        return
      }
      // 切换编辑状态
      if (record.isEditing) {
        record.isEditing = false
        record.url = record.wenShuYLURL
      } else {
        record.isEditing = true
        if (record.editor === '4') {
          const value = record.wenShuNR || '{}'
          if (this.fcApiLists[recordId]) {
            const api = new FcApi()
            this.fcApiLists[recordId] = api
          }
          // await this.getGeShiDaiMa(record)
          await this.handleFcIframeLoad(record)
          await fcApi.updateForm({ value: JSON.parse(value) })
          return
        }
        record.url = this.getRecordEditUrl(record)
      }
    },
    //模板导入
    async _export(type, recordId) {
      let record = this.recordDetailList.find((item) => item.id === recordId)
      if (!record) {
        this.$message.error('未找到对应的记录')
        return
      }
      let res
      switch (type) {
        case 'weijizhi':
          // eslint-disable-next-line no-case-declarations
          res = await getWeiDaoRuCriticalValueByBingLiID({ bingLiID: this.bingLiID })
          if (res.hasError == 0) {
            this.exportTableData = res.data
            this.exportDialog.visible = true
          }
          break
        case 'moban':
          // eslint-disable-next-line no-case-declarations
          res = await initZhuYuanBingLiMB({
            daiMa: record.geshiDM,
            tableName: 'e_zyblmbl1',
            yongHuID: 30276,
            yongHuZH: 'XK3',
            zhuanKeDM: 'C2'
          })
          if (res.hasError == 0) {
            this.exportTableData = res.data
            this.exportMbDialog.visible = true
          }
          break
        case 'EMPI':
          res = await getMedicalRecordByEMPI({
            empi: '**********',
            kaiShiSJ: '2025-6-31 00:00:00',
            currUserId: 13131
          })
          if (res.hasError == 0) {
            this.exportTableData = res.data
            this.exportDialog.visible = true
          }
          break
        case 'SSJLQK':
          res = await getBinRenSSJLQKByBingLiID({ bingLiID: '2628663' })
          if (res.hasError == 0) {
            this.exportTableData = res.data
            this.exportDialog.visible = true
          }
          break
        case 'YiYong':
          res = await getYiYongCaiLiao({
            pageIndex: 0,
            pageSize: 10,
            mode: '2',
            taoCanMC: '',
            zhuanKeDM: '42',
            yongHuID: 13131
          })
          if (res.hasError == 0) {
            this.exportTableData = res.data
            this.exportDialog.visible = true
          }
          break
        case 'WSZD':
          res = await getQuanBuWSZDByBingLiID({ bingLiID: '2628663' })
          if (res.hasError == 0) {
            this.exportTableData = res.data
            this.exportDialog.visible = true
          }
          break
        case 'YYXZ':
          await getMedicalStaff({
            neiBuLB: '0041',
            shiFouFY: true,
            shuLiang: '10',
            yeShu: 1,
            zhuanKeID: this.zhuanKeID
          })
          res = await getShouShuRyk()
          if (res.hasError == 0) {
            this.exportYYTableData = this.currentPageZhuDaoYSList(res.data)
            this.exportYYDialog = true
          }
          break
      }
    },
    currentPageZhuDaoYSList(arr) {
      let curData = arr
      if (this.benZhuanKe) {
        curData = arr.filter((item) => item.xianZhuanKeID == this.zhuanKeID)
      }
      return curData.slice(
        (this.currentPage - 1) * this.pageSize,
        (this.currentPage - 1) * this.pageSize + this.pageSize
      )
    },
    //
    async queryZhuDaoYS() {
      let res = await getShouShuRyk({
        key: this.renYuanXM
      })
      this.exportYYTableData = this.currentPageZhuDaoYSList(res.data)
    },
    //
    async handleSelectRY(item) {
      if (this.CurrentLocation) {
        const value = await fcApi.getValue()
        if (this.CurrentLocation) {
          let item = item
          if (list) {
            const text = this.CurrentLocation.text || ''
            const field = this.CurrentLocation.field
            value[field] =
              text.slice(0, this.CurrentLocation.selectionStart) +
              list.jianChaJG +
              text.slice(this.CurrentLocation.selectionEnd, text.length)
            await fcApi.updateForm({ value: value })
          }
        }
      }
    },
    // 处理保存按钮点击
    async handleSaveClick(recordId) {
      // 查找对应的记录
      const record = this.recordDetailList.find((item) => item.id === recordId)
      if (!record) {
        this.$message.error('未找到对应的记录')
        return
      }

      // 获取iframe元素
      const iframe = document.getElementById(`iframe_${recordId}`)
      if (!iframe) {
        this.$message.error('iframe未加载完成')
        return
      }

      try {
        if (record.editor === '4') {
          const value = await fcApi.getValue()
          const q = Object.keys(value)
          let zyblnrSimpleVoList = []
          q.forEach((e) => {
            let list = value[e]
            if (typeof value[e] == 'array' || typeof value[e] == 'object') {
              list = JSON.stringify(value[e])
            }
            if (this.qianMingLists[e]) {
              zyblnrSimpleVoList.push({
                daiMa: e,
                jieGuo: this.yongHuID + '^' + this.qianMingLists[e].mingCheng
              })
            } else {
              zyblnrSimpleVoList.push({ daiMa: e, jieGuo: list })
            }
          })
          const params = {
            wenShuID: record.wenShuID || 0,
            geShiDM: record.geShiDM,
            bingLiID: record.bingliID || this.bingLiID,
            yongHuID: this.yongHuID,
            zyblnrSimpleVoList: zyblnrSimpleVoList,
            wenShuNR: JSON.stringify(value),
            wenShuMBNR: record.wenShuFaBuNR_Real,
            wenShuDYNR: record.wenShuDaYinNR_Real,
            banBenHao: this.patientInfo.bingAnHao || this.patientInfo.bingRenBH
          }
          let msg
          if (record.geShiDM === '148') {
            msg = await savePFBNewEditor(params)
          } else {
            msg = await saveWenShuJiluNewEditor(params)
          }
          if (msg.data.hasError == 0) {
            this.$message.success('保存成功')
            setTimeout(async () => {
              await this.refreshYiShuXieWenShuList()
            }, 1000)
          } else {
            this.$message.error('保存失败: ' + (msg.data.errorMessage || '未知错误'))
          }
        } else {
          // 使用简化的iframe通信
          const message = await iframeCommunication.save(recordId, iframe)
          this.$message.success(message)
          // 保存成功后的处理
          // record.isEditing = false
          setTimeout(async () => {
            await this.refreshYiShuXieWenShuList()
          }, 1000)
        }
      } catch (error) {
        console.error('保存失败', error)
        this.$message.error('保存失败: ' + (error.message || '未知错误'))
      }
    },

    // 刷新已书写文书列表
    async refreshYiShuXieWenShuList() {
      try {
        // 获取初始化数据
        const initRes = await getProgressNoteInit({
          bingLiID: this.bingLiID,
          zhuanKeID: this.zhuanKeID
        })

        if (initRes.hasError === 0) {
          const data = initRes.data

          // 处理可书写文书列表（左侧列表）
          if (data && data.keShuXieWenShuList) {
            this.keShuXieWenShuList = data.keShuXieWenShuList
          }

          // 更新已书写文书列表
          if (data && data.yiShuXieWenShuList) {
            this.yiShuXieWenShuList = data.yiShuXieWenShuList.sort((a, b) => {
              return new Date(a.jilluSJ || 0) - new Date(b.jilluSJ || 0)
            })

            // 更新详情列表，保留展开状态（修改详情列表可能会出现bug，暂时注释）
            // const expandedStates = {}
            // const editingStates = {}
            // this.recordDetailList.forEach((record) => {
            //   expandedStates[record.id] = record.expanded
            //   editingStates[record.id] = record.isEditing
            // })

            // // 重新构建详情列表
            // this.recordDetailList = data.yiShuXieWenShuList.map((item) => {
            //   const expanded =
            //     expandedStates[item.id] !== undefined ? expandedStates[item.id] : true
            //   const isEditing =
            //     editingStates[item.id] !== undefined ? editingStates[item.id] : false
            //   const url = isEditing ? this.getRecordEditUrl(item) : item.wenShuYLURL
            //   return {
            //     ...item,
            //     expanded,
            //     isEditing,
            //     url
            //   }
            // })
          }
        }
      } catch (error) {
        console.error('刷新已书写文书列表失败', error)
        this.$message.error('刷新已书写文书列表失败')
      }
    },

    // 处理删除按钮点击
    handleDeleteClick(recordId) {
      this.$confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          // 查找对应的记录
          const record = this.recordDetailList.find((item) => item.id === recordId)
          if (!record) {
            this.$message.error('未找到对应的记录')
            return
          }

          // 获取iframe元素
          const iframe = document.getElementById(`iframe_${recordId}`)
          if (!iframe) {
            this.$message.error('iframe未加载完成')
            return
          }

          try {
            if (record.editor === '4') {
              if (record.id && record.id.indexOf('t') === -1) {
                const res = await deleteWenShuJLNewEditor({
                  wenShuID: record.id,
                  yongHuID: this.yongHuID
                })
                if (res.hasError === 0) {
                  this.$message.success('删除成功！')
                }
              }
            } else {
              // 使用简化的iframe通信
              const message = await iframeCommunication.delete(recordId, iframe)
              this.$message.success(message)
            }

            // 删除成功后的处理
            const isTemporaryRecord = recordId.toString().startsWith('t')
            if (isTemporaryRecord) {
              // 临时记录直接从列表中移除
              const index = this.recordDetailList.findIndex((item) => item.id === recordId)
              if (index !== -1) {
                this.recordDetailList.splice(index, 1)
              }
            } else {
              // 已保存记录重新获取列表
              setTimeout(async () => {
                await this.refreshYiShuXieWenShuList()
              }, 1000)
            }
          } catch (error) {
            console.error('删除失败', error)
            this.$message.error('删除失败: ' + (error.message || '未知错误'))
          }
        })
        .catch(() => {
          // 取消删除，不做任何操作
        })
    },

    // 处理打印按钮点击
    async handlePrintClick(recordId) {
      // 获取iframe元素
      const iframe = document.getElementById(`iframe_${recordId}`)
      if (!iframe) {
        this.$message.error('iframe未加载完成')
        return
      }

      try {
        // 使用简化的iframe通信
        const message = await iframeCommunication.print(recordId, iframe)
        this.$message.success(message)
      } catch (error) {
        console.error('打印失败', error)
        this.$message.error('打印失败: ' + (error.message || '未知错误'))
      }
    },

    // 打开评分表页面（使用弹窗）
    openScoreForm(title, pfid) {
      // 设置弹窗属性
      this.scoreFormDialog.title = title
      this.scoreFormDialog.url = `${this.baseUrl}/pfbgl/pfblist.aspx?as_blid=${this.bingLiID}&as_pfid=${pfid}&as_pfzhid=0`
      this.scoreFormDialog.loading = true
      this.scoreFormDialog.visible = true
    },

    // 关闭评分表弹窗
    closeScoreFormDialog() {
      this.scoreFormDialog.visible = false
      this.scoreFormDialog.url = ''
      this.scoreFormDialog.title = ''
    },

    // 关闭导入数据弹窗
    closeExportDialog() {
      this.exportDialog.visible = false
      this.exportDialog.title = ''
    },
    // 关闭模板弹窗
    closeMbExportDialog() {
      this.exportMbDialog.visible = false
      this.exportMbDialog.title = ''
    },
    //
    handleSelectionChange() {
      console.log()
    },

    // 处理评分表iframe加载完成
    handleScoreFormIframeLoad() {
      this.scoreFormDialog.loading = false
    },
    //新版编辑器显示
    async isEditShow(_d, recordId) {
      console.log(this.recordDetailList)
      //id类型不一致
      const record = this.recordDetailList.find((item) => item.id == recordId)
      if (!record) {
        // this.$message.error('未找到对应的记录')
        return
      }
      if (record.editor === '4') {
        // const value = record.wenShuNR || '{}'
        // await this.getGeShiDaiMa(record)
        try {
          if (this.fcApiLists[recordId]) {
            const api = new FcApi()
            this.fcApiLists[recordId] = api
          }
          await this.handleFcIframeLoad(record, _d)
          api.setMode('print')
        } catch (err) {
          console.error(err)
        }

        // await fcApi.updateForm({ value: JSON.parse(value) })

        return true
      }
    },
    async handleFcIframeLoad(record, _d) {
      // 查找对应的记录
      if (record.wenShuMBNR) {
        let json = JSON.parse(record.wenShuMBNR)
        const FcEditorRule = JSON.parse(json.rule || '{}')
        const FcEditorOption = JSON.parse(json.options || '{}')
        const FcEditorValue = JSON.parse(record.wenShuNR || '{}')
        const i1 = document.getElementById('iframe_' + record.id) || _d
        const fcApi = this.fcApiLists[record.id]
        //http://localhost:5173/e/v.html
        await fcApi.init(i1, 'http://************/ehr/wyy/e/v.html', {
          rule: FcEditorRule,
          option: FcEditorOption,
          value: FcEditorValue
        })
        let _this = this
        fcApi.on('emitSelection', function (e) {
          if (e[0]) {
            _this.CurrentLocation = e[0]
          }
        })
        fcApi.on('signatureRequest', function (e) {
          if (e[0]) {
            _this.CurrentLocation = e[0]
            _this.qmPhotoType.visible = true
            _this.qmPhotoType.qmtype = true
          }
        })
      }
    },
    //二级密码
    async SaveSecPass(data) {
      if (data) {
        const value = await fcApi.getValue()
        if (this.CurrentLocation) {
          if (data.QMdata) {
            const field = this.CurrentLocation.field
            if (data.QMdata.qianMingTPBase64) {
              value[field] = 'data:image/jpeg;base64,' + data.QMdata.qianMingTPBase64
            } else if (data.QMdata.qianMingTPURL) {
              value[field] = data.QMdata.qianMingTPURL
            }
            this.qianMingLists[field] = data.QMdata
            await fcApi.updateForm({ value: value })
            this.qmPhotoType.visibledialog = false
          }
        }
      }
    },
    addMbData() {
      this.mobanId = 0
      this.moBanMC = ''
      this.moBanMCArea = ''
    },
    //
    async saveMbData() {
      let res
      const eZyblmbVo = {
        bianHao: '',
        mingCheng: '',
        daiMa: '',
        daiMaZhi: '',
        jieGuo: '',
        caoZuoZhe: '',
        xiuGaiSJ: '',
        zhuanKeDM: '',
        caoZuoZID: 0,
        leiXing: '',
        tableName: ''
      }
      if (this.mobanId === 0) {
        res = await updatetMoban(eZyblmbVo)
      } else {
        res = await insertMoban(eZyblmbVo)
      }
    },
    cancelDialog() {
      this.qmPhotoType.visibledialog = false
    }
  }
}
</script>

<style scoped lang="scss">
.progress-note-container {
  height: 100%;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #f6f6f6;
  position: relative;
}

.progress-note-layout {
  display: flex;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.left-panel {
  width: 380px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background-color: #eff3fb;
  border-radius: 4px;
}

.left-header {
  padding: 10px;
  border-bottom: 1px solid #dcdfe6;
  background-color: #eff3fb;
}

.title-area {
  display: flex;
  align-items: center;

  span {
    font-weight: bold;
    margin-right: 10px;
    display: inline-block;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    vertical-align: middle;
  }

  .record-type-select {
    margin-left: 0;
    width: 180px;
  }
}

.record-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px;

  ::v-deep .el-table {
    .record-name {
      cursor: pointer;

      &:hover {
        text-decoration: underline;
      }
    }

    .record-time {
      font-size: 14px;
    }
  }
}

.right-panel {
  flex: 1;
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  background-color: #eff3fb;
  padding: 10px;
  border-radius: 4px;
}

.right-header {
  padding: 0 12px;
  height: 58px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #eff3fb;
  display: flex;
  align-items: center;
}

.button-group {
  display: flex;
}

.record-details {
  flex: 1;
  overflow: auto;
  padding-top: 10px;
  background-color: #eff3fb;
}

.no-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #909399;

  i {
    font-size: 48px;
    margin-bottom: 10px;
  }

  p {
    font-size: 16px;
  }
}

.record-detail-item {
  margin-bottom: 10px;
  border: 1px solid #dcdfe6;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  }
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 50px;
  background-color: #eaf0f9;
  padding: 8px 12px;
  border-bottom: 1px solid #dcdfe6;
  transition: background-color 0.3s;

  &:hover {
    background-color: #ecf5ff;
  }

  .record-actions {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-right: 10px;

    .action-button {
      margin-left: 0;
      margin-right: 0;
      padding: 5px 10px;
    }

    i {
      margin-left: 5px;
      cursor: pointer;
    }
  }
}

.record-title {
  display: flex;
  align-items: center;
  padding-left: 8px;
  border-left: 4px solid #356ac5;
  cursor: pointer;
  flex: 1;

  span {
    font-weight: bold;
    color: #303133;
    font-size: 15px;
  }
}

.record-content {
  padding: 0;
  background-color: #fff;
  transition: height 0.3s;

  iframe {
    width: 100%;
    height: 540px;
    border: none;
    display: block;
  }

  .no-content {
    padding: 30px;
    text-align: center;
    color: #909399;
    font-size: 14px;
    background-color: #fafafa;
  }
}

// 评分表弹窗样式
.score-form-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;

  .loading-spinner {
    text-align: center;

    i {
      font-size: 32px;
      color: #409eff;
    }

    p {
      margin-top: 10px;
      color: #606266;
    }
  }
}
.Dialog {
  ::v-deep .el-dialog__body {
    padding: 10px 20px;
    .renYuanXMInput {
      width: 43%;
      margin-right: 10px;
      .el-input__inner {
        padding: 0 6px;
        height: 24px;
        font-size: 13px;
      }
    }
    .renYuanList {
      margin-top: 20px;
      width: 100%;
      thead {
        background-color: #eaf0f9;
      }
      td {
        border: 1px solid #dcdfe6;
        border-collapse: collapse; /* 移除表格内边框间的间隙 */
        padding: 6px;
        font-size: 13px;
      }
      tbody {
        tr:nth-child(even) {
          background-color: #eaf0f9;
        }
        tr:nth-child(odd) {
          background-color: #f6f6f6;
        }
      }
    }
    .bottom-btn {
      margin-top: 30px;
      flex-direction: row-reverse;
      .el-button {
        margin-left: 10px;
      }
    }
  }
}
// 响应式调整
@media screen and (max-width: 768px) {
  .left-panel {
    width: 220px;
  }

  .title-area {
    flex-wrap: wrap;

    .record-type-select {
      margin-top: 5px;
      margin-left: 0;
    }
  }
}
</style>
