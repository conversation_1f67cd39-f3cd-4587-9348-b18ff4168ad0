<template>
  <div class="nutritional-box-view">
    <!-- 左侧列表 -->
    <div class="box-left-panel">
      <div class="left-screen-sheet">
        <div class="screen-header">
          <span class="screen-title">营养风险筛查</span>
          <i class="el-icon-plus" @click="addScreening"></i>
        </div>
        <div class="timeline-box">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in yyscList" :key="index">
              <span :class="{ active: activeIndex == index }" @click="handleItemClick(item, index)">
                {{ getDataBeforeColon(item.pingGuRQ) }} 营养风险筛查 {{ item.zongPingFen }}分
              </span>
            </el-timeline-item>
          </el-timeline>
        </div>
      </div>
    </div>
    <!-- 右侧列表 -->
    <div class="box-right-panel">
      <div class="right-screen-sheet">
        <div class="right-header top-fix">
          <div class="title">营养风险筛查表</div>
          <div class="actions">
            <el-button type="primary" @click="handleSaveClick">保存</el-button>
            <el-button type="primary" v-if="addScreeningShow" @click="deleteDYyscByID">
              删除
            </el-button>
            <el-button type="primary" v-if="addScreeningShow" @click="handleSaveClick">
              打印
            </el-button>
          </div>
        </div>
        <!-- 1. -->
        <div class="item-section">
          <div>
            <span>患者姓名:</span>
            <el-input disabled v-model="riskRecord.bingRenXM" class="select-input"></el-input>
          </div>
          <div>
            <span>病案号:</span>
            <el-input disabled v-model="riskRecord.bingAnHao" class="select-input"></el-input>
          </div>
          <div>
            <span>调查者:</span>
            <el-input v-model="riskRecord.pingGuRYXM" class="select-input"></el-input>
          </div>
          <div>
            <span style="width: 80px">病区-床位号:</span>
            <el-input disabled v-model="riskRecord.chuangWeiHao" class="select-input"></el-input>
          </div>
        </div>
        <!-- 2. -->
        <div class="right-header right-header-none">
          <div class="title">
            附件：患者基本信息(N-1)
            <span>以下入院时首次(文本框)填写，入院第二日上午前完成。</span>
          </div>
        </div>
        <!-- 3. -->
        <div class="item-section">
          <div>
            <span>科室名称:</span>
            <el-input disabled v-model="riskRecord.keShiMC" class="select-input"></el-input>
          </div>
          <div>
            <span>入院时间:</span>
            <el-date-picker
              disabled
              style="width: 174px"
              v-model="riskRecord.ruYuanRQ"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-dd HH:mm"
              placeholder="选择日期时间"
              type="datetime"
              class="select-input"
            ></el-date-picker>
          </div>
          <div>
            <span>评估时间:</span>
            <el-date-picker
              style="width: 174px"
              v-model="riskRecord.pingGuRQ"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期时间"
              class="select-input"
            ></el-date-picker>
          </div>
          <div>
            <span>性别:</span>
            <el-radio disabled v-model="riskRecord.bingRenXB" label="1">男</el-radio>
            <el-radio disabled v-model="riskRecord.bingRenXB" label="2">女</el-radio>
          </div>
        </div>
        <!-- 4. -->
        <div class="item-section">
          <div>
            <span>年龄(周岁:</span>
            <el-input disabled v-model="riskRecord.nianLing" class="select-input"></el-input>
          </div>
          <div>
            <span>神清:</span>
            <el-radio v-model="riskRecord.shenQing" label="1">是</el-radio>
            <el-radio v-model="riskRecord.shenQing" label="0">否</el-radio>
          </div>
          <div style="flex: 2">
            <span style="width: 36%; text-align: left">入院第2天上午8时前未行手术:</span>
            <el-radio disabled v-model="riskRecord.shouShuHL" label="1">是</el-radio>
            <el-radio disabled v-model="riskRecord.shouShuHL" label="0">否</el-radio>
          </div>
        </div>
        <!-- 5. -->
        <div class="item-section">
          <div>
            <span>临床诊断:</span>
            <el-input disabled v-model="riskRecord.linChuangZD" style="width: 93%"></el-input>
          </div>
        </div>
        <!-- 6. -->
        <!-- <div class="item-section">
          <div>
            <span>次要诊断:</span>
            <el-input v-model="riskRecord.ciYaoZD" style="width: 93%"></el-input>
          </div>
        </div> -->
        <!-- 7. -->
        <div class="item-section" style="margin-bottom: 0">
          <div>
            <span>筛查类型:</span>
            <el-radio v-model="riskRecord.shaiChaLX" label="1">入院24小时筛查</el-radio>
            <el-radio v-model="riskRecord.shaiChaLX" label="2">每七天筛查</el-radio>
            <el-radio v-model="riskRecord.shaiChaLX" label="3">出院前筛查</el-radio>
            <el-radio v-model="riskRecord.shaiChaLX" label="4">病情变化时筛查</el-radio>
          </div>
        </div>
        <!-- 8. -->
        <div class="right-header right-header-none">
          <div class="title">
            附件：患者基本信息(N-2)
            <span style="font-weight: normal">
              以下入院时首次(文本框)填写，入院第二日上午前完成。
            </span>
          </div>
        </div>
        <!-- 9. -->
        <div class="item-section">
          <div>
            <span>身高:</span>
            <el-input v-model="riskRecord.shenGao" class="select-input"></el-input>
          </div>
          <div>
            <span>体重:</span>
            <el-input disabled v-model="riskRecord.tiZhong" class="select-input"></el-input>
          </div>
          <div>
            <el-checkbox disabled v-model="checked">体重无法测量</el-checkbox>
          </div>
          <div>
            <span>BMI:</span>
            <el-input v-model="riskRecord.tiZhongZS" class="select-input"></el-input>
          </div>
        </div>
        <!-- 10. -->
        <div class="right-header" style="border-bottom: none; margin-right: 0px">
          <div>
            <span class="title" style="border-left: none">营养风险筛查评分</span>
            <span>(营养受损状况评分+疾病严重程度评分+年龄评分):</span>
          </div>
          <div>
            <el-button @click="handleSaveClick">{{ riskRecord.zongPingFen }}分</el-button>
          </div>
        </div>
        <!-- 11. -->
        <div style="border: 1px solid #e0e0e0; margin-bottom: 10px">
          <!-- 01. -->
          <div class="right-header right-header-rate">
            <div class="title1">营养受损状况评分</div>
            <div class="actions">
              <el-button @click="handleSaveClick">{{ riskRecord.yingYangSSZKZHDF }}分</el-button>
            </div>
          </div>
          <div>
            <el-descriptions class="top-box" title="" :column="1" border>
              <el-descriptions-item>
                <template slot="label">0分:</template>
                <el-checkbox
                  v-model="checkedJB"
                  :true-label="1"
                  :false-label="0"
                  @change="(event) => handleNormalChange('yy', event)"
                >
                  正常营养状态
                </el-checkbox>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">1分:</template>
                <el-checkbox
                  v-for="(item, index) in damagedList1"
                  :key="index"
                  v-model="riskRecord.yingYangSSZKPF1[index]"
                  true-label="1"
                  false-label="0"
                  @change="updateCheckedStatus('yy')"
                >
                  {{ item }}
                </el-checkbox>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">2分:</template>
                <el-checkbox
                  v-for="(item, index) in damagedList2"
                  :key="index"
                  v-model="riskRecord.yingYangSSZKPF2[index]"
                  true-label="1"
                  false-label="0"
                  @change="updateCheckedStatus('yy')"
                >
                  {{ item }}
                </el-checkbox>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">3分:</template>
                <el-checkbox
                  v-for="(item, index) in damagedList3"
                  :key="index"
                  v-model="riskRecord.yingYangSSZKPF3[index]"
                  true-label="1"
                  false-label="0"
                  @change="updateCheckedStatus('yy')"
                >
                  {{ item }}
                </el-checkbox>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <!-- 02. -->
          <div class="right-header right-header-rate">
            <div class="title1">疾病严重程度评分</div>
            <div class="actions">
              <el-button type="success" plain @click="handleSaveClick">
                {{ riskRecord.jiBingYZCDZHDF }}分
              </el-button>
            </div>
          </div>
          <div>
            <el-descriptions class="top-box" title="" :column="1" border>
              <el-descriptions-item>
                <template slot="label">0分:</template>
                <el-checkbox
                  v-model="checkedYY"
                  :true-label="1"
                  :false-label="0"
                  @change="(event) => handleNormalChange('jb', event)"
                >
                  正常营养状态
                </el-checkbox>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">1分:</template>
                <el-checkbox
                  v-for="(item, index) in diseaseList1"
                  :key="index"
                  v-model="riskRecord.jiBingYZCDPF1[index]"
                  true-label="1"
                  false-label="0"
                  @change="updateCheckedStatus('jb')"
                >
                  {{ item }}
                </el-checkbox>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">2分:</template>
                <el-checkbox
                  v-for="(item, index) in diseaseList2"
                  :key="index"
                  v-model="riskRecord.jiBingYZCDPF2[index]"
                  true-label="1"
                  false-label="0"
                  @change="updateCheckedStatus('jb')"
                >
                  {{ item }}
                </el-checkbox>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">3分:</template>
                <el-checkbox
                  v-for="(item, index) in diseaseList3"
                  :key="index"
                  v-model="riskRecord.jiBingYZCDPF3[index]"
                  true-label="1"
                  false-label="0"
                  @change="updateCheckedStatus('jb')"
                >
                  {{ item }}
                </el-checkbox>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <!-- 03. -->
          <div class="right-header right-header-rate">
            <div class="title1">年龄评分</div>
            <div class="actions">
              <el-button plain @click="handleSaveClick">{{ riskRecord.nianLingPF }}分</el-button>
            </div>
          </div>
          <div>
            <el-descriptions class="top-box" title="" :column="1" border>
              <el-descriptions-item>
                <template slot="label">0分:</template>
                <el-checkbox
                  :value="riskRecord.nianLingPF == 0"
                  @change="(val) => (riskRecord.nianLingPF = val ? 0 : riskRecord.nianLingPF)"
                >
                  年龄<70岁
                </el-checkbox>
              </el-descriptions-item>
              <el-descriptions-item>
                <template slot="label">1分:</template>
                <el-checkbox
                  :value="riskRecord.nianLingPF == 1"
                  @change="(val) => (riskRecord.nianLingPF = val ? 1 : riskRecord.nianLingPF)"
                >
                  年龄≥70岁
                </el-checkbox>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <!-- 04. -->
          <div style="border-top: 1px solid #dcdfe6; padding: 14px 14px 4px">
            <div class="left-tips">
              <i class="el-icon-warning"></i>
              <span class="el-icon-title1">注：</span>
              <span class="el-icon-title" style="width: 100%">
                若患者诊断不在疾病严重程度评分名称范围内，则向类似的情况靠拢，注意早晨空腹、着病房服、免鞋测量
              </span>
            </div>
            <div class="left-tips">
              <i class="el-icon-warning"></i>
              <span class="el-icon-title1">注：</span>
              <div>
                <div class="el-icon-content">
                  <span class="el-icon-rate">评分标准中疾病的严重程度(1分):</span>
                  <span class="el-icon-title">
                    慢性病的想者由于并发症的发生而住院，虽然身体很虚弱，但是还是可以规律地下床活动。许多愚者的蛋白需求增加量可以通过日常饮食或其他方式补充。
                  </span>
                </div>
                <div class="el-icon-content">
                  <span class="el-icon-rate">评分标准中疾病的严重程度(2分):</span>
                  <span class="el-icon-title">
                    患者由于疾病而卧床，这些患者的蛋白需求增加，例如:较大的腹部外科手术、严重的感染。尽管许多患者需要人工喂养辅助,但是仍然可以满足要求。
                  </span>
                </div>
                <div class="el-icon-content">
                  <span class="el-icon-rate">评分标准中疾病的严重程度(3分):</span>
                  <span class="el-icon-title">
                    需要辅助呼吸、正性肌力药物的危重症患者的蛋白需求大量增加，大部分的这些患者无法通过人工喂养满足，蛋白质分解和氮损失显著增加。
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 12. -->
        <div class="right-header right-header-comment" v-if="addScreeningShow">
          <div class="right-header-comment-title">
            <div class="title">营养师点评</div>
            <div class="actions">
              <el-button type="primary" @click="saveYingYangShiDP">保存点评结果</el-button>
            </div>
          </div>
          <div class="item-comment item-comment-describe" style="margin: 4px 0 8px">
            <div>
              <span style="margin-right: 15px">点评时间:</span>
              <span style="margin-right: 40px">{{ ylYyscdpVo.yingYangSXGSJ }}</span>
            </div>
            <div>
              <span style="margin-right: 20px">点评营养师:</span>
              <span style="margin-right: 90px">{{ ylYyscdpVo.yingYangSXM }}</span>
            </div>
            <div style="display: flex">
              <span>原因(可多选)：</span>
              <el-checkbox-group v-model="ylYyscdpVo.yingYangSYY">
                <el-checkbox label="1">营养状况评分错误</el-checkbox>
                <el-checkbox label="2">疾病严重程度评分错误</el-checkbox>
                <el-checkbox label="3">年龄评分错误</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="item-comment">
            <span class="remark">备注:</span>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="ylYyscdpVo.yingYangSBZ"
              :autosize="{ minRows: 5, maxRows: 5 }"
              show-word-limit
              clearable
            ></el-input>
          </div>
        </div>
        <!-- 13. -->
        <div class="right-header right-header-comment" v-if="addScreeningShow">
          <div class="right-header-comment-title">
            <div class="title">护士长营养筛查点评</div>
            <div class="actions">
              <el-button type="primary" @click="saveHuShiZhangDP">保存点评结果</el-button>
            </div>
          </div>
          <div class="item-comment item-comment-describe">
            <div>
              <span>点评结果:</span>
              <span class="comment-input">
                <el-select v-model="ylYyscdpVo.huShiZDPJG" placeholder="请选择">
                  <el-option
                    v-for="item in dpjgDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </span>
            </div>
            <div>
              <span>点评护士长:</span>
              <span class="comment-input">
                <el-input v-model="ylYyscdpVo.huShiZYHXM" class="select-input"></el-input>
              </span>
            </div>
            <div style="display: flex">
              <span>原因(可多选):</span>
              <el-checkbox-group v-model="ylYyscdpVo.huShiZYY">
                <el-checkbox label="1">营养状况评分错误</el-checkbox>
                <el-checkbox label="2">疾病严重程度评分错误</el-checkbox>
                <el-checkbox label="3">年龄评分错误</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="item-comment">
            <span class="remark">备注:</span>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="ylYyscdpVo.huShiZBZ"
              :autosize="{ minRows: 5, maxRows: 5 }"
              show-word-limit
              clearable
            ></el-input>
          </div>
        </div>
        <!-- 14. -->
        <div class="right-header right-header-comment" v-if="addScreeningShow">
          <div class="right-header-comment-title">
            <div class="title">护士长交叉点评</div>
            <div class="actions">
              <el-button type="primary" @click="saveJiaoChaDP">保存点评结果</el-button>
            </div>
          </div>
          <div class="item-comment item-comment-describe">
            <div>
              <span>点评结果:</span>
              <span class="comment-input">
                <el-select v-model="ylYyscdpVo.jiaoChaDPJG" placeholder="请选择">
                  <el-option
                    v-for="item in dpjgDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </span>
            </div>
            <div style="display: flex">
              <span class="blo70">交叉点评护士长:</span>
              <span class="comment-input">
                <el-input v-model="ylYyscdpVo.jiaoChaDPYHXM" class="select-input"></el-input>
              </span>
            </div>
            <div style="display: flex">
              <span>原因(可多选):</span>
              <el-checkbox-group v-model="ylYyscdpVo.jiaoChaDPYY">
                <el-checkbox label="1">营养状况评分错误</el-checkbox>
                <el-checkbox label="2">疾病严重程度评分错误</el-checkbox>
                <el-checkbox label="3">年龄评分错误</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="item-comment">
            <span class="remark">备注:</span>
            <el-input
              type="textarea"
              placeholder="请输入内容"
              v-model="ylYyscdpVo.jiaoChaDPBZ"
              :autosize="{ minRows: 5, maxRows: 5 }"
              show-word-limit
              clearable
            ></el-input>
          </div>
        </div>
        <!-- 15. -->
        <div class="right-header right-header-comment" v-if="addScreeningShow">
          <div class="right-header-comment-title">
            <div class="title">营养筛查点评反馈</div>
            <div class="actions">
              <el-button type="primary" @click="savePingGuRYFK">保存反馈</el-button>
            </div>
          </div>
          <div
            class="item-comment item-comment-describe"
            style="justify-content: flex-start; padding-left: 8px"
          >
            <div style="display: flex; margin-right: 25px">
              <span class="blo70">调查者是否知晓:</span>
              <span class="comment-input">
                <el-select v-model="ylYyscdpVo.huShiSFZX" placeholder="请选择">
                  <el-option
                    v-for="item in sfzxDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </span>
            </div>
            <div style="display: flex">
              <span class="blo70">调查者是否修改:</span>
              <span class="comment-input">
                <el-select v-model="ylYyscdpVo.huShiSFXG" placeholder="请选择">
                  <el-option
                    v-for="item in sfzxDM"
                    :key="item.daiMa"
                    :label="item.mingCheng"
                    :value="item.daiMa"
                  ></el-option>
                </el-select>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getInitData,
  getLastYYSCByBLIDList,
  getDYyscByID,
  saveDYyscReturnWithID,
  deleteDYyscByID,
  getYyscFullList,
  saveYingYangShiDP,
  saveHuShiZhangDP,
  saveJiaoChaDP,
  savePingGuRYFK
} from '@/api/nutritional-risk-screening'
import { mapState } from 'vuex'
import { format, parseISO } from 'date-fns'
import { tr } from 'date-fns/locale'
export default {
  data() {
    return {
      checked: false,
      checkedJB: false,
      checkedYY: false,
      dpjgDM: [
        { mingCheng: '合理', daiMa: '1' },
        { mingCheng: '不合理', daiMa: '0' }
      ],
      sfzxDM: [
        { mingCheng: '是', daiMa: '1' },
        { mingCheng: '否', daiMa: '0' }
      ],
      value1: '',
      damagedList1: ['3个月内体重丢失大于5%', '前1周的食物摄入为正常食物需求的50-75%'],
      damagedList2: ['2个月内体重丢失大于5%', '前1周的食物摄入为正常食物需求的25-50%'],
      damagedList3: [
        '1个月内体重丢失大于5%(3个月内大于15%)',
        '前1周的食物摄入为正常食物需求的0-25%',
        '体重指数小于18.5并全身情况受损'
      ],
      diseaseList1: [
        '骶骨折',
        '长期血液透析',
        '慢性疾病有急性并发性',
        '糖尿病',
        '肝硬化',
        '慢性阻塞性疾病',
        '恶性肿瘤',
        '其他'
      ],
      diseaseList2: ['腹部大手术', '血液系统恶性肿瘤', '重度肺炎', '卒中', '其他'],
      diseaseList3: ['头部损伤', '骨髓移植', '重症监护的患者(APACHE>10)', '其他'],
      riskRecord: {
        bingRenJBXX: '李超超',
        bingRenJBSJ: '2023-07-03 10:03',
        bingRenJBXX: '1'
      },
      yyscList: [],

      textarea: '',
      activeIndex: 0,
      riskRecord: {
        yingYangSSZKPF1: [],
        yingYangSSZKPF2: [],
        yingYangSSZKPF3: [],
        jiBingYZCDPF1: [],
        jiBingYZCDPF2: [],
        jiBingYZCDPF3: []
      },
      dyyscAutopfVoList: {
        icddm: '',
        icdmc: '',
        id: '',
        bingLiID: '',
        leiBie: '',
        leiBieMC: '',
        neiBuLB: '',
        neiBuLBMC: '',
        daiMa: '',
        mingChen: '',
        xuHao: '',
        xuanXiang: '',
        fenZhi: '',
        shiFouXZ: '',
        yingYangSCXMDM: '',
        yingYangSCXMMC: '',
        caoZuoZID: '',
        xiuGaiSJ: '',
        shuoMing: '',
        shuChuYJ: ''
      },
      ylYyscdpVo: {
        id: '',
        bingLiID: '',
        huShiYHID: '',
        huShiXM: '',
        huShiXGSJ: '',
        huShiSFZX: '',
        huShiSFXG: '',
        yingYangSYHID: '',
        yingYangSXM: '',
        yingYangSDPJG: '',
        yingYangSYY: [],
        yingYangSBZ: '',
        yingYangSXGSJ: '',
        huShiZDPJG: '',
        huShiZBZ: '',
        huShiZXGSJ: '',
        huShiZYHID: '',
        huShiZYY: [],
        huShiZYHXM: '',
        jiaoChaDPYHID: '',
        jiaoChaDPYHXM: '',
        jiaoChaDPJG: '',
        jiaoChaDPBZ: '',
        jiaoChaDPXGSJ: '',
        jiaoChaDPYY: []
      },
      addScreeningShow: true
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.init()
  },
  methods: {
    getDataBeforeColon(dateStr) {
      const lastColonIndex = dateStr.lastIndexOf(':')
      return dateStr.substring(0, lastColonIndex)
    },
    // 页面初始化
    async init() {
      this.getYyscFullList()
    },
    // 根据ID返回营养筛查记录
    async getInitData() {
      try {
        const res = await getInitData({
          bingLiID: this.patientDetail.bingLiID
        })
        if (res.hasError === 0) {
          this.delArr(res.data)
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 根据ID返回营养筛查记录
    async saveDYyscReturnWithID() {
      try {
        const res = await saveDYyscReturnWithID({
          ...this.riskRecord,
          yingYangSSZKPF1: this.riskRecord.yingYangSSZKPF1?.join(''),
          yingYangSSZKPF2: this.riskRecord.yingYangSSZKPF2?.join(''),
          yingYangSSZKPF3: this.riskRecord.yingYangSSZKPF3?.join(''),
          jiBingYZCDPF1: this.riskRecord.jiBingYZCDPF1?.join(''),
          jiBingYZCDPF2: this.riskRecord.jiBingYZCDPF2?.join(''),
          jiBingYZCDPF3: this.riskRecord.jiBingYZCDPF3?.join('')
        })
        if (res.hasError === 0) {
          this.getYyscFullList()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 删除营养筛查记录
    async deleteDYyscByID() {
      try {
        const res = await deleteDYyscByID({
          ID: this.riskRecord.id
        })
        if (res.hasError === 0) {
          this.getYyscFullList()
        }
      } catch (error) {
        console.log(error)
      }
    },

    addScreening() {
      this.activeIndex = '-1'
      this.addScreeningShow = false
      this.getInitData()
    },

    // 病人营养筛查记录获取（列表）
    async getYyscFullList() {
      try {
        const res = await getYyscFullList({
          bingLiID: this.patientDetail.bingLiID
        })
        if (res.hasError === 0) {
          this.yyscList = res.data
          this.delArr(res.data[0])
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 保存营养师点评
    async saveYingYangShiDP() {
      try {
        const res = await saveYingYangShiDP({
          ID: this.ylYyscdpVo.id,
          bingLilD: this.ylYyscdpVo.bingLiID,
          yingYangSYHID: this.ylYyscdpVo.yingYangSYHID,
          yingYangSXM: this.ylYyscdpVo.yingYangSXM,
          yingYangSDPJG: this.ylYyscdpVo.yingYangSDPJG,
          yingYangSBZ: this.ylYyscdpVo.yingYangSBZ,
          yingYangSXGSJ: this.ylYyscdpVo.yingYangSXGSJ,
          yingYangSYY: this.ylYyscdpVo.yingYangSYY.join(',')
        })
        if (res.hasError === 0) {
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 保存护士长点评
    async saveHuShiZhangDP() {
      try {
        const res = await saveHuShiZhangDP({
          ID: this.ylYyscdpVo.id,
          bingLilD: this.ylYyscdpVo.bingLiID,
          huShiZDPJG: this.ylYyscdpVo.huShiZDPJG,
          huShiZBZ: this.ylYyscdpVo.huShiZBZ,
          huShiZXGSJ: this.ylYyscdpVo.huShiZXGSJ,
          huShiZYHID: this.ylYyscdpVo.huShiZYHID,
          huShiZYHXM: this.ylYyscdpVo.huShiZYHXM,
          huShiZYY: this.ylYyscdpVo.huShiZYY.join(',')
        })
        if (res.hasError === 0) {
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 保存交叉点评
    async saveJiaoChaDP() {
      try {
        const res = await saveJiaoChaDP({
          ID: this.ylYyscdpVo.id,
          bingLilD: this.ylYyscdpVo.bingLiID,
          jiaoChaDPJG: this.ylYyscdpVo.jiaoChaDPJG,
          jiaoChaDPBZ: this.ylYyscdpVo.jiaoChaDPBZ,
          jiaoChaDPXGSJ: this.ylYyscdpVo.jiaoChaDPXGSJ,
          jiaoChaDPYHID: this.ylYyscdpVo.jiaoChaDPYHID,
          jiaoChaDPYHXM: this.ylYyscdpVo.jiaoChaDPYHXM,
          jiaoChaDPYY: this.ylYyscdpVo.jiaoChaDPYY.join(',')
        })
        if (res.hasError === 0) {
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 保存反馈点评
    async savePingGuRYFK() {
      try {
        const res = await savePingGuRYFK({
          ID: this.ylYyscdpVo.id,
          bingLilD: this.ylYyscdpVo.bingLiID,
          huShiYHID: this.ylYyscdpVo.huShiYHID,
          huShiXM: this.ylYyscdpVo.huShiXM,
          huShiSFZX: this.ylYyscdpVo.huShiSFZX,
          huShiSFXG: this.ylYyscdpVo.jiaoChaDPYHID,
          huShiXGSJ: this.ylYyscdpVo.huShiXGSJ
        })
        if (res.hasError === 0) {
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 初始化复选框状态，判断是否都未选
    initCheckboxStatus(type) {
      if (type == 'yy') {
        if (this.riskRecord.yingYangSSZKPF1[2] && this.riskRecord.yingYangSSZKPF1[2] == '2') {
          this.checkedJB = true
        } else {
          this.checkedJB = false
        }
        // const allYYUnchecked =
        //   JSON.stringify(this.riskRecord.yingYangSSZKPF1) == JSON.stringify(['0', '0']) ||
        //   (JSON.stringify(this.riskRecord.yingYangSSZKPF1) == JSON.stringify(['0', '0', '1']) &&
        //     JSON.stringify(this.riskRecord.yingYangSSZKPF2) == JSON.stringify(['0', '0']) &&
        //     JSON.stringify(this.riskRecord.yingYangSSZKPF3) == JSON.stringify(['0', '0', '0']))
        // this.checkedJB = allYYUnchecked ? 1 : 0
      } else if (type == 'jb') {
        if (this.riskRecord.jiBingYZCDPF1[8] && this.riskRecord.jiBingYZCDPF1[8] == '2') {
          this.checkedYY = true
        } else {
          this.checkedYY = false
        }
        // const allJBUnchecked =
        //   JSON.stringify(this.riskRecord.jiBingYZCDPF1) ==
        //     JSON.stringify(['0', '0', '0', '0', '0', '0', '0', '0']) &&
        //   JSON.stringify(this.riskRecord.jiBingYZCDPF2) ==
        //     JSON.stringify(['0', '0', '0', '0', '0']) &&
        //   JSON.stringify(this.riskRecord.jiBingYZCDPF3) == JSON.stringify(['0', '0', '0', '0'])
        // this.checkedYY = allJBUnchecked ? 1 : 0
      }
    },

    // 当任何复选框状态改变时调用
    updateCheckedStatus(type) {
      this.initCheckboxStatus(type)
    },

    // 正常营养状态复选框改变时调用
    handleNormalChange(type, value) {
      if (type == 'yy') {
        if (value === 1) {
          this.riskRecord.yingYangSSZKPF1 = ['0', '0', '2']
          this.riskRecord.yingYangSSZKPF2 = ['0', '0']
          this.riskRecord.yingYangSSZKPF3 = ['0', '0', '0']
        } else if (value === 0) {
          this.riskRecord.yingYangSSZKPF1.pop()
        }
      } else if (type == 'jb') {
        if (value === 1) {
          this.riskRecord.jiBingYZCDPF1 = ['0', '0', '0', '0', '0', '0', '0', '0', '2']
          this.riskRecord.jiBingYZCDPF2 = ['0', '0', '0', '0', '0']
          this.riskRecord.jiBingYZCDPF3 = ['0', '0', '0', '0']
        } else if (value === 0) {
          this.riskRecord.jiBingYZCDPF1.pop()
        }
      }
    },
    handleItemClick(item, index) {
      this.activeIndex = index
      this.addScreeningShow = true
      this.delArr(this.yyscList[index])
      // this.delArr(item)
    },

    delArr(arr) {
      // 全部转成数组
      this.riskRecord = arr
      if (!Array.isArray(this.riskRecord.yingYangSSZKPF1)) {
        this.riskRecord.yingYangSSZKPF1 = this.riskRecord.yingYangSSZKPF1.split('') || ''
      }
      if (!Array.isArray(this.riskRecord.yingYangSSZKPF2)) {
        this.riskRecord.yingYangSSZKPF2 = this.riskRecord.yingYangSSZKPF2.split('') || ''
      }
      if (!Array.isArray(this.riskRecord.yingYangSSZKPF3)) {
        this.riskRecord.yingYangSSZKPF3 = this.riskRecord.yingYangSSZKPF3.split('') || ''
      }
      if (!Array.isArray(this.riskRecord.jiBingYZCDPF1)) {
        this.riskRecord.jiBingYZCDPF1 = this.riskRecord.jiBingYZCDPF1.split('') || ''
      }
      if (!Array.isArray(this.riskRecord.jiBingYZCDPF2)) {
        this.riskRecord.jiBingYZCDPF2 = this.riskRecord.jiBingYZCDPF2.split('') || ''
      }
      if (!Array.isArray(this.riskRecord.jiBingYZCDPF3)) {
        this.riskRecord.jiBingYZCDPF3 = this.riskRecord.jiBingYZCDPF3.split('') || ''
      }
      this.checked = this.riskRecord.tiZhong < 0 ? true : false
      this.initCheckboxStatus('yy')
      this.initCheckboxStatus('jb')
      // if (arr.dyyscAutopfVoList) {
      //   this.dyyscAutopfVoList = arr.dyyscAutopfVoList
      // }
      if (arr.ylYyscdpVo) {
        this.ylYyscdpVo = arr.ylYyscdpVo
        if (arr.ylYyscdpVo?.yingYangSYY != null) {
          this.ylYyscdpVo.yingYangSYY = String(arr.ylYyscdpVo.yingYangSYY).split('')
        } else {
          this.ylYyscdpVo.yingYangSYY = []
        }
        if (arr.ylYyscdpVo?.huShiZYY != null) {
          this.ylYyscdpVo.huShiZYY = String(arr.ylYyscdpVo.huShiZYY).split('')
        } else {
          this.ylYyscdpVo.huShiZYY = []
        }
        if (arr.ylYyscdpVo?.jiaoChaDPYY != null) {
          this.ylYyscdpVo.jiaoChaDPYY = String(arr.ylYyscdpVo.jiaoChaDPYY).split('')
        } else {
          this.ylYyscdpVo.jiaoChaDPYY = []
        }
      }
      console.log(this.ylYyscdpVo)
    },
    handleSaveClick() {
      this.saveDYyscReturnWithID()
    }
  }
}
</script>

<style lang="scss" scoped>
.nutritional-box-view {
  height: 100%;
  display: flex;
}

.box-left-panel {
  width: 21%;
  background-color: #eff3fb;
  border-radius: 4px 0 0 4px;
  margin-right: -10px;

  .left-screen-sheet {
    padding: 10px;
    border-radius: 4px;
    height: 97%;
    border: 1px solid #e0e0e0;
    margin: 12px;

    .screen-header {
      margin-bottom: 12px;

      .screen-title {
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        border-left: 4px solid #356ac5;
        padding-left: 6px;
      }
    }

    .timeline-box {
      margin-top: 10px;
    }
  }

  .el-icon-plus {
    font-size: 14px;
    color: #356ac5;
    font-weight: bold;
    float: right;
    cursor: pointer;
  }

  ::v-deep .el-timeline-item {
    padding-bottom: 12px;
  }

  ::v-deep .el-timeline-item__node--normal {
    width: 7px;
    height: 7px;
    top: 3px;
    box-shadow: 2px 2px 2px 1px rgba(0, 0, 0, 0.1);
  }

  ::v-deep .el-timeline-item__tail {
    left: 1px;
  }

  ::v-deep .el-timeline-item__wrapper {
    top: -3px;
    font-size: 12px;
    padding-left: 24px;
    cursor: pointer;
  }

  .active {
    background: #356ac5;
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    margin-left: -8px;
    padding-left: 10px;
  }
}

.box-right-panel {
  width: 81%;
  background-color: #eff3fb;
  border-radius: 4px 0 0 4px;

  .right-screen-sheet {
    padding: 0 10px;
    border-radius: 4px;
    height: 97%;
    border: 1px solid #e0e0e0;
    margin: 12px;
    overflow: auto;
    padding-bottom: 10px;

    .right-header {
      padding: 5px 10px;
      height: 40px;
      border-bottom: 1px solid #dcdfe6;
      margin: 0 -10px;
      margin-bottom: 12px;
      // background-color: #eaf0f9;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 14px;
        font-weight: bold;
        color: #303133;
        display: inline-block;
        border-left: 4px solid #356ac5;
        padding-left: 8px;
        height: 14px;
        line-height: 14px;
      }

      .title1 {
        font-size: 14px;
        color: #303133;
        display: inline-block;
        height: 14px;
        line-height: 14px;
        border-left: none;
        font-weight: normal;
        padding-left: 0;
      }

      .actions {
        display: flex;
        gap: 10px;

        .el-button {
          // padding: 5px 12px;
          font-size: 12px;
        }
      }
    }

    .right-header-none {
      margin-bottom: 0;
      border-bottom: none;
    }

    .top-box {
      padding: 0 10px;
      margin-bottom: 10px;
    }

    .right-header-rate {
      background: #eaf0f9;
      margin: 0;
      margin-bottom: 12px;
      border-top: 1px solid #dcdfe6;
    }

    .right-header-rate:first-child {
      border-top: none;
    }

    .right-header-rate:last-child {
      border-top: none;
    }

    .right-header-comment {
      display: block;
      height: auto;
      border-bottom: none;
      margin-bottom: 0;

      .right-header-comment-title {
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .item-comment {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 8px;
        padding-left: 5px;

        .comment-input {
          display: inline-block;
          // width: 130px;
          margin: 0 5px;
        }

        .remark {
          display: inline-block;
          width: 70px;
          margin-right: 12px;
          text-align: right;
        }
      }

      .item-comment-describe {
        justify-content: space-between;
        padding-left: 18px;
        margin: 10px 0;

        .blo70 {
          display: inline-block;
          width: 70px;
        }
      }
    }

    .item-section {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding-left: 15px;

      div {
        flex: 1;

        span {
          margin-right: 8px;
          text-align: right;
          width: 65px;
          display: inline-block;
        }

        .select-input {
          width: 174px;
        }
      }

      ::v-deep .el-input .el-input__inner {
        width: 100%;
      }
    }

    .top-fix {
      position: sticky;
      top: 0;
      background: #eff3fb;
      z-index: 100;
    }

    .left-tips {
      display: flex;
      font-size: 14px;
      position: sticky;
      top: 0;
      z-index: 10;
      margin-bottom: 14px;

      .el-icon-warning {
        font-size: 16px;
        color: #356ac5;
        margin-right: 4px;
        margin-top: -2px;
      }

      .el-icon-content {
        display: flex;
        margin-top: -3px;
        margin-bottom: 6px;

        .el-icon-rate {
          width: 25%;
          line-height: 22px;
        }

        .el-icon-title {
          width: 95%;
          display: inline-block;
          line-height: 22px;
        }
      }
    }

    .left-tips:last-child {
      margin-bottom: 0;
    }
  }

  ::v-deep .el-descriptions-item__label.is-bordered-label {
    background: #eaf0f9;
    border: 1px solid #e0e0e0;
    width: 10% !important;
    text-align: right;
  }

  ::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
    line-height: 1 !important;
  }
  ::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
    padding: 8px 10px;
  }
}

::v-deep .el-input .el-input__inner {
  width: 174px;
}

::v-deep .el-checkbox {
  margin-right: 10px;
}
::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background: #356ac5;
  border-color: #356ac5;
}
// ::v-deep .el-input.is-disabled .el-input__inner {
//   background: #fff;
// }
</style>
