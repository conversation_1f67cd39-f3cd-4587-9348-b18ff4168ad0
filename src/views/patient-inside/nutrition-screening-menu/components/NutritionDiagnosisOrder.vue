<template>
  <div class="nutritional-box-view">
    <!-- 左侧列表 -->
    <div class="box-left-panel">
      <div class="left-screen-sheet">
        <div class="screen-header">
          <span class="screen-title">营养诊疗执行单</span>
          <i class="el-icon-plus"></i>
        </div>
        <div class="aaa0">
          <div class="aaatitle">执行单名称</div>
          <div class="aaa">
            <div
              class="bbb"
              :class="index == activeIndex ? 'menuActive' : ''"
              v-for="(item, index) in nutritionalList"
              :key="index"
              @click="handleItemClick(item, index)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧列表 -->
    <div class="box-right-panel">
      <div class="right-screen-sheet">
        <div class="right-header top-fix">
          <div class="title">营养诊疗执行单</div>
          <div class="actions">
            <el-button type="primary" @click="handleSaveClick">保存</el-button>
            <el-button type="primary" @click="handleSaveClick">删除</el-button>
            <el-button type="primary" @click="handleSaveClick">打印</el-button>
          </div>
        </div>
        <!-- 1. -->
        <div class="item-section">
          <div>
            <span>调查者:</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>调查时间:</span>
            <el-date-picker
              style="width: 174px"
              v-model="patientRecord.bingRenJBSJ"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期时间"
              class="select-input"
            ></el-date-picker>
          </div>
          <div>
            <span>开始时间:</span>
            <el-date-picker
              style="width: 174px"
              v-model="patientRecord.bingRenJBSJ"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期时间"
              class="select-input"
            ></el-date-picker>
          </div>
          <div>
            <span>结束时间:</span>
            <el-date-picker
              style="width: 174px"
              v-model="patientRecord.bingRenJBSJ"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期时间"
              class="select-input"
            ></el-date-picker>
          </div>
        </div>
        <!-- 2. -->
        <div class="right-header right-header-none">
          <div class="title">患者基本信息</div>
        </div>
        <!-- 3. -->
        <div class="item-section">
          <div>
            <span>患者姓名:</span>
            <el-input disabled v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>病案号：:</span>
            <el-input disabled v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>性别:</span>
            <el-radio v-model="patientRecord.bingRenJBXB" label="1">男</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">女</el-radio>
          </div>
          <div>
            <span>入院诊断:</span>
            <el-input disabled v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
        </div>
        <div class="item-section">
          <div>
            <span>次要诊断:</span>
            <el-input v-model="patientRecord.bingRenJBXX" style="width: 93%"></el-input>
          </div>
        </div>
        <!-- 4. -->
        <div class="item-section">
          <div>
            <span style="line-height: 16px">营养风险筛查评分:</span>
            <el-input disabled v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span style="line-height: 16px">GLIM营养不良诊断:</span>
            <el-radio v-model="patientRecord.bingRenJBXB" label="1">是</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">否</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="3">营养不良</el-radio>
          </div>
          <div></div>
          <div></div>
        </div>
        <!-- 8. -->
        <div class="right-header right-header-none">
          <div class="title">
            肠外营养支持(每天)
            <span style="font-weight: normal">
              (总热量=非蛋白质能量+总氮量*25;蛋白质=总氮量*6.25)
            </span>
          </div>
        </div>
        <!-- 9. -->
        <div class="item-section">
          <div>
            <span style="text-align: left">单瓶输注:</span>
            <el-radio v-model="patientRecord.bingRenJBXB" label="1">是</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">否</el-radio>
          </div>
          <div>
            <span style="line-height: 16px">总液体量(ml):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span style="line-height: 16px">剩余液体量(ml):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>制剂名称:</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
        </div>
        <div class="item-section">
          <div>
            <span>能量(kcal):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>蛋白质(g):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div></div>
          <div></div>
        </div>
        <div class="item-section tableaaa">
          <span class="titlebbb" style="width: 37%">制剂名称</span>
          <span class="titlebbb" style="width: 21%">一次用量</span>
          <span class="titlebbb" style="width: 21%">能量</span>
          <span class="titlebbb" style="width: 21%">蛋白质</span>
        </div>
        <!-- 10. -->
        <div class="right-header" style="border-bottom: none; margin-right: 0px">
          <div>
            <span class="title" style="border-left: none; padding-left: 5px">肠外营养支持合计</span>
          </div>
          <div>
            <el-button type="primary" plain @click="handleSaveClick">自动计算</el-button>
            <el-button type="primary" plain @click="handleSaveClick">肠外营养手动录入</el-button>
          </div>
        </div>
        <div class="item-section">
          <div>
            <span>能量(kcal):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>蛋白质(g):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div></div>
          <div></div>
        </div>
        <div class="right-header" style="border-bottom: none; margin-right: 0px">
          <div>
            <span class="title">肠内营养支持(每天)</span>
            <span>(肠内营养+医院用食品量)*实际肠内营养入量比例)</span>
          </div>
        </div>
        <div class="tableccc">
          <div class="tableddd">
            <div>
              <span>肠内营养:</span>
              <el-radio v-model="patientRecord.bingRenJBXB" label="1">是</el-radio>
              <el-radio v-model="patientRecord.bingRenJBXB" label="2">否</el-radio>
            </div>
            <div>
              <el-button type="primary" plain @click="handleSaveClick">肠外营养手动录入</el-button>
            </div>
          </div>
          <div style="width: 100%; padding: 10px 0; background: #eff3fb">
            <div class="tableaaa">
              <span class="titlebbb" style="width: 37%; display: inline-block">制剂名称</span>
              <span class="titlebbb" style="width: 21%; display: inline-block">一次用量</span>
              <span class="titlebbb" style="width: 21%; display: inline-block">能量</span>
              <span class="titlebbb" style="width: 21%; display: inline-block">蛋白质</span>
            </div>
          </div>
        </div>
        <div class="tableccc">
          <div class="tableddd">
            <div>
              <span>医院用食品:</span>
              <el-radio v-model="patientRecord.bingRenJBXB" label="1">是</el-radio>
              <el-radio v-model="patientRecord.bingRenJBXB" label="2">否</el-radio>
            </div>
            <div>
              <el-button type="primary" plain @click="handleSaveClick">
                医院用食品手动录入
              </el-button>
            </div>
          </div>
          <div style="width: 100%; padding: 10px 0; background: #eff3fb">
            <div class="tableaaa">
              <span class="titlebbb" style="width: 37%; display: inline-block">制剂名称</span>
              <span class="titlebbb" style="width: 21%; display: inline-block">一次用量</span>
              <span class="titlebbb" style="width: 21%; display: inline-block">能量</span>
              <span class="titlebbb" style="width: 21%; display: inline-block">蛋白质</span>
            </div>
          </div>
        </div>
        <div class="item-section">
          <div>肠内营养支持合计：</div>
          <div>
            <span>能量(kcal):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>蛋白质(g):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>(占肠内营养+医院用食品比例)</div>
        </div>
        <div class="item-section">
          <div>实际肠内营养入量：</div>
          <div>
            <el-radio v-model="patientRecord.bingRenJBXB" label="1">0%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">25%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">50%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">75%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">100%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">120%</el-radio>
          </div>
          <div></div>

          <div>(占肠内营养+医院用食品比例)</div>
        </div>
        <div class="item-section">
          <div>实际肠内营养支持合计：：</div>
          <div>
            <span>能量(kcal):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>蛋白质(g):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div></div>
        </div>
        <div class="right-header right-header-none">
          <div class="title">
            饮食摄入情况(每天)
            <span style="font-weight: normal">(饮食量*实际饮食摄入量比例)</span>
          </div>
        </div>
        <div class="item-section">
          <div>
            <span>饮食医嘱名称:</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>能量(kcal):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>蛋白质(g):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div></div>
        </div>
        <div class="item-section">
          <div>进食量(占送餐总量比例)</div>
          <div>
            <el-radio v-model="patientRecord.bingRenJBXB" label="1">0%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">25%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">50%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">75%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">100%</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">120%</el-radio>
          </div>
          <div></div>
          <div></div>
          <div></div>
        </div>
        <div class="item-section">
          <div>进食量合计：</div>
          <div>
            <span>能量(kcal):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>蛋白质(g):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div></div>
        </div>
        <div class="right-header" style="border-bottom: none; margin-right: 0px">
          <div>
            <div class="title">
              营养支持总量合计
              <span style="font-weight: normal">
                (肠外营养支持合计+肠内营养支持合计+进食量合计)
              </span>
            </div>
          </div>
          <div>
            <el-button type="primary" plain @click="handleSaveClick">自动计算</el-button>
          </div>
        </div>
        <div class="item-section">
          <div>
            <span>能量(kcal):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>蛋白质(g):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div></div>
          <div></div>
        </div>
        <div class="right-header" style="border-bottom: none; margin-right: 0px">
          <div>
            <div class="title">目标摄入量(每天)</div>
          </div>
        </div>
        <div class="item-section">
          <div>
            <span>能量(kcal):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div>
            <span>蛋白质(g):</span>
            <el-input v-model="patientRecord.bingRenJBXX" class="select-input"></el-input>
          </div>
          <div></div>
          <div></div>
        </div>
        <div class="right-header" style="border-bottom: none; margin-right: 0px">
          <div>
            <div class="title">肠内营养耐受情况</div>
          </div>
        </div>
        <div class="item-section">
          <div>
            <span>腹胀/腹痛:</span>
            <el-radio v-model="patientRecord.bingRenJBXB" label="1">无</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">轻度</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="3">
              明显腹胀或能自行缓解的腹痛
            </el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="4">
              严重腹胀或不能自行缓解的腹痛
            </el-radio>
          </div>
        </div>
        <div class="item-section">
          <div>
            <span>恶心/呕吐:</span>
            <el-radio v-model="patientRecord.bingRenJBXB" label="1">无或持续肠胃减压</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">有恶心无呕吐</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="3">
              有恶心呕吐，但无需肠胃减压
            </el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="4">呕吐切需肠胃减压</el-radio>
          </div>
          <div></div>
          <div></div>
        </div>
        <div class="item-section">
          <div>
            <span>腹泻:</span>
            <el-radio v-model="patientRecord.bingRenJBXB" label="1">无</el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="2">
              稀便3~4次/天月量<500ml天
            </el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="3">
              稀便≥5次/天月量500ml~1000ml天
            </el-radio>
            <el-radio v-model="patientRecord.bingRenJBXB" label="4">
              稀便≥5次/天月量≥1500ml天
            </el-radio>
          </div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
export default {
  data() {
    return {
      nutritionalList: [{ name: '新增营养诊疗执行单' }],
      checked: true,
      checked1: false,
      checkList: ['营养状况评分错误', '年龄评分错误'],
      options1: [
        {
          value: '选项1',
          label: '黄金糕'
        },
        {
          value: '选项2',
          label: '双皮奶'
        },
        {
          value: '选项3',
          label: '蚵仔煎'
        },
        {
          value: '选项4',
          label: '龙须面'
        },
        {
          value: '选项5',
          label: '北京烤鸭'
        }
      ],
      value1: '',
      checkboxList1: [
        '骶骨折',
        '长期血液透析',
        '慢性疾病有急性并发性',
        '糖尿病',
        '肝硬化',
        '慢性阻塞性疾病',
        '恶性肿瘤',
        '其他'
      ],
      checkboxList2: ['腹部大手术', '血液系统恶性肿瘤', '重度肺炎', '卒中', '其他'],
      checkboxList3: ['头部损伤', '骨髓移植', '重症监护的患者(APACHE>10)', '其他'],
      patientRecord: {
        bingRenJBXX: '李超超',
        bingRenJBSJ: '2023-07-03 10:03',
        bingRenJBXB: '1'
      },
      activities1: [
        {
          content: '2023-07-03 10:03  营养风险筛查  2分',
          type: 'primary'
        },
        {
          content: '2023-07-03 10:03 营养风险筛查 3分',
          type: 'primary'
        },
        {
          content: '2023-07-03 10:03 营养风险筛查 4分',
          type: 'primary'
        }
      ],
      activities2: [
        {
          content: '2023-11-24 10:01 NRS 2002',
          type: 'primary'
        }
      ],
      activities3: [
        {
          content: '2023-11-24 10:01 Nutric',

          type: 'primary'
        }
      ],
      activities4: [
        {
          content: '2023-11-24 10:01 Strong Kids',
          type: 'primary'
        }
      ],
      activeIndex: 0
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    // 页面初始化
    async init() {},
    handleItemClick(item, index) {
      this.activeIndex = index
    },
    handleSaveClick() {}
  }
}
</script>

<style lang="scss" scoped>
.nutritional-box-view {
  height: 100%;
  display: flex;
}

.box-left-panel {
  width: 21%;
  background-color: #eff3fb;
  border-radius: 4px 0 0 4px;
  margin-right: -10px;

  .left-screen-sheet {
    padding: 10px;
    border-radius: 4px;
    height: 97%;
    border: 1px solid #e0e0e0;
    margin: 12px;

    .screen-header {
      margin-bottom: 12px;

      .screen-title {
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        border-left: 4px solid #356ac5;
        padding-left: 6px;
      }
    }

    .timeline-box {
      margin-top: 10px;
    }
  }

  .el-icon-plus {
    font-size: 14px;
    color: #356ac5;
    font-weight: bold;
    float: right;
    cursor: pointer;
  }

  ::v-deep .el-timeline-item {
    padding-bottom: 12px;
  }

  ::v-deep .el-timeline-item__node--normal {
    width: 7px;
    height: 7px;
    top: 3px;
    box-shadow: 2px 2px 2px 1px rgba(0, 0, 0, 0.1);
  }

  ::v-deep .el-timeline-item__tail {
    left: 1px;
  }

  ::v-deep .el-timeline-item__wrapper {
    top: -3px;
    font-size: 12px;
    padding-left: 24px;
    cursor: pointer;
  }

  .active {
    background: #356ac5;
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    margin-left: -8px;
    padding-left: 10px;
  }
}

.box-right-panel {
  width: 81%;
  background-color: #eff3fb;
  border-radius: 4px 0 0 4px;

  .right-screen-sheet {
    padding: 0 10px;
    border-radius: 4px;
    height: 97%;
    border: 1px solid #e0e0e0;
    margin: 12px;
    overflow: auto;
    padding-bottom: 10px;

    .right-header {
      padding: 5px 10px;
      height: 40px;
      border-bottom: 1px solid #dcdfe6;
      margin: 0 -10px;
      margin-bottom: 12px;
      // background-color: #eaf0f9;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title {
        font-size: 14px;
        font-weight: bold;
        color: #303133;
        display: inline-block;
        border-left: 4px solid #356ac5;
        padding-left: 8px;
        height: 14px;
        line-height: 14px;
      }

      .title1 {
        font-size: 14px;
        color: #303133;
        display: inline-block;
        height: 14px;
        line-height: 14px;
        border-left: none;
        font-weight: normal;
        padding-left: 0;
      }

      .actions {
        display: flex;
        gap: 10px;

        .el-button {
          // padding: 5px 12px;
          font-size: 12px;
        }
      }
    }

    .right-header-none {
      margin-bottom: 0;
      border-bottom: none;
    }

    .top-box {
      padding: 0 10px;
      margin-bottom: 10px;
    }

    .right-header-rate {
      background: #eaf0f9;
      margin: 0;
      margin-bottom: 12px;
      border-top: 1px solid #dcdfe6;
    }

    .right-header-rate:first-child {
      border-top: none;
    }

    .right-header-rate:last-child {
      border-top: none;
    }

    .right-header-comment {
      display: block;
      height: auto;
      border-bottom: none;
      margin-bottom: 0;

      .right-header-comment-title {
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .item-comment {
        display: flex;
        align-items: center;
        justify-content: space-around;
        margin-bottom: 8px;
        padding-left: 5px;

        .comment-input {
          display: inline-block;
          // width: 130px;
          margin: 0 5px;
        }

        .remark {
          display: inline-block;
          width: 70px;
          margin-right: 12px;
          text-align: right;
        }
      }

      .item-comment-describe {
        justify-content: space-between;
        padding-left: 18px;
        margin: 10px 0;

        .blo70 {
          display: inline-block;
          width: 70px;
        }
      }
    }

    .item-section {
      display: flex;
      align-items: center;
      margin-bottom: 8px;
      padding-left: 15px;

      div {
        flex: 1;
        display: flex;
        align-items: center;

        span {
          margin-right: 8px;
          text-align: right;
          width: 70px;
          display: inline-block;
        }

        .select-input {
          width: 174px;
        }
      }

      ::v-deep .el-input .el-input__inner {
        // width: 100%;
      }
    }

    .top-fix {
      position: sticky;
      top: 0;
      background: #eff3fb;
      z-index: 100;
    }

    .left-tips {
      display: flex;
      font-size: 14px;
      position: sticky;
      top: 0;
      z-index: 10;
      margin-bottom: 14px;

      .el-icon-warning {
        font-size: 16px;
        color: #356ac5;
        margin-right: 4px;
        margin-top: -2px;
      }

      .el-icon-content {
        display: flex;
        margin-top: -3px;
        margin-bottom: 6px;

        .el-icon-rate {
          width: 25%;
          line-height: 22px;
        }

        .el-icon-title {
          width: 95%;
          display: inline-block;
          line-height: 22px;
        }
      }
    }

    .left-tips:last-child {
      margin-bottom: 0;
    }
  }

  ::v-deep .el-descriptions-item__label.is-bordered-label {
    background: #eaf0f9;
    border: 1px solid #e0e0e0;
    width: 10% !important;
    text-align: right;
  }

  ::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
    line-height: 1 !important;
  }
  ::v-deep .el-descriptions .is-bordered .el-descriptions-item__cell {
    padding: 8px 10px;
  }
}

::v-deep .el-input .el-input__inner {
  width: 174px;
}

::v-deep .el-checkbox {
  margin-right: 10px;
}

::v-deep .el-radio__input.is-disabled.is-checked .el-radio__inner {
  background: #356ac5;
  border-color: #356ac5;
}

.aaa0 {
  height: 95%;
  border: 1px solid #e0e0e0;
  overflow: hidden;
}
.aaatitle {
  font-size: 12px;
  line-height: 35px;
  text-align: left;
  padding-left: 8px;
  // font-weight: bold;
}
.aaa {
  height: 100%;
  overflow: auto;
  // background: #fff;
}
.bbb {
  line-height: 35px;
  text-align: left;
  padding-left: 8px;
  cursor: pointer;
}
.menuActive {
  border-radius: 2px;
  background: #356ac5;
  color: #fff;
}

.tableaaa {
  width: 76%;
  line-height: 32px;
  background: #eaf0f9;
  margin-left: 15px;
  padding-left: 0 !important;
}
.titlebbb {
  border: 1px solid #dadee5;
  padding-left: 12px;
  border-right: none;
}
.titlebbb:last-child {
  border-right: 1px solid #dadee5 !important;
}
.tableccc {
  display: flex;
  flex-direction: column;
  margin-bottom: 8px;
  align-items: center;
  justify-content: space-between;
  border: 1px solid #dadee5;
  background: #eaf0f9;
}
.tableddd {
  width: 100%;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid #dadee5;
  padding: 4px 10px;
}
</style>
