<template>
  <div class="temperature-sheet-view">
    <div ref="chart1" style="width: 100%; height: 400px; margin-top: 50px"></div>
    <div ref="chart2" style="width: 100%; height: 400px; margin-top: 50px"></div>
    <div ref="chart3" style="width: 100%; height: 400px; margin-top: 50px"></div>
    <div ref="chart4" style="width: 100%; height: 400px; margin-top: 50px"></div>
    <div ref="chart5" style="width: 100%; height: 400px; margin-top: 50px"></div>
    <div ref="chart6" style="width: 100%; height: 400px; margin-top: 50px"></div>
  </div>
</template>

<script>
import { format, parseISO, addDays, startOfWeek, endOfWeek, differenceInDays } from 'date-fns'
import { mapState } from 'vuex'
import * as echarts from 'echarts'
export default {
  data() {
    return {
      option1: {
        title: {
          text: '营养风险筛查评分趋势图',
          left: 'center',
          top: '0%',
          textStyle: {
            fontSize: 20
          }
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
          }
        ]
      },
      option2: {
        title: {
          text: '营养诊疗执行单每日热量蛋白质摄入趋势图',
          left: 'center',
          top: '0%',
          textStyle: {
            fontSize: 20
          }
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['Email', 'Union Ads', 'Video Ads', 'Direct', 'Search Engine'],
          top: '8%',
          left: 'center'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        toolbox: {
          feature: {
            saveAsImage: {}
          }
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: 'Email',
            type: 'line',
            stack: 'Total',
            data: [120, 132, 101, 134, 90, 230, 210]
          },
          {
            name: 'Union Ads',
            type: 'line',
            stack: 'Total',
            data: [220, 182, 191, 234, 290, 330, 310]
          },
          {
            name: 'Video Ads',
            type: 'line',
            stack: 'Total',
            data: [150, 232, 201, 154, 190, 330, 410]
          },
          {
            name: 'Direct',
            type: 'line',
            stack: 'Total',
            data: [320, 332, 301, 334, 390, 330, 320]
          },
          {
            name: 'Search Engine',
            type: 'line',
            stack: 'Total',
            data: [820, 932, 901, 934, 1290, 1330, 1320]
          }
        ]
      },
      option3: {
        title: {
          text: 'bmi指数趋势图',
          left: 'center',
          top: '0%',
          textStyle: {
            fontSize: 20
          }
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [120, 200, 150, 80, 70, 110, 130],
            type: 'bar'
          }
        ]
      },
      option4: {
        title: {
          text: '全周期体重趋势图',
          left: 'center',
          top: '0%',
          textStyle: {
            fontSize: 20
          }
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
          }
        ]
      },
      option5: {
        title: {
          text: '白蛋白趋势图',
          left: 'center',
          top: '0%',
          textStyle: {
            fontSize: 20
          }
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
          }
        ]
      },
      option6: {
        title: {
          text: '血红蛋白指标趋势图',
          left: 'center',
          top: '0%',
          textStyle: {
            fontSize: 20
          }
        },
        xAxis: {
          type: 'category',
          data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: [150, 230, 224, 218, 135, 147, 260],
            type: 'line'
          }
        ]
      }
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.initChart()
  },
  methods: {
    // 页面初始化
    async init() {},
    async initChart() {
      const chart1 = echarts.init(this.$refs.chart1)
      const chart2 = echarts.init(this.$refs.chart2)
      const chart3 = echarts.init(this.$refs.chart3)
      const chart4 = echarts.init(this.$refs.chart4)
      const chart5 = echarts.init(this.$refs.chart5)
      const chart6 = echarts.init(this.$refs.chart6)
      chart1.setOption(this.option1)
      chart2.setOption(this.option2)
      chart3.setOption(this.option3)
      chart4.setOption(this.option4)
      chart5.setOption(this.option5)
      chart6.setOption(this.option6)
    }
  }
}
</script>

<style lang="scss" scoped>
.temperature-sheet-view {
  padding: 8px;
  height: 100%;
  overflow-y: auto;
}
</style>
