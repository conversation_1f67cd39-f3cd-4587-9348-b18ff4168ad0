<template>
  <div class="nutritional-sheet-view">
    <!-- 左侧列表 -->
    <div class="left-panel">
      <!-- 左上列表 -->
      <div class="left-top">
        <div class="top-header">
          <span class="top-title">病人营养情况</span>
        </div>
        <div>
          <el-descriptions class="top-box" title="" :column="1" border>
            <el-descriptions-item>
              <template slot="label">身高:</template>
              <div class="top-item">
                <span>150.50</span>
                <span>cm</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">体重:</template>
              <div class="top-item">
                <span>35.0</span>
                <span>kg</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">BMI:</template>
              <div class="top-item">
                <span>16</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">握力:</template>
              <div class="top-item">
                <span>12</span>
                <span>kg</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">饮食/能量:</template>
              <div class="top-item">
                <span>351.2</span>
                <span>kcal</span>
              </div>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">饮食/蛋白质:</template>
              <div class="top-item">
                <span>365.1</span>
                <span>g</span>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <!-- 左下列表 -->
      <div class="left-section">
        <div class="section-header">
          <span class="section-title">病人营养管理</span>
        </div>
        <div class="section-list">
          <div
            v-for="(item, index) in records"
            :key="index"
            class="section-item"
            :class="{ active: activeIndex == index }"
            @click="handleItemClick(item, index)"
          >
            <div class="record-name">{{ item.mingCheng }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧列表 -->
    <div class="right-panel">
      <NutritionalRiskScreening v-if="activeItem == '营养风险筛查'" />
      <NutritionDiagnosisOrder v-if="activeItem == '营养诊疗执行单'" />
      <NutritionalConsultation v-if="activeItem == '营养会诊'" />
      <NutritionalMedicalFoods v-if="activeItem == '营养病历及营养食品'" />
      <NutritionalAssessmentSheet v-if="activeItem == '营养评估单'" />
      <NutritionalChart v-if="activeItem == '营养图表'" />
    </div>
  </div>
</template>

<script>
import NutritionalRiskScreening from './components/NutritionalRiskScreening'
import NutritionDiagnosisOrder from './components/NutritionDiagnosisOrder'
import NutritionalConsultation from './components/NutritionalConsultation'
import NutritionalMedicalFoods from './components/NutritionalMedicalFoods'
import NutritionalAssessmentSheet from './components/NutritionalAssessmentSheet'
import NutritionalChart from './components/NutritionalChart'
import { mapState } from 'vuex'
export default {
  components: {
    NutritionalRiskScreening,
    NutritionDiagnosisOrder,
    NutritionalConsultation,
    NutritionalMedicalFoods,
    NutritionalAssessmentSheet,
    NutritionalChart
  },
  data() {
    return {
      records: [
        { mingCheng: '营养风险筛查' },
        { mingCheng: '营养诊疗执行单' },
        { mingCheng: '营养会诊' },
        { mingCheng: '营养病历及营养食品' },
        { mingCheng: '营养评估单' },
        { mingCheng: '营养图表' }
      ],
      activeIndex: 0,
      activeItem: '营养风险筛查'
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    // 页面初始化
    async init() {},
    handleItemClick(item, index) {
      this.activeItem = item.mingCheng
      this.activeIndex = index
    }
  }
}
</script>

<style lang="scss" scoped>
.nutritional-sheet-view {
  background-color: #fff;
  padding: 8px;
  height: 100%;
  display: flex;
}

.left-panel {
  width: 14%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 4px 0 0 4px;
  background: #fff;
  margin-right: 10px;

  .left-top {
    padding: 10px;
    background-color: #eff3fb;
    margin-bottom: 10px;
    border-radius: 4px;

    .top-header {
      margin-bottom: -2px;

      .top-title {
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        border-left: 4px solid #356ac5;
        padding-left: 6px;
      }
    }

    .top-box {
      margin-top: 10px;

      .top-item {
        width: 90%;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
    }
  }

  .left-section {
    height: 100%;
    background-color: #eff3fb;
    border-radius: 4px;

    .section-header {
      padding: 10px;

      .section-title {
        font-weight: bold;
        font-size: 14px;
        color: #303133;
        border-left: 4px solid #356ac5;
        padding-left: 6px;
      }
    }

    .section-list {
      flex: 1;
      overflow-y: auto;

      .section-item {
        padding: 10px 20px;

        cursor: pointer;
        transition: background-color 0.3s;

        .record-name {
          font-size: 14px;
        }
      }

      .active {
        color: #3d6bbb;
        background: #cbdbf8;
        border-right: 3px solid #356ac5;
      }
    }
  }

  ::v-deep .el-descriptions-item__label.is-bordered-label {
    background: #eaf0f9;
    border: 1px solid #e0e0e0;
    width: 50%;
    text-align: right;
  }

  ::v-deep .el-descriptions__body .el-descriptions__table .el-descriptions-item__cell {
    line-height: 1;
  }
}

.right-panel {
  background-color: #eff3fb;
  width: 86%;
  border-radius: 4px;
}
</style>
