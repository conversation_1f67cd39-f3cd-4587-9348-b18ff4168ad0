<template>
  <div class="container">
    <div class="content">
      <el-form
        :model="form"
        :rules="rules"
        ref="messageForm"
        label-width="70px"
        label-position="right"
      >
        <el-form-item label="手机号：" prop="lianXiDH">
          <el-input v-model="form.lianXiDH"></el-input>
        </el-form-item>

        <el-form-item label="内容：">
          <el-button plain size="medium" @click="templateVisible = true">模板</el-button>
        </el-form-item>

        <el-form-item prop="muBan">
          <el-input
            v-model="form.muBan"
            type="textarea"
            placeholder="请选择模板"
            readonly
          ></el-input>
        </el-form-item>

        <el-form-item
          v-if="type == 1 || type == 3 || type == 4 || type == 5"
          label="时间："
          prop="shiJian"
        >
          <el-date-picker
            v-model="form.shiJian"
            type="datetime"
            placeholder="选择日期时间"
            value-format="yyyy-MM-dd HH:mm"
            format="yyyy-MM-dd HH:mm"
          ></el-date-picker>
        </el-form-item>

        <el-form-item v-if="type == 1 || type == 4" label="相关资料：" prop="xiangGuanZL">
          <el-input v-model="form.xiangGuanZL"></el-input>
        </el-form-item>

        <el-form-item
          v-if="type == 1 || type == 2 || type == 4 || type == 5"
          label="地点："
          prop="diDian"
        >
          <el-input v-model="form.diDian"></el-input>
        </el-form-item>

        <el-form-item v-if="type == 1 || type == 2 || type == 4" label="咨询电话：" prop="hotLine">
          <el-input v-model="form.hotLine"></el-input>
        </el-form-item>

        <el-form-item style="margin-top: 30px">
          <el-button type="primary" size="medium" @click="submitForm">发送</el-button>
          <el-button type="primary" size="medium" @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 模板列表 -->
    <div class="stock-class">
      <el-dialog
        :visible.sync="templateVisible"
        title="模板列表"
        width="800px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <el-table :data="Templates" stripe border max-height="480px">
          <el-table-column prop="ID" width="80" align="center" label="序号"></el-table-column>
          <el-table-column prop="mc" width="80" align="center" label="名称"></el-table-column>
          <el-table-column prop="temp" width="500" align="center" label="内容"></el-table-column>
          <el-table-column prop="" width="102" align="center" label="操作">
            <template #default="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">确定</el-button>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button @click="templateVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { sendInpatientMessage } from '@/api/check-appointment'
import { mapState } from 'vuex'
export default {
  data() {
    // 自定义验证规则
    const validateTemplate = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请选择模板'))
      } else {
        callback()
      }
    }

    const validatePhone = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入手机号'))
      } else if (!/^1[3-9]\d{9}$/.test(value)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }

    return {
      templateVisible: false,
      form: {
        lianXiDH: '',
        muBan: '',
        shiJian: '',
        xiangGuanZL: '',
        diDian: '',
        hotLine: ''
      },
      xiaoXiBH: '',
      type: 0,
      Templates: [
        {
          value: 1,
          mc: '模版一',
          ID: '90048',
          temp: '您好，医师已经电话联系您或者您家人@shiJian来住院，请携带相关资料（@xiangGuanZL）到@diDian来办理住院。办公室联系电话 @lianXiDH，祝早日康复！'
        },
        {
          value: 2,
          mc: '模版二',
          ID: '90049',
          temp: '第一步：门诊楼1楼交费处预存3000。\
  第二步：门诊楼1楼入院准备中心抽血、做心电图\
  第三步：门诊楼1楼入院准备中心预约B超、CT\
  第四步：回家等电话通知，有疑问在工作时间拨打该号码咨询（@lianXiDH，@diDian）\
  注意：办理预住院后，门诊不要挂其他科室门诊，否则影响住院报销，后果自负！'
        },
        {
          value: 3,
          mc: '模版三',
          ID: '90050',
          temp: '请至一号楼一楼入院准备中心完善相关检查，若检查今日无法完成，需要预约，请预约至@shiJian，届时完成后入院。'
        },
        {
          value: 4,
          mc: '模版四',
          ID: '90052',
          temp: '您好，医生已电话联系您或家人@shiJian来复诊，请携带相关资料（@xiangGuanZL）到@diDian来就诊。@lianXiDH'
        },
        {
          value: 5,
          mc: '模版五',
          ID: '90053',
          temp: '医生已安排您住院，请@shijian至@diDian办住院手续。请随身带好行李，医保卡，银行卡以及生活用品换洗衣物等准时入院。请勿佩戴耳饰,项链，手镯等首饰；女士请卸除美甲；陪护需携带被子软枕；备齐各类生活用品（包括水杯 筷子 勺子吸管等）'
        }
      ],
      // 验证规则
      rules: {
        lianXiDH: [{ required: true, validator: validatePhone, trigger: 'blur' }],
        muBan: [{ required: true, validator: validateTemplate, trigger: 'blur' }],
        shiJian: [{ required: true, message: '请选择时间', trigger: 'change' }],
        xiangGuanZL: [{ required: true, message: '请输入相关资料', trigger: 'blur' }],
        diDian: [{ required: true, message: '请输入地点', trigger: 'blur' }],
        hotLine: [{ required: true, message: '请输入咨询电话', trigger: 'blur' }]
      }
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit,
      yongHuID: ({ user }) => user.yongHuID,
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  async mounted() {},
  methods: {
    sendHandle() {
      // 1. 定义各类型必填字段的校验规则
      const validationRules = {
        1: ['lianXiDH', 'shiJian', 'xiangGuanZL', 'diDian', 'hotLine'],
        2: ['lianXiDH', 'diDian', 'hotLine'],
        3: ['lianXiDH', 'shiJian'],
        4: ['lianXiDH', 'shiJian', 'xiangGuanZL', 'diDian', 'hotLine'],
        5: ['lianXiDH', 'shiJian', 'diDian']
      }

      // 2. 公共校验逻辑：检查字段是否为空或空白
      const validateFields = (fields) => {
        return fields.every((field) => this[field]?.trim() !== '')
      }

      // 3. 主逻辑
      if (!this.xiaoXiBH) {
        this.$message.error('请先选择模板')
        return
      }

      const requiredFields = validationRules[this.type]
      if (!requiredFields || !validateFields(requiredFields)) {
        this.$message.error('请填写完整')
        return
      }

      // 4. 防抖处理（避免重复提交）
      this.debouncedSendInpatientMessage?.cancel()
      this.debouncedSendInpatientMessage = _.debounce(() => {
        this.sendInpatientMessage()
      }, 300)
      this.debouncedSendInpatientMessage()
    },
    // 发送短信接口
    async sendInpatientMessage() {
      try {
        const res = await sendInpatientMessage({
          bingLiID: this.bingLiID,
          diDian: this.form.diDian,
          lianXiDH: this.form.hotLine,
          muBan: this.form.muBan,
          shiJian: this.form.shiJian,
          shouJiHmList: [this.form.lianXiDH],
          xiangGuanZL: this.form.xiangGuanZL,
          xiaoXiBH: this.xiaoXiBH,
          yongHuID: this.yongHuID,
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.$message({
            message: '发送成功',
            type: 'success'
          })
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 提交表单
    submitForm() {
      this.$refs.messageForm.validate((valid) => {
        if (valid) {
          if (!this.xiaoXiBH) {
            this.$message.error('请先选择模板')
            return
          }
          this.sendInpatientMessage()
        } else {
          return false
        }
      })
    },

    // 重置表单
    resetForm() {
      this.$refs.messageForm.resetFields()
      this.type = 0
      this.xiaoXiBH = ''
    },

    // 重置操作
    resetHandle() {
      this.lianXiDH = ''
      this.shiJian = ''
      this.xiangGuanZL = ''
      this.diDian = ''
      this.hotLine = ''
      // this.type = 0
      // this.muBan = ''
      // this.xiaoXiBH = ''
    },
    // 选择某一条模板数据
    handleClick(row) {
      let selectedRow = Object.assign({}, row)

      this.type = selectedRow.value
      this.muBan = selectedRow.temp
      this.xiaoXiBH = selectedRow.ID
      this.templateVisible = false

      // 重置相关字段
      this.$refs.messageForm.clearValidate(['shiJian', 'xiangGuanZL', 'diDian', 'hotLine'])
      this.form = {
        ...this.form,
        shiJian: '',
        xiangGuanZL: '',
        diDian: '',
        hotLine: ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.content {
  background-color: #eaf0f9;
  height: 720px;
  padding: 40px 60px;
  border-radius: 4px;

  .query-word {
    color: #171c28;
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 10px;

    .query-word-title {
      width: 70px;
      display: inline-block;
      text-align: right;
      margin-right: 2px;
    }
  }
}

::v-deep .el-input {
  width: 450px;
}
::v-deep .el-textarea__inner {
  width: 450px;
  min-height: 120px !important;
}
::v-deep .el-date-editor.el-date-editor--daterange {
  width: 450px;
}

// 模板列表
.stock-class {
  ::v-deep .el-dialog {
    padding: 6px 10px;
  }

  ::v-deep .el-table--mini .el-table__cell {
    padding: 12px 0;
  }

  .dialog-footer {
    padding-bottom: 15px;
  }

  .radio-group {
    margin-top: -20px;
    margin-bottom: 4px;
  }

  ::v-deep .custom-radio.is-checked .el-radio__label {
    color: #171c28;
  }

  ::v-deep .el-dialog__header {
    // border-bottom: 1px solid #dadee6;
    padding: 10px 14px;
    font: var(--font-medium);
    font-size: var(--font-size-medium);
    color: #171c28;
    display: flex;
    align-items: center;
  }

  ::v-deep .el-dialog__header::before {
    // content: url('~@/assets/images/info.png');
    // // width: 3px;
    // // height: 16px;
    // // background: #356ac5;
    // // margin-right: 6px;
    // position: absolute;
    // top: 7px;
    // left: 6px;
    // transform: scale(0.55);
    position: absolute;
    left: 22px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }

  ::v-deep .el-dialog__title {
    font-weight: bold;
    margin-left: 10px;
    opacity: 0.8;
  }

  ::v-deep .el-pager li {
    margin: 0 4px;
  }
}

.container {
  padding: 12px;
  background-color: #fff;
}
.content {
  background-color: #eaf0f9;
  height: 720px;
  padding: 40px 60px;
  border-radius: 4px;
}

::v-deep .el-form-item__content {
  .el-input,
  .el-textarea,
  .el-date-editor {
    width: 450px;
  }

  .el-textarea__inner {
    min-height: 120px !important;
  }
}
</style>
