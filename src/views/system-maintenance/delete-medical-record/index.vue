<template>
  <div class="container">
    <div class="header">
      <div>
        <div style="display: flex; align-items: center">
          <div class="query-word">请输入病案号：</div>
          <div>
            <el-input v-model="bingAnHao" @change="onQuery"></el-input>
          </div>
          <div class="button">
            <el-button type="primary" @click="onQuery">查询</el-button>
          </div>
          <div>
            <span class="hint-info">
              <i class="el-icon-warning" />
              <span>
                电子病历删除前注意事项: 1.请确定该病历是需要删除的; 2.病历一旦删除无法恢复。
              </span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">删除病人病历</div>
      </div>
      <div style="flex: 1; min-height: 0">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="bingRenXM" label="姓名"></el-table-column>
          <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
          <el-table-column prop="bingAnHao" label="病案号"></el-table-column>
          <el-table-column prop="zhuanKeMC" label="专科"></el-table-column>
          <el-table-column prop="bingQuMC" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
          <el-table-column prop="bingQuRuYuanSJ" label="入院日期" width="150"></el-table-column>
          <el-table-column prop="chuYuanSJ" label="出院日期" width="150"></el-table-column>
          <el-table-column prop="ruYuanZD" label="入院诊断"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" label="病历状态"></el-table-column>
          <el-table-column prop="fuYingZT" label="复印状态"></el-table-column>
          <el-table-column prop="zhuYuanFY" label="住院费用" align="right">
            <template #default="scope">
              {{ scope.row.zhuYuanFY.toFixed(2) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getListByEmpi } from '@/api/information'
import { delBingLi } from '@/api/system-maintenance'
import { format } from 'date-fns'
import { template } from 'lodash'
export default {
  name: 'DeleteMedicalRecord',
  data() {
    return {
      bingAnHao: '',
      tableData: []
    }
  },
  methods: {
    async onQuery() {
      const res = await getListByEmpi({ bingAnHao: this.bingAnHao })
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    },
    async handleClick(row) {
      this.$confirm('是否确定删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await delBingLi({ bingLiID: row.bingLiID })
        if (res.hasError === 0) {
          this.$message.success('删除成功')
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin: 0px 6px;
  }
  .hint-info {
    margin-left: 10px;
    display: flex;
    background-color: #e4ecf9;
    border: 1px solid #86abe8;
    border-radius: 3px;
    padding: 3px 10px;
    font-weight: 400;
    .el-icon-warning {
      margin: 3px 3px 0 0;
      color: #356ac5;
    }
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
