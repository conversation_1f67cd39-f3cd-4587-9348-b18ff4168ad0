<!-- 儿科静脉营养医嘱 -->
<template>
  <div class="nutrition-advice">
    <div class="inpatient-content">
      <div class="table-title">
        <div>
          <span class="bar" />
          儿科静脉营养医嘱
        </div>
        <div>
          <el-button
            type="primary"
            @click="
              visible = true
              editData = null
              $nextTick(() => {
                $refs['editDialog'].initFormData()
              })
            "
          >
            新增
          </el-button>
        </div>
      </div>
      <div class="table-container">
        <el-table
          :data="nutritionData"
          style="width: 100%"
          height="100%"
          stripe
          border
          :row-class-name="tableRowClassName"
          @row-click="handleRowClicked"
        >
          <el-table-column
            v-for="(column, index) in columns"
            :key="index"
            :prop="column.value"
            :label="column.label"
            v-bind="column.props"
          >
            <template #default="scope">
              <template v-if="column.label === '操作'">
                <div style="display: flex; align-items: center; justify-content: center">
                  <el-button type="text" @click="openEdit(scope.row)">查看</el-button>
                </div>
              </template>
              <template v-else-if="column.label === '医嘱状态'">
                <el-tag v-if="scope.row.zhuangTaiBZ === '0'" type="danger">已停止</el-tag>
                <el-tag v-if="scope.row.zhuangTaiBZ === '1'">未提交</el-tag>
                <el-tag v-if="scope.row.zhuangTaiBZ === '2'" type="success">已提交</el-tag>
              </template>
              <template v-else>
                {{ scope.row[column.value] }}
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <edit-dialog
      ref="editDialog"
      :visible.sync="visible"
      :drug-info-list="drugInfoList"
      :yao-fang-d-m="yaoFangDM"
      :nutrition-data.sync="editData"
      @upload-success="breakData"
    />
  </div>
</template>

<script>
import { getYaoFangDmInit, getPdnDrugList, getPdnListByBingAnHao } from '@/api/nutrition-advice'
import EditDialog from './components/EditDialog.vue'
import { mapState } from 'vuex'

export default {
  name: 'NutritionAdvice',
  components: { EditDialog },
  data() {
    return {
      visible: false,
      drugInfoList: [],
      editData: {},
      renYuanKuID: null,
      // bingQuID: '3615', //当前病人病区
      // bingLiID: '2616474', //当前病人
      yaoFangDM: '',
      // patientInit: {
      //   bingAnHao: '**********',
      //   bingRenBH: '33030099000000004200854644',
      //   jieSuanDM: 'Y09',
      //   chuShengRQ: '1962-07-26',
      //   chuangWeiHao: '001',
      //   lianXiDH: '15957707284',
      //   lianXiDZ: '龙港市凰浦北路25号',
      //   xingBie: '1',
      //   bingRenXM: '潘时章', //姓名
      //   zhuYuanHao: '2057930',
      //   zhuYuanID: 2616474,
      //   shouShuTZD: {
      //     hunYinZK: '2'
      //   }
      // },
      nutritionData: [],
      nutritionInfo: {},
      clickedRowIndex: null
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      userInfo: (state) => state.user.userInfo,
      initInfo: ({ patient }) => patient.initInfo,
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,
      bingQuID: ({ patient }) => patient.bingQuID,
      patientInit: ({ patient }) => patient.patientInit
    }),
    columns() {
      return [
        // { value: 'xuhao', label: '序号  ', props: { width: '90' } },
        // { value: 'kaiShiSJ', label: '开始时间  ' },
        // { value: 'jieShuSJ', label: '结束时间  ' },
        { value: 'yiZhuSJ', label: '开单时间' },
        { value: 'yiZhuYSYHXM', label: '开单医师  ' },
        { value: 'zhuangTaiBZ', label: '医嘱状态', props: { align: 'center', width: '90' } },
        { label: '操作', props: { fixed: 'right', width: '90', align: 'center' } }
      ]
    }
  },
  async mounted() {
    const res = await getYaoFangDmInit({
      yuanQuDM: this.userInfo.yuanQuDM
    })
    console.log('初始化获取营养医嘱单药房代码', res)
    if (res.hasError === 0) {
      this.yaoFangDM = res.data
    }
    const res1 = await getPdnDrugList({
      yaoFangDM: this.yaoFangDM
    })
    console.log('儿科静脉营养医嘱_获取营养药品', res1)
    if (res.hasError === 0) {
      this.drugInfoList = res1.data
    }
    this.getNutritionData()
  },
  methods: {
    async openEdit(row) {
      this.visible = true
      this.editData = { ylEkpnyyzdVo: row }
      this.$nextTick(() => {
        this.$refs['editDialog'].initFormData()
      })
    },
    //初始化列表
    async getNutritionData() {
      const res = await getPdnListByBingAnHao({
        bingAnHao: this.patientInit.bingAnHao
      })

      console.log('儿科静脉营养医嘱_根据病案号查询列表)', res)
      if (res.hasError === 0) {
        this.nutritionData = res.data.data
      }
    },
    breakData() {
      // this.visible = false
      this.getNutritionData()
    },
    // 是否可选
    isRowSelectable(row) {
      return false
    },
    // 行点击高亮
    handleRowClicked(row) {
      return
      const index = this.nutritionData.indexOf(row)
      if (this.clickedRowIndex === index) {
        this.clickedRowIndex = null
      } else {
        this.clickedRowIndex = this.nutritionData.indexOf(row)
      }
    },
    // 动态绑定行类名
    tableRowClassName({ row, rowIndex }) {
      let className = ''
      if (rowIndex === this.clickedRowIndex) {
        className += 'selected-row '
      }
      switch (row.yanSe) {
        case 'blue':
          className += 'blue-row '
          break
        case 'red':
          className += 'red-row '
          break
        case 'black':
        default:
          className += ''
      }
      return className.trim()
    }
  }
}
</script>

<style scoped lang="scss">
.nutrition-advice {
  background: #eff3fb;
  padding: 8px;
  height: 100%;

  ::v-deep .el-tabs--border-card {
    box-shadow: none;
    background: #eff3fb;

    .el-tabs__header .el-tabs__nav {
      .el-tabs__item {
        height: 32px;
        line-height: 32px;
        background: #ffffff;
        border: 1px solid #dcdfe6;

        &.is-active {
          background: #eff3fb;
          border-bottom-color: transparent;
        }
      }
    }

    .el-tabs__content {
      padding: 0px;

      .el-tab-pane {
        display: flex;
        flex-direction: column;
      }
    }
  }

  .inpatient-content {
    padding-top: 10px;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    width: 1080px;

    .table-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 600;
      margin-bottom: 10px;

      .bar {
        border-left: 3px solid #356ac5;
        padding-left: 5px;
      }

      .sub-title {
        font-weight: 400;
      }
    }

    .table-container {
      flex: 1;
      position: relative;

      ::v-deep .el-drawer__wrapper {
        position: absolute;
        pointer-events: none;

        .el-drawer__header {
          padding: 4px 8px;

          .drawer-header {
            display: flex;
            align-items: center;
            position: relative;

            .title {
              margin-bottom: 0;
              margin-right: 8px;
            }

            .full-button {
              position: absolute;
              top: -4px;
              left: 50%;
              transform: translateX(-50%);
              width: 68px;
              height: 16px;
              text-align: center;
              line-height: 16px;
              font-size: 16px;
              background: #ecf1f9;
              border-radius: 0 0 6px 6px;
              cursor: pointer;
            }
          }
        }

        .el-drawer__container {
          pointer-events: none;
        }

        .el-drawer {
          pointer-events: auto;
        }
      }
    }

    .footer-action {
      margin-top: 8px;
      height: 58px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #eaf0f9;
      display: flex;
      align-items: center;
      padding-left: 10px;

      .el-checkbox {
        margin-left: var(--common-margin);
        margin-right: 0;
      }
    }

    ::v-deep .el-table {
      .el-checkbox__input.is-disabled {
        display: none;
      }

      .el-checkbox__inner {
        width: 16px;
        height: 16px;

        &:after {
          height: 8px;
          left: 5px;
        }
      }

      .selected-row td.el-table__cell {
        background-color: #6787cc;
        color: #fff;
      }

      .blue-row td.el-table__cell {
        color: #356ac5;
      }

      .red-row td.el-table__cell {
        color: #f56c6c;
      }

      .hover-row:not(.selected-row) td.el-table__cell {
        background: #eff3fb;
      }
    }
  }

  :deep(.el-dialog__footer) {
    border-top: none;
  }

  .el-icon-warning {
    font-size: 20px;
    color: #ed6a0c;
  }

  .hint-component {
    padding-left: 30px;
    color: #96999e;

    a {
      color: #356ac5;
    }
  }
}
</style>
