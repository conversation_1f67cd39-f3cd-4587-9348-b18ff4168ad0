<!-- 首页财务收费项目对照 -->
<template>
  <div class="system-canvas">
    <div class="system-finance">
      <div class="finance-search">
        <span class="search-label">收费项目搜索(支持国标码、项目名称、拼音、五笔):</span>
        <el-input v-model="searchValue" style="width: 300px" />
        <el-button type="primary">查询</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            财务项目对照
          </div>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th style="width: 70px"></th>
                <th>国标码</th>
                <th>项目名称</th>
                <th>财产分类码名称</th>
                <th>分类名称</th>
                <th style="width: 70px; text-align: center">操作</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in financeCompareList">
                <tr :key="index" :class="[index % 2 === 1 ? 'tr-two' : 'tr-one']">
                  <td>
                    {{ index + 1 }}
                  </td>
                  <td>
                    {{ item.field1 }}
                  </td>
                  <td>
                    {{ item.field2 }}
                  </td>
                  <td>
                    {{ item.field3 }}
                  </td>
                  <td>
                    {{ item.field4 }}
                  </td>
                  <td style="text-align: center">
                    <el-button type="text">编辑</el-button>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SystemFinanceCompare',
  data() {
    return {
      searchValue: '',
      financeCompareList: [
        {
          field1: '',
          field2: '灌注泵管系统',
          field3: '材料收入',
          field4: ''
        },
        {
          field1: '',
          field2: '腔镜关节头直线型切割吻合器和钉合',
          field3: '材料收入',
          field4: ''
        },
        {
          field1: '',
          field2: '青光眼引流阀',
          field3: '材料收入',
          field4: ''
        },
        {
          field1: '',
          field2: '一次性腔镜用直线型切割吻合器及组件(吻合器)',
          field3: '材料收入',
          field4: ''
        },
        {
          field1: '',
          field2: '一次性腔镜用直线型切割吻合器及组件(组件)',
          field3: '材料收入',
          field4: ''
        },
        {
          field1: '31150100307',
          field2: '成人孤独症诊断量表(ADI)',
          field3: '材料收入',
          field4: ''
        },
        {
          field1: '',
          field2: '一次性电动腔镜直线型切割吻合器及组件HDGW45',
          field3: '材料收入',
          field4: ''
        },
        {
          field1: '',
          field2: '一次性电动腔镜直线型切割吻合器及组件(吻合)',
          field3: '材料收入',
          field4: ''
        },
        {
          field1: '',
          field2: '电动腔镜关节头直线型切割吻合器(带钉)',
          field3: '材料收入',
          field4: ''
        },
        {
          field1: '',
          field2: '电动腔镜关节头直线型切割吻合器(带钉)',
          field3: '材料收入',
          field4: ''
        }
      ]
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-finance {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .finance-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 600;
        width: 670px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 960px;
        table {
          margin-top: 10px;
          width: 100%;
        }
        th,
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 40px;
          padding: 5px 8px;
        }
        .tr-one {
          background-color: #f6f6f6;
        }
        th,
        .tr-two {
          background-color: #eff3fb;
        }
      }
    }
  }
}
</style>
