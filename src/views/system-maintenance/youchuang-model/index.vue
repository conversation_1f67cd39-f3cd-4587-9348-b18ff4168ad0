<template>
  <div class="container">
    <div class="header">
      <div class="query-word">模板查询：</div>
      <div>
        <el-input v-model="query" @change="onQuery"></el-input>
      </div>
      <div class="button">
        <el-button type="primary" @click="onQuery">查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">有创模板维护</div>
        <div class="button"><el-button type="primary" @click="handleAdd">新增</el-button></div>
      </div>
      <div class="table">
        <el-table max-height="600" border :data="tableData" style="width: 670px">
          <el-table-column prop="mingCheng" width="374" label="模板名称"></el-table-column>
          <el-table-column prop="leiBie" width="130" label="类别"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" width="100" label="状态">
            <template #default="scope">
              <el-tag v-if="scope.row.zhuangTaiBZ == 0">启用</el-tag>
              <el-tag v-else-if="scope.row.zhuangTaiBZ == 1" type="danger">暂停</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="caoZuo" width="66" label="操作">
            <template #default="scope">
              <el-button type="text" size="medium" @click="handleUpdate(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
      <div slot="title" class="dialog-title">
        有创模板维护-{{ handleMode == 'update' ? '查看' : '新增' }}
      </div>
      <table>
        <tbody>
          <tr>
            <td class="info-label">名称:</td>
            <td class="info-value">
              <el-input v-model="mingCheng"></el-input>
            </td>
            <td class="info-label">类别:</td>
            <td class="info-value">
              <div class="flex">
                <el-radio v-model="leiBie" label="1">普通记录</el-radio>
                <el-radio v-model="leiBie" label="2">操作记录</el-radio>
              </div>
            </td>
          </tr>
          <tr>
            <td class="info-label">内容:</td>
            <td colspan="3" class="info-value">
              <el-input
                v-model="neiRong"
                type="textarea"
                :autosize="{ maxRows: 15 }"
                placeholder="请输入内容"
                maxlength="1000"
                show-word-limit
                :clearable="true"
              ></el-input>
            </td>
          </tr>
          <tr>
            <td class="info-label">状态:</td>
            <td class="info-value">
              <div class="flex">
                <el-radio v-model="zhuangTaiBZ" label="0">启用</el-radio>
                <el-radio v-model="zhuangTaiBZ" label="1">停用</el-radio>
              </div>
            </td>
            <td class="info-label">关联操作名称:</td>
            <td class="info-value no-padding">
              <div class="flex">
                <span class="no-padding-label">内镜下肠息肉切除术</span>
                <el-button type="primary">选择</el-button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary">关联专科</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  getMbglData,
  getYcjlData,
  getYcjlDataByPYGJZ,
  saveYcjl,
  deleteBatchMbgl
} from '@/api/system-maintenance'
export default {
  data() {
    return {
      query: '',
      tableData: [
        {
          mingCheng: '胃息肉内镜下治疗记录',
          leiBie: '操作记录',
          zhuangTaiBZ: 0,
          caoZuo: 0
        }
      ],
      dialogVisible: false,
      jilu: '0',
      zhuangTaiBZ: '0',
      mingCheng: '',
      leiBie: '',
      neiRong: '',
      icdid: '',
      moBanID: '',
      caoZuoZheID: '',
      handleMode: '',
      selectRow: null
    }
  },
  async mounted() {
    await this.refreshData()
  },
  methods: {
    async onQuery() {
      let res = await getYcjlDataByPYGJZ({
        keyWord: this.query
      })
      res.data.sort((a, b) => a.moBanID - b.moBanID)
      this.tableData = res.data
    },
    async refreshData() {
      let res = await getYcjlData()
      res.data.sort((a, b) => a.moBanID - b.moBanID)
      this.tableData = res.data
    },
    handleAdd() {
      this.handleMode = 'add'
      this.dialogVisible = true
      this.mingCheng = ''
      this.neiRong = ''
      this.zhuangTaiBZ = ''
      this.leiBie = ''
      this.icdid = ''
      this.moBanID = ''
      this.caoZuoZheID = ''
    },
    handleUpdate(row) {
      this.handleMode = 'update'
      this.dialogVisible = true
      this.mingCheng = row.mingCheng
      this.neiRong = row.neiRong
      this.zhuangTaiBZ = row.zhuangTaiBZ
      this.leiBie = row.leiBie
      this.icdid = row.icdid
      this.moBanID = row.moBanID
      this.caoZuoZheID = row.caoZuoZheID
      // this.selectRow = row
      console.log(row)
    },
    handleClose() {
      this.dialogVisible = false
    },
    async handleSave() {
      let params = {
        mingCheng: this.mingCheng,
        neiRong: this.neiRong,
        zhuangTaiBZ: this.zhuangTaiBZ,
        leiBie: this.leiBie,
        caoZuoZheID: this.caoZuoZheID
      }
      if (this.handleMode == 'update') {
        params['moBanID'] = this.moBanID
        params['icdid'] = this.icdid
      }
      let res = await saveYcjl(params)
      if (res.hasError === 0) {
        this.$message({
          message: '保存成功',
          type: 'success'
        })
        this.dialogVisible = false
        await this.refreshData()
        // this.selectRow.mingCheng = this.mingCheng
        // this.selectRow.leiBie = this.leiBie
        // this.selectRow.neiRong = this.neiRong
        // this.selectRow.zhuangTaiBZ = this.zhuangTaiBZ
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 700px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 670px;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}
::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
td {
  border: 1px solid #ddd;
  border-collapse: collapse; /* 移除表格内边框间的间隙 */
  height: 35px;
  padding: 10px;
}
.info-label {
  text-align: right;
  width: 10vw;
  background-color: #eaf0f9;
}
.info-value {
  width: 14vw;
  height: 30px;
  background-color: #f7f9fd;
}
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.no-padding {
  padding: 0;
  .no-padding-label {
    padding: 10px;
  }
  ::v-deep .el-button {
    flex: 1;
    height: 100%;
  }
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
</style>
