<template>
  <el-dialog :visible="visible" width="700px" @open="initFormData()" @close="updateVisible(false)">
    <span slot="title">
      <span class="dialog-title">
        <span class="bar" />
        病案首页损伤外因ICD维护-编辑
      </span>
    </span>
    <span>
      <div class="dialog-component">
        <table v-if="formData">
          <tbody>
            <tr>
              <td class="info-label">ID:</td>
              <td class="info-value">
                {{ formData.id }}
              </td>
              <td class="info-label">
                <span>*</span>
                代码:
              </td>
              <td class="info-value">
                <el-input v-model="formData.daiMa" placeholder="请输入代码" />
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                名称:
              </td>
              <td class="info-value" colspan="3">
                <el-input v-model="formData.mingChen" placeholder="请输入名称" />
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                排序:
              </td>
              <td class="info-value">
                <el-input v-model="formData.paiXu" placeholder="请输入排序" />
              </td>
              <td class="info-label">
                <span>*</span>
                状态:
              </td>
              <td class="info-value">
                <el-radio v-model="formData.zhuangTaiBZ" label="1">启用</el-radio>
                <el-radio v-model="formData.zhuangTaiBZ" label="0">停用</el-radio>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="uploadForm()">保 存</el-button>
      <el-button
        type="primary"
        :disabled="JSON.stringify(formData) === JSON.stringify(icdData)"
        @click="initFormData"
      >
        还 原
      </el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'
import { saveYlBasySswy } from '@/api/system-maintenance'

export default {
  name: 'Icddialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    icdData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      formData: null
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },
    initFormData() {
      this.formData = deepClone(this.icdData)
    },
    uploadForm() {
      saveYlBasySswy(this.formData).then((res) => {
        if (res.hasError === 0) {
          console.log('saveYlBasySswy res:', res)
          this.updateVisible(false)
          this.$emit('upload-success')
          this.$message({
            message: '保存成功',
            type: 'success',
            duration: 700
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
  .bar {
    border-left: 3px solid #356ac5;
    padding-left: 5px;
  }
}

.dialog-component {
  padding: 0px 16px;
  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 5px;
  }
  .info-label {
    text-align: right;
    width: 110px;
    background-color: #eaf0f9;
    span {
      color: #f35656;
      position: relative;
      top: 3px;
      right: 3px;
    }
  }
  .info-value {
    width: 230px;
    background-color: #ffffff;
    a {
      text-decoration: underline;
      color: #356ac5;
    }
  }
}

.dialog-footer {
  :deep(.el-button--primary.is-disabled) {
    background-color: #f3f3f4;
    border-color: #f3f3f4;
    color: #8f9298;
  }
}

:deep(.el-dialog__footer) {
  border-top: none;
}

:deep(.el-radio) input[aria-hidden='true'] {
  display: none !important;
}
</style>
