<template>
  <div class="container">
    <div class="header">
      <div>
        <div style="display: flex; align-items: center">
          <div class="query-word">请输入病案号：</div>
          <div>
            <el-input v-model="bingAnHao" @change="onQuery"></el-input>
          </div>
          <div class="button">
            <el-button type="primary" @click="onQuery">查询</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">修改病人病历专科格式</div>
      </div>
      <div class="table">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="bingRenXM" label="姓名" width="100"></el-table-column>
          <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
          <el-table-column prop="bingAnHao" label="病案号" width="120"></el-table-column>
          <el-table-column prop="zhuanKeMC" label="专科" width="100"></el-table-column>
          <el-table-column prop="bingQuMC" label="病区" width="100"></el-table-column>
          <el-table-column prop="chuangWeiHao" label="床位号" width="100"></el-table-column>
          <el-table-column prop="bingQuRuYuanSJ" label="入院日期" width="150"></el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">修改</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <default-dialog
      :visible="visible"
      width="25%"
      title="修改此病人病历专科格式"
      @confirm="confirmClick"
      @cancel="updateVisible(false)"
    >
      <span>
        <div class="dialog-component">
          <div class="search-head">
            <el-select v-model="zhuanKe" placeholder="请选择专科" autofocus filterable>
              <el-option
                v-for="item in zhuanKeList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID.toString()"
              />
            </el-select>
          </div>
        </div>
      </span>
    </default-dialog>
  </div>
</template>

<script>
import { getListByEmpi } from '@/api/information'
import { updateByBingLiIDAndZhuanKeID } from '@/api/system-maintenance'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { format } from 'date-fns'
import { mapState } from 'vuex'
export default {
  name: 'MedicalSpecialistFormat',
  components: {
    DefaultDialog
  },
  data() {
    return {
      bingAnHao: '',
      tableData: [],
      visible: false,
      zhuanKe: '',
      bingLiID: ''
    }
  },
  computed: {
    ...mapState({
      zhuanKeList: ({ patient }) => patient.zhuanKeList,
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  methods: {
    async onQuery() {
      const res = await getListByEmpi({ bingAnHao: this.bingAnHao })
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    },
    async handleClick(row) {
      this.visible = true
      this.zhuanKe = this.zhuanKeID
      bingLiID = row.bingLiID
    },
    updateVisible() {
      this.visible = false
    },
    async confirmClick() {
      const res = await updateByBingLiIDAndZhuanKeID({
        bingLiID: this.zhuanKe,
        zhuanKeID: this.zhuanKe
      })
      if (res.hasError === 0) {
        this.$message.success('修改成功！')
        this.onQuery()
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin: 0px 6px;
  }
  .hint-info {
    margin-left: 10px;
    display: flex;
    background-color: #e4ecf9;
    border: 1px solid #86abe8;
    border-radius: 3px;
    padding: 3px 10px;
    font-weight: 400;
    .el-icon-warning {
      margin: 3px 3px 0 0;
      color: #356ac5;
    }
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .table {
    width: 835px;
    height: calc(100% - 40px);
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
