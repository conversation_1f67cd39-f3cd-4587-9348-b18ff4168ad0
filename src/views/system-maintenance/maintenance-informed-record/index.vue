<template>
  <div class="container">
    <div class="left">
      <div class="left-header">
        <div class="left-header-title">知情记录列表维护</div>
        <el-input v-model="zhuanKe" placeholder="请输入专科名称" @change="onSearch"></el-input>
        <el-button @click="onAdd">新增</el-button>
      </div>
      <div class="left-content">
        <div class="left-content-title">列表名称</div>
        <div
          v-for="(item, index) in listData"
          :key="index"
          :class="activeIndex === index ? 'active' : 'left-content-item'"
          @click="handleSelectionChange(item, index)"
        >
          {{ item.wenShuMC }}
        </div>
      </div>
    </div>
    <div class="right">
      <div class="right-header">
        <div class="right-header-title">专科现有的常用文书</div>
      </div>
      <div class="right-content">
        <el-table border :data="tableData">
          <el-table-column prop="leiXingMC" width="125" label="文书类型"></el-table-column>
          <el-table-column prop="wenShuMC" width="400" label="文书名字"></el-table-column>
          <el-table-column prop="xingMing" width="110" label="文书排序"></el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button @click="handleClick(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getListByZkid,
  addZhuanKeWenShu,
  deleteZhuanKeWenShu,
  getAllWenShu
} from '@/api/system-maintenance'
import { format } from 'date-fns'
import { getZhuanKeList } from '@/api/medical-record-api'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      activeIndex: '',
      selectContent: '',
      zhuanKe: '',
      zhuanKeList: [],
      listData: [],
      tableData: [],
      patientData: [],
      currentRow: ''
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  async mounted() {
    this.getZhuanKeList()
    this.initData()
    this.getListByList()
  },
  methods: {
    // 住院医生站_查询
    async onSearch() {
      if (this.selectContent) {
        const arr = []
        this.patientData.map((item) => {
          if (item.wenShuMC.indexOf(this.selectContent) >= 0) {
            arr.push(item)
          } else if (item.pinYin && item.pinYin.indexOf(this.selectContent.toUpperCase()) >= 0) {
            arr.push(item)
          }
        })
        this.listData = arr
      }
    },
    async onAdd() {
      console.log(this.currentRow)
      if (this.currentRow) {
        const res = await addZhuanKeWenShu({
          zhuanKeID: this.zhuanKe,
          geShiDM: this.currentRow.geShiDM,
          wenShuLX: this.currentRow.wenShuLX,
          paiXu: this.currentRow.paiXu,
          zhuangTaiBZ: this.currentRow.zhuangTaiBZ,
          caoZuoZheID: this.currentRow.caoZuoZheID,
          xiuGaiSJ: this.currentRow.xiuGaiSJ
        })
        if (res.hasError === 0) {
          this.$message.success('新增成功!')
          this.getListByList()
        }
      } else {
        this.$message.error('请选择至少一条!')
      }
    },
    handleSelectionChange(val, index) {
      this.activeIndex = index
      this.currentRow = val
    },
    async initData() {
      const res = await getAllWenShu()
      if (res.hasError === 0) {
        this.listData = res.data
        this.patientData = res.data
      }
    },
    async getListByList() {
      const res = await getListByZkid({ zhuanKeID: this.zhuanKeID })
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    },
    // 获取专科治疗组列表
    async getZhuanKeList() {
      try {
        const res = await getZhuanKeList()
        if (res.hasError === 0) {
          this.zhuanKeList = res.data
          this.zhuanKeList.unshift({
            daiMa: '0',
            mingCheng: '所有专科'
          })
        }
      } catch (error) {
        console.log(error)
      }
    },

    async handleClick(row) {
      this.$confirm('是否确定删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await deleteZhuanKeWenShu({
          geShiDM: row.geShiDM,
          zhuanKeID: this.zhuanKe
        })
        if (res.hasError === 0) {
          this.$message.success('删除成功!')
          this.getListByList()
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 80px;
  display: flex;
  background-color: #fff;
  .left {
    margin-right: 20px;
    width: 25%;
    height: 100%;
    border-radius: 4px;
    background: #eaf0f9;
    padding: 10px 12px;
    .left-header {
      margin-left: 9px;
      margin-top: 10px;
      display: flex;
      align-items: center;
      .left-header-title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        width: 200px;
      }
      .left-header-title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      ::v-deep .el-button {
        background-color: #a66dd4;
        color: #fff;
      }
    }
    .left-content {
      height: calc(100% - 70px);
      overflow: auto;
      margin-top: 16px;
      font-size: 14px;
      line-height: 14px;
      color: #171c28;
      .left-content-title {
        padding: 9px 12px;
        background-color: #eaf0f9;
        border: 1px solid #dcdfe6;
      }
      .left-content-item {
        padding: 9px 12px;
        border: 1px solid #dcdfe6;
      }
      .left-content-item:nth-child(odd) {
        background-color: #eff3fb;
      }
      .left-content-item:nth-child(even) {
        background-color: #f6f6f6;
      }
      .active {
        background-color: #6787cc;
        color: #fff;
        padding: 9px 12px;
        border: 1px solid #dcdfe6;
      }
    }
  }
  .right {
    height: 100%;
    background-color: #eff3fb;
    padding: 10px 12px;
    width: 75%;
    overflow: auto;
    .right-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 70%;
      .right-header-title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }
      .right-header-title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      ::v-deep .el-button {
        background-color: #3b76ef;
        color: #fff;
      }
    }
    .right-content {
      width: 70%;
      margin-top: 20px;
      ::v-deep .el-button {
        color: #356ac5;
        border: none;
        margin: 0;
      }
    }
  }
}
</style>
