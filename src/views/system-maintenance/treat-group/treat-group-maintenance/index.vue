<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">选择专科:</div>
        <div class="header-item-select">
          <el-select v-model="zhuanKe" placeholder="请选择" size="small">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">输入名称或拼音:</div>
        <div class="header-item-input">
          <el-input v-model="selectContent" placeholder="请输入内容"></el-input>
        </div>
        <div class="header-item-button"><el-button>查询</el-button></div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">治疗组维护</div>
        <div class="button"><el-button type="primary" @click="handleAdd">新增</el-button></div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: 992px">
          <el-table-column prop="zhiLiaoZuMC" width="224" label="治疗组名称"></el-table-column>
          <el-table-column prop="zhuanKe" width="224" label="专科"></el-table-column>
          <el-table-column prop="zhuanKeSX" width="224" label="专科属性"></el-table-column>
          <el-table-column prop="zuZhang" width="120" label="组长"></el-table-column>
          <el-table-column width="100" label="状态">
            <template #default="scope">
              <el-tag v-if="scope.row.zhuangTai == 0">启用</el-tag>
              <el-tag v-else-if="scope.row.zhuangTai == 1" type="danger">暂停</el-tag>
            </template>
          </el-table-column>
          <el-table-column width="100" label="操作">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
      <div slot="title" class="dialog-title">
        治疗组维护-{{ handleMode == 'update' ? '查看' : '新增' }}
      </div>
      <table>
        <tbody>
          <tr>
            <td class="info-label">治疗组名称:</td>
            <td class="info-value">
              <el-input v-model="zhiLiaoZuMC"></el-input>
            </td>
            <td class="info-label">所属专科:</td>
            <td class="info-value">
              <el-input v-model="zhuanKe"></el-input>
            </td>
          </tr>
          <tr>
            <td class="info-label">专科属性:</td>
            <td class="info-value">
              <el-input v-model="zhuanKeSX"></el-input>
            </td>
            <td class="info-label">组长:</td>
            <td class="info-value">
              <el-input v-model="zuZhang"></el-input>
            </td>
          </tr>
          <tr>
            <td class="info-label">床位数:</td>
            <td class="info-value">
              <span>每月床位数维护</span>
            </td>
            <td class="info-label">状态标志:</td>
            <td class="info-value">
              <div class="flex">
                <el-radio v-model="zhuangTai" label="0">启用</el-radio>
                <el-radio v-model="zhuangTai" label="1">停用</el-radio>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  data() {
    return {
      options: [
        {
          value: '1',
          label: '消化内科'
        }
      ],
      selectContent: '',
      tableData: [
        {
          zhiLiaoZuMC: '消化内科吴伟组',
          zhuanKe: '消化内科',
          zhuanKeSX: 'test',
          zuZhang: '吴伟',
          zhuangTai: '1',
          caoZuo: ''
        }
      ],
      dialogVisible: false,
      handleMode: '',
      zhiLiaoZuMC: '',
      zhuanKe: '',
      zhuanKeSX: '',
      zuZhang: '',
      zhuangTai: ''
    }
  },
  methods: {
    handleClick(row) {
      this.dialogVisible = true
      this.handleMode = 'update'
      this.zhiLiaoZuMC = row.zhiLiaoZuMC
      this.zhuanKe = row.zhuanKe
      this.zhuanKeSX = row.zhuanKeSX
      this.zuZhang = row.zuZhang
      this.zhuangTai = row.zhuangTai
      console.log(row)
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleAdd() {
      this.handleMode = 'add'
      this.dialogVisible = true
      this.zhiLiaoZuMC = ''
      this.zhuanKe = ''
      this.zhuanKeSX = ''
      this.zuZhang = ''
      this.zhuangTai = ''
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
  .table {
    min-height: 650px;
  }
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}

::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
td {
  border: 1px solid #ddd;
  border-collapse: collapse; /* 移除表格内边框间的间隙 */
  height: 35px;
  padding: 10px;
}
.info-label {
  text-align: right;
  width: 10vw;
  background-color: #eaf0f9;
}
.info-value {
  width: 14vw;
  height: 30px;
  background-color: #f7f9fd;
}
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.no-padding {
  padding: 0;
  .no-padding-label {
    padding: 10px;
  }
  ::v-deep .el-button {
    flex: 1;
    height: 100%;
  }
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
</style>
