<template>
  <div class="container">
    <div class="left">
      <div class="left-header">专科列表</div>
      <div class="left-content">
        <div class="left-content-title">专科选择</div>
        <div class="left-content-item">401病区</div>
        <div class="left-content-item">402病区</div>
      </div>
    </div>
    <div class="right">
      <div class="right-header">
        <div class="right-header-title">402病区专科住院总列表</div>
        <div>
          <el-button class="right-header-btn">新增</el-button>
        </div>
      </div>
      <div class="right-content">
        <el-table max-height="648" border :data="tableData" width="100%">
          <el-table-column prop="yiShiXM" label="医师姓名"></el-table-column>
          <el-table-column prop="renYuanLB" label="人员类别"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" label="状态标志"></el-table-column>
          <el-table-column prop="caoZuoZhe" label="操作者"></el-table-column>
          <el-table-column prop="xiuGaiSJ" label="修改时间"></el-table-column>
          <el-table-column prop="caoZuo" label="操作"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      tableData: [
        {
          yiShiXM: '王雨萌',
          renYuanLB: '住院总',
          zhuangTaiBZ: '',
          caoZuoZhe: '王雨萌',
          xiuGaiSJ: '',
          caoZuo: '保存'
        }
      ]
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 10px 80px;
  display: flex;
  background-color: #fff;
  .left {
    margin-right: 20px;
    width: 20%;
    border-radius: 4px;
    background: #eaf0f9;
    padding: 5px 16px;
    .left-header {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
      margin-top: 10px;
    }
    .left-header::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .left-content {
      margin-top: 16px;
      font-size: 14px;
      line-height: 14px;
      color: #171c28;
      .left-content-title {
        padding: 9px 12px;
        background-color: #eaf0f9;
        border: 1px solid #dcdfe6;
      }
      .left-content-item {
        padding: 9px 12px;
        border: 1px solid #dcdfe6;
        font-weight: 600;
      }
      .left-content-item:nth-child(odd) {
        background-color: #eff3fb;
      }
      .left-content-item:nth-child(even) {
        background-color: #f6f6f6;
      }
      .left-content-item:hover {
        background-color: #6787cc;
        color: #fff;
      }
    }
  }
  .right {
    background-color: #eff3fb;
    padding: 10px 12px;
    width: 80%;
    .right-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 70%;
      .right-header-title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }
      .right-header-title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      ::v-deep .el-button {
        background-color: #3b76ef;
        color: #fff;
      }
    }
    .right-content {
      width: 70%;
      margin-top: 20px;
    }
  }
}
</style>
