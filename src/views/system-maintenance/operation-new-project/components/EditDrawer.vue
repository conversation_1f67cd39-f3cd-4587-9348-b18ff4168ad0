<template>
  <el-dialog :visible="visible" width="700px" @close="updateVisible(false)">
    <span slot="title">
      <span class="dialog-title">
        <span class="bar" />
        手术新技术新项目维护-{{ dialogType === 'edit' ? '编辑' : '新增' }}
      </span>
    </span>
    <span>
      <div class="dialog-component">
        <table v-if="formData">
          <tbody>
            <tr>
              <td class="info-label">
                <span>*</span>
                代码:
              </td>
              <td class="info-value">
                <el-input v-model="formData.field1" placeholder="请输入代码" />
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                名称:
              </td>
              <td class="info-value" colspan="3">
                <el-input v-model="formData.field2" placeholder="请输入名称" />
              </td>
            </tr>
            <tr>
              <td class="info-label">
                <span>*</span>
                状态:
              </td>
              <td class="info-value">
                <el-radio v-model="formData.field4" label="启用">启用</el-radio>
                <el-radio v-model="formData.field4" label="停用">停用</el-radio>
              </td>
              <td class="info-label">
                <span>*</span>
                专科:
              </td>
              <td class="info-value">
                <el-select v-model="formData.field3" placeholder="">
                  <el-option
                    v-for="item in tableOptions"
                    :key="item.label"
                    :label="item.label"
                    :value="item.label"
                  ></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td class="info-label" style="vertical-align: top; padding-top: 10px">备注:</td>
              <td class="info-value" colspan="3">
                <el-input
                  v-model="formData.field5"
                  type="textarea"
                  maxlength="1000"
                  clearable
                  show-word-limit
                  :autosize="{ minRows: 6 }"
                />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" @click="updateVisible(false)">保 存</el-button>
      <el-button @click="updateVisible(false)">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { deepClone } from '@/utils'

export default {
  name: 'Editdialog',
  props: {
    type: {
      type: String,
      default: 'edit'
    },
    visible: {
      type: Boolean,
      default: false
    },
    data: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      notes: '',
      formData: null,
      tableOptions: [
        {
          value: 0,
          label: '呼吸内科'
        },
        {
          value: 1,
          label: '血液内科'
        },
        {
          value: 2,
          label: ''
        }
      ]
    }
  },
  watch: {
    data(newValue) {
      if (newValue) {
        this.formData = deepClone(this.data)
      }
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-title {
  font-size: 16px;
  .bar {
    border-left: 3px solid #356ac5;
    padding-left: 5px;
  }
}

.dialog-component {
  padding: 0px 16px;
  table {
    width: 100%;
  }
  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 35px;
    padding: 5px;
  }
  .info-label {
    text-align: right;
    width: 110px;
    background-color: #eaf0f9;
    span {
      color: #f35656;
      position: relative;
      top: 3px;
      right: 3px;
    }
  }
  .info-value {
    width: 230px;
    background-color: #ffffff;
    a {
      text-decoration: underline;
      color: #356ac5;
    }
  }
}
:deep(.el-dialog__footer) {
  border-top: none;
}

:deep(.el-radio) input[aria-hidden='true'] {
  display: none !important;
}
</style>
