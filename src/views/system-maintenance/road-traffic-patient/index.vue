<template>
  <div class="container">
    <div class="header">
      <div style="display: flex; align-items: center">
        <el-radio v-model="selectType" label="2">姓名</el-radio>
        <el-radio v-model="selectType" label="3">住院号</el-radio>
        <el-radio v-model="selectType" label="1">病案号:</el-radio>
        <el-input v-model="selectContent"></el-input>
        <el-button>查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">道路交通患者维护</div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData">
          <el-table-column prop="zhuanKe" label="专科" width="80"></el-table-column>
          <el-table-column prop="bingAnHao" label="住院号" width="120"></el-table-column>
          <el-table-column prop="xingMing" label="姓名" width="80"></el-table-column>
          <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
          <el-table-column prop="chuShengRQ" label="出生日期" width="120"></el-table-column>
          <el-table-column prop="chuangWeiHao" label="病区-床位号" width="100"></el-table-column>
          <el-table-column prop="ruYuanRQ" label="入院日期" width="200"></el-table-column>
          <el-table-column prop="bingQuRYRQ" label="病区入院日期" width="200"></el-table-column>
          <el-table-column prop="chuYuanRQ" label="出院日期" width="200"></el-table-column>
          <el-table-column prop="ruYuanZD" label="入院诊断" width="200"></el-table-column>
          <el-table-column prop="huaYanDan" label="化验单"></el-table-column>
          <el-table-column prop="fuYinZT" label="复印状态"></el-table-column>
          <el-table-column prop="bingLiZT" label="病历状态"></el-table-column>
          <el-table-column prop="bingLiZT" label="医生归档"></el-table-column>
          <el-table-column prop="bingLiZT" label="护士归档"></el-table-column>
          <el-table-column prop="bingLiZT" label="护士归档日期"></el-table-column>
          <el-table-column prop="bingLiZT" label="是否道路交通患者"></el-table-column>
          <el-table-column fixed="right" label="操作" width="200" align="center">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">
                添加标记
              </el-button>
              <el-button type="text" size="small" @click="deleteList(scope.row)">
                移除标记
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { getBinRenXXByParam } from '@/api/information'
import { insertZhuYuanBRSX, deleteZhuYuanBRSX } from '@/api/system-maintenance'
import { format } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      value: '1',
      input: '',
      tableData: [],
      selectType: '1',
      selectContent: ''
    }
  },
  computed: {
    ...mapState({
      patientInfo: ({ patient }) => patient.initInfo
    })
  },
  methods: {
    async onSerch() {
      const res = await getBinRenXXByParam({
        type: this.selectType,
        param: this.selectContent,
        yongHuID: this.patientInfo.yiShiYHID
      })
      if (res.hasError === 0) {
        this.tableData = res.data || []
      }
    },
    async handleClick(row) {
      console.log(row)
      const listVo = [
        {
          bingLiID: row.bingLiID,
          shuXingDM: '0004',
          shuXingZhi: '交通事故患者标识',
          caoZuoZheID: this.patientInfo.yiShiYHID,
          xiuGaiSJ: format(new Date(), 'yyyy-MM-dd HH:mm:ss')
        }
      ]
      const res = await insertZhuYuanBRSX(listVo)
      if (res.hasError === 0) {
        this.$message({
          message: '设置成功！',
          type: 'success'
        })
      }
    },
    async deleteList(row) {
      const res = await deleteZhuYuanBRSX({ idList: [row.id] })
      if (res.hasError === 0) {
        this.$message({
          message: '移除成功！',
          type: 'success'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 10px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    padding: 9px 16px;
    color: #fff;
  }
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
