<!-- 病案首页手术同步内容维护 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="table-background">
        <div class="head-search">
          <span class="search-label">BLID:</span>
          <el-input v-model="searchValue" style="width: 170px" />
          <el-button class="search-button" type="primary">查询</el-button>
          <el-button type="primary">删除选择行</el-button>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th style="width: 70px; text-align: center">
                  <el-checkbox disabled />
                </th>
                <th>序号</th>
                <th>ICD代码</th>
                <th>手术日期</th>
                <th style="width: 220px">手术名称</th>
                <th style="width: 110px">术者名称</th>
                <th style="width: 110px">第一助手</th>
                <th style="width: 110px">第二助手</th>
                <th style="width: 110px">麻醉医师</th>
                <th style="width: 110px">手术编号</th>
                <th style="width: 110px">顺序号</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in data1List">
                <tr :key="index" :class="[index % 2 === 1 ? 'tr-two' : 'tr-one']">
                  <td style="text-align: center">
                    <el-checkbox disabled />
                  </td>
                  <td>
                    {{ item.field1 }}
                  </td>
                  <td>
                    {{ item.field2 }}
                  </td>
                  <td>
                    {{ item.field3 }}
                  </td>
                  <td>
                    {{ item.field4 }}
                  </td>
                  <td>
                    {{ item.field5 }}
                  </td>
                  <td>
                    {{ item.field6 }}
                  </td>
                  <td>
                    {{ item.field7 }}
                  </td>
                  <td>
                    {{ item.field8 }}
                  </td>
                  <td>
                    {{ item.field9 }}
                  </td>
                  <td>
                    {{ item.field10 }}
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
      <div class="table-background">
        <div class="head-search">
          <span class="search-label">BLID:</span>
          <el-input v-model="searchValue" style="width: 170px" />
          <el-button class="search-button" type="primary">查询</el-button>
          <el-button type="primary">解除绑定</el-button>
        </div>
        <div class="table-component">
          <table>
            <thead>
              <tr style="text-align: left">
                <th style="width: 70px; text-align: center">
                  <el-checkbox disabled />
                </th>
                <th>序号</th>
                <th>文书ID</th>
                <th>绑定的手术日期</th>
                <th style="width: 880px">绑定的手术名称</th>
              </tr>
            </thead>
            <tbody>
              <template v-for="(item, index) in data2List">
                <tr :key="index" :class="[index % 2 === 1 ? 'tr-two' : 'tr-one']">
                  <td style="text-align: center">
                    <el-checkbox disabled />
                  </td>
                  <td>
                    {{ item.field1 }}
                  </td>
                  <td>
                    {{ item.field2 }}
                  </td>
                  <td>
                    {{ item.field3 }}
                  </td>
                  <td>
                    {{ item.field4 }}
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SystemOperationIcd',
  data() {
    return {
      searchValue: '',
      data1List: [],
      data2List: []
    }
  },
  mounted() {
    this.initSystemIcd()
  },
  methods: {
    initSystemIcd() {
      this.data1List = [
        {
          field1: '1',
          field2: '06.0201A',
          field3: '2024-04-09 14:00:00',
          field4: '(左)甲状腺术后探查',
          field5: '',
          field6: '',
          field7: '',
          field8: '',
          field9: '1',
          field10: '1'
        },
        {
          field1: '2',
          field2: '99.9208',
          field3: '2020-10-29 10:00:00',
          field4: '耳朵放血',
          field5: '',
          field6: '',
          field7: '',
          field8: '',
          field9: '2',
          field10: '2'
        }
      ]
      this.data2List = [
        {
          field1: '1',
          field2: '06.0201A',
          field3: '2024-04-09 14:00:00',
          field4: '(左)甲状腺术后探查'
        }
      ]
      // getYlSsicdByPage({
      //   pageIndex: 1,
      //   pageSize: 10
      // }).then((res) => {
      //   if (res.hasError === 0) {
      //     // this.data1List = res.data;
      //     console.log('手术icd res:', res)
      //   }
      // })
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      width: 1280px;
      padding: 16px;
      background-color: #eaf0f9;
      border-radius: 4px;
      border: 1px solid #ddd;
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button.search-button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover).search-button {
        background: #a66dd4;
        border-color: #a66dd4;
      }
      :deep(.el-button--primary:focus).search-button {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 0.5;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-component {
        width: 1280px;
        margin-top: 10px;
        table {
          margin-top: 10px;
          width: 100%;
        }
        th,
        td {
          border: 1px solid #ddd;
          border-collapse: collapse; /* 移除表格内边框间的间隙 */
          height: 35px;
          padding: 5px 8px;
        }
        .tr-one {
          background-color: #f6f6f6;
        }
        th,
        .tr-two {
          background-color: #eaf0f9;
        }
      }
    }
  }
}
</style>
