<template>
  <div class="container">
    <div class="content">
      <div class="left">
        <div class="left-button-group">
          <el-button type="primary" @click="handleAdd">新建</el-button>
          <el-button type="primary" @click="handleUpdate">修改</el-button>
          <el-button type="primary" @click="handleDelete">删除</el-button>
        </div>
        <div class="model-group">
          <el-collapse accordion>
            <template v-for="(item, index) in modelData">
              <div :key="index" class="model-item">
                <el-collapse-item :title="item.name" :name="index">
                  <template v-for="children in item.children">
                    <div
                      :key="children.moBanID"
                      class="model-item-child"
                      @click="onModelClick(children, item)"
                    >
                      {{ children.mingCheng }}
                    </div>
                  </template>
                </el-collapse-item>
              </div>
            </template>
          </el-collapse>
        </div>
      </div>
      <div v-if="currentModelName != ''" class="right">
        <div class="right-header">
          <div class="right-header-title">{{ currentModelName }}</div>
          <div class="right-button-group">
            <el-button type="primary">编辑</el-button>
            <el-button type="primary">保存</el-button>
          </div>
        </div>
        <div class="right-table">
          <el-table max-height="648" border :data="tableData" style="width: 100%">
            <el-table-column prop="yiZhuMLMC" width="424" label="医嘱名称"></el-table-column>
            <el-table-column prop="zhiXingPL" width="124" label="执行频率"></el-table-column>
            <el-table-column prop="zhiXingFF" width="124" label="执行方法"></el-table-column>
            <el-table-column prop="zhiXingSJ" width="124" label="执行时间"></el-table-column>
            <el-table-column prop="teShuSM" width="324" label="特殊说明"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
      <div slot="title" class="dialog-title">治疗医嘱模板-新增</div>
      <div>
        <span>模板名称:</span>
        <el-input v-model="muBanMingCheng"></el-input>
      </div>
      <div class="muBanLX">
        <span>模板类型:</span>
        <el-radio v-model="muBanLeiBie" label="0">个人</el-radio>
        <el-radio v-model="muBanLeiBie" label="1">专科</el-radio>
      </div>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="delDialogVisible" width="30%" :before-close="handleClose">
      <div slot="title" class="dialog-title">
        <i class="el-icon-warning"></i>
        <span>删除提示</span>
      </div>
      <div class="dialog-content">
        确认删除
        <span>【{{ selData.mingCheng }}】</span>
        模板吗？
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="MlDelete">确 认</el-button>
        <el-button @click="handleClose">取 消</el-button>
      </span>
    </el-dialog>

    <el-dialog :visible.sync="upDialogVisible" width="40%" :before-close="handleClose">
      <div slot="title" class="dialog-title">治疗医嘱模板-修改</div>
      <div>
        <span>模板名称:</span>
        <el-input v-model="upMingCheng"></el-input>
      </div>
      <div class="muBanLX">
        <span>模板类型:</span>
        <el-radio v-model="upLeiBie" label="0">个人</el-radio>
        <el-radio v-model="upLeiBie" label="1">专科</el-radio>
      </div>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="MlUpdate">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getZhiLiaoMbMlByLb,
  getZhiLiaoMbZh,
  saveZhiLiaoMbMl,
  saveZhiLiaoMbZh,
  zhiLiaoMbInit
} from '@/api/system-maintenance'
export default {
  data() {
    return {
      headerTabsValue: '0',
      headerTabs: [],
      tableData: [
        // {
        //   yiZhuMLMC: '二级护理(心电监护)',
        //   zhiXingPL: 'st',
        //   zhiXingFF: 'po',
        //   zhiXingSJ: 'ac',
        //   teShuSM: ''
        // }
      ],
      modelData: [],
      currentModelName: '',
      dialogVisible: false,
      upDialogVisible: false,
      delDialogVisible: false,
      muBanMingCheng: '',
      muBanLeiBie: '',
      handleMode: '',
      selData: {},
      upMingCheng: '',
      upLeiBie: '',
      upID: ''
    }
  },
  created() {
    this.headerTabs = [
      {
        title: this.$route.query.title
      }
    ]
  },
  async mounted() {
    // getZhiLiaoMbMlByLb(1)
    // getZhiLiaoMbZh()
    // saveZhiLiaoMbMl()
    // saveZhiLiaoMbZh()
    await this.getDataList()
  },
  methods: {
    async getDataList() {
      let modelData = await zhiLiaoMbInit()
      let muBanLB = modelData.data.leiBie
      let dataList = []
      const that = this
      modelData.data.leiBie.map(async (item) => {
        let modelChild = await getZhiLiaoMbMlByLb(item.daiMa)
        dataList.push({
          name: item.mingCheng,
          children: modelChild.data,
          daiMa: item.daiMa
        })
        that.modelData = dataList
        // that.modelData.push({
        //   name: item.mingCheng,
        //   children: modelChild.data,
        //   daiMa: item.daiMa
        // })
        if (that.modelData.length === muBanLB.length) {
          that.modelData.sort((a, b) => a.daiMa - b.daiMa)
        }
      })
      console.log('this.modelData:', this.modelData)
    },
    async onModelClick(item, item2) {
      this.selData.moBanID = item.moBanID
      this.selData.mingCheng = item.mingCheng
      this.selData.daiMa = item2.daiMa
      let MbZh = await getZhiLiaoMbZh({ muBanID: item.moBanID })
      this.tableData = MbZh.data
      this.tableData.map((item) => {
        if (item.teShuSM == null) {
          item.teShuSM = 'null'
        }
        if (item.yiZhuMLMC == null) {
          item.yiZhuMLMC = 'null'
        }
        if (item.zhiXingFF == null) {
          item.zhiXingFF = 'null'
        }
        if (item.zhiXingPL == null) {
          item.zhiXingPL = 'null'
        }
        if (item.zhiXingSJ == null) {
          item.zhiXingSJ = 'null'
        }
      })
      this.currentModelName = item.mingCheng
      console.log(this.tableData)
    },
    handleClose() {
      this.dialogVisible = false
      this.upDialogVisible = false
      this.delDialogVisible = false
    },
    handleAdd() {
      this.handleMode = 'add'
      this.dialogVisible = true
    },
    handleUpdate() {
      this.handleMode = 'update'
      this.upDialogVisible = true
      this.upMingCheng = this.selData.mingCheng
      this.upLeiBie = this.selData.daiMa
      this.upID = this.selData.moBanID
    },
    handleDelete() {
      this.handleMode = 'delete'
      this.delDialogVisible = true
    },
    async handleSave() {
      let addList = [
        {
          mingCheng: this.muBanMingCheng
        }
      ]
      let res = await saveZhiLiaoMbMl({
        leiBie: this.muBanLeiBie,
        addList
      })
      if (res.hasError === 0) {
        this.handleClose()
        await this.getDataList()
      }
    },
    async MlUpdate() {
      let modifyList = [
        {
          moBanID: this.upID,
          mingCheng: this.upMingCheng
        }
      ]
      let res = await saveZhiLiaoMbMl({
        leiBie: this.upLeiBie,
        modifyList
      })
      if (res.hasError === 0) {
        this.handleClose()
        await this.getDataList()
      }
    },
    async MlDelete() {
      let deleteList = [
        {
          moBanID: this.selData.moBanID
        }
      ]
      let res = await saveZhiLiaoMbMl({
        leiBie: this.selData.daiMa,
        deleteList
      })
      if (res.hasError === 0) {
        this.handleClose()
        await this.getDataList()
      }
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-button {
  height: 32px;
  border-radius: 4px;
}
::v-deep .el-button:hover {
  background-color: #3b76ef;
}
::v-deep .el-collapse-item__content {
  padding-bottom: 0;
}
::v-deep .el-collapse-item__header::before {
  display: inline-block;
  width: 3px;
  height: var(--font-size-regular);
  margin-right: -webkit-calc(var(--common-margin) / 2);
  margin-right: -moz-calc(var(--common-margin) / 2);
  margin-right: calc(var(--common-margin) / 2);
  content: '';
  background-color: var(--color-primary);
}
.container {
  background-color: #fff;
}
.tag-bar {
  padding: 0px 10px;
}
.content {
  padding: 10px 70px;
  display: flex;
  .left {
    background-color: #eff3fb;
    // width: 382px;
    height: 800px;
    padding: 14px;
    overflow: auto;
    .left-button-group {
      width: 354px;
      height: 58px;
      margin-bottom: 14px;
      box-shadow: 0 0 0 0.08px black;
      border-radius: 4px;
      padding: 13px 14px;
    }
    .model-group {
      .model-item {
        width: 354px;
        box-shadow: 0 0 0 0.04px black;
        margin-bottom: 14px;
        .model-item-child {
          font-size: 14px;
          padding: 12px;
          user-select: none;
          border: 1px solid #dcdfe6;
        }
        .model-item-child:hover {
          background-color: #6787cc;
          color: #fff;
          cursor: pointer;
        }
        ::v-deep .el-collapse-item__content {
          padding: 12px;
          border: 1px solid #dcdfe6;
        }
      }
    }
  }
  .right {
    background-color: #eff3fb;
    // width: 1273px;
    height: 800px;
    margin-left: 10px;
    overflow: auto;
    padding: 14px;
    border-radius: 4px;
    .right-header {
      display: flex;
      align-items: center;
      margin-bottom: 14px;
      .right-header-title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }
      .right-header-title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      .right-button-group {
        margin-left: 27px;
      }
    }
  }
}
::v-deep .el-dialog__body {
  padding: 20px 20px 10px;
}
td {
  border: 1px solid #ddd;
  border-collapse: collapse; /* 移除表格内边框间的间隙 */
  height: 35px;
  padding: 10px;
}
.info-label {
  text-align: right;
  width: 10vw;
  background-color: #eaf0f9;
}
.info-value {
  width: 14vw;
  height: 30px;
  background-color: #f7f9fd;
}
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.no-padding {
  padding: 0;
  .no-padding-label {
    padding: 10px;
  }
  ::v-deep .el-button {
    flex: 1;
    height: 100%;
  }
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
.muBanLX {
  display: flex;
  align-items: center;
  span {
    margin-right: 20px;
  }
}
</style>
