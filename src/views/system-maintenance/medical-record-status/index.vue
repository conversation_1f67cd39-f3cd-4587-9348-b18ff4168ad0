<template>
  <div class="container">
    <div class="header">
      <div class="header-search">
        <span class="hint-info">
          <i class="el-icon-warning" />
          <span>
            电子病历解封前注意事项:
            <div>
              1、询问病历修改原因和修改内容，必要时要打开（点击患者的名字）病历，检查病历书写质量。
            </div>
            <div>
              2、出院录、与出院录有相关的各种病历记录内容不能修改(如:诊断、治疗方案、手术方案、手术记录等），因为，患者手中已有出院录，会造成前后相关内容不对应。
            </div>
            <div>3、询问病历的患者有无存在纠纷，如有纠纷，不能解封。</div>
            <div>4、询问病历有无被患者复印，如有复印，不能解封。</div>
          </span>
        </span>
      </div>
      <div class="header-search">
        <div class="query-word">请输入病案号：</div>
        <div>
          <el-input v-model="bingAnHao" placeholder="请填写" @change="onQuery"></el-input>
        </div>
        <div class="radio-group">
          <el-radio-group v-model="stype">
            <el-radio label="1">医生解封</el-radio>
            <el-radio label="2">护士解封</el-radio>
          </el-radio-group>
        </div>
        <div class="button">
          <el-button type="primary" @click="onQuery">查询</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">修改病历状态</div>
      </div>
      <div class="table">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="bingRenXM" label="姓名">
            <template #default="scope">
              <a @click="goToBingRenXQ(scope.row)">{{ scope.row.bingRenXM }}</a>
            </template>
          </el-table-column>
          <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
          <el-table-column prop="bingAnHao" label="病案号"></el-table-column>
          <el-table-column prop="zhuanKeMC" label="专科"></el-table-column>
          <el-table-column prop="bingQuMC" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
          <el-table-column prop="bingQuRuYuanSJ" label="入院日期" width="150"></el-table-column>
          <el-table-column prop="chuYuanSJ" label="出院日期" width="150"></el-table-column>
          <el-table-column prop="ruYuanZD" label="入院诊断"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" label="病历状态"></el-table-column>
          <el-table-column prop="" label="复印状态"></el-table-column>
          <el-table-column label="修改病历状态	">
            <template #default="scope">
              <el-button
                v-if="scope.row.zhuangTaiBZ != '已封存'"
                type="text"
                size="small"
                @click="handleClick(scope.row, 1)"
              >
                封存
              </el-button>
              <el-button v-else type="text" size="small" @click="handleClick(scope.row, 2)">
                解封
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="修改其他状态">
            <template #default="scope">
              <el-button v-if="scope.row.zhuangTaiBZ != '已封存'" type="text" size="small">
                封存
              </el-button>
              <el-button v-else type="text" size="small">解封</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="" label="病案首页状态"></el-table-column>
          <el-table-column label="解封病案首页">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row, 3)">
                解封
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getListByEmpi } from '@/api/information'
import { fengCun, huShiJF, yiShiJF } from '@/api/system-maintenance'
import { format } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      stype: '',
      bingAnHao: '',
      tableData: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  methods: {
    async onQuery() {
      const res = await getListByEmpi({ bingAnHao: this.bingAnHao })
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    },
    goToBingRenXQ(row) {
      const formatChuangWeiHao = (bingQuMC, chuangWeiHao) => {
        return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
      }
      this.$router.push({
        path: `/patient-detail/${row.bingLiID}`,
        query: {
          title: `${row.jieSuanLXMC || ''} ${formatChuangWeiHao(row.bingQuMC, row.chuangWeiHao)} ${
            row.xingMing
          }`
        }
      })
    },
    async handleClick(row, type) {
      if (type === 1) {
        this.$confirm('是否确定封存, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          const res = await fengCun({
            yongHuID: this.yongHuID,
            bingLiID: row.bingLiID
          })
          if (res.hasError === 0) {
            this.$message.success('封存成功！')
          }
        })
      } else if (type === 2) {
        this.$confirm('是否确定解封, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          if (this.stype === '1') {
            const res = await yiShiJF({
              yongHuID: this.yongHuID,
              bingLiID: row.bingLiID
            })
            if (res.hasError === 0) {
              this.$message.success('解封成功！')
              this.onQuery()
            }
          } else {
            const res = await huShiJF({
              yongHuID: this.yongHuID,
              bingLiID: row.bingLiID
            })
            if (res.hasError === 0) {
              this.$message.success('解封成功！')
              this.onQuery()
            }
          }
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  // display: flex;
  // align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .header-search-tip {
    display: flex;
    align-items: center;
    padding: 10px 4px 0 4px;
    .hint-info {
      margin-left: 10px;
      display: flex;
      background-color: #e4ecf9;
      border: 1px solid #86abe8;
      border-radius: 3px;
      padding: 3px 10px;
      font-weight: 400;
      .el-icon-warning {
        margin: 3px 3px 0 0;
        color: #356ac5;
      }
    }
  }
  .header-search {
    display: flex;
    align-items: center;
    padding: 8px 14px;
    .radio-group {
      padding-left: 10px;
    }
    .query-word {
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 10px;
      margin-right: 10px;
    }
    .query-word:first-child {
      margin-left: 0px;
    }
    ::v-deep .el-button {
      background-color: #a66dd4;
      border: 1px solid #a66dd4;
      color: #fff;
    }
    .button {
      margin-left: 6px;
    }
  }
}
.content {
  background-color: #eaf0f9;
  height: 580px;
  padding: 14px;
  .table {
    height: 100%;
  }
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

::v-deep .el-table__cell:last-child {
  padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
