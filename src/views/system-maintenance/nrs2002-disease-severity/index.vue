<template>
  <div class="container">
    <div class="header">
      <div style="display: flex; align-items: center">
        <div class="query-value">
          <el-select v-model="ruleForm.leiBie" @change="getNeiBu">
            <el-option
              v-for="item in baseInfo['LeiBie']"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
        <div class="query-value">
          <el-select v-model="ruleForm.neiBuLBDM" @change="query">
            <el-option
              v-for="item in baseInfo['NeiBuLB']"
              :key="item.daiMa"
              :label="item.mingCheng"
              :value="item.daiMa"
            ></el-option>
          </el-select>
        </div>
      </div>
    </div>
    <div class="content" style="flex-direction: row">
      <div class="content-border" style="width: 35%">
        <el-table
          :data="tableData"
          border
          size="medium"
          height="100%"
          highlight-current-row
          @current-change="handleCurrentChange"
        >
          <el-table-column prop="mingCheng" label="名称"></el-table-column>
          <el-table-column prop="beiZhu" label="选项">
            <template #default="scope">
              {{ scope.row.beiZhu.split('^')[2] + '分 ' + scope.row.beiZhu.split('^')[1] }}
            </template>
          </el-table-column>
          <el-table-column prop="fuJiaSX" label="说明"></el-table-column>
          <el-table-column prop="xingBie" label="原NRS2002对照"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" label="状态">
            <template #default="scope">
              {{ scope.row.zhuangTaiBZ === '1' ? '启用' : '停用' }}
            </template>
          </el-table-column>
          <el-table-column prop="xingBie" label="数量"></el-table-column>
        </el-table>
      </div>
      <div class="content-border" style="width: 35%">
        <el-table :data="tableData2" border size="medium" height="100%">
          <el-table-column type="index" label="序号"></el-table-column>
          <el-table-column prop="mingCheng" label="名称"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" label="状态">
            <template #default="scope">
              {{ scope.row.zhuangTaiBZ === '1' ? '启用' : '停用' }}
            </template>
          </el-table-column>
          <el-table-column prop="fuJiaSX" label="ICD"></el-table-column>
          <el-table-column label="操作" align="center">
            <template #default="scope">
              <el-button type="text" size="medium" @click="delICD(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="content-border" style="width: 30%; margin-right: 0">
        <div class="border-value">
          <el-input v-model="wenBen"></el-input>
          <div class="button">
            <el-button type="primary" style="margin-left: 10px" @click="getICD">查询</el-button>
          </div>
        </div>
        <div class="content-header">
          <div v-if="ruleForm.leiBie === 'YYJB'" class="title">诊断库</div>
          <div v-else-if="ruleForm.leiBie === 'YYSS'" class="title">手术库</div>
          <div>
            <el-button type="primary" @click="addICD">添加</el-button>
          </div>
        </div>
        <div v-if="ruleForm.leiBie === 'YYJB'" class="content-table">
          <div class="content-table">
            <el-table
              :data="tableData3"
              border
              size="medium"
              height="100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="mingCheng" label="诊断名称"></el-table-column>
              <el-table-column prop="icd" label="诊断ICD" width="160"></el-table-column>
            </el-table>
          </div>
          <el-pagination
            :current-page.sync="pageIndex"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            :total="total"
            @current-change="setPage"
          ></el-pagination>
        </div>
        <div v-else-if="ruleForm.leiBie === 'YYSS'" class="content-table">
          <div class="content-table">
            <el-table
              :data="tableData4"
              border
              size="medium"
              height="100%"
              @selection-change="handleSelectionChange2"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="shouShuMC" label="手术名称"></el-table-column>
              <el-table-column prop="icddm" label="手术ICD" width="160"></el-table-column>
            </el-table>
          </div>
          <el-pagination
            :current-page.sync="pageIndex2"
            :page-size="pageSize"
            layout="total, prev, pager, next"
            :total="total2"
            @current-change="setPage"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { format } from 'date-fns'
import { getAllWardList, getICUFenJiStatisticVo, getRepPatList } from '@/api/information'
import { getEndoscopyInPatientList, getEndoscopyTypeList } from '@/api/department-menus'
import {
  addDiagnoseICD,
  addSurgeryICD,
  deleteICD,
  getDiagnosesInfoByPinYinOrWuBi,
  getICDS,
  getKeyWordData,
  getLeiBie,
  getNeiBuLB,
  getYlSsicdByPage
} from '@/api/system-maintenance'
import { getInfectiousDiseaseDiagnosis } from '@/api/report-card'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {},
  data() {
    return {
      baseInfo: { LeiBie: [], NeiBuLB: [] },
      ruleForm: {
        leiBie: '',
        neiBuLBDM: ''
      },
      tableData: [],
      tableData2: [],
      tableData3: [],
      tableData3Z: [],
      tableData4: [],
      wenBen: '',
      total: 0,
      total2: 0,
      pageIndex: 1,
      pageIndex2: 1,
      pageSize: 12,
      keyWordDM: '',
      selectData: [],
      selectData2: []
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      await this.initGetLeiBie()
      await this.getData3()
      await this.getData4()
    },
    async initGetLeiBie() {
      this.baseInfo['LeiBie'] = []
      let res = await getLeiBie()
      for (const key in res.data) {
        const d = {
          id: key,
          name: res.data[key]
        }
        if (key === 'YYJB' || key === 'YYSS') {
          this.baseInfo['LeiBie'].push(d)
        }
      }
      this.ruleForm.leiBie = this.baseInfo['LeiBie'][0].id
      await this.getNeiBu(this.ruleForm.leiBie)
    },
    async getData3() {
      const res = await getDiagnosesInfoByPinYinOrWuBi({
        leiBie: 'PY',
        pinYinOrWuBi: this.wenBen
      })
      this.tableData3Z = res.data
      this.total = this.tableData3Z.length
      this.tableData3 = this.tableData3Z.slice(
        (this.pageIndex - 1) * this.pageSize,
        this.pageIndex * this.pageSize
      )
    },
    async getData4() {
      const res = await getYlSsicdByPage({
        pageIndex: this.pageIndex2,
        pageSize: this.pageSize,
        wenBen: this.wenBen
      })
      this.total2 = res.extendData.total
      this.tableData4 = res.data
    },
    async getICD() {
      this.pageIndex = 1
      if (this.ruleForm.leiBie === 'YYJB') {
        await this.getData3()
      } else if (this.ruleForm.leiBie === 'YYSS') {
        await this.getData4()
      }
    },
    async setPage(pageIndex) {
      if (this.ruleForm.leiBie === 'YYJB') {
        this.pageIndex = pageIndex
        this.tableData3 = this.tableData3Z.slice(
          (this.pageIndex - 1) * this.pageSize,
          this.pageIndex * this.pageSize
        )
      } else if (this.ruleForm.leiBie === 'YYSS') {
        this.pageIndex2 = pageIndex
        await this.getData4()
      }
    },
    async getNeiBu(value) {
      const res = await getNeiBuLB({ leiBie: value })
      this.baseInfo['NeiBuLB'] = res.data
      this.ruleForm.neiBuLBDM = this.baseInfo['NeiBuLB'][0].daiMa
      await this.query()
    },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      const res = await getKeyWordData(reqData)
      // if (res.hasError === -1) {
      //   this.$message.error(res.errorMessage)
      // } else {
      //   this.$message({
      //     message: res.errorMessage,
      //     type: 'success'
      //   })
      // }
      this.tableData = res.data
    },
    async handleCurrentChange(val) {
      this.keyWordDM = val.daiMa
      const res = await getICDS({
        KeyWordDM: val.daiMa,
        leiBie: this.ruleForm.leiBie
      })
      this.tableData2 = res.data
    },
    handleSelectionChange(val) {
      this.selectData = val
      console.log(val)
    },
    handleSelectionChange2(val) {
      this.selectData2 = val
      console.log(val)
    },
    async addICD() {
      if (this.keyWordDM === '') {
        this.$message({
          message: '请先选择一个疾病或手术',
          type: 'warning'
        })
        return
      }
      if (this.ruleForm.leiBie === 'YYJB') {
        if (this.selectData.length > 0) {
          const res = await addDiagnoseICD({ keyWordDM: this.keyWordDM }, this.selectData)
          console.log(res)
        }
      } else if (this.ruleForm.leiBie === 'YYSS') {
        if (this.selectData2.length > 0) {
          const res = await addSurgeryICD({ keyWordDM: this.keyWordDM }, this.selectData2)
          console.log(res)
        }
      }
      await this.handleCurrentChange({ daiMa: this.keyWordDM })
    },
    async delICD(row) {
      await deleteICD({
        ICDS: row.fuJiaSX,
        keyWordDM: this.keyWordDM,
        leiBie: this.ruleForm.leiBie
      })
      await this.handleCurrentChange({ daiMa: this.keyWordDM })
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }
  .content-border {
    display: flex;
    flex-direction: column;
    height: 100%;
    border: 1px solid #dcdfe6;
    padding: 14px;
    margin-right: 14px;
    border-radius: 4px;

    .border-value {
      display: flex;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      padding: 14px;
      margin-bottom: 12px;
    }
  }

  .content-table {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 200px;
    min-width: 200px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

.red-star {
  color: red;
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
