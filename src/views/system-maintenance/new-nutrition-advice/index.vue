<!-- 新营养医嘱单 -->
<template>
  <div class="nutrition-advice">
    <div class="inpatient-content">
      <div class="table-title">
        <div>
          <span class="bar" />
          营养医嘱单
        </div>
        <div>
          <el-button
            type="primary"
            @click="
              editData = { yaoPinXX: [], yingYangYZD: {} }
              $nextTick(() => {
                $refs['editDialog'].initFormData()
                visible = true
                dialogType = 0
              })
            "
          >
            新增
          </el-button>
          <!-- <el-button type="primary">刷新</el-button> -->
        </div>
      </div>
      <div class="table-container">
        <el-table
          :data="nutritionData"
          style="width: 100%"
          height="100%"
          stripe
          border
          :row-class-name="tableRowClassName"
          @row-click="handleRowClicked"
        >
          <el-table-column type="index" align="center" width="50"></el-table-column>
          <el-table-column
            v-for="(column, index) in columns"
            :key="index"
            :prop="column.value"
            :label="column.label"
            v-bind="column.props"
          >
            <template #default="scope">
              <template v-if="column.label === '操作'">
                <div style="display: flex; align-items: center; justify-content: center">
                  <el-button type="text" @click="openEdit(scope.row)">编辑</el-button>
                  <el-divider direction="vertical" />
                  <el-button
                    type="text"
                    :disabled="scope.row.zhuangTaiBZ === '0'"
                    @click="
                      hintVisible = true
                      editData = scope.row
                    "
                  >
                    停止
                  </el-button>
                  <el-divider direction="vertical" />
                  <el-button type="text" @click="pagePrint(scope.row)">打印</el-button>
                </div>
              </template>
              <template v-else-if="column.label === '医嘱状态'">
                <el-tag v-if="scope.row.zhuangTaiBZ === '0'" type="danger">已停止</el-tag>
                <el-tag v-if="scope.row.zhuangTaiBZ === '1'">未提交</el-tag>
                <el-tag v-if="scope.row.zhuangTaiBZ === '2'" type="success">已提交</el-tag>
              </template>
              <template v-else>
                {{ scope.row[column.value] }}
              </template>
            </template>
          </el-table-column>
        </el-table>

        <div v-show="false">
          <div id="printableDiv">
            <print-page
              v-if="printState"
              ref="printPage"
              :drug-info-list="drugInfoList"
              :crrt-info="crrtInfo"
              :vital-sign="vitalSign"
              :nutrition-data.sync="editData"
            />
          </div>
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="hintVisible" width="350px">
      <span slot="title">
        <span style="font-size: 16px">
          <i class="el-icon-warning"></i>
          停止提示
        </span>
      </span>
      <div class="hint-component">确认停止吗?</div>

      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="
            hintVisible = false
            stopnutrition(editData)
          "
        >
          确 认
        </el-button>
        <el-button @click="hintVisible = false">取 消</el-button>
      </span>
    </el-dialog>

    <edit-dialog
      ref="editDialog"
      :visible.sync="visible"
      :drug-info-list="drugInfoList"
      :nutrition-info="nutritionInfo"
      :crrt-info="crrtInfo"
      :dialog-type="dialogType"
      :save-limits="saveLimits"
      :ren-yuan-ku-i-d="renYuanKuID"
      :vital-sign="vitalSign"
      :nutrition-data.sync="editData"
      @upload-success="breakData"
    />
  </div>
</template>

<script>
import {
  getDoctorInfoByUserID,
  getYyyzdByBlid,
  getYyypByYuanQuDM,
  getTestResultsForCrrt,
  checkNewNutritionDocAdvicePrivi,
  getVitalSignList,
  getYlYyyzdYpByYzdId,
  stopYlyyyzd
} from '@/api/nutrition-advice'
import EditDialog from './components/EditDialog.vue'
import PrintPage from './components/PrintPage.vue'
import { mapState } from 'vuex'

export default {
  name: 'NutritionAdvice',
  components: { EditDialog, PrintPage },
  data() {
    return {
      saveLimits: false,
      vitalSign: null,
      visible: false,
      drugInfoList: [],
      dialogType: 0, // 0 新增 1 编辑 2 删除
      hintVisible: false,
      hintType: 0, //0 删除 1停止
      editData: {},
      renYuanKuID: null,
      // bingQuID: '3615', //当前病人病区
      // bingLiID: '2616474', //当前病人
      // patientInit: {
      //   bingRenBH: '33030099000000004200854644',
      //   jieSuanDM: 'Y09',
      //   chuShengRQ: '1962-07-26',
      //   chuangWeiHao: '001',
      //   lianXiDH: '15957707284',
      //   lianXiDZ: '龙港市凰浦北路25号',
      //   xingBie: '1',
      //   bingRenXM: '潘时章', //姓名
      //   zhuYuanHao: '2057930',
      //   zhuYuanID: 2616474,
      //   shouShuTZD: {
      //     hunYinZK: '2'
      //   }
      // },
      printState: false,
      yiZhuLB: '1', //医嘱类别 长期:1 临时:2 出院:3
      nutritionData: [],
      nutritionInfo: {},
      crrtInfo: [],
      clickedRowIndex: null
    }
  },
  computed: {
    bingLiID() {
      return this.$route.params.id
    },
    ...mapState({
      userInfo: (state) => state.user.userInfo,
      initInfo: ({ patient }) => patient.initInfo,
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,
      bingQuID: ({ patient }) => patient.bingQuID,
      patientInit: ({ patient }) => patient.patientInit
    }),
    columns() {
      return [
        { value: 'kaiShiSJ', label: '开始时间  ' },
        { value: 'chiXuTS', label: '持续天数', props: { width: '90' } },
        { value: 'jieShuSJ', label: '结束时间  ' },
        { value: 'yiZhuYSYHXM', label: '开单医师  ', props: { width: '170' } },
        { value: 'yiZhuSJ', label: '开单时间  ' },
        { value: 'tingZhiYSYHXM', label: '停止医师  ', props: { width: '170' } },
        { value: 'zhuangTaiBZ', label: '医嘱状态', props: { align: 'center', width: '90' } },
        { label: '操作', props: { fixed: 'right', width: '270', align: 'center' } }
      ]
    }
  },
  async mounted() {
    this.getNutritionData()

    const res = await getTestResultsForCrrt({
      bingLiID: this.bingLiID
    })

    console.log('根据病历ID查询化验结果_初始化调用', res)
    if (res.hasError === 0) {
      this.crrtInfo = res.data
    }

    const res1 = await getYyypByYuanQuDM({
      yuanQuDM: this.userInfo.yuanQuDM,
      zhuanKeID: this.initInfo.zhuanKeID
    })

    console.log('获取营养医嘱药品信息(e_GetYyypList)', res1)
    if (res1.hasError === 0) {
      this.drugInfoList = res1.data
    }

    const res2 = await checkNewNutritionDocAdvicePrivi()

    console.log('判断用户是否有保存权限', res2)
    if (res2.hasError === 0) {
      this.saveLimits = res2.data
    }

    const res3 = await getDoctorInfoByUserID({ yongHuID: this.userInfo.yongHuID })

    console.log('通用_查询_根据用户ID获取医疗人院信息', res3)
    if (res3.hasError === 0) {
      this.renYuanKuID = res3.data.renYuanKuID
    }

    const res4 = await getVitalSignList({ bingLiID: this.bingLiID })

    console.log('获取病人生命体征', res4)
    if (res4.hasError === 0 && res4.data.length > 0) {
      this.vitalSign = {
        shenGao: res4.data[0].shenGao,
        tiZhong: res4.data[0].tiZhong
      }
    }
  },
  methods: {
    async pagePrint(row) {
      const res = await getYlYyyzdYpByYzdId({ yiZhuDanID: row.id })

      console.log('根据ID获取医嘱单药品信息(e_GetYyyzd_yp)', res)
      if (res.hasError === 0) {
        this.editData = {
          yaoPinXX: res.data,
          yingYangYZD: row
        }
      }
      this.printState = true
      this.$nextTick(async () => {
        await this.$refs['printPage'].initData()

        // 创建一个临时的iframe，用于生成打印内容
        var iframe = document.createElement('iframe')
        document.body.appendChild(iframe)
        var doc = iframe.contentWindow.document
        doc.write('<html><head><title>Print Page</title></head><body>')
        doc.write(document.getElementById('printableDiv').innerHTML) // 仅包含需要打印的内容
        doc.write('</body></html>')
        doc.close()
        iframe.contentWindow.focus()
        iframe.contentWindow.print()
        this.printState = false
        // 打印完成后可以选择移除iframe或保留，根据需求决定
        // document.body.removeChild(iframe);
      })
    },
    async openEdit(row) {
      const res = await getYlYyyzdYpByYzdId({ yiZhuDanID: row.id })

      console.log('根据ID获取医嘱单药品信息(e_GetYyyzd_yp)', res)
      if (res.hasError === 0) {
        this.editData = {
          yaoPinXX: res.data,
          yingYangYZD: row
        }
        this.$nextTick(() => {
          this.$refs['editDialog'].initFormData()
          this.visible = true
          this.dialogType = 1
        })
      }
    },
    //初始化列表
    async getNutritionData() {
      const res = await getYyyzdByBlid({
        bingLiID: this.bingLiID
      })

      console.log('根据病例ID获取YlYyyzd(e_GetYyyzdList)', res)
      if (res.hasError === 0) {
        this.nutritionData = res.data
      }
    },
    //停止
    async stopnutrition(data) {
      const res = await stopYlyyyzd({
        yiShengYHID: this.userInfo.yongHuID,
        yiZhuDanID: data.id,
        zuHao: data.zuHao
      })

      console.log('停止医嘱(e_StopYyyzd)', res)
      if (res.hasError === 0) {
        this.$message({
          message: '停止成功',
          type: 'success',
          duration: 700
        })
        this.getNutritionData()
      }
    },
    breakData() {
      this.visible = false
      this.getNutritionData()
    },
    // 是否可选
    isRowSelectable(row) {
      return false
    },
    // 行点击高亮
    handleRowClicked(row) {
      return
      const index = this.nutritionData.indexOf(row)
      if (this.clickedRowIndex === index) {
        this.clickedRowIndex = null
      } else {
        this.clickedRowIndex = this.nutritionData.indexOf(row)
      }
    },
    // 动态绑定行类名
    tableRowClassName({ row, rowIndex }) {
      let className = ''
      if (rowIndex === this.clickedRowIndex) {
        className += 'selected-row '
      }
      switch (row.yanSe) {
        case 'blue':
          className += 'blue-row '
          break
        case 'red':
          className += 'red-row '
          break
        case 'black':
        default:
          className += ''
      }
      return className.trim()
    }
  }
}
</script>

<style scoped lang="scss">
.nutrition-advice {
  background: #eff3fb;
  padding: 8px;
  height: 100%;

  ::v-deep .el-tabs--border-card {
    box-shadow: none;
    background: #eff3fb;

    .el-tabs__header .el-tabs__nav {
      .el-tabs__item {
        height: 32px;
        line-height: 32px;
        background: #ffffff;
        border: 1px solid #dcdfe6;

        &.is-active {
          background: #eff3fb;
          border-bottom-color: transparent;
        }
      }
    }

    .el-tabs__content {
      padding: 0px;

      .el-tab-pane {
        display: flex;
        flex-direction: column;
      }
    }
  }

  .inpatient-content {
    padding-top: 10px;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    .table-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 600;
      margin-bottom: 10px;

      .bar {
        border-left: 3px solid #356ac5;
        padding-left: 5px;
      }

      .sub-title {
        font-weight: 400;
      }
    }

    .table-container {
      flex: 1;
      position: relative;

      ::v-deep .el-drawer__wrapper {
        position: absolute;
        pointer-events: none;

        .el-drawer__header {
          padding: 4px 8px;

          .drawer-header {
            display: flex;
            align-items: center;
            position: relative;

            .title {
              margin-bottom: 0;
              margin-right: 8px;
            }

            .full-button {
              position: absolute;
              top: -4px;
              left: 50%;
              transform: translateX(-50%);
              width: 68px;
              height: 16px;
              text-align: center;
              line-height: 16px;
              font-size: 16px;
              background: #ecf1f9;
              border-radius: 0 0 6px 6px;
              cursor: pointer;
            }
          }
        }

        .el-drawer__container {
          pointer-events: none;
        }

        .el-drawer {
          pointer-events: auto;
        }
      }
    }

    .footer-action {
      margin-top: 8px;
      height: 58px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      background: #eaf0f9;
      display: flex;
      align-items: center;
      padding-left: 10px;

      .el-checkbox {
        margin-left: var(--common-margin);
        margin-right: 0;
      }
    }

    ::v-deep .el-table {
      .el-checkbox__input.is-disabled {
        display: none;
      }

      .el-checkbox__inner {
        width: 16px;
        height: 16px;

        &:after {
          height: 8px;
          left: 5px;
        }
      }

      .selected-row td.el-table__cell {
        background-color: #6787cc;
        color: #fff;
      }

      .blue-row td.el-table__cell {
        color: #356ac5;
      }

      .red-row td.el-table__cell {
        color: #f56c6c;
      }

      .hover-row:not(.selected-row) td.el-table__cell {
        background: #eff3fb;
      }
    }
  }

  :deep(.el-dialog__footer) {
    border-top: none;
  }

  :deep(.el-table__fixed-body-wrapper) {
    z-index: 0;
  }

  .el-icon-warning {
    font-size: 20px;
    color: #ed6a0c;
  }

  .hint-component {
    padding-left: 30px;
    color: #96999e;

    a {
      color: #356ac5;
    }
  }
}
</style>
