<template>
  <div class="container">
    <div class="header">
      <div class="query-word">治疗组:</div>
      <el-select v-model="ZhiLiaoZu" filterable @change="handleBingQuChange">
        <el-option
          v-for="item in ZhiLiaoZuList"
          :key="item.id"
          :label="item.mingCheng"
          :value="item.id"
        ></el-option>
      </el-select>
      <div class="query-word">报告时间：</div>
      <el-date-picker
        v-model="zhuanKeSJ"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        format="yyyy-MM-dd"
        @change="changeDay"
      ></el-date-picker>
      <!-- <div class="button">
        <el-button type="primary" @click="getInpatientFinishedReportInfo">查询</el-button>
      </div> -->
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">专科本治疗组特检报告查询一览表</div>
      </div>
      <div class="table">
        <el-table max-height="630" border stripe :data="patientData" style="width: 1032px">
          <el-table-column type="index" align="center" label="序号" width="60">
            <template #default="{ $index }">
              {{ (listPagination.currentPage - 1) * listPagination.pageSize + $index + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            prop="bingRenXM"
            align="center"
            width="130"
            label="病人姓名"
          ></el-table-column>
          <el-table-column prop="empi" align="center" width="160" label="病案号"></el-table-column>
          <el-table-column prop="bingQu" align="center" width="160" label="病区-床位号">
            <template #default="{ row }">
              <span>{{ row.bingQu }}-{{ row.chuangWeiHao }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="jianChaLX"
            align="center"
            width="160"
            label="检查类型"
          ></el-table-column>
          <el-table-column
            prop="xiuGaiSJ"
            align="center"
            width="200"
            label="报告时间"
          ></el-table-column>
          <el-table-column prop="caoZuo" align="center" width="160" label="操作">
            <template #default="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">
                查看病人报告
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          class="pagination"
          :current-page="listPagination.currentPage"
          :page-size="listPagination.pageSize"
          :page-sizes="[10, 20, 30]"
          background
          layout="total,sizes, prev, pager, next"
          :total="patientLength"
          @size-change="handleSizeChange"
          @current-change="listPageChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { getZhiLiaoZuListByRYKID, getInpatientFinishedReportInfo } from '@/api/specialized-patients'
import { format, subMonths } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      patientData: [], //数据列表
      patientLength: 0, //数据列表长度
      listPagination: {
        pageSize: 20,
        currentPage: 1
      }, //列表分页
      ZhiLiaoZu: '', //选择治疗组
      ZhiLiaoZuList: [], //治疗组列表
      zhuanKeSJ: [format(subMonths(new Date(), 3), 'yyyy-MM-dd'), format(new Date(), 'yyyy-MM-dd')], //默认展示时间
      kaiShiSJ: format(subMonths(new Date(), 3), 'yyyy-MM-dd'), //开始时间
      jieShuSJ: format(new Date(), 'yyyy-MM-dd') //结束时间
    }
  },
  computed: {
    ...mapState({
      renYuanKuID: ({ patient }) => patient.doctorInfo.renYuanKuID,
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.getZhiLiaoZuListByRYKID()
  },
  methods: {
    // 根据人员库ID获取治疗组列表
    async getZhiLiaoZuListByRYKID() {
      try {
        const res = await getZhiLiaoZuListByRYKID({
          renYuanKuID: this.renYuanKuID
        })
        if (res.hasError === 0) {
          this.ZhiLiaoZuList = res.data
          this.ZhiLiaoZu = this.ZhiLiaoZuList[0].id
          this.getInpatientFinishedReportInfo()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取列表初始化接口
    async getInpatientFinishedReportInfo() {
      try {
        const res = await getInpatientFinishedReportInfo({
          kaiShiSJ: this.kaiShiSJ + ' 00:00:00',
          jieShuSJ: this.jieShuSJ + ' 23:59:59',
          pageNum: this.listPagination.currentPage,
          pageSize: this.listPagination.pageSize,
          zhiLiaoZuID: this.ZhiLiaoZu,
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.patientData = res.data.fininshedReportVoList
          this.patientLength = res.data.total
          this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 查询列表分页处理
    listPageChange(page) {
      this.listPagination.currentPage = page
      this.getInpatientFinishedReportInfo()
    },

    // 每页条数切换
    handleSizeChange(pageSize) {
      this.listPagination.pageSize = pageSize
      this.listPagination.currentPage = 1 // 切换条数后重置到第一页
      this.getInpatientFinishedReportInfo() // 重新加载数据
    },

    // 数据处理
    delArr() {
      this.patientData = this.patientData.map((item) => {
        const formattedItem = { ...item }
        if (item.xiuGaiSJ) {
          formattedItem.xiuGaiSJ = format(new Date(item.xiuGaiSJ), 'yyyy-MM-dd HH:mm:ss')
        }
        return formattedItem
      })
    },

    // 选择日期
    changeDay(e) {
      this.kaiShiSJ = format(new Date(e[0]), 'yyyy-MM-dd')
      this.jieShuSJ = format(new Date(e[1]), 'yyyy-MM-dd')
      // this.listPagination.pageSize = 20
      this.listPagination.currentPage = 1 // 切换条数后重置到第一页
      this.getInpatientFinishedReportInfo()
    },

    // 切换病区
    handleBingQuChange(id) {
      const selectedItem = this.ZhiLiaoZuList.find((item) => item.id === id)
      if (selectedItem) {
        this.ZhiLiaoZu = selectedItem.id
        // this.listPagination.pageSize = 20
        this.listPagination.currentPage = 1 // 切换条数后重置到第一页
        this.getInpatientFinishedReportInfo()
      }
    },

    // 点击查看病人报告
    handleClick(row) {
      let baoGaoLJ = `http://************/EHR/WebReportJump.aspx?targetUrl=tjbg/Tjbgcx.aspx?&as_zyh=${row.zhuYuanHao}&kssj=${this.kaiShiSJ}&jssj=${this.jieShuSJ}`
      window.open(baoGaoLJ, '_blank')
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  position: relative;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

.pagination {
  display: flex;
  justify-content: flex-start;
  position: absolute;
  bottom: 2.5%;
  right: 44.5%;
}

::v-deep .el-pager li {
  background: #fff;
}

::v-deep .el-table__cell {
  // padding-left: 8px;
  padding-top: 4px;
  padding-bottom: 4px;
}

// ::v-deep .el-table__cell:last-child {
//   padding-left: 0px;
// }

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

// ::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
//   padding-top: 4px;
//   padding-bottom: 4px;
// }
</style>
