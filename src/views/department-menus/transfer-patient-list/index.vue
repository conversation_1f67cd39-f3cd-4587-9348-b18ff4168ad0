<template>
  <div class="container">
    <div class="header">
      <el-select v-model="ZhiLiaoZu">
        <el-option
          v-for="item in ZhiLiaoZuList"
          :key="item.zhiLiaoZuID"
          :label="item.zhiLiaoZuMC"
          :value="item.zhiLiaoZuID"
        ></el-option>
      </el-select>
      <div class="query-word">转科日期：</div>
      <el-date-picker
        v-model="zhuanKeSJ"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="changeDay"
      ></el-date-picker>
      <div class="query-word">输入姓名:</div>
      <div>
        <el-input v-model="xingMing"></el-input>
      </div>
      <div class="query-word">输入病案号：</div>
      <div>
        <el-input v-model="bingAnHao"></el-input>
      </div>
      <div class="button">
        <el-button type="primary" @click="searchFun">查询</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">{{ kaiShiSJ }}至{{ jieShuSJ }}转科病人列表</div>
      </div>
      <div class="table">
        <el-table max-height="688" border stripe :data="patientData" style="width: 1531px">
          <el-table-column prop="bingQuMC" width="115" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="115" label="床号"></el-table-column>
          <el-table-column prop="xingMing" width="115" label="姓名"></el-table-column>
          <el-table-column prop="bingAnHao" width="115" label="病案号"></el-table-column>
          <el-table-column prop="xingBie" width="115" label="性别">
            <template #default="{ row }">
              <span>{{ row.xingBie == 1 ? '男' : '女' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="chuShengRQ" width="115" label="出生日期"></el-table-column>
          <el-table-column prop="ruYuanZD" width="245" label="入院诊断"></el-table-column>
          <el-table-column prop="ruYuanRQ" width="115" label="入院日期"></el-table-column>
          <el-table-column prop="zhuanChuSJ" width="165" label="转科时间"></el-table-column>
          <el-table-column prop="zhiLiaoZuMC" width="200" label="治疗组"></el-table-column>
          <el-table-column prop="caoZuo" width="114" align="center" label="操作">
            <template #default="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getEZyblbrTransferData, getZhiLiaoZuListByZhuanKeID } from '@/api/specialized-patients'
import { format, subDays } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      ZhiLiaoZu: '',
      ZhiLiaoZuList: [],
      zhuanKeSJ: '',
      kaiShiSJ: format(subDays(new Date(), 7), 'yyyy-MM-dd'), //开始时间
      jieShuSJ: format(new Date(), 'yyyy-MM-dd'), //结束时间
      // kaiShiSJ: '2023-05-01',
      // jieShuSJ: '2023-08-05',
      xingMing: '',
      bingAnHao: '',
      patientData: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.getEZyblbrTransferData()
    await this.getZhiLiaoZuListByZhuanKeID()
  },
  methods: {
    // 获取列表初始化接口
    async getEZyblbrTransferData() {
      try {
        const res = await getEZyblbrTransferData({
          kaiShiSJ: this.kaiShiSJ + ' 00:00:00',
          jieShuSJ: this.jieShuSJ + ' 23:59:59',
          zhiLiaoZuID: this.patientDetail.zhiLiaoZuID,
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.patientData = res.data
          this.patientData1 = res.data
          this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取专科治疗组列表
    async getZhiLiaoZuListByZhuanKeID() {
      try {
        const res = await getZhiLiaoZuListByZhuanKeID({
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.ZhiLiaoZuList = res.data.concat(
            {
              zhiLiaoZuID: 0,
              zhiLiaoZuMC: '未归属治疗组'
            },
            {
              zhiLiaoZuID: '-1',
              zhiLiaoZuMC: '转专科未处理治疗组'
            }
          )
        }
      } catch (error) {
        console.log(error)
      }
    },

    delArr() {
      this.patientData = this.patientData.map((item) => ({
        ...item,
        chuShengRQ: format(new Date(item.chuShengRQ), 'yyyy-MM-dd'),
        ruYuanRQ: format(new Date(item.ruYuanRQ), 'yyyy-MM-dd')
      }))
    },

    changeDay(e) {
      this.daterange = e
    },

    searchFun() {
      if (this.daterange) {
        this.kaiShiSJ = format(new Date(this.daterange[0]), 'yyyy-MM-dd')
        this.jieShuSJ = format(new Date(this.daterange[1]), 'yyyy-MM-dd')
      }
      this.patientData = this.patientData1.filter((item) => {
        // 1. 治疗组ID匹配（精确，空值跳过）
        let isZhiLiaoZuMatch = true
        if (this.ZhiLiaoZu != '') {
          isZhiLiaoZuMatch = item.zhiLiaoZuID == this.ZhiLiaoZu
        }

        // 2. 姓名匹配（模糊）
        let isXingMingMatch = true
        if (this.xingMing != '') {
          isXingMingMatch = item.xingMing.toLowerCase().includes(this.xingMing.toLowerCase())
        }

        // 3. 病案号匹配（模糊）
        let isBingAnHaoMatch = true
        if (this.bingAnHao != '') {
          isBingAnHaoMatch =
            item.bingAnHao != null && String(item.bingAnHao).includes(String(this.bingAnHao))
        }

        // 4. 时间范围匹配（空值跳过）
        let isTimeMatched = true
        if (this.kaiShiSJ && this.jieShuSJ) {
          const transferTime = new Date(item.zhuanChuSJ)
          const startTime = new Date(this.kaiShiSJ)
          const endTime = new Date(this.jieShuSJ)
          isTimeMatched = transferTime >= startTime && transferTime <= endTime
        }

        // 组合条件：任一条件满足即可（逻辑或）
        return isZhiLiaoZuMatch && isXingMingMatch && isBingAnHaoMatch && isTimeMatched
      })
      this.delArr()
    },

    handleClick(row) {
      const formatChuangWeiHao = (bingQuMC, chuangWeiHao) => {
        return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
      }
      this.$router.push({
        path: `/patient-detail/${row.bingLiID}`,
        query: {
          title: `${row.jieSuanLXMC || ''} ${formatChuangWeiHao(row.bingQuMC, row.chuangWeiHao)} ${
            row.xingMing
          }`
        }
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 1476px;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

::v-deep .el-table__cell:last-child {
  padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
