<template>
  <div class="container">
    <div class="header">
      <div class="header-search-top">
        <div class="query-word">选择类型:</div>
        <el-select v-model="selectValue" filterable>
          <el-option
            v-for="item in listData"
            :key="item.huiZhenLX"
            :label="item.huiZhenLXMC"
            :value="item.huiZhenLX"
          ></el-option>
        </el-select>
        <div class="button">
          <el-button type="primary" size="small" class="search-button" @click="onSerch">
            查询
          </el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">医务处审批排班</div>
      </div>
      <div>
        <el-row>
          <el-col :span="4">
            <el-checkbox v-model="zhouChongFu">周重复</el-checkbox>
            <el-button type="primary" size="small" class="search-button" @click="onAdd">
              保存
            </el-button>
            <el-table
              ref="multipleTable"
              class="table"
              :data="reYuanData"
              tooltip-effect="dark"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="mingCheng" label="姓名" width="120"></el-table-column>
            </el-table>
          </el-col>
          <el-col :span="20">
            <el-calendar>
              <template #dateCell="{ data }">
                <div @click="onNewDateSelect">
                  <p>{{ data.day.split('-').slice(-1)[0] }} {{ data.isSelected ? '✔️' : '' }}</p>
                  <!-- 假设你想在这里显示额外的数据 -->
                  <p v-for="(item, index) in isShow(data)" :key="index">
                    {{ item.huiZhenYSXM }}
                  </p>
                </div>
              </template>
            </el-calendar>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>
<script>
import { getBasicInfo, getPaiBanData } from '@/api/information'
import { format } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      patientData: {},
      //
      rangda: ['2025-2-1', '2025-28'],
      //类型
      listData: [],
      //人员
      reYuanData: [],
      selectValue: '',
      //周重复
      zhouChongFu: '',
      //选中人员
      dateList: {},
      dateTime: ''
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit,
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  async mounted() {
    await this.initData()
  },
  methods: {
    // 获取列表初始化接口
    async initData() {
      try {
        const res = await getBasicInfo()
        if (res.hasError === 0) {
          this.listData = res.data.paiBanLX
          // if()
          this.selectValue = this.listData[0].huiZhenLX
          await this.onSerch()
        }
      } catch (error) {
        console.log(error)
      }
    },
    async onSerch() {
      const msg = await getPaiBanData({
        kaiShiSJ: '2025-7-15 00:00:00',
        jieShuSJ: '2025-7-25 23:59:59',
        huiZhenLX: this.selectValue
      })
      if (msg.hasError === 0) {
        this.patientData = {}
        msg.data.map((item) => {
          if (this.patientData[item.paiBanRQ]) {
            this.patientData[item.paiBanRQ].push(item)
          } else {
            this.patientData[item.paiBanRQ] = [item]
          }
        })
      }
      if (this.selectValue) {
        this.reYuanData = []
        const fd = this.listData.filter((ev) => this.selectValue === ev.huiZhenLX)
        if (fd && fd[0]) {
          this.reYuanData = fd[0].paiBanRYList
        }
      }
    },
    //是否显示排版人员
    isShow(data) {
      if (this.patientData) {
        const fd = this.patientData[data.day]
        if (fd && fd.length > 0) {
          return fd
        }
        return ''
      }
    },
    handleSelectionChange(val) {
      if (this.dateTime) {
        this.dateList[this.dateTime] = val
        this.patientData = val
      }
    },
    //新增
    onNewDateSelect(val) {
      if (val.day) {
        this.dateTime = val.day
        if (!this.newDateList[val.day]) {
          this.newDateList[val.day] = []
        }
      }
    },
    onAdd() {}
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  .header-search-top {
    display: flex;
    align-items: center;
  }
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  .search-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.left-content {
  margin-top: 16px;
  font-size: 14px;
  line-height: 14px;
  color: #171c28;
  .left-content-title {
    padding: 9px 12px;
    background-color: #eaf0f9;
    border: 1px solid #dcdfe6;
  }
  .left-content-item {
    padding: 9px 12px;
    border: 1px solid #dcdfe6;
  }
  .left-content-item:nth-child(odd) {
    background-color: #eff3fb;
  }
  .left-content-item:nth-child(even) {
    background-color: #f6f6f6;
  }
  .left-content-item:hover {
    background-color: #6787cc;
    color: #fff;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.table {
  width: 180px;
  min-height: 500px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
.is-selected {
  color: #1989fa;
}
</style>
