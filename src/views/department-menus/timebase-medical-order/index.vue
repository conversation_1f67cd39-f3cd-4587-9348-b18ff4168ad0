<template>
  <div class="container">
    <div class="content" style="margin-bottom: 16px">
      <div class="content-header">
        <div class="title">时间针提醒列表</div>
      </div>
      <div class="prompt-box">
        <span>时间针提醒列表（根据当前登录医生的治疗组罗列）</span>
        <el-button type="primary" @click="getShiJianZhenTX">刷新</el-button>
        <el-button type="primary" @click="handleDelete">删除提醒</el-button>
      </div>
      <div class="table">
        <el-table
          max-height="305"
          border
          stripe
          :data="patientData1"
          @selection-change="(selection) => handleSelectionChange(selection)"
          style="width: 100%"
        >
          <el-table-column type="index" label="序号" width="60"></el-table-column>
          <el-table-column type="selection" width="60"></el-table-column>
          <el-table-column prop="bingAnHao" width="130" label="病案号"></el-table-column>
          <el-table-column prop="bingRenXM" width="110" label="姓名"></el-table-column>
          <el-table-column prop="bingQuZDYM" width="100" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="100" label="床号"></el-table-column>
          <el-table-column prop="shiJianDian" width="170" label="提醒时间点"></el-table-column>
          <el-table-column prop="kaiShiSJ" width="170" label="计划开始时间"></el-table-column>
          <el-table-column prop="zuHao" width="120" label="组号"></el-table-column>
          <el-table-column prop="mingCheng" width="200" label="药品名称"></el-table-column>
          <el-table-column prop="jiLiang" width="100" label="剂量"></el-table-column>
          <el-table-column prop="yiCiYL" width="100" label="一次用量"></el-table-column>
          <el-table-column prop="jiLiangDW" width="100" label="计量单位"></el-table-column>
          <el-table-column prop="zhiXingPL" width="100" label="执行频率"></el-table-column>
          <el-table-column prop="zhiXingPL2" width="100" label="时间针频率"></el-table-column>
          <el-table-column prop="zhiXingFF" width="100" label="执行方法"></el-table-column>
          <el-table-column prop="geiYaoSJ" width="170" label="给药时间"></el-table-column>
          <el-table-column prop="teShuYF" width="130" label="备注"></el-table-column>
        </el-table>
      </div>
    </div>

    <div class="content">
      <div class="content-header">
        <div class="title">时间针医嘱</div>
      </div>
      <div class="prompt-box">
        <span>时间针医嘱（根据当前登录医生的治疗组罗列）</span>
        <el-button type="primary" @click="getShiJianZhenYz">刷新</el-button>
      </div>
      <div class="table">
        <el-table max-height="305" border stripe :data="patientData2" style="width: 100%">
          <el-table-column type="index" label="序号" width="60"></el-table-column>
          <el-table-column prop="bingAnHao" width="130" label="病案号"></el-table-column>
          <el-table-column prop="bingRenXM" width="110" label="姓名"></el-table-column>
          <el-table-column prop="bingQuZDYM" width="100" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="100" label="床号"></el-table-column>
          <el-table-column prop="kaiShiSJ" width="160" label="计划开始时间"></el-table-column>
          <el-table-column prop="zuHao" width="120" label="组号"></el-table-column>
          <el-table-column prop="mingCheng" width="200" label="药品名称"></el-table-column>
          <el-table-column prop="jiLiang" width="95" label="剂量"></el-table-column>
          <el-table-column prop="yiCiYL" width="95" label="一次用量"></el-table-column>
          <el-table-column prop="jiLiangDW" width="95" label="计量单位"></el-table-column>
          <el-table-column prop="zhiXingPL" width="95" label="执行频率"></el-table-column>
          <el-table-column prop="zhiXingPL2" width="95" label="时间针频率"></el-table-column>
          <el-table-column prop="zhiXingFF" width="95" label="执行方法"></el-table-column>
          <el-table-column prop="geiYaoSJ" width="170" label="给药时间"></el-table-column>
          <el-table-column prop="teShuYF" width="146" label="备注"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getShiJianZhenTX, getShiJianZhenYz, deleteShiJianZhenTx } from '@/api/specialized-patients'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      patientData1: [],
      patientData2: [],
      currUserId: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.getShiJianZhenTX()
    await this.getShiJianZhenYz()
  },
  methods: {
    // 时间针提醒
    async getShiJianZhenTX() {
      try {
        const res = await getShiJianZhenTX()
        if (res.hasError === 0) {
          this.patientData1 = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取时间针医嘱
    async getShiJianZhenYz() {
      try {
        const res = await getShiJianZhenYz()
        if (res.hasError === 0) {
          this.patientData2 = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 勾选要删除的数据
    handleSelectionChange(selection) {
      this.currUserId = selection.map((item) => item.id)
    },

    // 确认删除
    handleDelete() {
      if (this.currUserId.length > 0) {
        this.deleteShiJianZhenTx()
      } else {
        this.$message({
          message: '请选择要删除的数据',
          type: 'error'
        })
      }
    },

    // 删除时间针提醒
    async deleteShiJianZhenTx() {
      try {
        const res = await deleteShiJianZhenTx(this.currUserId)
        if (res.hasError === 0) {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.currUserId = []
          this.getShiJianZhenTX()
        }
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 396px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 20px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 22px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
  .prompt-box {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
  padding-top: 4px;
  padding-bottom: 4px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
  padding-left: 8px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
  padding-left: 8px;
}

::v-deep .el-radio__original {
  display: none !important;
}
</style>
