<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">查询范围:</div>
        <div class="header-item-radio">
          <el-radio-group v-model="leiXing">
            <el-radio label="2" size="large">
              <span class="search-label">本人</span>
            </el-radio>
            <el-radio label="1" size="large">
              <span class="search-label">{{ yongHuZKMC }}</span>
            </el-radio>
            <el-radio label="0" size="large">
              <span class="search-label">全院</span>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">审批结果:</div>
        <div class="header-item-radio">
          <el-radio-group v-model="zhuangTaiBZ">
            <el-radio label="1" size="large">
              <span class="search-label">未同意</span>
            </el-radio>
            <el-radio label="2" size="large">
              <span class="search-label">已同意</span>
            </el-radio>
            <el-radio label="3" size="large">
              <span class="search-label">修回</span>
            </el-radio>
            <el-radio label="9" size="large">
              <span class="search-label">已拒绝</span>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">申请时间:</div>
        <div class="header-item-date">
          <el-date-picker
            v-model="shenQingRQ"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="default"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-button"><el-button @click="queryData">查询</el-button></div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">三联抗菌药物会诊单审批窗口</div>
      </div>
      <div class="table">
        <el-table max-height="648" stripe border :data="tableData">
          <el-table-column width="110" label="急">
            <template #default="{ row }">
              <i v-if="row.shiFouJZ === '1'" class="el-icon-check"></i>
            </template>
          </el-table-column>
          <el-table-column prop="bingAnHao" width="110" label="病案号"></el-table-column>
          <el-table-column prop="bingRenXM" width="110" label="姓名"></el-table-column>
          <el-table-column prop="xingBieMC" width="110" label="性别"></el-table-column>
          <el-table-column prop="zhuanKeMC" width="130" label="专科"></el-table-column>
          <el-table-column prop="bingQuMC" width="130" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="110" label="床位号"></el-table-column>
          <el-table-column prop="shenQingSJ" width="170" label="申请时间"></el-table-column>
          <el-table-column prop="shenQingYHXM" width="110" label="申请医生"></el-table-column>
          <el-table-column prop="qiWangHZYSXM" width="110" label="申请会诊医生"></el-table-column>
          <el-table-column prop="huiZhenSJ" width="170" label="会诊时间"></el-table-column>
          <el-table-column prop="huiZhenYSXM" width="110" label="会诊医生"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template #default="{ row }">
              <el-button type="text" size="small">查看病人</el-button>
              <el-button type="text" size="small" @click="openDuoXueKeHZD(row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog width="70%" class="duoXueKeDialog" :visible.sync="duoXueKeDialog">
      <div slot="title" class="title">温州医科大学附属第一医院三联抗菌药物会诊单</div>
      <div style="border: 1px solid #dcdfe6; padding: 0px 15px 15px">
        <triple-antibacterial-drug-consultation
          ref="TripleAntibacterialDrugConsultation"
          style="height: 600px"
          :extra-data="extraData"
          @save="saveMultiConsultation"
          @close="closeMultiConsultation"
        ></triple-antibacterial-drug-consultation>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getWeiShenPiSLKJYWHZDList } from '@/api/specialist-menu'
import TripleAntibacterialDrugConsultation from '@/views/patient-inside/consultation-form-manage/components/triple-antibacterial-drug-consultation'
import { mapState } from 'vuex'
export default {
  components: {
    TripleAntibacterialDrugConsultation
  },
  data() {
    return {
      extraData: {},
      duoXueKeDialog: false,
      leiXing: '',
      zhuangTaiBZ: '',
      shenQingRQ: '',
      tableData: []
    }
  },
  computed: {
    ...mapState({
      yongHuZKMC: ({ patient }) => patient.initInfo.yongHuZKMC,
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  methods: {
    openDuoXueKeHZD(row) {
      console.log(row)
      this.extraData = { bingLiID: row.bingLiID, huiZhenDanID: row.huiZhenDanID }
      this.duoXueKeDialog = true
      this.$nextTick(() => {
        this.$refs.TripleAntibacterialDrugConsultation.loadData()
      })
    },
    async saveMultiConsultation() {
      this.duoXueKeDialog = false
      await this.queryData()
    },
    closeMultiConsultation() {
      this.duoXueKeDialog = false
    },
    async queryData() {
      let res = await getWeiShenPiSLKJYWHZDList({
        leiXing: this.leiXing,
        zhuanKeID: this.zhuanKeID,
        jieShuSJ: this.shenQingRQ[1],
        kaiShiSJ: this.shenQingRQ[0],
        zhuangTaiBZ: this.zhuangTaiBZ
      })
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
  .title {
    position: relative;
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 9px;
  }
  .title::before {
    position: absolute;
    left: -9px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }
}

.duoXueKeDialog {
  .title {
    position: relative;
    color: #171c28;
    font-size: 16px;
    line-height: 14px;
    margin-left: 9px;
  }
  .title::before {
    content: url('~@/assets/images/info.png');
    position: absolute;
    top: -12px;
    left: -29px;
    transform: scale(0.55);
  }
}
</style>
