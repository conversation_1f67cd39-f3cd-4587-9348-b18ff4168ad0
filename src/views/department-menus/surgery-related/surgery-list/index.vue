<template>
  <!-- 手术一览表 -->
  <div class="container">
    <!-- 查询条件区域 -->
    <div class="filter-container">
      <div class="filter-col">
        <!-- 手术日期 -->
        <div class="filter-item">
          <span class="filter-label">手术日期：</span>
          <el-date-picker
            v-model="searchForm.shouShuRQ"
            type="date"
            placeholder="选择手术日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            size="mini"
            style="width: 100%"
          />
        </div>

        <!-- 当前专科（只读显示） -->
        <div class="filter-item">
          <span class="filter-label">当前专科：</span>
          <el-select :value="Number(zhuanKeID)" size="mini" style="width: 150px" clearable disabled>
            <el-option
              v-for="item in zhuanKeList"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="filter-col">
        <div class="filter-item">
          <el-button
            type="primary"
            size="mini"
            class="purple-button"
            :loading="loading"
            @click="handleSearch"
          >
            查询
          </el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-container">
      <div class="title">
        <span>手术一览表</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        size="mini"
      >
        <el-table-column prop="shouShuJianMC" label="手术间" min-width="80" />
        <el-table-column prop="bingRenXM" label="姓名" min-width="100" />
        <el-table-column prop="nianLing" label="年龄" min-width="60"></el-table-column>
        <el-table-column prop="bingRenXBMC" label="性别" min-width="60" />
        <el-table-column prop="bingQuMC" label="病区" min-width="100" />
        <el-table-column prop="chuangWeiHao" label="床位" min-width="60" />
        <el-table-column label="拟施手术" min-width="150">
          <template #default="{ row }">
            <span>{{ formatShouShuXM(row.shouShuXM) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="zhuDaoYSXM" label="主刀" min-width="80" />
        <el-table-column prop="diYiZSXM" label="一助" min-width="80" />
        <el-table-column prop="diErZSXM" label="二助" min-width="80" />
        <el-table-column prop="maZuiYS1XM" label="主麻" min-width="80" />
        <el-table-column prop="maZuiYS2XM" label="二麻" min-width="80" />
        <el-table-column prop="maZuiYS3XM" label="三麻" min-width="80" />
        <el-table-column prop="xunHuiHS1XM" label="巡回" min-width="80" />
        <el-table-column prop="qiXieHS1XM" label="器械" min-width="80" />
        <el-table-column prop="yuJiSSDDSJ" label="预计手术等待时间" min-width="120" />
        <el-table-column prop="yuJiSSSC" label="预计时长" min-width="80" />
        <el-table-column prop="zhuangTaiBZMC" label="状态" min-width="80" />
      </el-table>
    </div>
  </div>
</template>

<script>
import { getShouShuYLB } from '@/api/surgery-related'
import { mapState } from 'vuex'
import { format } from 'date-fns'

export default {
  name: 'SurgeryList',
  data() {
    return {
      loading: false,
      // 查询条件
      searchForm: {
        shouShuRQ: format(new Date(), 'yyyy-MM-dd') // 默认当天
      },
      // 表格数据
      tableData: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      zhuanKeList: ({ patient }) => patient.zhuanKeList
    })
  },
  async mounted() {
    await this.initData()
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        // 初始化完成后，加载一次数据
        await this.getSurgeryList()
      } catch (error) {
        this.$message.error('初始化数据失败')
      }
    },
    // 获取手术一览表数据
    async getSurgeryList() {
      this.loading = true
      try {
        // 接口参数：手术日期和当前登录医生的专科ID
        const params = {
          shouShuRQ: this.searchForm.shouShuRQ,
          zhuanKeID: this.zhuanKeID
        }

        const res = await getShouShuYLB(params)
        if (res.hasError === 0) {
          this.tableData = res.data || []
        }
      } catch (error) {
        this.$message.error('获取数据失败')
        this.tableData = []
        console.error('获取手术一览表失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 格式化手术名称数组
    formatShouShuXM(shouShuXMArray) {
      if (!shouShuXMArray || !Array.isArray(shouShuXMArray)) {
        return '-'
      }
      // 提取手术名称并用逗号连接
      return shouShuXMArray.map((item) => item.shouShuXM || item.mingCheng || item).join('、')
    },
    // 查询按钮点击事件
    handleSearch() {
      this.getSurgeryList()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.filter-col {
  display: flex;

  .filter-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
}

.filter-label {
  font-weight: bold;
  white-space: nowrap;
  text-align: right;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 62px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
