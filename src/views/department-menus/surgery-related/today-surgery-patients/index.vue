<template>
  <!-- 今日手术病人一览表 -->
  <div class="container">
    <!-- 查询条件区域 -->
    <div class="filter-container">
      <div class="filter-col">
        <!-- 手术日期 -->
        <div class="filter-item">
          <span class="filter-label">手术日期：</span>
          <el-date-picker
            v-model="searchForm.shouShuRQ"
            type="date"
            placeholder="选择手术日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            size="mini"
            style="width: 100%"
          />
        </div>

        <!-- 病案号 -->
        <div class="filter-item">
          <span class="filter-label">病案号：</span>
          <el-input
            v-model="searchForm.bingAnHao"
            placeholder="请输入病案号"
            size="mini"
            style="width: 150px"
            clearable
          />
        </div>

        <!-- 手术间 -->
        <div class="filter-item">
          <span class="filter-label">手术间：</span>
          <el-select
            v-model="searchForm.shouShuJian"
            placeholder="请选择手术间"
            size="mini"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="item in [{ shouShuJian: '', shouShuJianMC: '全部手术间' }, ...shouShuJianList]"
              :key="item.shouShuJian"
              :label="item.shouShuJianMC"
              :value="item.shouShuJian"
            />
          </el-select>
        </div>
      </div>

      <div class="filter-col">
        <!-- 开单病区 -->
        <div class="filter-item">
          <span class="filter-label">开单病区：</span>
          <el-select
            v-model="searchForm.kaiDanBQID"
            placeholder="请选择开单病区"
            size="mini"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="item in [{ daiMa: '', mingCheng: '所有病区' }, ...bingQuList]"
              :key="item.daiMa"
              :label="item.mingCheng"
              :value="item.daiMa"
            />
          </el-select>
        </div>

        <!-- 当前病区 -->
        <div class="filter-item">
          <span class="filter-label">当前病区：</span>
          <el-select
            v-model="searchForm.bingQuID"
            placeholder="请选择当前病区"
            size="mini"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="item in [{ daiMa: '', mingCheng: '所有病区' }, ...bingQuList]"
              :key="item.daiMa"
              :label="item.mingCheng"
              :value="item.daiMa"
            />
          </el-select>
        </div>

        <!-- 专科 -->
        <div class="filter-item">
          <span class="filter-label">专科：</span>
          <el-select
            v-model="searchForm.zhuanKeID"
            placeholder="请选择专科"
            size="mini"
            style="width: 150px"
            clearable
          >
            <el-option
              v-for="item in [{ buMenID: '', buMenMC: '所有专科' }, ...zhuanKeList]"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </div>
      </div>

      <div class="filter-col">
        <div class="filter-item">
          <el-button
            type="primary"
            size="mini"
            class="purple-button"
            :loading="loading"
            @click="handleSearch"
          >
            查询
          </el-button>
          <el-button size="mini" @click="handleReset">重置</el-button>
        </div>
      </div>
    </div>

    <!-- 数据表格区域 -->
    <div class="table-container">
      <div class="title">
        <span>今日手术病人一览表</span>
        <span class="total-count">共{{ pagination.total }}条数据</span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 80px)"
        size="mini"
      >
        <el-table-column prop="BQMC" label="开单病区" min-width="100" />
        <el-table-column prop="XBQMC" label="当前病区" min-width="100" />
        <el-table-column prop="ZKMC" label="专科" min-width="100" />
        <el-table-column prop="EMPI" label="病案号" min-width="100" />
        <el-table-column prop="CWH" label="床位号" min-width="80" />
        <el-table-column prop="BRXM" label="姓名" min-width="100" />
        <el-table-column prop="BRXB" label="性别" min-width="60" />
        <el-table-column prop="CSRQ" label="出生日期" min-width="120" />
        <el-table-column prop="SSJ" label="手术间" min-width="100" />
        <el-table-column prop="NSSSJ" label="拟施手术时间" min-width="150" />
      </el-table>

      <!-- 分页区域 -->
      <div class="pagination-container">
        <el-pagination
          :current-page="pagination.page"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size="pagination.rows"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          size="mini"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getTodaySurgeryPatientList, getAllShouShuJianList } from '@/api/surgery-related'
import { getBingQuList } from '@/api/medical-quality'
import { mapState } from 'vuex'
import { format } from 'date-fns'

export default {
  name: 'TodaySurgeryPatients',
  computed: {
    ...mapState({
      zhuanKeList: ({ patient }) => patient.zhuanKeList
    })
  },
  data() {
    return {
      loading: false,
      // 查询条件
      searchForm: {
        shouShuRQ: format(new Date(), 'yyyy-MM-dd'), // 默认当天
        bingAnHao: '',
        shouShuJian: '',
        kaiDanBQID: '',
        bingQuID: '',
        zhuanKeID: ''
      },
      // 下拉选项数据
      shouShuJianList: [],
      bingQuList: [],
      // 表格数据
      tableData: [],
      // 分页信息
      pagination: {
        page: 1,
        rows: 10,
        total: 0
      }
    }
  },
  async mounted() {
    await this.initData()
    // 初始化完成后，可以加载一次数据
    await this.getTodaySurgeryPatientList()
  },
  methods: {
    // 初始化数据
    async initData() {
      try {
        await Promise.all([this.getShouShuJianList(), this.getBingQuList()])
      } catch (error) {
        this.$message.error('初始化数据失败')
      }
    },
    // 获取手术间列表
    async getShouShuJianList() {
      try {
        const res = await getAllShouShuJianList()
        if (res.hasError === 0) {
          this.shouShuJianList = res.data || []
        }
      } catch (error) {
        console.error('获取手术间列表失败:', error)
      }
    },
    // 获取病区列表
    async getBingQuList() {
      try {
        const res = await getBingQuList()
        if (res.hasError === 0) {
          this.bingQuList = res.data || []
        }
      } catch (error) {
        console.error('获取病区列表失败:', error)
      }
    },
    // 获取今日手术病人列表
    async getTodaySurgeryPatientList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.page,
          rows: this.pagination.rows,
          ...this.searchForm
        }

        const res = await getTodaySurgeryPatientList(params)
        if (res.hasError === 0) {
          this.tableData = res.data || []
          this.pagination.total = res.extendData?.total || 0
        }
      } catch (error) {
        this.$message.error('获取数据失败')
        this.tableData = []
        this.pagination.total = 0
        console.error('获取今日手术病人列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    // 查询按钮点击事件
    handleSearch() {
      this.pagination.page = 1 // 重置到第一页
      this.getTodaySurgeryPatientList()
    },
    // 重置按钮点击事件
    handleReset() {
      this.searchForm = {
        shouShuRQ: format(new Date(), 'yyyy-MM-dd'),
        bingAnHao: '',
        shouShuJian: '',
        kaiDanBQID: '',
        bingQuID: '',
        zhuanKeID: ''
      }
      this.pagination.page = 1
      this.getTodaySurgeryPatientList()
    },
    // 每页显示条数改变
    handleSizeChange(val) {
      this.pagination.rows = val
      this.pagination.page = 1 // 重置到第一页
      this.getTodaySurgeryPatientList()
    },
    // 当前页改变
    handleCurrentChange(val) {
      this.pagination.page = val
      this.getTodaySurgeryPatientList()
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.filter-col {
  display: flex;

  .filter-item {
    display: flex;
    align-items: center;
    margin-right: 10px;
  }
}

.filter-label {
  font-weight: bold;
  white-space: nowrap;
  text-align: right;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 62px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .total-count {
    margin-left: auto;
    margin-right: auto;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    padding: 10px 0 0 0;
  }
}
</style>
