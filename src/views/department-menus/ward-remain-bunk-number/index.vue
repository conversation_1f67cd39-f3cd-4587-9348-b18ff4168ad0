<template>
  <div class="container">
    <div class="header">
      <div class="query-word">病区:</div>
      <el-select v-model="bingQuID" filterable @change="handleBingQuChange">
        <el-option
          v-for="item in bingQuList"
          :key="item.daiMa"
          :label="item.mingCheng"
          :value="item.daiMa"
        ></el-option>
      </el-select>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">{{ mingCheng }}剩余床位</div>
      </div>
      <div class="table">
        <el-table max-height="688" border stripe :data="patientData" style="width: 576px">
          <el-table-column prop="bingQu" width="220" label="病区-床号">
            <template #default="{ row }">{{ row.bingQu }}-{{ row.chuangWeiHao }}</template>
          </el-table-column>
          <el-table-column prop="xingBie" width="120" label="性别">
            <template #default="{ row }">
              <span>{{ row.xingBie == '1' ? '男' : '女' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="chuangWeiLX" width="230" label="床位类型"></el-table-column>
          <!-- <el-table-column prop="caoZuo" width="114" align="center" label="操作">
            <template #default="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getBqSycw, getBingQuZDYM, getCwlxData, getGuanLianBQ } from '@/api/specialized-patients'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      bingQuID: '', //病区ID
      mingCheng: '', //病区名称
      bingQuList: [], //病区列表
      patientData: []
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      yongHuZKMC: ({ patient }) => patient.initInfo.yongHuZKMC
    })
  },
  async mounted() {
    await this.getGuanLianBQ()
    await this.getBqSycw()
  },
  methods: {
    // 获取关联病区接口
    async getGuanLianBQ() {
      try {
        const res = await getGuanLianBQ({
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.bingQuList = res.data
          this.bingQuID = res.data[0].daiMa
          this.mingCheng = res.data[0].mingCheng
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取列表初始化接口
    async getBqSycw() {
      try {
        const res = await getBqSycw({
          bingQuID: this.bingQuID
        })
        if (res.hasError === 0) {
          this.patientData = res.data
          await this.getBingQuZDYM()
          await this.getCwlxData()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取病区列表接口
    async getBingQuZDYM() {
      try {
        const res = await getBingQuZDYM()
        if (res.hasError === 0) {
          this.patientData.forEach((item) => {
            item.bingQu = res.data.find((itm) => itm.daiMa == item.bingQuID).mingCheng
          })
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取床位列表接口
    async getCwlxData() {
      try {
        const res = await getCwlxData()
        if (res.hasError === 0) {
          this.patientData.forEach((item) => {
            item.chuangWeiLX = res.data.find((itm) => itm.daiMa == item.chuangWeiLX).mingCheng
          })
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 切换病区
    handleBingQuChange(daiMa) {
      const selectedItem = this.bingQuList.find((item) => item.daiMa === daiMa)
      if (selectedItem) {
        this.mingCheng = selectedItem.mingCheng
        this.bingQuID = selectedItem.daiMa
        this.getBqSycw()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

// ::v-deep .el-table__cell:last-child {
//   padding-left: 0px;
// }

// ::v-deep .el-table th.el-table__cell,
// .el-table td.el-table__cell {
//   padding-top: 9px;
//   padding-bottom: 9px;
// }

// ::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
//   padding-top: 4px;
//   padding-bottom: 4px;
// }

// ::v-deep .el-radio__original {
//   display: none !important;
// }
</style>
