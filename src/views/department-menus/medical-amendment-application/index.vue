<template>
  <div class="container">
    <div class="header">
      <div class="header-search-top">
        <div class="query-word">状态：</div>
        <el-radio-group v-model="radioValue" style="margin-right: 10px">
          <el-radio :label="0">全部</el-radio>
          <el-radio :label="1">等待完成</el-radio>
          <el-radio :label="2">完成修正</el-radio>
        </el-radio-group>
        <div class="query-word">选择日期：</div>
        <el-date-picker
          v-model="zhuanKeSJ"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="changeDay"
        ></el-date-picker>
        <div class="query-word">选择类型:</div>
        <el-select v-model="selectID" filterable>
          <el-option
            v-for="item in ZhiLiaoZuList"
            :key="item.buMenID"
            :label="item.buMenMC"
            :value="item.buMenID"
          ></el-option>
        </el-select>
        <el-checkbox v-model="yuWoYG" style="margin: 0 10px 0 20px">与本人相关</el-checkbox>
        <div class="button">
          <el-button type="primary" class="search-button" @click="searchBlxztzdByCondition">
            查询
          </el-button>
          <el-button type="success" @click="exportToExcel">导出到Excel</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">转/跨科病历修正申请单管理</div>
      </div>
      <div class="table">
        <el-table max-height="688" border stripe :data="patientData" style="width: 100%">
          <el-table-column prop="bingRenXM" width="112" label="">
            <template #default="scope">
              <el-button type="text" size="medium" @click="selectClick(scope.row)">选择</el-button>
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="xingMing" width="90" label="姓名"></el-table-column>
          <el-table-column prop="bingAnHao" width="60" label="性别">
            <template #default="{ row }">
              <span>{{ row.xingBie == 1 ? '男' : '女' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="bingQuMC" width="170" label="病区-床位">
            <template #default="{ row }">
              <span>{{ row.bingQuMC }}-{{ row.chuangWeiHao }}床</span>
            </template>
          </el-table-column>
          <el-table-column prop="bingRenBH" width="230" label="门诊号"></el-table-column>
          <el-table-column prop="shenQingSJ" width="150" label="申请时间"></el-table-column>
          <el-table-column prop="shenQingZKMC" width="160" label="申请专科"></el-table-column>
          <el-table-column prop="shenQingYHXM" width="90" label="申请医师"></el-table-column>
          <el-table-column
            prop="shenQingYHLXDH"
            width="90"
            label="申请医师联系电话"
          ></el-table-column>
          <el-table-column prop="tongZhiZKMC" width="170" label="通知科室"></el-table-column>
          <el-table-column prop="tongZhiYHXM" width="90" label="通知人员"></el-table-column>
          <el-table-column prop="tongZhiNR" width="204" label="通知项目"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" width="100" label="状态">
            <template #default="{ row }">
              <span>{{ row.zhuangTaiBZ == 1 ? '等待处理' : '完成' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="caoZuo" width="150" align="center" label="操作">
            <template #default="{ row }">
              <el-button
                size="medium"
                type="primary"
                style="padding: 8px"
                v-if="row.tongZhiYHID == yongHuID && row.zhuangTaiBZ == 1"
                @click="correctConfirm(row, '2')"
              >
                修正完成
              </el-button>
              <el-button
                size="medium"
                type="primary"
                style="padding: 8px"
                v-if="row.tongZhiYHID == yongHuID && row.zhuangTaiBZ == 1"
                @click="correctConfirm(row, '0')"
              >
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <el-dialog
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="60%"
      :before-close="handleClose"
      class="dialog-class"
    >
      <div slot="title" class="dialog-title">转/跨科病历修正申请单</div>
      <div class="dialog-content">
        <div class="dialog-content-title">转/跨科病历修正申请单</div>
        <div class="dialog-box">
          <div class="right-header">
            <div class="title">病人基本信息</div>
          </div>
          <table class="right-table">
            <tr>
              <td class="right-table-title">姓名:</td>
              <td class="right-table-content">
                {{ selectRow.xingMing }}
              </td>
              <td class="right-table-title">性别:</td>
              <td class="right-table-content">
                {{ selectRow.zhuangTaiBZ == 1 ? '男' : '女' }}
              </td>
              <td class="right-table-title">出生日期:</td>
              <td class="right-table-content">
                {{ selectRow.chuShengRQ }}
              </td>
            </tr>
            <tr>
              <td class="right-table-title">转科时间:</td>
              <td class="right-table-content">{{ selectRow.ZKSJ }}</td>
              <td class="right-table-title">病区:</td>
              <td class="right-table-content">{{ selectRow.bingQuMC }}</td>
              <td class="right-table-title">床位:</td>
              <td class="right-table-content">
                {{ selectRow.bingQuID }}
              </td>
            </tr>
            <tr>
              <td class="right-table-title">病案号:</td>
              <td class="right-table-content">{{ selectRow.bingAnHao }}</td>
            </tr>
          </table>
          <div class="right-header">
            <div class="title">申请人信息</div>
          </div>
          <table class="right-table">
            <tr>
              <td class="right-table-title">申请医师:</td>
              <td class="right-table-content">{{ selectRow.shenQingYHXM }}</td>
              <td class="right-table-title">申请专科:</td>
              <td class="right-table-content">{{ selectRow.shenQingZKMC }}</td>
              <td class="right-table-title">联系电话:</td>
              <td class="right-table-content">{{ selectRow.shenQingYHLXDH }}</td>
            </tr>
          </table>
          <div class="right-header">
            <div class="title">申请信息</div>
          </div>
          <table class="right-table">
            <tr>
              <td class="right-table-title">通知科室:</td>
              <td class="right-table-content">{{ selectRow.tongZhiZKMC }}</td>
              <td class="right-table-title">通知人员:</td>
              <td class="right-table-content">{{ selectRow.tongZhiYHXM }}</td>
              <td class="right-table-title">申请时间:</td>
              <td class="right-table-content">
                {{ selectRow.shenQingSJ }}
              </td>
            </tr>
            <tr>
              <td class="right-table-title">通知内容：</td>
              <td class="right-table-content" colspan="5">
                <el-input
                  v-model="selectRow.tongZhiNR"
                  class="dialog-textarea"
                  size="mini"
                  clearable
                  maxlength="1000"
                  type="textarea"
                  show-word-limit
                  :autosize="{ minRows: 5, maxRows: 5 }"
                ></el-input>
              </td>
            </tr>
          </table>
          <div class="right-header">
            <div class="title">接收信息</div>
          </div>
          <table class="right-table">
            <tr>
              <td class="right-table-title">接收科室:</td>
              <td class="right-table-content">{{ selectRow.tongZhiZKMC }}</td>
              <td class="right-table-title">接收医师:</td>
              <td class="right-table-content">{{ selectRow.tongZhiYHXM }}</td>
              <td class="right-table-title">反馈时间:</td>
              <td class="right-table-content">
                <el-date-picker
                  v-model="selectRow.fanKuiSJ"
                  type="datetime"
                  placeholder="选择日期时间"
                ></el-date-picker>
              </td>
            </tr>
            <tr>
              <td class="right-table-title">接收医生反馈意见：</td>
              <td class="right-table-content" colspan="5">
                <el-input
                  v-model="selectRow.fanKuiYJ"
                  class="dialog-textarea"
                  size="mini"
                  clearable
                  maxlength="1000"
                  type="textarea"
                  show-word-limit
                  :autosize="{ minRows: 5, maxRows: 5 }"
                ></el-input>
              </td>
            </tr>
          </table>
        </div>
      </div>
      <div class="bottom-btn-group">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" v-if="fanKuiSJShow == true" @click="updateSingleBlxzsqd">
          保存
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBrZksjByBlid,
  searchBlxztzdByCondition,
  updateBlxztzdStatus,
  updateSingleBlxzsqd
} from '@/api/specialized-patients'
import { format, subDays } from 'date-fns'
import { mapState } from 'vuex'
import * as XLSX from 'xlsx'
export default {
  data() {
    return {
      radioValue: 0, //选择状态值
      zhuanKeSJ: [format(subDays(new Date(), 13), 'yyyy-MM-dd'), format(new Date(), 'yyyy-MM-dd')], // 默认值
      kaiShiSJ: format(subDays(new Date(), 13), 'yyyy-MM-dd'), //开始时间
      jieShuSJ: format(new Date(), 'yyyy-MM-dd'), //结束时间
      selectID: 0, //选择类型值
      ZhiLiaoZuList: [
        { buMenID: 0, buMenMC: '所有' },
        { buMenID: 1, buMenMC: '申请' },
        { buMenID: 2, buMenMC: '接受' }
      ], //选择类型列表
      yuWoYG: true, //与本人相关
      patientData: [], //病人数据
      selectRow: {}, //选择某个病人数据
      fanKuiSJShow: false, //时间有反馈时间
      dialogVisible: false //反馈弹框判断
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit,
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  async mounted() {
    await this.searchBlxztzdByCondition()
  },
  methods: {
    // 获取列表初始化接口
    async searchBlxztzdByCondition() {
      try {
        const res = await searchBlxztzdByCondition({
          kaiShiSJ: this.kaiShiSJ + ' 00:00:00',
          jieShuSJ: this.jieShuSJ + ' 23:59:59',
          leiXing: this.selectID,
          xiuZhengZT: this.radioValue,
          yuWoYG: this.yuWoYG,
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.patientData = res.data
          this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },

    delArr() {
      this.patientData = this.patientData.map((item) => {
        const formattedItem = { ...item }
        if (item.shenQingSJ) {
          formattedItem.shenQingSJ = format(new Date(item.shenQingSJ), 'yyyy-MM-dd HH:mm')
        }
        if (item.chuShengRQ) {
          formattedItem.chuShengRQ = format(new Date(item.chuShengRQ), 'yyyy-MM-dd')
        }
        if (item.fanKuiSJ == '0001-01-01 00:00:00') {
          item.fanKuiSJ = ''
        }
        return formattedItem
      })
    },

    // 选择日期
    changeDay(e) {
      this.kaiShiSJ = format(new Date(e[0]), 'yyyy-MM-dd')
      this.jieShuSJ = format(new Date(e[1]), 'yyyy-MM-dd')
    },

    // 选择通知单进行回复
    selectClick(row) {
      this.selectRow = row
      if (this.selectRow.fanKuiSJ == '0001-01-01 00:00:00') {
        this.fanKuiSJShow = true
        this.selectRow.fanKuiSJ = new Date()
      } else {
        this.fanKuiSJShow = false
      }
      this.dialogVisible = true
      this.getBrZksjByBlid(row)
    },

    // 根据病例ID获取最新转床时间
    async getBrZksjByBlid(row) {
      try {
        const res = await getBrZksjByBlid({
          bingLiID: row.bingLiID
        })
        if (res.hasError === 0) {
          this.selectRow.ZKSJ = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 回复新增接口
    async updateSingleBlxzsqd() {
      try {
        const {
          shouShuTZDID,
          bingLiID,
          xingMing,
          xingBie,
          chuShengRQ,
          zhuYuanHao,
          bingQuID,
          chuangWeiHao,
          shenQingSJ,
          shenQingZKID,
          shenQingZKMC,
          shenQingYHID,
          shenQingYHXM,
          tongZhiNR,
          tongZhiZK,
          tongZhiZKMC,
          tongZhiYHID,
          zhiKongSJ,
          beiZhu,
          shenQingYHLXDH,
          tongZhiYHLXDH,
          jiLuSJ,
          xiuGaiSJ,
          bingRenBH,
          caoZuoZheID,
          zhuangTaiBZ,
          bingAnHao
        } = this.selectRow
        const res = await updateSingleBlxzsqd({
          ...this.selectRow,
          // ZKSJ: this.selectRow.ZKSJ,
          chuShengRQ: this.selectRow.chuShengRQ + ' 00:00:00',
          shenQingSJ: format(new Date(this.selectRow.shenQingSJ), 'yyyy-MM-dd HH:mm:ss'),
          fanKuiSJ: format(new Date(this.selectRow.fanKuiSJ), 'yyyy-MM-dd HH:mm:ss'),
          fanKuiYJ: this.selectRow.fanKuiYJ
        })

        if (res.hasError === 0) {
          this.dialogVisible = false
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          this.searchBlxztzdByCondition()
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 查看病人详情
    handleClick(row) {
      const formatChuangWeiHao = (bingQuMC, chuangWeiHao) => {
        return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
      }
      this.$router.push({
        path: `/patient-detail/${row.bingLiID}`,
        query: {
          title: `${row.jieSuanLXMC || ''} ${formatChuangWeiHao(row.bingQuMC, row.chuangWeiHao)} ${
            row.xingMing
          }`
        }
      })
    },

    // 进行修正操作
    correctConfirm(row, type) {
      this.$confirm(type == '2' ? '是否确认完成修正？' : '是否确定删除该记录？', '提示信息', {
        type: 'info'
      }).then(() => {
        this.updateBlxztzdStatus(row, type)
      })
    },

    // 修正接口
    async updateBlxztzdStatus(row, type) {
      try {
        const res = await updateBlxztzdStatus({
          shouShuTZDID: row.shouShuTZDID,
          zhuangTaiBZ: type
        })
        if (res.hasError === 0) {
          this.$message({
            message: type == '2' ? '修正成功' : '删除成功',
            type: 'success'
          })
          this.searchBlxztzdByCondition()
        }
      } catch (error) {
        console.log(error)
      }
    },

    handleClose() {
      this.dialogVisible = false
    },

    exportToExcel() {
      if (this.patientData.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const worksheet = XLSX.utils.json_to_sheet(
        this.patientData.map((data) => {
          let d = {}
          let columns = [
            { value: 'xingMing', label: '姓名' },
            { value: 'xingBie', label: '性别' },
            { value: 'bingQuMC', label: '病区' },
            { value: 'chuangWeiHao', label: '床位' },
            { value: 'bingRenBH', label: '门诊号' },
            { value: 'shenQingSJ', label: '申请时间' },
            { value: 'shenQingZKMC', label: '申请专科' },
            { value: 'shenQingYHXM', label: '申请医师' },
            { value: 'shenQingYHLXDH', label: '申请医师联系电话' },
            { value: 'tongZhiZKMC', label: '通知科室' },
            { value: 'tongZhiYHXM', label: '通知人员' },
            { value: 'tongZhiNR', label: '通知项目' },
            { value: 'zhuangTaiBZ', label: '状态' }
          ]
          columns.forEach((col) => {
            if (col.value === 'xingBie') {
              d[col.label] = data[col.value] === '1' ? '男' : '女'
            } else if (col.value === 'zhuangTaiBZ') {
              d[col.label] = data[col.value] === '1' ? '等待处理' : '完成'
            } else {
              d[col.label] = data[col.value]
            }
          })
          return d
        })
      )
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '本治疗组在院病人列表.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .header-search-top {
    display: flex;
    align-items: center;
  }
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  .search-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding: 8px 20px;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

::v-deep .el-table__cell:last-child {
  padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}

// ------------------------------
::v-deep .el-dialog__body {
  padding: 10px;
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 30px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
::v-deep .el-dialog__footer {
  margin-top: 10px;
  border: none;
}

.dialog-class {
  .dialog-title {
    margin-left: 24px;
    font-weight: bold;
  }
  .dialog-content {
    border: 1px solid #dcdfe6;
    padding: 15px;
    background-color: #fafbfc;
    .dialog-content-title {
      text-align: center;
      font-size: 20px;
      font-weight: bold;
      margin-bottom: 15px;
    }
    .dialog-box {
      padding: 4px 14px;
      border: 1px solid #dde0e7;

      .right-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin: 12px 0;
        // height: 5%;
        .title {
          font-weight: 600;
          border-left: 3px solid #356ac5;
          padding-left: 6px;
          line-height: 15px;
          text-align: left;
        }
      }
    }
  }
  ::v-deep .el-dialog {
    padding: 6px 10px;
  }

  ::v-deep .el-table--mini .el-table__cell {
    padding: 12px 0;
  }

  ::v-deep .el-dialog__header {
    // border-bottom: 1px solid #dadee6;
    padding: 10px 14px;
    font: var(--font-medium);
    font-size: var(--font-size-medium);
    color: #171c28;
    display: flex;
    align-items: center;
  }

  ::v-deep .el-dialog__header::before {
    // content: url('~@/assets/images/info.png');
    // // width: 3px;
    // // height: 16px;
    // // background: #356ac5;
    // // margin-right: 6px;
    // position: absolute;
    // top: 7px;
    // left: 6px;
    // transform: scale(0.55);
    position: absolute;
    left: 22px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }

  ::v-deep .el-dialog__title {
    font-weight: bold;
    margin-left: 10px;
    opacity: 0.8;
  }
}

.right-table {
  text-align: left;
  table-layout: fixed;
  // height: 100%;
  width: 100%;
  margin-top: 10px;
  td {
    border: 1px solid #dcdfe6;
    padding: 4px 6px;
    // width: 13%;
    overflow: hidden;
    box-sizing: border-box;
  }
  .right-table-title {
    text-align: right;
    background-color: #eff3fb;
    width: 11%;
    height: 100%;
    line-height: 16px;
    background: #eff3fb;
  }
  .right-table-content {
    background-color: #ffffff;
    // width: 13%;
    height: 40px;
    ::v-deep .el-input__inner {
      width: 100%;
      min-width: 0;
      // background-color: #e4ecfb;
      // height: 24px;
    }
  }
}
</style>
