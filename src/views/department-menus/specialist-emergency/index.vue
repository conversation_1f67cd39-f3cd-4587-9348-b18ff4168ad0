<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">危急值上报时间:</div>
        <el-date-picker
          v-model="chaXunSJ"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd 00:00:00"
        ></el-date-picker>
      </div>
      <div class="header-item">
        <div class="header-item-title">选择治疗组:</div>
        <div>
          <el-select v-model="zhiLiaoZu">
            <el-option
              v-for="item in zhiLiaoZuOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">危机值类型:</div>
        <div>
          <el-select v-model="weiJiZhi">
            <el-option
              v-for="item in weiJiZhiLXOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">检查类型:</div>
        <div>
          <el-select v-model="jianChaLX">
            <el-option
              v-for="item in jianChaLXOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">超时处理/记录:</div>
        <div>
          <el-select v-model="chaoShiCL">
            <el-option
              v-for="item in chaoShiCLOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="header-item">
        <el-button @click="handleQuery">查询</el-button>
        <el-button>导出</el-button>
        <el-button @click="handleWatch">查看危急值自查表</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="flex">
          <div class="title">本科室危急值/提醒值查询</div>
          <div class="title-tip">
            <div class="flex">
              <i class="el-icon-info" style="color: #356ac5"></i>
              <span>处理状态:</span>
            </div>
            <div class="flex" style="margin-left: 10px">
              <div class="box" style="background-color: #ed6a0c"></div>
              <span>超过20分钟未处理</span>
            </div>
            <div class="flex" style="margin-left: 10px">
              <div class="box" style="background-color: #f6d817"></div>
              <span>记录超过6小时</span>
            </div>
            <div class="flex" style="margin-left: 10px">
              <div class="box" style="background-color: #f35656"></div>
              <span>两项都超过</span>
            </div>
          </div>
        </div>
        <div class="button"><el-button type="primary" @click="handleAdd">新增</el-button></div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData">
          <el-table-column align="center" prop="chuLiZT" width="150" label="处理状态">
            <template slot-scope="slot">
              <div
                v-if="slot.row.shiFouCS == 1"
                style="
                  border: 1px solid #ed6a0c;
                  color: #ed6a0c;
                  padding: 2px;
                  text-align: center;
                  width: 80px;
                  margin: 0 auto;
                "
              >
                超时未处理
              </div>
              <div
                v-else-if="slot.row.shiFouCS == 2"
                style="
                  border: 1px solid #edd535;
                  color: #edd535;
                  padding: 2px;
                  text-align: center;
                  width: 70px;
                  margin: 0 auto;
                "
              >
                记录超时
              </div>
              <div
                v-else-if="slot.row.shiFouCS == 3"
                style="
                  border: 1px solid #f5d5d5;
                  color: #f35656;
                  padding: 2px;
                  text-align: center;
                  width: 130px;
                  margin: 0 auto;
                "
              >
                超时(未处理+记录)
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="kaiDanZKMC" width="100" label="开单科室"></el-table-column>
          <el-table-column prop="zhiLiaoZuMC" width="100" label="治疗组"></el-table-column>
          <el-table-column prop="bingRenXM" width="100" label="患者姓名"></el-table-column>
          <el-table-column prop="bingAnHao" width="120" label="病案号"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="100" label="床号"></el-table-column>
          <el-table-column prop="baoGaoBH" width="160" label="检验/检查号"></el-table-column>
          <el-table-column prop="jianChaXM" width="160" label="检验/检查项目"></el-table-column>
          <el-table-column prop="jianChaJG" width="130" label="结果"></el-table-column>
          <el-table-column prop="jianChaSJ" width="150" label="检查时间"></el-table-column>
          <el-table-column prop="baoGaoSJ" width="150" label="报告时间"></el-table-column>
          <el-table-column prop="baoGaoRYXM" width="100" label="报告人"></el-table-column>
          <el-table-column prop="chuZhiYSXM" width="100" label="处理护士"></el-table-column>
          <el-table-column prop="tongZhiSJ" width="150" label="护士通知医生时间"></el-table-column>
          <el-table-column prop="jieShouRen" width="100" label="接收人"></el-table-column>
          <el-table-column fixed="right" label="操作" align="center">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">闭环</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog :visible.sync="dialogVisible" width="35%" :before-close="handleClose">
      <div slot="title" class="dialog-title">
        <i class="el-icon-coin"></i>
        <span style="margin-left: 5px">科室危急值自查表列表</span>
      </div>
      <div class="dialog-content">
        <div class="flex" style="justify-content: space-between; padding: 10px 0px 20px">
          <div class="flex" style="margin-right: 5px">
            <span style="width: 70px">检查日期:</span>
            <el-date-picker
              v-model="selfDatetime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd 00:00:00"
            ></el-date-picker>
          </div>
          <div class="flex">
            <span style="width: 40px; flex-shrink: 0">科室:</span>
            <el-select v-model="selfZhuanKeID">
              <el-option
                v-for="item in selfKeShiOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </div>
          <el-button class="selectBtn" @click="handleSelfQuery">查询</el-button>
        </div>
        <el-table :data="selfTableData">
          <el-table-column width="100" label="操作">
            <template slot-scope="slot">
              <el-button class="self-details-btn" @click="handleSelfDetails(slot.row)">
                查看明细
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="jianChaSJ" label="检查时间"></el-table-column>
          <el-table-column prop="yongHuXM" width="100" label="检查人"></el-table-column>
          <el-table-column prop="zhuanKeMC" width="140" label="专科"></el-table-column>
        </el-table>
        <div class="bottom-btn-group">
          <el-button @click="handleClose">关闭</el-button>
          <el-button type="primary" @click="handleSave">确认</el-button>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="addDialog" width="70%" :before-close="handleAddDialogClose">
      <div slot="title" class="dialog-title">
        <i class="el-icon-coin"></i>
        <span style="margin-left: 5px">科室危急值自查表列表</span>
      </div>
      <div class="add-dialog-content">
        <div class="flex" style="padding: 10px 0px">
          <div class="flex" style="margin-right: 5px">
            <span style="width: 70px">检查日期:</span>
            <el-date-picker
              v-model="datetime"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="yyyy-MM-dd 00:00:00"
            ></el-date-picker>
          </div>
          <el-button class="query-btn" @click="handleAddDialogQuery">查询</el-button>
        </div>
        <div style="border: 1px solid #dcdfe6; padding: 10px">
          <div style="text-align: center">
            <div class="list-title">科室危急值自查分析表</div>
            <div class="list-msg">
              <span>科室: {{ yongHuZKMC }}</span>
              <span>检查人: {{ yongHuXM }}</span>
              <span>检查日期: {{ currentDate }}</span>
            </div>
            <table style="width: 100%">
              <tr>
                <td style="width: 20%; background: #eff3fb">
                  本月危急值总例数: {{ addDialogData.length }}
                </td>
                <td style="width: 80%">
                  <tr style="display: block; padding: 10px">20分钟内处置率: {{ chuZhiLv }}%</tr>
                  <tr style="display: block; padding: 10px">6小时内记录率: {{ jiLuLv }}%</tr>
                </td>
              </tr>
            </table>
          </div>
          <div style="margin-top: 10px">
            <div style="border: 1px solid #dcdfe6; padding: 10px">
              <div class="title">自查明细</div>
            </div>
            <div style="border: 1px solid #dcdfe6; padding: 10px">
              <el-table max-height="300" :data="addDialogData">
                <el-table-column label="报告时间"></el-table-column>
                <el-table-column prop="bingRenXM" label="患者姓名"></el-table-column>
                <el-table-column prop="bingAnHao" label="病案号"></el-table-column>
                <el-table-column prop="jianChaXM" label="检验/检查项目"></el-table-column>
                <el-table-column prop="jianChaJG" label="结果"></el-table-column>
                <el-table-column width="50" prop="shiFouCSCZ" label="是否按时处理">
                  <template slot-scope="slot">
                    <div v-if="slot.row.shiFouCSCZ == 2">&check;</div>
                    <div v-else>&times;</div>
                  </template>
                </el-table-column>
                <el-table-column width="50" label="是否按时记录">
                  <template slot-scope="slot">
                    <div v-if="slot.row.shiFouCSJL == 1">&check;</div>
                    <div v-else>&times;</div>
                  </template>
                </el-table-column>
                <el-table-column prop="xiaoGuoPJ" label="记录中有无体现处理效果"></el-table-column>
                <el-table-column prop="beiZhuSM" label="不合格情况备注说明"></el-table-column>
                <el-table-column prop="jingGuanYS" label="经管医生"></el-table-column>
                <!-- <el-table-column prop="weiJiZJLID" label="非本科室危急值"></el-table-column> -->
                <el-table-column prop="shanChuLY" label="删除理由"></el-table-column>
              </el-table>
            </div>

            <table style="width: 100%">
              <tr>
                <td
                  style="
                    width: 20%;
                    background-color: #eff3fb;
                    text-align: right;
                    padding-right: 10px;
                  "
                >
                  存在问题及原因分析:
                </td>
                <td style="width: 80%; padding: 5px">
                  <el-input v-model="wenTiFX" type="textarea"></el-input>
                </td>
              </tr>
              <tr>
                <td
                  style="
                    width: 20%;
                    background-color: #eff3fb;
                    text-align: right;
                    padding-right: 10px;
                  "
                >
                  整改措施及后续追踪情况:
                </td>
                <td style="width: 80%; padding: 5px">
                  <el-input v-model="zhengGaiCS" type="textarea"></el-input>
                </td>
              </tr>
            </table>
          </div>
        </div>
        <div style="margin-top: 20px">
          <i class="el-icon-info" style="color: #356ac5"></i>
          <span style="margin-left: 1px">
            自查结果提交科室医疗质量与安全小组会议讨论分析，提出整改措施，OA上传。
          </span>
        </div>
        <div style="float: right; padding-bottom: 16px">
          <el-button type="primary">保存</el-button>
          <el-button @click="handleAddDialogClose">关闭</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  criticalValueReportSearch,
  criticalValueSelfInspectionReportInit,
  criticalValueSelfInspectionReportSave,
  criticalValueSelfInspectionReportSearch,
  getCriticalValueSelfInspectionReportByJiLuID,
  insertHuiZhenWSHD,
  getZhiChaKeShiKuang
} from '@/api/specialist-menu'
import { mapState } from 'vuex'
import { round } from 'lodash'
export default {
  data() {
    return {
      zhiLiaoZuOptions: [], // 治疗组选项列表
      zhiLiaoZu: 0, // 治疗组选项值
      weiJiZhiLXOptions: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '危急值',
          value: 1
        },
        {
          label: '提醒值',
          value: 2
        }
      ], // 危急值类型选项列表
      weiJiZhi: 0, //危急值选项值
      jianChaLXOptions: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '检验',
          value: 1
        },
        {
          label: '检查',
          value: 2
        }
      ], // 检查类型选项列表
      jianChaLX: 0, //检查类型选项值
      chaoShiCLOptions: [
        {
          label: '全部',
          value: 0
        },
        {
          label: '超时20分钟处理',
          value: 1
        },
        {
          label: '超过6小时记录',
          value: 2
        }
      ], //超时处理/记录选项列表
      chaoShiCL: 0, //超时处理选项值
      chaXunSJ: '', //查询时间
      tableData: [], //主页危急值列表数据
      addDialogData: [],
      selfTableData: [],
      dialogVisible: false, // 查看危急值自查表弹窗
      addDialog: false, // 新增弹窗
      datetime: '', // 新增弹窗时间查询
      selfDatetime: '', // 危机值自查表弹窗时间查询
      selfKeShiOptions: [], // 危机值自查表科室选择列表
      selfZhuanKeID: 0, // 危机值自查表当前选择值
      wenTiFX: '', //新增弹窗问题分析
      zhengGaiCS: '', //新增弹窗问题措施
      chuZhiLv: 100, //新增弹窗处置率
      jiLuLv: 100 //新增弹窗记录率
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      yongHuZKMC: ({ patient }) => patient.initInfo.yongHuZKMC,
      yongHuXM: ({ patient }) => patient.initInfo.yongHuXM
    }),
    currentDate: () => {
      let currentDate = new Date()
      let year = currentDate.getFullYear() // 获取当前年份
      let month = currentDate.getMonth() + 1 // 获取当前月份，注意月份从0开始，所以要加1
      let day = currentDate.getDate() // 获取当前日期
      return year + '-' + month + '-' + day
    }
  },
  async mounted() {
    console.log(this.currentDate)
    let res1 = await insertHuiZhenWSHD()
    res1.data.ylZlzVos.map((item) => {
      // zhuanKeID为null时为全选，全选需传0
      if (item.zhuanKeID == null) {
        item.zhuanKeID = 0
      }
      this.zhiLiaoZuOptions.push({
        label: item.mingCheng,
        value: item.id,
        zhuanKeID: item.zhuanKeID
      })
    })
    res1.data.cloudBmdmVos.map((item) => {
      this.selfKeShiOptions.push({
        label: item.buMenMC,
        value: item.buMenID
      })
    })
    // let res2 = await getZhiChaKeShiKuang()
    // let res3 = await criticalValueSelfInspectionReportInit()
  },
  methods: {
    handleClick(row) {
      console.log(row)
    },
    handleWatch(row) {
      this.dialogVisible = true
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleAddDialogClose() {
      this.addDialog = false
    },
    handleAdd() {
      this.addDialog = true
      this.wenTiFX = ''
      this.zhengGaiCS = ''
      this.addDialogData = []
      this.datetime = ''
      this.chuZhiLv = 100
      this.jiLuLv = 100
    },
    handleSave() {},
    async handleQuery() {
      let zhuanKeID = 0
      let zhiLiaoZuID = 0
      this.zhiLiaoZuOptions.map((item) => {
        if (item.value == this.zhiLiaoZu) {
          zhuanKeID = item.zhuanKeID
          zhiLiaoZuID = item.value
        }
      })
      let res = await criticalValueReportSearch({
        caoShiLX: this.chaoShiCL,
        jieShuSJ: this.chaXunSJ[1],
        kaiShiSJ: this.chaXunSJ[0],
        weiJiZhiLB: this.weiJiZhi,
        weiJiZhiLX: this.jianChaLX,
        zhiLiaoZuID: zhiLiaoZuID,
        zhuanKeID: zhuanKeID
      })
      this.tableData = res.data.criticalValueVos
    },
    async handleSelfQuery() {
      let res = await criticalValueSelfInspectionReportSearch({
        jieShuSJ: this.selfDatetime[1],
        kaiShiSJ: this.selfDatetime[0],
        zhuanKeID: this.selfZhuanKeID
      })
      // let res2 = await criticalValueSelfInspectionReportInit({
      //   jieShuSJ: this.selfDatetime[1],
      //   kaiShiSJ: this.selfDatetime[0],
      //   zhuanKeID: this.selfZhuanKeID
      // })
      this.selfTableData = res.data
      console.log(this.selfDatetime)
      console.log(this.selfZhuanKeID)
    },
    async handleSelfDetails(row) {
      console.log(row)
    },
    async handleAddDialogQuery() {
      let res = await criticalValueSelfInspectionReportInit({
        jieShuSJ: this.datetime[1],
        kaiShiSJ: this.datetime[0],
        zhuanKeID: this.zhuanKeID
      })
      this.addDialogData = res.data.criticalValueVos
      let ASCZ = 0
      let ASJL = 0
      this.addDialogData.map((item) => {
        if (item.shiFouCSCZ == '2') {
          ASCZ++
        }
        if (item.shiFouCSJL == '1') {
          ASJL++
        }
      })
      this.chuZhiLv = Math.round((ASCZ / this.addDialogData.length) * 100)
      this.jiLuLv = Math.round((ASJL / this.addDialogData.length) * 100)
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0px 20px 0px;
}
table {
  tr,
  td {
    border: 1px solid #ddd;
    border-collapse: collapse;
  }
}
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 6px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .header-item-title {
    color: #171c28;
    flex-shrink: 0;
    font-size: 14px;
    margin-right: 8px;
  }
  ::v-deep .el-date-editor {
    width: 360px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
  height: 720px;
  .table {
    max-height: 650px;
  }
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}

.title-tip {
  border: 1px solid rgba($color: #155bd4, $alpha: 0.45);
  padding: 6px 10px;
  margin-left: 10px;
  display: flex;
  align-items: center;
}

.flex {
  display: flex;
  align-items: center;
}
.box {
  width: 10px;
  height: 10px;
  margin-right: 5px;
}
.bottom-btn-group {
  display: flex;
  flex-direction: row-reverse;
  margin: 30px 0 10px;
  ::v-deep .el-button {
    margin-left: 10px;
  }
}
.query-btn {
  background-color: #a66dd4;
  color: #fff;
}
.add-dialog-content {
  .list-title {
    font-weight: 600;
    font-size: 18px;
  }
  .list-msg {
    padding: 5px 0;
    margin-left: 24px;
    span {
      margin: 0 8px;
    }
  }
}
::v-deep .self-details-btn {
  border: none;
}
</style>
