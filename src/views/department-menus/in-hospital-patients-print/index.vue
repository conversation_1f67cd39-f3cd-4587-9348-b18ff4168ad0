<template>
  <div class="container">
    <div class="header">
      <div class="query-word">治疗组:</div>
      <el-select v-model="ZhiLiaoZu" filterable>
        <el-option
          v-for="item in ZhiLiaoZuList"
          :key="item.id"
          :label="item.mingCheng"
          :value="item.id"
        ></el-option>
      </el-select>
      <div class="button">
        <el-button type="success" @click="exportToExcel">导出EXCEL</el-button>
        <el-button type="primary" @click="printHandle">打印</el-button>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">本治疗组在院病人列表</div>
      </div>
      <div class="table">
        <el-table max-height="688" border stripe :data="patientData" style="width: 100%">
          <el-table-column type="index" label="序号" width="60"></el-table-column>
          <el-table-column prop="bingQuMC" width="130" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="130" label="床位号"></el-table-column>
          <el-table-column prop="bingAnHao" width="160" label="病案号"></el-table-column>
          <el-table-column prop="bingRenXM" width="130" label="姓名"></el-table-column>
          <el-table-column prop="xingBie" width="100" label="性别">
            <template #default="{ row }">
              <span>{{ row.xingBie == 1 ? '男' : '女' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="chuShengRQ" width="140" label="出生日期"></el-table-column>
          <el-table-column prop="nianLing" width="120" label="年龄"></el-table-column>
          <el-table-column prop="ruYuanZD" width="486" label="入院诊断"></el-table-column>
          <el-table-column prop="ruYuanRQ" width="140" label="入院日期"></el-table-column>
          <el-table-column prop="shouShuKSSJ" width="140" label="手术日期"></el-table-column>
          <el-table-column prop="zhuGuanYSXM" width="130" label="主管医生"></el-table-column>
          <!-- <el-table-column prop="caoZuo" width="106" align="center" label="操作">
            <template #default="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getZhiLiaoZuListByRYKID, getInPatientListByZLZID } from '@/api/specialized-patients'
import { format, subMonths } from 'date-fns'
import { mapState } from 'vuex'
import * as XLSX from 'xlsx'
export default {
  data() {
    return {
      patientData: [],
      ZhiLiaoZu: '',
      ZhiLiaoZuList: []
    }
  },
  computed: {
    ...mapState({
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,
      renYuanKuID: ({ patient }) => patient.doctorInfo.renYuanKuID,
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.getZhiLiaoZuListByRYKID()
  },
  methods: {
    // 获取列表初始化接口
    async getZhiLiaoZuListByRYKID() {
      try {
        const res = await getZhiLiaoZuListByRYKID({
          renYuanKuID: this.renYuanKuID
        })
        if (res.hasError === 0) {
          this.ZhiLiaoZuList = res.data
          this.ZhiLiaoZu = this.ZhiLiaoZuList[0].id
          this.getInPatientListByZLZID()
        }
      } catch (error) {
        console.log(error)
      }
    },

    async getInPatientListByZLZID() {
      try {
        const res = await getInPatientListByZLZID({
          zhiLiaoZuIDList: this.ZhiLiaoZu,
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.patientData = res.data
          this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },

    delArr() {
      this.patientData = this.patientData.map((item) => {
        const formattedItem = { ...item }
        if (item.chuShengRQ) {
          formattedItem.chuShengRQ = format(new Date(item.chuShengRQ), 'yyyy-MM-dd')
        }
        if (item.ruYuanRQ) {
          formattedItem.ruYuanRQ = format(new Date(item.ruYuanRQ), 'yyyy-MM-dd')
        }
        if (item.shouShuKSSJ) {
          formattedItem.shouShuKSSJ = format(new Date(item.shouShuKSSJ), 'yyyy-MM-dd')
        }
        return formattedItem
      })
    },

    printHandle() {},

    exportToExcel() {
      if (this.patientData.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const worksheet = XLSX.utils.json_to_sheet(
        this.patientData.map((data) => {
          let d = {}
          let columns = [
            { value: 'bingQuMC', label: '病区' },
            { value: 'chuangWeiHao', label: '床位号' },
            { value: 'bingAnHao', label: '病案号' },
            { value: 'bingRenXM', label: '姓名' },
            { value: 'xingBie', label: '性别' },
            { value: 'chuShengRQ', label: '出生日期' },
            { value: 'nianLing', label: '年龄' },
            { value: 'ruYuanZD', label: '入院诊断' },
            { value: 'ruYuanRQ', label: '入院日期' },
            { value: 'shouShuKSSJ', label: '手术日期' },
            { value: 'zhuGuanYSXM', label: '主管医生' }
          ]
          columns.forEach((col) => {
            if (col.value === 'xingBie') {
              d[col.label] = data[col.value] === '1' ? '男' : '女'
            } else {
              d[col.label] = data[col.value]
            }
          })
          return d
        })
      )
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '本治疗组在院病人列表.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

// ::v-deep .el-table__cell:last-child {
//   padding-left: 0px;
// }

// ::v-deep .el-table th.el-table__cell,
// .el-table td.el-table__cell {
//   padding-top: 9px;
//   padding-bottom: 9px;
// }

// ::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
//   padding-top: 4px;
//   padding-bottom: 4px;
// }
</style>
