<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">出院病人病理结果提醒维护</div>
        <!-- <div class="button"><el-button type="primary">新增</el-button></div> -->
      </div>
      <div class="table">
        <el-table max-height="756" border stripe :data="patientData" style="width: 1396px">
          <el-table-column prop="baoGaoSQRQ" width="160" label="报告申请日期"></el-table-column>
          <el-table-column prop="baoGaoRQ" width="160" label="报告时间"></el-table-column>
          <el-table-column prop="bingAnHao" width="120" label="病案号"></el-table-column>
          <el-table-column prop="bingRenXM" width="120" label="病人姓名"></el-table-column>
          <el-table-column prop="bingQuCYSJ" width="160" label="出院日期"></el-table-column>
          <el-table-column prop="chuYuanZKMC" width="160" label="出院专科"></el-table-column>
          <el-table-column prop="bingQuID" width="120" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="120" label="床位"></el-table-column>
          <el-table-column prop="zhiLiaoZuMC" width="160" label="治疗组"></el-table-column>
          <el-table-column prop="caoZuo" width="114" align="center" label="操作">
            <template #default="scope">
              <el-button type="text" size="medium" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getInPatientCheckResultMes } from '@/api/system-maintenance'
import { mapState } from 'vuex'
import { format } from 'date-fns'
export default {
  data() {
    return {
      patientData: []
    }
  },
  async mounted() {
    await this.getInPatientCheckResultMes()
  },
  methods: {
    // 获取列表初始化接口
    async getInPatientCheckResultMes() {
      try {
        const res = await getInPatientCheckResultMes({
          yongHuID: this.$store.state.user.yongHuID
        })
        if (res.hasError === 0) {
          this.patientData = res.data
          this.delArr()
        }
      } catch (error) {
        console.log(error)
      }
    },
    delArr() {
      this.patientData = this.patientData.map((item) => {
        const formattedItem = { ...item }
        if (item.baoGaoSQRQ) {
          formattedItem.baoGaoSQRQ = format(new Date(item.baoGaoSQRQ), 'yyyy-MM-dd HH:mm')
        }
        if (item.baoGaoRQ) {
          formattedItem.baoGaoRQ = format(new Date(item.baoGaoRQ), 'yyyy-MM-dd HH:mm')
        }
        if (item.bingQuCYSJ) {
          formattedItem.bingQuCYSJ = format(new Date(item.bingQuCYSJ), 'yyyy-MM-dd HH:mm')
        }
        return formattedItem
      })
    },
    handleClick(row) {
      window.open(row.baoGaoLJ, '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 808px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
  padding-top: 4px;
  padding-bottom: 4px;
}

::v-deep .el-table__cell:last-child {
  // padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}

::v-deep .el-radio__original {
  display: none !important;
}
</style>
