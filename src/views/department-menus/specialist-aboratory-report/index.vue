<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">专科化验单打印</div>
      </div>
      <div class="table">
        <el-table max-height="754" border stripe :data="patientData" style="width: 682px">
          <template>
            <el-table-column prop="zhuanKeMC" width="180" label="病区床位">
              <template #default="{ row }">
                <span>{{ row.bingQu }}-{{ row.chuangWeiHao }}床</span>
              </template>
            </el-table-column>
            <el-table-column prop="xingMing" width="150" label="病人姓名"></el-table-column>
            <el-table-column prop="caoZuo" width="350" align="center" label="操作">
              <template #default="{ row }">
                <el-button type="text" size="medium" @click="onHandleClick(row)">
                  已打印化验单
                </el-button>
                <el-button
                  type="text"
                  size="medium"
                  style="margin-left: 30px"
                  @click="offHandleClick(row)"
                  v-if="row.weiDaYinSL > 0"
                >
                  共有{{ row.weiDaYinSL }}张化验单未打印
                </el-button>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </div>
    </div>

    <!-- 已打印化验单 -->
    <div class="stock-class">
      <el-dialog
        :visible.sync="onVisible"
        title="已打印化验单"
        width="708px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <el-table :data="paginatedData" stripe border max-height="480px">
          <el-table-column prop="huaYanXH" width="100" label="化验序号"></el-table-column>
          <el-table-column prop="yeMa" width="100" label="页码"></el-table-column>
          <el-table-column prop="yangBenHao" width="170" label="样本号"></el-table-column>
          <el-table-column prop="daYinSJ" width="170" label="打印时间"></el-table-column>
          <el-table-column prop="caoZuo" width="130" align="center" label="操作">
            <template #default="{ row }">
              <el-button type="text" size="medium" @click="see1(row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          :current-page="printPagination.currentPage"
          :page-size="printPagination.pageSize"
          background
          layout="total, prev, pager, next"
          :total="printData.length"
          @current-change="printPageChange"
        ></el-pagination>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="onVisible = false">关闭</el-button>
        </span>
      </el-dialog>

      <!-- 未打印化验单 -->
      <div class="stock-class">
        <el-dialog
          :visible.sync="offVisible"
          title="未打印化验单"
          width="928px"
          pop-type="tip"
          :close-on-click-modal="false"
        >
          <el-table :data="paginatedData" stripe border max-height="480px">
            <el-table-column prop="patientname" width="100" label="患者姓名"></el-table-column>
            <el-table-column prop="examinaim" width="200" label="检查项目"></el-table-column>
            <el-table-column prop="doctadviseno" width="120" label="条形码"></el-table-column>
            <el-table-column prop="sampleno" width="160" label="样本号"></el-table-column>
            <el-table-column prop="checktime" width="170" label="采集时间"></el-table-column>
            <el-table-column prop="caoZuo" width="140" align="center" label="操作">
              <template #default="{ row }">
                <el-button type="text" size="medium" @click="see2(row)">查看</el-button>
                <el-button type="text" size="medium" @click="print(row)">打印</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            :current-page="printPagination.currentPage"
            :page-size="printPagination.pageSize"
            background
            layout="total, prev, pager, next"
            :total="printData.length"
            @current-change="printPageChange"
          ></el-pagination>
          <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="offVisible = false">关闭</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getPatInfoByDeptId,
  getLPatientinfoByBingLiIdList,
  getHydyList,
  insertAssayPrintRecord,
  getAssayPrintRecordListByBingLiID,
  getTestVoList
} from '@/api/specialized-patients'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      onVisible: false, //已打印列表显示判断
      offVisible: false, //未打印列表显示判断
      printPagination: {
        currentPage: 1,
        pageSize: 10
      }, //打印分页
      patientData: [], //病人数据列表
      printData: [], //打印数据列表
      paginatedData: [] // 打印当前页数据
    }
  },
  computed: {
    ...mapState({
      zhiLiaoZuID: ({ patient }) => patient.zhiLiaoZuID,
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit,
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  async mounted() {
    await this.getPatInfoByDeptId()
  },

  methods: {
    // 根据专科id获取病人列表
    async getPatInfoByDeptId() {
      try {
        const res = await getPatInfoByDeptId({
          zhuanKeID: this.zhuanKeID
        })
        if (res.hasError === 0) {
          this.blidArray = res.data.map((item) => item.bingLiID)
          this.getHydyList(this.blidArray)
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取专科治疗组列表
    async getHydyList() {
      try {
        const res = await getHydyList(this.blidArray)
        if (res.hasError === 0) {
          this.patientData = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取化验已打印记录列表
    async onHandleClick(row) {
      try {
        const res = await getAssayPrintRecordListByBingLiID({
          bingLiID: row.bingLiID
        })
        if (res.hasError === 0) {
          this.printData = res.data.sort((a, b) => a.huaYanXH - b.huaYanXH)
          this.printPagination.currentPage = 1 // 重置为第一页
          this.updatePaginatedData() // 更新当前页数据
          this.onVisible = true
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取化验未打印详情
    async offHandleClick(row) {
      try {
        const res = await getTestVoList({
          bingLiID: row.bingLiID
        })
        if (res.hasError === 0) {
          this.bingLiID = row.bingLiID
          this.printData = res.data
          this.printPagination.currentPage = 1 // 重置为第一页
          this.updatePaginatedData() // 更新当前页数据
          this.offVisible = true
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 更新当前页数据
    updatePaginatedData() {
      const { currentPage, pageSize } = this.printPagination
      this.paginatedData = this.printData.slice(
        (currentPage - 1) * pageSize,
        currentPage * pageSize
      )
    },

    // 打印列表分页处理
    printPageChange(page) {
      this.printPagination.currentPage = page
      this.updatePaginatedData()
    },

    // 插入化验打印记录
    async print(row) {
      try {
        const res = await insertAssayPrintRecord([
          {
            bingLiID: this.bingLiID,
            yangBenHao: row.sampleno,
            huaYanXH: 1,
            yeMa: 1,
            daYinSJ: row.checktime
          }
        ])
        if (res.hasError === 0) {
          this.offVisible = false
          this.$message({
            message: '插入化验打印成功',
            type: 'success'
          })
          this.getHydyList()
        }
      } catch (error) {
        console.log(error)
      }
    },

    see1(row) {
      let url = `http://************:8092/HYYZ/testresult/Viewtestresult.aspx?av_new=1&ybh=${row.yangBenHao}`
      window.open(url, '_blank', 'width=950,height=700,left=150,top=150')
    },

    see2(row) {
      let url = `http://************:8092/HYYZ/testresult/Viewtestresult.aspx?av_new=1&yzh=${row.doctadviseno}&ybh=${row.sampleno}`
      window.open(url, '_blank', 'width=950,height=700,left=150,top=150')
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 806px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}

// 化验单打印
.stock-class {
  ::v-deep .el-pagination {
    margin-top: 5px;
    text-align: center;
  }
  ::v-deep .el-dialog {
    padding: 6px 10px;
  }

  ::v-deep .el-table--mini .el-table__cell {
    padding: 12px 0;
  }

  .dialog-footer {
    padding-bottom: 15px;
  }

  .radio-group {
    margin-top: -20px;
    margin-bottom: 4px;
  }

  ::v-deep .custom-radio.is-checked .el-radio__label {
    color: #171c28;
  }

  ::v-deep .el-dialog__header {
    // border-bottom: 1px solid #dadee6;
    padding: 10px 14px;
    font: var(--font-medium);
    font-size: var(--font-size-medium);
    color: #171c28;
    display: flex;
    align-items: center;
  }

  ::v-deep .el-dialog__header::before {
    // content: url('~@/assets/images/info.png');
    // // width: 3px;
    // // height: 16px;
    // // background: #356ac5;
    // // margin-right: 6px;
    // position: absolute;
    // top: 7px;
    // left: 6px;
    // transform: scale(0.55);
    position: absolute;
    left: 22px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }

  ::v-deep .el-dialog__title {
    font-weight: bold;
    margin-left: 10px;
    opacity: 0.8;
  }

  ::v-deep .el-pager li {
    margin: 0 4px;
  }
}

::v-deep .el-table__cell {
  padding-left: 8px;
  padding-top: 6px;
  padding-bottom: 6px;
}

::v-deep .el-table__cell:last-child {
  padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}
</style>
