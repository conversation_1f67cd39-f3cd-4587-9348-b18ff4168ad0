<template>
  <div class="container">
    <div class="header">
      <div style="display: flex; align-items: center">
        <div class="query-word">出院日期：</div>
        <div class="query-value">
          <el-date-picker
            v-model="ruleForm.date"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </div>
        <div class="button">
          <el-button type="primary" @click="query">查询</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">ICU病人分级情况查询</div>
      </div>
      <div class="content-table">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="fenJi" label="分级"></el-table-column>
          <el-table-column prop="zongRenShu" label="总人数"></el-table-column>
          <el-table-column prop="zhiYuRS" label="治愈"></el-table-column>
          <el-table-column prop="haoZhuanRS" label="好转"></el-table-column>
          <el-table-column prop="weiYuRS" label="未愈"></el-table-column>
          <el-table-column prop="siWangRS" label="死亡"></el-table-column>
          <el-table-column prop="qitaRS" label="其他"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { format } from 'date-fns'
import { getICUFenJiStatisticVo } from '@/api/information'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {},
  data() {
    return {
      ruleForm: {
        date: [],
        jieShuSJ: '', //结束时间
        kaiShiSJ: '' //开始时间
      },
      tableData: []
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const now = new Date()
      const date1 = new Date().setMonth(new Date().getMonth() - 1)
      const nowStr = format(now, 'yyyy-MM-dd')
      const date1Str = format(date1, 'yyyy-MM-dd')
      this.ruleForm['date'] = [date1Str, nowStr]
    },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      if (reqData.date) {
        reqData['kaiShiSJ'] = reqData.date[0] + ' 00:00:00'
        reqData['jieShuSJ'] = reqData.date[1] + ' 23:59:59'
      }
      delete reqData.date
      const res = await getICUFenJiStatisticVo(reqData)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
      }
      const tableKey = ['一级', '二级', '三级', '四级-低危', '四级-不可逆性疾病终末期']
      let tableData = []
      for (const k of tableKey) {
        tableData.push({
          fenJi: k,
          zongRenShu: 0,
          zhiYuRS: 0,
          haoZhuanRS: 0,
          weiYuRS: 0,
          siWangRS: 0,
          qitaRS: 0
        })
      }
      res.data.forEach((item) => {
        for (let i = 0; i < tableData.length; i++) {
          if (item.fenJi === tableData[i].fenJi) {
            tableData[i] = item
          }
        }
      })
      this.tableData = tableData
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 200px;
    min-width: 200px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

.red-star {
  color: red;
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
