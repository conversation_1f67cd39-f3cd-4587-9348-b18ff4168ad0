<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">姓名:</div>
        <div class="header-item-content">
          {{ formList.xingMing }}
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">科室:</div>
        <div class="header-item-content">
          {{ formList.xingMing }}
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">终生码:</div>
        <div class="header-item-content">
          {{ formList.zhongShengMa }}
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">人员ID:</div>
        <div class="header-item-content">
          {{ formList.yongHuID }}
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">获取目前专业技术职称时间:</div>
        <div class="header-item-content">
          {{ formList.huoQuSJ }}
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">年审时间:</div>
        <div class="header-item-content">
          {{ formList.xingMing }}
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">医疗人员医疗技术资格审批</div>
      </div>
      <div class="table">
        <el-form ref="form" :model="form" label-width="80px">
          <el-form-item label="活动名称">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="活动名称">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="活动名称">
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="即时配送">
            <el-switch v-model="form.delivery"></el-switch>
          </el-form-item>
          <el-form-item label="活动性质">
            <el-checkbox-group v-model="form.type">
              <el-checkbox label="美食/餐厅线上活动" name="type"></el-checkbox>
              <el-checkbox label="地推活动" name="type"></el-checkbox>
              <el-checkbox label="线下主题活动" name="type"></el-checkbox>
              <el-checkbox label="单纯品牌曝光" name="type"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSubmit">立即创建</el-button>
            <el-button>取消</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getMedicalqualificationByYongHuId,
  getMedicalqualificationById,
  getAllByLieBie
} from '@/api/information'
import { getZhuanKeList } from '@/api/report-card'
import { format } from 'date-fns'
export default {
  data() {
    return {
      formList: {},
      form: {},
      zhuanKeID: '',
      ylYwryyljszgglBgVo: {},
      tableData: [],
      yongHuID: ''
    }
  },
  mounted() {
    this.fetchInit()
  },
  methods: {
    handleClick(row) {},
    async fetchInit() {
      console.log(this.$route)
      if (this.$route.query?.yongHuID) {
        this.yongHuID = this.$route.query.yongHuID
        const res = await getMedicalqualificationByYongHuId({ yongHuId: this.yongHuID })
        if (res.hasError === 0) {
          this.formList = res.data
        }
      } else {
        const res = await getMedicalqualificationById()
        if (res.hasError === 0) {
          this.formList = res.data
          this.ylYwryyljszgglBgVo = res.data.ylYwryyljszgglBgVo
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.table {
  width: 980px;
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
