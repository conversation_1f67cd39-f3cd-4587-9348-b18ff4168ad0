<template>
  <div class="container_content">
    <default-dialog
      :visible="visible"
      title="修改"
      width="400px"
      confirm-button-text="保存"
      cancel-button-text="关闭"
      @confirm="submitForm"
      @cancel="cancelDialog"
    >
      <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="80px">
        <el-form-item label="类别" prop="leiBie">
          <el-input v-model="ruleForm.leiBie"></el-input>
        </el-form-item>
        <el-form-item label="代码" prop="daiMa">
          <el-input v-model="ruleForm.daiMa"></el-input>
        </el-form-item>
        <el-form-item label="名称" prop="mingChen">
          <el-input v-model="ruleForm.mingChen"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="beiZhu">
          <el-input v-model="ruleForm.beiZhu"></el-input>
        </el-form-item>
      </el-form>
    </default-dialog>
    <div class="header">
      <div>
        <div style="display: flex; align-items: center">
          <div class="query-word">请输入类别：</div>
          <div style="margin-left: 5px">
            <el-input v-model="lieBie"></el-input>
          </div>
          <div class="button">
            <el-button @click="initData">查询</el-button>
            <el-button type="primary" @click="back">返回</el-button>
            <el-button type="primary" @click="handleAddClick">新增</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">列表信息</div>
      </div>
      <div style="flex: 1; min-height: 0">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="leiBie" label="类别"></el-table-column>
          <el-table-column prop="daiMa" label="代码"></el-table-column>
          <el-table-column prop="mingChen" label="名称"></el-table-column>
          <el-table-column prop="beiZhu" label="备注"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" label="状态">
            <template #default="scope">
              {{ scope.row.zhuangTaiBZ === '0' ? '暂停' : '启用' }}
            </template>
          </el-table-column>
          <el-table-column prop="caoZuoZheID" label="修改人员"></el-table-column>
          <el-table-column prop="xiuGaiSJ" label="修改时间" width="150"></el-table-column>
          <el-table-column label="操作" width="200">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row, 1)">
                启用
              </el-button>
              <el-button type="text" size="small" @click="handleClick(scope.row, 2)">
                暂停
              </el-button>
              <el-button type="text" size="small" @click="handleEditClick(scope.row)">
                修改
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getAllByLieBie,
  updateBaoGaoJieGouZhuangTaiBZ,
  updateBaoGaoJieGou,
  insertBaoGaoJieGou
} from '@/api/information'
import { format } from 'date-fns'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { mapState } from 'vuex'
export default {
  name: 'Approval',
  components: {
    DefaultDialog
  },
  data() {
    return {
      visible: false,
      lieBie: '',
      tableData: [],
      ruleForm: {},
      rules: {
        leiBie: [{ required: true, message: '请输入类别', trigger: 'blur' }],
        daiMa: [{ required: true, message: '请输入代码', trigger: 'blur' }],
        mingChen: [{ required: true, message: '请输入名称', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapState({
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  mounted() {
    this.initData()
  },
  methods: {
    async initData() {
      const res = await getAllByLieBie({ lieBie: this.lieBie || '0000' })
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    },
    //启用or暂停
    async handleClick(row, type) {
      let zhuangTaiBZ = ''
      let msg = ''
      if (type === 1) {
        msg = '启用'
        zhuangTaiBZ = '0'
      } else if (type === 2) {
        zhuangTaiBZ = '1'
        msg = '暂停'
      }
      this.$confirm(`是否确定${msg}, 是否继续?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await updateBaoGaoJieGouZhuangTaiBZ({
          zhuangTaiBZ: zhuangTaiBZ,
          daiMa: row.daiMa,
          lieBie: row.lieBie
        })
        if (res.hasError === 0) {
          this.$message.success(msg + '成功！')
          this.initData()
        }
      })
    },
    //编辑
    handleEditClick(row) {
      this.ruleForm = row
      this.defaultDialog = true
    },
    //新增
    handleAddClick() {
      this.ruleForm = {
        leiBie: '',
        daiMa: '',
        mingChen: '',
        beiZhu: ''
      }
      this.defaultDialog = true
    },
    //保存修改内容
    async submitForm() {
      if (this.ruleForm.caoZuoZheID) {
        const res = await updateBaoGaoJieGou({
          mingChen: row.mingChen,
          daiMa: row.daiMa,
          lieBie: row.lieBie,
          beiZhu: row.beiZhu,
          caoZuoZheID: 0,
          xiuGaiSJ: format(new Date(), 'yyyy-MM-dd HH:mm:ss')
        })
        if (res.hasError === 0) {
          this.$message.success('修改成功！')
          this.initData()
        }
      } else {
        const res = await insertBaoGaoJieGou({
          mingChen: row.mingChen,
          daiMa: row.daiMa,
          lieBie: row.lieBie,
          beiZhu: row.beiZhu,
          caoZuoZheID: 0,
          xiuGaiSJ: format(new Date(), 'yyyy-MM-dd HH:mm:ss')
        })
        if (res.hasError === 0) {
          this.$message.success('新增成功！')
          this.initData()
        }
      }
    },
    //关闭弹框
    cancelDialog() {
      this.defaultDialog = false
    },
    //返回
    back() {
      this.$emit('confirm')
    }
  }
}
</script>

<style scoped lang="scss">
.container_content {
  height: calc(100% - 40px);
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  height: calc(100% - 40px);
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
