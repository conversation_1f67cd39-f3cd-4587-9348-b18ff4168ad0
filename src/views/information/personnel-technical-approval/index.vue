<template>
  <div class="container">
    <approval v-if="weiHuType" @confirm="weiHuType = false"></approval>
    <div v-if="!weiHuType" class="header">
      <div class="header-item">
        <div class="header-item-title">提交日期:</div>
        <div class="header-item-date">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="default"
          />
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">科室:</div>
        <div class="header-item-input">
          <el-select v-model="zhuanKeID" placeholder="请选择专科" autofocus filterable>
            <el-option
              v-for="item in zhuanKeList"
              :key="item.daiMa"
              :label="item.mingCheng"
              :value="item.daiMa"
            />
          </el-select>
        </div>
        <div class="header-item-button"><el-button @click="onSerchData">查询</el-button></div>
        <div class="header-item-button">
          <el-button @click="weiHuType = true">进入维护页</el-button>
        </div>
      </div>
    </div>
    <div v-if="!weiHuType" class="content">
      <div class="content-header">
        <div class="title">（医务处）医疗人员医疗技术资格审批</div>
      </div>
      <div class="table">
        <el-table max-height="648" stripe border :data="listsData">
          <el-table-column prop="baoGaoID" width="100" label="报告ID"></el-table-column>
          <el-table-column prop="yongHuID" width="100" label="用户ID"></el-table-column>
          <el-table-column prop="xingMing" width="100" label="姓名"></el-table-column>
          <el-table-column prop="zhongShengMa" width="100" label="终生码"></el-table-column>
          <el-table-column prop="zhuanKeMC" width="120" label="科室"></el-table-column>
          <el-table-column prop="tiJiaoSJ" width="150" label="提交日期"></el-table-column>
          <el-table-column prop="zhuangTaiBZ" width="100" label="当前状态"></el-table-column>
          <el-table-column fixed="right" width="200" label="操作" align="center">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
              <el-button type="text" size="small" @click="handleSPClick(scope.row)">审批</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            :current-page.sync="currentPage"
            :page-size.sync="pageSize"
            layout="total, prev, pager, next"
            :total="total"
            @current-change="handleCurrentChange"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { getYiWuChuShenPiLieBiaoAll, updateShenPiZhuangTai } from '@/api/information'
import { getZhuanKeList } from '@/api/report-card'
import Approval from './compontents/Approval.vue'
import { format } from 'date-fns'
export default {
  components: {
    Approval
  },
  data() {
    return {
      weiHuType: false,
      currentPage: '1',
      dateRange: '',
      zhuanKeID: '',
      zhuanKeList: [],
      total: '',
      listsData: [],
      tableData: [],
      pageSize: 10
    }
  },
  async mounted() {
    await this.fetchInit()
    this.onSerchData()
  },
  methods: {
    //查看
    handleClick(row) {
      this.$router.push({
        path: `/information/personnel-qualification-registration`,
        query: { yongHuID: row.yongHuID }
      })
    },
    //审批
    handleSPClick(row) {
      this.$confirm('是否确认审批?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await updateShenPiZhuangTai({ baoGaoID: row.baoGaoID, zhuangTaiBZ: '2' })
        if (res.hasError === 0) {
          this.$message.success('审批成功!')
        }
      })
    },
    //初始化
    async fetchInit() {
      const res = await getZhuanKeList()
      if (res.hasError === 0) {
        this.zhuanKeList = res.data
      }
    },
    async onSerchData() {
      // const start = format(this.dateRange[0], 'yyyy-MM-dd HH:mm:ss')
      // const end = format(this.dateRange[1], 'yyyy-MM-dd HH:mm:ss')
      const res = await getYiWuChuShenPiLieBiaoAll({
        jieShuSJ: '2023-12-20 00:00:00',
        kaiShiSJ: '2023-12-17 00:00:00',
        zhuanKeID: this.zhuanKeID || 0,
        pageIndex: this.currentPage,
        pageSize: this.pageSize
      })
      if (res.hasError === 0) {
        this.listsData = res.data
        this.total = res.extendData.total
      }
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
      this.onSerchData()
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.table {
  width: 980px;
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
.pagination-container {
  max-width: 1500px;
  height: 58px;
  padding: 10px;
  background-color: #eaf0f9;
  text-align: right;
}
</style>
