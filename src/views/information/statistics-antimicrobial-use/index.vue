<template>
  <div style="width: 100%; height: 100%">
    <div v-show="!shenpiTjType" class="container">
      <div class="header">
        <div>
          <div style="display: flex; align-items: center">
            <div class="query-word">日期选择：</div>
            <div class="query-value">
              <el-date-picker
                v-model="ruleForm.date"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </div>
            <div class="query-word">状态标志：</div>
            <div class="query-value">
              <el-checkbox-group v-model="ruleForm.zhuangTaiBZList">
                <el-checkbox
                  v-for="data in baseInfo.zhuangTaiBZList"
                  :key="data.code"
                  :label="data.code"
                >
                  {{ data.name }}
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div style="display: flex; align-items: center; margin-top: 4px">
            <div class="query-word">医生或科室：</div>
            <div class="query-value">
              <el-radio-group v-model="ruleForm.leiXing">
                <el-radio label="ys">
                  <span>处方医师：</span>
                  <el-input
                    v-model="ruleForm.leiXingYS"
                    clearable
                    style="width: 200px"
                    @focus="showYSTC"
                  ></el-input>
                </el-radio>
                <el-radio label="ks">
                  <span>科室：</span>
                  <el-select v-model="ruleForm.leiXingKS" style="width: 200px">
                    <el-option
                      v-for="item in zhuanKeList"
                      :key="item.buMenID"
                      :label="item.buMenMC"
                      :value="item.buMenID"
                    ></el-option>
                  </el-select>
                </el-radio>
              </el-radio-group>
            </div>
            <div class="query-word">药品：</div>
            <div class="query-value">
              <el-radio-group v-model="ruleForm.leiBie">
                <el-radio label="ypmc">
                  <span>药品名称：</span>
                  <el-input
                    v-model="ruleForm.leiBieYPMC"
                    clearable
                    style="width: 200px"
                    @focus="showYWTC"
                  ></el-input>
                </el-radio>
                <el-radio label="ypzl">
                  <span>药品种类：</span>
                  <el-select v-model="ruleForm.leiBieZL" style="width: 200px">
                    <el-option
                      v-for="item in baseInfo.yaoPinZLList"
                      :key="item.daiMa"
                      :label="item.beiZhu"
                      :value="item.daiMa"
                    ></el-option>
                  </el-select>
                </el-radio>
              </el-radio-group>
            </div>
            <div class="button">
              <el-button type="primary" @click="query">统计</el-button>
              <el-button type="primary" @click="shenpiTjType = true">查看审批</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="content-header">
          <div class="title">特殊使用级抗菌药物使用情况统计</div>
          <div>
            <el-button type="primary" @click="shenPiDialog = true">审批</el-button>
            <el-button type="primary" @click="exportExcel('tableData')">导出</el-button>
            <el-button type="primary" @click="query">打印</el-button>
          </div>
        </div>
        <div class="content-table">
          <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
            <el-table-column label="">
              <el-table-column prop="tongYongMing" label="通用名"></el-table-column>
            </el-table-column>
            <el-table-column label="用药病人信息" align="center">
              <el-table-column prop="xingMing" label="姓名"></el-table-column>
              <el-table-column prop="bingAnHao" label="病案号"></el-table-column>
              <el-table-column prop="chuFangYSKS" label="处方医生科室"></el-table-column>
              <el-table-column prop="zhuYaoZD" label="用药主要诊断"></el-table-column>
            </el-table-column>
            <el-table-column label="用药信息" align="center">
              <el-table-column prop="yongFa" label="药物用法"></el-table-column>
              <el-table-column prop="shiFouHZ" label="感染性疾病科是否会诊"></el-table-column>
              <el-table-column prop="shiFouWSWSJ" label="使用前是否微生物送检"></el-table-column>
              <el-table-column prop="shiFouJZ" label="是否紧急情况下使用"></el-table-column>
              <el-table-column prop="chuFangYS" label="处方医师"></el-table-column>
              <el-table-column prop="chuFangSJ" label="处方时间"></el-table-column>
              <el-table-column prop="huiZhenSPYS" label="会诊审批医生"></el-table-column>
              <el-table-column prop="shenFangYS" label="审方药师"></el-table-column>
            </el-table-column>
            <el-table-column label="统计数据备注" align="center">
              <el-table-column label="备注">
                <template #default="scope">
                  <el-input
                    v-model="scope.row.beiZhu"
                    :disabled="!scope.row.beiZhuZT"
                    type="textarea"
                    :rows="2"
                    @blur="saveBeiZhu(scope.$index, scope.row)"
                  ></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="beiZhuXGRY" label="修改人员"></el-table-column>
              <el-table-column prop="beiZhuXGSJ" label="修改时间"></el-table-column>
            </el-table-column>
            <el-table-column label="">
              <el-table-column label="操作" align="center">
                <template #default="scope">
                  <el-button type="text" size="mini" @click="setBeiZhu(scope.$index, scope.row)">
                    编辑
                  </el-button>
                </template>
              </el-table-column>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <div v-show="shenpiTjType" class="container">
      <div class="header">
        <div>
          <div style="display: flex; align-items: center">
            <div class="query-word">日期选择：</div>
            <div class="query-value">
              <el-date-picker
                v-model="ruleForm2.date"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </div>
            <div class="query-word">科室：</div>
            <div class="query-value">
              <el-select v-model="ruleForm2.zhuanKeID" style="width: 200px">
                <el-option
                  v-for="item in zhuanKeList"
                  :key="item.buMenID"
                  :label="item.buMenMC"
                  :value="item.buMenID"
                ></el-option>
              </el-select>
            </div>
            <div class="query-word">药品种类：</div>
            <div class="query-value">
              <el-select v-model="ruleForm2.yaoPinZL" style="width: 200px">
                <el-option
                  v-for="item in baseInfo.yaoPinZLList"
                  :key="item.daiMa"
                  :label="item.beiZhu"
                  :value="item.daiMa"
                ></el-option>
              </el-select>
            </div>
            <div class="query-word">审批状态：</div>
            <div class="query-value">
              <el-select v-model="ruleForm2.zhuangTaiBZ" style="width: 200px">
                <el-option
                  v-for="item in baseInfo2.zhuangTaiBZList"
                  :key="item.code"
                  :label="item.name"
                  :value="item.code"
                ></el-option>
              </el-select>
            </div>
            <div class="button">
              <el-button type="primary" @click="query2">查询</el-button>
              <el-button type="primary" @click="exportExcel('tableData2')">导出Excel</el-button>
              <el-button type="primary" @click="shenpiTjType = false">返回上一层</el-button>
            </div>
          </div>
        </div>
      </div>
      <div class="content">
        <div class="content-header">
          <div class="title">审批统计</div>
        </div>
        <div class="content-table">
          <el-table :data="tableData2" border size="medium" height="100%" style="width: 100%">
            <el-table-column prop="zhuanKeMC" label="科室名称"></el-table-column>
            <el-table-column prop="kaiShiSJ" label="统计开始时间"></el-table-column>
            <el-table-column prop="jieShuSJ" label="统计结束时间"></el-table-column>
            <el-table-column prop="shenPiRYID" label="审批人员"></el-table-column>
            <el-table-column prop="shenPiSJ" label="审批时间"></el-table-column>
            <el-table-column label="审批状态">
              <template #default="scope">
                <el-tag v-if="scope.row.zhuangTaiBZ === '2'">已审批</el-tag>
                <el-tag v-else-if="scope.row.zhuangTaiBZ === '3'" type="success">审批通过</el-tag>
                <el-tag v-else type="info">未审批</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="shenPiYJ" label="审批意见"></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <doctor-selector
      :request="request"
      :visible="showDoctorSelector"
      @handleConfim="afterSelect"
      @handleChangeVisible="afterCloseDialog"
    />
    <el-dialog width="30%" :visible.sync="kangJunYWDialog">
      <div slot="title" class="title">选择抗菌药物</div>
      <table class="renYuanList">
        <thead>
          <tr>
            <td>药物名称</td>
            <td>计量</td>
            <td>库存量</td>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in currentPageYaoWuList" :key="item.id" @dblclick="selectYaoWu(item)">
            <td>{{ item.mingCheng }}</td>
            <td>{{ item.jiLiang }}</td>
            <td>{{ item.keShouFeiYL > 0 ? '有库存' : '【缺货】' }}</td>
          </tr>
        </tbody>
      </table>
      <el-pagination
        class="pagination"
        small
        background
        :current-page.sync="currentYaoWuPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="yaoWuList.length"
      ></el-pagination>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="kangJunYWDialog = false">关闭</el-button>
        <el-button size="mini" type="primary" @click="kangJunYWDialog = false">确认</el-button>
      </span>
    </el-dialog>
    <el-dialog
      title="审批窗口"
      :visible.sync="shenPiDialog"
      width="30%"
      :before-close="shenPiClose"
    >
      <el-form :model="shenPiForm">
        <el-form-item label="审批状态：" :label-width="shenPiWidth">
          <el-radio-group v-model="shenPiForm.zhuangTaiBZ" @input="shenPiInput">
            <el-radio v-for="item in baseInfo.shenPiZTList" :key="item.code" :label="item.code">
              {{ item.name }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审批意见：" :label-width="shenPiWidth">
          <el-input v-model="shenPiForm.shenPiYJ" type="textarea" :rows="2"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="shenPiClose">取 消</el-button>
        <el-button type="primary" @click="shenPi">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'
import { getGuiDangBBCX } from '@/api/medical-quality'
import {
  chaKanSPTsKjywSyqkTj,
  getYyyzdListByCondition,
  getZlzCybr,
  initCKSPTsKjywSyqkTj,
  initTeShuKjywSyqkTj,
  saveBeiZhuTsKjywSyqkTj,
  shenPiTsKjywSyqkTj,
  teShuKjywSyqkTj
} from '@/api/information'

import DoctorSelector from 'wyyy-component/packages/doctor-selector'
import request from '@/utils/request'
import { getZhuanKeList } from '@/api/user'
import { searchKangJunYP } from '@/api/consultation-form-manage'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {
    DoctorSelector
  },
  data() {
    return {
      ruleForm: {
        date: [],
        kaiShiSJ: '', //开始时间(yyyy-MM-dd)
        jieShuSJ: '', //结束时间(yyyy-MM-dd)
        leiXing: 'ks', //类型(ys=处方医生,ks=科室)
        leiXingYS: '',
        leiXingYSID: '',
        leiXingKS: 0,
        leiXingID: '', //类型ID(医生ID或科室ID)
        leiBie: 'ypzl', //类别(ypmc=药品名称,ypzl=药品种类)
        leiBieYPMC: '',
        leiBieYPID: '',
        leiBieZL: '',
        leiBieID: '', //类别ID(药品ID或药品种类)
        zhuangTaiBZList: ['1', '2'] //会诊单状态标志
      },
      tableData: [],
      zhuanKeList: [],
      baseInfo: {},
      // total: 0,
      // pageIndex: 1,
      // pageSize: 10,

      request: request,
      showDoctorSelector: false,

      beiZhuNR: '',
      yaoWuList: [],
      kangJunYWDialog: false,
      currentPage: 1, //抗菌药弹出框 当前页号
      currentYaoWuPage: 1,
      pageSize: 13, //抗菌药弹出框 页面行数

      shenPiDialog: false,
      shenPiWidth: '120px',
      shenPiForm: {
        zhiDingZKID: '', //指定专科ID
        yaoPinZL: '', //药品种类
        kaiShiSJ: '', //开始时间yyyy-MM-dd HH:mm:ss
        jieShuSJ: '', //结束时间yyyy-MM-dd HH:mm:ss
        shenPiJLS: '', //审批记录数
        shenPiYJ: '通过', //审批意见，纯文本
        zhuangTaiBZ: '2' //状态标志
      },

      shenpiTjType: false,
      baseInfo2: {},
      ruleForm2: {
        date: [],
        kaiShiSJ: '', //开始时间(yyyy-MM-dd)
        jieShuSJ: '', //结束时间(yyyy-MM-dd)
        yaoPinZL: '', //药品种类
        zhuanKeID: '', //专科ID
        zhuangTaiBZ: '' //状态标志(1=未审批，2=已审批，3=审批通过)
      },
      tableData2: []
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    },
    currentPageYaoWuList() {
      console.log('this.yaoWuList', this.yaoWuList)
      return this.yaoWuList.slice(
        (this.currentYaoWuPage - 1) * this.pageSize,
        (this.currentYaoWuPage - 1) * this.pageSize + this.pageSize
      )
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      await this.initKangJunYP()
      await this.getReportCard()
      this.ruleForm['date'] = [this.baseInfo.kaiShiSJ, this.baseInfo.jieShuSJ]
      this.ruleForm2['date'] = [this.baseInfo.kaiShiSJ, this.baseInfo.jieShuSJ]
    },
    async getReportCard() {
      const res = await initTeShuKjywSyqkTj({})
      this.baseInfo = res.data
      console.log('baseInfo:', this.baseInfo)

      const res2 = await initCKSPTsKjywSyqkTj({})
      this.baseInfo2 = res2.data
      this.baseInfo2.zhuangTaiBZList.splice(0, 0, {
        code: '',
        name: '全部'
      })

      const resZK = await getZhuanKeList()
      console.log('zhuanKeList:', resZK)
      this.zhuanKeList = resZK.data
      this.zhuanKeList.splice(0, 0, {
        buMenID: 0,
        buMenMC: '全院'
      })
    },
    // async setPage(pageIndex) {
    //   this.pageIndex = pageIndex
    //   await this.query()
    // },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      // reqData['curr_page'] = this.pageIndex
      // reqData['count'] = this.pageSize
      if (reqData.date) {
        reqData['kaiShiSJ'] = reqData.date[0]
        reqData['jieShuSJ'] = reqData.date[1]
        // reqData['kaiShiSJ'] = reqData.date[0] + ' 00:00:00'
        // let jieShuSJ = new Date(reqData.date[1])
        // jieShuSJ.setDate(jieShuSJ.getDate() + 1)
        // reqData['jieShuSJ'] = format(jieShuSJ, 'yyyy-MM-dd') + ' 00:00:00'
      }
      delete reqData.date
      if (reqData.leiXing === 'ks') {
        reqData.leiXingID = reqData.leiXingKS
      } else {
        reqData.leiXingID = reqData.leiXingYSID
      }
      if (reqData.leiBie === 'ypzl') {
        reqData.leiBieID = reqData.leiBieZL
      } else {
        reqData.leiBieID = reqData.leiBieYPID
      }
      console.log(reqData)
      const res = await teShuKjywSyqkTj(reqData)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        this.tableData = res.data.shuJuList
        // this.total = this.tableData.length
      }
    },
    async query2() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm2))
      if (reqData.date) {
        reqData['kaiShiSJ'] = reqData.date[0]
        reqData['jieShuSJ'] = reqData.date[1]
      }
      delete reqData.date
      console.log(reqData)
      const res = await chaKanSPTsKjywSyqkTj(reqData)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        this.tableData2 = res.data
      }
    },
    showYSTC() {
      this.ruleForm.leiXing = 'ys'
      this.showDoctorSelector = true
    },
    afterSelect(info) {
      console.log('选中的用户信息为：', info)
      this.showDoctorSelector = false
      this.ruleForm.leiXingYS = info.xingMing
      this.ruleForm.leiXingYSID = info.yongHuID
    },
    afterCloseDialog(value) {
      console.log('点击弹窗关闭按钮')
      this.showDoctorSelector = value
    },
    async initKangJunYP() {
      let res = await searchKangJunYP({
        kangJunJB: '3',
        key: '',
        yaoFangDM: '2A3'
      })
      this.yaoWuList = res.data
      console.log('res.data', res.data)
    },
    showYWTC() {
      this.ruleForm.leiBie = 'ypmc'
      this.kangJunYWDialog = true
    },
    selectYaoWu(item) {
      console.log('item', item)
      this.ruleForm.leiBieYPMC = item.mingCheng
      this.ruleForm.leiBieYPID = item.yaoPinID
      this.kangJunYWDialog = false
    },
    view(row, column, event) {
      console.log('row', row)
      console.log('column', column)
      console.log('event', event)
    },
    setBeiZhu(index, row) {
      this.$set(this.tableData[index], 'beiZhuZT', true)
      this.beiZhuNR = row.beiZhu
    },
    saveBeiZhu(index, row) {
      console.log('失去焦点')
      // const this = this
      const now = format(new Date(), 'yyyy-MM-dd HH:mm:ss')
      this.$confirm('确定修改备注内容吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          console.log('确认')
          const res = await saveBeiZhuTsKjywSyqkTj({
            yiZhuID: row.yiZhuID, //医嘱ID
            beiZhu: row.beiZhu, //备注
            xiuGaiRYID: this.patientInfo.yiShiYHID, //修改人员ID
            xiuGaiSJ: now //修改时间
          })
          console.log(res)
          if (this.msg(res)) {
            console.log('返回成功')
            this.$set(this.tableData[index], 'beiZhuXGRY', this.patientInfo.yongHuXM)
            this.$set(this.tableData[index], 'beiZhuXGSJ', now)
          }
        })
        .catch(() => {
          console.log('进入取消')
          this.$set(this.tableData[index], 'beiZhu', this.beiZhuNR)
        })
      this.$set(this.tableData[index], 'beiZhuZT', false)
    },
    shenPiClose() {
      this.shenPiDialog = false
    },
    shenPiInput(label) {
      this.shenPiForm.shenPiYJ = this.baseInfo.shenPiZTList.find((item) => item.code === label).name
    },
    async shenPi() {
      console.log('点击审批按钮')
      if (this.ruleForm['leiXing'] !== 'ks' || this.ruleForm['leiBie'] !== 'ypzl') {
        this.$message.error('请选择科室和药品种类进行审批')
        return
      }
      if (this.ruleForm['leiXingKS'] === 0) {
        this.$message.error('请选择具体科室进行审批')
        return
      }
      if (this.tableData.length <= 0) {
        this.$message.error('当前不存在需要审批的数据')
        return
      }
      this.shenPiForm['zhiDingZKID'] = this.ruleForm['leiXingKS']
      this.shenPiForm['yaoPinZL'] = this.ruleForm['leiBieZL']
      this.shenPiForm['kaiShiSJ'] = this.ruleForm['date'][0] + ' 00:00:00'
      this.shenPiForm['jieShuSJ'] = this.ruleForm['date'][1] + ' 00:00:00'
      this.shenPiForm['shenPiJLS'] = this.tableData.length
      const res = await shenPiTsKjywSyqkTj(this.shenPiForm)
      this.msg(res)
      this.shenPiClose()
    },
    exportExcel(type) {
      if (this[type].length === 0) {
        return this.$message.error('暂无数据可导出')
      }
      let keyMap = {}
      let fileName = ''
      if (type === 'tableData') {
        fileName = '特殊使用级抗菌药物使用情况统计.xlsx'
        keyMap = {
          bianHao: '编号',
          zhuYuanHao: '病案号',
          bingRenXM: '病人姓名',
          xingBie: '性别',
          siWangSJ: '死亡时间',
          siWangYYA: '直接死亡的原因',
          huJiDZ: '户籍地址',
          tianBiaoSJ: '填写时间',
          zhuanKeMC: '填写科室',
          yiShengQM: '填写医生'
        }
      } else if (type === 'tableData2') {
        fileName = '审批统计查询.xlsx'
        keyMap = {
          zhuanKeMC: '科室名称',
          kaiShiSJ: '统计开始时间',
          jieShuSJ: '统计结束时间',
          shenPiRYID: '审批人员',
          shenPiSJ: '审批时间',
          zhuangTaiBZ: '审批状态',
          shenPiYJ: '审批意见'
        }
      }
      // 创建工作表
      const worksheet = XLSX.utils.json_to_sheet(
        this[type].map((data) => {
          let d = {}
          for (const key in keyMap) {
            if (key === 'zhuangTaiBZ') {
              if (data[key] === '2') {
                d[keyMap[key]] = '已审批'
              } else if (data[key] === '3') {
                d[keyMap[key]] = '审批通过'
              } else {
                d[keyMap[key]] = '未审批'
              }
            } else {
              d[keyMap[key]] = data[key]
            }
          }
          return d
        })
      )
      // 创建工作簿并添加工作表
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
      // 生成 Excel 文件并下载
      try {
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    },
    msg(res) {
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
        return false
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        return true
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 200px;
    min-width: 200px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

.renYuanList {
  margin-top: 20px;
  width: 100%;
  thead {
    background-color: #eaf0f9;
  }
  td {
    border: 1px solid #dcdfe6;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    padding: 6px;
    font-size: 13px;
  }
  tbody {
    tr:nth-child(even) {
      background-color: #eaf0f9;
    }
    tr:nth-child(odd) {
      background-color: #f6f6f6;
    }
  }
}

.red-star {
  color: red;
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
