<template>
  <div class="container">
    <div class="header no-print">
      <div>
        <div class="header-item">
          <div class="header-item-title">选择年:</div>
          <div class="header-item-radio">
            <el-select v-model="fromList.year" class="item_date" autofocus filterable>
              <el-option
                v-for="item in nianFenList"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.daiMa.toString()"
              />
            </el-select>
          </div>
          <div class="header-item-title">选择月:</div>
          <div class="header-item-radio">
            <el-select v-model="fromList.month" class="item_date" autofocus filterable>
              <el-option
                v-for="item in yueFenList"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.daiMa.toString()"
              />
            </el-select>
          </div>
          <div class="header-item-title">选择专科:</div>
          <div class="header-item-radio">
            <el-select v-model="fromList.zhuanKeID" placeholder="请选择专科" autofocus filterable>
              <el-option
                v-for="item in zhuanKeList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID.toString()"
              />
            </el-select>
          </div>
          <div class="header-item-title">选择院区:</div>
          <div class="header-item-radio">
            <el-select v-model="fromList.yuanQuID" placeholder="请选择院区" autofocus filterable>
              <el-option
                v-for="item in yuanQuList"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.daiMa.toString()"
              />
            </el-select>
          </div>
          <div class="header-item-button">
            <el-button @click="loadData">查询</el-button>
          </div>
          <div class="header-item-button">
            <el-button style="background-color: #356ac5" @click="exportToExcel">导出</el-button>
          </div>
          <div class="header-item-button">
            <el-button style="background-color: #356ac5" @click="exportToPrint">打印</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header no-print">
        <div class="title">临床路径绩效报表</div>
      </div>
      <div id="section-to-print" class="table">
        <div class="page-print" style="text-align: center">
          <h1>温州医科大学附属第一医院</h1>
          <div>制表人：{{ userInfo.yongHuXM }}</div>
        </div>
        <el-table max-height="648" stripe border :data="listsData">
          <el-table-column
            v-for="obj in tableData"
            :key="obj.value"
            :prop="obj.value"
            :width="obj.width"
            :label="obj.label"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { initLcljJiXiaoBB, getLcljJiXiaoBB } from '@/api/clinical-pathway-performance-report'
import * as XLSX from 'xlsx'
import { format } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      fromList: {
        year: '',
        month: '',
        zhuanKeID: '',
        yuanQuID: ''
      },
      //年
      nianFenList: [],
      //月
      yueFenList: [],
      //专科
      zhuanKeList: [],
      yuanQuList: [
        {
          daiMa: '0',
          mingCheng: '所有院区'
        },
        {
          daiMa: '01',
          mingCheng: '公园路院区'
        },
        {
          daiMa: '02',
          mingCheng: '南白象院区'
        },
        {
          daiMa: '55',
          mingCheng: '龙港院区'
        }
      ],
      listsData: [],
      tableData: [
        {
          label: '科室',
          value: 'zhuanKeMC',
          width: '120'
        },
        {
          label: '医生',
          value: 'yiShengXM',
          width: '120'
        },
        {
          label: '终身代码',
          value: 'zhongShenDM',
          width: '100'
        },
        {
          label: '人员库ID',
          value: 'renYuanKuID',
          width: '100'
        },
        {
          label: '临床路径例数',
          value: 'linChuangLJLS',
          width: '100'
        }
      ]
    }
  },
  computed: {
    ...mapState({
      userInfo: ({ user }) => user.userInfo,
      curZhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  async mounted() {
    this.fromList = {
      year: format(new Date(), 'yyyy'),
      month: format(new Date(), 'MM'),
      zhuanKeID: this.curZhuanKeID,
      yuanQuID: '0'
    }
    await this.initLcljData()
  },
  methods: {
    //查询
    async loadData() {
      const res = await getLcljJiXiaoBB({
        nianFen: this.fromList.year,
        yueFen: this.fromList.month,
        zhuanKeID: this.fromList.zhuanKeID
      })
      if (res.hasError === 0) {
        this.listsData = res.data || []
      }
    },
    //初始化
    async initLcljData() {
      const res = await initLcljJiXiaoBB()
      if (res.hasError == 0) {
        this.nianFenList = res.data.nianFenList.reverse()
        this.yueFenList = res.data.yueFenList.reverse()
        this.zhuanKeList = res.data.zhuanKeList
      }
    },
    //导出
    exportToExcel() {
      if (this.listsData.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const arr = this.listsData.map((item) => {
        let d = {}
        this.tableData.forEach((col) => {
          d[col.label] = item[col.value]
        })
        return d
      })
      const worksheet = XLSX.utils.json_to_sheet(arr)
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '临床路径绩效报表.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    },
    //打印
    exportToPrint() {
      window.print()
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  padding-bottom: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .input_txt {
    width: 235px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
  .header-item-radio {
    margin-right: 8px;
  }
  .item_date {
    width: 100px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.table {
  width: 550px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
.page-print {
  display: none;
}
@media print {
  .no-print {
    display: none;
  } /* 隐藏不需要打印的元素 */
  body * {
    visibility: hidden;
  } /* 隐藏页面上所有元素 */
  #section-to-print,
  .page-print * {
    visibility: visible;
  } /* 显示要打印的元素 */
}
</style>
