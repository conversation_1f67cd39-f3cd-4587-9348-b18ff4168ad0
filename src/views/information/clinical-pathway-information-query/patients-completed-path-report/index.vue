<template>
  <div class="container">
    <div class="header no-print">
      <div>
        <div class="header-item">
          <div class="header-item-title">选择年:</div>
          <div class="header-item-radio">
            <el-select v-model="fromList.year" class="item_date" autofocus filterable>
              <el-option
                v-for="item in nianFenList"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.daiMa.toString()"
              />
            </el-select>
          </div>
          <div class="header-item-title">选择月:</div>
          <div class="header-item-radio">
            <el-select v-model="fromList.month" class="item_date" autofocus filterable>
              <el-option
                v-for="item in yueFenList"
                :key="item.daiMa"
                :label="item.mingCheng"
                :value="item.daiMa.toString()"
              />
            </el-select>
          </div>
          <div class="header-item-title">选择专科:</div>
          <div class="header-item-radio">
            <el-select v-model="fromList.zhuanKeID" placeholder="请选择专科" autofocus filterable>
              <el-option
                v-for="item in zhuanKeList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID.toString()"
              />
            </el-select>
          </div>
          <div class="header-item-button">
            <el-button @click="loadData">查询</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header no-print">
        <div class="title">已完成路径的患者报表</div>
      </div>
      <div class="table">
        <el-table max-height="648" stripe border :data="listsData">
          <el-table-column
            v-for="obj in tableData"
            :key="obj.value"
            :prop="obj.value"
            :width="obj.width"
            :label="obj.label"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import {
  initYiWanChengLjHzBB,
  getYiWanChengLjHzBB
} from '@/api/clinical-pathway-performance-report'
import { format } from 'date-fns'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      fromList: {
        year: '',
        month: '',
        zhuanKeID: ''
      },
      //年
      nianFenList: [],
      //月
      yueFenList: [],
      //专科
      listsData: [],
      tableData: [
        {
          label: '病案号',
          value: 'bingAnHao',
          width: '120'
        },
        {
          label: '病人姓名',
          value: 'bingRenXM',
          width: '120'
        },
        {
          label: '出院专科',
          value: 'zhuanKeMC',
          width: '120'
        },
        {
          label: '入院日期',
          value: 'ruYuanRQ',
          width: '150'
        },
        {
          label: '出院日期',
          value: 'chuYuanRQ',
          width: '150'
        },
        {
          label: '路径名称',
          value: 'luJingMC',
          width: '300'
        },
        {
          label: '入径医师',
          value: 'ruJingYSXM',
          width: '120'
        },
        {
          label: '监管医师',
          value: 'jianGuanYSXM',
          width: '120'
        },
        {
          label: '治疗组组长',
          value: 'zhiLiaoZuZZXM',
          width: '120'
        }
      ]
    }
  },
  computed: {
    ...mapState({
      curZhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  async mounted() {
    this.fromList = {
      year: format(new Date(), 'yyyy'),
      month: format(new Date(), 'MM'),
      zhuanKeID: this.curZhuanKeID
    }
    await this.initLcljData()
  },
  methods: {
    //查询
    async loadData() {
      const res = await getYiWanChengLjHzBB({
        nianFen: this.fromList.year,
        yueFen: this.fromList.month,
        zhuanKeID: this.fromList.zhuanKeID
      })
      if (res.hasError === 0) {
        this.listsData = res.data || []
      }
    },
    //初始化
    async initLcljData() {
      const res = await initYiWanChengLjHzBB()
      if (res.hasError == 0) {
        this.nianFenList = res.data.nianFenList.reverse()
        this.yueFenList = res.data.yueFenList.reverse()
        this.zhuanKeList = res.data.zhuanKeList
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  padding-bottom: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .input_txt {
    width: 235px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
  .header-item-radio {
    margin-right: 8px;
  }
  .item_date {
    width: 100px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.table {
  width: 1325px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
