<template>
  <div class="container">
    <div class="header">
      <div>
        <div style="display: flex; align-items: center">
          <div class="query-word">请选择查询条件：</div>
          <div>
            <el-radio-group v-model="formData1.type">
              <el-radio label="1">病案号</el-radio>
              <el-radio label="2">姓名</el-radio>
              <el-radio label="3">住院号</el-radio>
              <el-radio label="6">身份证号</el-radio>
            </el-radio-group>
          </div>
          <div style="margin-left: 5px">
            <el-input v-model="formData1.param"></el-input>
          </div>
          <div class="button">
            <el-button type="primary" @click="onQuery1">查询</el-button>
          </div>
        </div>
        <div style="display: flex; align-items: center; margin-top: 4px">
          <div class="query-word">请选择查询条件：</div>
          <div>
            <el-radio-group v-model="formData2.type">
              <el-radio label="4">入院诊断</el-radio>
              <el-radio label="5">出院诊断</el-radio>
            </el-radio-group>
          </div>
          <div style="margin-left: 5px">
            <el-input v-model="formData2.zhenDuanMC"></el-input>
          </div>
          <div style="margin-left: 5px">
            <el-date-picker
              v-model="formData2.date"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </div>
          <div class="button">
            <el-button type="primary" @click="onQuery2">查询</el-button>
          </div>
        </div>
        <div style="display: flex; align-items: center; margin-top: 4px">
          <div class="query-word">请选择查询条件：</div>
          <div>
            <el-select v-model="formData3.bingQuDM" filterable placeholder="选择病区">
              <el-option
                v-for="item in baseInfo"
                :key="item.buMenDM"
                :label="item.buMenMC"
                :value="item.buMenDM"
              ></el-option>
            </el-select>
          </div>
          <div style="margin-left: 5px">
            <el-radio-group v-model="formData3.type">
              <el-radio label="6">入院日期</el-radio>
              <el-radio label="7">出院日期</el-radio>
            </el-radio-group>
          </div>
          <div style="margin-left: 5px">
            <el-date-picker
              v-model="formData3.date"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            ></el-date-picker>
          </div>
          <div class="button">
            <el-button type="primary" @click="onQuery3">查询</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">病历信息</div>
      </div>
      <div style="flex: 1; min-height: 0">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="keShiMC" label="专科"></el-table-column>
          <el-table-column prop="empi" label="病案号"></el-table-column>
          <el-table-column label="姓名">
            <template #default="scope">
              <el-button type="text" size="mini" @click="handleClickPatient(scope.row)">
                {{ scope.row.xingMing }}
              </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
          <el-table-column prop="chuShengRQ" label="出生日期">
            <template #default="scope">
              {{ scope.row.chuShengRQ === null ? '' : scope.row.chuShengRQ.slice(0, 10) }}
            </template>
          </el-table-column>
          <el-table-column prop="bingQuDM" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
          <el-table-column prop="ruYuanRQ" label="入院日期">
            <template #default="scope">
              {{ scope.row.ruYuanRQ === null ? '' : scope.row.ruYuanRQ.slice(0, 10) }}
            </template>
          </el-table-column>
          <el-table-column prop="bingQuRYRQ" label="病区入院日期" width="100">
            <template #default="scope">
              {{ scope.row.bingQuRYRQ === null ? '' : scope.row.bingQuRYRQ.slice(0, 10) }}
            </template>
          </el-table-column>
          <el-table-column prop="chuYuanRQ" label="出院日期">
            <template #default="scope">
              {{ scope.row.chuYuanRQ === null ? '' : scope.row.chuYuanRQ.slice(0, 10) }}
            </template>
          </el-table-column>
          <el-table-column prop="ruYuanZD" label="入院诊断"></el-table-column>
          <!--          <el-table-column prop="asd" label="化验单1"></el-table-column>-->
          <el-table-column prop="fuYinZT" label="复印状态"></el-table-column>
          <el-table-column label="病历状态">
            <template #default="scope">
              <span v-if="scope.row.zhuangTaiBZ === '1'">已封存</span>
              <span v-else>可书写</span>
            </template>
          </el-table-column>
          <el-table-column label="电子归档状态" width="100">
            <template #default="scope">
              <el-tag v-if="scope.row.guiDangBZ === '1'" type="success">已归档</el-tag>
              <el-tag v-else>未归档</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="guiDangYS" label="归档医生"></el-table-column>
          <el-table-column prop="yiShiGDRQ" label="医生归档日期" width="100">
            <template #default="scope">
              {{ scope.row.yiShiGDRQ === null ? '' : scope.row.yiShiGDRQ.slice(0, 10) }}
            </template>
          </el-table-column>
          <el-table-column prop="guiDangHS" label="护士归档"></el-table-column>
          <el-table-column prop="huShiGDRQ" label="护士归档日期" width="100">
            <template #default="scope">
              {{ scope.row.huShiGDRQ === null ? '' : scope.row.huShiGDRQ.slice(0, 10) }}
            </template>
          </el-table-column>
          <el-table-column prop="bingLiID" label="病历ID"></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import {
  getAllWardList,
  getBinRenXXByParam,
  getBinRenXXByTime,
  getBinRenXXByZD
} from '@/api/information'
import { format } from 'date-fns'

export default {
  name: 'VerifyCheckOrdersStatus',
  data() {
    return {
      formData1: {
        type: '1',
        param: ''
      },
      formData2: {
        type: '4',
        zhenDuanMC: '',
        date: ''
      },
      formData3: {
        bingQuDM: '',
        type: '6',
        date: ''
      },
      tableData: [],
      baseInfo: []
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      console.log('初始化')
      this.formData1['yongHuID'] = this.patientInfo.yiShiYHID
      this.formData2['yongHuID'] = this.patientInfo.yiShiYHID
      this.formData3['yongHuID'] = this.patientInfo.yiShiYHID
      const now = new Date()
      const date1 = new Date().setMonth(new Date().getMonth() - 1)
      const date2 = new Date().setMonth(new Date().getMonth() - 6)
      const nowStr = format(now, 'yyyy-MM-dd')
      const date1Str = format(date1, 'yyyy-MM-dd')
      const date2Str = format(date2, 'yyyy-MM-dd')
      this.formData2['date'] = [date2Str, nowStr]
      this.formData3['date'] = [date1Str, nowStr]
      const res = await getAllWardList({ yiLiaoJGDM: '01' })
      this.baseInfo = res.data
    },
    msg(res) {
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
        return false
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        return true
      }
    },
    async onQuery1() {
      const keyList = ['param']
      const formData = this.formData1
      if (this.errMsg(keyList, formData)) {
        return
      }
      const res = await getBinRenXXByParam(formData)
      console.log(res)
      if (this.msg(res)) {
        this.tableData = res.data
      }
    },
    async onQuery2() {
      const keyList = ['zhenDuanMC', 'date']
      const formData = { ...this.formData2 }
      if (this.errMsg(keyList, formData)) {
        return
      }
      formData['kaiShiSJ'] = formData.date[0] + ' 00:00:00'
      formData['jieShuSJ'] = formData.date[1] + ' 00:00:00'
      delete formData.date
      const res = await getBinRenXXByZD(formData)
      console.log(res)
      if (this.msg(res)) {
        this.tableData = res.data
      }
    },
    async onQuery3() {
      // const keyList = ['bingQuDM', 'date']
      const formData = { ...this.formData3 }
      // if (this.errMsg(keyList, formData)) {
      //   return
      // }
      formData['kaiShiSJ'] = formData.date[0] + ' 00:00:00'
      formData['jieShuSJ'] = formData.date[1] + ' 00:00:00'
      delete formData.date
      console.log(formData)
      const res = await getBinRenXXByTime(formData)
      console.log(res)
      if (this.msg(res)) {
        this.tableData = res.data
      }
    },
    handleClickPatient(patient) {
      console.log(patient)
      this.$router.push({
        path: `/patient-detail/${patient.bingLiID}`,
        query: {
          title: `${patient.jieSuanLXMC} ${this.formatChuangWeiHao(
            patient.bingQuDM,
            patient.chuangWeiHao
          )} ${patient.xingMing}`
        }
      })
    },
    formatChuangWeiHao(bingQuMC, chuangWeiHao) {
      return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
    },
    errMsg(keyList, formData) {
      for (const key of keyList) {
        if (formData[key].length <= 0) {
          this.$message.error('请输入查询内容！')
          return true
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
