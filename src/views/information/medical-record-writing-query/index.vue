<template>
  <div class="container">
    <!--医生选择-->
    <doctor-dialog :visible.sync="doctorVisible" @confirm="confirmDoctor"></doctor-dialog>
    <div class="header no-print">
      <div>
        <div class="header-item">
          <div class="header-item-title">选择医生:</div>
          <div class="header-item-radio" @click="doctorVisible = true">
            <el-input v-model="yiShengList.yiShengMC" readonly></el-input>
          </div>
          <div class="header-item-title">选择日期:</div>
          <div class="header-item-date">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="default"
              value-format="yyyy-MM-dd HH:mm:ss"
            />
          </div>
          <div class="header-item-button">
            <el-button @click="loadData">查询</el-button>
          </div>
          <div class="header-item-button">
            <el-button style="background-color: #356ac5" @click="exportToExcel">导出</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header no-print">
        <div class="title">查询医生书写的病历</div>
      </div>
      <div class="table">
        <el-table max-height="648" stripe border :data="listsData">
          <el-table-column
            v-for="obj in tableData"
            :key="obj.value"
            :prop="obj.value"
            :width="obj.width"
            :label="obj.label"
            :align="obj.align"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { getShuXieBingLi } from '@/api/information'
import DoctorDialog from '@/components/Dialog/DoctorDialog.vue'
import { format } from 'date-fns'
import { mapState } from 'vuex'
import * as XLSX from 'xlsx'
export default {
  components: {
    DoctorDialog
  },
  data() {
    return {
      doctorVisible: false,
      yiShengList: {
        yiShengID: '',
        yiShengMC: ''
      },
      dateRange: [],
      selectDcLists: [],
      //专科
      zhuanKeList: [],
      listsData: [],
      tableData: [
        {
          label: '病案号',
          value: 'bingAnHao',
          width: '120',
          align: 'left'
        },
        {
          label: '姓名',
          value: 'xingMing',
          width: '100'
        },
        {
          label: '性别',
          value: 'xingBie',
          width: '100'
        },
        {
          label: '出生日期',
          value: 'chuShengRQ',
          width: '150'
        },
        {
          label: '专科',
          value: 'keShiMC',
          width: '100'
        },
        {
          label: '病区',
          value: 'bingQuDM',
          width: '100'
        },
        {
          label: '床位号',
          value: 'chuangWeiHao',
          width: '100'
        },
        {
          label: '入院日期',
          value: 'ruYuanRQ',
          width: '150'
        },
        {
          label: '出院日期',
          value: 'chuYuanRQ',
          width: '150'
        },
        {
          label: '住院天数',
          value: 'zhuYuanTS',
          width: '100'
        },
        {
          label: '是否日间',
          value: 'riJianSS',
          width: '100'
        }
      ]
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo
    })
  },
  methods: {
    //查询
    async loadData() {
      console.log(this.dateRange)
      const start = this.dateRange[0]
      const end = this.dateRange[1]
      const res = await getShuXieBingLi({
        kaiShiSJ: start,
        jieShuSJ: end,
        caoZuoZID: this.yiShengList.yiShengID
      })
      if (res.hasError === 0) {
        this.listsData = res.data
      }
    },
    confirmDoctor(obj) {
      if (obj) {
        this.yiShengList.yiShengID = obj.yongHuID
        this.yiShengList.yiShengMC = obj.xingMing
      }
    },
    exportToExcel() {
      if (this.listsData.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const arr = this.listsData.map((item) => {
        let d = {}
        this.tableData.forEach((col) => {
          d[col.label] = item[col.value]
        })
        return d
      })
      const worksheet = XLSX.utils.json_to_sheet(arr)
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '查询医生书写的病历.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  padding-bottom: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .input_txt {
    width: 235px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
  .header-item-radio {
    margin-right: 8px;
  }
  .item_date {
    width: 100px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.table {
  width: 1275px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
