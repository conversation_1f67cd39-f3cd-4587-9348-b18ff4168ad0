<template>
  <div class="container">
    <div class="header">
      <div style="display: flex; align-items: center">
        <div class="query-word">查询条件：</div>
        <div class="query-value">
          <el-select v-model="ruleForm.chaXunLB" style="width: 120px">
            <el-option label="所有" value="ALL"></el-option>
            <el-option label="病案号" value="BAH"></el-option>
            <el-option label="姓名" value="BRXM"></el-option>
            <el-option label="住院号" value="ZYH"></el-option>
            <el-option label="临床诊断" value="LCZD"></el-option>
          </el-select>
        </div>
        <div class="query-value" style="margin-left: -5px">
          <el-input v-model="ruleForm.chaXunZhi"></el-input>
        </div>
        <div class="query-word">日期选择：</div>
        <div class="query-value">
          <el-radio-group v-model="ruleForm.shiJianLX">
            <el-radio label="1">配液日期</el-radio>
            <el-radio label="2">医嘱日期</el-radio>
          </el-radio-group>
        </div>
        <div class="query-value">
          <el-date-picker
            v-model="ruleForm.date"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </div>
        <div class="button">
          <el-button type="primary" @click="query">查询</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">营养医嘱查询</div>
      </div>
      <div class="content-table">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="yiZhuSJ" label="医嘱日期">
            <template #default="scope">
              {{ scope.row.yiZhuSJ === null ? '' : scope.row.yiZhuSJ.slice(0, 10) }}
            </template>
          </el-table-column>
          <el-table-column prop="bingRenXM" label="姓名"></el-table-column>
          <el-table-column prop="bingRenXB" label="性别">
            <template #default="scope">
              {{ scope.row.bingRenXB === '1' ? '男' : '女' }}
            </template>
          </el-table-column>
          <el-table-column prop="nianLing" label="年龄"></el-table-column>
          <el-table-column prop="shenGao" label="身高"></el-table-column>
          <el-table-column prop="tiZhong" label="体重"></el-table-column>
          <el-table-column prop="bingAnHao" label="住院号"></el-table-column>
          <el-table-column label="病区-床位号">
            <template #default="scope">
              {{ scope.row.bingQuMC + '-' + scope.row.chuangWeiHao + '床' }}
            </template>
          </el-table-column>
          <el-table-column prop="linChuangZD" label="临床诊断"></el-table-column>
          <el-table-column prop="yiZhuYSYHXM" label="医师姓名"></el-table-column>
          <el-table-column prop="zongYeTL" label="总液体量"></el-table-column>
          <el-table-column prop="shenTouYa" label="渗透压"></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                查看
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'
import { getGuiDangBBCX } from '@/api/medical-quality'
import { getYyyzdListByCondition, getZlzCybr } from '@/api/information'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {},
  data() {
    return {
      ruleForm: {
        date: [],
        chaXunLB: 'ALL', //查询类别 ZYH/BRXM/LCZD
        chaXunZhi: '', //查询值
        jieShuSJ: '', //结束时间
        kaiShiSJ: '', //开始时间
        shiJianLX: '1' //时间类型  1-配液日期/2-医嘱日期
      },
      tableData: [],
      // baseInfo: {},
      total: 0,
      pageIndex: 1,
      pageSize: 10
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const now = new Date()
      const date1 = new Date().setMonth(new Date().getMonth() - 1)
      const nowStr = format(now, 'yyyy-MM-dd')
      const date1Str = format(date1, 'yyyy-MM-dd')
      this.ruleForm['date'] = [date1Str, nowStr]
    },
    async setPage(pageIndex) {
      this.pageIndex = pageIndex
      await this.query()
    },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      reqData['curr_page'] = this.pageIndex
      reqData['count'] = this.pageSize
      if (reqData.date) {
        reqData['kaiShiSJ'] = reqData.date[0] + ' 00:00:00'
        let jieShuSJ = new Date(reqData.date[1])
        jieShuSJ.setDate(jieShuSJ.getDate() + 1)
        reqData['jieShuSJ'] = format(jieShuSJ, 'yyyy-MM-dd') + ' 00:00:00'
      }
      delete reqData.date
      console.log(reqData)
      const res = await getYyyzdListByCondition(reqData)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        this.tableData = res.data
        // this.total = this.tableData.length
      }
    },
    view(index, row) {
      let url = 'http://************'
      const params = {
        as_blid: row.bingLiID,
        as_yzdid: row.id,
        tempid: Math.random()
      }
      // 将参数转换为URL查询字符串
      const queryString = Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join('&')
      // 返回完整URL
      url = `${url}/ehr/xypyz/nutriOrder_prt.aspx?${queryString}`
      window.open(url, '_blank', 'width=700,height=600,left=100,top=100')
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 200px;
    min-width: 200px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

.red-star {
  color: red;
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
