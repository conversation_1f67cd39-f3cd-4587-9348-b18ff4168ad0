<template>
  <div class="container">
    <div class="header">
      <div style="display: flex; align-items: center">
        <div class="query-word">医嘱名称：</div>
        <div class="query-value">
          <el-input
            v-model="ruleForm.mingChengs"
            clearable
            style="width: 200px"
            @focus="showYZTC"
          ></el-input>
        </div>
        <div class="query-word">查询条件：</div>
        <div class="query-value">
          <el-select v-model="ruleForm.leiBie" style="width: 120px">
            <el-option label="病案号" value="1"></el-option>
            <el-option label="住院号" value="2"></el-option>
            <el-option label="日期" value="3"></el-option>
          </el-select>
        </div>
        <div class="query-value" style="margin-left: -5px">
          <el-input v-if="ruleForm.leiBie === '1'" v-model="ruleForm.bingAnHao"></el-input>
          <el-input v-if="ruleForm.leiBie === '2'" v-model="ruleForm.zhuYuanHao"></el-input>
          <el-date-picker
            v-if="ruleForm.leiBie === '3'"
            v-model="ruleForm.date"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </div>
        <div class="button">
          <el-button type="primary" @click="query">查询</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">治疗医嘱查询</div>
      </div>
      <div class="content-table">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="" label="病案号"></el-table-column>
          <el-table-column prop="" label="床位"></el-table-column>
          <el-table-column prop="bingRenXM" label="病人姓名"></el-table-column>
          <el-table-column prop="yiZhuMC" label="医嘱名称"></el-table-column>
          <el-table-column prop="yiZhuSL" label="数量"></el-table-column>
          <el-table-column prop="zhiXingPL" label="频率"></el-table-column>
          <el-table-column prop="kaiShiSJ" label="开始时间"></el-table-column>
          <el-table-column label="操作">
            <template #default="scope">
              <el-button type="text" size="mini" @click="view(scope.$index, scope.row)">
                明细
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog width="25%" :visible.sync="kangJunYWDialog">
      <div slot="title" class="title">选择医嘱名称</div>
      <div style="display: flex; margin-bottom: 10px">
        <div style="align-content: center">请输入医嘱拼音：</div>
        <div style="flex: 1">
          <el-input v-model="chaxunPY" @change="getYiZhuList"></el-input>
        </div>
      </div>
      <el-input v-model="ruleForm.mingChengs" clearable style="margin-bottom: 10px"></el-input>
      <el-table
        :data="currentPageYaoWuList"
        border
        size="medium"
        style="width: 100%"
        @row-dblclick="selectYaoWu"
      >
        <el-table-column prop="yiZhuMC" label="医嘱名称"></el-table-column>
      </el-table>
      <el-pagination
        class="pagination"
        small
        background
        :current-page.sync="currentYaoWuPage"
        :page-size="pageSize"
        layout="total, prev, pager, next"
        :total="yaoWuList.length"
      ></el-pagination>
      <span slot="footer" class="dialog-footer">
        <el-button size="mini" @click="kangJunYWDialog = false">关闭</el-button>
        <el-button size="mini" type="primary" @click="kangJunYWDialog = false">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'
import { getGuiDangBBCX } from '@/api/medical-quality'
import {
  getYyyzdListByCondition,
  getYzmlByYzlbdmLikeRight,
  getZhiLiaoYzByBahCmclsfcx,
  getZhiLiaoYzByRqCmclsfcx,
  getZlzCybr
} from '@/api/information'
import { searchKangJunYP } from '@/api/consultation-form-manage'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {},
  data() {
    return {
      ruleForm: {
        biaoShiHao: '', //病案号或住院号
        bingAnHao: '', //病案号
        zhuYuanHao: '', //住院号
        leiBie: '1', //类别
        mingChengs: '', //医嘱名称列表(以逗号“,”隔开)
        date: [],
        jieShuSJ: '', //结束时间
        kaiShiSJ: '' //开始时间
      },
      tableData: [],
      // baseInfo: {},
      // total: 0,
      // pageIndex: 1,
      // pageSize: 10,

      chaxunPY: '',
      yiZhuList: [],
      yaoWuList: [],
      kangJunYWDialog: false,
      currentPage: 1, //弹出框 当前页号
      currentYaoWuPage: 1,
      pageSize: 13 //弹出框 页面行数
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    },
    currentPageYaoWuList() {
      return this.yaoWuList.slice(
        (this.currentYaoWuPage - 1) * this.pageSize,
        (this.currentYaoWuPage - 1) * this.pageSize + this.pageSize
      )
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const now = new Date()
      const date1 = new Date().setMonth(new Date().getMonth() - 1)
      const nowStr = format(now, 'yyyy-MM-dd')
      const date1Str = format(date1, 'yyyy-MM-dd')
      this.ruleForm['date'] = [date1Str, nowStr]
      await this.initKangJunYP()
    },
    // async setPage(pageIndex) {
    //   this.pageIndex = pageIndex
    //   await this.query()
    // },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      if (reqData.date) {
        reqData['kaiShiSJ'] = reqData.date[0]
        reqData['jieShuSJ'] = reqData.date[1]
        // reqData['kaiShiSJ'] = reqData.date[0] + ' 00:00:00'
        // let jieShuSJ = new Date(reqData.date[1])
        // jieShuSJ.setDate(jieShuSJ.getDate() + 1)
        // reqData['jieShuSJ'] = format(jieShuSJ, 'yyyy-MM-dd') + ' 00:00:00'
      }
      delete reqData.date
      if (reqData['leiBie'] === '1') {
        reqData['biaoShiHao'] = reqData['bingAnHao']
      } else {
        reqData['biaoShiHao'] = reqData['zhuYuanHao']
      }
      let res = {}
      if (reqData['leiBie'] === '3') {
        res = await getZhiLiaoYzByRqCmclsfcx(reqData)
      } else {
        res = await getZhiLiaoYzByBahCmclsfcx(reqData)
      }
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
        this.tableData = res.data
        // this.total = this.tableData.length
      }
    },
    async initKangJunYP() {
      let res = await getYzmlByYzlbdmLikeRight({
        yiZhuLBDM: '08'
      })
      this.yiZhuList = res.data
      this.yaoWuList = JSON.parse(JSON.stringify(this.yiZhuList))
    },
    getYiZhuList(value) {
      let data = []
      for (const d of this.yiZhuList) {
        if (d['pinYin'].includes(value.toUpperCase())) {
          data.push(d)
        }
      }
      this.yaoWuList = data
    },
    showYZTC() {
      this.kangJunYWDialog = true
    },
    selectYaoWu(item, column, event) {
      if (this.ruleForm.mingChengs.includes(item.yiZhuMC)) {
        console.log('已经存在')
        return
      }
      if (this.ruleForm.mingChengs.length === 0) {
        this.ruleForm.mingChengs += item.yiZhuMC
      } else {
        this.ruleForm.mingChengs += ',' + item.yiZhuMC
      }
      // this.kangJunYWDialog = false
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 200px;
    min-width: 200px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

.red-star {
  color: red;
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
