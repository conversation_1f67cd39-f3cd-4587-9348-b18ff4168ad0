<template>
  <div class="container">
    <div class="header">
      <div>
        <div class="header-item">
          <div class="header-item-title">选择专科:</div>
          <div class="header-item-radio">
            <el-select v-model="zhuanKeID" placeholder="请选择专科" autofocus filterable>
              <el-option
                v-for="item in zhuanKeList"
                :key="item.buMenID"
                :label="item.buMenMC"
                :value="item.buMenID.toString()"
              />
            </el-select>
          </div>
          <div class="header-item-title">选择日期范围:</div>
          <div class="header-item-date">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="default"
            />
          </div>
          <div class="header-item-button">
            <el-button @click="loadData('date')">查询</el-button>
          </div>
          <div class="header-item-button">
            <el-button style="background-color: #356ac5" @click="exportToExcel">导出</el-button>
          </div>
        </div>

        <div class="header-item">
          <div class="header-item-title">&nbsp;&nbsp;&nbsp;&nbsp;病案号:</div>
          <div class="header-item-input">
            <el-input v-model="bingAnHao" class="input_txt" />
          </div>
          <div class="header-item-button"><el-button @click="loadData('bah')">查询</el-button></div>
          <div class="header-item-button">
            <el-button style="background-color: #356ac5" @click="exportToExcel">导出</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">微生物无样可采情况查询</div>
      </div>
      <div class="table">
        <el-table max-height="648" stripe border :data="listsData">
          <el-table-column
            v-for="obj in tableData"
            :key="obj.value"
            :prop="obj.value"
            :width="obj.width"
            :label="obj.label"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { getZhuanKeList, getWeiShengWuWykclyByBah } from '@/api/user'
import { getWeiShengWuWykclyByZk } from '@/api/information'
import { format } from 'date-fns'
import * as XLSX from 'xlsx'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      radio1: '1',
      dateRange: [],
      bingAnHao: '',
      zhuanKeID: '',
      zhuanKeList: [],
      listsData: [],
      tableData: [
        {
          label: '科室*',
          value: 'zhuanKeMC',
          width: '100'
        },
        {
          label: '病区*',
          value: 'bingQuZDYM',
          width: '120'
        },
        {
          label: '病案号',
          value: 'bingAnHao',
          width: '100'
        },
        {
          label: '姓名',
          value: 'bingRenXM',
          width: '90'
        },
        {
          label: '年龄',
          value: 'nianLing',
          width: '100'
        },
        {
          label: '医生',
          value: 'yiShengXM',
          width: '100'
        },
        {
          label: '药品名称',
          value: 'yaoPinMC',
          width: '300'
        },
        {
          label: '感染诊断',
          value: 'ganRanZD',
          width: '300'
        },
        {
          label: '无样可采理由',
          value: 'wuYangKCLY',
          width: '300'
        },
        {
          label: '日期',
          value: 'xiuGaiSJ',
          width: '150'
        }
      ]
    }
  },
  async mounted() {
    await this.showZhuanKeList()
  },
  methods: {
    async loadData(type) {
      console.log(this.dateRange)
      switch (type) {
        case 'date':
          if (this.dateRange.length > 0) {
            const start = format(this.dateRange[0], 'yyyy-MM-dd HH:mm:ss')
            const end = format(this.dateRange[1], 'yyyy-MM-dd HH:mm:ss')
            const res = await getWeiShengWuWykclyByZk({
              jieShuSJ: end,
              kaiShiSJ: start,
              zhuanKeID: this.zhuanKeID
            })
            if (res.hasError === 0) {
              this.listsData = res.data || []
            }
          } else {
            this.$message.error('选择日期不可为空！')
          }
          break
        case 'bah':
          if (this.bingAnHao) {
            const res = await getWeiShengWuWykclyByBah({
              zhuanKeID: this.bingAnHao
            })
            if (res.hasError === 0) {
              this.listsData = res.data || []
            }
          } else {
            this.$message.error('选择病案号不可为空！')
          }
          break
      }
    },
    showZhuanKeList() {
      getZhuanKeList().then((res) => {
        if (res.hasError !== 0) {
          return
        }
        this.zhuanKeList = [{ buMenID: '', buMenMC: '全院' }, ...res.data]
      })
    },
    exportToExcel() {
      if (this.listsData.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const arr = this.listsData.map((item) => {
        let d = {}
        this.tableData.forEach((col) => {
          d[col.label] = item[col.value]
        })
        return d
      })
      const worksheet = XLSX.utils.json_to_sheet(arr)
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '微生物无样可采情况查询.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  padding-bottom: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .input_txt {
    width: 235px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
  .header-item-radio {
    margin-right: 8px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.table {
  width: 1663px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
