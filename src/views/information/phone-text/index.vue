<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">语音录入文本</div>
      </div>
      <div class="table">
        <el-table width="600" max-height="648" stripe border :data="listsData">
          <el-table-column prop="content" width="400" label="内容"></el-table-column>
          <el-table-column prop="type" width="90" label="文件类型"></el-table-column>
          <el-table-column width="100" label="操作">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">
                {{ scope.row.type === '' || scope.row.type === 'txt' ? '复制' : '下载' }}
              </el-button>
              <el-button type="text" size="small" @click="deleteData(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { getShouJiLR, deleteShouJiXXByID } from '@/api/information'
export default {
  data() {
    return {
      listsData: []
    }
  },
  computed: {
    ...mapState({
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  async mounted() {
    await this.loadData()
  },
  methods: {
    //初始化
    async loadData() {
      const res = await getShouJiLR({ yongHuID: this.yongHuID })
      if (res.hasError === 0) {
        this.listsData = res.data || []
      }
    },
    handleClick(row) {
      switch (row.type) {
        case '':
        case 'txt':
          this.copyTxt(row.content)
          break
        case 'jpg':
        case 'jpeg':
        case 'png':
          if (row.filedata) {
            this.getBase(row.filedata, row.recordid)
          } else {
            this.$message.error('无附件！')
          }
          break
      }
    },
    //复制
    async copyTxt(text) {
      try {
        await navigator.clipboard.writeText(text)
        this.$message.success('复制成功！')
      } catch (error) {
        console.log(error)
      }
    },
    //图片下载
    getBase(base64String, name) {
      const byteCharacters = atob(base64String)
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      const blob = new Blob([byteArray], { type: 'image/jpeg' })

      // 创建一个临时的 a 标签来触发下载
      const link = document.createElement('a')
      link.href = URL.createObjectURL(blob)
      link.download = name + '.jpg' // 设置下载的文件名
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link) // 清理
    },
    //删除
    async deleteData(row) {
      const res = await deleteShouJiXXByID({ recordID: row.recordID })
      if (res.hasError === 0) {
        this.listsData = res.data || []
        this.$message.success('删除成功！')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.table {
  width: 600px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
  width: 400px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
