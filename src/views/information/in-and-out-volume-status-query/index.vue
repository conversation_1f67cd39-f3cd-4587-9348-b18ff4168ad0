<template>
  <div class="container">
    <div class="header">
      <div style="display: flex; align-items: center">
        <div class="query-word">截止时间点：</div>
        <div class="query-value">
          <el-date-picker
            v-model="ruleForm.jieZhiSJ"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="选择日期"
          ></el-date-picker>
        </div>
        <div class="query-word">治疗组：</div>
        <div class="query-value">
          <el-select v-model="ruleForm.zhiLiaoZuID">
            <el-option
              v-for="item in baseInfo"
              :key="item.zhiLiaoZuID"
              :label="item.zhiLiaoZuMC"
              :value="item.zhiLiaoZuID"
            ></el-option>
          </el-select>
        </div>
        <div class="button">
          <el-button type="primary" @click="query">查询</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">出入量情况查询表</div>
      </div>
      <div class="content-table">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="zhiLiaoZuMC" label="治疗组" width="140"></el-table-column>
          <el-table-column label="床位号" width="120">
            <template #default="scope">
              {{ scope.row.bingQuMC + '-' + scope.row.chuangWeiHao }}
            </template>
          </el-table-column>
          <el-table-column prop="bingAnHao" label="病案号" width="120"></el-table-column>
          <el-table-column prop="bingRenXM" label="姓名"></el-table-column>
          <el-table-column prop="xingBie" label="性别">
            <template #default="scope">
              {{ scope.row.xingBie === '1' ? '男' : '女' }}
            </template>
          </el-table-column>
          <el-table-column prop="nianLing" label="年龄"></el-table-column>
          <el-table-column prop="tiZhong" label="体重"></el-table-column>
          <el-table-column prop="fluidBalanceTarget" label="液体平衡目标"></el-table-column>
          <el-table-column label="近8小时" align="center">
            <el-table-column prop="zongRuLiang_8h" label="入量"></el-table-column>
            <el-table-column prop="zongChuLiang_8h" label="出量"></el-table-column>
            <el-table-column prop="niaoLiang_8h" label="尿量"></el-table-column>
            <el-table-column prop="qiTaPCL_8h" label="其他出量"></el-table-column>
            <el-table-column prop="fluidBalance_8h" label="出入量平衡情况"></el-table-column>
          </el-table-column>
          <el-table-column label="近16小时" align="center">
            <el-table-column prop="zongRuLiang_16h" label="入量"></el-table-column>
            <el-table-column prop="zongChuLiang_16h" label="出量"></el-table-column>
            <el-table-column prop="niaoLiang_16h" label="尿量"></el-table-column>
            <el-table-column prop="qiTaPCL_16h" label="其他出量"></el-table-column>
            <el-table-column prop="fluidBalance_16h" label="出入量平衡情况"></el-table-column>
          </el-table-column>
          <el-table-column label="近24小时" align="center">
            <el-table-column prop="zongRuLiang_24h" label="入量"></el-table-column>
            <el-table-column prop="zongChuLiang_24h" label="出量"></el-table-column>
            <el-table-column prop="niaoLiang_24h" label="尿量"></el-table-column>
            <el-table-column prop="qiTaPCL_24h" label="其他出量"></el-table-column>
            <el-table-column prop="fluidBalance_24h" label="出入量平衡情况"></el-table-column>
          </el-table-column>
          <el-table-column label="近48小时" align="center">
            <el-table-column prop="zongRuLiang_48h" label="入量"></el-table-column>
            <el-table-column prop="zongChuLiang_48h" label="出量"></el-table-column>
            <el-table-column prop="niaoLiang_48h" label="尿量"></el-table-column>
            <el-table-column prop="qiTaPCL_48h" label="其他出量"></el-table-column>
            <el-table-column prop="fluidBalance_48h" label="出入量平衡情况"></el-table-column>
          </el-table-column>
          <el-table-column label="近72小时" align="center">
            <el-table-column prop="zongRuLiang_72h" label="入量"></el-table-column>
            <el-table-column prop="zongChuLiang_72h" label="出量"></el-table-column>
            <el-table-column prop="niaoLiang_72h" label="尿量"></el-table-column>
            <el-table-column prop="qiTaPCL_72h" label="其他出量"></el-table-column>
            <el-table-column prop="fluidBalance_72h" label="出入量平衡情况"></el-table-column>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getInPatientCRLStatisticList } from '@/api/information'
import { getZhiLiaoZuListByZhuanKeID } from '@/api/patient'
import { format } from 'date-fns'

export default {
  name: 'VerifyCheckOrdersStatus',
  components: {},
  data() {
    return {
      baseInfo: {},
      ruleForm: {
        jieZhiSJ: '',
        zhiLiaoZuID: '',
        zhuanKeID: ''
      },
      tableData: []
    }
  },
  computed: {
    ...mapState({
      patientDetail: ({ patient }) => patient.patientInit,
      patientInfo: ({ patient }) => patient.initInfo
    }),
    bingLiID() {
      return this.$route.params.id
    }
  },
  async mounted() {
    await this.init()
  },
  methods: {
    async init() {
      const now = new Date()
      this.ruleForm.jieZhiSJ = format(now, 'yyyy-MM-dd HH:mm:ss')
      this.ruleForm.zhuanKeID = this.patientInfo.zhuanKeID
      const res = await getZhiLiaoZuListByZhuanKeID({ zhuanKeID: this.patientInfo.zhuanKeID })
      this.baseInfo = res.data
      this.ruleForm.zhiLiaoZuID = this.baseInfo[0].zhiLiaoZuID
      this.baseInfo.push({ zhiLiaoZuID: 0, zhiLiaoZuMC: '全部治疗组' })
    },
    async query() {
      let reqData = JSON.parse(JSON.stringify(this.ruleForm))
      const res = await getInPatientCRLStatisticList(reqData)
      if (res.hasError === -1) {
        this.$message.error(res.errorMessage)
      } else {
        this.$message({
          message: res.errorMessage,
          type: 'success'
        })
      }
      this.tableData = res.data
    }
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  .dz {
    ::v-deep .el-select {
      width: 160px;
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  ::v-deep .el-tabs__nav {
    background: #ffffff;

    .is-active {
      background: #eaf0f9;
      border-bottom: none;
    }
  }

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  .content-table {
    flex: 1;
    min-height: 0;
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 200px;
    min-width: 200px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

.red-star {
  color: red;
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}

::v-deep .el-scrollbar {
  max-width: 1400px !important;
}

::v-deep .el-tabs {
  display: flex;
  flex-direction: column;

  .el-tabs__content {
    flex: 1;
    min-height: 0;

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
}
</style>
