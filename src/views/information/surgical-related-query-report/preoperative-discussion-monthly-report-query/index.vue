<template>
  <div class="container">
    <div class="title">需讨论手术月报表</div>
    <div class="header">
      <div>
        <span>时间范围：</span>
        <el-date-picker
          v-model="shiJianFW"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </div>

      <el-button @click="queryData">查询</el-button>
    </div>
    <div class="content">
      <el-table max-height="648" :data="tableData">
        <el-table-column prop="zhuanKeMC" label="专科"></el-table-column>
        <el-table-column prop="totalCount" label="需讨论例数"></el-table-column>
        <el-table-column prop="discussedCount" label="已讨论"></el-table-column>
        <el-table-column prop="undiscussedCount" label="未讨论"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getSurgeryDiscussionDept } from '@/api/information'
export default {
  data() {
    return {
      tableData: [],
      shiJianFW: []
    }
  },
  methods: {
    async queryData() {
      let res = await getSurgeryDiscussionDept({
        kaiShiSJ: this.shiJianFW[0],
        jieShuSJ: this.shiJianFW[1]
      })
      this.tableData = res.data
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.title {
  font-size: 20px;
  text-align: center;
  margin: 10px 0;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}

::v-deep .year {
  .el-select {
    width: 92px;
  }
}
::v-deep .month {
  margin-left: 20px;
  .el-select {
    width: 92px;
  }
}
</style>
