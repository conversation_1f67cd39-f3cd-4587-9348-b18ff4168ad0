<template>
  <div class="container">
    <div class="title">温州医科大学附属第一医院手术准入表</div>
    <div class="header">
      <div class="flex">
        <div class="flex-item">医师代码：{{ yiShengDM }}</div>
        <div class="flex-item">姓名：{{ xingMing }}</div>
        <div class="flex-item">
          性别：{{ xingBie === '1' ? '男' : xingBie === '0' ? '女' : '' }}
        </div>
        <div class="flex-item">等级：{{ gongZhongMC }}</div>
      </div>

      <div>打印时间：{{ currentTime }}</div>
    </div>

    <div class="content">
      <el-table max-height="648" :data="tableData">
        <el-table-column prop="手术代码" label="手术代码"></el-table-column>
        <el-table-column prop="手术名称" label="手术名称"></el-table-column>
        <el-table-column prop="状态标志" label="状态"></el-table-column>
        <el-table-column prop="SSLB" label="手术类别"></el-table-column>
        <el-table-column prop="管理级别" label="管理级别"></el-table-column>
        <el-table-column prop="ZTBZ_TEXT" label="备注"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getSurgeonSurgeryPermissionList, getDoctorDataByYongHuID } from '@/api/information'
import { mapState } from 'vuex'
import { format } from 'date-fns'
export default {
  data() {
    return {
      tableData: [],
      currentTime: '',
      gongZhongMC: ''
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      renYuanKuID: ({ patient }) => patient.doctorInfo.renYuanKuID,
      yiShengDM: ({ patient }) => patient.doctorInfo.yiShengDM,
      xingMing: ({ patient }) => patient.doctorInfo.xingMing,
      xingBie: ({ patient }) => patient.doctorInfo.xingBie,
      yongHuID: ({ user }) => user.yongHuID
    })
  },
  async mounted() {
    console.log(this.$store.state)
    this.currentTime = format(new Date(), 'yyyy-MM-dd HH:mm:ss')
    let res = await getDoctorDataByYongHuID({
      yongHuID: this.yongHuID
    })
    this.gongZhongMC = res.data.gongZhongMC
    await this.getSurgeonSurgeryPermissionList()
  },
  methods: {
    async getSurgeonSurgeryPermissionList() {
      let res = await getSurgeonSurgeryPermissionList({
        zhuanKeID: 19,
        renYuanKuID: 18810
      })
      this.tableData = res.data
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  height: 100%;
  .flex-item {
    margin-right: 45px;
  }
}
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.title {
  font-size: 20px;
  text-align: center;
  margin: 10px 0;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}

::v-deep .year {
  .el-select {
    width: 92px;
  }
}
::v-deep .month {
  margin-left: 20px;
  .el-select {
    width: 92px;
  }
}
</style>
