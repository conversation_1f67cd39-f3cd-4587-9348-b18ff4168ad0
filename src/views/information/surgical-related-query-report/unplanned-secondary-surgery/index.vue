<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <span>专科：</span>
        <el-select v-model="buMenDaiMa">
          <el-option
            v-for="item in buMenList"
            :key="item.daiMa"
            :label="item.mingCheng"
            :value="item.daiMa"
          ></el-option>
        </el-select>
      </div>
      <div class="header-item">
        <span>时间：</span>
        <el-date-picker
          v-model="shiJianFW"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
      </div>

      <div class="header-item">
        <span>相隔天数：</span>
        <el-select v-model="xiangGeTS">
          <el-option
            v-for="item in xiangGeTSList"
            :key="item"
            :label="item"
            :value="item"
          ></el-option>
        </el-select>
      </div>

      <el-button @click="queryData">查询</el-button>
      <el-button>导出Excel</el-button>
    </div>
    <div class="content">
      <el-table max-height="648" :data="tableData">
        <el-table-column type="index"></el-table-column>
        <el-table-column prop="medicalRecordNo" width="90" label="病案号"></el-table-column>
        <el-table-column prop="patientName" label="病人姓名"></el-table-column>
        <el-table-column prop="departmentName" label="所在科室"></el-table-column>
        <el-table-column prop="ruYuanZD" label="入院诊断"></el-table-column>
        <el-table-column prop="firstSurgeryName" label="第一次手术名称"></el-table-column>
        <el-table-column prop="firstSurgeryTime" label="第一次手术时间"></el-table-column>
        <el-table-column prop="secondSurgeryName" label="第二次手术名称"></el-table-column>
        <el-table-column prop="secondSurgeryTime" label="第二次手术时间"></el-table-column>
        <el-table-column prop="thirdSurgeryName" label="第三次手术名称"></el-table-column>
        <el-table-column prop="thirdSurgeryTime" label="第三次手术时间"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getUnplannedSecondarySurgery } from '@/api/information'
import { getZhuanKeList } from '@/api/medical-quality'
export default {
  data() {
    return {
      xiangGeTSList: [],
      xiangGeTS: 1,
      shiJianFW: [],
      tableData: [],
      buMenDaiMa: null,
      buMenList: []
    }
  },
  async mounted() {
    for (let i = 1; i < 32; i++) {
      this.xiangGeTSList.push(i)
    }
    await this.getZhuanKeList()
  },
  methods: {
    async queryData() {
      let res = await getUnplannedSecondarySurgery({
        departmentId: this.buMenDaiMa,
        startDate: this.shiJianFW[0],
        endDate: this.shiJianFW[1],
        intervalDays: this.xiangGeTS
      })
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    },
    async getZhuanKeList() {
      let res = await getZhuanKeList()
      if (res.hasError === 0) {
        this.buMenList = res.data
        this.buMenList.unshift({
          daiMa: '0',
          mingCheng: '所有专科'
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}

::v-deep .year {
  .el-select {
    width: 92px;
  }
}
::v-deep .month {
  margin-left: 20px;
  .el-select {
    width: 92px;
  }
}
</style>
