<template>
  <div class="container">
    <div class="title">温州医科大学附属第一医院专科手术一览表</div>
    <div class="header">
      <div>打印时间：{{ currentTime }}</div>

      <div>
        <el-button>打印</el-button>
      </div>
    </div>
    <div class="content">
      <el-table max-height="648" :data="tableData">
        <el-table-column prop="ZKID" label="专科代码"></el-table-column>
        <el-table-column prop="SSDM" label="手术名称"></el-table-column>
        <el-table-column prop="SSLB" label="手术类别"></el-table-column>
        <el-table-column prop="GLJB_TEXT" label="管理级别"></el-table-column>
        <el-table-column prop="ZTBZ" label="状态"></el-table-column>
        <el-table-column prop="ZTBZ_TEXT" label="备注"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { getDeptOperationListByZhuanKeID } from '@/api/information'
import { mapState } from 'vuex'
import { format } from 'date-fns'
export default {
  data() {
    return {
      tableData: [],
      currentTime: ''
    }
  },
  computed: {
    ...mapState({ zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID })
  },
  async mounted() {
    this.currentTime = format(new Date(), 'yyyy-MM-dd HH:mm:ss')
    await this.getDeptOperationList()
  },
  methods: {
    async getDeptOperationList() {
      let res = await getDeptOperationListByZhuanKeID({
        zhuanKeID: this.zhuanKeID
      })
      this.tableData = res.data
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.title {
  font-size: 20px;
  text-align: center;
  margin: 10px 0;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}

::v-deep .year {
  .el-select {
    width: 92px;
  }
}
::v-deep .month {
  margin-left: 20px;
  .el-select {
    width: 92px;
  }
}
</style>
