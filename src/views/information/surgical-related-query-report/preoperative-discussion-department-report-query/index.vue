<template>
  <div class="container">
    <div class="title">需讨论手术科室报表</div>
    <div class="header">
      <div>
        <span>时间范围：</span>
        <el-date-picker
          v-model="shiJianFW"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd HH:mm:ss"
        ></el-date-picker>
        <span style="margin-left: 10px">科室：</span>
        <el-select v-model="buMenDaiMa">
          <el-option
            v-for="item in buMenList"
            :key="item.daiMa"
            :label="item.mingCheng"
            :value="item.daiMa"
          ></el-option>
        </el-select>
      </div>
      <el-button @click="queryData">查询</el-button>
    </div>
    <div class="content">
      <el-table max-height="648" :data="tableData">
        <el-table-column prop="name" label="手术"></el-table-column>
        <el-table-column prop="totalCount" label="需讨论例数"></el-table-column>
        <el-table-column prop="discussedCount" label="已讨论"></el-table-column>
        <el-table-column prop="undiscussedCount" label="未讨论"></el-table-column>
      </el-table>
    </div>
  </div>
</template>
<script>
import { querySurgeryDiscussionByFullSql } from '@/api/information'
import { getZhuanKeList } from '@/api/medical-quality'
export default {
  data() {
    return {
      tableData: [],
      shiJianFW: [],
      buMenDaiMa: null,
      buMenList: []
    }
  },
  async mounted() {
    await this.getZhuanKeList()
  },
  methods: {
    async queryData() {
      let res = await querySurgeryDiscussionByFullSql({
        startTime: this.shiJianFW[0],
        endTime: this.shiJianFW[1],
        zhuanKeID: this.buMenDaiMa
      })
      this.tableData = res.data.items
    },
    async getZhuanKeList() {
      let res = await getZhuanKeList()
      if (res.hasError === 0) {
        this.buMenList = res.data
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.flex {
  display: flex;
  align-items: center;
  height: 100%;
}
.container {
  background-color: #fff;
  padding: 6px 70px;
  z-index: 999;
}
.title {
  font-size: 20px;
  text-align: center;
  margin: 10px 0;
}
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}

::v-deep .year {
  .el-select {
    width: 92px;
  }
}
::v-deep .month {
  margin-left: 20px;
  .el-select {
    width: 92px;
  }
}
</style>
