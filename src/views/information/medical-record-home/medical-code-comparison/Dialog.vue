<template>
  <div class="surgeries-class">
    <el-dialog
      class="list-class"
      :visible.sync="dialogVisible"
      :title="
        selectType == '3'
          ? '职业列表'
          : selectType == '6'
          ? '籍贯列表'
          : selectType == '7'
          ? '婚姻列表'
          : selectType == '8'
          ? '民族列表'
          : selectType == '9'
          ? '医生列表'
          : '科室列表'
      "
      width="620px"
      pop-type="tip"
      :close-on-click-modal="false"
    >
      <!-- 职业列表--籍贯列表--婚姻列表--民族列表--医生列表--科室列表 -->
      <div class="flex">
        <el-input
          v-model="listXmInput"
          class="listXMInput"
          :placeholder="
            selectType == '3'
              ? '请输入职业名称搜索'
              : selectType == '6'
              ? '请输入籍贯名称搜索'
              : selectType == '7'
              ? '请输入婚姻名称搜索'
              : selectType == '8'
              ? '请输入民族名称搜索'
              : selectType == '8'
              ? '请输入医生名称搜索'
              : '请输入科室名称搜索'
          "
          @input="listXmSearch"
        ></el-input>
      </div>
      <el-table :data="listList" border size="mini" max-height="480px">
        <!-- 职业 -->
        <template v-if="selectType == '3'">
          <el-table-column key="daiMa" prop="daiMa" label="HIS代码" width="230"></el-table-column>
          <el-table-column
            key="mingCheng"
            prop="mingCheng"
            label="职业名称"
            width="230"
          ></el-table-column>
        </template>
        <!-- 籍贯 -->
        <template v-if="selectType == '6'">
          <el-table-column key="daiMa" prop="daiMa" label="籍贯代码" width="150"></el-table-column>
          <el-table-column
            key="mingCheng"
            prop="mingCheng"
            label="籍贯名称"
            width="160"
          ></el-table-column>
          <el-table-column
            key="shuXing"
            prop="shuXing"
            label="病案对照"
            width="150"
          ></el-table-column>
        </template>
        <!-- 婚姻 -->
        <template v-if="selectType == '7'">
          <el-table-column key="daiMa" prop="daiMa" label="HIS代码" width="230"></el-table-column>
          <el-table-column
            key="mingCheng"
            prop="mingCheng"
            label="HIS代码名称"
            width="230"
          ></el-table-column>
        </template>
        <!-- 民族 -->
        <template v-if="selectType == '8'">
          <el-table-column key="daiMa" prop="daiMa" label="民族代码" width="230"></el-table-column>
          <el-table-column
            key="mingCheng"
            prop="mingCheng"
            label="民族名称"
            width="230"
          ></el-table-column>
        </template>
        <!-- 职工 -->
        <template v-if="selectType == '9'">
          <el-table-column
            key="xingMing"
            prop="xingMing"
            width="160"
            label="医生姓名"
          ></el-table-column>
          <el-table-column
            key="zhongShenDM"
            prop="zhongShenDM"
            width="150"
            label="终身代码"
          ></el-table-column>
          <el-table-column
            key="renYuanKuID"
            prop="renYuanKuID"
            width="150"
            label="HIS代码"
          ></el-table-column>
        </template>
        <!-- 科室 -->
        <template v-if="selectType == 'depart'">
          <el-table-column
            key="keShiMC"
            prop="keShiMC"
            width="460"
            label="科室名称"
          ></el-table-column>
        </template>
        <el-table-column label="操作" width="122" align="center">
          <template #default="scope">
            <el-button size="mini" @click="listXmSelect(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="listPagination.currentPage"
        :page-size="
          selectType == '3' || selectType == '7' || selectType == '8'
            ? listPagination.pageSize1
            : listPagination.pageSize2
        "
        background
        layout="total, prev, pager, next"
        :total="listLength"
        @current-change="listPageChange"
      ></el-pagination>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDuoZhiBiaoByDaiMaLB,
  getAreaList,
  getYlRykList,
  getBingAnKeShiDMByPage
} from '@/api/medical-record-api'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      dialogVisible: false, //弹框是否显示
      selectType: '3', //选择类型
      propsRow: {},
      listXmInput: '', //搜索输入框
      listPagination: {
        currentPage: 1,
        pageSize1: 80,
        pageSize2: 10
      }, //列表分页
      listList: [], //列表数据
      listLength: 0 //列表长度
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {},
  methods: {
    // 打开弹框搜索事件
    openDialog(value, props) {
      this.selectType = value
      this.propsRow = props
      this.dialogVisible = true
      this.listXmInput = ''
      this.listPagination.currentPage = 1
      if (this.selectType == '3') {
        this.getDuoZhiBiaoByDaiMaLB('3') //职业接口
      } else if (this.selectType == '6') {
        this.getAreaList() //籍贯接口
      } else if (this.selectType == '7') {
        this.getDuoZhiBiaoByDaiMaLB('7') //婚姻接口
      } else if (this.selectType == '8') {
        this.getDuoZhiBiaoByDaiMaLB('8') //民族接口
      } else if (this.selectType == '9') {
        this.getYlRykList() //职工接口
      } else if (this.selectType == 'depart') {
        this.getBingAnKeShiDMByPage() //科室接口
      }
    },

    // 获取其它列表查询接口
    async getDuoZhiBiaoByDaiMaLB(code) {
      try {
        const res = await getDuoZhiBiaoByDaiMaLB({
          lieBie: code
        })
        if (res.hasError === 0) {
          this.listList = res.data.sort((a, b) => a.daiMa - b.daiMa)
          this.listList1 = res.data.sort((a, b) => a.daiMa - b.daiMa)
          this.listLength = res.data.length
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取籍贯列表查询接口
    async getAreaList() {
      try {
        const res = await getAreaList({
          yeShu: this.listPagination.currentPage,
          shuLiang: 10,
          neiRong: this.listXmInput
        })
        if (res.hasError === 0) {
          this.listList = res.data.jiLuLB
          this.listLength = res.data.jiLuZS
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取职工列表查询接口
    async getYlRykList() {
      try {
        const res = await getYlRykList({
          pageIndex: this.listPagination.currentPage,
          pageSize: 10,
          guanJianZi: this.listXmInput,
          renYuanLB: '',
          // mode: '0拼音1五笔 空 只查名字',
          zhuanKeID: 0
        })
        if (res.hasError === 0) {
          this.listList = res.data
          this.listLength = res.extendData.total
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取科室列表查询接口
    async getBingAnKeShiDMByPage() {
      try {
        const res = await getBingAnKeShiDMByPage({
          keShiLB: '2',
          pageIndex: this.listPagination.currentPage,
          pageSize: 10,
          wenBen: this.listXmInput
        })
        if (res.hasError === 0) {
          console.log(res)

          this.listList = res.data
          this.listLength = res.extendData.total
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 查询列表分页处理
    listPageChange(page) {
      this.listPagination.currentPage = page
      if (this.selectType == '3') {
      } else if (this.selectType == '6') {
        this.getAreaList() //籍贯接口
      } else if (this.selectType == '7') {
      } else if (this.selectType == '8') {
      } else if (this.selectType == '9') {
        this.getYlRykList() //职工接口
      } else if (this.selectType == 'depart') {
        this.getBingAnKeShiDMByPage() //科室接口
      }
    },

    // 查询列表查询初始化
    async listXmSearch() {
      this.listPagination.currentPage = 1
      if (this.selectType == '3') {
        this.searchFun() //职业接口
      } else if (this.selectType == '6') {
        this.getAreaList() //籍贯接口
      } else if (this.selectType == '7') {
        this.searchFun('7') //婚姻接口
      } else if (this.selectType == '8') {
        this.searchFun('8') //民族接口
      } else if (this.selectType == '9') {
        this.getYlRykList() //职工接口
      } else if (this.selectType == 'depart') {
        this.getBingAnKeShiDMByPage() //科室接口
      }
    },

    // 查询列表关键词查询
    searchFun() {
      this.listList = this.listList1.filter((item) => {
        let isZhiLiaoZuMatch = true
        if (this.listXmInput != '') {
          isZhiLiaoZuMatch = item.mingCheng == this.listXmInput
        }
        return isZhiLiaoZuMatch
      })
      this.listLength = this.listList.length
    },

    // 查询列表选择某一项
    listXmSelect(row) {
      if (
        this.selectType == '3' ||
        this.selectType == '6' ||
        this.selectType == '7' ||
        this.selectType == '8'
      ) {
        this.propsRow.hisdmmc = row.mingCheng
        this.propsRow.hisdm = row.daiMa
      } else if (this.selectType == '9') {
        this.propsRow.hismc = row.xingMing
        this.propsRow.hisdm = row.renYuanKuID
        this.propsRow.zhongShenDM = row.zhongShenDM
      } else if (this.selectType == 'depart') {
        this.propsRow.keShiDM = row.keShiDM
        this.propsRow.keShiMC = row.keShiMC
      }
      this.$emit('save-success', this.propsRow)
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="scss" scoped>
// 列表弹框
.surgeries-class {
  ::v-deep .el-dialog {
    padding: 6px 10px;
  }

  ::v-deep .el-table--mini .el-table__cell {
    padding: 12px 0;
  }

  .dialog-footer {
    padding-bottom: 15px;
  }

  ::v-deep .el-dialog__header {
    // border-bottom: 1px solid #dadee6;
    padding: 10px 14px;
    font: var(--font-medium);
    font-size: var(--font-size-medium);
    color: #171c28;
    display: flex;
    align-items: center;
  }

  ::v-deep .el-dialog__header::before {
    // content: url('~@/assets/images/info.png');
    // // width: 3px;
    // // height: 16px;
    // // background: #356ac5;
    // // margin-right: 6px;
    // position: absolute;
    // top: 7px;
    // left: 6px;
    // transform: scale(0.55);
    position: absolute;
    left: 22px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }

  ::v-deep .el-dialog__title {
    font-weight: bold;
    margin-left: 10px;
    opacity: 0.8;
  }

  ::v-deep .el-pager li {
    margin: 0 4px;
  }
}
// 列表样式
.list-class {
  ::v-deep .el-table--mini .el-table__cell {
    padding: 5px 0;
  }
  .flex {
    // flex-direction: column;
    .listXMInput {
      margin-bottom: 18px;
      width: 60%;
    }
    .listXMInit {
    }
  }
}
</style>
