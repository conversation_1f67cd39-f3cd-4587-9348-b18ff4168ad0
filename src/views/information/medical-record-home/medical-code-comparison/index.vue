<template>
  <div class="container">
    <div class="header">
      <div class="header-search">
        <div class="query-word">病案代码对照:</div>
        <el-select v-model="medicalCode" @change="changeSelect">
          <el-option
            v-for="item in medicalCodeList"
            :key="item.daiMa"
            :label="item.mingCheng"
            :value="item.daiMa"
          ></el-option>
        </el-select>
      </div>
      <div class="header-search" v-if="medicalCode == '9'" style="padding-top: 0">
        <div class="query-word" style="font-weight: normal">新增职工</div>
        <div class="query-word">姓名：</div>
        <el-input
          v-model="addRow.hismc"
          class="select-input1"
          readonly
          @click.native="selectFun({}, 'add')"
          placeholder="请选择"
        ></el-input>
        <div class="query-word">工号：</div>
        <el-input
          v-model="addRow.hisdm"
          class="select-input1"
          readonly
          @click.native="selectFun({}, 'add')"
          placeholder="请选择"
        ></el-input>
        <div class="query-word">属性：</div>
        <el-select v-model="attributeCode">
          <el-option
            v-for="item in attributeList"
            :key="item.daiMa"
            :label="item.mingCheng"
            :value="item.daiMa"
          ></el-option>
        </el-select>
        <div class="query-word">科室：</div>
        <el-input
          v-model="departRow.keShiMC"
          class="select-input1"
          readonly
          @click.native="selectFun({}, 'depart')"
          placeholder="请选择"
        ></el-input>
        <div class="button">
          <el-button type="primary" @click="insertOneBaZgxx">新增</el-button>
        </div>
      </div>
      <div class="header-search" v-if="medicalCode == '9'" style="padding-top: 0">
        <div class="query-word" style="font-weight: normal">职工查询</div>
        <div class="query-word">姓名：</div>
        <el-input v-model="name1" class="select-input1" placeholder="请填写"></el-input>
        <div class="button">
          <el-button type="primary" @click="getBingAnZhiGongXXByPage">查询</el-button>
        </div>
      </div>
    </div>
    <div class="content" :style="{ height: medicalCode == '9' ? '650px' : '740px' }">
      <div class="content-header">
        <div class="title">病案代码对照一览表</div>
      </div>
      <div class="table">
        <el-table
          :max-height="medicalCode == '9' ? 560 : 688"
          border
          stripe
          :data="patientData"
          style="width: 838px"
        >
          <template v-if="medicalCode != '9'">
            <el-table-column prop="daiMa" key="daiMa" width="150" label="代码"></el-table-column>
            <el-table-column
              prop="mingCheng"
              key="mingCheng"
              width="180"
              label="代码名称"
            ></el-table-column>
            <el-table-column prop="hisdm" key="hisdm" width="250" label="HIS代码">
              <template #default="{ row }">
                <el-button type="primary" @click="selectFun(row)">选择</el-button>
                <el-input v-model="row.hisdm" readonly class="select-input"></el-input>
              </template>
            </el-table-column>
            <el-table-column
              prop="hisdmmc"
              key="hisdmmc"
              width="150"
              label="HIS代码名称"
            ></el-table-column>
          </template>

          <template v-if="medicalCode == '9'">
            <el-table-column
              prop="zhiGongBH"
              key="zhiGongBH"
              width="150"
              label="职工编码"
            ></el-table-column>
            <el-table-column
              prop="zhiGongXM"
              key="zhiGongXM"
              width="180"
              label="职工姓名"
            ></el-table-column>
            <el-table-column prop="hisdm" key="hisdm" width="250" label="HIS代码">
              <template #default="{ row }">
                <el-button type="primary" @click="selectFun(row)">选择</el-button>
                <el-input v-model="row.hisdm" readonly class="select-input"></el-input>
              </template>
            </el-table-column>
            <el-table-column
              prop="hismc"
              key="hismc"
              width="150"
              label="HIS代码名称"
            ></el-table-column>
          </template>

          <el-table-column prop="caoZuo" width="106" align="center" label="操作">
            <template #default="{ row }">
              <el-button type="text" size="medium" @click="saveClick(row)">保存</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="medicalCode == '9'"
          class="pagination"
          :current-page="listPagination.currentPage"
          :page-size="listPagination.pageSize"
          background
          layout="total, prev, pager, next"
          :total="patientLength"
          @current-change="listPageChange"
        ></el-pagination>
      </div>
    </div>
    <Dialog ref="childRef" @save-success="handleSaveSuccess" />
  </div>
</template>

<script>
import {
  getBingAnZhiGongXXByPage,
  getFuZaByDaiMaLB,
  saveOneGyGydm,
  saveOneBaZgxx,
  insertOneBaZgxx
} from '@/api/medical-record-api'
import Dialog from './Dialog'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      medicalCode: '3', //选择类型值
      medicalCodeList: [
        { daiMa: '3', mingCheng: '职业' },
        { daiMa: '6', mingCheng: '籍贯' },
        { daiMa: '7', mingCheng: '婚姻' },
        { daiMa: '8', mingCheng: '民族' },
        { daiMa: '9', mingCheng: '*职工*' }
      ], //选择类型列表
      attributeCode: '1', //职工-属性值
      attributeList: [
        { daiMa: '1', mingCheng: '医生' },
        { daiMa: '2', mingCheng: '操作员' }
      ], //职工-属性值列表
      name1: '', //职工姓名查询
      patientData: [], //数据列表
      patientLength: 0, //数据列表长度
      listPagination: {
        currentPage: 1,
        pageSize: 100
      }, //列表分页
      selectRow: {}, //没选择直接保存的列表项
      propsRow: {}, //有选择直接保存的列表项
      addRow: {}, //职工新增-选择职工姓名选择项
      departRow: {}, //职工新增-选择科室选择项
      addFlag: '' //职工新增是否选择职工
    }
  },
  components: {
    Dialog
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.getFuZaByDaiMaLB()
  },
  methods: {
    // 获取列表初始化接口
    async getFuZaByDaiMaLB() {
      try {
        const res = await getFuZaByDaiMaLB({
          lieBie: this.medicalCode
        })
        if (res.hasError === 0) {
          const filteredData = res.data.filter((item) => item.daiMa !== 0)
          this.patientData = filteredData.sort((a, b) => a.daiMa - b.daiMa)
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 获取列表初始化接口--职工
    async getBingAnZhiGongXXByPage() {
      try {
        const res = await getBingAnZhiGongXXByPage({
          pageIndex: this.listPagination.currentPage,
          pageSize: 100,
          wenBen: this.name1
        })
        if (res.hasError === 0) {
          const filteredData = res.data.filter((item) => item.daiMa !== 0)
          this.patientData = filteredData.sort((a, b) => a.daiMa - b.daiMa)
          this.patientData1 = filteredData.sort((a, b) => a.daiMa - b.daiMa)
          this.patientLength = res.extendData.total
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 切换选择病案代码类型
    changeSelect() {
      if (this.medicalCode != '9') {
        this.getFuZaByDaiMaLB()
      } else {
        this.getBingAnZhiGongXXByPage()
      }
    },

    // 查询列表分页处理
    listPageChange(page) {
      this.listPagination.currentPage = page
      if (
        this.medicalCode == '3' ||
        this.medicalCode == '6' ||
        this.medicalCode == '7' ||
        this.medicalCode == '8'
      ) {
      } else if (this.medicalCode == '9') {
        this.getBingAnZhiGongXXByPage() //职工接口
      }
    },

    // 点击选择打开弹框
    selectFun(row, type) {
      this.addFlag = type
      this.$nextTick(() => {
        this.$refs.childRef?.openDialog(type == 'depart' ? 'depart' : this.medicalCode, row)
      })
    },

    // 子组件传递的列表选择某一项
    handleSaveSuccess(propsRow) {
      if (this.addFlag == 'add') {
        this.addRow = propsRow
      } else if (this.addFlag == 'depart') {
        this.departRow = propsRow
      } else {
        this.propsRow = propsRow
      }
    },

    // 保存一条GyGydm的内容-其他类
    async saveOneGyGydm() {
      let row = {}
      if (this.propsRow.hisdm == undefined) {
        row = this.selectRow //没选择保存
      } else {
        row = this.propsRow //有选择保存
      }
      try {
        const res = await saveOneGyGydm({
          hisdm: row.hisdm,
          hisdmmc: row.hisdmmc,
          daiMaLB: row.daiMaLB,
          daiMa: row.daiMa,
          mingCheng: row.mingCheng,
          shuRuMa1: row.shuRuMa1,
          zuoFeiPB: row.zuoFeiPB,
          buNengXG: row.buNengXG,
          jiuDaiMa: row.jiuDaiMa,
          biaoZhunDM: row.biaoZhunDM,
          daiMaGL: row.daiMaGL
        })
        if (res.hasError === 0) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.propsRow = {}
        } else {
          this.propsRow = {}
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 保存一条BaZgxx的内容-职工类
    async saveOneBaZgxx() {
      let row = {}
      if (this.propsRow.hisdm == undefined) {
        row = this.selectRow //没选择保存
      } else {
        row = this.propsRow //有选择保存
      }
      try {
        const res = await saveOneBaZgxx({
          hisdm: row.hisdm,
          hismc: row.hismc,
          jiuDaiMa: row.jiuDaiMa,
          keShiDM: row.keShiDM,
          shuRuM1: row.shuRuM1,
          shuRuM2: row.shuRuM2,
          shuRuM3: row.shuRuM3,
          zhiGongBH: row.zhiGongBH,
          zhiGongSX: row.zhiGongSX,
          zhiGongXM: row.zhiGongXM,
          zuoFeiPB: row.zuoFeiPB
        })
        if (res.hasError === 0) {
          this.$message({
            message: '保存成功',
            type: 'success'
          })
          this.propsRow = {}
        } else {
          this.propsRow = {}
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 保存接口
    saveClick(row) {
      this.selectRow = row
      if (this.medicalCode == '9') {
        this.saveOneBaZgxx()
      } else {
        this.saveOneGyGydm()
      }
    },

    // 住院医生站_保存_保存病案接口-新增职工事件
    async insertOneBaZgxx() {
      if (
        this.departRow.keShiDM == undefined ||
        this.addRow.zhongShenDM == undefined ||
        this.addRow.hismc == undefined
      ) {
        this.$message({
          message: '请填写完整职工信息',
          type: 'error'
        })
      } else {
        let row = this.addRow
        try {
          const res = await insertOneBaZgxx({
            hisdm: '',
            hismc: '',
            jiuDaiMa: '',
            keShiDM: this.departRow.keShiDM,
            shuRuM1: '',
            shuRuM2: '',
            shuRuM3: '',
            zhiGongBH: row.zhongShenDM,
            zhiGongSX: this.attributeCode,
            zhiGongXM: row.hismc,
            zuoFeiPB: ''
          })
          if (res.hasError === 0) {
            this.$message({
              message: '新增成功',
              type: 'success'
            })
            this.getBingAnZhiGongXXByPage()
            this.propsRow = {}
            this.addRow = {}
            this.departRow = {}
          } else {
            this.propsRow = {}
            this.addRow = {}
            this.departRow = {}
          }
        } catch (error) {
          console.log(error)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  // display: flex;
  // align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .header-search {
    display: flex;
    align-items: center;
    padding: 12px 14px;
    .query-word {
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 10px;
      margin-right: 10px;
    }
    .query-word:first-child {
      margin-left: 0px;
    }
    padding: 12px 14px;
    ::v-deep .el-button {
      background-color: #a66dd4;
      border: 1px solid #a66dd4;
      color: #fff;
    }
    .button {
      margin-left: 6px;
    }
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 14px;
    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }
    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
    .table {
      ::v-deep .el-button {
        // color: #356ac5;
      }
    }
  }
}
.pagination {
  display: flex;
  justify-content: flex-start;
  margin-top: 2px;
  margin-left: 360px;
}

::v-deep .el-table__cell {
  padding-left: 8px;
}

::v-deep .el-table__cell:last-child {
  // padding-left: 0px;
}

::v-deep .el-table th.el-table__cell,
.el-table td.el-table__cell {
  padding-top: 9px;
  padding-bottom: 9px;
}
::v-deep .el-table .el-table__cell {
  padding-top: 4px !important;
  padding-bottom: 4px !important;
}
::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
  padding-top: 4px;
  padding-bottom: 4px;
}
.select-input {
  width: 160px;
  margin-left: 14px;
}
.select-input1 {
  width: 180px;
  cursor: pointer;
}
</style>
