<template>
  <div class="container">
    <div class="header">
      <el-radio v-model="inType" label="1" @change="inTypeSelect">按科室</el-radio>
      <el-radio v-model="inType" label="2" @change="inTypeSelect">按病区</el-radio>
      <!-- <div class="button">
        <el-button type="primary" @click="searchFun">查询</el-button>
      </div> -->
    </div>
    <div class="content">
      <div class="content-left">
        <div class="content-header">
          <div class="title">专科编码员对照一览表</div>
        </div>
        <div class="table">
          <el-table max-height="688" border stripe :data="patientData" style="width: 900px">
            <el-table-column prop="buMenMC" width="285" label="部门名称"></el-table-column>
            <el-table-column prop="yongHuXM" width="200" label="编码员"></el-table-column>
            <el-table-column prop="bingAnKSMC" width="285" label="病案科室"></el-table-column>
            <el-table-column prop="caoZuo" width="128" align="center" label="操作">
              <template #default="scope">
                <el-button type="text" size="medium" @click="handleClick(scope.row)">
                  选择
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="content-right">
        <table class="right-table">
          <tr>
            <td class="right-table-title">科室</td>
            <td class="right-table-content">{{ selectedRow.buMenMC }}</td>
            <td class="right-table-button">
              <el-button plain>同步专科</el-button>
            </td>
          </tr>
          <tr>
            <td class="right-table-title">编码员</td>
            <td class="right-table-content">{{ selectedRow.yongHuXM }}</td>
            <td class="right-table-button">
              <el-button plain @click="changeCoder('coder')">修改编码员</el-button>
            </td>
          </tr>
          <tr>
            <td class="right-table-title">病案科室</td>
            <td class="right-table-content">{{ selectedRow.bingAnKSMC }}</td>
            <td class="right-table-button">
              <el-button plain @click="changeCoder('depart')">修改病案科室</el-button>
            </td>
          </tr>
          <tr>
            <td class="right-table-title"></td>
            <td class="right-table-content">
              <el-button plain @click="saveBianMaYuanXX">保存</el-button>
            </td>
            <td class="right-table-button"></td>
          </tr>
        </table>
      </div>
    </div>

    <!-- 编码员列表 -->
    <div class="surgeries-class">
      <el-dialog
        :visible.sync="coderVisible"
        :title="popType == 'coder' ? '编码员列表' : '病案科室列表'"
        width="620px"
        pop-type="tip"
        :close-on-click-modal="false"
      >
        <div class="flex">
          <el-input
            v-model="coderInput"
            class="coderInput"
            :placeholder="
              popType == 'coder' ? '请输入人员终身码或拼音/五笔码' : '请输入病案科室名称'
            "
            @input="coderSearch"
          ></el-input>
        </div>
        <el-table
          :data="coderData"
          border
          stripe
          size="mini"
          max-height="480px"
          @row-click="handleRowClick"
        >
          <template v-if="popType == 'coder'">
            <el-table-column
              key="xingMing"
              prop="xingMing"
              width="130"
              label="姓名"
            ></el-table-column>
            <el-table-column
              key="zhongShenDM"
              prop="zhongShenDM"
              width="226"
              label="终身码"
            ></el-table-column>
            <el-table-column
              key="xianZhuanKeMC"
              prop="xianZhuanKeMC"
              width="226"
              label="现专科名称"
            ></el-table-column>
          </template>
          <template v-if="popType == 'depart'">
            <el-table-column
              key="keShiMC"
              prop="keShiMC"
              width="582"
              label="科室名称"
            ></el-table-column>
          </template>
        </el-table>
        <el-pagination
          :current-page="coderPagination.currentPage"
          :page-size="coderPagination.pageSize"
          background
          layout="total, prev, pager, next"
          :total="coderLength"
          @current-change="coderPageChange"
        ></el-pagination>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="coderVisible = false">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getBianMaYuanAllByLieBie,
  getYlRykList,
  getBingAnKeShiDMByPage,
  saveBianMaYuanXX
} from '@/api/medical-record-api'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      inType: '1', //查询类型
      patientData: [], //查询返回数据
      selectedRow: {}, //单选某某一条数据
      coderVisible: false, //编码员显示判断
      coderData: [], //编码员列表数据
      popType: 'coder', //搜索框选择类型
      coderInput: '', //编码员搜索输入框
      coderPagination: {
        currentPage: 1,
        pageSize: 10
      }, //编码员分页
      coderLength: 0, //编码员列表长度
      selectedCoderRow: {}, //选择编码员数据
      selectedDepartRow: {} //选择病案科室数据
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID,
      patientDetail: ({ patient }) => patient.patientInit
    })
  },
  async mounted() {
    await this.getBianMaYuanAllByLieBie()
  },
  methods: {
    // 获取列表初始化接口
    async getBianMaYuanAllByLieBie() {
      try {
        const res = await getBianMaYuanAllByLieBie({
          lieBie: this.inType
        })
        if (res.hasError === 0) {
          this.patientData = res.data
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 切换查询类型
    inTypeSelect(value) {
      this.inType = value
      this.getBianMaYuanAllByLieBie()
    },

    // 查询_病案职工信息
    async getYlRykList() {
      try {
        const res = await getYlRykList({
          pageIndex: this.coderPagination.currentPage,
          pageSize: 10,
          guanJianZi: this.coderInput,
          renYuanLB: '',
          // mode: '0拼音1五笔 空 只查名字',
          zhuanKeID: 0
        })
        if (res.hasError === 0) {
          this.coderData = res.data
          this.coderLength = res.extendData.total
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 查询_病案科室信息
    async getBingAnKeShiDMByPage() {
      try {
        const res = await getBingAnKeShiDMByPage({
          pageIndex: this.coderPagination.currentPage,
          pageSize: 10,
          wenBen: this.coderInput,
          keshilb: 2
        })
        if (res.hasError === 0) {
          this.coderData = res.data
          this.coderLength = res.extendData.total
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 选择左侧某一条数据
    handleClick(row) {
      this.selectedRow = Object.assign({}, row)
    },

    // 选择编码员/病案科室弹框显示
    changeCoder(type) {
      this.popType = type
      this.coderInput = ''
      this.coderPagination.currentPage = 1
      this.coderVisible = true
      if (type == 'coder') {
        this.getYlRykList()
      } else if (type == 'depart') {
        this.getBingAnKeShiDMByPage()
      }
    },

    // 编码员列表查询
    async coderSearch() {
      this.coderPagination.currentPage = 1
      if (this.popType == 'coder') {
        this.getYlRykList()
      } else if (this.popType == 'depart') {
        this.getBingAnKeShiDMByPage()
      }
    },

    // 编码员列表分页处理
    coderPageChange(page) {
      this.coderPagination.currentPage = page
      if (this.popType == 'coder') {
        this.getYlRykList()
      } else if (this.popType == 'depart') {
        this.getBingAnKeShiDMByPage()
      }
    },

    // 单选弹框某一条数据
    handleRowClick(row) {
      if (this.popType == 'coder') {
        this.selectedRow.yongHuXM = row.xingMing
        this.selectedCoderRow = row
      } else if (this.popType == 'depart') {
        this.selectedRow.bingAnKSMC = row.keShiMC
        this.selectedDepartRow = row
      }
      this.coderVisible = false
    },

    // 保存一条专科编码员信息
    async saveBianMaYuanXX() {
      if (this.selectedRow.buMenMC == null) {
        this.$message.error('请选择部门名称')
        return
      } else {
        try {
          const res = await saveBianMaYuanXX({
            buMenMC: this.selectedRow.buMenMC,
            buMenID: this.selectedRow.buMenID,
            buMenDM: this.selectedRow.buMenDM,
            yongHuID: this.selectedRow.yongHuID,
            renYuanKuID: this.selectedRow.renYuanKuID,
            yongHuXM: this.selectedCoderRow.xingMing,
            pinYin: this.selectedRow.pinYin,
            xiuGaiSJ: this.selectedRow.xiuGaiSJ,
            caoZuoZheID: this.selectedRow.caoZuoZheID,
            bingAnKSDM: this.selectedDepartRow.keShiDM,
            bingAnKSMC: this.selectedDepartRow.keShiMC,
            leiBie: this.selectedRow.leiBie
          })
          if (res.hasError === 0) {
            this.$message.success('保存成功')
            this.getBianMaYuanAllByLieBie()
          }
        } catch (error) {
          console.log(error)
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  padding: 12px;
  background-color: #fff;
}
.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  .query-word {
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 10px;
    margin-right: 10px;
  }
  .query-word:first-child {
    margin-left: 0px;
  }
  padding: 12px 14px;
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }
  .button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  height: 740px;
  padding: 14px;
  display: flex;
  .content-left {
    .content-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      margin-bottom: 14px;
      .title {
        position: relative;
        color: #171c28;
        font-size: 14px;
        line-height: 14px;
        font-weight: bold;
        margin-left: 9px;
      }
      .title::before {
        position: absolute;
        left: -9px;
        width: 3px;
        height: 14px;
        content: '';
        background-color: #356ac5;
      }
      .table {
        ::v-deep .el-button {
          // color: #356ac5;
        }
      }
    }
  }
  .content-right {
    margin-left: 50px;
    margin-top: 28px;
    .right-table {
      text-align: left;
      table-layout: fixed;
      width: 550px;
      td {
        border: 1px solid #dcdfe6;
        padding: 4px 6px;
        overflow: hidden;
        box-sizing: border-box;
      }
      .right-table-title {
        background-color: #ffffff;
        width: 20%;
        height: 100%;
        line-height: 16px;
        padding-left: 10px;
      }
      .right-table-content {
        background-color: #ffffff;
        padding-left: 10px;
      }
      .right-table-button {
        background-color: #ffffff;
        width: 30%;
        height: 100%;
        line-height: 16px;
      }
    }
  }

  ::v-deep .el-table__cell {
    padding-left: 8px;
    padding-top: 4px;
    padding-bottom: 4px;
  }

  ::v-deep .el-table__cell:last-child {
    padding-left: 0px;
  }

  ::v-deep .el-table th.el-table__cell,
  .el-table td.el-table__cell {
    padding-top: 9px;
    padding-bottom: 9px;
  }

  ::v-deep .el-table--enable-row-transition .el-table__body td.el-table__cell {
    padding-top: 4px;
    padding-bottom: 4px;
  }
}

::v-deep .el-radio__original {
  display: none !important;
}

// 编码员列表弹框
.surgeries-class {
  ::v-deep .el-dialog {
    padding: 6px 10px;
    .flex {
      .coderInput {
        margin-bottom: 18px;
        width: 60%;
      }
    }
  }

  ::v-deep .el-table--mini .el-table__cell {
    padding: 12px 0;
  }

  .dialog-footer {
    padding-bottom: 15px;
  }

  ::v-deep .el-dialog__header {
    padding: 10px 14px;
    font: var(--font-medium);
    font-size: var(--font-size-medium);
    color: #171c28;
    display: flex;
    align-items: center;
  }

  ::v-deep .el-dialog__header::before {
    // content: url('~@/assets/images/info.png');
    // // width: 3px;
    // // height: 16px;
    // // background: #356ac5;
    // // margin-right: 6px;
    // position: absolute;
    // top: 7px;
    // left: 6px;
    // transform: scale(0.55);
    position: absolute;
    left: 22px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }

  ::v-deep .el-dialog__title {
    font-weight: bold;
    margin-left: 10px;
    opacity: 0.8;
  }

  ::v-deep .el-pager li {
    margin: 0 4px;
  }
}

::v-deep .el-table__body tr.current-row > td {
  background-color: #f0f7ff !important;
}
</style>
