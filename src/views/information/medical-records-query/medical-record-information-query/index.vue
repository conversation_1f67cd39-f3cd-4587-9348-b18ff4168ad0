<template>
  <div class="container">
    <div class="header">
      <div>
        <div style="display: flex; align-items: center">
          <div class="query-word">请选择查询条件：</div>
          <div>
            <el-radio-group v-model="leiBie">
              <el-radio label="1">病案号</el-radio>
              <el-radio label="3">住院号</el-radio>
            </el-radio-group>
          </div>
          <div style="margin-left: 5px">
            <el-input v-model="bingAnHao"></el-input>
          </div>
          <div class="button">
            <el-button type="primary" @click="onQuery">查询</el-button>
            <el-button type="primary" @click="bingAnHao = ''">清空</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">病案信息查询</div>
      </div>
      <div style="flex: 1; min-height: 0">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="zhuanKeMC" label="专科"></el-table-column>
          <el-table-column prop="bingAnHao" label="病案号"></el-table-column>
          <el-table-column prop="bingRenXM" label="姓名"></el-table-column>
          <el-table-column prop="xingBie" label="性别" width="60"></el-table-column>
          <el-table-column prop="chuShengRQ" label="出生日期"></el-table-column>
          <el-table-column prop="bingQuMC" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
          <el-table-column prop="riJianSS" label="入院日期" width="150"></el-table-column>
          <el-table-column prop="bingQuRuYuanSJ" label="病区入院日期" width="150"></el-table-column>
          <el-table-column prop="chuYuanSJ" label="出院日期" width="150"></el-table-column>
          <el-table-column prop="ruYuanZD" label="入院诊断"></el-table-column>
          <el-table-column label="病例归档" width="100">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row, 1)">
                下载病历记录
              </el-button>
            </template>
          </el-table-column>
          <el-table-column label="查询记录" width="100">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row, 2)">
                查询记录
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getByBingAnHaos } from '@/api/medical-record-information-query'
import { format } from 'date-fns'
export default {
  name: 'MedicalRecordInformationQuery',
  data() {
    return {
      defaultDialog: false,
      leiBie: '1',
      bingLiID: '',
      bingRenXM: '',
      bingAnHao: '',
      tableData: []
    }
  },
  methods: {
    async onQuery() {
      const arr = this.bingAnHao.split(',')
      const res = await getByBingAnHaos(arr)
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    },
    handleClick(row, type) {}
  }
}
</script>

<style scoped lang="scss">
.container {
  height: 100%;
  display: flex;
  flex-direction: column;

  padding: 12px;
  background-color: #fff;
}

.header {
  display: flex;
  align-items: center;
  background-color: #eaf0f9;
  margin-bottom: 12px;
  border-radius: 4px;

  .query-word {
    white-space: nowrap; //不换行
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
  }

  .query-value {
    white-space: nowrap;
    margin-right: 10px;

    ::v-deep .el-checkbox {
      margin-right: 5px;

      .el-checkbox__label {
        padding-left: 5px;
      }
    }
  }

  padding: 12px 14px;

  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
  }

  .button {
    white-space: nowrap;
    margin-left: 6px;
  }
}

.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 14px;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
