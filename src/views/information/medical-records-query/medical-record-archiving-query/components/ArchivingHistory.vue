<template>
  <div class="container">
    <div class="content">
      <div class="content-header">
        <div class="title">查询记录</div>
      </div>
      <div style="flex: 1; min-height: 0">
        <el-table :data="tableData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="zhuanKeMC" label="查询用户"></el-table-column>
          <el-table-column prop="bingAnHao" label="查询时间"></el-table-column>
          <el-table-column prop="bingRenXM" label="文件ID"></el-table-column>
          <el-table-column label="查看文件 " width="100">
            <template #default="scope">
              <el-button type="text" size="small" @click="handleClick(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="content-header">
        <div class="title">打印记录</div>
      </div>
      <div style="flex: 1; min-height: 0">
        <el-table :data="listData" border size="medium" height="100%" style="width: 100%">
          <el-table-column prop="zhuanKeMC" label="打印用户"></el-table-column>
          <el-table-column prop="bingAnHao" label="打印时间"></el-table-column>
          <el-table-column prop="bingRenXM" label="文件ID "></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
import { getAllBLCXByBingLiID, getAllBLDYByBingLiID } from '@/api/information'
import { format } from 'date-fns'

export default {
  name: 'MedicalRecordArchivingQuery',
  data() {
    return {
      bingLiID: '',
      bingAnHao: '',
      bingRenXM: '',
      listData: [],
      tableData: []
    }
  },
  methods: {
    async resetData(row) {
      if (obj) {
        this.bingLiID = row.bingLiID
        this.bingAnHao = row.bingAnHao
        this.bingRenXM = row.bingRenXM
        const res = await getAllBLCXByBingLiID({ bingLiID: this.bingLiID })
        if (res.hasError === 0) {
          this.tableData = res.data
        }
        const msg = await getAllBLDYByBingLiID({ bingLiID: this.bingLiID })
        if (msg.hasError === 0) {
          this.listData = msg.data
        }
      }
    },
    async onQuery() {
      const res = await getListByEmpi({ bingAnHao: this.bingAnHao })
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    },
    handleClick(row) {
      window.open(`/EHR/zyblgd/showPDF2.aspx?as_blid=${this.bingLiID}&as_fileid=${row.fileid}`)
    }
  }
}
</script>

<style scoped lang="scss">
.content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;

  background-color: #eaf0f9;
  padding: 14px;

  .content-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 14px 0;

    .title {
      position: relative;
      color: #171c28;
      font-size: 14px;
      line-height: 14px;
      font-weight: bold;
      margin-left: 9px;
    }

    .title::before {
      position: absolute;
      left: -9px;
      width: 3px;
      height: 14px;
      content: '';
      background-color: #356ac5;
    }
  }

  table {
    width: 100%;
  }

  td {
    border: 1px solid #ddd;
    border-collapse: collapse; /* 移除表格内边框间的间隙 */
    height: 40px;
    padding: 4px;
  }

  .info-label {
    text-align: right;
    width: 150px;
    min-width: 150px;
    background-color: #eaf0f9;
  }

  .info-label2 {
    text-align: right;
    width: 85px;
    min-width: 85px;
    background-color: #eaf0f9;
  }

  .info-value {
    background-color: #f7f9fd;

    .value {
      margin-left: 10px;

      .risk {
        margin-right: 15px;
      }
    }

    a {
      text-decoration: underline;
      color: #356ac5;
    }

    ::v-deep .el-form-item {
      margin-bottom: 0;
      width: 240px;
    }

    .cz-select {
      display: flex;
      align-items: center;

      ::v-deep .el-form-item {
        width: auto;
      }

      ::v-deep .el-select {
        width: 160px;
        margin-right: 5px;
      }

      ::v-deep .el-input {
        margin-right: 5px;
      }
    }

    .value2 {
      ::v-deep .el-form-item {
        width: 100%;
      }

      ::v-deep .el-form-item__content {
        width: 100%;
        max-width: none;
      }

      ::v-deep .el-select {
        width: 100%;
      }

      ::v-deep .el-input {
        width: 100%;
      }
    }

    .el-date-editor {
      ::v-deep input {
        width: 100%;
      }
    }

    //.el-button {
    //  margin-top: -3px;
    //  margin-left: 30px;
    //}
    .radio {
      ::v-deep .el-radio {
        margin-right: 8px;
      }

      ::v-deep .el-radio__label {
        padding-left: 4px;
      }
    }
  }
}

::v-deep .el-form-item__error {
  z-index: 3000;
  margin-top: -10px;
}

::v-deep .el-dialog__body {
  padding: 20px;
}
</style>
