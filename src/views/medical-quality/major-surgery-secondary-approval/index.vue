<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">手术状态:</div>
        <div class="header-item-radio">
          <el-radio-group v-model="shenPiZT">
            <el-radio label="" size="large">
              <span class="search-label">全部</span>
            </el-radio>
            <el-radio label="1" size="large">
              <span class="search-label">已审批</span>
            </el-radio>
            <el-radio label="0" size="large">
              <span class="search-label">未审批</span>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">拟手术日期/开单日期:</div>
        <div class="header-item-date">
          <el-date-picker
            v-model="dateFW"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="default"
            value-format="yyyy-MM-dd"
          />
        </div>
        <div class="header-item-button"><el-button @click="queryData">查询</el-button></div>
        <div class="header-item-button">
          <el-button style="background-color: #356ac5" @click="exportToExcel">
            导出医务处审批报表
          </el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">手术审批</div>
        <div class="filter-box-right">
          <div class="filter-box-right">
            <el-alert style="width: 490px" type="primary" show-icon :closable="false" size="small">
              <div style="color: #000">
                <span>当前审批：医务处二次审批；</span>
                <span>您可以审批的手术级别：{{ initData.shenPiSSLBMC }}</span>
              </div>
            </el-alert>
          </div>
        </div>
      </div>
      <div class="table">
        <el-table max-height="648" border stripe :data="tableData" @row-click="rowClick">
          <el-table-column width="60" label="审批">
            <template #default="{ row }">
              <i v-if="row.shenPiYJ == '1'" class="el-icon-check"></i>
              <i v-else-if="row.shenPiYJ == '0'" class="el-icon-close"></i>
            </template>
          </el-table-column>
          <el-table-column prop="shouShuJianMC" width="70" label="手术间"></el-table-column>
          <el-table-column prop="taiXu" width="60" label="台序"></el-table-column>
          <el-table-column width="200" label="拟施手术">
            <template #default="{ row }">
              {{ niShiShouShuMapper(row.shouShuXM) }}
            </template>
          </el-table-column>
          <el-table-column prop="niShouShuSJ" width="100" label="拟手术日期">
            <template #default="{ row }">
              {{ formatDate(row.niShouShuSJ, 'yyyy-MM-dd') }}
            </template>
          </el-table-column>
          <el-table-column prop="bingQuMC" width="100" label="病区"></el-table-column>
          <el-table-column prop="chuangWeiHao" width="60" label="床位"></el-table-column>
          <el-table-column prop="bingRenXM" width="80" label="姓名"></el-table-column>
          <el-table-column prop="shouShuJBMC" width="90" label="手术级别"></el-table-column>
          <el-table-column prop="zhuanKeXZ" width="60" label="小组"></el-table-column>
          <el-table-column prop="xiaoZuTX" width="60" label="台序"></el-table-column>
          <el-table-column prop="zhuDaoYSXM" width="80" label="主刀医师"></el-table-column>
          <el-table-column prop="zhuanKeMC" width="60" label="专科"></el-table-column>
          <el-table-column prop="bingAnHao" width="110" label="病案号"></el-table-column>
          <el-table-column prop="kaiDanSJ" width="150" label="开单时间"></el-table-column>
          <el-table-column prop="kaiDanYSXM" width="80" label="开单医师"></el-table-column>
          <el-table-column prop="shenPiYSXM" width="80" label="审批医师"></el-table-column>
          <el-table-column prop="shenPiSJ" width="150" label="审批时间"></el-table-column>
          <el-table-column prop="kaiDanBZ" label="备注"></el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog width="70%" class="shouShuTZDDialog" :visible.sync="shouShuTZDDialog">
      <div slot="title" class="title">手术通知单</div>
      <surgical-notice
        ref="SurgicalNotice"
        :extra-data="extraData"
        style="height: 600px"
        @close="close"
        @save="save"
      ></surgical-notice>
    </el-dialog>
  </div>
</template>
<script>
import { erCiSPInit, getErCiSPList, saveECSP } from '@/api/medical-quality'
import { format } from 'date-fns'
import SurgicalNotice from '@/views/patient-inside/inpatient-orders-menu/components/surgical-notice'
import * as XLSX from 'xlsx'
export default {
  components: {
    SurgicalNotice
  },
  data() {
    return {
      shenPiZT: '',
      dateFW: [],
      tableData: [],
      initData: {},
      shouShuTZDDialog: false,
      extraData: {}
    }
  },
  computed: {
    formatDate() {
      return function (val, type) {
        return format(new Date(val), type)
      }
    },
    niShiShouShuMapper() {
      return function (shouShuXM) {
        let arr = []
        shouShuXM.map((item) => {
          arr.push(item.shouShuMC)
        })
        if (arr.length > 0) {
          return arr.join('、')
        } else {
          return ''
        }
      }
    }
  },
  async mounted() {
    console.log(this.$store.state)
    await this.init()
  },
  methods: {
    async init() {
      let res = await erCiSPInit()
      this.initData = res.data
      this.dateFW.push(this.initData.kaiShiSJ)
      this.dateFW.push(this.initData.jieShuSJ)
    },
    async queryData() {
      let res = await getErCiSPList({
        erCiSPZT: this.shenPiZT,
        jieShuSJ: this.dateFW[1],
        kaiShiSJ: this.dateFW[0]
      })
      if (res.hasError === 0) {
        this.tableData = res.data
      }
    },
    rowClick(row, col, e) {
      this.extraData = row
      this.shouShuTZDDialog = true
      this.$nextTick(() => {
        this.$refs.SurgicalNotice.loadData()
      })

      console.log(row)
    },
    close() {
      this.shouShuTZDDialog = false
    },
    async save(erCiSP) {
      this.close()
      this.extraData.erCiSP = erCiSP
    },
    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }
      console.log(this.tableData)
      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            病人姓名: item.bingRenXM,
            住院号: item.zhuYuanHao,
            拟施手术: this.niShiShouShuMapper(item.shouShuXM),
            手术级别: item.shouShuJBMC,
            拟施手术时间: this.formatDate(item.niShouShuSJ, 'yyyy-MM-dd'),
            专科: item.zhuanKeMC,
            病区: item.bingQuMC,
            主刀医师: item.zhuDaoYSXM,
            '审批时间（科室）': item.shenPiSJ,
            // '重大手术':item.shenPiSJ,
            // '非计划再次手术': item.huiZhenXJYSZC,
            // '新技术新项目': item.renYuanKuID,
            审批状态: item.erCiSP.shenPiZTMC,
            审批备注: item.erCiSP.beiZhu
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `${this.dateFW[0]}~${this.dateFW[1]}医务部手术审批情况.xlsx`
        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  //.header-item-date{
  //  margin-right: 8px;
  //}
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  //justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
  margin-right: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
.filter-box-right {
  .el-alert {
    padding: 6px 10px;
    font-size: 12px;
    border: 1px solid rgba(21, 91, 212, 0.45);
    background-color: rgba(21, 91, 212, 0.05);
  }
}
::v-deep .table {
  tr {
    cursor: pointer;
  }

  tr:hover td {
    background-color: #356ac5 !important;
    color: #fff;
  }
}
</style>
