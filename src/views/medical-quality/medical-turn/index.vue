<!-- 病历上缴查询 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <span class="search-label">科室:</span>
        <el-input
          :value="selectZhuanke.buMenMC"
          style="width: 170px; margin-right: 10px"
          @focus="openSelectZhuanke()"
        />
        <span class="search-label">治疗组:</span>
        <el-select v-model="selectZhiLiaoZu" style="width: 170px; margin-right: 10px">
          <el-option
            v-for="item in zhiLiaoZuList"
            :key="item.zhiLiaoZuID"
            :label="item.zhiLiaoZuMC"
            :value="item.zhiLiaoZuID"
          ></el-option>
        </el-select>
        <span class="search-label">出院日期:</span>
        <el-date-picker
          v-model="searchDate"
          style="margin-right: 10px"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd"
        />
        <span class="search-label">结算日期:</span>
        <el-date-picker v-model="jieShuanDate" type="date" value-format="yyyy-MM-dd" />
        <el-button type="primary" @click="getRecord">查询</el-button>
        <el-button type="primary" @click="exportToExcel">导出到Excel</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            病历上缴情况查询
          </div>
        </div>
        <div class="table-component">
          <el-table :data="currentList" style="width: 100%" stripe border>
            <el-table-column
              v-for="(column, index) in columns"
              :key="index"
              :prop="column.value"
              :label="column.label"
              v-bind="column.props"
            >
              <template v-if="column.value === 'xingBie'" #default="{ row }">
                {{ row.xingBie === '1' ? '男' : '女' }}
              </template>
            </el-table-column>
          </el-table>
          *超期(天)：出院后3个工作日开始计算
          <br />
          ***：出院后5个工作日开始计算
          <el-pagination
            background
            layout="total, prev, pager, next"
            :current-page.sync="currentPage"
            :page-size="10"
            :pager-count="5"
            :total="dataList.length"
          ></el-pagination>
        </div>
      </div>
    </div>
    <zhuan-ke-dialog
      :visible.sync="visible"
      @confirm="
        (zhuanke) => {
          selectZhuanke = zhuanke
        }
      "
    />
  </div>
</template>

<script>
import * as XLSX from 'xlsx'
import { getBingLiShangJiaoQingKuangCX } from '@/api/medical-quality'
import { getZhiLiaoZuListByZhuanKeID } from '@/api/patient'
import ZhuanKeDialog from '@/components/Dialog/zhuanKeDialog.vue'
export default {
  name: 'MedicalTurn',
  components: {
    ZhuanKeDialog
  },
  data() {
    return {
      visible: false,
      currentPage: 1,
      searchDate: [],
      jieShuanDate: null,
      dataList: [],
      zhuanKeList: [],
      zhiLiaoZuList: [],
      selectZhuanke: {
        buMenID: 0,
        buMenMC: '所有专科'
      }, //当前选择专科
      selectZhiLiaoZu: 0,
      columns: [
        { value: 'bingAnHao', label: '病案号', props: { fixed: true } },
        { value: 'zhuanKeMC', label: '专科', props: { fixed: true } },
        { value: 'bingRenXM', label: '姓名', props: { fixed: true } },
        { value: 'xingBie', label: '性别', props: { fixed: true } },
        { value: 'chuShengRQ', label: '出生日期', props: { fixed: true, width: '160' } },
        { value: 'bingQuMC', label: '病区', props: { fixed: true } },
        { value: 'chuangWeiHao', label: '床位号', props: { fixed: true } },
        { value: 'ruYuanSJ', label: '入院日期', props: { fixed: true, width: '160' } },
        { value: 'shangJiaoSJ', label: '上缴时间', props: { fixed: true, width: '160' } }
      ]
    }
  },
  computed: {
    currentList() {
      const start = (this.currentPage - 1) * 10
      return this.dataList.slice(start, start + 10)
    }
  },
  watch: {
    selectZhuanke: {
      handler: async function (newZhuanke) {
        this.selectZhiLiaoZu = 0
        const res = await getZhiLiaoZuListByZhuanKeID({
          zhuanKeID: newZhuanke.buMenID //专科ID
        })
        console.log('根据专科id获取治疗组列表: ', res)
        if (res.hasError === 0 && Array.isArray(res.data)) {
          this.zhiLiaoZuList = [{ zhiLiaoZuID: 0, zhiLiaoZuMC: '所有治疗组' }, ...res.data]
        }
      },
      immediate: true
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async getRecord() {
      const res = await getBingLiShangJiaoQingKuangCX({
        kaiShiSJ: this.searchDate[0] + ' 00:00:00', //开始时间
        jieShuSJ: this.searchDate[1] + ' 00:00:00', //结束时间
        jieSuanSJ: this.jieShuanDate + ' 00:00:00', //结算时间
        zhiLiaoZuID: 0,
        zhuanKeID: this.selectZhuanke.buMenID //专科ID
      })
      console.log('住院医生站_病历查询_病历上缴情况查询: ', res)
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },
    async initPage() {
      let date = new Date()
      this.searchDate = [
        this.dateFormat(new Date(date.setMonth(date.getMonth() - 1))),
        this.dateFormat()
      ]
      this.jieShuanDate = this.dateFormat(new Date(date.setMonth(date.getMonth() + 2)))
    },

    exportToExcel() {
      if (this.dataList.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const worksheet = XLSX.utils.json_to_sheet(
        this.dataList.map((data) => {
          let d = {}
          this.columns.forEach((col) => {
            if (col.value === 'xingBie') {
              d[col.label] = data[col.value] === '1' ? '男' : '女'
            } else {
              d[col.label] = data[col.value]
            }
          })
          return d
        })
      )
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '病历上缴情况查询.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
    },
    openSelectZhuanke() {
      this.visible = true
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 960px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 1200px;
      }
    }
  }
}
</style>
