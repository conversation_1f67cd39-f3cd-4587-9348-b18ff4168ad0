<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">审批状态:</div>
        <div class="header-item-radio">
          <el-radio-group v-model="zhuangTaiBZ">
            <el-radio label="5" size="large">
              <span class="search-label">待审批</span>
            </el-radio>
            <el-radio label="1" size="large">
              <span class="search-label">审批通过</span>
            </el-radio>
            <el-radio label="6" size="large">
              <span class="search-label">审批未通过</span>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">审批日期:</div>
        <div class="header-item-date">
          <el-date-picker
            v-model="shenPiRQ"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            size="default"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">病案号:</div>
        <div class="header-item-input">
          <el-input v-model="bingAnHao" style="width: 170px" />
        </div>
        <div class="header-item-checkbox">
          <el-checkbox v-model="benRenXG" label="与本人相关" size="large" />
        </div>
        <div class="header-item-button"><el-button @click="queryData">查询</el-button></div>
        <div class="header-item-button">
          <el-button style="background-color: #356ac5" @click="exportToExcel">导出</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">【医务处】多学科会诊审批</div>
      </div>
      <div class="table">
        <el-table max-height="648" stripe border :data="tableData" :span-method="objectSpanMethod">
          <el-table-column prop="bingRenXM" width="90" label="姓名"></el-table-column>
          <el-table-column width="90" label="性别">
            <template #default="{ row }">
              {{ row.bingRenXB === '0' ? '女' : row.bingRenXB === '1' ? '男' : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="bingQuCW" width="120" label="病区-床位号"></el-table-column>
          <el-table-column prop="bingAnHao" width="100" label="病案号"></el-table-column>
          <el-table-column prop="shenQingSJ" width="150" label="申请时间"></el-table-column>
          <el-table-column prop="shenQingZKMC" width="130" label="申请专科"></el-table-column>
          <el-table-column prop="shenQingYSXM" width="90" label="申请医师"></el-table-column>
          <el-table-column
            prop="shenQingYSLXDH"
            width="130"
            label="申请医师联系电话"
          ></el-table-column>
          <el-table-column prop="huiZhenZKMC" width="100" label="会诊专科">
            <template #default="{ row }">
              <div v-for="item in splitMapper(row.huiZhenZKMC)" :key="item">{{ item }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="shenQingHZYSXM" width="100" label="邀请会诊医师">
            <template #default="{ row }">
              <div v-for="item in splitMapper(row.shenQingHZYSXM)" :key="item">{{ item }}</div>
            </template>
          </el-table-column>
          <el-table-column width="130" label="邀请医患办">
            <template #default="{ row }">
              {{ row.yiHuanCJ === '0' ? '否' : row.yiHuanCJ === '1' ? '是' : '' }}
            </template>
          </el-table-column>
          <el-table-column prop="yuYueHZSJ" width="150" label="预约会诊时间"></el-table-column>
          <el-table-column prop="yuYueHZDD" width="130" label="预约会诊地点"></el-table-column>
          <el-table-column prop="keShiSPYHXM" width="90" label="科室审批人"></el-table-column>
          <el-table-column prop="keShiSPJG" width="100" label="科室审批结果">
            <template #default="{ row }">
              <el-tag v-if="row.keShiSPJG === '0'">同意</el-tag>
              <el-tag v-else-if="row.keShiSPJG === '1'" type="danger">拒绝</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="keShiSHSJ" width="150" label="科室审批时间"></el-table-column>
          <el-table-column width="150" label="医务处/医患处审批">
            <template #default="{ row }">
              <el-radio v-model="row.yiWuSPJG" label="1">同意</el-radio>
              <el-radio v-model="row.yiWuSPJG" label="0">拒绝</el-radio>
            </template>
          </el-table-column>
          <el-table-column width="300" label="医务处/医患处审批备注">
            <template #default="{ row }">
              <el-input v-model="row.yiWuSPBZ" :rows="7" type="textarea" placeholder="备注" />
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="200" label="操作" align="center">
            <template #default="{ row }">
              <el-button type="text" size="small" @click="handleClickPatient(row)">
                查看病人
              </el-button>
              <el-button type="text" size="small" @click="openDuoXueKeHZD(row)">查看</el-button>
              <el-button type="text" size="small" @click="updateConsulationYWCSPXX(row)">
                审批
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <el-dialog width="60%" class="duoXueKeDialog" :visible.sync="duoXueKeDialog">
      <div slot="title" class="title">【医务处】多学科会诊审批-查看</div>
      <div style="border: 1px solid #dcdfe6; padding: 0 15px 15px">
        <div style="text-align: center; font-size: 22px; font-weight: 600; padding: 10px">
          疑难病历多学科会诊单
        </div>
        <multidiscriplinary-consultation
          ref="multidiscriplinaryConsultation"
          style="height: 600px"
          :extra-data="extraData"
          @save="saveMultiConsultation"
          @close="closeMultiConsultation"
        ></multidiscriplinary-consultation>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getConsulationDXKSPList, updateConsulationYWCSPXX } from '@/api/medical-quality'
import MultidiscriplinaryConsultation from '@/views/patient-inside/consultation-form-manage/components/multidiscriplinary-consultation'
import * as XLSX from 'xlsx'
import { format } from 'date-fns'
export default {
  components: {
    MultidiscriplinaryConsultation
  },
  data() {
    return {
      extraData: {},
      duoXueKeDialog: false,
      zhuangTaiBZ: '',
      shenPiRQ: '',
      bingAnHao: '',
      benRenXG: '',
      tableData: []
    }
  },
  computed: {
    splitMapper() {
      return function (str) {
        let arr = []
        if (str != null && str != '') arr = str.split(',')
        return arr
      }
    }
  },
  methods: {
    handleClickPatient(patient) {
      this.$router.push({
        path: `/patient-detail/${patient.bingLiID}`,
        query: {
          title: `社保 ${patient.bingQuCW} ${patient.bingRenXM}`
        }
      })
    },
    openDuoXueKeHZD(row) {
      console.log(row)
      this.extraData = { bingLiID: row.bingLiID, huiZhenDanID: row.huiZhenDanID }
      this.duoXueKeDialog = true
      this.$nextTick(() => {
        this.$refs.multidiscriplinaryConsultation.loadData()
      })
    },
    async saveMultiConsultation() {
      this.duoXueKeDialog = false
      await this.queryData()
    },
    closeMultiConsultation() {
      this.duoXueKeDialog = false
    },
    objectSpanMethod(row, column, rowIndex, columnIndex) {
      let size = row.row.size
      if (!(row.column.property === 'huiZhenZKMC' || row.column.property === 'shenQingHZYSXM')) {
        if (size !== undefined) {
          return {
            rowspan: size,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    async queryData() {
      let res = await getConsulationDXKSPList({
        benRenXG: this.benRenXG ? '1' : '0',
        bingAnHao: this.bingAnHao,
        jieShuSJ: this.shenPiRQ[1],
        kaiShiSJ: this.shenPiRQ[0],
        zhuangTaiBZ: this.zhuangTaiBZ
      })
      if (res.hasError === 0) {
        this.tableData = res.data
        let processedData = []
        for (let i = 0; i < this.tableData.length; i++) {
          let splitHuiZhenZKMC = this.splitMapper(this.tableData[i].huiZhenZKMC)
          let splitShenQingHZYSXM = this.splitMapper(this.tableData[i].shenQingHZYSXM)
          let splitZhiCheng = this.splitMapper(this.tableData[i].zhiCheng)
          for (let j = 0; j < splitHuiZhenZKMC.length; j++) {
            let d = { ...this.tableData[i] }
            d.huiZhenZKMC = splitHuiZhenZKMC[j]
            d.shenQingHZYSXM = splitShenQingHZYSXM[j]
            d.zhiCheng = splitZhiCheng[j]
            if (j === 0) {
              d.size = splitHuiZhenZKMC.length
            }
            processedData.push(d)
          }
        }
        this.tableData = processedData
        console.log(this.tableData)
      }
    },
    async updateConsulationYWCSPXX(item) {
      console.log(item)
      let res = await updateConsulationYWCSPXX({
        huiZhenDanID: item.huiZhenDanID,
        shenPiBZ: item.yiWuSPBZ,
        shenPiJG: item.yiWuSPJG
      })
      if (res.hasError === 0) {
        this.$message.success('审批成功')
      }
    },
    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }
      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            会诊单ID: item.huiZhenDanID,
            多学科会诊申请时间: item.shenQingSJ,
            初步诊断: item.chuBuZD,
            申请科室: item.shenQingZKMC,
            患者姓名: item.bingRenXM,
            病案号: item.bingAnHao,
            床位: item.bingQuCW,
            会诊科室: item.huiZhenZKMC,
            会诊医师: item.shenQingHZYSXM,
            职称: item.zhiCheng,
            人员库ID: item.renYuanKuID,
            会诊小结医师: item.huiZhenXJYS,
            小结医生职称: item.huiZhenXJYSZC,
            小结医生人员库ID: item.renYuanKuID_XJ
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `多学科会诊审批导出_${format(new Date(), 'yyyy-MM-dd')}.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
  .title {
    position: relative;
    color: #171c28;
    font-size: 14px;
    line-height: 14px;
    font-weight: bold;
    margin-left: 9px;
  }
  .title::before {
    position: absolute;
    left: -9px;
    width: 3px;
    height: 14px;
    content: '';
    background-color: #356ac5;
  }
}

.duoXueKeDialog {
  .title {
    position: relative;
    color: #171c28;
    font-size: 16px;
    line-height: 14px;
    margin-left: 9px;
  }
  .title::before {
    content: url('~@/assets/images/info.png');
    position: absolute;
    top: -12px;
    left: -29px;
    transform: scale(0.55);
  }
}
</style>
