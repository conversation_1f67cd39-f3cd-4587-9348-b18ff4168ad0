<!-- 药占比查询 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <el-radio v-model="leiBie" label="1">按专科</el-radio>
        <el-radio v-model="leiBie" label="3">按治疗组</el-radio>
        <span class="search-label">时间范围:</span>
        <el-date-picker
          v-model="searchDate"
          style="margin-right: 10px"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd"
        />
        <el-checkbox v-model="bingRenLB" :true-label="'1'" :false-label="'0'">社保病人</el-checkbox>
        <el-button type="primary" @click="getRecord">查询</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            药占比报表
          </div>
        </div>
        <div class="table-component">
          <el-table :data="currentList" style="width: 100%" stripe border>
            <el-table-column
              v-for="(column, index) in columns"
              :key="index"
              :prop="column.value"
              :label="column.label"
              v-bind="column.props"
            >
              <template v-if="column.value === 'shouRuHZVoList'" #default="{ row }">
                {{ getShouRuValue(row, column.label) }}
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="total, prev, pager, next"
            :current-page.sync="currentPage"
            :page-size="12"
            :pager-count="5"
            :total="dataList.length"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import * as XLSX from 'xlsx'
import { getYaoZhanBiBB } from '@/api/medical-quality'
export default {
  name: 'MedicalTurn',
  data() {
    return {
      visible: false,
      currentPage: 1,
      searchDate: [],
      leiBie: '1',
      bingRenLB: '0',
      dataList: [],
      columns: []
    }
  },
  computed: {
    currentList() {
      const start = (this.currentPage - 1) * 12
      return this.dataList.slice(start, start + 12)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async getRecord() {
      const res = await getYaoZhanBiBB({
        kaiShiSJ: this.searchDate[0] + ' 00:00:00', //开始时间
        jieShuSJ: this.searchDate[1] + ' 00:00:00', //结束时间
        leiBie: this.leiBie,
        bingRenLB: this.bingRenLB
      })
      console.log('药占比报表: ', res)
      if (res.hasError === 0) {
        let dynamicColumns = []
        if (res.data[0] && res.data[0].shouRuHZVoList?.length) {
          dynamicColumns = res.data[0].shouRuHZVoList.map((item) => {
            return { value: 'shouRuHZVoList', label: item.mingCheng }
          })
        }
        this.columns = [
          { value: 'zhuanKeMC', label: '专科' },
          ...dynamicColumns,
          { value: 'dianHaiChunHDFC', label: '碘海醇和碘佛醇' },
          { value: 'yaoZhanBiBFB', label: '药品占比' },
          { value: 'xiYaoZB', label: '西药占比' },
          { value: 'kangJunYaoZBBFB', label: '抗菌药物占比' },
          { value: 'chuYuanRC', label: '出院人次(本地居保)' },
          { value: 'zhuYuanZFY', label: '出院总费用(本地居保)' },
          { value: 'junCiFY', label: '均次费用(本地居保)' }
        ]
        this.dataList = res.data
      }
    },
    getShouRuValue(row, label) {
      let value = 0
      if (row.shouRuHZVoList && row.shouRuHZVoList.length) {
        const shouRuHZVo = row.shouRuHZVoList.find((item) => {
          return item.mingCheng === label
        })
        value = shouRuHZVo?.zongJinE || 0
      }
      return value.toFixed(2)
    },
    async initPage() {
      let date = new Date()
      this.searchDate = [
        this.dateFormat(new Date(date.setMonth(date.getMonth() - 1))),
        this.dateFormat()
      ]
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 100%;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 100%;
      }
    }
  }
}
</style>
