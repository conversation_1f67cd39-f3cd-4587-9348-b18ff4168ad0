<template>
  <!-- 营养评估单信息查询 -->
  <div class="container">
    <div class="filter-container">
      <div class="filter-row">
        <div>
          <span class="filter-label">选择专科：</span>
          <el-select
            v-model="queryParams.zhuanKeID"
            placeholder="请选择专科"
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in [{ buMenID: '', buMenMC: '所有专科' }, ...zhuanKeList]"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </div>
        <div>
          <span class="filter-label">时间范围：</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '12:59:59']"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </div>
      </div>
      <div class="filter-row">
        <div>
          <span class="filter-label">评估单类型：</span>
          <el-radio-group v-model="queryParams.pingGuDanLX" size="mini">
            <el-radio v-for="item in pingGuDanLXOptions" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio>
          </el-radio-group>
        </div>
        <div>
          <el-button type="primary" size="mini" class="purple-button" @click="handleQuery">
            查询
          </el-button>
          <el-button type="success" size="mini" @click="exportToExcel">导出Excel</el-button>
        </div>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">
        <span>营养评估单信息查询</span>
        <span class="total-count">共{{ tableData.length }}条数据</span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        row-key="bingAnHao"
      >
        <el-table-column prop="zhuanKeMC" label="专科名称"></el-table-column>
        <el-table-column prop="bingAnHao" label="病案号"></el-table-column>
        <el-table-column prop="xingMing" label="姓名"></el-table-column>
        <el-table-column prop="xingBie" label="性别"></el-table-column>
        <el-table-column prop="bingQuMC" label="病区名称"></el-table-column>
        <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
        <el-table-column prop="pingGuDanLX" label="评估单类型"></el-table-column>
        <el-table-column prop="pingGuJG" label="评估结果" show-overflow-tooltip></el-table-column>
        <el-table-column prop="diaoChaSJ" label="调查时间"></el-table-column>
        <el-table-column prop="diaoChaZheXM" label="调查者姓名"></el-table-column>
        <el-table-column prop="zongPingFen" label="总评分"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getNutritionRecordList } from '@/api/medical-quality'
import { format, startOfMonth } from 'date-fns'
import * as XLSX from 'xlsx'
import { mapState } from 'vuex'

export default {
  name: 'AssessmentFormQuery',
  data() {
    return {
      // 查询参数
      queryParams: {
        zhuanKeID: '',
        pingGuDanLX: 'rbSGA',
        kaiShiSJ: '',
        jieShuSJ: ''
      },
      dateRange: [],
      // 表格数据
      tableData: [],
      // 加载状态
      loading: false,
      // 评估单类型选项
      pingGuDanLXOptions: [
        { value: 'rbSGA', label: 'SGA评估单' },
        { value: 'rbPGSGA', label: 'PG-SGA评估单' },
        { value: 'rbPGD', label: '住院病人营养访视单' },
        { value: 'rbGLIM', label: 'GLIM营养评估单' },
        { value: 'rbMNASF', label: 'MNASF营养评估单' }
      ]
    }
  },
  computed: {
    ...mapState({
      zhuanKeList: ({ patient }) => patient.zhuanKeList
    })
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      // 设置默认时间范围：当月1号到当天
      const start = startOfMonth(new Date())
      const end = new Date()
      this.dateRange = [format(start, 'yyyy-MM-dd 00:00:00'), format(end, 'yyyy-MM-dd 23:59:59')]

      // 初始查询
      await this.handleQuery()
    },

    // 查询数据
    async handleQuery() {
      // 设置查询参数
      const params = { ...this.queryParams }
      if (this.dateRange && this.dateRange.length === 2) {
        params.kaiShiSJ = this.dateRange[0]
        params.jieShuSJ = this.dateRange[1]
      }

      try {
        this.loading = true
        const res = await getNutritionRecordList(params)
        this.loading = false

        if (res.hasError === 0 && res.data) {
          this.tableData = res.data
        } else {
          this.tableData = []
        }
      } catch (error) {
        this.loading = false
        this.$message.error(error.errorMessage)
        this.tableData = []
      }
    },

    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            专科名称: item.zhuanKeMC,
            病案号: item.bingAnHao,
            姓名: item.xingMing,
            性别: item.xingBie,
            病区名称: item.bingQuMC,
            床位号: item.chuangWeiHao,
            评估单类型: this.getPingGuDanLXLabel(item.pingGuDanLX),
            评估结果: item.pingGuJG,
            调查时间: item.diaoChaSJ,
            调查者姓名: item.diaoChaZheXM,
            总评分: item.zongPingFen
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `营养评估单信息查询_${format(new Date(), 'yyyy-MM-dd')}.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-weight: bold;
  min-width: 80px;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 114px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
