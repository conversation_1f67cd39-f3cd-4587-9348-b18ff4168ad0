<template>
  <!-- 营养风险筛查点评统计 -->
  <div class="container">
    <div class="filter-container">
      <div class="filter-col">
        <div class="filter-item">
          <span class="filter-label">选择专科：</span>
          <el-select
            v-model="queryParams.zhuanKeID"
            placeholder="请选择专科"
            filterable
            style="width: 200px"
            @change="handleZhuanKeChange"
          >
            <el-option
              v-for="item in [{ buMenID: '', buMenMC: '所有专科' }, ...zhuanKeList]"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="filter-col vertical">
        <div class="filter-item">
          <span class="filter-label">选择病区：</span>
          <el-select v-model="queryParams.bingQuID" placeholder="请选择病区" style="width: 200px">
            <el-option
              v-for="item in [{ buMenID: '', buMenMC: '所有关联病区' }, ...bingQuList]"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </div>
        <div class="filter-item">
          <span class="filter-label">选择治疗组：</span>
          <el-select
            v-model="queryParams.zhiLiaoZuID"
            placeholder="请选择治疗组"
            style="width: 200px"
          >
            <el-option
              v-for="item in [{ zhiLiaoZuID: '', zhiLiaoZuMC: '所有治疗组' }, ...zhiLiaoZuList]"
              :key="item.zhiLiaoZuID"
              :label="item.zhiLiaoZuMC"
              :value="item.zhiLiaoZuID"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="filter-col">
        <div class="filter-item">
          <span class="filter-label">时间范围：</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '12:59:59']"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </div>
      </div>
      <div class="filter-col">
        <div class="filter-item">
          <el-radio-group v-model="queryParams.selectType" size="mini">
            <template v-for="(item, index) in selectOptions">
              <el-radio v-if="!(index % 2)" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio>
            </template>
          </el-radio-group>
        </div>
        <div class="filter-item">
          <el-radio-group v-model="queryParams.selectType" size="mini">
            <template v-for="(item, index) in selectOptions">
              <el-radio v-if="index % 2" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio>
            </template>
          </el-radio-group>
        </div>
      </div>
      <div class="filter-col">
        <div class="filter-item">
          <el-button type="primary" size="mini" class="purple-button" @click="handleQuery">
            查询
          </el-button>
          <el-button type="success" size="mini" @click="exportToExcel">导出Excel</el-button>
        </div>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">
        <span>营养风险筛查点评统计</span>
        <span class="total-count">
          共{{ tableData.length }}条数据； 该条件下筛查正确率为：营养师点评：{{
            rightRateVo.yingYangSRightRate ? `${rightRateVo.yingYangSRightRate}%` : '--'
          }}({{ rightRateVo.yingYangSRight || 0 }}/{{ rightRateVo.yingYangSTotal || 0 }})；
          护士长点评：{{
            rightRateVo.huShiZRightRate ? `${rightRateVo.huShiZRightRate}%` : '--'
          }}({{ rightRateVo.huShiZRight || 0 }}/{{
            rightRateVo.huShiZTotal || 0
          }})；护士长交叉点评：{{
            rightRateVo.jiaoChaDPRightRate ? `${rightRateVo.jiaoChaDPRightRate}%` : '--'
          }}({{ rightRateVo.jiaoChaDPRight || 0 }}/{{ rightRateVo.jiaoChaDPTotal || 0 }})
        </span>
      </div>
      <el-table
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        row-key="id"
      >
        <el-table-column prop="zhuanKeMC" label="专科"></el-table-column>
        <el-table-column prop="bingAnHao" label="病案号"></el-table-column>
        <el-table-column prop="bingRenXM" label="姓名"></el-table-column>
        <el-table-column prop="xingBie" label="性别"></el-table-column>
        <el-table-column prop="bingQuMC" label="病区"></el-table-column>
        <el-table-column prop="chuangWeiHao" label="床位号"></el-table-column>
        <el-table-column prop="yingYangSDPJG" label="营养师点评结果"></el-table-column>
        <el-table-column
          prop="yingYangSYY"
          label="错误原因"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="yingYangSBZ" label="备注" show-overflow-tooltip></el-table-column>
        <el-table-column prop="yingYangSXGSJ" label="时间" width="85"></el-table-column>
        <el-table-column prop="yingYangSXM" label="营养师姓名"></el-table-column>
        <el-table-column prop="pingGuRYXM" label="调查者姓名"></el-table-column>
        <el-table-column prop="huShiSFZX" label="调查者是否知晓点评结果"></el-table-column>
        <el-table-column prop="huShiSFXG" label="调查者是否修改"></el-table-column>
        <el-table-column prop="huShiXGSJ" label="调查者修改时间" width="85"></el-table-column>
        <el-table-column prop="huShiZDPJG" label="护士长点评结果"></el-table-column>
        <el-table-column prop="huShiZYY" label="错误原因" show-overflow-tooltip></el-table-column>
        <el-table-column prop="huShiZBZ" label="备注" show-overflow-tooltip></el-table-column>
        <el-table-column prop="huShiZYHXM" label="护士长姓名"></el-table-column>
        <el-table-column prop="huShiZXGSJ" label="时间" width="85"></el-table-column>
        <el-table-column prop="jiaoChaDPJG" label="交叉点评结果"></el-table-column>
        <el-table-column
          prop="jiaoChaDPYY"
          label="错误原因"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="jiaoChaDPBZ" label="备注" show-overflow-tooltip></el-table-column>
        <el-table-column prop="jiaoChaDPYHXM" label="交叉点评护士长姓名"></el-table-column>
        <el-table-column prop="jiaoChaDPXGSJ" label="时间" width="85"></el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { getYlYyscdpList } from '@/api/medical-quality'
import { getBingqQuListByZhuanKeID, getZhiLiaoZuListByZhuanKeID } from '@/api/patient'
import { format, startOfMonth } from 'date-fns'
import * as XLSX from 'xlsx'
import { mapState } from 'vuex'
export default {
  name: 'ScreeningReviewStats',
  data() {
    return {
      // 查询参数
      queryParams: {
        zhuanKeID: '',
        bingQuID: '',
        zhiLiaoZuID: '',
        selectType: 1,
        kaiShiSJ: '',
        jieShuSJ: ''
      },
      dateRange: [],
      bingQuList: [],
      zhiLiaoZuList: [],
      // 表格数据
      tableData: [],
      // 点评数据
      rightRateVo: {
        buMenID: 0, //部门ID
        buMenMC: '', //部门名称
        yingYangSTotal: 0, //营养师点评总数
        yingYangSRight: 0, //营养师点评正确数
        yingYangSWrong: 0, //营养师点评错误数
        yingYangSRightRate: '', //营养师点评正确率
        huShiZTotal: 0, //护士长点评总数
        huShiZRight: 0, //护士长点评正确数
        huShiZWrong: 0, //护士长点评错误数
        huShiZRightRate: '', //护士长点评正确率
        jiaoChaDPTotal: 0, //护士长交叉点评点评总数
        jiaoChaDPRight: 0, //护士长交叉点评点评正确数
        jiaoChaDPWrong: 0, //护士长交叉点评点评错误数
        jiaoChaDPRightRate: '' //护士长交叉点评点评正确率
      },
      // 加载状态
      loading: false,
      // 点评选项
      selectOptions: [
        { value: 1, label: '点评合理' },
        { value: 2, label: '点评不合理' },
        { value: 3, label: '营养师点评合理' },
        { value: 4, label: '营养师点评不合理' },
        { value: 5, label: '护士长点评合理' },
        { value: 6, label: '护士长点评不合理' },
        { value: 7, label: '交叉点评合理' },
        { value: 8, label: '交叉点评不合理' }
      ]
    }
  },
  computed: {
    ...mapState({
      zhuanKeList: ({ patient }) => patient.zhuanKeList,
      curZhuanKeID: ({ patient }) => Number(patient.initInfo.zhuanKeID)
    })
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化
    async init() {
      // 设置默认时间范围：当月1号到当天
      const start = startOfMonth(new Date())
      const end = new Date()
      this.dateRange = [format(start, 'yyyy-MM-dd 00:00:00'), format(end, 'yyyy-MM-dd 23:59:59')]
      this.queryParams.zhuanKeID = this.curZhuanKeID
      await this.getBingQuListByZhuanKeID()
      await this.getZhiLiaoZuListByZhuanKeID()

      // 初始查询
      await this.handleQuery()
    },

    // 根据专科ID获取病区列表
    async getBingQuListByZhuanKeID() {
      try {
        const res = await getBingqQuListByZhuanKeID({
          ZKID: this.queryParams.zhuanKeID
        })
        if (res.hasError === 0) {
          this.bingQuList = res.data || []
          if (this.bingQuList.length === 1) {
            this.queryParams.bingQuID = this.bingQuList[0].buMenID
          } else if (this.bingQuList.length > 1) {
            this.queryParams.bingQuID = ''
          }
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 根据专科ID获取治疗组列表
    async getZhiLiaoZuListByZhuanKeID() {
      try {
        const res = await getZhiLiaoZuListByZhuanKeID({
          zhuanKeID: this.queryParams.zhuanKeID
        })
        if (res.hasError === 0) {
          this.zhiLiaoZuList = res.data || []
          if (this.zhiLiaoZuList.length === 1) {
            this.queryParams.zhiLiaoZuID = this.zhiLiaoZuList[0].zhiLiaoZuID
          } else if (this.zhiLiaoZuList.length > 1) {
            this.queryParams.zhiLiaoZuID = ''
          }
        }
      } catch (error) {
        console.log(error)
      }
    },

    // 切换专科
    async handleZhuanKeChange(value) {
      this.queryParams.zhuanKeID = value
      if (this.queryParams.zhuanKeID) {
        await this.getBingQuListByZhuanKeID()
        await this.getZhiLiaoZuListByZhuanKeID()
      } else {
        this.queryParams.bingQuID = ''
        this.queryParams.zhiLiaoZuID = ''
        this.bingQuList = []
        this.zhiLiaoZuList = []
      }
    },

    // 查询数据
    async handleQuery() {
      // 设置查询参数
      const params = { ...this.queryParams }
      if (this.dateRange && this.dateRange.length === 2) {
        params.kaiShiSJ = this.dateRange[0]
        params.jieShuSJ = this.dateRange[1]
      }

      try {
        this.loading = true
        const res = await getYlYyscdpList(params)
        this.loading = false

        if (res.hasError === 0 && res.data) {
          this.tableData = res.data.detailList
          this.rightRateVo = res.data.rightRateVo
        }
      } catch (error) {
        this.loading = false
        console.error('查询数据失败:', error)
        this.$message.error(error.errorMessage)
        this.tableData = []
      }
    },

    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            专科: item.zhuanKeMC,
            病案号: item.bingAnHao,
            姓名: item.bingRenXM,
            性别: item.xingBie,
            病区: item.bingQuMC,
            床位号: item.chuangWeiHao,
            营养师点评结果: item.yingYangSDPJG,
            营养师错误原因: item.yingYangSYY,
            营养师备注: item.yingYangSBZ,
            营养师时间: item.yingYangSXGSJ,
            营养师姓名: item.yingYangSXM,
            调查者姓名: item.pingGuRYXM,
            调查者是否知晓点评结果: item.huShiSFZX,
            调查者是否修改: item.huShiSFXG,
            调查者修改时间: item.huShiXGSJ,
            护士长点评结果: item.huShiZDPJG,
            护士长错误原因: item.huShiZYY,
            护士长备注: item.huShiZBZ,
            护士长姓名: item.huShiZYHXM,
            护士长时间: item.huShiZXGSJ,
            交叉点评结果: item.jiaoChaDPJG,
            交叉点评错误原因: item.jiaoChaDPYY,
            交叉点评备注: item.jiaoChaDPBZ,
            交叉点评护士长姓名: item.jiaoChaDPYHXM,
            交叉点评时间: item.jiaoChaDPXGSJ
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `营养风险筛查点评统计_${format(new Date(), 'yyyy-MM-dd')}.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
}

.filter-col {
  display: flex;
  padding: 0 8px;

  .filter-item {
    display: flex;
    align-items: center;
    justify-content: center;
    ::v-deep .el-radio-group {
      display: flex;
      flex-direction: column;
      .el-radio {
        line-height: 20px;
      }
    }
  }
}
.vertical {
  flex-direction: column;
  align-items: flex-end;
  justify-content: center;
  gap: 10px;
}

.filter-label {
  font-weight: bold;
  min-width: 80px;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 110px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .total-count {
    margin-left: auto;
    margin-right: auto;
  }
}
</style>
