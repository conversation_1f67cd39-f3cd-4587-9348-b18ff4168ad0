<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <div class="header-item-title">请选择查询条件:</div>
        <div class="header-item-radio">
          <el-radio-group v-model="chaXunFS">
            <el-radio label="1" size="large">
              <span class="search-label">当日在院病人</span>
            </el-radio>
            <el-radio label="2" size="large">
              <span class="search-label">当日出院病人</span>
            </el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-title">日期:</div>
        <div class="header-item-date">
          <el-date-picker v-model="riQi" type="date" />
        </div>
      </div>
      <div class="header-item">
        <div class="header-item-button"><el-button @click="onSerch">查询</el-button></div>
        <div class="header-item-button">
          <el-button style="background-color: #356ac5" @click="exportToExcel">导出</el-button>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">专科当日在院或出院病人查询</div>
      </div>
      <div class="table">
        <el-table width="1285" max-height="648" stripe border :data="processedData">
          <el-table-column
            v-for="obj in tableData"
            :key="obj.value"
            :prop="obj.value"
            :width="obj.width"
            :label="obj.label"
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { getZhuanKeDRZYLB } from '@/api/specialist-discharge-inpatient-inquiry'
import { format } from 'date-fns'
export default {
  data() {
    return {
      chaXunFS: '1',
      riQi: '',
      processedData: [],
      tableData: [
        {
          label: '专科',
          value: 'zhuanKeMC',
          width: '90'
        },
        {
          label: '病案号',
          value: 'bingAnHao',
          width: '100'
        },
        {
          label: '姓名',
          value: 'bingRenXM',
          width: '90'
        },
        {
          label: '性别',
          value: 'bingRenXB',
          width: '90'
        },
        {
          label: '年龄',
          value: 'nianLing',
          width: '90'
        },
        {
          label: '入院病区',
          value: 'ruYuanBQ',
          width: '100'
        },
        {
          label: '当前病区',
          value: 'dangQianBQ',
          width: '100'
        },
        {
          label: '床位号',
          value: 'chuangWeiHao',
          width: '90'
        },
        {
          label: '入院日期',
          value: 'ruYuanRQ',
          width: '150'
        },
        {
          label: '出院日期',
          value: 'chuYuanRQ',
          width: '150'
        },
        {
          label: '日期',
          value: 'xiuGaiSJ',
          width: '200'
        }
      ]
    }
  },
  async mounted() {},
  methods: {
    async onSerch() {
      const res = await getZhuanKeDRZYLB({
        chaXunFS: this.chaXunFS,
        riQi: format(this.riQi, 'yyyy-MM-dd HH:mm:ss')
      })
      if (res.hasError === 0) {
        this.processedData = res.data
      }
    },
    exportToExcel() {
      if (this.processedData.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const arr = this.processedData.map((item) => {
        let d = {}
        this.tableData.forEach((col) => {
          d[col.label] = item[col.value]
        })
        return d
      })
      const worksheet = XLSX.utils.json_to_sheet(arr)
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '专科当日在院或出院病人查询.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
    font-weight: bold;
  }
  .header-item-input {
    margin-right: 8px;
  }
  .header-item-date {
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 6px;
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
  max-width: 1255px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
