<template>
  <div>
    <default-dialog
      :visible.sync="dialogVisible"
      :title="'新增跨科治疗记录'"
      width="500px"
      @confirm="handleSubmit"
      @cancel="handleCancel"
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="small">
        <el-form-item label="病案号" prop="bingAnHao">
          <el-input
            v-model="form.bingAnHao"
            placeholder="请输入病案号"
            @keyup.enter.native="handleQueryPatient"
          ></el-input>
        </el-form-item>
        <el-form-item label="病人姓名" prop="bingRenXM">
          <el-input v-model="form.bingRenXM" disabled></el-input>
        </el-form-item>
        <el-form-item label="病人专科" prop="keShiMC">
          <el-input v-model="form.keShiMC" disabled></el-input>
        </el-form-item>
        <el-form-item label="选择科室" prop="kuaKeZKID">
          <el-select v-model="form.kuaKeZKID" placeholder="请选择跨科科室" style="width: 100%">
            <el-option
              v-for="item in departmentOptions"
              :key="item.buMenID"
              :label="item.buMenMC"
              :value="item.buMenID"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择医生" prop="kuaKeYSMC">
          <el-input v-model="form.kuaKeYSMC" readonly @click.native="handlePersonSelect"></el-input>
        </el-form-item>
        <el-form-item label="权限结束时间" prop="jieShuSJ">
          <el-date-picker
            v-model="form.jieShuSJ"
            type="datetime"
            placeholder="选择权限结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
          />
        </el-form-item>
        <el-form-item label="备注" prop="beiZhu">
          <el-input
            v-model="form.beiZhu"
            type="textarea"
            :rows="3"
            placeholder="请输入备注"
          ></el-input>
        </el-form-item>
      </el-form>
      <span style="color: red; margin-left: 20px">*为必填项</span>
    </default-dialog>
    <doctor-selector
      class="doctor-selector"
      :request="request"
      :zhuan-ke-m-c="form.kuaKeZKMC"
      :ben-zhuanke-i-d="form.kuaKeZKID.toString()"
      :is-ben-zhuanke="true"
      :visible.sync="doctorSelectorVisible"
      @handleChangeVisible="handleChangeVisible"
      @handleConfim="handleDoctorSelect"
    />
  </div>
</template>

<script>
import { getEzyblbrByEmpi, insertKuaKeZLJL, getAllBuMen } from '@/api/medical-quality'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import DoctorSelector from '@/components/PersonSelector/DoctorSelector.vue'
import request from '@/utils/request'
import { format } from 'date-fns'
import { mapState } from 'vuex'

export default {
  name: 'AddRecordDialog',
  components: {
    DefaultDialog,
    DoctorSelector
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      request,
      dialogVisible: false,
      form: {
        bingAnHao: '',
        bingLiID: '',
        bingRenXM: '',
        xingBie: '',
        zhuYuanHao: '',
        kuaKeZKID: '',
        kuaKeZKMC: '',
        kuaKeYSID: '',
        kuaKeYSMC: '',
        chuZhiYSID: '',
        chuZhiYSMC: '',
        beiZhu: '',
        zhuangTaiBZ: '1', // 默认启用状态
        leiBie: '1' // 默认类别
      },
      rules: {
        bingAnHao: [{ required: true, message: '请输入病案号', trigger: 'blur' }],
        kuaKeZKID: [{ required: true, message: '请选择跨科专科', trigger: 'change' }]
      },
      departmentOptions: [], // 专科选项
      loading: false,
      doctorSelectorVisible: false
    }
  },
  computed: {
    ...mapState({
      yongHuID: ({ user }) => user.yongHuID,
      yongHuXM: ({ user }) => user.yongHuXM
    })
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.init()
      }
    },
    'form.kuaKeZKID'(val) {
      if (val) {
        // 设置专科名称
        const dept = this.departmentOptions.find((item) => item.buMenID === val)
        if (dept) {
          this.form.kuaKeZKMC = dept.buMenMC
        }
      }
    }
  },
  methods: {
    // 初始化
    init() {
      this.resetForm()
      this.getBuMenList()
    },
    // 重置表单
    resetForm() {
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
      this.form = {
        bingAnHao: '',
        bingLiID: '',
        bingRenXM: '',
        xingBie: '',
        zhuYuanHao: '',
        kuaKeZKID: '',
        kuaKeZKMC: '',
        kuaKeYSID: '',
        kuaKeYSMC: '',
        chuZhiYSID: '',
        chuZhiYSMC: '',
        beiZhu: '',
        zhuangTaiBZ: '1', // 默认启用状态
        leiBie: '1' // 默认类别
      }
    },
    // 获取部门列表
    async getBuMenList() {
      try {
        const res = await getAllBuMen()
        if (res.hasError === 0 && res.data) {
          this.departmentOptions = res.data
        }
      } catch (error) {
        console.error('获取部门列表失败', error)
        this.$message.error('获取部门列表失败')
      }
    },
    // 根据病案号查询患者信息
    async handleQueryPatient() {
      if (!this.form.bingAnHao) return

      try {
        this.loading = true
        const res = await getEzyblbrByEmpi({ empi: this.form.bingAnHao })
        this.loading = false

        if (res.hasError === 0 && res.data && res.data.length > 0) {
          // 获取当前在院患者
          const patients = res.data.filter((item) => !item.chuYuanSJ)
          if (patients.length === 0) {
            await this.$alert('患者当前不是在院状态', '提示', {
              confirmButtonText: '确定',
              type: 'warning'
            })
            return
          }

          const patientInfo = patients[0]
          // 填充表单
          this.form.bingLiID = patientInfo.bingLiID
          this.form.bingRenXM = patientInfo.xingMing
          this.form.xingBie = patientInfo.xingBie
          this.form.zhuanKeID = patientInfo.zhuanKeID
          this.form.keShiMC = patientInfo.keShiMC
        } else {
          this.$message.warning('未找到患者信息')
        }
      } catch (error) {
        this.loading = false
        console.error('查询患者信息失败', error)
        this.$message.error('查询患者信息失败')
      }
    },
    // 打开选择医生
    handlePersonSelect() {
      if (!this.form.kuaKeZKID) {
        this.$message.warning('请先选择跨科专科')
        return
      }
      this.doctorSelectorVisible = true
    },
    handleChangeVisible(val) {
      this.doctorSelectorVisible = val
    },
    // 确认选择医生
    handleDoctorSelect(row) {
      this.form.kuaKeYSID = row.yongHuID
      this.form.kuaKeYSMC = row.xingMing
    },
    // 提交表单
    handleSubmit() {
      this.$refs.form.validate(async (valid) => {
        if (!valid) return

        try {
          this.loading = true
          // 添加当前用户ID和时间
          this.form.chuZhiYSID = this.yongHuID
          this.form.chuZhiYSMC = this.yongHuXM
          this.form.shouCiLRSJ = format(new Date(), 'yyyy-MM-dd HH:mm:ss')
          const params = { ...this.form }
          delete params.keShiMC
          const res = await insertKuaKeZLJL(params)
          this.loading = false

          if (res.hasError === 0) {
            this.$message.success('添加成功')
            this.$emit('success')
            this.handleCancel()
          } else {
            this.$message.error(res.errorMessage || '添加失败')
          }
        } catch (error) {
          this.loading = false
          console.error('添加跨科治疗记录失败', error)
          this.$message.error('添加跨科治疗记录失败')
        }
      })
    },
    // 取消
    handleCancel() {
      this.resetForm()
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="scss" scoped>
.el-form {
  padding: 10px 20px;
}
</style>
