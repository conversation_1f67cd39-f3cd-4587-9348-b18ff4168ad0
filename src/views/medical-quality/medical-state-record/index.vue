<!-- 病历状态修改记录 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <span class="search-label">解封状态:</span>

        <el-select v-model="jieFengLX" style="width: 170px; margin-right: 10px">
          <el-option
            v-for="item in [
              { label: '解封', value: '0' },
              { label: '封存 ', value: '1' },
              { label: '全部', value: '2' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <span class="search-label">选择专科:</span>
        <el-input
          :value="selectZhuanke.buMenMC"
          style="width: 170px; margin-right: 10px"
          @focus="openSelectZhuanke()"
        />
        <span class="search-label">时间范围:</span>
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd"
        />
        <!--
          <el-input
          style="width: 170px; margin-right: 10px"
        />
        -->
        <el-button type="primary" style="margin-right: 10px" @click="getRecordZhuanKe">
          查询
        </el-button>
        <span class="search-label">根据病案号查询:</span>
        <el-input v-model="bingAnHao" style="width: 170px; margin-right: 10px" />
        <el-button type="primary" @click="getRecordBingAnHao">查询</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            病历状态修改记录
          </div>
          <!-- <el-button
            type="primary"
            @click="
              openEditDrawer({
                shouShuID: 0,
                shouShuMC: '',
                icddm: '',
                shiFouBM: '',
                zhuangTaiBZ: true,
                shouShuSX: '',
                suoShuXT: '',
                shiFouBT: true
              })
            "
          >
            新增
          </el-button> -->
        </div>
        <div class="table-component">
          <el-table
            :data="currentList"
            style="width: 100%"
            stripe
            border
            @selection-change="() => {}"
          >
            <el-table-column
              v-for="(column, index) in columns"
              :key="index"
              :prop="column.value"
              :label="column.label"
              v-bind="column.props"
            >
              <template v-if="column.label === '解封状态'" #default="{ row }">
                {{ row.leiBie === '1' ? '封存' : '解封' }}
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="total, prev, pager, next"
            :current-page.sync="currentPage"
            :page-size="12"
            :pager-count="5"
            :total="dataList.length"
          ></el-pagination>
        </div>
      </div>
    </div>
    <zhuan-ke-dialog
      :visible.sync="visible"
      @confirm="
        (zhuanke) => {
          selectZhuanke = zhuanke
        }
      "
    />
  </div>
</template>

<script>
import { getBingLiZTXGJLByBingAnHao, getBingLiZTXGJLByZhuanKe } from '@/api/system-maintenance'
import ZhuanKeDialog from '@/components/Dialog/zhuanKeDialog.vue'
import { format } from 'date-fns'
export default {
  name: 'MedicalStateionRecord',
  components: {
    ZhuanKeDialog
  },
  data() {
    return {
      visible: false,
      currentPage: 1,
      searchValue: '',
      jieFengLX: '2',
      bingAnHao: '',
      searchDate: [],
      dataList: [],
      selectZhuanke: {
        buMenID: 0,
        buMenMC: '所有专科'
      }, //当前选择专科
      columns: [
        { value: 'chuYuanZK', label: '出院专科', props: { fixed: true } },
        { value: 'bingAnHao', label: '病案号', props: { fixed: true, width: '130' } },
        { value: 'bingRenXM', label: '病人姓名', props: { fixed: true } },
        { value: 'bingQuRYRQ', label: '入院日期', props: { fixed: true, width: '160' } },
        { value: 'bingQuCYRQ', label: '出院日期', props: { fixed: true, width: '160' } },
        { value: 'jieFengNR', label: '解封内容', props: { fixed: true } },
        { value: 'leiBie', label: '解封状态', props: { fixed: true } },
        { value: 'xiuGaiSJ', label: '解封时间', props: { fixed: true, width: '160' } },
        { value: 'xiuGaiRYXM', label: '解封人员', props: { fixed: true } }
      ]
    }
  },
  computed: {
    currentList() {
      const start = (this.currentPage - 1) * 12
      return this.dataList.slice(start, start + 12)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async getRecordZhuanKe() {
      const res = await getBingLiZTXGJLByZhuanKe({
        leiBie: this.jieFengLX,
        kaiShiSJ: this.searchDate[0] + ' 00:00:00', //开始时间
        jieShuSJ: this.searchDate[1] + ' 00:00:00', //结束时间
        zhuanKeID: this.selectZhuanke.buMenID //专科ID
      })
      console.log('住院医生站_病历查询_病历状态修改记录查询_根据专科: ', res)
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },
    async getRecordBingAnHao() {
      const res = await getBingLiZTXGJLByBingAnHao({
        bingAnHao: this.bingAnHao
      })
      console.log('住院医生站_病历查询_病历状态修改记录查询_根据病案号: ', res)
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },
    initPage() {
      let date = new Date()
      this.searchDate = [
        this.dateFormat(new Date(date.setDate(date.getDate() - 1))),
        this.dateFormat()
      ]
      console.log(this.searchDate)
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      return format(date, 'yyyy-MM-dd')
    },
    openSelectZhuanke() {
      this.visible = true
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 960px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 1080px;
      }
    }
  }
}
</style>
