<!-- 专家名单维护 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div style="display: flex; flex-direction: column; flex-grow: 1; width: 100%">
        <div class="head-search">
          <span class="search-label">查询科室:</span>
          <el-select
            v-model="zhuanKeID"
            style="width: 150px; margin-right: 10px"
            filterable
            clearable
          >
            <el-option
              v-for="item in zhuanKeOptions"
              :key="item.daiMa"
              :label="item.mingCheng"
              :value="item.daiMa"
            ></el-option>
          </el-select>
          <span class="search-label">查询专家名称:</span>
          <el-input v-model="searchValue" type="text" style="width: 170px; margin-right: 10px" />

          <el-button type="primary" @click="getdataList">查询</el-button>
        </div>
        <div class="table-background">
          <div class="table-title">
            <div>
              <span class="bar" />
              专家名单维护
            </div>
            <el-button type="primary" @click="visible = true">增加</el-button>
          </div>
          <div class="table-component">
            <el-table :data="currentList" style="width: 100%; flex-grow: 1" stripe border>
              <el-table-column
                v-for="(column, index) in columns"
                :key="index"
                :prop="column.value"
                :label="column.label"
                v-bind="column.props"
              >
                <template v-if="column.label === '状态/操作'" #default="{ row }">
                  <el-button type="text" @click="updateType(row)">
                    {{ row.zhuangTaiBZ === '1' ? '有效' : '暂停' }}
                  </el-button>
                  <el-divider direction="vertical" />
                  <el-button type="text" @click="deleteZhuanJia(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="total, prev, pager, next"
              :current-page.sync="currentPage"
              :page-size="pageSize"
              :pager-count="5"
              :total="dataList.length"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <default-dialog
      :visible="visible"
      width="25%"
      title="新增专家信息"
      @confirm="addZhuanJia"
      @cancel="visible = false"
      @open="initDialog()"
    >
      <span>
        <div class="dialog-component" style="max-height: 600px; overflow-y: auto">
          <el-form label-width="200px" :model="addForm">
            <el-form-item label="科室">
              <el-select
                v-model="addForm.zhuanKeID"
                style="width: 150px; margin-right: 10px"
                filterable
                clearable
              >
                <el-option
                  v-for="item in zhuanKeOptions"
                  :key="item.daiMa"
                  :label="item.mingCheng"
                  :value="item.daiMa"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="专家姓名拼音">
              <el-select
                v-model="addForm.yiShengYHID"
                style="width: 150px; margin-right: 10px"
                filterable
                remote
                reserve-keyword
                placeholder=""
                :remote-method="remoteMethod"
                :loading="loading"
              >
                <el-option
                  v-for="item in zhuanJiaOptions"
                  :key="item.yongHuID"
                  :label="item.xingMing"
                  :value="item.yongHuID"
                >
                  <span style="float: left">{{ item.yiShengDM }}</span>
                  <span style="float: right; font-size: 13px">
                    {{ item.xingMing }}
                  </span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </span>
    </default-dialog>
  </div>
</template>
<script>
import {
  getZjmdList,
  deleteZjmdByYhid,
  getZhuanKeList,
  insertZjmd,
  getExpertInfoList,
  updateZjmd
} from '@/api/medical-quality'
import { mapState } from 'vuex'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'

export default {
  name: 'ExpertService',
  components: {
    DefaultDialog
  },
  data() {
    return {
      loading: false,
      visible: false,
      zhuangTaiBZ: '0',
      searchDate: [],
      zhuanKeID: null,
      searchValue: '',
      zhuanKeOptions: [],
      zhuanJiaOptions: [],
      scjlList: [], //筛查记录列表
      dataList: [],
      schDate: null,
      addForm: {
        zhuanKeID: null,
        yiShengYHID: null
      },
      columns: [
        { value: 'zhuanKeMC', label: '科室' },
        { value: 'yiShengMC', label: '专家名称' },
        { value: 'xiuGaiSJ', label: '修改时间' },
        { label: '状态/操作', props: { align: 'center' } }
      ],
      currentPage: 1,
      pageSize: 10
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo
    }),
    currentList() {
      const start = (this.currentPage - 1) * this.pageSize
      return this.dataList.slice(start, start + this.pageSize)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async geizhuanKeOptions() {
      const res = await getZhuanKeList()
      if (res.hasError === 0) {
        this.zhuanKeOptions = res.data
      }
    },
    async initDialog() {
      this.addForm = {
        zhuanKeID: null,
        yiShengYHID: null
      }
    },
    async addZhuanJia() {
      if (this.addForm.zhuanKeID === null || this.addForm.yiShengYHID === null) {
        this.$message.error('科室或者专家姓名为空')
        return
      }
      const zhuanKe = this.zhuanKeOptions.find((item) => {
        return item.daiMa === this.addForm.zhuanKeID
      })
      const zhuanJia = this.zhuanJiaOptions.find((item) => {
        return item.yongHuID === this.addForm.yiShengYHID
      })
      const res = await insertZjmd({
        zhuanKeID: this.addForm.zhuanKeID,

        yiShengYHID: this.addForm.yiShengYHID, //医生用户ID
        zhuanKeMC: zhuanKe?.mingCheng, //专科名称
        zhuangTaiBZ: '1', //状态标志
        caoZuoZheID: this.userInfo.yongHuID, //操作者ID
        yiShengMC: zhuanJia?.xingMing, //医生名称
        yiShengPY: zhuanJia?.pinYin, //医生拼音
        xiuGaiSJ: this.dateFormat() //修改时间
      })
      if (res.hasError === 0) {
        this.visible = false
        this.$message.success('增加成功')
      }
    },
    async updateType(row) {
      await this.$confirm(`是否更新？`, '更新', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        const res = await updateZjmd({
          ...row,
          zhuangTaiBZ: row.zhuangTaiBZ === '0' ? '1' : '0', //状态标志
          xiuGaiSJ: this.dateFormat() //修改时间
        })
        if (res.hasError === 0) {
          this.$message.success('更新成功')
          this.getdataList()
        }
      })
    },
    async remoteMethod(query) {
      if (query !== '') {
        this.loading = true
        const res = await getExpertInfoList({
          zhuanJiaPY: query,
          zhuanJiaXM: query
        })
        this.loading = false
        if (res.hasError === 0) {
          this.zhuanJiaOptions = res.data
        }
      } else {
        this.options = []
      }
    },
    async getdataList() {
      const res = await getZjmdList({
        zhuanKeID: this.zhuanKeID,
        zhuanJiaPY: this.searchValue,
        zhuanJiaXM: this.searchValue
      })
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },
    deleteZhuanJia(row) {
      this.$confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await deleteZjmdByYhid({ yongHuID: row.yiShengYHID })
          if (res.hasError === 0) {
            this.$message.success('删除成功')
            this.getdataList()
          }
        })
        .catch(() => {
          // 取消删除，不做任何操作
        })
    },
    initPage() {
      let date = new Date()
      this.searchDate = [this.dateFormat(new Date(date.setDate(1))), this.dateFormat()]
      this.schDate = this.dateFormat()
      this.getdataList()
      this.geizhuanKeOptions()
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      const hours = date.getHours()
      const minutes = date.getMinutes()
      const seconds = date.getSeconds()
      return (
        year +
        '-' +
        (month < 10 ? '0' + month : month) +
        '-' +
        (day < 10 ? '0' + day : day) +
        ' ' +
        (hours < 10 ? '0' + hours : hours) +
        ':' +
        (minutes < 10 ? '0' + minutes : minutes) +
        ':' +
        (seconds < 10 ? '0' + seconds : seconds)
      )
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    padding: 10px;
    height: 100%;
    .page-right {
      margin-left: 5px;
      display: flex;
    }
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      margin-bottom: 10px;
      .head-row {
        margin-bottom: 5px;
      }
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
        margin-right: 10px;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        margin-top: 10px;
      }

      .table-left {
        width: 250px;
      }
    }
  }
}
</style>
