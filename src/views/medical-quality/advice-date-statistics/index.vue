<!-- 医嘱按时间间隔统计 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <span class="search-label">时间:</span>
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd"
          style="margin-right: 10px"
        />
        <span class="search-label">时间间隔:</span>
        <el-select v-model="shiJianJG" style="width: 170px; margin-right: 10px">
          <el-option
            v-for="item in [
              { label: '30分钟内', value: '30' },
              { label: '1小时内 ', value: '60' },
              { label: '2小时内', value: '120' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <span class="search-label">类型:</span>
        <el-select v-model="yaoPinLX" style="width: 170px; margin-right: 10px">
          <el-option
            v-for="item in [
              { label: '药品', value: 'yp' },
              { label: '治疗 ', value: 'zl' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="getdataList">查询</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span class="bar" />
            医嘱按时间间隔统计
          </div>
          <div style="color: #356ac5">
            合计比例：{{ rate }}
            <span style="color: #f35656">(时间间隔内总条数与时间间隔外总条数的比例)</span>
          </div>
        </div>
        <div class="table-component">
          <el-table
            :data="currentList"
            style="width: 100%"
            stripe
            border
            @selection-change="() => {}"
          >
            <el-table-column
              v-for="(column, index) in columns"
              :key="index"
              :prop="column.value"
              :label="column.label"
              v-bind="column.props"
            >
              <template v-if="column.label === '解封状态'" #default="{ row }">
                {{ row.leiBie === '1' ? '封存' : '解封' }}
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="total, prev, pager, next"
            :current-page.sync="currentPage"
            :page-size="12"
            :pager-count="5"
            :total="dataList.length"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getTongJiYzBySjjg } from '@/api/system-maintenance'
export default {
  name: 'MedicalDateStatistics',
  data() {
    return {
      currentPage: 1,
      rate: '',
      yaoPinLX: 'yp',
      shiJianJG: '30',
      searchDate: [],
      dataList: [],
      columns: [
        { value: 'bingAnHao', label: '病案号', props: { fixed: true, width: '130' } },
        { value: 'luRuSJ', label: '录入时间 ', props: { fixed: true, width: '160' } },
        { value: 'shouCiLRSJ', label: '首次录入时间', props: { fixed: true, width: '160' } },
        { value: 'yiZhuMC', label: '药品名', props: { fixed: true, width: '180' } },
        { value: 'jianGeSJ', label: '时间间隔(分钟)', props: { fixed: true } }
      ]
    }
  },
  computed: {
    currentList() {
      const start = (this.currentPage - 1) * 12
      return this.dataList.slice(start, start + 12)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async getdataList() {
      const res = await getTongJiYzBySjjg({
        leiXing: this.yaoPinLX,
        kaiShiSJ: this.searchDate[0] + ' 00:00:00', //开始时间
        jieShuSJ: this.searchDate[1] + ' 00:00:00', //结束时间
        pageIndex: 1,
        pageSize: 12,
        shiJianJG: this.shiJianJG
      })
      console.log('医嘱按时间间隔统计: ', res)
      if (res.hasError === 0) {
        this.dataList = res.data
        this.rate = res.extendData.rate
      }
    },
    initPage() {
      this.searchDate = [this.dateFormat(), this.dateFormat()]
      this.getdataList()
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 800px;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 800px;
      }
    }
  }
}
</style>
