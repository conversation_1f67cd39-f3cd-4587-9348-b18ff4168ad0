<!-- 病历上缴率 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <span class="search-label">出院日期:</span>
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd"
          style="margin-right: 10px"
        />
        <span class="search-label">上缴时限 **按工作日计算:</span>
        <el-select v-model="shangJiaoSX" style="width: 170px; margin-right: 10px">
          <el-option
            v-for="item in [
              { label: '2', value: '2' },
              { label: '3 ', value: '3' },
              { label: '5', value: '5' },
              { label: '7', value: '7' },
              { label: '10', value: '10' },
              { label: '15', value: '15' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <span class="search-label" style="margin-right: 10px">个工作日</span>
        <span class="search-label">{{ searchType === 'zk' ? '专科:' : '病区:' }}</span>
        <el-select v-model="zhuanKeBingQuID" style="width: 170px; margin-right: 10px">
          <el-option
            v-for="item in zhuanKeBingQuOptions"
            :key="item.daiMa"
            :label="item.mingCheng"
            :value="item.daiMa"
          ></el-option>
        </el-select>
        <el-select
          v-model="searchType"
          style="width: 170px; margin-right: 10px"
          @change="
            (type) => {
              geizhuanKeBingQuOptions(type)
            }
          "
        >
          <el-option
            v-for="item in [
              { label: '专科病历上缴率', value: 'zk' },
              { label: '病区病历上缴率 ', value: 'bq' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="getdataList">查询</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span class="bar" />
            病历上缴率
          </div>
        </div>
        <div class="table-component">
          <el-table
            :data="currentList"
            style="width: 100%"
            stripe
            border
            @selection-change="() => {}"
          >
            <el-table-column
              v-for="(column, index) in columns"
              :key="index"
              :prop="column.value"
              :label="column.label"
              v-bind="column.props"
            >
              <template v-if="column.label === '解封状态'" #default="{ row }">
                {{ row.leiBie === '1' ? '封存' : '解封' }}
              </template>
            </el-table-column>
          </el-table>
          * 总人数 = 有出院记录或二十四小时内入出院记录或病案首页的出院病人
          <br />
          上缴数 = 未超期上缴数 + 超期上缴数
          <br />
          超期数 = 超期上缴数 + 超期未上缴数
          <br />
          上缴率 = 上缴数/总人数
          <br />
          超期率 = 超期数/总人数
          <br />
          未超期上缴数(病理)：在未超期上缴数的基础上，出院后有新的病理报告，且辅检记录文书的记录日期在上缴时限内。
          <el-pagination
            background
            layout="total, prev, pager, next"
            :current-page.sync="currentPage"
            :page-size="8"
            :pager-count="5"
            :total="dataList.length"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getBingLiShangJiaoLvTJB, getBingLiShangJiaoLvTJBBQ } from '@/api/system-maintenance'
import { getZhuanKeList, getBingQuList } from '@/api/medical-quality'
export default {
  name: 'MedicalSubmitRatio',
  data() {
    return {
      currentPage: 1,
      rate: '',
      zhuanKeBingQuID: '0',
      shangJiaoSX: '3',
      zhuanKeBingQuOptions: [],
      searchDate: [],
      dataList: [],
      searchType: 'zk', //zk专科 bq病区
      columns: [
        { value: 'bingAnHao', label: '病案号', props: { fixed: true, width: '130' } },
        { value: 'zongRenShu', label: '总人数 ', props: { fixed: true, width: '160' } },
        { value: 'weiChaoQiSJS', label: '未超期上缴数', props: { fixed: true, width: '160' } },
        { value: 'weiChaoQiSJL', label: '未超期上缴率', props: { fixed: true, width: '160' } },
        { value: 'shangJiaoShu', label: '上缴数', props: { fixed: true, width: '180' } },
        { value: 'shangJiaoLv', label: '上缴率', props: { fixed: true } }
      ]
    }
  },
  computed: {
    currentList() {
      const start = (this.currentPage - 1) * 8
      return this.dataList.slice(start, start + 8)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async geizhuanKeBingQuOptions(type) {
      this.zhuanKeBingQuID = '0'
      if (type === 'zk') {
        const res = await getZhuanKeList()
        if (res.hasError === 0) {
          this.zhuanKeBingQuOptions = [
            {
              daiMa: '0',
              mingCheng: '所有专科'
            },
            ...res.data
          ]
        }
      } else if (type === 'bq') {
        const res = await getBingQuList()
        if (res.hasError === 0) {
          this.zhuanKeBingQuOptions = [
            {
              daiMa: '0',
              mingCheng: '所有病区'
            },
            ...res.data
          ]
        }
      }
    },
    async getdataList() {
      const res = await (this.searchType === 'zk'
        ? getBingLiShangJiaoLvTJB
        : getBingLiShangJiaoLvTJBBQ)({
        shangJiaoSX: this.shangJiaoSX,
        kaiShiSJ: this.searchDate[0] + ' 00:00:00', //开始时间
        jieShuSJ: this.searchDate[1] + ' 00:00:00', //结束时间
        zhuanKeBingQuID: this.zhuanKeBingQuID
      })
      console.log('住院医生站_病历查询_专科病历上缴率查询: ', res)
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },
    initPage() {
      let date = new Date()
      this.searchDate = [
        this.dateFormat(new Date(date.setMonth(date.getMonth() - 1))),
        this.dateFormat()
      ]
      this.geizhuanKeBingQuOptions('zk')
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 800px;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 800px;
      }
    }
  }
}
</style>
