<!-- 病危、死亡病人查询 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <span class="search-label">病人类型:</span>

        <el-select v-model="bingRenLX" style="width: 170px; margin-right: 10px">
          <el-option
            v-for="item in [
              { label: '死亡病人', value: '1' },
              { label: '疑难、危重病人', value: '2' },
              { label: '急危重症患者', value: '3' },
              { label: '非医嘱离院', value: '4' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <span class="search-label">选择专科:</span>
        <el-input
          :value="selectZhuanke.buMenMC"
          style="width: 170px; margin-right: 10px"
          @focus="openSelectZhuanke()"
        />
        <span class="search-label">选择院区:</span>

        <el-select v-model="yuanQu" style="width: 170px; margin-right: 10px">
          <el-option
            v-for="item in [
              { label: '全院', value: '' },
              { label: '公园路院区', value: '01' },
              { label: '南白象院区', value: '02' },
              { label: '龙港院区', value: '55' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <span class="search-label">
          <el-select v-model="shiJianLX">
            <el-option
              v-for="item in [
                { label: '出院时间', value: '1' },
                { label: '创建时间', value: '2' }
              ]"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          :
        </span>
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd"
        />
        <el-button type="primary" @click="getRecord">查询</el-button>
        <el-button type="primary" @click="exportToExcel">导出到Excel</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span />
            病危、死亡病人查询
          </div>
        </div>
        <div class="table-component">
          <el-table
            :data="currentList"
            style="width: 100%"
            stripe
            border
            @selection-change="() => {}"
          >
            <el-table-column
              v-for="(column, index) in columns"
              :key="index"
              :prop="column.value"
              :label="column.label"
              v-bind="column.props"
            >
              <template v-if="column.value === 'xingMing'" #default="{ row }">
                <el-button type="text" @click="handleClickPatient(row)">
                  {{ row.xingMing }}
                </el-button>
              </template>
              <template v-else-if="column.value === 'xingBie'" #default="{ row }">
                {{ row.xingBie === '1' ? '男' : '女' }}
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="total, prev, pager, next"
            :current-page.sync="currentPage"
            :page-size="12"
            :pager-count="5"
            :total="dataList.length"
          ></el-pagination>
        </div>
      </div>
    </div>
    <zhuan-ke-dialog
      :visible.sync="visible"
      @confirm="
        (zhuanke) => {
          selectZhuanke = zhuanke
        }
      "
    />
  </div>
</template>

<script>
import * as XLSX from 'xlsx'
import { getWeiZhongSiWangBRCX } from '@/api/system-maintenance'
import ZhuanKeDialog from '@/components/Dialog/zhuanKeDialog.vue'
export default {
  name: 'CriticalPatients',
  components: {
    ZhuanKeDialog
  },
  data() {
    return {
      visible: false,
      currentPage: 1,
      searchValue: '',
      bingRenLX: '1',
      shiJianLX: '1',
      yuanQu: '',
      searchDate: [],
      dataList: [],
      zhuanKeList: [],
      selectZhuanke: {
        buMenID: 0,
        buMenMC: '所有专科'
      }, //当前选择专科
      columns: [
        { value: 'bingAnHao', label: '病案号', props: { fixed: true, width: '130' } },
        { value: 'xingMing', label: '姓名', props: { fixed: true } },
        { value: 'zhuanKeMC', label: '专科', props: { fixed: true } },
        { value: 'xingBie', label: '性别', props: { fixed: true } },
        { value: 'chuShengRQ', label: '出生日期', props: { fixed: true, width: '160' } },
        { value: 'bingQuMC', label: '病区', props: { fixed: true } },
        { value: 'chuangWeiHao', label: '床位号', props: { fixed: true } },
        { value: 'ruYuanSJ', label: '入院日期', props: { fixed: true, width: '160' } },
        { value: 'chuYuanSJ', label: '死亡日期', props: { fixed: true, width: '160' } },
        { value: 'ruYuanZD', label: '入院诊断', props: { fixed: true } }
      ]
    }
  },
  computed: {
    currentList() {
      const start = (this.currentPage - 1) * 12
      return this.dataList.slice(start, start + 12)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async getRecord() {
      const res = await getWeiZhongSiWangBRCX({
        leiBie: this.bingRenLX,
        yuanQu: this.yuanQu,
        kaiShiSJ: this.searchDate[0] + ' 00:00:00', //开始时间
        jieShuSJ: this.searchDate[1] + ' 00:00:00', //结束时间
        shiJianLX: this.shiJianLX, //时间类型 1出院时间 2入院时间 3创建时间
        zhuanKeID: this.selectZhuanke.buMenID //专科ID
      })
      console.log('住院医生站_病历查询_危重病人记录查询: ', res)
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },
    async initPage() {
      let date = new Date()
      this.searchDate = [
        this.dateFormat(new Date(date.setDate(date.getDate() - 1))),
        this.dateFormat()
      ]
    },
    handleClickPatient(row) {
      const formatChuangWeiHao = (bingQuMC, chuangWeiHao) => {
        return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
      }
      this.$router.push({
        path: `/patient-detail/${row.bingLiID}`,
        query: {
          title: `${row.jieSuanLXMC || ''} ${formatChuangWeiHao(row.bingQuMC, row.chuangWeiHao)} ${
            row.xingMing
          }`
        }
      })
    },

    exportToExcel() {
      if (this.dataList.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const worksheet = XLSX.utils.json_to_sheet(
        this.dataList.map((data) => {
          let d = {}
          this.columns.forEach((col) => {
            if (col.value === 'xingBie') {
              d[col.label] = data[col.value] === '1' ? '男' : '女'
            } else {
              d[col.label] = data[col.value]
            }
          })
          return d
        })
      )
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '病危、死亡病人查询.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
    },
    openSelectZhuanke() {
      this.visible = true
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 960px;
        span {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 1200px;
      }
    }
  }
}
</style>
