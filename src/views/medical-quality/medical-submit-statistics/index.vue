<!-- 病历上缴统计表 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div class="head-search">
        <span class="search-label">请选择出院日期:</span>
        <el-date-picker
          v-model="searchDate"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd"
          style="margin-right: 10px"
        />
        <span class="search-label">类别:</span>
        <el-select v-model="type" style="width: 170px; margin-right: 10px">
          <el-option
            v-for="item in [
              { label: '监管医师', value: '1' },
              { label: '质控医师 ', value: '2' },
              { label: '质控护士', value: '3' }
            ]"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="getdataList">查询</el-button>
        <el-button type="primary" @click="exportToExcel">导出到Excel</el-button>
      </div>
      <div class="table-background">
        <div class="table-title">
          <div>
            <span class="bar" />
            监管医师监管病历统计表
          </div>
          <div>
            <span style="color: #f35656">**</span>
            按工作日计算
          </div>
        </div>
        <div class="table-component">
          <el-table
            :data="currentList"
            style="width: 100%"
            stripe
            border
            @selection-change="() => {}"
          >
            <el-table-column
              v-for="(column, index) in columns"
              :key="index"
              :prop="column.value"
              :label="column.label"
              v-bind="column.props"
            >
              <template v-if="column.label === '解封状态'" #default="{ row }">
                {{ row.leiBie === '1' ? '封存' : '解封' }}
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            background
            layout="total, prev, pager, next"
            :current-page.sync="currentPage"
            :page-size="12"
            :pager-count="5"
            :total="dataList.length"
          ></el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getJianGuanYSJGBLTJBCX } from '@/api/system-maintenance'
import { mapState } from 'vuex'
export default {
  name: 'AdviceSubmitStatistics',
  data() {
    return {
      currentPage: 1,
      type: '1',
      searchDate: [],
      dataList: [],
      columns: [
        { value: 'zhuanKeMC', label: '专科名称', props: { fixed: true, width: '160' } },
        { value: 'zongFengShu', label: '总份数', props: { fixed: true, width: '130' } },
        { value: 'sanRiNeiSJFS', label: '总人数 ', props: { fixed: true, width: '160' } },
        {
          value: 'weiChaoQiSJS',
          label: '病区三个工作日内上缴份数',
          props: { fixed: true, width: '220' }
        },
        { value: 'yongHuXM', label: '医师/护士名称', props: { fixed: true, width: '180' } },
        { value: 'bingLiFS', label: '病历份数', props: { fixed: true } }
      ]
    }
  },
  computed: {
    currentList() {
      const start = (this.currentPage - 1) * 12
      return this.dataList.slice(start, start + 12)
    },
    ...mapState({
      initInfo: ({ patient }) => patient.initInfo
    })
  },
  mounted() {
    this.initPage()
  },
  methods: {
    //导出excel
    exportToExcel() {
      if (this.dataList.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const worksheet = XLSX.utils.json_to_sheet(
        this.dataList.map((data) => {
          let d = {}
          this.columns.forEach((col) => {
            d[col.label] = data[col.value]
          })
          return d
        })
      )
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '病历上缴统计表.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    },
    async getdataList() {
      const res = await getJianGuanYSJGBLTJBCX({
        type: this.type,
        kaiShiSJ: this.searchDate[0] + ' 00:00:00', //开始时间
        jieShuSJ: this.searchDate[1] + ' 00:00:00', //结束时间
        zhuanKeID: this.initInfo.zhuanKeID
      })
      console.log('住院医生站_病历查询_专科病历上缴率查询: ', res)
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },
    initPage() {
      let date = new Date()
      this.searchDate = [
        this.dateFormat(new Date(date.setMonth(date.getMonth() - 1))),
        this.dateFormat()
      ]
      this.geizhuanKeBingQuOptions('zk')
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    flex-direction: column;
    padding: 10px;
    height: 100%;
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      margin-top: 10px;
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        width: 800px;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        margin-top: 10px;
        width: 800px;
      }
    }
  }
}
</style>
