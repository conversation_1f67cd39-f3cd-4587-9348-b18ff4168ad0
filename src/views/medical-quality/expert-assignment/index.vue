<!-- 专家分配 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div style="display: flex; flex-direction: column; flex-grow: 1; width: 100%">
        <div class="head-search">
          <div style="margin-bottom: 5px">
            <span class="search-label">状态:</span>

            <el-select
              v-model="zhuangTaiBZ"
              style="width: 170px; margin-right: 10px"
              filterable
              @change="getdataList"
            >
              <el-option
                v-for="item in [
                  { label: '未分配', value: '0' },
                  { label: '已分配', value: '1' }
                ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            未分配显示所有
          </div>
          <el-select v-model="shaiChaLX" style="width: 170px; margin-right: 10px" filterable>
            <el-option
              v-for="item in [
                { label: '药品医嘱', value: '0' },
                { label: '外购药品医嘱', value: '3' },
                { label: '特食医嘱', value: '5' }
              ]"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <span class="search-label">筛查时间:</span>
          <el-date-picker
            v-model="searchDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
            style="margin-right: 10px"
          />
          <span class="search-label">
            <el-select v-model="searchType" style="width: 170px" filterable>
              <el-option
                v-for="item in [
                  { label: '专家姓名', value: '0' },
                  { label: '药品姓名', value: '1' },
                  { label: '患者姓名', value: '2' }
                ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </span>
          <el-input v-model="searchText" type="text" style="width: 170px; margin-right: 10px" />

          <el-button
            type="primary"
            @click="
              getdataList({
                kaiShiSJ: searchDate[0] + ' 00:00:00', //开始时间
                jieShuSJ: searchDate[1] + ' 00:00:00' //结束时间
              })
            "
          >
            查询
          </el-button>
        </div>
        <div class="table-background">
          <div class="table-title">
            <div>
              <span class="bar" />
              医嘱
            </div>
            <el-button type="primary" @click="expertVisible = true">分配</el-button>
          </div>
          <div class="table-component">
            <el-table
              :data="currentList"
              style="width: 100%; flex-grow: 1"
              stripe
              border
              @selection-change="
                (rows) => {
                  selectList = rows
                }
              "
            >
              <el-table-column type="selection" width="40" />
              <el-table-column
                v-for="(column, index) in columns"
                :key="index"
                :prop="column.value"
                :label="column.label"
                v-bind="column.props"
              >
                <template v-if="column.label === '性别'" #default="{ row }">
                  {{ row.bingRenXB === '1' ? '男' : '女' }}
                </template>
                <template v-else-if="column.label === '是否复核'" #default="{ row }">
                  {{ row.zhuanJiaNR ? '是' : '否' }}
                </template>
                <template v-else-if="column.label === '点评'" #default="{ row }">
                  <el-tooltip class="item" effect="light" placement="bottom">
                    <div slot="content">
                      <table>
                        <tbody>
                          <tr>
                            <td class="info-label">筛查者点评:</td>
                            <td class="info-value">
                              {{
                                row.yaoXueBuNR && row.yaoXueBuNR.includes(';')
                                  ? row.yaoXueBuNR.split(';')[0]
                                  : ''
                              }}
                            </td>
                          </tr>
                          <tr>
                            <td class="info-label">筛查者建议:</td>
                            <td class="info-value">
                              {{
                                row.yaoXueBuNR && row.yaoXueBuNR.split(';').length > 1
                                  ? row.yaoXueBuNR.split(';')[1]
                                  : row.yaoXueBuNR
                              }}
                            </td>
                          </tr>
                          <tr>
                            <td class="info-label">医生点评:</td>
                            <td class="info-value"></td>
                          </tr>
                          <tr>
                            <td class="info-label">医生建议:</td>
                            <td class="info-value">
                              {{ row.yiShengNR }}
                            </td>
                          </tr>
                          <tr>
                            <td class="info-label">专家点评:</td>
                            <td class="info-value"></td>
                          </tr>
                          <tr>
                            <td class="info-label">专家建议:</td>
                            <td class="info-value">
                              {{ row.zhuanJiaNR }}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div style="cursor: pointer">
                      {{
                        row.yaoXueBuNR && row.yaoXueBuNR.includes(';')
                          ? row.yaoXueBuNR.split(';')[0]
                          : ''
                      }}
                    </div>
                  </el-tooltip>
                </template>
                <template v-else-if="column.label === '详情/查看病历'" #default="{ row }">
                  <el-button type="text" @click="openDetails(row)">详情</el-button>
                  <el-divider direction="vertical" />
                  <el-button type="text" @click="handleClickPatient(row)">查看病历</el-button>
                </template>
                <template v-else #default="{ row }">
                  {{ getRowValue(row, column.value) }}
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="total, prev, pager, next"
              :current-page.sync="currentPage"
              :page-size="pageSize"
              :pager-count="5"
              :total="dataList.length"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
    <default-dialog
      :visible="expertVisible"
      width="25%"
      title="分配专家"
      @confirm="assignZhuanJia"
      @cancel="expertVisible = false"
      @open="initExpertDialog()"
    >
      <span>
        <div class="dialog-component" style="max-height: 600px; overflow-y: auto">
          <el-form label-width="200px" :model="dialogForm">
            <el-form-item label="科室">
              <el-select
                v-model="dialogForm.zhuanKeID"
                style="width: 150px; margin-right: 10px"
                filterable
                @change="getZhuanJiaOptions"
              >
                <el-option
                  v-for="item in zhuanKeOptions"
                  :key="item.daiMa"
                  :label="item.mingCheng"
                  :value="item.daiMa"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="科室专家">
              <el-select
                v-model="dialogForm.zhuanJiaYHID"
                style="width: 150px; margin-right: 10px"
                filterable
              >
                <el-option
                  v-for="item in zhuanJiaOptions"
                  :key="item.yiShengYHID"
                  :label="item.yiShengMC"
                  :value="item.yiShengYHID"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </span>
    </default-dialog>
    <details-dialog :advice="currentAdvice" :visible.sync="detailsVisible" />
  </div>
</template>

<script>
import { getZjmdList, getAssignedYPYZ, getZhuanKeList, assignExpert } from '@/api/medical-quality'
import { mapState } from 'vuex'

import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import DetailsDialog from '@/components/AdviceComment/DetailsDialog.vue'

export default {
  name: 'ExpertAssignment',
  components: {
    DefaultDialog,
    DetailsDialog
  },
  data() {
    return {
      //弹窗控制
      expertVisible: false,
      detailsVisible: false,
      currentAdvice: null,

      zhuangTaiBZ: '0', //状态标志
      shaiChaLX: '0', //筛查类型
      searchType: '0', //搜索类型
      searchText: '', //搜索内容
      searchDate: [], //搜索时间
      dataList: [], //数据列表
      selectList: [], //选择列表
      zhuanKeOptions: [], //专科选项
      zhuanJiaOptions: [], //专家选项
      dialogForm: {
        zhuanKeID: null,
        zhuanJiaYHID: null
      },
      columns: [
        { value: 'empi', label: '病案号' },
        { value: 'xingMing', label: '姓名', props: { width: '80' } },
        { value: 'xingBie', label: '性别', props: { width: '60' } },
        { value: 'keShiMC', label: '专科' },
        { value: 'bingQuMC', label: '病区' },
        { value: 'ruYuanRQ', label: '入院日期', props: { width: '160' } },
        { value: 'chuYuanRQ', label: '出院日期', props: { width: '160' } },
        { value: 'yiShengYHXM', label: '医嘱医生' },
        { value: 'ruYuanZD', label: '入院诊断' },
        { value: 'zhuanJiaYHXM', label: '分配专家' },
        { label: '是否复核', props: { width: '60' } },
        { value: 'yiZhuMC', label: '医嘱名称' },
        { label: '点评', props: { align: 'center' } },
        { label: '详情/查看病历', props: { align: 'center' } }
      ],
      currentPage: 1,
      pageSize: 9
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo
    }),
    currentList() {
      const start = (this.currentPage - 1) * this.pageSize
      return this.dataList.slice(start, start + this.pageSize)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    async getZhuanJiaOptions(zhuanKeID) {
      this.dialogForm.zhuanJiaYHID = null
      const res = await getZjmdList({
        zhuanKeID
      })
      if (res.hasError === 0) {
        this.zhuanJiaOptions = res.data
      }
    },
    openDetails(row) {
      this.currentAdvice = row
      this.detailsVisible = true
    },
    initExpertDialog() {
      this.dialogForm = {
        zhuanKeID: null,
        zhuanJiaYHID: null
      }
    },
    assignZhuanJia() {
      if (!this.selectList.length) {
        this.$message.error('请选择一条信息分配')
        return
      }
      if (this.dialogForm.zhuanKeID === null || this.dialogForm.zhuanJiaYHID === null) {
        this.$message.error('科室或者专家姓名为空')
        return
      }
      for (let i = 0; i < this.selectList.length; i++) {
        const data = this.selectList[i]
        assignExpert({
          bingAnHao: data?.bingRenXX?.empi,
          bingLiID: data.bingLiID,
          // bingRenBH: '',
          bingRenXM: data?.bingRenXX?.xingMing,
          shaiChaID: data.shaiChaID,
          // shaiChaLX: '',
          // shaiChaSJ: '',
          yiZhuID: Number(data.yiZhuID),
          zhuanJiaYHID: this.dialogForm.zhuanJiaYHID,
          zhuanKeID: Number(this.dialogForm.zhuanKeID)
        }).then((res) => {
          if (res.hasError === 0) {
            this.expertVisible = false
            this.getdataList()
          }
        })
      }
    },
    async getdataList() {
      const res = await getAssignedYPYZ({
        endTime: this.searchDate[1] + ' 00:00:00', //结束时间
        startTime: this.searchDate[0] + ' 00:00:00', //开始时间
        searchText: this.searchText,
        searchType: this.searchType,
        shaiChaLX: this.shaiChaLX,
        zhuangTaiBZ: this.zhuangTaiBZ
      })
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },

    async getzhuanKeOptions() {
      const res = await getZhuanKeList()
      if (res.hasError === 0) {
        this.zhuanKeOptions = res.data
      }
    },
    initPage() {
      let date = new Date()
      this.searchDate = [this.dateFormat(new Date(date.setDate(1))), this.dateFormat()]
      this.getdataList()
      this.getzhuanKeOptions()
    },
    async handleClickPatient(row) {
      const formatChuangWeiHao = (bingQuMC, chuangWeiHao) => {
        return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
      }
      this.$router.push({
        path: `/patient-detail/${row.bingLiID}`,
        query: {
          title: `${row.jieSuanLXMC || ''} ${formatChuangWeiHao(
            row?.bingRenXX?.ziDingYM,
            row?.bingRenXX?.chuangWeiHao
          )} ${row?.bingRenXX?.xingMing}`
        }
      })
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
    },
    //获取表格渲染值
    getRowValue(row, key) {
      if (row[key]) {
        return row[key]
      } else if (row.bingRenXX) {
        return row.bingRenXX[key]
      } else {
        return ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    padding: 10px;
    height: 100%;
    .page-right {
      margin-left: 5px;
      display: flex;
    }
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      margin-bottom: 10px;
      .head-row {
        margin-bottom: 5px;
      }
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
        margin-right: 10px;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        margin-top: 10px;
      }

      .table-left {
        width: 250px;
      }
    }
  }
}
table {
  .info-label {
    padding: 5px;
    vertical-align: top;
    font-weight: 600;
  }
  .info-value {
    padding: 5px;
    width: 150px;
  }
}
</style>
