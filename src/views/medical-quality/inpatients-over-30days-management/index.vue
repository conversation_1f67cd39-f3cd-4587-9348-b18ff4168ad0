<template>
  <div class="container">
    <!-- 住院超30天患者管理 -->
    <div class="filter-container">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始时间"
        end-placeholder="结束时间"
        value-format="yyyy-MM-dd"
      ></el-date-picker>
      <el-select
        v-model="searchForm.zhuanKeID"
        placeholder="请选择专科"
        style="width: 200px; margin-left: 10px"
        clearable
      >
        <el-option
          v-for="item in zhuanKeOptions"
          :key="item.buMenID"
          :label="item.buMenMC"
          :value="item.buMenID"
        />
      </el-select>
      <el-button type="primary" size="mini" class="purple-button" @click="queryData">
        查询
      </el-button>
      <el-button type="primary" size="mini" @click="exportToExcel">导出</el-button>
    </div>

    <!-- 表格区域 -->
    <div class="table-container">
      <div class="title">住院超过30天患者</div>
      <el-table
        v-loading="loading"
        :data="pagedTableData"
        border
        stripe
        style="width: 1500px"
        height="calc(100% - 80px)"
        row-key="zhuYuanHao"
      >
        <el-table-column
          prop="zhuYuanHao"
          label="住院号"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="xingMing"
          label="患者姓名"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="bingQuMC"
          label="病区"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="chuangWeiHao"
          label="床位"
          width="80"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="zhuanKeMC"
          label="所在科室"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="zhiLiaoZuMC"
          label="当前治疗组"
          width="120"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="zhuGuanYSXM"
          label="主管医生"
          width="100"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="bingQuRYSJ"
          label="病区入院时间"
          width="150"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="ruYuanZD"
          label="入院诊断"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="ruYuanZD"
          label="住院超30天原因"
          min-width="200"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          :current-page.sync="currentPage"
          :page-sizes="[10, 20, 30, 40, 50]"
          :page-size.sync="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import * as XLSX from 'xlsx'
import { getOver30DaysStayZKList, getOver30DaysStayList } from '@/api/medical-quality'
import { format, subDays } from 'date-fns'

export default {
  name: 'InpatientsOver30days',
  data() {
    return {
      loading: false,
      // 查询表单
      searchForm: {
        zhuanKeID: ''
      },
      // 时间范围
      dateRange: [],
      // 专科选项
      zhuanKeOptions: [],
      // 表格数据
      tableData: [], // 所有数据
      // 分页配置
      currentPage: 1,
      pageSize: 20,
      total: 0
    }
  },
  computed: {
    // 分页后的表格数据
    pagedTableData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.tableData.slice(start, end)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    // 初始化页面
    async initPage() {
      // 设置默认时间范围（最近30天）
      const today = new Date()
      const thirtyDaysAgo = subDays(today, 30)
      this.dateRange = [format(thirtyDaysAgo, 'yyyy-MM-dd'), format(today, 'yyyy-MM-dd')]

      // 获取专科列表
      await this.getZhuanKeList()
    },

    // 获取专科列表
    async getZhuanKeList() {
      try {
        const res = await getOver30DaysStayZKList()
        if (res.hasError === 0) {
          this.zhuanKeOptions = [{ buMenID: '', buMenMC: '全部' }, ...res.data]
        }
      } catch (error) {
        this.$message.error(error.errorMessage)
      }
    },

    // 查询数据
    async queryData() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        this.$message.warning('请选择时间范围')
        return
      }

      this.loading = true
      try {
        const params = {
          kaiShiSJ: this.dateRange[0] + ' 00:00:00',
          jieShuSJ: this.dateRange[1] + ' 23:59:59',
          zhuanKeID: ''
        }

        // 如果选择了具体专科，添加专科ID参数
        if (this.searchForm.zhuanKeID) {
          params.zhuanKeID = this.searchForm.zhuanKeID
        }

        const res = await getOver30DaysStayList(params)
        if (res.hasError === 0) {
          this.tableData = res.data || []
          this.total = this.tableData.length
          this.currentPage = 1 // 重置到第一页
        }
      } catch (error) {
        this.$message.error(error.errorMessage)
        this.tableData = []
        this.total = 0
      } finally {
        this.loading = false
      }
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
    },

    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            住院号: item.zhuYuanHao || '',
            患者姓名: item.xingMing || '',
            病区: item.bingQuMC || '',
            床位: item.chuangWeiHao || '',
            所在科室: item.zhuanKeMC || '',
            当前治疗组: item.zhiLiaoZuMC || '',
            主管医生: item.zhuGuanYSXM || '',
            病区入院时间: item.bingQuRYSJ || '',
            入院诊断: item.ruYuanZD || '',
            住院超30天原因: item.chao30TianYY || ''
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `${this.searchForm.dateRange[0]}-${this.searchForm.dateRange[1]}住院超过30天患者.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  margin: 0 auto;
  padding: 10px;
  background: #fff;

  .purple-button {
    background: #a66dd4;
    border: 1px solid #a66dd4;
    &:hover,
    &:focus {
      background: #ce8be0;
      border-color: #ce8be0;
    }
  }
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
}

.table-container {
  height: calc(100% - 62px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
  }

  .pagination-container {
    max-width: 1500px;
    height: 58px;
    padding: 10px;
    background-color: #eaf0f9;
    text-align: right;
  }
}
</style>
