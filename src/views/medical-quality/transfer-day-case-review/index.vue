<template>
  <div class="container">
    <!-- 转日间审批 -->
    <div class="filter-container">
      <div class="filter-row">
        <div>
          <span class="filter-label">类别：</span>
          <el-radio-group v-model="queryParams.leiBie" size="mini">
            <el-radio label="1">普通转日间</el-radio>
            <el-radio label="2">日间转普通</el-radio>
          </el-radio-group>
        </div>
        <div>
          <span class="filter-label">时间范围：</span>
          <el-date-picker
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="yyyy-MM-dd HH:mm:ss"
          ></el-date-picker>
        </div>
      </div>
      <div class="filter-row">
        <div>
          <span class="filter-label">病案号：</span>
          <el-input
            v-model="queryParams.bingAnHao"
            placeholder="请输入病案号"
            style="width: 200px"
            clearable
          ></el-input>
        </div>
        <el-checkbox v-model="queryParams.shiFouZY" label="是否在院"></el-checkbox>
        <div>
          <el-button type="primary" size="mini" class="purple-button" @click="handleQuery">
            查询
          </el-button>
          <el-button type="success" size="mini" @click="exportToExcel">导出Excel</el-button>
        </div>
      </div>
    </div>

    <!-- 数据展示区域 -->
    <div class="table-container">
      <div class="title">转日间审批</div>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        stripe
        style="width: 100%"
        height="calc(100% - 30px)"
        row-key="id"
      >
        <el-table-column prop="leiBieMC" label="类别"></el-table-column>
        <el-table-column prop="zhuanKeMC" label="专科名称"></el-table-column>
        <el-table-column prop="bingAnHao" label="病案号"></el-table-column>
        <el-table-column prop="xingMing" label="病人姓名"></el-table-column>
        <el-table-column prop="bingQuRYRQ" label="病区入院时间" width="150"></el-table-column>
        <el-table-column prop="bingQuCyRQ" label="病区出院时间" width="150"></el-table-column>
        <el-table-column
          prop="bingAnSYSS"
          label="病案首页手术"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="riJianSSMC" label="日间状态"></el-table-column>
        <el-table-column prop="shenQingYHXM" label="申请人员"></el-table-column>
        <el-table-column prop="shenQingBZ" label="申请理由" show-overflow-tooltip></el-table-column>
        <el-table-column prop="shenQingSJ" label="申请时间" width="150"></el-table-column>
        <el-table-column prop="zhuangTaiBZMC" label="审批状态">
          <template #default="{ row }">
            <span v-if="row.zhuangTaiBZ === '1'">待审批</span>
            <span v-else-if="row.zhuangTaiBZ === '2'" style="color: green">同意</span>
            <span v-else-if="row.zhuangTaiBZ === '3'" style="color: red">拒绝</span>
          </template>
        </el-table-column>
        <el-table-column prop="shenPiYHXM" label="审批人"></el-table-column>
        <el-table-column prop="shenPiBZ" label="审批意见" show-overflow-tooltip></el-table-column>
        <el-table-column prop="shenPiSJ" label="审批时间" width="150"></el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="{ row }">
            <div v-if="row.zhuangTaiBZ === '1'">
              <el-button
                type="success"
                size="mini"
                class="actions"
                @click="handleApproval(row, '1')"
              >
                同意
              </el-button>
              <el-button
                type="danger"
                size="mini"
                class="actions"
                @click="handleApproval(row, '0')"
              >
                拒绝
              </el-button>
            </div>
            <span v-else class="no-approval">无需审批</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 审批理由弹窗 -->
    <default-dialog
      :visible.sync="approvalDialogVisible"
      :title="approvalDialogTitle"
      width="400px"
      @confirm="handleApprovalConfirm"
      @cancel="handleApprovalCancel"
    >
      <el-form ref="approvalForm" :model="approvalForm" :rules="approvalRules" label-width="100px">
        <el-form-item label="审批理由" prop="shenPiYJ">
          <el-input
            v-model="approvalForm.shenPiYJ"
            type="textarea"
            :rows="4"
            placeholder="请输入审批理由（不超过50个字符）"
            maxlength="50"
            show-word-limit
          ></el-input>
        </el-form-item>
      </el-form>
    </default-dialog>
  </div>
</template>

<script>
import { daySurgeryPatInfo, daySurgeryShenPi } from '@/api/medical-quality'
import { format, addDays, subDays } from 'date-fns'
import { mapState } from 'vuex'
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import * as XLSX from 'xlsx'

export default {
  name: 'TransferDayCaseReview',
  components: {
    DefaultDialog
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        leiBie: '1', // 默认普通转日间
        kaiShiSJ: '',
        jieShuSJ: '',
        shiFouZY: true, // 是否在院
        bingAnHao: '',
        zhuanKeID: ''
      },
      dateRange: [],
      // 表格数据
      tableData: [],
      // 加载状态
      loading: false,
      // 审批弹窗
      approvalDialogVisible: false,
      approvalDialogTitle: '',
      approvalForm: {
        shenPiYJ: '' // 审批意见
      },
      approvalRules: {
        shenPiYJ: [
          { required: true, message: '请输入审批理由', trigger: 'blur' },
          { max: 50, message: '审批理由不能超过50个字符', trigger: 'blur' }
        ]
      },
      // 当前审批的记录
      currentApprovalRow: null,
      currentApprovalType: '' // '1' 同意, '0' 拒绝
    }
  },
  computed: {
    ...mapState({
      zhuanKeID: ({ patient }) => patient.initInfo.zhuanKeID
    })
  },
  created() {
    this.init()
  },
  methods: {
    // 初始化
    init() {
      // 设置默认时间范围为3天前到明天
      const end = addDays(new Date(), 1)
      const start = subDays(new Date(), 3)
      this.dateRange = [format(start, 'yyyy-MM-dd 00:00:00'), format(end, 'yyyy-MM-dd 00:00:00')]

      // 初始查询
      this.handleQuery()
    },

    // 查询数据
    async handleQuery() {
      // 设置查询参数
      const params = { ...this.queryParams }
      if (this.dateRange && this.dateRange.length === 2) {
        params.kaiShiSJ = this.dateRange[0]
        params.jieShuSJ = this.dateRange[1]
      }
      params.shiFouZY = params.shiFouZY ? '1' : '0'
      params.zhuanKeID = this.zhuanKeID.toString()

      try {
        this.loading = true
        const res = await daySurgeryPatInfo(params)
        this.loading = false

        if (res.hasError === 0 && res.data) {
          this.tableData = res.data
        }
      } catch (error) {
        this.loading = false
        this.$message.error(error.message)
        this.tableData = []
      }
    },

    // 处理审批操作
    handleApproval(row, type) {
      this.currentApprovalRow = row
      this.currentApprovalType = type
      this.approvalDialogTitle = type === '1' ? '同意审批' : '拒绝审批'
      this.approvalForm.shenPiYJ = ''
      this.approvalDialogVisible = true
    },

    // 审批确认
    async handleApprovalConfirm() {
      try {
        await this.$refs.approvalForm.validate()

        const params = {
          ID: this.currentApprovalRow.id,
          bingLiID: this.currentApprovalRow.bingLiID,
          zhuYuanID: this.currentApprovalRow.zhuYuanID,
          shenPiZT: this.currentApprovalType,
          shenPiYJ: this.approvalForm.shenPiYJ
        }

        this.loading = true
        const res = await daySurgeryShenPi(params)
        this.loading = false

        if (res.hasError === 0) {
          this.$message.success('审批成功')
          this.approvalDialogVisible = false
          // 刷新列表
          await this.handleQuery()
        }
      } catch (error) {
        this.loading = false
        this.$message.error(error.message)
      }
    },

    // 审批取消
    handleApprovalCancel() {
      this.approvalDialogVisible = false
      this.currentApprovalRow = null
      this.currentApprovalType = ''
    },

    // 导出Excel
    exportToExcel() {
      if (this.tableData.length === 0) {
        this.$message.warning('暂无数据可导出')
        return
      }

      try {
        // 准备导出数据
        const exportData = this.tableData.map((item) => {
          return {
            类别: item.leiBieMC,
            专科名称: item.zhuanKeMC,
            病案号: item.bingAnHao,
            病人姓名: item.xingMing,
            状态: item.zhuangTaiBZMC,
            病区入院时间: item.bingQuRYRQ,
            病区出院时间: item.bingQuCyRQ,
            病案首页手术: item.bingAnSYSS,
            日间状态: item.riJianSSMC,
            申请人员: item.shenQingYHXM,
            申请理由: item.shenQingBZ,
            申请时间: item.shenQingSJ,
            审批人: item.shenPiYHXM,
            审批意见: item.shenPiBZ,
            审批时间: item.shenPiSJ
          }
        })

        // 创建工作表
        const worksheet = XLSX.utils.json_to_sheet(exportData)
        const workbook = XLSX.utils.book_new()
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

        // 生成文件名
        const fileName = `转日间审批_${format(new Date(), 'yyyy-MM-dd')}.xlsx`

        // 下载文件
        XLSX.writeFile(workbook, fileName)
        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;
  padding: 10px;
  background: #fff;
}

.filter-container {
  margin-bottom: 10px;
  padding: 10px;
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
}

.filter-row {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-label {
  font-weight: bold;
  min-width: 80px;
}

.purple-button {
  background: #a66dd4;
  border: 1px solid #a66dd4;
  &:hover,
  &:focus {
    background: #ce8be0;
    border-color: #ce8be0;
  }
}

.table-container {
  height: calc(100% - 114px);
  background-color: #eaf0f9;
  border-radius: 4px;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.15);
  padding: 10px;

  .title {
    height: 20px;
    margin-bottom: 10px;
    border-left: 4px solid #356ac5;
    padding-left: 8px;
    font-weight: bold;
  }
  ::v-deep .actions.el-button--mini {
    padding: 4px 12px;
  }
}

.no-approval {
  color: #999;
  font-size: 12px;
}
</style>
