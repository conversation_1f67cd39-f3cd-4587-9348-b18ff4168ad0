<!-- 医嘱点评统计 -->
<template>
  <div class="system-canvas">
    <div class="system-page">
      <div style="display: flex; flex-direction: column; flex-grow: 1; width: 100%">
        <div class="head-search">
          <div style="margin-bottom: 5px">
            <span class="search-label">状态:</span>

            <el-select
              v-model="zhuangTaiBZ"
              style="width: 170px; margin-right: 10px"
              filterable
              @change="getdataList"
            >
              <el-option
                v-for="item in [
                  { label: '筛查者-进行中', value: '0' },
                  { label: '筛查者-已提交(不合理)', value: '1' },
                  { label: '筛查者-已提交(合理)', value: 'A' },
                  { label: '医生-已提交(反对)', value: '2' },
                  { label: '医生-已提交(同意)', value: 'B' },
                  { label: '专家-已提交(医嘱不合理)', value: '3' },
                  { label: '专家-已提交(医嘱合理)', value: 'C' },
                  { label: '医务部-已提交(医嘱不合理)', value: '4' },
                  { label: '医务部-已提交(医嘱合理)', value: 'D' },
                  { label: '按流水号显示', value: 'Z' }
                ]"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <el-button type="primary">导出</el-button>
            <el-button type="primary">导出特定药品</el-button>
          </div>
          <el-select :value="'0'" style="width: 170px; margin-right: 10px" filterable>
            <el-option
              v-for="item in [{ label: '药品医嘱', value: '0' }]"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
          <span class="search-label">日期:</span>
          <el-date-picker
            v-model="searchDate"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
            style="margin-right: 10px"
          />

          <el-button
            type="primary"
            @click="
              getdataList({
                kaiShiSJ: searchDate[0] + ' 00:00:00', //开始时间
                jieShuSJ: searchDate[1] + ' 00:00:00' //结束时间
              })
            "
          >
            查询
          </el-button>
        </div>
        <div class="table-background">
          <div class="table-title">
            <div>
              <span class="bar" />
              病例
            </div>
          </div>
          <div class="table-component">
            <el-table :data="currentList" style="width: 100%; flex-grow: 1" stripe border>
              <el-table-column
                v-for="(column, index) in columns"
                :key="index"
                :prop="column.value"
                :label="column.label"
                v-bind="column.props"
              >
                <template v-if="column.label === '性别'" #default="{ row }">
                  {{ row.bingRenXB === '1' ? '男' : '女' }}
                </template>
                <template v-else-if="column.label === '状态/操作'" #default="{ row }">
                  <el-button type="text" @click="() => {}">有效</el-button>
                  <el-divider direction="vertical" />
                  <el-button type="text" @click="handleClickPatient(row)">删除</el-button>
                </template>
                <template v-else #default="{ row }">
                  {{ getRowValue(row, column.value) }}
                </template>
              </el-table-column>
            </el-table>
            <el-pagination
              background
              layout="total, prev, pager, next"
              :current-page.sync="currentPage"
              :page-size="pageSize"
              :pager-count="5"
              :total="dataList.length"
            ></el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getScjlListByTimeType,
  getSczdpYz,
  deleteShaiChaJL,
  getScmxByScid
} from '@/api/medical-quality'
import { mapState } from 'vuex'

export default {
  name: 'AdviceComment',
  data() {
    return {
      zhuangTaiBZ: '0',
      searchDate: [],
      scjlList: [], //筛查记录列表
      dataList: [],
      schDate: null,
      columns: [
        { value: 'keShiMC', label: '科室' },
        { value: 'bingQuMC', label: '专家名称' },
        { value: 'ruYuanRQ', label: '修改时间' },
        { label: '状态/操作', props: { align: 'center' } }
      ],
      pageSize: 11
    }
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.user.userInfo
    }),
    currentList() {
      const start = (this.currentPage - 1) * this.pageSize
      return this.dataList.slice(start, start + this.pageSize)
    }
  },
  mounted() {
    this.initPage()
  },
  methods: {
    //获取筛查记录
    async geiScjlList() {
      const startDate = new Date(new Date(this.schDate).setDate(1))
      const endDate = new Date(new Date(startDate).setMonth(startDate.getMonth() + 1))
      const res = await getScjlListByTimeType({
        endDate: this.dateFormat(endDate) + ' 00:00:00',
        startDate: this.dateFormat(startDate) + ' 00:00:00',
        shaiChaLX: '1'
      })
      if (res.hasError === 0) {
        this.scjlList = res.data.sort((a, b) => {
          return a.shaiChaSJ > b.shaiChaSJ ? -1 : 1
        })
      }
    },
    async getdataList(params = {}) {
      // const res = await getSczdpYz({
      //   yiShengYHID: this.userInfo.yongHuID,
      //   zhuangTaiBZ: this.zhuangTaiBZ,
      //   ...params
      // })
      // if (res.hasError === 0) {
      //   this.dataList = res.data
      // }
    },
    async getScmx(shaiChaID) {
      const res = await getScmxByScid({ shaiChaID })
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },
    deleteScjl(row) {
      this.$confirm('确定要删除该记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(async () => {
          const res = await deleteShaiChaJL({ shaiChaID: row.shaiChaID })
          if (res.hasError === 0) {
            this.$message.success('删除成功')
            this.geiScjlList()
          }
        })
        .catch(() => {
          // 取消删除，不做任何操作
        })
    },
    initPage() {
      let date = new Date()
      this.searchDate = [this.dateFormat(new Date(date.setDate(1))), this.dateFormat()]
      this.schDate = this.dateFormat()
      this.getdataList()
      this.geiScjlList()
    },
    async handleClickPatient(row) {
      const formatChuangWeiHao = (bingQuMC, chuangWeiHao) => {
        return bingQuMC && chuangWeiHao ? `${bingQuMC.replace(/病区$/, '')}-${chuangWeiHao}` : '空'
      }
      this.$router.push({
        path: `/patient-detail/${row.bingLiID}`,
        query: {
          title: `${row.jieSuanLXMC || ''} ${formatChuangWeiHao(
            row?.bingRenXX?.ziDingYM,
            row?.bingRenXX?.chuangWeiHao
          )} ${row.bingRenXM}`
        }
      })
    },
    //时间转换标准格式
    dateFormat(date = new Date()) {
      const year = date.getFullYear()
      const month = date.getMonth() + 1
      const day = date.getDate()
      return year + '-' + (month < 10 ? '0' + month : month) + '-' + (day < 10 ? '0' + day : day)
    },
    //获取表格渲染值
    getRowValue(row, key) {
      if (row[key]) {
        return row[key]
      } else if (key === 'bingAnHao') {
        return row.bingAnHao || row.bingRenXX.empi
      } else if (row.bingRenXX) {
        return row.bingRenXX[key]
      } else if (row.brxx) {
        return row.brxx[key]
      } else {
        return ''
      }
    }
  }
}
</script>

<style scoped lang="scss">
.system-canvas {
  background-color: #fff;
  width: 100%;
  height: 100%;
  .system-page {
    display: flex;
    padding: 10px;
    height: 100%;
    .page-right {
      margin-left: 5px;
      display: flex;
    }
    .head-search {
      padding: 16px;
      background-color: #eff3fb;
      border-radius: 4px;
      box-shadow: 0px 3px 3px 0px rgba(0, 0, 0, 0.1);
      margin-bottom: 10px;
      .head-row {
        margin-bottom: 5px;
      }
      .search-label {
        margin-right: 10px;
        font-weight: 600;
      }
      button {
        --color-primary: #a66dd4;
        margin-right: 10px;
      }
      :deep(.el-button--primary:hover),
      :deep(.el-button--primary:focus) {
        background: #ce8be0;
        border-color: #ce8be0;
      }
    }
    .table-background {
      flex-grow: 1;
      display: flex;
      flex-direction: column;
      padding: 10px;
      background-color: #eff3fb;
      border-radius: 2px;
      .table-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 600;
        .bar {
          border-left: 3px solid #356ac5;
          padding-left: 5px;
        }
      }
      .table-component {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        margin-top: 10px;
      }

      .table-left {
        width: 250px;
      }
    }
  }
}
</style>
