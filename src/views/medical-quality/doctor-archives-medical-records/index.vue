<template>
  <div class="container">
    <div class="header">
      <div class="header-item">
        <el-radio-group v-model="radio1">
          <el-radio label="1" size="large">
            <span class="search-label">住院号:</span>
            <el-input v-model="hospitalizationNumber" style="width: 170px" />
          </el-radio>
          <el-radio label="2" size="large">
            <span class="search-label">病历号(empi):</span>
            <el-input v-model="medicalRecordNumber" style="width: 170px" />
          </el-radio>
          <el-radio label="3" size="large">
            <span class="search-label">出院日期:</span>
            <el-date-picker
              v-model="dischargeDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="default"
            />
          </el-radio>
        </el-radio-group>
      </div>
      <div class="header-item">
        <div class="header-item-select">
          <el-select v-model="optionsValue" style="width: 150px">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div class="header-item-button"><el-button>查询</el-button></div>
        <!--        <div class="header-item-alert">-->
        <!--          <el-alert class="alert" title="未归档显示所有" type="info" show-icon />-->
        <!--        </div>-->
        <div class="filter-box-right">
          <div class="filter-box-right">
            <el-alert type="primary" show-icon plain size="small">未归档显示所有</el-alert>
          </div>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="content-header">
        <div class="title">医生归档病历查询</div>
      </div>
      <div class="table">
        <el-table max-height="648" border :data="tableData" style="width: 1250px">
          <el-table-column type="index" width="50" />
          <el-table-column prop="medicalRecordNo" width="130" label="病案号"></el-table-column>
          <el-table-column prop="name" width="130" label="姓名"></el-table-column>
          <el-table-column prop="sex" width="130" label="性别"></el-table-column>
          <el-table-column prop="specialist" width="150" label="专科"></el-table-column>
          <el-table-column prop="admissionDate" width="150" label="入院日期"></el-table-column>
          <el-table-column prop="dischargeDate" width="150" label="出院日期"></el-table-column>
          <el-table-column prop="submitDoctor" width="130" label="提交医生"></el-table-column>
          <el-table-column prop="submitNurse" width="130" label="提交护士"></el-table-column>
          <el-table-column width="98" label="操作" align="center">
            <template #default="scope">
              <el-button type="text" style="padding: 0" @click="handleClick(scope.row)">
                查看病人
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      radio1: '1',
      hospitalizationNumber: '',
      medicalRecordNumber: '',
      dischargeDate: '',
      options: [
        {
          value: '1',
          label: '医生未归档'
        },
        {
          value: '2',
          label: '医生已归档'
        }
      ],
      optionsValue: '1',

      tableData: [
        {
          medicalRecordNo: '**********',
          name: '黛雅',
          sex: '女',
          specialist: '风湿免疫科',
          admissionDate: '2023-07-12',
          dischargeDate: '2023-07-13',
          submitDoctor: '',
          submitNurse: ''
        },
        {
          medicalRecordNo: '0015',
          name: '海梅',
          sex: '女',
          specialist: '风湿免疫科',
          admissionDate: '2023-07-12',
          dischargeDate: '2023-07-13',
          submitDoctor: '',
          submitNurse: ''
        }
      ]
    }
  },
  methods: {
    handleClick(row) {
      console.log(row)
      console.log(this.dischargeDate)
    }
  }
}
</script>
<style lang="scss" scoped>
.container {
  background-color: #fff;
  padding: 12px;
  z-index: 999;
}
.header {
  display: flex;
  background-color: #eaf0f9;
  border-radius: 4px;
  padding: 12px 14px;
  margin-bottom: 12px;
}
.header-item {
  display: flex;
  align-items: center;
  margin-right: 8px;
  .search-label {
    margin-right: 10px;
  }
  ::v-deep .el-button {
    background-color: #a66dd4;
    border: 1px solid #a66dd4;
    color: #fff;
    padding-left: 15px;
    padding-right: 15px;
  }
  .header-item-title {
    color: #171c28;
    margin-right: 8px;
  }
  .header-item-select {
    flex: 0.5;
  }
  .header-item-button {
    margin-left: 8px;
    margin-right: 8px;
  }
  .filter-box-right {
    .el-alert {
      padding: 6px 10px;
      font-size: 12px;
      border: 1px solid rgba(21, 91, 212, 0.45);
      background-color: rgba(21, 91, 212, 0.05);
    }
  }
}
.content {
  background-color: #eaf0f9;
  padding: 14px;
}
.content-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 992px;
  margin-bottom: 14px;
}
.title {
  position: relative;
  color: #171c28;
  font-size: 14px;
  line-height: 14px;
  font-weight: bold;
  margin-left: 9px;
}
.title::before {
  position: absolute;
  left: -9px;
  width: 3px;
  height: 14px;
  content: '';
  background-color: #356ac5;
}
</style>
