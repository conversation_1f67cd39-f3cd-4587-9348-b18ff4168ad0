<template>
  <default-dialog
    :visible="visible"
    width="25%"
    title="病区选择"
    @confirm="confirmClick"
    @cancel="updateVisible(false)"
    @open="initDialog()"
  >
    <span>
      <div class="dialog-component" style="max-height: 600px; overflow-y: auto">
        <el-table
          :data="dataList"
          style="width: 100%; margin-top: 10px"
          stripe
          border
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="40" />
          <el-table-column
            v-for="(column, index) in [
              { value: 'mingCheng', label: '病区名称', props: { fixed: true } }
            ]"
            :key="index"
            :prop="column.value"
            :label="column.label"
            v-bind="column.props"
          ></el-table-column>
        </el-table>
      </div>
    </span>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { getBingQuList } from '@/api/medical-quality'

export default {
  name: 'SelectDrawer',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataList: [],
      searchValue: '',
      currentPage: 1,
      selectList: null,
      benZhuanKe: true
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },

    handleSelectionChange(rows) {
      this.selectList = rows.map((row) => {
        return {
          buMenMC: row.mingCheng,
          buMenID: Number(row.daiMa)
        }
      })
    },
    async initDialog() {
      this.searchValue = ''
      this.searchData()
    },
    async searchData() {
      const res = await getBingQuList()
      if (res.hasError === 0) {
        this.dataList = [
          {
            daiMa: '0',
            mingCheng: '所有病区'
          },
          ...res.data
        ].filter((item) => {
          return item.mingCheng?.indexOf(this.searchValue) > -1
        })
      }
    },
    confirmClick() {
      this.updateVisible(false)
      console.log(this.selectList)
      this.$emit('confirm', this.selectList)
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-component {
  padding: 0 10px;
  .search-head {
    button {
      --color-primary: #a66dd4;
    }
    :deep(.el-button--primary:hover),
    :deep(.el-button--primary:focus) {
      background: #ce8be0;
      border-color: #ce8be0;
    }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
}
</style>
