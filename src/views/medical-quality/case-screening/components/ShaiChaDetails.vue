<template>
  <default-dialog
    :visible="visible"
    width="70%"
    title="筛查明细"
    @confirm="updateVisible(false)"
    @cancel="updateVisible(false)"
    @open="initDialog()"
  >
    <span>
      <div class="dialog-component" style="max-height: 600px; overflow-y: auto">
        <el-button type="primary" @click="exportToExcel">导出到Excel</el-button>
        <el-table :data="dataList" style="width: 100%; margin-top: 10px" stripe border>
          <el-table-column
            v-for="(column, index) in columns"
            :key="index"
            :prop="column.value"
            :label="column.label"
            v-bind="column.props"
          >
            <template v-if="column.label === '性别'" #default="{ row }">
              {{ row.bingRenXB === '1' ? '男' : '女' }}
            </template>
          </el-table-column>
        </el-table>
      </div>
    </span>
  </default-dialog>
</template>

<script>
import DefaultDialog from '@/components/Dialog/DefaultDialog.vue'
import { getScmxByScid } from '@/api/medical-quality'

export default {
  name: 'SelectDrawer',
  components: {
    DefaultDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    shaiChaID: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      dataList: [],
      columns: [
        { value: 'bingAnHao', label: '病案号', props: { width: '130' } },
        { value: 'bingRenXM', label: '姓名' },
        { value: 'bingRenXB', label: '性别' },
        { value: 'zhuanKeMC', label: '专科', props: { width: '160' } },
        { value: 'bingQuMC', label: '病区' },
        { value: 'chuangWeiHao', label: '床位' },
        { value: 'ruYuanRQ', label: '入院日期', props: { width: '160' } },
        { value: 'chuYuanRQ', label: '出院日期', props: { width: '160' } },
        { value: 'ruYuanZD', label: '入院诊断', props: { width: '160' } },
        { value: 'zhuanJiaYHID', label: '分配专家 ' }
      ]
    }
  },
  methods: {
    updateVisible(visible) {
      this.$emit('update:visible', visible)
    },

    handleSelectionChange(rows) {
      this.selectList = rows.map((row) => {
        return {
          buMenMC: row.mingCheng,
          buMenID: Number(row.daiMa)
        }
      })
    },
    async initDialog() {
      this.getData()
    },
    async getData() {
      const res = await getScmxByScid({ shaiChaID: this.shaiChaID })
      if (res.hasError === 0) {
        this.dataList = res.data
      }
    },
    exportToExcel() {
      if (this.dataList.length == 0) {
        return this.$message.error('暂无数据可导出')
      }
      const worksheet = XLSX.utils.json_to_sheet(
        this.dataList.map((data) => {
          let d = {}
          this.columns.forEach((col) => {
            if (col.value === 'bingRenXB') {
              d[col.label] = data[col.value] === '1' ? '男' : '女'
            } else {
              d[col.label] = data[col.value]
            }
          })
          return d
        })
      )
      // return
      const workbook = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')

      try {
        XLSX.writeFile(workbook, '筛查记录明细.xlsx')
        this.$message.success('导出成功')
      } catch (e) {
        this.$message.error('导出失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-component {
  padding: 0 10px;
  .search-head {
    button {
      --color-primary: #a66dd4;
    }
    :deep(.el-button--primary:hover),
    :deep(.el-button--primary:focus) {
      background: #ce8be0;
      border-color: #ce8be0;
    }
    display: flex;
    align-items: center;
    label {
      margin: 0 10px 0 10px;
    }
  }
}
</style>
