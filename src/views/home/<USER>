<template>
  <div class="home-container"></div>
</template>

<script>
import request from '@/utils/request'
import { mapGetters, mapState } from 'vuex'

export default {
  name: 'Dashboard',
  components: {},
  data() {
    return {
      request
    }
  },
  computed: {
    ...mapGetters(['name']),
    ...mapState({
      yiLiaoJGDM: (state) => state.user.userInfo.yiLiaoJGDM,
      auth_token: (state) => state.user.token,
      access_code: (state) => state.user.yongHuID,
      access_dm: (state) => state.user.yiLiaoJGDM,
      routes: (state) => state.permission.routes,
      admin: (state) => state.user.userInfo.guanLiYuan,
      yingYongDM: (state) => state.user.systemID
    })
  },
  created() {},
  mounted() {
    window.addEventListener('message', this.handleMessage, false)
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage, false)
  },
  methods: {
    handleMessage(e) {
      if (e.data.source === 'quickMenu') {
        let path = ''
        this.routes.map((element) => {
          if (element.path === e.data.payload.path) {
            path = element.path
            return
          }

          if (element.children) {
            element.children.map((item) => {
              if (item.path === e.data.payload.path) {
                path = element.path + '/' + item.path
              }
            })
          }
        })
        this.$router.push({ path })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.home {
  &-container {
    //width: 100%;
    //height: 100%;
    //overflow: auto;
    background: white;

    .web-frame {
      display: block;
    }
  }

  &-hearder {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;

    .header-left {
      width: 100%;
    }
  }
}

.home-card {
  padding: 0;
}
</style>
