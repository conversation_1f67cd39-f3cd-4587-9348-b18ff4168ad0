import request from '@/utils/request'
const BaseURL = '/sysmanage'

/**
 * h获取日志管理list
 *  @搜索条件  {Object}
 */
export const logsGetList = (data) => {
  return request({
    url: `${BaseURL}/v1/logs/getList`,
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 添加日志
 *
 * @yingYongMC  应用名称 string
 * @yingYongDM  应用代码 string
 * @caoZuoLX  操作类型（1新增，2修改，3删除,4打开窗体，5查询，6登录、7登出） string
 * @caoZuoZheID  操作者ID string
 * @caoZuoSJ  操作时间 string
 * @id id string
 * @fuWuMing 服务名 string
 * @moKuaiMC 模块名称 string
 * @moKuaiDM 模块代码 string
 * @yuanZhi  原值 string
 * @xianZhi  现值 string
 * @xiangXiMS  数据类型（1基础数据，2医疗数据） string
 * @caoZuoJG 操作结果（1成功，0失败） string
 * @wangLuoHJ 网络环境 1内网，2，外网 string
 * @ip  ip string
 * @mac  mac string
 * @caoZuoZheXM 操作者姓名 string
 * @yuanCaoZuoZheXX 原操作者信息 string
 */
export const addAuditLog = (data) => {
  return request({
    url: `${BaseURL}/v1/logs/addAuditLog`,
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 *
 * @param appKey appKey
 * @param xiangXiMS 详细描述
 * @param shuJuLX 数据类型（1基础数据，2医疗数据）
 * @param 操作类型 （1新增，2修改，3删除,4打开窗体，5查询，6登录、7登出）
 * @returns
 */
export const addWebAuditLog = (data) => {
  return request({
    url: `${BaseURL}/v1/logs/addWebAuditLog`,
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 *
 * @param {string} params.caiDanID 菜单ID
 * @returns
 */
export const menuClickLog = (params) => {
  return request({
    url: `${BaseURL}/v1/menu/menuClick`,
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}
