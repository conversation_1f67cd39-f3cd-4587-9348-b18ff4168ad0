import request from '@/utils/request'
/**
 * 病人内页初始化
 * @param bingLiID 病历ID
 * @returns {*}
 */
export function PatientInit(bingLiID) {
  return request({
    url: '/app-emrservice/v1/mainpage/getInPatientInfoByBLID',
    method: 'get',
    params: { bingLiID }
  })
}

/**
 * 根据BLID获取病人提醒信息列表
 * @param bingQuID 病区ID
 * @param zhiLiaoZuID 治疗组ID
 * @param zhuanKeID 专科ID
 * @returns {*}
 */
export function getAlertMessages({ bingQuID, zhiLiaoZuID, zhuanKeID }) {
  return request({
    url: '/app-emrservice/v1/mainpage/getAlertMessages',
    method: 'get',
    params: { bingQuID, zhiLiaoZuID, zhuanKeID }
  })
}

/**
 * 获取最新评分结果汇总
 * @param bingLiID 病历ID
 * @returns {*}
 */
export function getAssessmentRecords(bingLiID) {
  return request({
    url: '/medicalrecord/v1/InPatient/getAssessmentRecords',
    method: 'get',
    params: { bingLiID },
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 根据BLID获取病人当前8小时内出入量平衡信息
 * @param bingLiID 病历ID
 * @returns {*}
 */
export function getFluidBalanceInfoByCalculate(bingLiID) {
  return request({
    url: '/patient/v1/inPatientDashboard/getFluidBalanceInfoByCalculate',
    method: 'get',
    params: { bingLiID },
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 设置病人出入量平衡（G）
 * @param bingLiID 病历ID
 * @param fuidBalanceSetValue 病人出入量平衡（G）设置值（ml）
 * @returns {*}
 */
export function setFluidBalance({ bingLiID, fluidBalanceSetValue }) {
  return request({
    url: '/patient/v1/inPatientDashboard/setFluidBalance',
    method: 'post',
    params: { bingLiID, fluidBalanceSetValue },
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_护理级别
export function getHuLiJBBaseInfo(zhuYuanID) {
  return request({
    url: `/medicaladvice/v1/treatment/getHuLiJBBaseInfo`,
    method: 'post',
    params: { zhuYuanID },
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_修改_护理级别
export function updateHuLiJB(data) {
  return request({
    url: `/medicaladvice/v1/treatment/updateHuLiJB`,
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据专科获取医疗人员库列表
export function getDoctorListByZKID(zhuanKeID) {
  return request({
    url: `/staff/v1/doctor/getDoctorListByZKID`,
    method: 'post',
    params: { zhuanKeID },
    headers: {
      verifyApp: false
    }
  })
}

// 根据ZYID设置病人主管医师
export function setZhuGuanYS({ zhuGuanYSID, zhuYuanID }) {
  return request({
    url: `/patient/v1/inPatientDashboard/setZhuGuanYS`,
    method: 'post',
    params: { zhuGuanYSID, zhuYuanID },
    headers: {
      verifyApp: false
    }
  })
}

// 设置在院病人治疗组ID
export function setZhiLiaoZuID({ zhiLiaoZuID, zhuYuanID }) {
  return request({
    url: `/patient/v1/inPatientDashboard/setZhiLiaoZuID`,
    method: 'post',
    params: { zhiLiaoZuID, zhuYuanID },
    headers: {
      verifyApp: false
    }
  })
}

// 根据blid列表获取对应的DRG状态列表
export function getSyDRGByJiBingIDs(data) {
  return request({
    url: '/medicalrecord/v1/BasicData/getSyDRGByJiBingIDs',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病历ID列表获取住院病人最新诊断
export function getLastDiagnoseByBingLiIDList(params) {
  return request({
    url: '/medicalrecord/v1/InPatient/getLastDiagnoseByBingLiIDList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据ZYID（IDB）获取病人计划出院时间
export function getJiHuaCYSJ(params) {
  return request({
    url: '/patient/v1/inPatientDashboard/getJiHuaCYSJ',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据ZYID（IDB）清除病人计划出院时间
export function clearJiHuaCYSJ(params) {
  return request({
    url: '/patient/v1/inPatientDashboard/clearJiHuaCYSJ',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据ZYID（IDB）设置病人计划出院时间
export function setJiHuaCYSJ(params) {
  return request({
    url: '/patient/v1/inPatientDashboard/setJiHuaCYSJ',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}
