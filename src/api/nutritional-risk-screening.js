import request from '@/utils/request'

// 根据BLID获取营养筛查初始化表单数据
export function getInitData(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getInitData',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 病人营养筛查记录获取（列表）
export function getYyscListByBLID(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getYyscListByBLID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据BLID列表获取最新营养筛查结果
export function getLastYYSCByBLIDList(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getLastYYSCByBLIDList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据ID返回营养筛查记录
export function getDYyscByID(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getDYyscByID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 保存或修改营养筛查记录
export function saveDYyscReturnWithID(data) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/saveDYyscReturnWithID',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 删除营养筛查记录
export function deleteDYyscByID(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/deleteDYyscByID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_按病历ID查询营养筛查完整记录
export function getYyscFullList(params) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/getYyscFullList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据ID和病历ID保存营养师点评记录
export function saveYingYangShiDP(data) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/saveYingYangShiDP',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据ID和病历ID保存护士长点评记录
export function saveHuShiZhangDP(data) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/saveHuShiZhangDP',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 根据ID和病历ID保存交叉点评记录
export function saveJiaoChaDP(data) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/saveJiaoChaDP',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 根据ID和病历ID保存评估人员信息反馈
export function savePingGuRYFK(data) {
  return request({
    url: '/medicalrecord/v1/NutritionRiskScreening/savePingGuRYFK',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
