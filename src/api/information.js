import request from '@/utils/request'

// 安全模块_查询_获取所有病区
export function getAllWardList(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getAllWardList',
    method: 'POST',
    params
  })
}

// 根据病人基本信息查询患者
export function getBinRenXXByParam(params) {
  return request({
    url: '/app-emrservice/v1/ezyblbr/getBinRenXXByParam',
    method: 'GET',
    params,
    timeout: 60 * 1000
  })
}

// 根据病人出院时间和诊断查询病人信息
export function getBinRenXXByZD(params) {
  return request({
    url: '/app-emrservice/v1/ezyblbr/getBinRenXXByZD',
    method: 'GET',
    params,
    timeout: 60 * 1000
  })
}

// 根据病人出入院时间和病区查询病人信息
export function getBinRenXXByTime(params) {
  return request({
    url: '/app-emrservice/v1/ezyblbr/getBinRenXXByTime',
    method: 'GET',
    params,
    timeout: 60 * 1000
  })
}

// 住院医生站_查询_获取出院病人数据根据治疗组
export function getZlzCybr(params) {
  return request({
    url: 'app-emrservice/v1/mrInPatient/getZlzCybr',
    method: 'POST',
    params
  })
}

// 根据条件查询YlYyyzd
export function getYyyzdListByCondition(data) {
  return request({
    url: 'medicaladvice/v1/Nutrition/getYyyzdListByCondition',
    method: 'POST',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 特殊使用级抗菌药物使用情况统计初始化
export function initTeShuKjywSyqkTj(params) {
  return request({
    url: 'medicaladvice/v1/DrugInpatient/initTeShuKjywSyqkTj',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 初始化【查看审批】特殊使用级抗菌药物使用情况统计
export function initCKSPTsKjywSyqkTj(params) {
  return request({
    url: 'medicaladvice/v1/DrugInpatient/initCKSPTsKjywSyqkTj',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 特殊使用级抗菌药物使用情况统计
export function teShuKjywSyqkTj(data) {
  return request({
    url: 'medicaladvice/v1/DrugInpatient/teShuKjywSyqkTj',
    method: 'POST',
    data,
    headers: {
      verifyApp: false
    },
    timeout: 5 * 60 * 1000
  })
}

// 【查看审批】特殊使用级抗菌药物使用情况统计
export function chaKanSPTsKjywSyqkTj(data) {
  return request({
    url: 'medicaladvice/v1/DrugInpatient/chaKanSPTsKjywSyqkTj',
    method: 'POST',
    data,
    headers: {
      verifyApp: false
    },
    timeout: 5 * 60 * 1000
  })
}

// 【备注】特殊使用级抗菌药物使用情况统计
export function saveBeiZhuTsKjywSyqkTj(data) {
  return request({
    url: 'medicaladvice/v1/DrugInpatient/saveBeiZhuTsKjywSyqkTj',
    method: 'POST',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 【审批】特殊使用级抗菌药物使用情况统计
export function shenPiTsKjywSyqkTj(data) {
  return request({
    url: 'medicaladvice/v1/DrugInpatient/shenPiTsKjywSyqkTj',
    method: 'POST',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据医嘱类别代码模糊查询医嘱目录
export function getYzmlByYzlbdmLikeRight(params) {
  return request({
    url: 'medicaladvice/v1/CheckTreat/getYzmlByYzlbdmLikeRight',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_治疗医嘱查询按病案号Cmclsfcx
export function getZhiLiaoYzByBahCmclsfcx(params) {
  return request({
    url: 'medicaladvice/v1/treatment/getZhiLiaoYzByBahCmclsfcx',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    },
    timeout: 5 * 60 * 1000
  })
}

// 查询_治疗医嘱查询按时间Cmclsfcx
export function getZhiLiaoYzByRqCmclsfcx(params) {
  return request({
    url: 'medicaladvice/v1/treatment/getZhiLiaoYzByRqCmclsfcx',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    },
    timeout: 5 * 60 * 1000
  })
}

// 获取ICU分级统计数据
export function getICUFenJiStatisticVo(params) {
  return request({
    url: 'app-emrservice/v1/statistics/getICUFenJiStatisticVo',
    method: 'GET',
    params
  })
}

// 获取在院患者出入量统计数据
export function getInPatientCRLStatisticList(params) {
  return request({
    url: 'app-emrservice/v1/statistics/getInPatientCRLStatisticList',
    method: 'GET',
    params
  })
}

// 根据时间获取胸痛中心患者信息报表
export function getRepPatList(params) {
  return request({
    url: 'app-greenchannel/v1/PatInfo/getRepPatList',
    method: 'POST',
    params
  })
}

// 住院医生站_病历查询_手机录入文本查询
export function getShouJiLR(params) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getShouJiLR',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_根据ID删除手机录入文本查询
export function deleteShouJiXXByID(params) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/deleteShouJiXXByID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询微生物无样可采理由(专科)
export function getWeiShengWuWykclyByZk(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getWeiShengWuWykclyByZk',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 查询微生物无样可采理由(病案号)
export function getWeiShengWuWykclyByBah(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getWeiShengWuWykclyByBah',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 住院医生站_查询_获取医疗人员资质医务处审批列表
export function getYiWuChuShenPiLieBiaoAll(data) {
  return request({
    url: '/app-emrservice/v1/Medicalqualification/getYiWuChuShenPiLieBiaoAll',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_更新_医务处审批医疗人员资质状态
export function updateShenPiZhuangTai(data) {
  return request({
    url: '/app-emrservice/v1/Medicalqualification/updateShenPiZhuangTai',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 病人病历状态修改_根据病案号查询病人
export function getListByEmpi(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/getListByEmpi',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 获取归档病历PDF查询记录
export function getAllBLCXByBingLiID(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/getAllBLCXByBingLiID',
    method: 'get',
    params
  })
}

// 【手术相关查询报表=>专科手术一览表】根据专科ID获取专科手术一览表
export function getDeptOperationListByZhuanKeID(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getDeptOperationListByZhuanKeID',
    method: 'GET',
    params
  })
}

// 【手术相关查询报表=>医师手术准入表】根据专科ID获取医师手术准入表
export function getSurgeonSurgeryPermissionList(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getSurgeonSurgeryPermissionList',
    method: 'GET',
    params
  })
}

// 【手术相关查询报表=>医师手术准入表】根据用户ID获取医生信息
export function getDoctorDataByYongHuID(params) {
  return request({
    url: '/staff/v1/doctor/getDoctorDataByYongHuID',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 【手术相关查询报表=>术前讨论月报表查询】术前讨论月报表查询
export function getSurgeryDiscussionDept(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getSurgeryDiscussionDept',
    method: 'GET',
    params
  })
}

// 【手术相关查询报表=>术前讨论科室报表查询】术前讨论科室报表查询
export function querySurgeryDiscussionByFullSql(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/querySurgeryDiscussionByFullSql',
    method: 'GET',
    params
  })
}

// 【手术相关查询报表=>非计划二次手术】非计划二次手术查询
export function getUnplannedSecondarySurgery(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getUnplannedSecondarySurgery',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取归档病历PDF打印记录
export function getAllBLDYByBingLiID(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/getAllBLDYByBingLiID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取书写病历查询
export function getShuXieBingLi(params) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getShuXieBingLi',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    },
    timeout: 60 * 1000
  })
}

// 住院医生站_会诊单_获取会诊排版基础数据
export function getBasicInfo(params) {
  return request({
    url: '/app-emrservice/v1/consulation/getBasicInfo',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_会诊单_获取会诊排版数据
export function getPaiBanData(params) {
  return request({
    url: '/app-emrservice/v1/consulation/getPaiBanData',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_根据【当前用户id】获取【医疗人员资质】
export function getMedicalqualificationById(params) {
  return request({
    url: '/app-emrservice/v1/Medicalqualification/getMedicalqualificationById',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_根据【用户id】获取【医疗人员资质】
export function getMedicalqualificationByYongHuId(params) {
  return request({
    url: '/app-emrservice/v1/Medicalqualification/getMedicalqualificationByYongHuId',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_根据类别医疗资质报表结构
export function getAllByLieBie(params) {
  return request({
    url: '/app-emrservice/v1/Medicalqualification/getAllByLieBie',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_更新_根据类别和代码修改一条的状态标志
export function updateBaoGaoJieGouZhuangTaiBZ(params) {
  return request({
    url: '/app-emrservice/v1/Medicalqualification/updateBaoGaoJieGouZhuangTaiBZ',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_新增医疗资质报表结构
export function insertBaoGaoJieGou(params) {
  return request({
    url: '/app-emrservice/v1/Medicalqualification/insertBaoGaoJieGou',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_更新医疗资质报表结构
export function updateBaoGaoJieGou(params) {
  return request({
    url: '/app-emrservice/v1/Medicalqualification/updateBaoGaoJieGou',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
