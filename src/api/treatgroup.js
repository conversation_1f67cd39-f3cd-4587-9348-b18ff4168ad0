import request from '@/utils/request'

// 住院医生站_知情记录查询
export function getZqjlListForSearch(data) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getZqjlListForSearch',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_专科知情记录_新增
export function addList(data) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/addList',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_专科文书格式维护_删除
export function deleteGS(data) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/delete',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_知情记录初始化_获取专科常用知情记录
export function getZqjlListInit(data) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getZqjlListInit',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
