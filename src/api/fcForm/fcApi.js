/** fcApi v1.0.6
 */
const iframeContainer = document.createElement('div')
iframeContainer.style.overflow = 'hidden'
iframeContainer.style.height = '0px'
let _iframeBase = '/e/v.html'
async function print(arr) {
  if (!iframeContainer.parentElement) {
    document.body.appendChild(iframeContainer)
  }
  const apis = []

  let content = ''
  for (let i = 0; i < arr.length; i++) {
    const data = arr[i]
    apis.push(new FcApi())
    var iframe = document.createElement('iframe')
    iframe.style = 'width: 100%;'
    iframeContainer.appendChild(iframe)
    const isBreak = data.isBreak
    delete data.isBreak
    await apis[i].init(iframe, _iframeBase, { ...data })
    await apis[i].setMode('print')

    content += `<div class=${isBreak ? 'page-break' : ''}>
        ${apis[i].iframe.contentDocument.documentElement.outerHTML}
        </div>
        `
  }

  const printWindow = window.open('', '_blank')
  printWindow.document.open()
  printWindow.document.write(`
            <html>
            <head>
                <title>打印多个Iframe</title>
                <style>
                @media print {
                    .page-break {
                    page-break-after: always;
                    }
                    .page-break:last-child {
                    page-break-after: auto;
                    }
                }
                </style>
            </head>
            <body>
        `)
  printWindow.document.write(content)
  printWindow.document.write(`
        </body>
        </html>
    `)
  printWindow.document.close()
  iframeContainer.innerHTML = ''
  setTimeout(() => {
    printWindow.print()
    printWindow.close() // 可选：打印后自动关闭
  }, 500)
}
export class FcApi {
  subform = null // 子表单信息 {name:'子表单名称', index: 可选，索引}
  eventId = null // 事件id
  isReady = false // 对话连接成功
  iframe = null // iframe dom
  resolveInit = null // 初始化回调
  resolveReady = [] // Ready 回调
  nID = 0 // 事件id
  events = [] // 事件队列 eventName, fn , offId 解除绑定ID，从fcForm返回
  // 事件相关： on、off、emit
  nanoid() {
    if (this.nID == 0) {
      this.nID = parseInt(new Date().getTime() % 1000000)
    } else {
      this.nID++
    }
    return this.nID + '-' + Math.floor(Math.random() * 10000)
  }
  on(eventName, fn) {
    if (typeof fn == 'function') {
      // 防止重复绑定
      if (!this.checkEvent(eventName, fn)) {
        const nId = this.postMessage('on', { eventName })
        this.events.push({ eventName: eventName, fn: fn, offId: nId })
      }
    }
  }
  off(eventName, fn) {
    this.events.map((item, index) => {
      if (
        (fn === undefined && item.eventName === eventName) ||
        (item.eventName === eventName && item.fn === fn)
      ) {
        this.events.splice(index, 1)
        this.postMessage('off', { eventName: eventName, offId: item.offId })
      }
    })
    if (fn === undefined) {
    }
  }
  checkEvent(eventName, fn) {
    let isExist = false
    this.events.map((item, index) => {
      if (
        (fn === undefined && item.eventName === eventName) ||
        (item.eventName === eventName && item.fn === fn)
      ) {
        isExist = true
        // Todo: ?? this.postMessage('off', {eventName:eventName})
      }
    })
    return isExist
  }
  async emit(eventName) {
    let rtn = null
    for (let i = 0; i < this.events.length; i++) {
      if (this.events[i].eventName == eventName) {
        let params = Array.prototype.slice.call(arguments, 1)
        rtn = await this.events[i].fn.apply(this, params)
      }
    }
    return rtn
  }

  emitOff(eventName) {
    this.events.map((item) => {
      if (item.eventName == eventName) {
        let params = Array.prototype.slice.call(arguments, 1)
        item.fn.apply(this, params)
        this.off(eventName, item.fn)
      }
    })
  }

  /** 发送消息给表单
   * @param name 消息名称
   * @param payload 消息内容
   */
  postMessage(name, payload, callback) {
    if (!this.isReady) return console.error('未连接 fcForm 表单')
    const nid = this.nanoid()
    this.iframe.contentWindow.postMessage(
      {
        name,
        payload,
        id: nid,
        subform: this.subform ? JSON.parse(JSON.stringify(this.subform)) : null
      },
      '*'
    )
    if (callback) {
      this.on(nid, callback)
    }
    return nid
  }

  /** 初始化
   * @param iframe iframe dom
   * @param baseUrl 表单地址
   * @param config 配置
   */
  async init(iframe, baseUrl, config = {}) {
    _iframeBase = baseUrl
    if (!iframe) return console.error('iframe 不能为空')
    this.eventId = new Date().getTime()
    this.events = []
    this.iframe = iframe
    this.isReady = false
    const that = this

    // 准备侦听
    if (!window._fc_formEvent) {
      // 统一侦听事件
      window._fc_formEvent = {}
      window.addEventListener('message', function (e) {
        if (e.data.eventId && window._fc_formEvent && window._fc_formEvent[e.data.eventId]) {
          window._fc_formEvent[e.data.eventId](e.data)
        } else {
          console.error('message no eventId', e.data, window._fc_formEvent)
          //that.dispatchEvent(e.data);
        }
      })
    }

    if (window._fc_formEvent[this.eventId]) {
      this.eventId = '-' + new Date().getTime()
    }

    window._fc_formEvent[this.eventId] = (data) => {
      // console.log('event', data)
      if (data.name === 'ready' && !that.isReady) {
        that.isReady = true
        // 初始化
        that.postMessage('updateForm', config, () => {
          if (that.resolveInit) {
            that.resolveInit()
            that.resolveInit = null
          }
          that.resolveReady.forEach((element) => {
            element()
          })
          that.resolveReady = []
        })
      }
      if (data.id) {
        that.emitOff(data.id, data.payload)
      }
      if (data.eventName) {
        that.emit(data.eventName, data.payload)
      }
    }

    // 初始化
    this.iframe.src = `${baseUrl}?e=${this.eventId}`
    return new Promise((resolve, reject) => {
      that.resolveInit = resolve
    })
  }

  // 等待Ready
  async ready() {
    if (this.isReady) return true
    const that = this
    return new Promise((resolve, reject) => {
      that.resolveReady.push(resolve)
    })
  }

  async close() {
    // 结束
    window._fc_formEvent[this.eventId] = null
  }

  async defaultFunc(name, params) {
    return new Promise((resolve) => {
      this.postMessage(name, JSON.parse(JSON.stringify(params)), (data) => {
        resolve(data)
      })
    })
  }

  ////////////////////  表单操作

  async withSubform(sub, func) {
    this.subform = sub
    const r = await func()
    this.subform = null
    return r
  }

  updateForm(config) {
    const payload = config || {}
    if (!payload) return
    this.postMessage('updateForm', payload)
  }

  async getRule() {
    return this.defaultFunc('getRule', arguments)
  }

  async getRefRule() {
    return this.defaultFunc('getRefRule', arguments)
  }

  async getOption() {
    return this.defaultFunc('getOption', arguments)
  }

  async getValue() {
    return this.defaultFunc('getValue', arguments)
  }

  async refresh() {
    return this.defaultFunc('refresh', arguments)
  }

  async reload() {
    return this.defaultFunc('reload', arguments)
  }

  async validate() {
    return this.defaultFunc('validate', arguments)
  }

  async changeStatus() {
    return this.defaultFunc('changeStatus', arguments)
  }

  async clearChangeStatus() {
    return this.defaultFunc('clearChangeStatus', arguments)
  }

  async fields() {
    return this.defaultFunc('fields', arguments)
  }

  async setValue() {
    return this.defaultFunc('setValue', arguments)
  }

  async resetFields() {
    return this.defaultFunc('resetFields', arguments)
  }

  async removeField() {
    return this.defaultFunc('removeField', arguments)
  }

  async hidden() {
    return this.defaultFunc('hidden', arguments)
  }

  async display() {
    return this.defaultFunc('display', arguments)
  }

  async hiddenStatus() {
    return this.defaultFunc('hiddenStatus', arguments)
  }

  async displayStatus() {
    return this.defaultFunc('displayStatus', arguments)
  }

  async disabled() {
    return this.defaultFunc('disabled', arguments)
  }

  async sync() {
    return this.defaultFunc('sync', arguments)
  }

  async method() {
    return this.defaultFunc('method', arguments)
  }

  async exec() {
    return this.defaultFunc('exec', arguments)
  }

  async trigger() {
    return this.defaultFunc('trigger', arguments)
  }

  async el() {
    return this.defaultFunc('el', arguments)
  }

  async wrapEl() {
    return this.defaultFunc('wrapEl', arguments)
  }

  async updateRule() {
    return this.defaultFunc('updateRule', arguments)
  }

  async updateRules() {
    return this.defaultFunc('updateRules', arguments)
  }

  async mergeRule() {
    return this.defaultFunc('mergeRule', arguments)
  }

  async mergeRules() {
    return this.defaultFunc('mergeRules', arguments)
  }

  async setEffect() {
    return this.defaultFunc('setEffect', arguments)
  }
  async setMode() {
    return this.defaultFunc('setMode', arguments)
  }
  // dark light
  setTheme(mode) {
    return this.defaultFunc('setTheme', arguments)
  }

  /**
   *
   * @param {*} arr
   * id 表单id
   * isBreak 是否分页
   * value 值
   * options 选项
   */
  static async prints(arr) {
    await print(arr)
  }
}

export default FcApi
