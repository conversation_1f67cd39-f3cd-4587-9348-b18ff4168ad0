import request from '@/utils/request'

// 查询_临床路径绩效报表初始化
export function initLcljJiXiaoBB(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/initLcljJiXiaoBB',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_临床路径绩效报表
export function getLcljJiXiaoBB(data) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getLcljJiXiaoBB',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_已完成路径的患者报表初始化
export function initYiWanChengLjHzBB(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/initYiWanChengLjHzBB',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_临床路径绩效报表
export function getYiWanChengLjHzBB(data) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getYiWanChengLjHzBB',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_临床路径管理报表初始化
export function initLcljGuanLiBB(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/initLcljGuanLiBB',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_临床路径管理报表
export function getLcljGuanLiBB(data) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getLcljGuanLiBB',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_临床路径符合诊断患者信息初始化
export function initLcljFuHeZdHzXx(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/initLcljFuHeZdHzXx',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_临床路径符合诊断患者信息
export function getLcljFuHeZdHzXx(data) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getLcljFuHeZdHzXx',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
