import request from '@/utils/request'
/**
 *  根据拼音或五笔获取诊断列表
 * @leiBie {string} 类别(PY/WB)
 * @pinYinOrWuBi {string} 拼音或者五笔
 */

export const getDiagnoseAllByBingAnBHAndMingCheng = (params) => {
  return request({
    url: `/app-doctorstation/v1/diagnose/getDiagnoseAllByBingAnBHAndMingCheng`,
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 *获取前后缀
 */
export function getPrefixOrSuffix() {
  return request({
    url: `/diagnoses/v1/diagnoses/getPrefixOrSuffix`,
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}
