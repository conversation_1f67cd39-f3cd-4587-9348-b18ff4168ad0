import request from '@/utils/request'

// 医嘱界面初始化
export function inpatientInit(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/initAdvice',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 获取病人住院医嘱
export function getInpatientOrders(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getBingRenYz',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取未提交医嘱
export function getUnCommitOrders(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getWeiTiJiaoYz',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 获取当日静脉输液量
export function getDangRiJmSyl(params) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getDangRiJmSyl',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 保存医嘱标题个性化排序
export function saveYzBiaoTiGxh(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/saveYzBiaoTiGxh',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 搜索医嘱
export function searchYiZhu(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/searchItems',
    method: 'post',
    data,
    globalLoading: false,
    headers: {
      verifyApp: false
    }
  })
}
// 获取当前时间
export function getCurrentDateTime(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getCurrentDateTime',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 获取药品基本使用方法
export function getYaoPinJBSYFF(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getYaoPinJBSYFF',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取药品附加信息
export function getYaoPinFJXX(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getYaoPinFjxx',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据非中选药品ID获取对应的中选药品
export function getZhongXuanYaoPinList(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZhongXuanYpByYpid',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取最后一次抗菌药物医嘱的使用方法
export function checkYuFangYY(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/checkYuFangYY',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 检查中药饮片的一次用量
export function checkZhongYaoYiCiYL(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/checkZhongYaoYiCiYL',
    method: 'get',
    params,
    showError: false,
    headers: {
      verifyApp: false
    }
  })
}

// 获取用药持续天数
export function getYYTS(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/jiSuanYYTS',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取病人历史限定范围
export function getHistoryXDFW(params) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getLiShiXdfw',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 中成药辩证
export function getZhongYiBZXX(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZhongYiBZXX',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 处方药诊断
export function getChuFangZD(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getChuFangZD',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取诊断列表
export function getZhenDuanList(params) {
  return request({
    url: '/medicalrecord/v1/BasicData/getZhenDuanByPageAndZhuanTai',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取麻醉用途
export function getMaZuiYT(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getMaZuiYT',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取饮食附加信息
export function getYinShiFJXX(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getYinShiFjxx',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 获取治疗附加信息
export function getZhiLiaoFJXX(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZhiLiaoFjxx',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 查询有无出院录
export function checkChuYuanLu(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/checkChuYuanLu',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 检查执行病区
export function checkZhiXingBQ(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/checkZhiXingBQ',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 保存医嘱
export function saveYiZhu(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/saveYiZhu',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 提交医嘱
export function submitYZ(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/submitYiZhu',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取病人最新身高体重
export function getLastSGTZ(params) {
  return request({
    url: '/nursingservice/v1/ShengMingTZ/getLastSGTZ',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 保存病人最新身高体重
export function saveLastSGTZ(data) {
  return request({
    url: '/nursingservice/v1/ShengMingTZ/svaeShenGaoTZ',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 检查术前医嘱
export function checkShuQianYz(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/checkShuQianYz',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 检查今日出院
export function checkJinRiCY(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/checkJinRiCY',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取患者住院快递地址
export function getZyKuaiDiDz(params) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getZyKuaiDiDz',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取所有省份代码
export function getProvinceOptions(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getAllProvince',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据省份代码获取市
export function getCityOptions(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getCitysByShengFenDM',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据市代码获取区县
export function getCountyOptions(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getAreasByChengShiDM',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 保存住院快递地址
export function saveZyKuaiDiDz(data) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/saveZyKuaiDiDz',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 停止医嘱
export function stopYiZhu(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/stopYiZhu',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 撤回医嘱
export function disSubmitYiZhu(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/disSubmitYiZhu',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 临时医嘱执行
export function linShiYzZx(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/linShiYzZx',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据组号列表查询静配配置记录信息
export function getJingPeiConfigByZuHaoList(data) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getJingPeiZtByZh',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 检查出院带药收费状态
export function checkYpSfzt(data) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/checkCydyShouFeiZt',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 提交医嘱
export function submitYiZhu(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/submitYiZhu',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 获取历史用药模板
export function getLiShiYyMb(data) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getLiShiYyMb',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 根据病人基本信息查询患者
export function getBinRenXXNoQuanXianByParam(params) {
  return request({
    url: '/patient/v1/ezyblbr/getBinRenXXNoQuanXianByParam',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 根据类别获取药品模板
export function getYaoPinMbByLb(data) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getYaoPinMbByLb',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 根据模板名称获取模板详细内容
export function getYaoPinMbDetail(data) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getYaoPinMbDetail',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 长期医嘱转临时医嘱
export function copyToLinShiYz(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/copyToLinShiYz',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 撤销执行医嘱
export function cheXiaoZxByYs(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/cheXiaoZxByYs',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 查询新外购药品
export function getNewWaiGouYP(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getNewWaiGouYP',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 保存为药品模板
export function saveAsYaoPinMb(data) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/saveAsYaoPinMb',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 初始化肠外营养套餐
export function getChangWaiYyInit(params) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/initWeiChangYyTc',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取肠外营养套餐 /medicaladvice/v1/DrugInpatient/getWeiChangYyTcmc
export function getChangWaiYyTcmc(params) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getWeiChangYyTcmc',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据肠外营养套餐名称获取明细
export function getChangWaiYyByTcMc(params) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getWeiChangYyByTcMc',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 保存肠外营养套餐
export function saveChangWaiYyTc(data) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/saveWeiChangYyTc',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取治疗医嘱模板初始化目录
export function zhiLiaoMbInit(data) {
  return request({
    url: '/medicaladvice/v1/treatment/zhiLiaoMbInit',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取治疗医嘱模板组合
export function getZhiLiaoMbZuHe(params) {
  return request({
    url: '/medicaladvice/v1/treatment/getZhiLiaoMbZh',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取综合医嘱模板初始化
export function getZongHeYzMbInit(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/zongHeYzMbInit',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据ID获取综合医嘱模板明细
export function getZongHeYzMbMX(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMx',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 2天内开过的药品
export function getYaoPinYzByTime(params) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getYaoPinYzByTime',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 5天内开过的草药
export function getBrLiShiCaoYao(params) {
  return request({
    url: '/medicaladvice/v1/DrugInpatient/getBrLiShiCaoYao',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 通用_查询_已开医嘱查询内容
export function getAdmissionList(params) {
  return request({
    url: '/medicaladvice/v1/AdmissionlistController/getAdmissionList',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 电子病历_查询_查询未完成检验检查医嘱
export function getUnFinishedExamDataAll(params) {
  return request({
    url: '/medicalrecord/v1/PatientAttributeOperation/getUnFinishedExamDataAll',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 自费项目查询
export function getSelfPayItemsByCondition(params) {
  return request({
    url: '/medicalrecord/v1/PatientAttributeOperation/getSelfPayItemsByCondition',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 电子病历_查询_查询未完成检验检查医嘱
export function getKangFuZLJL(params) {
  return request({
    url: '/app-emrservice/v1/mdedicalAdviceKFZL/v1/InterdisciplinaryTreatment/getKangFuZLJL',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 电子病历_查询_查询未完成检验检查医嘱
export function getKangFuZLYZ(params) {
  return request({
    url: '/app-emrservice/v1/mdedicalAdviceKFZL/v1/InterdisciplinaryTreatment/getKangFuZLYZ',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据申请单ID获取查看申请单信息链接
export function getSqdShowUrlById(params) {
  return request({
    url: '/medicaladvice/v1/CheckTreat/getSqdShowUrlById',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据申请单列表查检查报告列表
export function getCheckReportDataBySqdIdList(params) {
  return request({
    url: '/report-platform/v1/report/getCheckReportDataBySqdIdList',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据（医嘱ID + 类别）获取有效的医嘱执行记录
export function getZhiXingJLByYZID(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZhiXingJLByYZID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据（医嘱ID + 类别）获取有效的医嘱执行记录
export function getYiZhuZXJLByZH(params) {
  return request({
    url: '/app-nursestation/v1/yiZhuZhiXing/getYiZhuZXJLByZH',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取华法令调整表
export function getHuaFaLingTzb(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getHuaFaLingTzb',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 规则引擎校验开关
export function itemOfRuleEngine(data) {
  return request({
    url: '/app-decisionsupport/v1/rule/itemOfRuleEngine',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 规则引擎校验
export function triggerRuleByScenarioCodeVersion2(data) {
  return request({
    url: '/app-decisionsupport/v1/rule/triggerRuleByScenarioCodeVersion2',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 获取抗菌药品管理
 * @param {string} data.bingLiID 病历ID
 * @param {string} data.yaoPinID 药品ID
 * @param {Array} data.zhiLiaoSYYPIDList 治疗使用药品ID列表
 * @returns {Promise}
 */
export function getKangJunYpGl(data) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getKangJunYpGl',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 是否一类手术
 * @param {string} params.bingLiID 病历ID
 * @returns {Promise}
 */
export function getShiFouYLSS(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getShiFouYLSS',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 获取感染诊断
 * @param {string} params.bingLiID 病历ID
 * @returns {Promise}
 */
export function getGanRanZD(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getGanRanZD',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 初始化微生物送检情况选项
 * @returns {Promise}
 */
export function initWeiShengWuSjQkXx() {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/initWeiShengWuSjQkXx',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 初始化新生儿抗生素预防使用问卷
 * @returns {Promise}
 */
export function initXinShengErKssYtSyWq() {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/initXinShengErKssYtSyWq',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 初始化清洁手术高危因素
 * @returns {Promise}
 */
export function initQingJieSsGwYs() {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/initQingJieSsGwYs',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 二级密码
 * @returns {Promise}
 */
export function checkSecendPassword(data) {
  return request({
    url: '/sysmanage/v1/user/checkSecendPassword',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 通用_查询_根据YHID和类别获取电子签名
 * @returns {Promise}
 */
export function getDZQMBYhid(params) {
  return request({
    url: '/staff/v1/staff/getDZQMBYhid',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}
