import request from '@/utils/request'

// 获取其他医嘱目录
export function getBaseInfo(params) {
  return request({
    url: '/app-emrservice/v1/Report/getBaseInfo',
    method: 'POST',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_获取患者基本信息
export function getPatientInfoByEmrID(params) {
  return request({
    url: 'app-emrservice/v1/basicInfo/getPatientInfoByEmrID',
    method: 'GET',
    params
  })
}

// 报告卡_查询_获取地址编码详情
export function getAddressListByAddress(params) {
  return request({
    url: 'app-emrservice/v1/Report/getAddressListByAddress',
    method: 'POST',
    params
  })
}

// 报告卡(传染病)获取诊断
export function getInfectiousDiseaseDiagnosis(params) {
  return request({
    url: '/app-emrservice/v1/Report/getInfectiousDiseaseDiagnosis',
    method: 'POST',
    params
  })
}

// 报告卡(传染病)_查询_地址列表
export function getAddressList(params) {
  return request({
    url: '/app-emrservice/v1/Report/getAddressList',
    method: 'POST',
    params,
    globalLoading: false
  })
}

// 报告卡(传染病)_查询_诊断列表
export function getDiagnoseList(params) {
  return request({
    url: '/app-emrservice/v1/Report/getDiagnoseList',
    method: 'POST',
    params
  })
}

// 报告卡(传染病)_新增/修改_保存传染病报告卡
export function saveReport(data) {
  return request({
    url: 'app-emrservice/v1/Report/saveReport',
    method: 'POST',
    data
  })
}

// 报告卡(传染病)_查询_传染病报告卡分页查询
export function queryInfectiousDiseaseReport(data) {
  return request({
    url: '/app-emrservice/v1/Report/queryInfectiousDiseaseReport',
    method: 'POST',
    data
  })
}

// 报告卡(传染病)_查询_传染病报告卡查看
export function viewInfectiousDiseaseReport(params) {
  return request({
    url: 'app-emrservice/v1/Report/viewInfectiousDiseaseReport',
    method: 'POST',
    params
  })
}

// 报告卡(慢性病)_查询_获取地址编码详情
export function getAddressListByAddressMXB(params) {
  return request({
    url: '/app-emrservice/v1/Report/getAddressListByAddressMXB',
    method: 'POST',
    params,
    globalLoading: false
  })
}

// 报告卡(慢性病)_查询_地址列表
export function getAddressListMxb(params) {
  return request({
    url: '/app-emrservice/v1/Report/getAddressListMxb',
    method: 'POST',
    params,
    globalLoading: false
  })
}

// 报告卡(慢性病)_查询_地址维护地址列表
export function getAddressListMxbModify(params) {
  return request({
    url: '/app-emrservice/v1/Report/getAddressListMxbModify',
    method: 'POST',
    params,
    globalLoading: false
  })
}

// 报告卡(慢性病)_查询_慢性病报告卡分页查询
export function queryIhronicDiseaseReport(data) {
  return request({
    url: '/app-emrservice/v1/Report/queryIhronicDiseaseReport',
    method: 'POST',
    data
  })
}

//报告卡(糖尿病)_查询_获取基础数据
export function getBaseInfoTNB(params) {
  return request({
    url: 'app-emrservice/v1/Report/getBaseInfoTNB',
    method: 'POST',
    params
  })
}

// 报告卡(糖尿病)_新增_糖尿病报告卡
export function addOrUpdateTNB(data) {
  return request({
    url: 'app-emrservice/v1/Report/addOrUpdateTNB',
    method: 'POST',
    data
  })
}

//报告卡(心脑)_查询_获取基础数据
export function getBaseInfoXN(params) {
  return request({
    url: 'app-emrservice/v1/Report/getBaseInfoXN',
    method: 'POST',
    params
  })
}

// 报告卡(心脑)_新增_报告卡
export function addOrUpdateXNBGK(data) {
  return request({
    url: 'app-emrservice/v1/Report/addOrUpdateXNBGK',
    method: 'POST',
    data
  })
}

//报告卡(肿瘤)_查询_获取基础数据
export function getBaseInfoZL(params) {
  return request({
    url: 'app-emrservice/v1/Report/getBaseInfoZL',
    method: 'POST',
    params
  })
}

// 报告卡(肿瘤)_新增_报告卡
export function addOrUpdateZLBGK(data) {
  return request({
    url: 'app-emrservice/v1/Report/addOrUpdateZLBGK',
    method: 'POST',
    data
  })
}

// 报告卡(糖尿病)_查看_报告卡
export function viewTnbReport(params) {
  return request({
    url: 'app-emrservice/v1/Report/viewTnbReport',
    method: 'POST',
    params
  })
}

// 报告卡(心脑)_查看_报告卡
export function viewXnReport(params) {
  return request({
    url: 'app-emrservice/v1/Report/viewXnReport',
    method: 'POST',
    params
  })
}

// 报告卡(肿瘤)_查看_报告卡
export function viewZlReport(params) {
  return request({
    url: 'app-emrservice/v1/Report/viewZlReport',
    method: 'POST',
    params
  })
}

//报告卡(食源)_查询_获取基础数据
export function getBaseInfoSY(params) {
  return request({
    url: 'app-emrservice/v1/Report/getBaseInfoSY',
    method: 'POST',
    params
  })
}

//报告卡(食源)_查询_地址列表
export function getAddressListSy(params) {
  return request({
    url: 'app-emrservice/v1/Report/getAddressListSy',
    method: 'POST',
    params
  })
}

// 报告卡_新增_食源报告卡
export function addOrUpdateSYBGK(data) {
  return request({
    url: 'app-emrservice/v1/Report/addOrUpdateSYBGK',
    method: 'POST',
    data
  })
}

// 报告卡(食源性)_查询_食源性报告卡分页查询
export function queryFoodSourceDiseaseReport(data) {
  return request({
    url: 'app-emrservice/v1/Report/queryFoodSourceDiseaseReport',
    method: 'POST',
    data
  })
}

// 报告卡(食源性)_查询_报告卡查看
export function viewFoodSourceDiseaseReport(params) {
  return request({
    url: 'app-emrservice/v1/Report/viewFoodSourceDiseaseReport',
    method: 'POST',
    params
  })
}

//报告卡(重精)_查询_获取基础数据
export function getBaseInfoZJ(params) {
  return request({
    url: 'app-emrservice/v1/Report/getBaseInfoZJ',
    method: 'POST',
    params
  })
}

// 报告卡_新增_重精报告卡
export function addOrUpdateZJBGK(data) {
  return request({
    url: 'app-emrservice/v1/Report/addOrUpdateZJBGK',
    method: 'POST',
    data
  })
}

// 报告卡(重精)_查询_重精报告卡分页查询
export function seriousMentalDiseaseReport(data) {
  return request({
    url: 'app-emrservice/v1/Report/seriousMentalDiseaseReport',
    method: 'POST',
    data
  })
}

// 报告卡_查询_分页获取(慢性病)地址的列表
export function getYlGwjkSdtbhzdzPoList(params) {
  return request({
    url: 'app-emrservice/v1/Report/getYlGwjkSdtbhzdzPoList',
    method: 'POST',
    params
  })
}

// 报告卡(重精)_查询_报告卡查看
export function viewSeriousMentalDiseaseReport(params) {
  return request({
    url: 'app-emrservice/v1/Report/viewSeriousMentalDiseaseReport',
    method: 'POST',
    params
  })
}

// 报告卡_新增_新增地址
export function addYlGwjkSdtbhzdzPo(data) {
  return request({
    url: 'app-emrservice/v1/Report/addYlGwjkSdtbhzdzPo',
    method: 'POST',
    data
  })
}

// 报告卡_删除_删除地址
export function deleteYlGwjkSdtbhzdzPo(params) {
  return request({
    url: 'app-emrservice/v1/Report/deleteYlGwjkSdtbhzdzPo',
    method: 'POST',
    params
  })
}

// 报告卡_修改_修改地址
export function updateYlGwjkSdtbhzdzPo(data) {
  return request({
    url: 'app-emrservice/v1/Report/updateYlGwjkSdtbhzdzPo',
    method: 'POST',
    data
  })
}

// 报告卡_删除报告卡
export function deleteReport(params) {
  return request({
    url: 'app-emrservice/v1/Report/deleteReport',
    method: 'POST',
    params
  })
}

// 获取专科
export function getZhuanKeList(params) {
  return request({
    url: 'app-emrservice/v1/basicInfo/getZhuanKeList',
    method: 'GET',
    params
  })
}

// 住院医生站_查询_根据参数查询死亡证明书
export function getSiWangZMByCanShu(data) {
  return request({
    url: 'app-emrservice/v1/yiLiaoZM/getSiWangZMByCanShu',
    method: 'POST',
    data
  })
}

// 住院医生站_查询_死亡证明下拉框初始化
export function initSiWangZMByBingLiID(data) {
  return request({
    url: 'app-emrservice/v1/yiLiaoZM/initSiWangZMByBingLiID',
    method: 'POST',
    data
  })
}

// 住院医生站_查询_根据格式代码获取病人文书列表
export function getWenShuListByGsdm(params) {
  return request({
    url: 'app-emrservice/v1/zhuyuanWS/getWenShuListByGsdm',
    method: 'GET',
    params
  })
}

// 住院医生站_上传死亡证明
export function upSiWangZM(params) {
  return request({
    url: 'app-emrservice/v1/yiLiaoZM/upSiWangZM',
    method: 'GET',
    params
  })
}

// 住院医生站_重新上传死亡证明
export function reuploadSiWangZM(params) {
  return request({
    url: 'app-emrservice/v1/yiLiaoZM/reuploadSiWangZM',
    method: 'GET',
    params
  })
}

// 设置死亡证明书状态
export function setSiWangZMZT(params) {
  return request({
    url: 'app-emrservice/v1/yiLiaoZM/setSiWangZMZT',
    method: 'GET',
    params
  })
}
