import request from '@/utils/request'

// 获取输血申请单（住院）列表
export function listBloodTransSqd(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/listBloodTransSqd',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取血型和RH的化验结果
export function getAssayBloodTypeAndRH(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getAssayBloodTypeAndRH',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 获取输血理由列表
export function getBloodTransReasonList(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getBloodTransReasonList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取输血类型列表（紧急输血、平诊输血等等）
export function getBloodTransType(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getBloodTransType',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 输血医嘱基础数据
export function getBaseInfo(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBaseInfo',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取输血申请单详情
export function getSqdAndAdviceListByShenQingDanID(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getSqdAndAdviceListByShenQingDanID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 输血可开附加化验医嘱列表
export function getEnableAddAssayListByLeiXing(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqd/getEnableAddAssayListByLeiXing',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 新增输血申请单
export function addBloodTransSqd(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/addBloodTransSqd',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 查询血库血型
export function getBloodBankInventory(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBloodBankInventory',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据申请单ID获取血袋数量
export function getBloodQuantityBySQDID(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBloodQuantityBySQDID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取右侧化验指标
export function getInPatientBloodTypeByLB(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getInPatientBloodTypeByLB',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 删除-输血申请单（住院）
export function delBloodTransSqd(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/delBloodTransSqd',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病区ID获取院区和血库编号
export function getXueKuBH(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getXueKuBH',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 修改-输血申请单（住院）
export function updateBloodTransSqd(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/updateBloodTransSqd',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据成分代码获取需要导入的化验指标
export function getLaboratoryIndicatorsByCFDM(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getLaboratoryIndicatorsByCFDM',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 根据化验项目获取对应化验指标
export function getLaboratoryIndicatorsByHYXM(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getLaboratoryIndicatorsByHYXM',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 输血医嘱-修改-删除or暂停住院医嘱（住院）
export function stopBloodTransAdvice(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/stopBloodTransAdvice',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 判断能否能新增
export function getBloodFormVerify(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBloodFormVerify',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 判断能否能新增1
export function getBloodFormVerify1(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBloodFormVerify1',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 判断能否能新增2
export function getBloodFormVerify2(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBloodFormVerify2',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 1.查看输血满意度评价
export function getBloodTransSatisfactionEvaluate(data) {
  return request({
    url: '/app-emrservice/v1/bloodTrans/getBloodTransSatisfactionEvaluate',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 2.输血评价初始化接口
export function getBloodEvaluateInfo(data) {
  return request({
    url: '/app-emrservice/v1/bloodTrans/getBloodEvaluateInfo',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 3.新增临床用血满意度
export function insert(data) {
  return request({
    url: '/app-emrservice/v1/bloodTrans/insert',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 4.更新输血满意度评价
export function updateBloodTransSatisfaction(data) {
  return request({
    url: '/app-emrservice/v1/bloodTrans/updateBloodTransSatisfaction',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据申请单id查询已开住院附加化验医嘱列表
export function getInpatinentAlreadyExistAddAssayList(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getInpatinentAlreadyExistAddAssayList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取开输血医嘱cdss提示
export function getBloodCdss(data) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdZY/getBloodCdss',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
