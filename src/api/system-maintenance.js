import request from '@/utils/request'
import { getYongHuID } from '@/utils/auth'

//治疗医嘱

export function getZhiLiaoMbMlByLb(leiBie) {
  return request({
    url: '/medicaladvice/v1/treatment/getZhiLiaoMbMlByLb',
    method: 'get',
    params: {
      leiBie
    },
    headers: {
      verifyApp: false
    }
  })
}

export function getZhiLiaoMbZh(params) {
  return request({
    url: '/medicaladvice/v1/treatment/getZhiLiaoMbZh',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

export function saveZhiLiaoMbMl(params) {
  return request({
    url: '/medicaladvice/v1/treatment/saveZhiLiaoMbMl',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

export function saveZhiLiaoMbZh(params) {
  return request({
    url: '/medicaladvice/v1/treatment/saveZhiLiaoMbZh',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

export function zhiLiaoMbInit() {
  return request({
    url: '/medicaladvice/v1/treatment/zhiLiaoMbInit',
    method: 'post',
    headers: {
      verifyApp: false
    }
  })
}

//综合遗嘱

export function getZongHeYzMbMlByID(moBanID) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMlByID',
    method: 'get',
    params: {
      moBanID
    },
    headers: {
      verifyApp: false
    }
  })
}

export function getZongHeYzMbMlByLb(moBanLB) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMlByLb',
    method: 'get',
    params: {
      moBanLB
    },
    headers: {
      verifyApp: false
    }
  })
}

export function getZongHeYzMbMx(moBanID) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getZongHeYzMbMx',
    method: 'get',
    params: {
      moBanID
    },
    headers: {
      verifyApp: false
    }
  })
}

export function saveZongHeYzMbMl(params) {
  return request({
    // url: '/app-emrservice/v1/mdedicalAdviceZYYZ/saveZongHeYzMbMl',
    url: '/medicaladvice/v1/AdviceInpatient/saveZongHeYzMbMl',
    method: 'post',
    data: params,
    headers: {
      accept: '*/*',
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

export function saveZongHeYzMbMx(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/saveZongHeYzMbMx',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

export function zongHeYzMbInit() {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/zongHeYzMbInit',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}

//有创模板

//查询本有创记录模板关联的专科
export function getMbglData(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getMbglData',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//获取所有有创纪录模板
export function getYcjlData() {
  return request({
    url: '/app-emrservice/v1/basicInfo/getYcjlData',
    method: 'post',
    headers: {
      verifyApp: false
    }
  })
}

//根据拼音或关键字获取有创纪录模板
export function getYcjlDataByPYGJZ(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getYcjlDataByPYGJZ',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

//保存_新增或修改有创记录
export function saveYcjl(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/saveYcjl',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

//删除_删除多条有创模板关联专科记录
export function deleteBatchMbgl(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/deleteBatchMbgl',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false
    }
  })
}

//医疗证明管理

export function medicalCertificateInit() {
  return request({
    url: '/app-emrservice/v1/yiLiaoZM/medicalCertificateInit',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}
// 获取出院病人病理结果提醒列表
export function getInPatientCheckResultMes(params) {
  return request({
    url: '/medicalrecord/v1/PatientAttributeOperation/getInPatientCheckResultMes',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_分页查询YlBasySswy
export function getYlBasySswyByPage(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getYlBasySswyByPage',
    method: 'get',
    params
  })
}

// 住院医生站_保存_保存一条YlBasySswy内容
export function saveYlBasySswy(data) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/saveYlBasySswy',
    method: 'post',
    data
  })
}

// 获取手术项目列表
export function getShouShuLieBiao(params) {
  return request({
    url: '/app-emrservice/v1/YlSsicd/getShouShuLieBiao',
    method: 'get',
    params
  })
}

// 住院医生站_保存_保存一条YlSsicd内容
export function saveYlSsicd(data) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/saveYlSsicd',
    method: 'post',
    data
  })
}

// 住院医生站_查询_分页查询手术icd
export function getYlSsicdByPage(params) {
  return request({
    url: 'app-emrservice/v1/MedicalrecordDocument/getYlSsicdByPage',
    method: 'get',
    params
  })
}

// 输血审批接口
export function approval(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/approval',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 全部输血审批查询接口
export function getApprovalAll(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalAll',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 科室审批查询接口(当日800ml-1600ml)
export function getApprovalFromKeShi(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalFromKeShi',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 上级医师审批查询接口（当日800ml以内）
export function getApprovalFromShangJiYiShi(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalFromShangJiYiShi',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 医务科审批查询接口(当日1600ml以上)
export function getApprovalFromYiWuKe(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalFromYiWuKe',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 无需审批查询接口
export function getApprovalWithoutSP(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getApprovalWithoutSP',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 输血单页面基础数据
export function getBaseInfo(params) {
  return request({
    url: '/medicaladvice/v1/bloodTransSqdApproval/getBaseInfo',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_病历讨论记录查询
export function getBingLiTaoLunJiLu(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiTaoLunJiLu',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_危重病人记录查询
export function getWeiZhongSiWangBRCX(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getWeiZhongSiWangBRCX',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_病历状态修改记录查询_根据专科
export function getBingLiZTXGJLByZhuanKe(params) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiZTXGJLByZhuanKe',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_病历状态修改记录查询_根据病案号
export function getBingLiZTXGJLByBingAnHao(params) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiZTXGJLByBingAnHao',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 医嘱按时间间隔统计
export function getTongJiYzBySjjg(params) {
  return request({
    url: '/medicaladvice/v1/AdviceInpatient/getTongJiYzBySjjg',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_专科病历上缴率查询
export function getBingLiShangJiaoLvTJB(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiShangJiaoLvTJB',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_病区病历上缴率查询
export function getBingLiShangJiaoLvTJBBQ(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getBingLiShangJiaoLvTJBBQ',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_病历查询_监管医师监管病历统计表
export function getJianGuanYSJGBLTJBCX(data) {
  return request({
    url: '/app-emrservice/v1/BingLiCX/getJianGuanYSJGBLTJBCX',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// NRS2002知识库获取类别
export function getLeiBie(params) {
  return request({
    url: 'medicalrecord/v1/NutritionRiskScreening/getLeiBie',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// NRS2002知识库获取内层类别
export function getNeiBuLB(params) {
  return request({
    url: 'medicalrecord/v1/NutritionRiskScreening/getNeiBuLB',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// NRS2002知识库获取关键词
export function getKeyWordData(params) {
  return request({
    url: 'medicalrecord/v1/NutritionRiskScreening/getKeyWordData',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// NRS2002知识库获取ICD
export function getICDS(params) {
  return request({
    url: 'medicalrecord/v1/NutritionRiskScreening/getICDS',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 门诊医生站_诊断_根据拼音或五笔获取诊断列表
export function getDiagnosesInfoByPinYinOrWuBi(params) {
  return request({
    url: 'diagnoses/v1/diagnoses/getDiagnosesInfoByPinYinOrWuBi',
    method: 'GET',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// NRS2002知识库添加诊断ICD
export function addDiagnoseICD(params, data) {
  return request({
    url: 'medicalrecord/v1/NutritionRiskScreening/addDiagnoseICD',
    method: 'POST',
    params,
    data,
    headers: {
      verifyApp: false
    }
  })
}

// NRS2002知识库添加手术ICD
export function addSurgeryICD(params, data) {
  return request({
    url: 'medicalrecord/v1/NutritionRiskScreening/addSurgeryICD',
    method: 'POST',
    params,
    data,
    headers: {
      verifyApp: false
    }
  })
}

// NRS2002知识库删除ICD
export function deleteICD(params) {
  return request({
    url: 'medicalrecord/v1/NutritionRiskScreening/deleteICD',
    method: 'POST',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_删除病历
export function delBingLi(data) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/delBingLi',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_知情记录查询
export function getZqjlListForSearch(data) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getZqjlListForSearch',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_知情记录初始化_获取专科常用知情记录
export function getZhuanKeZQJLInit(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getZhuanKeZQJLInit',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_专科知情记录_根据专科id和格式代码删除
export function deleteZqjlList(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/delete',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_专科知情记录_新增
export function addList(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/addList',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_新增单条胃肠营养套餐
export function addYlWcyytcList(data) {
  return request({
    url: '/app-emrservice/v1/YlWcyytc/addYlWcyytcList',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_删除胃肠营养套餐数据
export function deleteYlWcyytcListByIds(params) {
  return request({
    url: '/app-emrservice/v1/YlWcyytc/deleteYlWcyytcListByIds',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_根据页码获取营养药品列表
export function getYingYangYPVoListByPage(params) {
  return request({
    url: '/app-emrservice/v1/YlWcyytc/getYingYangYPVoListByPage',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_获取所有胃肠营养套餐名称列表
export function getYlWcyytcAllTaoCanMC(params) {
  return request({
    url: '/app-emrservice/v1/YlWcyytc/getYlWcyytcAllTaoCanMC',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}
// 住院医生站_查询所有胃肠营养套餐数据（含药品名称）
export function getYlWcyytcDMCVoList(params) {
  return request({
    url: '/app-emrservice/v1/YlWcyytc/getYlWcyytcDMCVoList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_更新胃肠营养套餐列表
export function updateYlWcyytc(data) {
  return request({
    url: '/app-emrservice/v1/YlWcyytc/updateYlWcyytc',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站新增_新增特殊病人医生权限数据
export function insertSpecPatientPriviList(data) {
  return request({
    url: '/medicalrecord/v1/PatientAttributeOperation/insertSpecPatientPriviList',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 住院医生站_删除特殊病人医生权限数据
export function deleteSpecPatientPriviList(data) {
  return request({
    url: '/medicalrecord/v1/PatientAttributeOperation/deleteSpecPatientPriviList',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站删除住院病人属性
export function deleteZhuYuanBRSX(data) {
  return request({
    url: '/medicalrecord/v1/PatientAttributeOperation/deleteZhuYuanBRSX',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
// 住院医生站新增住院病人属性
export function insertZhuYuanBRSX(data) {
  return request({
    url: '/medicalrecord/v1/PatientAttributeOperation/insertZhuYuanBRSX',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 知情记录列表维护专科文书格式维护_获取全部文书列表
export function getAllWenShu(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getAllWenShu',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 知情记录列表维护专科文书格式维护_根据专科查询
export function getListByZkid(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getListByZkid',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 知情记录列表维护专科文书格式维护_新增
export function addZhuanKeWenShu(data) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/addZhuanKeWenShu',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 知情记录列表维护专科文书格式维护_删除
export function deleteZhuanKeWenShu(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/delete',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_系统维护_病人病历状态修改_封存
export function fengCun(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/fengCun',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_系统维护_病人病历状态_医生解封
export function yiShiJF(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/yiShiJF',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_系统维护_病人病历状态_护士解封
export function huShiJF(params) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/huShiJF',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_修改病人病历专科格式
export function updateByBingLiIDAndZhuanKeID(data) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/updateByBingLiIDAndZhuanKeID',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
