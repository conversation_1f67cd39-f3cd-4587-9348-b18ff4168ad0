import request from '@/utils/request'

// 住院医生站_查询_病案首页初始化
export function InitTheFirstPageOfMedicalRecord(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/InitTheFirstPageOfMedicalRecord',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_病案首页保存或提交
export function saveTheFirstPageOfMedicalRecord(data) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/saveTheFirstPageOfMedicalRecord',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_查询一个BingLiID的病案首页
export function getBingAnSYByBingLiID(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getBingAnSYByBingLiID',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// InitTheFirstPageFormOfMedicalRecord
export function InitTheFirstPageFormOfMedicalRecord(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/InitTheFirstPageFormOfMedicalRecord',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_病案首页cdss参数
export function getPatientBasic(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getShouYeCDSSNR',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 电子病历_查询_查询一个专科id的常用手术
export function getCommonSurgeriesByZhuanKe(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getCommonSurgeriesByZhuanKe',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 电子病历_查询_查询一个科室id的常用诊断
export function getCommonDiagnosesByZhuanKe(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getCommonDiagnosesByZhuanKe',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_分页查询YlBasySswy取状态
export function getYlBasySswyByPageAndZhuangTai(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getYlBasySswyByPageAndZhuangTai',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 分页搜索YlZdVo列表开启状态
export function getZhenDuanByPageAndZhuanTai(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getZhenDuanByPageAndZhuanTai',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_分页查询手术icd识别状态
export function getYlSsicdByPageAndZhuanTai(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getYlSsicdByPageAndZhuanTai',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_获取籍贯代码List
export function getJiGuanDMList(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getJiGuanDMList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 查询_获取麻醉方式List
export function getMaZuiFSList(params) {
  return request({
    url: '/app-emrservice/v1/basicInfo/getMaZuiFSList',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 获取绒刷时间
export function getShouYeCDSSNR(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getShouYeCDSSNR',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 调用提交接口前的状态
export function triggerRuleBySceneCodeInGeneralMode(data) {
  return request({
    url: '/app-decisionsupport/v1/rule/triggerRuleBySceneCodeInGeneralMode',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 常用的西医诊断的同步
export function getWenShuZDByBingLiID(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getWenShuZDByBingLiID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 常用的中医诊断的同步
export function getQuanBuWSZDByBingLiID(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getQuanBuWSZDByBingLiID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_保存_修改病案首页提交状态
export function changeBingAnShouYeTiJiaoZTByBingLiID(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/changeBingAnShouYeTiJiaoZTByBingLiID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 住院医生站_查询_根据病历ID获取院内感染信息
export function getYuanNeiGRByBingLiID(params) {
  return request({
    url: '/app-emrservice/v1/MedicalrecordDocument/getYuanNeiGRByBingLiID',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })
}

// 集中打印-打印
export function GetPrintContent(data) {
  return request({
    url: '/app-emrservice/v1/xiuGaiBingLiZT/GetPrintContent',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}
