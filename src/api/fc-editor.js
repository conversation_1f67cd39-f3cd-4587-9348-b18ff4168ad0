import request from '@/utils/request'

/**
 * 获取文书模板
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export const getByGeShiDM = (params) =>
  request({
    url: '/medicalrecord/v1/DJMEditor/getByGeShiDM',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })

/**
 * 获取文书ID
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export const getBingLiWSJL = (params) =>
  request({
    url: '/medicalrecord/v1/zhuyuanWS/getBingLiWSJL',
    method: 'get',
    params,
    headers: {
      verifyApp: false
    }
  })

/**
 * 删除文书ID
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export const deleteWenShuJLNewEditor = (params) =>
  request({
    url: '/medicalrecord/v1/zhuyuanWS/deleteWenShuJLNewEditor',
    method: 'post',
    params,
    headers: {
      verifyApp: false
    }
  })

/**
 * 保存评分
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export const savePFBNewEditor = (data) =>
  request({
    url: '/medicalrecord/V1/pingfenbiao/savePFBNewEditor',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })

/**
 * 保存
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export const saveWenShuJiluNewEditor = (data) =>
  request({
    url: '/medicalrecord/v1/zhuyuanWS/saveWenShuJiluNewEditor',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
