import request from '@/utils/request'

//获取内镜检查类型
export function getEndoscopyTypeList(data) {
  return request({
    url: 'medicaladvice/v1/CheckTreat/getEndoscopyTypeList',
    method: 'POST',
    data,
    headers: {
      verifyApp: false
    }
  })
}

//获取病人列表接口
export function getEndoscopyInPatientList(params) {
  return request({
    url: 'medicaladvice/v1/CheckTreat/getEndoscopyInPatientList',
    method: 'POST',
    params,
    headers: {
      verifyApp: false
    }
  })
}
