import request from '@/utils/request'

/**
 * 查询_根据病历ID查询病人入径记录
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.fenZhiLJ 分支路径
 */
export function getXuanZeLuJingDy(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getXuanZeLuJingDy',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 查询有效的入径评估维护列表
 * @param {Object} params 参数对象
 * @param {string} params.luJingID 路径ID
 */
export function getValidRuJingPgWhList(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getValidRuJingPgWhList',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 保存病人入径评估和入径记录
 * @param {Object} params 参数对象
 * @param {string} params.luJingID 路径ID
 */
export function saveBingRenRjPgAndRjJl(data) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/saveBingRenRjPgAndRjJl',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 查询_根据病历ID查询病人入径记录（弃用）
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 */
export function getBingRenRjJlByBlid(params) {
  return request({
    url: 'medicaladvice/v1/ClinicalPathway/getBingRenRjJlByBlid',
    method: 'post',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 获取临床路径初始化数据
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 */
export function getClinicalPathInit(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getLcljInit',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 查询_病人待办事项详情
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.luJingID 路径ID
 * @param {string} params.zhenLiaoJDID 诊疗阶段ID
 */
export function getBingRenDbsxDetail(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getBingRenDbsxDetail',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 查询_病人待办事项详情
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.luJingID 路径ID
 * @param {string} params.zhenLiaoJDID 诊疗阶段ID
 */
export function getBrDbsxYiZhu(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getBrDbsxYiZhu',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 查询_根据组合ID查询二级模板及模板详细书
 * @param {Object} params 参数对象
 * @param {string} params.zuHeID 组合ID
 */
export function getErJiMbAndMbXxsByZhid(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getErJiMbAndMbXxsByZhid',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 保存理由
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 组合ID
 * @param {string} params.luJingID 路径ID
 */
// export function saveQiangZhiCJ(params) {
//   return request({
//     url: '/medicaladvice/v1/ClinicalPathway/saveQiangZhiCJ',
//     method: 'post',
//     params: params,
//     headers: {
//       verifyApp: false
//     }
//   })
// }

/**
 * 完成路径
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.luJingID 路径ID
 */
export function saveWanChengLJ(data) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/saveWanChengLJ',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 保存强制出径
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.luJingID 路径ID
 * @param {string} params.chuJingLYDM 出径理由代码
 * @param {string} params.chuJingJTLY 出径具体理由
 */
export function saveQiangZhiCJ(data) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/saveQiangZhiCJ',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 保存强制出径
 * @param {Object} params 参数对象
 * @param {string} params.huaYanMbId 化验模板ID
 */
export function getHuaYanMbDetailByMbId(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getHuaYanMbDetailByMbId',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 获取出径理由
 * @param {Object} params 参数对象
 * @param {string} params.huaYanMbId 化验模板ID
 */
export function getinitChuJing(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/initChuJing',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 保存
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID //病历ID
 * @param {string} params.luJingID //路径ID
 * @param {string} params.zhenLiaoJDID //诊疗阶段ID
 * @param {string} params.daiBanSXVos //待办事项
 * @param {string} params.yaoPinYzVos //药品医嘱
 */
export function zhiXingDbsx(data) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/zhiXingDbsx',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

/**
 * 获取规则收费id
 * @param {Object} params 参数对象
 * @param {string} params.moBanID 模板ID
 */
export function getJianYanSfXmid(params) {
  return request({
    url: '/medicaladvice/v1/ClinicalPathway/getJianYanSfXmid',
    method: 'get',
    params: params,
    headers: {
      verifyApp: false
    }
  })
}
