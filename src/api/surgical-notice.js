import request from '@/utils/request'
import { getYongHuID } from '@/utils/auth'

// 查询_手术通知单列表初始化
export function initShouShuTZDList(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/initShouShuTZDList',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 查询_手术通知单初始化
export function initShouShuTZD(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/initShouShuTZD',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_查询_手术室列表
export function getShouShuShiList(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getShouShuShiList',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 根据专科ID获取新技术新项目数据
export function getListByZhuanKeID(params) {
  return request({
    url: '/app-emrservice/v1/YlXjsxxm/getListByZhuanKeID',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_修改_新增手术通知单
export function addShouShuTZD(data) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/addShouShuTZD',
    method: 'post',
    data,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_修改_删除手术通知单
export function deleteSstzd(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/deleteSstzd',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_修改_修改手术通知单
export function updateShouShuTZD(data) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/updateShouShuTZD',
    method: 'post',
    data,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_查询_获取手术人员库
export function getShouShuRyk(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getShouShuRyk',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

//住院医生站_查询_获取准入的手术项目
export function getZhunRuSSXM(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getZhunRuSSXM',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 获取化验结果
export function getBrHuaYanJg(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getBrHuaYanJg',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_查询_根据文书类型列表获取病人文书列表
export function getWenShuListByWslx(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/getWenShuListByWslx',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_查询_根据文书类型列表获取病人文书列表
export function getWenShuListByWSLXList(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/getWenShuListByWSLXList',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_查询_初始化开单备注模板
export function initKaiDanBzMb() {
  return request({
    url: '/medicaladvice/v1/operationInpatient/initKaiDanBzMb',
    method: 'get',
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_查询_根据模板类型获取开单备注模板列表
export function getKaiDanBzMbListByMoBanLx(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getKaiDanBzMbListByMoBanLx',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_查询_根据模板ID获取开单备注模板
export function getKaiDanBzMbById(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getKaiDanBzMbById',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_查询_新增开单备注模板
export function addKaiDanBzMb(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/addKaiDanBzMb',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 住院医生站_查询_修改开单备注模板
export function updateKaiDanBzMb(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/updateKaiDanBzMb',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 护排手术修改_初始化
export function initOperationXiuGaiJL(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/initOperationXiuGaiJL',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 护排手术修改_获取修改记录
export function getOperationXiuGaiJL(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getOperationXiuGaiJL',
    method: 'get',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

//护排手术修改_保存修改记录
export function saveOperationXiuGaiJL(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/saveOperationXiuGaiJL',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

//护排手术修改_删除修改记录
export function deleteOperationXiuGaiJL(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/deleteOperationXiuGaiJL',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

//住院医生站_查询_手术间列表
export function getShouShuJianList(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/getShouShuJianList',
    method: 'post',
    params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 医生停台初始化
export function stopOperationInit() {
  return request({
    url: '/operation/v1/Operation/dictionary',
    method: 'get',
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}

// 医生停台
export function stopOperationByDoctor(params) {
  return request({
    url: '/medicaladvice/v1/operationInpatient/stopOperationByDoctor',
    method: 'post',
    data: params,
    headers: {
      verifyApp: false,
      currUserId: getYongHuID()
    }
  })
}
