import request from '@/utils/request'

/**
 * 获取入院记录初始化数据
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @returns {Promise}
 */
export function getAdmissionNoteInit(params) {
  return request({
    url: '/app-emrservice/v1/zhuyuanWS/getWenShuDBLInit',
    method: 'get',
    params: params
  })
}

/**
 * 设置病人病历专科格式-大病历
 * @param {Object} data 参数对象
 * @param {string} data.bingLiID 病历ID
 * @param {string} data.leiBieDM 类别代码
 * @param {string} data.geShiDM 格式代码
 * @returns {Promise}
 */
export function insertSingleZuanKeGS(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/insertSingleZuanKeGS',
    method: 'get',
    params: params
  })
}
