import request from '@/utils/request'

// 专科知情记录和病历知情记录通用接口
/**
 * 根据病历ID和文书类型列表获取已书写知情记录列表
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {Array} params.wenShuLXList 文书类型列表
 * @returns {Promise}
 */
export function getAllYiShuXieWenShuByBLID(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getAllYiShuXieWenShuByBLID',
    method: 'post',
    params: params
  })
}

/**
 * 获取专科常用知情记录初始化数据
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getZqjlListInit(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getZqjlListInit',
    method: 'post',
    params: params
  })
}

/**
 * 根据关键词搜索知情记录
 * @param {Object} params 参数对象
 * @param {string} params.key 搜索关键词
 * @param {string} params.wenShuLX 文书类型
 * @returns {Promise}
 */
export function getZqjlListForSearch(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getZqjlListForSearch',
    method: 'post',
    params: params
  })
}

/**
 * 根据专科和文书类型获取专科知情记录数据
 * @param {Object} params 参数对象
 * @param {string} params.wenShuLX 文书类型
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getListByzhuanKeIDAndWenShuLX(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getListByzhuanKeIDAndWenShuLX',
    method: 'get',
    params: params
  })
}

/**
 * 获取特殊专科已书写文书, 目前包含超声科和康复科
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getTeShuZhuanKeYiShuXieWS(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getTeShuZhuanKeYiShuXieWS',
    method: 'post',
    params: params
  })
}

/**
 * 获取特殊专科文书，目前包含超声科和康复科
 * @param {Object} params 参数对象
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */
export function getTeShuZhuanKeGeShiDM(params) {
  return request({
    url: '/app-emrservice/v1/zhuanKeGeShiDZ/getTeShuZhuanKeGeShiDM',
    method: 'post',
    params: params
  })
}
