import request from '@/utils/request'

/**
 * 根据bingLiID查询住院病人的评分表数据含VTE评分表
 * @param {Object} params 参数对象
 * @param {string} params.bingLiID 病历ID
 * @returns {Promise}
 */

export function getInPatientPFB(params) {
  return request({
    url: '/app-emrservice/v1/pingfenbiao/getInPatientPFB',
    method: 'get',
    params
  })
}

/**
 * 获取所有评分表ID、名称
 * @param {Object} params 参数对象
 * @param {string} params.zhuanKeID 专科ID
 * @returns {Promise}
 */

export function getPingFenBiaoIDMC(params) {
  return request({
    url: '/app-emrservice/v1/pingfenbiao/getPingFenBiaoIDMC',
    method: 'get',
    params
  })
}

/**
 * 根据pingFenZHID删除一条数据
 * @param {Object} data 参数对象
 * @param {string} data.pingFenZHID 评分总和ID
 * @returns {Promise}
 */

export function deletePingFenBiao(data) {
  return request({
    url: '/app-emrservice/v1/pingfenbiao/deletePingFenZH',
    method: 'post',
    data
  })
}
