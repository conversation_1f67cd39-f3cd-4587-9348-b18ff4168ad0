import request from '@/utils/request'
export function login(data) {
  return request({
    url: '/basic/v2/login/loginByAccount',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

export const getVerList = (data) =>
  request({
    url: '/sysmanage/v1/logs/getUpdateLogList',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })

export const getIconfontUrl = () =>
  request({
    url: '/sysmanage/v1/appsystem/getIconfontUrl',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })

// 获取领域限制白名单
export function getAppWhitelist() {
  return request({
    url: 'sysmanage/v1/config/getAppWhitelist',
    method: 'get',
    headers: {
      verifyApp: false
    }
  })
}
