import request from '@/utils/request'

// 根据记录ID查询生长发育曲线记录
export function getShiJiangetGrowthDevelopmentRecordByIDFWByBlid(data) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/getGrowthDevelopmentRecordByID',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 根据病历ID查询生长发育曲线列表
export function getGrowthDevelopmentRecordListByBingLiID(id) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/getGrowthDevelopmentRecordListByBingLiID/' + id,
    method: 'get',
    id,
    headers: {
      verifyApp: false
    }
  })
}

// 新增生长发育曲线记录
export function insertGrowthDevelopmentRecord(data) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/insertGrowthDevelopmentRecord',
    method: 'post',
    data,
    headers: {
      verifyApp: false
    }
  })
}

// 删除生长发育曲线记录
export function deleteGrowthDevelopmentRecord(id) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/deleteGrowthDevelopmentRecord/' + id,
    method: 'delete',
    id,
    headers: {
      verifyApp: false
    }
  })
}

// 更新生长发育曲线记录
export function updateGrowthDevelopmentRecord(id, params) {
  return request({
    url: '/app-emrservice/v1/TemperatureList/updateGrowthDevelopmentRecord/' + id,
    method: 'put',
    params,
    headers: {
      verifyApp: false
    }
  })
}
