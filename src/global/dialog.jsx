import { Message } from 'element-ui'
import Vue from 'vue'
import MessageDialog from '@/components/Dialog/MessageDialog.vue'

const voidFunction = () => {}

function createDialogApp() {
  const dialogContainer = document.createElement('div')
  dialogContainer.id = 'custom-dialog-container'
  document.body.prepend(dialogContainer)
  return new Vue({
    el: '#custom-dialog-container',
    data() {
      const messageData = {
        visible: false,
        width: '320px',
        message: '',
        size: undefined,
        title: '信息提示',
        type: 'warning',
        popType: '',
        confirmButtonText: '确 认',
        cancelButtonText: '取 消',
        showConfirmButton: true,
        showCancelButton: true,
        confirmCallback: voidFunction,
        cancelCallback: voidFunction,
        closeCallback: voidFunction
      }

      return {
        messageData
      }
    },
    methods: {
      showMessageDialog(options) {
        this.messageData.visible = true
        this.messageData.confirmCallback = options.confirmCallback
        this.messageData.message = options.message

        this.messageData.width = options.width
        this.messageData.size = options.size
        this.messageData.title = options.title
        this.messageData.type = options.type
        this.messageData.popType = options.popType
        this.messageData.confirmButtonText = options.confirmButtonText
        this.messageData.cancelButtonText = options.cancelButtonText
        this.messageData.showConfirmButton = options.showConfirmButton
        this.messageData.showCancelButton = options.showCancelButton
        this.messageData.cancelCallback = options.cancelCallback
        this.messageData.closeCallback = options.closeCallback ?? voidFunction
        return {
          close: async () => {
            await this.messageData.closeCallback?.()
            this.messageData.visible = false
          },
          confirm: async () => {
            await this.messageData.confirmCallback()
            this.messageData.visible = false
          },
          cancel: async () => {
            await this.messageData.cancelCallback()
            this.messageData.visible = false
          }
        }
      }
    },
    render() {
      const contents = []
      if (this.messageData.visible) {
        contents.push(
          <MessageDialog
            width={this.messageData.width}
            title={this.messageData.title}
            onConfirm={this.messageData.confirmCallback}
            onCancel={this.messageData.cancelCallback}
            onClose={this.messageData.closeCallback}
            visible={this.messageData.visible}
            size={this.messageData.size}
            type={this.messageData.type}
            popType={this.messageData.popType}
            confirmButtonText={this.messageData.confirmButtonText}
            cancelButtonText={this.messageData.cancelButtonText}
            showConfirmButton={this.messageData.showConfirmButton}
            showCancelButton={this.messageData.showCancelButton}
            on={{
              'update:visible': () => {
                this.messageData.visible = false
              }
            }}
          >
            {typeof this.messageData.message === 'function'
              ? this.messageData.message()
              : this.messageData.message}
          </MessageDialog>
        )
      }
      return <div id="custom-dialog-container">{contents}</div>
    }
  })
}
let dialogApp

function getDialogApp() {
  if (!dialogApp) {
    dialogApp = createDialogApp()
  }
  return dialogApp
}

/**
 * 弹出确认框
 * @param message
 * @param options
 */
export function confirm(message, callback, options) {
  const dialogApp = getDialogApp()

  const {
    title,
    errorMsg,
    width,
    size,
    type,
    popType,
    confirmButtonText,
    cancelButtonText,
    showConfirmButton,
    showCancelButton,
    closeCallback,
    errorCallback
  } = options ?? {}

  function handleCancelOrError(error) {
    const err = errorMsg || error?.message

    if (error) {
      console.error(error)
    }
    if (errorCallback) {
      errorCallback(error)
      return
    }
    if (err) {
      Message({
        type: 'error',
        message: err
      })
      return
    }
  }

  return dialogApp.showMessageDialog({
    message,
    width,
    title,
    size,
    type,
    popType,
    confirmButtonText,
    cancelButtonText,
    showConfirmButton,
    showCancelButton,
    confirmCallback: async function () {
      try {
        await callback.call(dialogApp)
      } catch (ex) {
        handleCancelOrError(ex)
      }
    },
    closeCallback,
    cancelCallback: handleCancelOrError
  })
}
