<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title></title>
  <meta name="description" content="">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">

</head>
<style>

</style>
<body>
  <div class="main">
    
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
    $.ajax({
      url: WRT_config.server + '/BrMain.aspx/e_GetSysMgs',
      type: 'Post',
      dataType: "json",
      cache:false,
      crossDomain:true, //设置跨域为true
      xhrFields: {
        withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
      },
      contentType: "application/json; charset=utf-8",
      success: function (msg) {
        if(msg.d.Result){
          $(".mian").html(msg.d.Result)
        }
      },
      error: function (error) {
        
      }
    })
  </script>

</body>
</html>