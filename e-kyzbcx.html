<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<title>科研样本查询</title>
	<meta name="description" content="科研样本查询">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
	<link rel="icon" href="favicon.ico" type="image/x-icon">
	<!-- 公用模块CSS -->
	<!-- <link rel="stylesheet" href="css/e-common.css"> -->
	<script type="text/javascript">
		document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
	</script>
	<!-- 页面CSS -->
	<!-- <link rel="stylesheet" href="css/e-style.css"> -->
	<script type="text/javascript">
		document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
	</script>
</head>
<style>
	#kyzbcx {
		position: relative;
		width: 80%;
		border: 1px ;
		border: 2px solid #B7C3DA;
		/* border: 2px solid red; */
		margin: 0 auto;
	}
	.titleCard {
		position: relative;
		font-size: 20px;
		padding: 5px 20px;
		border-bottom: 1px solid #B7C3DA;
		background-color: #f4f6fe;
		color: #155bd4;
		font-weight: bold;
	}
	/* 查询 */

	.sCondition {
		position: relative;
		padding: 20px 25px;
	}
	.Ltitle {
		position: relative;
		padding: 0 16px;
		font-size: 18px;
    border-left: 3px solid #155bd4;
    height: 23px;
    line-height: 23px;
	}
	.LInputForm {
		position: relative;
	}
	.qCondition {
		position: relative;
		display: flex;
		/* width: 98%; */
		width: calc(100% - 10px);
    margin: auto;
    border-bottom: 1px solid #ccc;
		justify-content: flex-start;
		align-items: center;
	}
	/* 查询条件 */
	.inputSelect {
		position: relative;
		width: 50%;
		/* padding: 35px 0px 35px 30px; */
    padding: 15px 0px 15px 30px;
		/* border: 1px solid red */
	}
	.initCondition {
		position: relative;
		/* padding-bottom: 10px; */
		width:100%;
		display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: baseline;
	}
	.control_bah {
		position: relative;
		display: flex;
    flex-direction: row;
    align-items: center;
	}
	.allBtn {

	}
	
	/* 表格 */

	.sTable {
		position: relative;
		width: 100%;
		margin: 0 auto;
		padding: 0px 0 20px;
	}
	/* 弹窗表格内容 */
	
	.ssoverall_table_frame {
		position: relative;
		width: calc(100% - 40px);
		padding: 20px 0;
		margin: 0 auto;
		text-align: center;
	}
	.ssoverall_table_frame> table {
		position: relative;
		width: 100%;
	}
	#sstable_internal {
		position: relative;
		border: 1px solid #B7C3DA;
		text-align: center;
		width: 100%;
	}
	.ssrow_head >th{
		/* background: #85A2DB; */
		background: #f4f6fe;
		/* color: #FFFFFF; */
		color: #000;
		text-align: center;
		font-family: Microsoft YaHei;
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 30px;
		letter-spacing: 0em;
	}
	.ssrow_nr >td {
		text-align: center;
		font-family: Microsoft YaHei;
		font-size: 14px;
		font-style: normal;
		font-weight: 400;
		line-height: 28px;
		letter-spacing: 0em;
	}
	.ssline_nr tr:nth-child(odd) {
		background: #fff;
	} 
	.ssline_nr tr:nth-child(even){
		background: #85a2db47;
	}
	/* 表格内容 */
	.overall_table_frame {
		position: relative;
		width: calc(100% - 40px);
		border: 1px solid #57595c;
		margin: 0 auto;
		/* margin-bottom: 10px; */
	}
	.overall_table_frame> table {
		position: relative;
		width: 100%;
	}
	#table_internal {
		position: relative;
		border: 1px solid #B7C3DA;
	}
	.row_head >th{
		/* background: #85A2DB; */
		background: #f4f6fe;
		/* color: #FFFFFF; */
		color: #000;
		text-align: center;
		font-family: Microsoft YaHei;
		font-size: 18px;
		font-style: normal;
		font-weight: 400;
		line-height: 40px;
		letter-spacing: 0em;
	}
	.row_nr >td {
		text-align: center;
		font-family: Microsoft YaHei;
		font-size: 16px;
		font-style: normal;
		font-weight: 400;
		line-height: 36px;
		letter-spacing: 0em;
	}
	.kybsWin {
		padding-bottom: 10px;
    display: flex;
    flex-direction: row;
	}
	.kybsWinBtn {
		position: relative;
		float: right;
	}
</style>

<body style="padding:10px">
	<div id="kyzbcx">
		<!-- 标贴 -->
		<div class="titleCard">科研样本查询</div>
		<!-- 查询选择条件 -->
		<div class="sCondition">
			<div class="Ltitle label-name">
				条件查询
			</div>
			<div class="LInputForm">

			</div>
		</div>
		<!-- 展示表单 -->
		<div class="sTable">

		</div>
	</div>
	<!-- 配置文件 -->
	<script src="./e-config.js"></script>
  <script src="./lib/echarts/echarts.min.js"></script>
  <script src="./lib/charts/echarts.js"></script>
	<!-- 公用模块JS -->
	<!-- <script src="js/e-common.js"></script> -->
	<script type="text/javascript">
		document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
	</script>
	<!-- 临床营养中心会诊js文件 -->
	<script type="text/javascript">
		document.write("<script type='text/javascript' src='js/e-kyzbcx.js?v=" + Date.now() + "'><\/script>");
	</script>
  
</body>

</html>