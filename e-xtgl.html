<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>首页</title>
  <meta name="description" content="首页">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
.text{
  width: 140px;
  height: 25px;
  border: 1px solid #ddd
}
.head_inner .Wdate,.select{
  width: 140px;
  height: 25px;
  border: 1px solid #ddd;
}
label{
  width: 300px;
}
span{
  width: 90px;
  display: inline-block;
  text-align: right;
}
.head_inner{
  padding: 20px;
  width: 680px;
}
.xtgl_table{
  padding: 20px;
  width: 80%;
}
.table_head{
  background: #b0c4de;
  line-height: 25px;
  font-size: 15px;
}
#xtgl_data td,.table_head th{
  text-align: center;
}
#xtgl_data tr:nth-child(even) {
    color: #4e4e4f;
    background: #d8e2f4;
}
</style>
<body>
  <div class="main">
    <div id="head_info">
      
    </div>
    <div style="margin-top: 15px;">
      <table class="xtgl_table">
        <thead class="table_head">
          <th width="100px">专科</th>
          <th width="80px">姓名</th>
          <th width="80px">住院号</th>
          <th width="50px">性别</th>
          <th width="120px">出生日期</th>
          <th width="80px">病区</th>
          <th width="50px">床位号</th>
          <th width="160px">入院时间</th>
          <th>入院诊断</th>
        </thead>
        <tbody id="xtgl_data">

        </tbody>
      </table>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-xtgl.js'><\/script>");
  </script>
</body>
</html>