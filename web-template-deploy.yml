apiVersion: apps/v1
kind: Deployment
metadata:
  name: $WEB_NAME
  namespace: $NAMESPACE
  labels:
    app: $WEB_NAME
spec:
  replicas: 1
  selector:
    matchLabels:
      app: $WEB_NAME    # has to match .spec.template.metadata.labels
  template:
    metadata:
      labels:
        app: $WEB_NAME # has to match .spec.selector.matchLabels
    spec:
      imagePullSecrets:
        - name: harbor-secret
      containers:
        - name: $WEB_NAME
          image: $WEB_IMAGE #10.104.60.11/k8s/web-safe:v1.0.3.test
          imagePullPolicy: Always
          #args: [--spring.profiles.active=cluster]
          ports:
            - containerPort: 80
              name: $WEB_NAME
          resources:
            requests:
              memory: 300Mi
              cpu: 200m
            limits:
              memory: 500Mi
              cpu: 400m

---
kind: Service
apiVersion: v1
metadata:
  name: $WEB_NAME
  namespace: $NAMESPACE
  labels:
    app: $WEB_NAME
spec:
  ports:
    - name: $WEB_NAME
      port: 80
  selector:
    app: $WEB_NAME

---
#ingress
#apiVersion: extensions/v1beta1
#kind: Ingress
#metadata:
#  name: web-auth # Ingress 的名称
#  namespace: wfw-prod2 # 命名空间 接下来的部署都会使用这个命名空间
#spec:
#  rules:
#    - host: auth.wyyy.com # 通过 Ingress 映射的地址 , 需要通过host去配置,下面讲解
#     http:
#        paths:
#          - path: /
#            backend:
#              serviceName: web-auth # 这里对应着service 的 名字
#              servicePort: 80 # 对应着需要映射的service的端口
