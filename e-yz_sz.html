<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>医嘱界面</title>
  <meta name="description" content="医嘱界面">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  #idtitle {
    table-layout: fixed;
    font-size: 12px;
    width: 2280px;
    border-color: black;
  }
  #btn_list {
    width: max-content;
  }

  .btn_lists {
    color: rgba(51, 51, 51);
    border: 1px solid #999;
    border-radius: 25px;
    padding: 3px 15px;
    background: #fff;
  }

  .search_K {
    width: 94%;
    padding: 0 0px 10px 0;
    display: flex;
    align-items: center;
    justify-content: flex-start;
  }
  
  .left_Search_input {
    position: relative;
    /* width: 120% !important; */
    border: 1px solid #BFBFBF;
    /* margin: auto; */
    box-shadow:none;
  }
  .left_Search_input:focus {
    background: #fff;
    box-shadow:none;
  }
  .input-group-btn {
    /* padding: 0 20px; */
  }
  /* #left_Search_btn{
    position: relative;
    background: none;
    color: #4975cb;
    border: none;
    z-index: 999;
  } */

  #yztitle_pad {
    padding-top: 3px;
    width: 1590px;
  }

  .container-yz_sz .tb_title {
    table-layout: fixed;
    font-size: 13px;
    width: 2280px;
    border-color: black;
  }

  .container-person {
    background: linear-gradient(180deg, rgba(61, 108, 200, 0.24) 0%, rgba(61, 108, 200, 0) 100%);
    border-bottom: 1px solid #3D6CC8;
    padding: 8px 0px 5px 10px;
    width: 2415px;
  }

  #yz_pad {
    width: fit-content;
    height: 170px;
    overflow: hidden auto;
  }

  #yz_pad .container-person {
    padding: 3px 0px 0px 3px;
  }

  #yzdata_list .trc td {
    height: 30px;
  }

  #newyztitle_pad {
    width: 1590px;
    height: 80px;
  }

  #newyz_pad {
    width: 2415px;
    max-height: 265px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  #div_newBtn {
    width: 1600px;
    position: fixed;
    bottom: 0px;
    height: 33px;
  }

  .container-yz_sz .table_header {
    height: 40px;
    /* color: #4e4e4f;
    background: #d8e2f4; */
    color: #fff;
    background: #4682b4;
  }
  .mclist{
    width: 673px;
  }
  .isModel {
    border: 1px solid;
    position: absolute;
    height: 265px;
    z-index: 10;
    background-color: white;
    visibility: visible;
    overflow: hidden auto;
    display: none;
  }

  .yflist {
    width: 72px;
  }

  .yzlxlist {
    width: 60px;
  }

  .pllist {
    width: 120px;
  }

  .gysjlist {
    width: 67px;
  }

  .lblist {
    width: 47px;
  }
  /* #yzdata_list .trc td{
    position: relative;
  } */
  .container-yz_sz #yzdata_list .lblist .li,
  .container-yz_sz #yzdata_list .yzlxlist .li,
  .container-yz_sz #yzdata_list .yflist .li,
  .container-yz_sz #yzdata_list .pllist .li,
  .container-yz_sz #yzdata_list .gysjlist .li {
    border: none 0px;
    cursor: context-menu;
  }

  .container-yz_sz #yzdata_list .isModel input:active {
    background-color: cornflowerblue;
  }
  .container-yz_sz #yzdata_list .isModel .changeItem{
    background-color: cornflowerblue;
  }
  .container-yz_sz #yzdata_list .isModel .changeItemMC{
    /* background-color: rgb(175, 168, 168); */
    background-color: cornflowerblue;
  }
  .container-yz_sz .mclist td {
    margin: 0px;
    overflow: hidden;
    white-space: nowrap;
    font-size: 14px;
    height: 40px;
    line-height: 35px;
  }

  .container-yz_sz .mclist .trlist:active {
    background-color: lightblue
  }

  .container-yz_sz #th_list tr td {
    border: 1px solid #fff;
  }

  #brmain>.combo-p>.panel-body-noheader {
    width: 180px;
    height: auto;
    overflow-x: hidden;
    padding: 0;
  }

  #tb_yplistdetail {
    font-size: 12px;
  }

  #tb_yplistdetail tbody tr:nth-child(even) {
    height: 40px;
    color: #4e4e4f;
    background: #fff;
  }

  #tb_yplistdetail tbody tr:nth-child(odd) {
    height: 40px;
    color: #4e4e4f;
    background: #d8e2f4;
  }

  #tb_yplistdetail tbody tr:nth-child(1) {
    height: 40px;
    color: #000;
    background: #b0c4de;
  }
  .container-yz_sz #data_list tr .mc_title{
    text-align: left;
    padding-left: 5px;
  }
  #if_tysj tr td,#dv_mbHtml tr td,#tb_yplistdetail tbody tr td,#tb_mdetail tbody tr td {
    text-align: center;
    margin: 0px;
    overflow: hidden;
    white-space: nowrap;
    font-size: 14px;
    height: 40px;
  }
  .trblue>td{
    color: #266be5;
  }
  #data_list td{
    border-left: 1px solid rgb(0 0 0 / 10%);
    border-right: 1px solid rgb(0 0 0 / 10%);
  }
  #data_list .trbody{
    border-top: 1px solid #4D7FA9;
  }
  #data_list .trnobody{
    border-top: 1px solid #B2C7D8;
  }
  #data_list tr:hover {
    background-color: #CCFFFF;
  }
  .trbody_color{
    background-color: #CCFFFF;
  }
  .trred>td{
    color: #ff0000;
  }
  #dv_mbHtml .headerbody{
    height: 40px;
    color: #4e4e4f;
    background: #b0c4de;
  }
  #if_tysj .headerbody{
    height: 40px;
    color: #4e4e4f;
    background: #b0c4de;
  }
  #if_tysj .iziModal-wrap .izi_modal .izi_modal_content{
    height: 900px;
    overflow-y: auto;
  }
  #if_tysj #txt_mc{
    border: 1px solid;
    padding: 0 5px;
  }
  .zlyzmb_left{
    width: 170px;
    height: 530px;
    font-size: 12px;
    float: left;
    border-right: dimgray 1pt solid;
    border-top: dimgray 1pt solid;
    overflow-Y: auto;
    overflow-X: hidden;
    border-left: dimgray 1pt solid;
    border-bottom: dimgray 1pt solid;
  }
  .zlyzmb_left .title{
    height: 20px;
    background-color: #E6E6FA;
    vertical-align: middle;
    border: solid 1px silver;
  }
  .zlyzmb_left .title1{
    height: 20px;
    background-color: #E6E6CA;
    vertical-align: middle;
    border: solid 1px silver;
  }
  .zlyzmb_left .detail{
    background-color: #E6E67A;
    vertical-align: middle;
    border: solid 1px silver;
  }
  .zlyzmb_right{
    width: 725px;
    height: 530px;
    float: right;
  }
  .moveClass{
    background: #b0c4de;
  }
  #newyztitle_pad,#newyz_pad{
    display: none;
  }
  #tb_zlyz th{
    text-align: center;
  }
  #tb_zlyz input{
    border: 1px solid rgb(0, 0, 0,0.3);
    font-family:宋体;
    font-size:12px;
    ime-mode: disabled 
  }
  #tb_zlyz .str{
    border-bottom: dimgray 1px dashed;
    height: 30px;
  }
  .drug_name:hover{
    cursor: pointer;
    text-decoration: none;
  }
  html,body{
    width: 100%;
  }
  #container-yz_sz{
    width: 100%;
    overflow-x: auto;
  }
  .bhleft{
    cursor: pointer;
  }
  .bhleft:hover{
    text-decoration: none;
  }
  .container-yz_sz #data_list>tr>.mc_title{
    white-space:normal
  }
  .tag-no {
    font-family: Microsoft YaHei;
    font-style: normal;
    font-weight: bold;
    font-size: 14px;
    line-height: 16px;
  }
  .zhmbleft{
    width: 170px;
    height: 530px;
    font-size: 12px;
    left: 10px;
    float: left;
    top: 4px;
    border-right: dimgray 1pt solid;
    border-top: dimgray 1pt solid;
    overflow-Y: auto;
    overflow-X: hidden;
    border-left: dimgray 1pt solid;
    border-bottom: dimgray 1pt solid;
  }
  .zhmbleft .Zlmb_title{
    height: 20px;
    background-color: #E6E6FA;
    vertical-align: middle;
    border: solid 1px silver;
  }
  .zhmbleft .Zlmb_title1{
    height: 20px;
    background-color: #E6E6CA;
    vertical-align: middle;
    border: solid 1px silver;
  }
  .Zldetail{
    background-color: #E6E67A;
    vertical-align: middle;
    border: solid 1px silver;
    display: none;
  }
  .Zl_input{
    font-family:宋体;
    font-size:12px;
    ime-mode: disabled 
  }
  .zhmbleft .Zhmb_title1 {
    height: auto;
    background-color: #E6E63A;
    vertical-align: middle;
    border: solid 1px silver;
    cursor: pointer;
  }
  .zhmbleft .Zhmb_title {
    height: 20px;
    background: linear-gradient(180deg, #FFFFFF 0%, #D1D1D1 100%);
    border: 1px solid #9A9A9A;
    /* background-color: #E6E6FC; */
    vertical-align: middle;
    /* border: solid 1px silver; */
  }
  .zlmbright{
    width:720px;
    height:530px; 
    z-index: 1; 
    float:right;
    top: 4px;
  }
  .zhmbright{
    width:1020px;
    height:530px; 
    z-index: 1; 
    float:right;
    top: 4px;
  }
  .zhmbright #tb_yzmbmx .selected {
    background-color: #C7EDCC;
  }
  .zhmbright .trc:hover {
    background-color: #CCFFFF;
  }
  .zhmbright tr:nth-child(odd){
    background: rgba(85, 85, 85, 0.1);
  }
  .zhmbright tr:nth-child(even){
    background: #FFFFFF
  }
  .legython{
    background: #4682b4;
    border: 1px solid #3D6CC8;
    color: #fff;
    display: block;
    width: 1200px;
    padding: 0 5px;
    font-size: 21px;
    line-height: inherit;
    margin: 0;
  }
  .cfy_text{
    border: 1px solid #c1c1dd;
    line-height: 25px;
    font-size: 14px;
    padding-left: 5px;
    background: aliceblue;
  }
  /* .cfy_text:focus{
    background: aliceblue;
  } */
  .text_class{
    border: 1px solid #000;
    border-radius: 3px;
  }
  #zykdd_table select{
    min-width: 70px;
    height: 24px;
  }
  #zykdd_table label{
    width:70px;text-align: right;
  }
  #zykdd_table input{
    margin-left: 3px;
  }
  #zykdd_table textarea{
    margin-left: 3px;
    margin-top: 2px;
  }
  .e_btn{
    line-height: 2em;
  }
  .none{
    display: none;
  }
  .gjm_title{
    width: 105px;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: context-menu;
  }
  .yz_sz_jxzl_select{
    width: 100px;
    height: 30px;
    border: 1px solid #b0a7a7;
    border-radius: 5px;
    text-align: center;
  }
  .yz_sz_jxzl_text{
    width: 50px;
    height: 30px;
    border: 1px solid #b0a7a7;
    border-radius: 5px;
    text-align: center;
  }
#tb_Xtbhdz tbody tr:nth-child(1){
  height: 40px;
  color: #000;
  background: #b0c4de;
  text-align: center;
}
#tb_Xtbhdz tbody tr td {
  text-align: center;
  height: 40px;
  color: #000;
}
#tb_Xtbhdz tbody tr td a{
  cursor: pointer;
  margin-left: 3px;
}
#tb_Xtbhdz tbody tr:nth-child(odd) {
  color: #4e4e4f;
  background: #d8e2f4;
}
#tssm_input{
  border: 1px solid;
  width: 315px;
  height: 30px;
  margin: 5px 0;
}
#lishilistdetail thead tr {
    height: 40px;
    color: #000;
    background: #b0c4de;
}
#lishilistdetail tbody tr:nth-child(even) {
    height: 40px;
    color: #4e4e4f;
    background: #fff;
}
#lishilistdetail tbody tr:nth-child(odd) {
    height: 40px;
    color: #4e4e4f;
    background: #d8e2f4;
}
</style>

<body id="brmain">
  <div id="container-yz_sz" class="container-yz_sz" style="overflow-y: hidden;height: calc(100% - 33px);">
    <div class="table-list" id="btn_list">
    </div>
    <div id="yztitle_pad">
      <table border="0" class="tb_title" cellpadding="0" cellspacing="1">
        <tbody id="th_list">

        </tbody>
      </table>
    </div>
    <div id="yz_pad">
      <table border="0" class="tb_title" cellpadding="0" cellspacing="1">
        <tbody id="data_list" style="border-bottom: 1px solid rgb(0 0 0 / 10%);">

        </tbody>
      </table>
    </div>
    <div id="box_pad" style="display:none">
      <img id="img_menu" src="./images/arrow_close.png" onclick="imgtab('open')" style="transform: translate(670px, 10px)">
    </div>
    <div id="newyztitle_pad">
      <div id="container-person" class="container-person">
      </div>
      <table border="0" class="tb_title" cellpadding="0" cellspacing="1">
        <tbody id="yz_list">
        </tbody>
      </table>
    </div>
    <div id="newyz_pad">
      <table border="0" class="tb_title" cellpadding="0" cellspacing="1">
        <tbody id="yzdata_list">
        </tbody>
      </table>
    </div>
    <div id="div_newBtn">
      <table cellpadding="5px" cellspacing="1px" class="tb_title">
        <tbody>
          <tr>
            <td>
              <div id="dv_newButton">

              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <!-- <div id="yplist">
    <input type="text" class="li" lx="ff" readonly="" value="口服" val="po">
    <input type="text" class="li" lx="ff" readonly="" value="肌肉注射" val="im">
    <input type="text" class="li" lx="ff" readonly="" value="静脉注射" val="iv">
    <input type="text" class="li" lx="ff" readonly="" value="静脉滴注" val="ivgtt">
  </div> -->
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 登录页js文件 -->
  <!-- <script src="js/e-yz_sz.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-yz_sz.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 惠每质控js（在本地可以注释掉不然网页加载会很慢） -->
  <script>
    if(WRT_config.hmzkZW==undefined || !WRT_config.hmzkZW){
      document.write("<script type='text/javascript' src='http://**************/cdss/jssdk?v=4.0&ak=CCD9D75192ACC4529888523BCD5AB9E2'><\/script>");
      // document.write("<script type='text/javascript' src='http://"+  WRT_config.server + "/zyblws/masonQC/wyyyMason.js?v=" + Date.now() + "'><\/script>");
      document.write("<script type='text/javascript' src='../zyblws/masonQC/wyyyMason.js?v=" + Date.now() + "'><\/script>");
    }
  </script>
</body>

</html>