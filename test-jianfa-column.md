# 煎法列功能实现测试

## 修改内容

已成功在 `DrawerTable.vue` 组件中添加了"煎法"列功能，满足所有需求。

## 主要修改

### 1. 数据结构修改
- 将 `drawerColumns` 改为 `baseDrawerColumns` (基础列配置)
- 新增 `drawerColumns` 计算属性，动态生成包含煎法列的列配置

### 2. 计算属性 `drawerColumns`
```javascript
drawerColumns() {
  const columns = [...this.baseDrawerColumns]
  
  // 如果是中医科室，在用法列后面插入煎法列
  if (this.inpatientInit?.yiShengZKZYKS === 1) {
    const zhiXingFFIndex = columns.findIndex(col => col.value === 'zhiXingFF')
    if (zhiXingFFIndex !== -1) {
      const jianFaColumn = {
        value: 'jianFa',
        label: '煎法',
        props: { width: '80' },
        component: 'DrawerTableSelect',
        options: []
      }
      columns.splice(zhiXingFFIndex + 1, 0, jianFaColumn)
    }
  }
  
  return columns
}
```

### 3. 动态列配置 `getDynamicColumn`
```javascript
// 处理煎法列
if (column.value === 'jianFa') {
  const isEditable = row.yiZhuLX === 'cy' || row.yiZhuLX === 'zcydy'
  return {
    ...column,
    options: this.mapOptions(this.inpatientInit?.caoYaoJF, 'fangFaDM', 'fangFaMC'),
    disabled: !isEditable
  }
}
```

### 4. 子组件修改
修改 `DrawerTableSelect.vue` 的 `isDisabled` 计算属性：
```javascript
isDisabled() {
  // 支持通过 column.disabled 属性禁用
  if (this.column.disabled !== undefined) {
    return this.column.disabled
  }
  // 原有的禁用逻辑
  return this.column.value === 'yiZhuLX' && !!this.row.mingCheng
}
```

### 5. Watch 修改
所有引用 `drawerColumns` 的地方都改为引用 `baseDrawerColumns`：
- `inpatientInit` watch
- `yaoPinJianSuo` watch  
- `tabActive` watch
- `handleUpdateColumns` 方法

## 功能特性

### 显示条件
- ✅ 仅当 `inpatientInit.yiShengZKZYKS === 1` (中医科室) 时显示煎法列
- ✅ 煎法列插入在"用法"列的后面

### 字段配置
- ✅ 字段名：`jianFa`
- ✅ 列标题：`煎法`
- ✅ 组件类型：`DrawerTableSelect`
- ✅ 列宽：80px

### 编辑权限
- ✅ 当 `yiZhuLX` 为 "cy" 或 "zcydy" 时可编辑
- ✅ 其他医嘱类型时禁用

### 数据源
- ✅ 选项来源：`inpatientInit.caoYaoJF`
- ✅ 使用 `mapOptions(inpatientInit.caoYaoJF, 'fangFaDM', 'fangFaMC')` 格式化

## 测试场景

### 1. 非中医科室
- 煎法列不显示
- 表格正常显示其他列

### 2. 中医科室 + 草药医嘱
- 煎法列显示在用法列后面
- 煎法列可编辑
- 下拉选项来自 `inpatientInit.caoYaoJF`

### 3. 中医科室 + 非草药医嘱
- 煎法列显示但禁用
- 下拉框呈灰色不可点击状态

### 4. 动态切换医嘱类型
- 从非草药切换到草药：煎法列变为可编辑
- 从草药切换到非草药：煎法列变为禁用

## 数据格式要求

确保 `inpatientInit` 包含以下数据：

```javascript
{
  "yiShengZKZYKS": 1, // 1=中医科室，其他值=非中医科室
  "caoYaoJF": [
    {
      "fangFaDM": "代码值", // 作为 value
      "fangFaMC": "显示名称" // 作为 label
    }
  ]
}
```

## 注意事项

1. 煎法列只在中医科室显示，不会影响非中医科室的表格布局
2. 煎法列的禁用状态会根据每行的 `yiZhuLX` 值动态变化
3. 所有现有功能保持不变，新增功能不会影响原有逻辑
4. 如果 `caoYaoJF` 数据不存在，会返回空数组，不会报错

## 修改完成状态

✅ **煎法列功能已完成并验证**

所有必要的修改都已成功应用：

### 文件修改清单
1. **DrawerTable.vue** - 主要功能实现
   - 数据结构重构 (`baseDrawerColumns` + `drawerColumns` 计算属性)
   - 动态列配置 (`getDynamicColumn` 方法扩展)
   - Watch 方法更新

2. **DrawerTableSelect.vue** - 支持禁用功能
   - `isDisabled` 计算属性扩展，支持 `column.disabled`

### 功能验证
- ✅ 显示条件：仅中医科室显示
- ✅ 位置正确：在用法列后面
- ✅ 编辑权限：草药医嘱可编辑，其他禁用
- ✅ 数据源：正确使用 `caoYaoJF` 数据
- ✅ 动态响应：医嘱类型变化时状态自动更新

该功能现在可以根据医生科室类型和医嘱类型智能显示和控制煎法列，完全满足用户需求。
