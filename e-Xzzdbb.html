<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>修正诊断修改报表</title>
  <meta name="description" content="修正诊断修改报表">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>

<body id="">
  <div class="xzzdxgbb">
    <!-- 输入查询内容  病案号+病区（选病区控件支持文字输入选择匹配） -->
    <div class="inputType">
    </div>
    <!-- 查询结果 -->
    <div class="resTB">

    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 按病人基本信息查询病历页js文件 -->
  <!-- <script src="js/e-blcxgj.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-Xzzdbb.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>
<style>
  .xzzdxgbb {
    position: relative;
    width: 100%;
    /* margin: 0 auto; */
  }

  .inputType {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 50px 50px 20px;
  }

  .infoCondition {
    position: relative;
    padding: 0 16px;
    font-size: 20px;
    /* border-left: 3px solid #155bd4;
		height: 34px;
    line-height: 34px; */
    display: flex;
    flex-direction: row;
  }

  .infoR {
    position: relative;
  }

  .infoRLabel {
    display: flex;
  }

  .searchT,
  .searchT1 {
    position: relative;
    left: 20px;
  }

  .radio-inline {
    font-size: 16px;
    position: relative;
    height: 26px;
    line-height: 22px;
  }

  .control-label {
    display: flex;
    align-items: center;
  }

  .iTabel {
    font-size: 16px;
    padding: 0 20px 0 5px;
  }

  .iTabel1 {
    width: 98px;
    font-size: 16px;
    line-height: 34px;
  }

  .showInput {
    position: relative;
    padding: 0 20px 0 0;
  }


  .iput {
    /* height: 25px; */
    width: 186px;
    padding-left: 5px;
  }

  .iop {
    /* height: 25px; */
    margin-left: 5px;
  }

  .iop:hover {
    background-color: #1E90FF;
  }

  .op-list {
    border: solid 1px #767676;
    width: 186px;
    max-height: 150px;
    height: auto;
    overflow-y: auto;
  }

  ::-webkit-scrollbar {
    /*隐藏滚轮*/
    display: none;
  }

  .hidden {
    display: none;
  }

  .resTB {
    position: relative;
    width: 90%;
    margin: 0 auto;
    padding: 25px 0;
    text-align: center;
  }

  .overall_table_frame {
    position: relative;
    width: calc(100% - 40px);
    border: 1px solid #B7C3DA;
    margin: 0 auto;
    /* margin-bottom: 10px; */
  }

  .overall_table_frame>table {
    position: relative;
    width: 100%;
  }

  .row_head>th {
    background: #85A2DB;
    /* background: #f4f6fe; */
    /* color: #FFFFFF; */
    color: #000;
    text-align: center;
    font-family: Microsoft YaHei;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 30px;
    letter-spacing: 0em;
  }

  .row_nr>td {
    text-align: center;
    font-family: Microsoft YaHei;
    color: #000;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    letter-spacing: 0em;
    padding: 4px 8px;
  }
</style>

</html>