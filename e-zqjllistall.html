<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>病例知情记录汇总</title>
  <meta name="description" content="病例知情记录汇总">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <style>
    /* 病例知情记录汇总 */
    /* 最外层框 */
    .container_bzqjllistall {
      height: 100%;
    }
    .bzqjllistall_inner {
      position: relative;
      margin-top: 27px;
    }
    .flex-row {
      display: flex;
      flex-wrap: nowrap;
    }
    /* 左边 */
    /* 左侧外框 */
    .bzqjllistall_inner_left {
      position: relative;
      /* width: 19.3%; */
      width: 302px;
      margin-right: 10px;
    }
    .blzqlist {
      position: relative;
      width: 100%;
    }
    
    /* 第一行 */
     /* .blzqlist li{
      display: inline-block;
      list-style-type: none;
      margin-right: 4px;
      margin-bottom: -1px;
      padding: 0;
      position: relative;
      text-align: center;
      -moz-border-radius-topleft: 5px;
      -moz-border-radius-topright: 5px;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
    } */
    .nav-tabs {
      border-bottom: none;
    }
    .nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
        border: none;
        border: 1px solid #E8E8E8;
        border-bottom: 3px solid #3D6CC8;
        cursor: default;
        background-color: none;
        color: #3D6CC8;
        margin-right: 3px;
        -moz-border-radius-topleft: 5px;
        -moz-border-radius-topright: 5px;
        border-radius: 4px 4px 0px 0px;
        /* border-top-left-radius: 5px;
        border-top-right-radius: 5px; */
    }
    .nav-tabs>li>a {
      margin-right: 2px;
      line-height: 1.42857143;
      border: 2px solid transparent;
      /* border: 1px solid #E8E8E8;  */
      border-radius: 4px 4px 0 0;
      /* border-bottom: none; */
      /* color: #9F9F9F; */
      color: #a18e8e;
      margin: 0 1px;
      padding:10px 8px;
      -moz-border-radius-topleft: 5px;
      -moz-border-radius-topright: 5px;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
      font-size: 15px;
    }
    /* .nav-tabs>li>a>span {
      position: relative;
      font-size: 12px;
    } */
    /* .nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover{
      border: 1px solid #E8E8E8;
      color: #3D6CC8;
      cursor: default;
      background-color: #fff;
      border-bottom-color: transparent;
      -moz-border-radius-topleft: 5px;
      -moz-border-radius-topright: 5px;
      border-top-left-radius: 5px;
      border-top-right-radius: 5px;
    } */
    /* .line1_right{
      position: relative;
      top: -21px;
      border: 1px solid #3D6CC8;
      border-radius: 4px;
      width: 20px;
      height: 20px;
      float: right;
      margin: 0;
    } */
    /* 箭头 */
    /* #arrow {
      position: relative;
      color: #3D6CC8;
      font-size: 8px;
      margin: auto;
      left: 3px;
      top: -0.5px;
    } */
    /* 左侧下方内容 */
    /* .tab-pane {
      border: 1px solid #E8E8E8;
      border-top: none;
    } */
    .zqhz,.xzjl,.xzjlss{
      border: 1px solid #E8E8E8;
      padding-bottom: 20px;
      /* border-top: none; */
    }
    .bzqjllistall_inner_left .list_title {
      position: relative;
      line-height: 24px;
      text-align: center;
      color: #fff;
      top: 22px;
      border: 1px solid #3D6CC8;
      background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%);
    }
    .bzqjllistall_inner_left .list_inner {
      position: relative;
      margin-top: 36px;
      padding: 0 24px;
      /* 左侧是否需要滚动条 */
      /* height: 660px;
      overflow-y: auto; */
    }

    .bzqjllistall_inner_left .list_inner>li {
      display: flex;
      margin: 12px 0;
      cursor: pointer;
    }

    .bzqjllistall_inner_left .list_inner>li>span>a {
      color: #707070;
      font-size: 14px;
    }

    .bzqjllistall_inner_left .list_inner>li>span>a:hover {
      color: #3D6CC8;
    }

    .bzqjllistall_inner_left .list_inner>li>a>i {
      font-size: 12px;
      margin-right: 6px;
      margin-top: 3px;
    }
    .left_Search {
      position: relative;
      padding-top: 36px;
      width: 80%;
      height: 34px;
      margin: auto;
      padding-bottom: 10px;
    }
    .left_Search_input {
      position: relative;
      width: 120% !important;
      border: 1px solid #BFBFBF;
      margin: auto;
      box-shadow:none;
    }
    .left_Search_input:focus {
      background: #fff;
      box-shadow:none;
    }
    #left_Search_btn{
      position: relative;
      /* left: 85%;
      top: -32px; */
      background: none;
      color: #4975cb;
      border: none;
      z-index: 999;
    }
    .search_res {
      position: relative;
      margin-top: 25px;
      padding: 0 24px;
    }
    .search_res>li {
      display: flex;
      /* margin: 12px 0; */
      cursor: pointer;
      margin: auto;

    }
    .search_res>li>span>a {
      color: #707070;
      font-size: 16px;
    }
    .search_res>li>span>a:hover {
      color: #3D6CC8;
    }
    .search_res>li>a>i {
      font-size: 12px;
      margin-right: 6px;
      margin-top: 3px;
    }

    /* 右边 */
    /* .bzqjllistall_inner_right {
      border: 1px solid #E8E8E8;
      background: #fff;
      height: 775px;
    } */

    .bzqjllistall_inner_right .panel {
      position: relative;
      height: 100%;
      box-shadow: none;
      font-size: 14px;
      border-radius: 0;
      margin-top: 15px;
    }

    .bzqjllistall_inner_right .panel-group .panel+.panel {
      margin-top: 2px;
    }

    .bzqjllistall_inner_right .panel-title {
      background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%);
      color: #fff;
      border: 1px solid #3D6CC8;
      padding: 0 30px 0 15px;
      line-height: 24px;
      /* text-align: center; */
      display: flex;
      /* justify-content: flex-start; */
      justify-content: space-between;
      cursor: pointer;
      font-size: 14px;
    }
    .bzqjllistall_inner_right .panel-title .btnTitle {
      position: relative;
      right: 20%;
    }
    .bzqjllistall_inner_right .panel-title>span>span>a {
      /* color: #fff; */
      margin-left: 10px;
      margin-right: 5px;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      text-decoration: none;
      border: 1px solid #fff;
      background: #fff;
      padding: 2px 3px;
      color: #000;
      border-radius: 3px;
    }
    .bzqjllistall_inner_right .panel-title>span>span>a:hover {
      /* text-decoration: none; */
      color: #fff;
      background: #707070;
    }

    /* 空白页文书内容 */
    .bzqjllistall_inner_right .panel-title1 {
      background: linear-gradient(180deg, #3D6CC8 0%, rgba(61, 108, 200, 0.6) 100%);
      color: #fff;
      border: 1px solid #3D6CC8;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
    }
    
    .bzqjllistall_inner_right .panel-title1>span>a {
      /* color: #fff; */
      margin-left: 10px;
      margin-right: 5px;
      line-height: 24px;
      text-align: center;
      cursor: pointer;
      font-size: 14px;
      text-decoration: none;
      border: 1px solid #fff;
      background: #fff;
      padding: 2px 3px;
      color: #000;
      border-radius: 3px;
    }
    .bzqjllistall_inner_right .panel-title1>span>a:hover {
      /* text-decoration: none; */
      color: #fff;
      background: #707070;
    }
    /* .bzqjllistall_inner_right .panel-title>span>a:hover {
      color: #FFFFFF;
      background: #888888;
    }
    .bzqjllistall_inner_right .panel-title>span>a {
        color: #fff;
        margin-left: 10px;
        margin-right: 5px;
        line-height: 24px;
        text-align: center;
        cursor: pointer;
        font-size: 14px;
        text-decoration: none;
        color: rgb(112, 112, 112);
        background: rgb(255, 255, 255);
        border: 1px solid #888888;
        padding: 3px 4px;
        border-radius: 4px;
    } */
    /* .bzqjllistall_inner_right .panel-title.collapsed {
      background: linear-gradient(180deg, rgba(61, 108, 200, 0.24) 0%, rgba(61, 108, 200, 0) 100%);
      color: #3D6CC8;
    }
    .bzqjllistall_inner_right .panel-title.collapsed>span>a {
      background: linear-gradient(180deg, rgba(61, 108, 200, 0.24) 0%, rgba(61, 108, 200, 0) 100%);
      color: #3D6CC8;
    } */
    .bzqjllistall_inner_right .panel-content {
      padding: 12px;
      overflow-y: scroll;
    }
    .blzqnr {
      position: relative;
      height: 662px;
      overflow-y: scroll;
    }
    .right_three_btn1 {
      position: relative;
      margin-left: 14px;
      margin-bottom: 30px;
      margin-top: 24px;
    }
    .right_three_btn {
      position: relative;
      margin-left: 14px;
      margin-bottom: 30px;
    }
    .right_three_btn>button {
      margin-right: 20px;
    }
  </style>
</head>

<body>
  <div class="container_bzqjllistall flex-row flex-column">
    <div class="bzqjllistall_inner flex-row flex-fill">
      <div class="bzqjllistall_inner_left">
      </div>
      
      <div class="bzqjllistall_inner_right flex-fill">
        <div class="panel-group">
         
        </div>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面js文件 -->
  <!-- <script src="js/e-zqjllistall.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-zqjllistall.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>

</html>