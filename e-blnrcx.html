<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>按病历内容查询</title>
  <meta name="description" content="按病历内容查询">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- 公用模块CSS -->
  <link rel="stylesheet" href="css/e-common.css">
  <!-- 页面CSS -->
  <link rel="stylesheet" href="css/e-style.css">
</head>

<body>
  <div class="main">
    <div id="container-blnrcx" class="container-blnrcx">
      <form action="" id="blnrcx_form"></form>
    </div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <script src="js/e-common.js"></script>
  <!-- 按病历内容查询页js文件 -->
  <script src="js/e-blnrcx.js"></script>
</body>
<style>
  /*按病历内容查询病历*/
.container-blnrcx{
  /* width: 1818px; */
  padding: 21px 25px 470px 37px;
}
/* 表格标题 */
.container-blnrcx .table-header{
  position: relative;
  background: linear-gradient(180deg, rgba(61, 108, 200, 0.24) 0%, rgba(61, 108, 200, 0) 100%);
  border: 1px solid #3D6CC8;
  height: 48px;
}
.container-blnrcx .table-header span {
  font-family: Microsoft YaHei;
  font-style: normal;
  font-weight: normal;
  font-size: 20px;
  line-height: 40px;
  padding: 11px 12px;
  color: #333333;
}
/*表格内容外框*/
.content-belw {
  position: relative;
  width: 100%;
  background: #FAFAFA;
  border: 1px solid #3D6CC8;
  margin: 0px auto;
}
.table_frame {
  position: relative;
  padding-top: 12px;
  padding-left: 12px;
  margin: 0px;
  margin-right: 0;
}
/* 表格内容 */
.table_frame>table {
  position: relative;
  width: 100%;
  border: 3px solid #B7C3DA;
}
/* 行标题th部分 */
.row_headings {
  background: #85A2DB;
  color: #FFFFFF;
  height: 40px;
}
.row_headings >th{
  background: #85A2DB;
  color: #FFFFFF;
  text-align: center;
  border: 1px solid #FAFAFA;;
}
.row_headings>.keyword{
  position: relative;
  width: 11.4%;
}
.row_headings>.equalion{
  position: relative;
  width: 19.1%;
}
.row_headings>.queries{
  position: relative;
  width: 47.1%;
}
.row_headings>.remmark{
  position: relative;
  width: 22.4%;
}
/* 表格内容td部分 */
#threeColum{
  position: relative;
  height: 40px;
}
.column_tag>td {
  position: relative;
  height: 40px;
  border: 3px solid #B7C3DA;
}
.table_content .column_tag>td>select {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  /* border: 2px solid #B7C3DA; */
}
.table_content .column_tag>td>input {
  position: relative;
  width: 100%;
  height: 100%;
  /* height: 40px; */
  border-radius: 4px;
  border: 2px solid #B7C3DA;
}
.table_content .column_tag>td>textarea {
  position: relative;
  width: 100%;
  /* height: 100%; */
  height: 32px;
  border-radius: 4px;
  border: 2px solid #B7C3DA;
}

/* 入院等选择信息 */
.message-option {
  position: relative;
  padding: 0px 12px;
  margin-top: 20px;
}
.message-option>input {
  position: relative;
  border: 1px solid #3D6CC8;
  background: #FFFFFF;
  height: 22px;
  width: 8.8%;
}
#dt {
  position: relative;
  vertical-align:auto;
  margin: 0px 20px 0px 20px;
  border: 1px solid #3D6CC8;
  width: 10%;
}
.message-option>span {
  color: #3D6CC8;
}
#appear {
  position: relative;
  vertical-align:auto;
  margin: 0px 68px 0px 20px;
  border: 1px solid #3D6CC8;
  width: 10%;
}
.departmentList {
  position: relative;
  margin-left:20px;
  width: 98px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid #3D6CC8;
}
/* 查询按钮 */
#inquire-btn>button {
  position: relative;
  margin: 28px 0px 54px 12px;
  width: 90px;
  height: 32px;
  background: #3D6CC8;
  border: none;
  border-radius: 8px;
}
#inquire-btn>button>span {
  font-family: Microsoft YaHei;
  font-size: 18px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  letter-spacing: 0em;
  text-align: left;
  color: #fff;
}
/* 查询结果 */
.Object_list {
  position: relative;
  width: 100%;
}
.table_searchContent {
  position: relative;
  width: 99%;
  margin: auto;
  margin-bottom: 349px;
  border-collapse:collapse;
  vertical-align: middle;
  border: 1px solid #FFFFFF;
}

.search_table>th {
  position: relative;
  background: #85A2DB;
  color: #FFFFFF;
  text-align: center;
  height: 40px;
}
.search_content>td {
  text-align: center;
  background: #D3DEF3;
  color: #707070;
  height: 40px;
}
</style>
</html>
