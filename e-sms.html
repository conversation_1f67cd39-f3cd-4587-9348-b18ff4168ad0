<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>首页</title>
  <meta name="description" content="首页">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
.text{
  width: 470px;
  height: 30px;
  border: 1px solid #ddd
}
.bz_text{
  width: 440px;
  height: 30px;
  border: 1px solid #ddd
}
textarea{
  width: 470px;
}
#tb_text{
  margin-left: 95px;

}
span{
  width: 90px;
  display: inline-block;
  text-align: right;
  margin-right: 5px;
}
.head_inner{
  padding: 20px;
  width: 680px;
}
.smg_table{
  margin: auto;
  /* width: 50%; */
}
.table_head{
  background: #b0c4de;
  line-height: 25px;
  font-size: 15px;
}
#smg_data td,.table_head th{
  text-align: center;
}
#smg_data tr:hover{
  color: #4e4e4f;
  background: #d8e2f4;
  cursor:context-menu;
}
/* #smg_data tr:nth-child(even) {
    color: #4e4e4f;
    background: #d8e2f4;
} */
.smg_btn{
  margin-right: 5px;
  cursor: pointer;
}
.smg_text{
  width: 450px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  border: 1px solid gray;
  border-right:none;
  border-left:none;
  border-top:none;
}
html>#body_content>#if_Msg{
  z-index:1000 !important;
}
</style>
<body id="body_content">
  <div class="main">
    <div id="head_info">
      
    </div>
    
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 页面JS -->
  <!-- <script src='js/e-EhrSz.js'></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-sms.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>
</html>