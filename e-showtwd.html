<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>体温单</title>
  <meta name="description" content="体温单">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
  <style>
    .twd_data {
      width: 1400px;
      height: 2040px;
      padding: 30px 80px 60px;
      zoom: 0.5;
      color: #000;
    }

    .twd_data_info {
      zoom: 1.94;
    }

    .twd_data_table .e_table {
      width: 1248px;
      height: 1828px;
      border-collapse: collapse;
      border-spacing: 0;
      table-layout: fixed;
      font-size: 20px;
      line-height: 1;

    }

    .twd_data_table .e_table table {
      border-collapse: collapse;
      border-spacing: 0;
      table-layout: fixed;
      width: 100%;
    }

    .twd_data_table .e_table>tbody>tr {
      height: 46px;
      overflow: hidden;
    }

    .twd_data_table .e_table>tbody>tr>td {
      border: 2px solid #000;
      text-align: center;
      padding: 0;
      position: relative;
    }

    .twd_data_table .e_table>tbody>tr>td:first-child {
      font-size: 24px;
    }

    .twd_data_table .e_table dl.table_flex {
      display: flex;
      height: 100%;
      margin: 0;
      position: absolute;
      left: 0;
      top: 0;
      right: 0;
      left: 0;
    }

    .twd_data_table .e_table dl.table_flex.table_colnum {
      flex-flow: column;
    }

    .twd_data_table .e_table dl.table_flex>dd {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      border-left: 2px solid rgba(0, 0, 0, 0.3);
    }

    .twd_data_table .e_table dl.table_flex>dd.ssd {
      overflow: inherit;
    }

    .twd_data_table .e_table dl.table_flex.table_colnum>dd {
      border-top: 2px solid rgba(0, 0, 0, 0.3);
      border-left: none;
    }

    .twd_data_table .e_table dl.table_flex>dd:first-child,
    .twd_data_table .e_table dl.table_flex.table_colnum>dd:first-child {
      border: none;
    }

    .twd_data_table .e_table dl.table_flex.table_colnum>dd:nth-child(5n-2) {
      border-color: #000;
    }

    .twd_data_table .e_table dl.table_flex.table_colnum>dd:nth-child(28),
    .twd_data_table .e_table dl.table_flex.table_colnum>dd:nth-child(43) {
      border-color: #D81E06;
    }

    .twd_data_table .e_table .mb,
    .twd_data_table .e_table .ssd {
      display: flex;
      flex-flow: column;
      line-height: 1;
      justify-content: flex-start !important;
    }

    .twd_data_table .e_table .mb>span {
      margin-bottom: 81px;
    }

    .twd_data_table .e_table .mb>span:first-child {
      margin-top: 10px;
      margin-bottom: 186px;
    }

    .twd_data_table .e_table .mb>span:nth-last-child(2) {
      margin-bottom: 69px;
    }

    .twd_data_table .e_table .ssd>span {
      margin-bottom: 81px;
    }

    .twd_data_table .e_table .ssd>span:first-child {
      margin-top: 10px;
      margin-bottom: 81px;
    }

    .twd_data_table .e_table .ssd>span:nth-last-child(2) {
      margin-bottom: 190px;
    }

    .twd_data_page {
      text-align: center;
      font-size: 24px;
    }

    .twd_data_page>span {
      display: inline-block;
      width: 70px;
    }
    .lb_ts_tooltip {
      
      border: 1px solid #727272;
      color: #333333;
      background-color: #fff;
      font-size: 14px;
      /* height:20px; */
      padding: 5px 10px 5px 10px;
      border-radius: 6px;
      width: 150px;
      position: absolute;
      /* top: 20px; */
      z-index: 9999;
      /* display: none; */
    }
    .lb_ts_tooltiptext {
      width: 135px;
    }
    .lb_ts {
      font-size: 12px;
      height:20px;
      z-index: 9999;
    }
    
  </style>
</head>

<body>
  <div class="container-twd">
    <div id="twd_table" style="zoom: 1.0;"></div>
  </div>
  <!-- 配置文件 -->
  <script src="./e-config.js"></script>
  <!-- 公用模块JS -->
  <!-- <script src="js/e-common.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 登录页js文件 -->
  <script>
    var params = {} //url带参
    //统一页面启动
    $(document).ready(() => {
      for (let item of window.location.href.split('?')[1].split('&')) {
        params[item.split('=')[0]] = item.split('=')[1]
      }
      //获取体温单表格
      getTwd()
    })
    var tableData = [] //表格数据结构
    var listDate = [] //获取住院数组

    //体温单详情
    var twdData_View = WRT_e.view.extend({
      render: function () {
        //修改数据结构
        // title="上圧：${e_2&&e_2.SY||''}(mmHg)" 
        // title="下圧：${e_2&&e_2.XY||''}(mmHg)" 
        // <span style="font-size:12px;height:20px;">${e_2&&e_2.XY||''}</span><span style="height:48px;">${e_2&&e_2.XYBW||''}</span>
        changeTemperature(this.data.list)
        // tableData.HX=[[{"type":"up","val":34},{"type":"down","val":32},{"type":"up","val":20},{"type":"down","val":36},"",{"type":"up","val":32},null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,{"type":"down","val":30}],[{"type":"up","val":32},{"type":"down","val":26},{"type":"up","val":33},{"type":"down","val":22},{"type":"up","val":21},{"type":"down","val":22}],[{"type":"up","val":30},{"type":"down","val":29},{"type":"up","val":20},"",{"type":"down","val":27},{"type":"up","val":26}],[{"type":"down","val":25},{"type":"up","val":23},{"type":"up","val":31},"",{"type":"down","val":35},{"type":"up","val":28}],[{"type":"down","val":19},{"type":"up","val":34},"","","",""],["","","","","",""],["","","","","",""]]
        // tableData.SYXY=[[{"type":"up","SY":134},{"type":"up","XY":32},{"type":"up","SY":20},{"type":"up","XY":36},"",{"type":"up","SY":32},{"type":"up","XY":30}],[{"type":"up","SY":32},{"type":"up","XY":26},{"type":"up","SY":33},{"type":"up","XY":22},{"type":"up","SY":21},{"type":"up","XY":22}],[{"type":"up","SY":30},{"type":"up","XY":29},{"type":"up","SY":20},"",{"type":"up","XY":27},{"type":"up","SY":26}],[{"type":"up","XY":25},{"type":"up","SY":23},{"type":"up","SY":31},"",{"type":"up","XY":35},{"type":"up","SY":28}],[{"type":"up","XY":19},{"type":"up","SY":34},"","","",""],["","","","","",""],["","","","","",""]]
        // tableData.QT2=['1', '2', '3', 'vcx', 'dddss', '', '']
        // tableData.WOLI=['1', '2', '3', 'vcx', '', '33', 'gfds']
        // tableData.WL=['1n', '2h', 'o', 'vcx1', 'dddss1', '', '']
        var height1 = window.screen.height;
        var width1 = window.screen.width;
        this.$el.html(`
        <div class="twd_data">
          <div class="twd_data_info">
            ${this.data.title}
          </div>
          <div class="twd_data_table">
            <table class="e_table">
              <tbody>
                <tr>
                  <td>日期</td>
                  ${_.map(tableData.CLSJ_copy,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>住院天数</td>
                  ${_.map(tableData.XYTS,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>手术后日期</td>
                  ${_.map(tableData.SSTS,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>时间</td>
                  ${_.map(tableData.CLSJ,(e,i,arr)=>`<td ${(i+1)==arr.length?"":"style='border-right: 2px solid #D81E06'"}><dl class="table_flex"><dd>2</dd><dd>6</dd><dd>10</dd><dd>14</dd><dd>18</dd><dd>22</dd></dl></td>`).join('')}
                </tr>
                <tr style="height: 60%;">
                  <td>
                    <dl class="table_flex">
                      <dd class="mb"><span>脉搏<br/>(次/分)</span><span>200</span><span>180</span><span>160</span><span>140</span><span>120</span><span>100</span><span>80</span><span>60</span><span>40</span></dd>
                      <dd class="ssd"><span>摄氏度<br/>42</span><span>41</span><span>40</span><span>39</span><span>38</span><span>37</span><span>36</span><span>35</span><span class="flex-row justify-content-center" style="line-height: 0.95;position: absolute;bottom: 0px;margin:0px"><span>疼<br/>痛<br/>评<br/>分</span><span style="font-size:20px;">10<br/>8<br/>6<br/>4<br/>2<br/>0</span></span></dd>
                    </dl>
                  </td>
                  ${_.map(['','','','','','',''],(e,i,arr)=>`<td ${(i+1)==arr.length?"":"style='border-right: 2px solid #D81E06'"}><dl class="table_flex"><dd>${i==0?'<canvas id="cavsTable" style="position:absolute;left:-0.5px;top:-0.5px;z-index: 9;"></canvas>':''}</dd><dd></dd><dd></dd><dd></dd><dd></dd><dd></dd></dl><dl class="table_flex table_colnum">${_.map(['','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','','',''],e=>`<dd></dd>`).join('')}</dl></td>`).join('')}
                </tr>
                <tr style="${width1 == 1280 && height1==1024?``:`height: 3%;`}">
                  <td>呼吸(次/分)</td>
                  ${_.map(tableData.HX,(e,i,arr)=>`<td ${(i+1)==arr.length?"":"style='border-right: 2px solid #D81E06'"}>
                    <dl class="table_flex" style="color:#D81E06;">
                      ${_.map(e,(e_2)=>`
                        <dd class="${e_2&&e_2.type=='up'?'align-items-start':'align-items-end'}" style="font-size:15px;">
                          ${e_2?e_2.val||'':''}
                        </dd>
                      `).join('')}
                    </dl>
                  </td>`).join('')}
                </tr>
                <tr style="height: 6%;">
                  <td>血压(mmHg)</td>
                  ${_.map(tableData.SYXY,(e,i,arr)=>`<td ${(i+1)==arr.length?"":"style='border-right: 2px solid #D81E06'"}>
                    <dl class="table_flex">
                      ${_.map(e,(e_2,index)=>`
                        <dd class="flex-column" style="line-height: 1.2;">
                          <span onmouseout="sytool('sy',false,${e_2&&e_2.SY || ''},${i},${index})" onmouseover="sytool('sy',true,${e_2&&e_2.SY || ''},${i},${index})" style="font-size:12px;height:20px;">
                            ${e_2&&e_2.SY||''}
                          </span>
                          <span id="syShow`+i+'_'+index+`" ></span>
                          <span style="border-top: 2px solid rgba(0, 0, 0, 0.3);transform:skewY(-30deg);width:100%;margin: 6px 0;"></span>
                          <span onmouseout="sytool('xy',false,${e_2&&e_2.XY || ''},${i},${index})" onmouseover="sytool('xy',true,${e_2&&e_2.XY || ''},${i},${index})" style="font-size:12px;height:20px;">
                            ${e_2&&e_2.XY||''}
                          </span>
                          <span style="font-size: 14px;color: red;position: absolute;bottom:0;writing-mode: vertical-lr;text-orientation: upright;">${e_2.XYBW=='下左'||e_2.XYBW=='下右'?`下`:''}</span>
                          <span id="xyShow`+i+'_'+index+`" ></span>
                        </dd>
                      `).join('')}
                    </dl>
                  </td>`).join('')}
                </tr>
                <tr>
                  <td>体重(Kg)/BMI</td>
                  ${_.map(tableData.TZ,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>总入量</td>
                  ${_.map(tableData.RL,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td style="font-size:20px;">总出量/尿量(ml)</td>
                  ${_.map(tableData.ZCL,(e,i)=>`<td>${(tableData.ZCL[i]||tableData.ZCL[i]==0)||(tableData.NL[i]||tableData.NL[i]==0)?`${tableData.ZCL[i]} / ${tableData.NL[i]}`:''}</td>`).join('')}
                </tr>
                <tr>
                  <td style="font-size:20px;">其他排出量/引流量(ml)</td>
                  ${_.map(tableData.CL,(e,i)=>`<td>${(tableData.CL[i]||tableData.CL[i]==0)||(tableData.YLL[i]||tableData.YLL[i]==0)?`${tableData.CL[i]} / ${tableData.YLL[i]}`:''}</td>`).join('')}
                </tr>
                <tr>
                  <td>大便次数</td>
                  ${_.map(tableData.DBCS,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>身高/腹围</td>
                  ${_.map(tableData.SG,(e,i)=>`<td>${tableData.SG[i]||tableData.FW[i]?`${tableData.SG[i]} / ${tableData.FW[i]}`:''}</td>`).join('')}
                </tr>
                <tr>
                  <td style="font-size:20px;">经皮胆红汞值(mg/dl)</td>
                  ${_.map(tableData.QT1,e=>`<td>${e}</td>`).join('')}
                </tr>
                <tr>
                  <td>其他/握力</td>
                  ${_.map(tableData.QT2,(e,i)=>`
                    <td>
                      ${tableData.QT2[i] || (tableData.WL || tableData.WOLI)?`
                        ${tableData.WL && tableData.WL[i]!=undefined?`
                          ${tableData.QT2[i]} / ${tableData.WL[i]}`:`
                          ${tableData.WOLI && tableData.WOLI[i]!=undefined?`
                            ${tableData.QT2[i]} / ${tableData.WOLI?tableData.WOLI[i]:``}
                          `:`${tableData.QT2[i]}/`}`
                        }
                      `:`/`}
                    </td>
                  `).join('')}
                </tr>
              </tbody>
            </table>  
          </div>
          <div class="twd_data_page">第<span>${params['as_week']}</span>页</div>
        </div>
        `)
        return this
      },
      events: {},
    })
    
    // tooltip ${_.map(tableData.QT2,e=>`<td>${e}</td>`).join('')}
    function sytool(type,zt,val,index1,index2){
      let indexNow = index1 +'_' + index2
      if(type=='sy'){
        if(zt==true){
          // $('#syShow')[0].title = `上圧：${val}(mmHg)`
          let html = `
          <span id="lb_ts" class="lb_ts_tooltip" >
            <span class="lb_ts_tooltiptext">
              <span id="lb_ts">上压：${val}(mmHg)</span><br/>
            </span>
          </span>
          `
          $(`#syShow`+indexNow+``).html(html)
        } else {
          // style="display: none;"
          let html = `
          <span id="lb_ts" class="lb_ts_tooltip" style="display: none;">
            <span class="lb_ts_tooltiptext">
              <span id="lb_ts">上压：${val}(mmHg)</span><br/>
            </span>
          </span>
          `
          $(`#syShow`+indexNow+``).html(html)
        }
      } else if(type =='xy') {
        if(zt==true){
          let html = `
          <span id="lb_ts" class="lb_ts_tooltip" >
            <span class="lb_ts_tooltiptext">
              <span id="lb_ts">下压：${val}(mmHg)</span><br/>
            </span>
          </span>
          `
          $(`#xyShow`+indexNow+``).html(html)
        } else {
          //  style="display: none;" 
          let html = `
          <span id="lb_ts"class="lb_ts_tooltip" style="display: none;">
            <span class="lb_ts_tooltiptext">
              <span id="lb_ts">下压：${val}(mmHg)</span><br/>
            </span>
          </span>
          `
          $(`#xyShow`+indexNow+``).html(html)
        }
      }
    }
    //修改体温单数据的结构
    function changeTemperature(data) {
      tableData = {
        CLSJ_copy:[],//日期显示
        CLSJ: [], //日期
        XYTS: [], //天数
        SSTS: [], //手术天数
        TW: [], //体温
        XLMB: [], //脉搏&&心率
        TTCD: [], //疼痛评分
        HX: [], //呼吸
        SYXY: [], //血压
        TZ: [], //体重
        RL: [], //入量
        NL: [], //尿量
        ZCL: [], //总出量
        CL: [], //出量
        YLL: [], //引流量
        DBCS: [], //大便次数
        SG: [], //身高
        FW: [], //腹围
        QT1: [], //经皮胆红汞值
        QT2: [], //其他
        WL:[],
        WOLI:[]
      }

      //初始化列表
      let count = 0
      for (var i = 0; i < 7; i++) {
        //添加后7天日期
        let d = new Date(new Date(parseInt(data.kssj.slice(6, 19))).setDate(new Date(parseInt(data.kssj.slice(6, 19)))
          .getDate() + i))
        let d1 = new Date(new Date(parseInt(data.kssj.slice(6, 19))).setDate(new Date(parseInt(data.kssj.slice(6, 19)))
          .getDate() + i+1))
        
        let date1=new Date(parseInt(data.kssj.slice(6, 19)))
        let date2=`${d.getDate()<10?'0':''}${d.getDate()}`
        if (i == 0) { // 每页第1天应填写年、月、日
          date2=`${(new Date(parseInt(data.kssj.slice(6, 19)))).getFullYear()}-${(new Date(parseInt(data.kssj.slice(6, 19)))).getMonth()<9?'0':''}${(new Date(parseInt(data.kssj.slice(6, 19)))).getMonth()+ 1}-${(new Date(parseInt(data.kssj.slice(6, 19)))).getDate()<10?'0':''}${(new Date(parseInt(data.kssj.slice(6, 19)))).getDate()}`
        } else {
          if (date1.getFullYear() !== d.getFullYear() || date1.getMonth() !== d.getMonth()) {
            date2=`${d.getFullYear()}-${d.getMonth()<9?'0':''}${d.getMonth()+ 1}-${d.getDate()<10?'0':''}${d.getDate()}`
            count++
            if (count!=1) {
              date2=`${d.getDate()<10?'0':''}${d.getDate()}`
            }
          }
        }

        tableData.CLSJ.push(`${d.getFullYear()}-${d.getMonth()<9?'0':''}${d.getMonth()+ 1}-${d.getDate()<10?'0':''}${d.getDate()}`)
        tableData.CLSJ_copy.push(date2)
        //当前入院天数
        let zyts1=parseInt(data.kssj.slice(6, 19)) - parseInt(data.bqryrq.slice(6, 19))
        let zyts=parseInt(zyts1/ (24 * 60 * 60 *1000))
        if(zyts==0){
          zyts=zyts+i+1
        }else{
          zyts=zyts+i+2
        }

        tableData.XYTS.push(zyts)
        //手术天数
        let ssjl = ''
        // listDate.ssjl=[{"FYSH":"3","XYJS":"0","JS1ID":0,"JS2ID":0,"JS1XM":null,"JS2XM":null,"BDQP":"0","SSJL":"2","YZID":null,"XECSP":"0","XJSXXM":null,"TZDID":2022586,"BRBH":"00011412833668","ZYID":2906353,"BLID":2906353,"BQID":3187,"ZKID":3437,"CWH":"012","ZYH":"2183425","JSLX":"00","BRXM":"傅永作","BRXB":"1","CSRQ":"/Date(-315648000000)/","ZYDM":"01","HYZK":"2","LXDZ":"黄岩区澄江街道后林村四区４０号","LXDH":"15258088718","RYRQ":"/Date(1715164400000)/","LCZD":"右手部损伤","ZKXZ":"903927","XZTX":6,"SSYYSJ":null,"YJSSSC":120,"NSSSJ":"/Date(1716393600000)/","ZDYSID":2259,"ZDYSXM":"张怀保","D1ZSID":10635,"D1ZSXM":"金永龙","D2ZSID":14697,"D2ZSXM":"翁万青","D3ZSID":11846,"D3ZSXM":"余方正","D4ZSID":null,"D4ZSXM":null,"TSZDID":null,"TSZDXM":null,"CGZ":null,"MZHZ":"1","SFJZ":"0","KDYSID":5910,"KDSJ":"/Date(1716342826000)/","SPYSID":5910,"SPSJ":"/Date(1716343236000)/","SPYJ":"1","SSSDM":"04","SSJ":"C43","TX":5,"MZYS1ID":11870,"MZYS1XM":"谢俊杰","MZYS2ID":13620,"MZYS2XM":"傅海峰","MZYS3ID":null,"MZYS3XM":null,"XHHS1ID":6878,"XHHS1XM":"薛莉芝","XHHS2ID":null,"XHHS2XM":null,"QXHS1ID":null,"QXHS1XM":"倪子淇","QXHS2ID":null,"QXHS2XM":null,"GRCZ1ID":null,"GRCZ1XM":null,"SSLB":"8","GLJB":"3","QKLB":"2","ZTBZ":"5","SSKSSJ":"/Date(1716437753000)/","SJSSSC":null,"HSBZ":null,"HSCZZID":null,"HSXGSJ":null,"SFMZTJ":null,"SSSBZ":null,"SSSXGSJ":"/Date(1716444776000)/","SSSCZZID":12143,"KDBZ":"电动取皮刀，vsd","MZSS":"0","SSJSSJ":"/Date(1716441675000)/","YXJB":"F","ZDZKID":3437,"GLZL":null,"CRBYXZB":null,"RJSS":"0","TSSSTW":"6","BQMC":null,"SSSMC":null,"IN_SSJ":null,"OUT_SSJ":null,"OUT_SSS":null,"SHSCTXSJ":null},{"FYSH":"3","XYJS":"0","JS1ID":0,"JS2ID":0,"JS1XM":null,"JS2XM":null,"BDQP":"0","SSJL":"2","YZID":null,"XECSP":"0","XJSXXM":null,"TZDID":2014838,"BRBH":"00011412833668","ZYID":2906353,"BLID":2906353,"BQID":3187,"ZKID":3437,"CWH":"012","ZYH":"2183425","JSLX":"00","BRXM":"傅永作","BRXB":"1","CSRQ":"/Date(-315648000000)/","ZYDM":"01","HYZK":"2","LXDZ":"黄岩区澄江街道后林村四区４０号","LXDH":"15258088718","RYRQ":"/Date(1715164400000)/","LCZD":"右手部损伤","ZKXZ":"903927","XZTX":4,"SSYYSJ":null,"YJSSSC":120,"NSSSJ":"/Date(1715788800000)/","ZDYSID":2259,"ZDYSXM":"张怀保","D1ZSID":10635,"D1ZSXM":"金永龙","D2ZSID":14697,"D2ZSXM":"翁万青","D3ZSID":11846,"D3ZSXM":"余方正","D4ZSID":null,"D4ZSXM":null,"TSZDID":null,"TSZDXM":null,"CGZ":null,"MZHZ":"1","SFJZ":"0","KDYSID":5910,"KDSJ":"/Date(1715732194000)/","SPYSID":5910,"SPSJ":"/Date(1715732517000)/","SPYJ":"1","SSSDM":"04","SSJ":"C43","TX":4,"MZYS1ID":1670,"MZYS1XM":"郑浩2","MZYS2ID":-20909,"MZYS2XM":"颜巧琴","MZYS3ID":13620,"MZYS3XM":"傅海峰","XHHS1ID":6878,"XHHS1XM":"薛莉芝","XHHS2ID":null,"XHHS2XM":null,"QXHS1ID":-1007219,"QXHS1XM":"倪子淇","QXHS2ID":null,"QXHS2XM":null,"GRCZ1ID":null,"GRCZ1XM":null,"SSLB":"8","GLJB":"2","QKLB":"2","ZTBZ":"5","SSKSSJ":"/Date(1715833629000)/","SJSSSC":null,"HSBZ":null,"HSCZZID":null,"HSXGSJ":null,"SFMZTJ":null,"SSSBZ":null,"SSSXGSJ":"/Date(1715838906000)/","SSSCZZID":12143,"KDBZ":"vsd","MZSS":"0","SSJSSJ":"/Date(1715836200000)/","YXJB":"F","ZDZKID":3437,"GLZL":null,"CRBYXZB":null,"RJSS":"0","TSSSTW":"6","BQMC":null,"SSSMC":null,"IN_SSJ":null,"OUT_SSJ":null,"OUT_SSS":null,"SHSCTXSJ":null},{"FYSH":"3","XYJS":"0","JS1ID":0,"JS2ID":0,"JS1XM":null,"JS2XM":null,"BDQP":"0","SSJL":"2","YZID":null,"XECSP":"0","XJSXXM":null,"TZDID":2017809,"BRBH":"00011412833668","ZYID":2906353,"BLID":2906353,"BQID":3187,"ZKID":3437,"CWH":"012","ZYH":"2183425","JSLX":"00","BRXM":"傅永作","BRXB":"1","CSRQ":"/Date(-315648000000)/","ZYDM":"01","HYZK":"2","LXDZ":"黄岩区澄江街道后林村四区４０号","LXDH":"15258088718","RYRQ":"/Date(1715164400000)/","LCZD":"右手部损伤","ZKXZ":"903927","XZTX":4,"SSYYSJ":null,"YJSSSC":90,"NSSSJ":"/Date(1716134400000)/","ZDYSID":2259,"ZDYSXM":"张怀保","D1ZSID":11846,"D1ZSXM":"余方正","D2ZSID":14697,"D2ZSXM":"翁万青","D3ZSID":null,"D3ZSXM":"同学","D4ZSID":null,"D4ZSXM":null,"TSZDID":null,"TSZDXM":null,"CGZ":null,"MZHZ":"1","SFJZ":"0","KDYSID":5910,"KDSJ":"/Date(1715910235000)/","SPYSID":5910,"SPSJ":"/Date(1715910651000)/","SPYJ":"1","SSSDM":"04","SSJ":"C43","TX":4,"MZYS1ID":11864,"MZYS1XM":"李剑","MZYS2ID":13620,"MZYS2XM":"傅海峰","MZYS3ID":null,"MZYS3XM":null,"XHHS1ID":6878,"XHHS1XM":"薛莉芝","XHHS2ID":null,"XHHS2XM":null,"QXHS1ID":-1007224,"QXHS1XM":"吴静莉","QXHS2ID":null,"QXHS2XM":null,"GRCZ1ID":null,"GRCZ1XM":null,"SSLB":"8","GLJB":"1","QKLB":"2","ZTBZ":"5","SSKSSJ":"/Date(1716178672000)/","SJSSSC":null,"HSBZ":null,"HSCZZID":null,"HSXGSJ":null,"SFMZTJ":null,"SSSBZ":null,"SSSXGSJ":"/Date(1716180442000)/","SSSCZZID":12143,"KDBZ":null,"MZSS":"0","SSJSSJ":"/Date(1716179880000)/","YXJB":"F","ZDZKID":3437,"GLZL":null,"CRBYXZB":null,"RJSS":"0","TSSSTW":"6","BQMC":null,"SSSMC":null,"IN_SSJ":null,"OUT_SSJ":null,"OUT_SSS":null,"SHSCTXSJ":null},{"FYSH":"3","XYJS":"0","JS1ID":0,"JS2ID":0,"JS1XM":null,"JS2XM":null,"BDQP":"0","SSJL":"2","YZID":null,"XECSP":"0","XJSXXM":null,"TZDID":2008060,"BRBH":"00011412833668","ZYID":2906353,"BLID":2906353,"BQID":3159,"ZKID":3437,"CWH":"023","ZYH":"2183425","JSLX":"00","BRXM":"傅永作","BRXB":"1","CSRQ":"/Date(-315648000000)/","ZYDM":"01","HYZK":"2","LXDZ":"黄岩区澄江街道后林村四区４０号","LXDH":"15258088718","RYRQ":"/Date(1715164400000)/","LCZD":"右手部损伤","ZKXZ":"903927","XZTX":5,"SSYYSJ":null,"YJSSSC":120,"NSSSJ":"/Date(1715097600000)/","ZDYSID":10635,"ZDYSXM":"金永龙","D1ZSID":14697,"D1ZSXM":"翁万青","D2ZSID":11846,"D2ZSXM":"余方正","D3ZSID":-17218,"D3ZSXM":"胡骏浩","D4ZSID":null,"D4ZSXM":null,"TSZDID":2259,"TSZDXM":"张怀保","CGZ":null,"MZHZ":"1","SFJZ":"1","KDYSID":30872,"KDSJ":"/Date(1715166457000)/","SPYSID":5910,"SPSJ":"/Date(1715168482000)/","SPYJ":"1","SSSDM":"04","SSJ":"B21","TX":8,"MZYS1ID":13620,"MZYS1XM":"傅海峰","MZYS2ID":null,"MZYS2XM":null,"MZYS3ID":null,"MZYS3XM":null,"XHHS1ID":12870,"XHHS1XM":"谢培云","XHHS2ID":10523,"XHHS2XM":"何青青","QXHS1ID":15733,"QXHS1XM":"叶珈辰","QXHS2ID":11960,"QXHS2XM":"林文","GRCZ1ID":12870,"GRCZ1XM":"谢培云","SSLB":"9","GLJB":"1","QKLB":"2","ZTBZ":"5","SSKSSJ":"/Date(1715171568000)/","SJSSSC":null,"HSBZ":null,"HSCZZID":null,"HSXGSJ":null,"SFMZTJ":null,"SSSBZ":null,"SSSXGSJ":"/Date(1715184185000)/","SSSCZZID":24231,"KDBZ":"双羊克氏针，电钻，c臂，显微器械","MZSS":"0","SSJSSJ":"/Date(1715184183000)/","YXJB":"F","ZDZKID":3437,"GLZL":null,"CRBYXZB":null,"RJSS":"0","TSSSTW":"6","BQMC":null,"SSSMC":null,"IN_SSJ":null,"OUT_SSJ":null,"OUT_SSS":null,"SHSCTXSJ":null}]
        let ssjl_arr=listDate.ssjl.sort((a,b)=>{
          if(a.SSKSSJ==null){
            a.SSKSSJ=''
          }
          if(b.SSKSSJ==null){
            b.SSKSSJ=''
          }
          return parseInt(b.SSKSSJ.slice(6, 19))-parseInt(a.SSKSSJ.slice(6, 19))
        })
        for (let [index, item] of ssjl_arr.entries()) {
          let day1=parseInt(data.kssj.slice(6, 19))-parseInt((item.SSKSSJ==null?'':item.SSKSSJ.slice(6, 19)))
          let day = Math.floor(day1 / (24 * 60 * 60 * 1000)) + i + 1
          if (day > 0 & day <= 7) {
            ssjl += `/${day}`
          }
        }
        tableData.SSTS.push(ssjl.substr(1))
        //初始化tableData
        tableData.TW[i] = [] //体温
        tableData.XLMB[i] = [] //脉搏&&心率
        tableData.TTCD[i] = [] //脉搏&&心率
        tableData.HX[i] = ['', '', '', '', '', ''] //呼吸
        tableData.SYXY[i] = ['', '', '', '', '', ''] //血压
        tableData.TZ[i] = '' //体重
        tableData.RL[i] = '' //入量
        tableData.NL[i] = '' //尿量
        tableData.ZCL[i] = '' //总出量
        tableData.CL[i] = '' //出量
        tableData.YLL[i] = '' //引流量
        tableData.DBCS[i] = '' //大便次数
        tableData.SG[i] = '' //身高
        tableData.FW[i] = '' //腹围
        tableData.QT1[i] = '' //经皮胆红汞值
        tableData.QT2[i] = '' //其他
      }
      let arr=JSON.parse(data.smtzData)
      arr=arr.sort(function(a,b){
        let fd=new Date(a.CLSJ).getTime()-new Date(b.CLSJ).getTime()
        if(fd<0) return -1
        else return 1
      })
      let idx = 1
      for (let obj of arr) {
        obj.SJD < 99 ? idx++ : ''
        //判断这条是否同一天
        let index = tableData.CLSJ.indexOf(obj.CLSJ.split('T')[0])
        let time = obj.CLSJ.split('T')[1].split(':')
        let SJD = (Number(time[0]) + Number(time[1]) / 60 + Number(time[2]) / 3600)
        //体温
        obj.TW ? tableData.TW[index].push({
          SJD: SJD,
          TW: obj.TW,
          FCTW: obj.FCTW,
          TWCLBW: obj.TWCLBW
        }) : ''
        //脉搏
        obj.MB ? tableData.XLMB[index].push({
          SJD: SJD,
          VAL: obj.MB,
          type: 'heart'
        }) : ''
        //心率
        obj.XL ? tableData.XLMB[index].push({
          SJD: SJD,
          VAL: obj.XL,
          type: 'heart_o'
        }) : ''
        //疼痛程度
        obj.TTCD != null ? tableData.TTCD[index].push({
          SJD: SJD,
          VAL: obj.TTCD,
          type: 'circular_o_xs'
        }) : ''
        //呼吸(分上下)
        obj.SJD < 99 ? tableData.HX[index][((Number(obj.SJD < 98?obj.SJD:SJD) + 2) / 4) - 1] = {
          type: idx % 2 == 0 ? `up` : `down`,
          val: obj.HX
        } : ''
        //血压(分上下)
        obj.SJD < 99 ? tableData.SYXY[index][((Number(obj.SJD < 98?obj.SJD:SJD) + 2) / 4) - 1] = {
          SY: obj.SY,
          XY: obj.XY,
          XYBW: obj.XYBW == 00 ? '上左' : obj.XYBW == 01 ? '上右' : obj.XYBW == 10 ? '下左' : obj.XYBW == 11 ? '下右' : ''
        } : ''
        obj.TZ ? tableData.TZ[index] = (obj.TZ == -1 ? '平车' : obj.TZ == -2 ? '轮椅' : obj.TZ == -3 ? '卧床' : `${obj.TZ} / ${obj.SG?(obj.TZ/Math.pow(obj.SG/100,2)).toFixed(1):''}`) :
          '' //体重
        obj.RL||obj.RL==0 ? tableData.RL[index] = obj.RL : '' //入量
        obj.NL||obj.NL==0 ? tableData.NL[index] = obj.NL : '' //尿量
        obj.ZCL||obj.ZCL==0 ? tableData.ZCL[index] = obj.ZCL : '' //总出量
        obj.CL||obj.CL==0 ? tableData.CL[index] = obj.CL : '' //出量
        obj.YLL||obj.YLL==0 ? tableData.YLL[index] = obj.YLL : '' //引流量
        obj.DBCS ? tableData.DBCS[index] = obj.DBCS : '' //大便次数
        obj.SG ? tableData.SG[index] = obj.SG : '' //身高
        obj.FW ? tableData.FW[index] = obj.FW : '' //腹围
        obj.QT1 ? tableData.QT1[index] = obj.QT1 : '' //经皮胆红汞值
        obj.QT2 ? tableData.QT2[index] = obj.QT2 : '', //其他
        obj.WL ? tableData.WL[index] = obj.WL : '', //woli
        obj.WOLI ? tableData.WOLI[index] = obj.WOLI : ''
      }
    }
    //图表
    function cavsTable() {
      var canvas = document.querySelector('#cavsTable');
      var ctx = canvas.getContext('2d');
      let width = 1090; //表格宽
      let height = 1096; //表格高
      canvas.width = width;
      canvas.height = height + 20;
      ctx.lineWidth = 2
      // var height1 = window.screen.height;
      // var width1 = window.screen.width;
      var TW = [] //体温
      var FCTW = [] //复测体温
      _.each(tableData.TW, (e, i) => {
        _.each(e, (el, idx) => {
          if (el.FCTW) {
            FCTW.push({
              x: width * ((Number(el.SJD) + 24 * i) / 168),
              y: height * (42.4 - Number(el.FCTW)) / 10.4,
              oldY: height * (42.4 - Number(el.TW)) / 10.4, //原来的点位
            })
          }
          TW.push({
            lxtw: (e.length>1 && idx<e.length-1)?(idx>1 && e[idx-1].TW<=35)?0:e[idx].TW<=35?0:1:1, // 用于判断体温是否为35度以下
            twdot: el.TW<=35?0:1, // 用于判断体温是否为35度以下
            x: width * ((Number(el.SJD) + 24 * i) / 168),
            y: height * (42.4 - Number(el.TW)) / 10.4,
            type: el.TWCLBW == 1 ? 'circular' : el.TWCLBW == 2 ? 'circular_o' : el.TWCLBW == 3 ?
              'fork' : el.TWCLBW == 4 ? 'triangle_o' : el.TWCLBW == 5 ? 'square' : ''
          })
        })
      })
      drawPoints(ctx, TW)
      drawPointsFCTW(ctx, FCTW)
      var XLMB = [] //心率脉搏
      _.each(tableData.XLMB, (e, i) => {
        XLMB = [...XLMB, ..._.map(e, (el, idx) => ({
          x: width * ((Number(el.SJD) + 24 * i) / 168),
          y: height * (252 - Number(el.VAL)) / 212,
          type: el.type
        }))]
      })
      drawPoints(ctx, XLMB)
      var TTCD = [] //疼痛程度
      _.each(tableData.TTCD, (e, i) => {
        TTCD = [...TTCD, ..._.map(e, (el, idx) => ({
          x: width * ((Number(el.SJD) + 24 * i) / 168),
          y: (height - 1) * (104 - Number(el.VAL)) / 104,
          type: el.type
        }))]
      })
      drawPoints(ctx, TTCD)
      //显示文字
      let time = function (e) {
        e=e||''
        return new Date(parseInt(e.replace("/Date(", "").replace(")/", "")))
      }
      let lb_die = false;
      
      let textList = []
      //操作信息
      for (let item of JSON.parse(listDate.smtzData)) {
        let name = ''
        if (item.CZXX) {
          switch (item.CZXX) {
            case "1":
              textList.push({
                name: `${chineseDate(new Date(item.CZSJ))}分娩`,
                time: item.CZSJ.split(' ')[0],
                SJD: item.SJD
              })
              break;
            case "2":
              let same = textList.find(e => (e.SJD == item.SJD && e.time == item.CLSJ.split('T')[0]))
              if (same) {
                same.name = (same.name.replace(/\s*/g, "") + '╱机械通气')
              } else {
                textList.push({
                  name: '机 械 通 气',
                  time: item.CLSJ.split('T')[0],
                  SJD: item.SJD
                })
              }
              break;
            case "3":
              lb_die=true;
              let hour=item.SJD
              if(item.SJD==98){
                let time=item.CZSJ.split(' ')[1]
                hour=time.split(':')[0]
              }
              textList.push({
                name: `${chineseDate(new Date(item.CZSJ))}死亡`,
                time: item.CZSJ.split(' ')[0],
                SJD: hour
              })
              break;
          }
        }
      }
      // let textList = []
      //入院出院
      textList.push({
        name: '入院' + chineseDate(time(listDate.bqryrq)),
        time: time(listDate.bqryrq).Format("yyyy-MM-dd"),
        SJD: getSjd(time(listDate.bqryrq).Format("HH"))
      })
      if(!lb_die){
        textList.push({
          name: '出     院',
          time: time(listDate.bqcyrq).Format("yyyy-MM-dd"),
          SJD: getSjd(time(listDate.bqcyrq).Format("HH"))
        })
      }
      //转科
      for (let item of listDate.zkjl) {
        textList.push({
          name: '转科'+ chineseDate(time(item.ZCSJ)),
          time: time(item.ZCSJ).Format("yyyy-MM-dd"),
          SJD: getSjd(time(item.ZCSJ).Format("HH"))
        })
      }
      //手术
      for (let item of listDate.ssjl) {
        if (item.SPYJ !== 0 && [0, 9].indexOf(item.ZTBZ) == -1) {
          textList.push({
            name: (item.MZHZ == 0 && ['05', '06'].indexOf(item.SSSDM) > -1) ? '介     入' : '手     术',
            time: time(item.SSKSSJ).Format("yyyy-MM-dd"),
            SJD: getSjd(time(item.SSKSSJ).Format("HH"))
          })
        }
      }
      for (let obj of textList) {
        let i = tableData.CLSJ.indexOf(obj.time)
        if (i > -1) {
          ctx.font = "24px Georgia";
          ctx.fillStyle = "#ff0000";
          for (let [index, item] of obj.name.split('').entries()) {
            ctx.fillText(item, width * ((Number(obj.SJD) + 24 * i) / 168) - 12, 65 + index * 24)
          }
        }
      }
    }
    //点坐标
    function drawPoints(ctx, data) {
      // 设置坐标点的大小  dotSize
      var dotSize = 14;
      //上一次循环的点
      var oldPoint = {
        x: 0,
        y: 0
      };
      ctx.setLineDash([]);
      //排序
      // 遍历点的坐标,以及绘画点
      data.sort(compare('x')).forEach(function (item, i) {
        // 设置坐标点的中心圆点位置（x0，y0）
        var x = item.x;
        var y = item.y;
        // 绘画坐标点
        switch (item.type) {
          case 'circular_o': //空心圆
            ctx.beginPath();
            ctx.arc(x, y, dotSize / 2, 0, 2 * Math.PI);
            ctx.strokeStyle = '#4E6EF2'
            ctx.stroke();
            break;
          case 'circular_o_xs': //小空心圆红
            ctx.beginPath();
            ctx.arc(x, y, dotSize / 3.5, 0, 2 * Math.PI);
            ctx.strokeStyle = '#D81E06'
            ctx.stroke();
            break;
          case 'circular': //实心圆
            ctx.beginPath();
            ctx.arc(x, y, dotSize / 2, 0, 2 * Math.PI);
            ctx.fillStyle = '#4E6EF2'
            ctx.fill();
            break;
          case 'fork': //叉叉
            ctx.beginPath();
            ctx.moveTo(x - dotSize / 2, y - dotSize / 2);
            ctx.lineTo(x + dotSize / 2, y + dotSize / 2);
            ctx.moveTo(x - dotSize / 2, y + dotSize / 2);
            ctx.lineTo(x + dotSize / 2, y - dotSize / 2);
            ctx.strokeStyle = '#4E6EF2'
            ctx.stroke();
            break;
          case 'triangle_o': //空心三角形
            if (item.twdot==1) {
              ctx.beginPath();
              ctx.moveTo(x, y - dotSize * 0.6);
              ctx.lineTo(x + dotSize / 2, y + dotSize * 0.4);
              ctx.lineTo(x - dotSize / 2, y + dotSize * 0.4);
              ctx.closePath(); //闭合路径
              ctx.strokeStyle = "#4E6EF2";
              ctx.stroke();
            } else {
              let wz = '不升'
              for (let [index, itemName] of wz.split('').entries()) {
                ctx.font = "24px Georgia";
                ctx.fillStyle = "black";
                ctx.fillText(itemName, x-12, y + index * 24)
              }
            }
            break;
          case 'square': //实心正方形
            ctx.fillStyle = '#4E6EF2'
            ctx.fillRect(x - dotSize / 2, y - dotSize / 2, dotSize, dotSize);
            break;
          case 'heart_o': //空心爱心
            var img = new Image();
            img.src = './images/heart_o.png'
            img.onload = function () {
              ctx.drawImage(img, x - dotSize / 2, y - dotSize / 2, dotSize * 1.2, dotSize * 1.2);
            }
            break;
          case 'heart': //实心爱心
            var img = new Image();
            img.src = './images/heart.png'
            img.onload = function () {
              ctx.drawImage(img, x - dotSize / 2, y - dotSize / 2, dotSize * 1.2, dotSize * 1.2);
            }
            break;
        }
        //绘制线
        if (i > 0) {
          if (item.twdot== undefined || item.lxtw==1) {
            ctx.beginPath();
            ctx.moveTo(oldPoint.x, oldPoint.y);
            ctx.lineTo(x, y);
            ctx.strokeStyle = (["heart_o", "heart"].indexOf(item.type) > -1 ? "#D81E06" : "#4E6EF2")
            ctx.stroke();
          }
        }
        oldPoint = {
          x,
          y
        }
      });
    }

    function drawPointsFCTW(ctx, data) {
      // 设置坐标点的大小  dotSize
      var dotSize = 14;
      // 遍历点的坐标,以及绘画点
      data.forEach(function (item, i) {
        ctx.setLineDash([]);
        // 设置坐标点的中心圆点位置（x0，y0）
        var x = item.x;
        var y = item.y;
        //空心圆红
        ctx.beginPath();
        ctx.arc(x, y, dotSize / 2, 0, 2 * Math.PI);
        ctx.strokeStyle = '#D81E06'
        ctx.stroke();
        //绘制虚线
        ctx.beginPath();
        ctx.setLineDash([4, 2]);
        ctx.moveTo(item.x, item.oldY);
        ctx.lineTo(item.x, item.y);
        ctx.strokeStyle = "#D81E06"
        ctx.stroke();
      });
    }
    //获取体温单表格
    function getTwd() {
      let obj = {
        as_blid: params['as_blid'],
        as_jssj: params['as_jssj'],
        as_kssj: params['as_kssj'],
        as_week: params['as_week']
      }
      //获取体温单标题
      WRT_e.api.brtwdlb.getTwdTitle({
        params: obj,
        success(data) {
          if (data.Code == 1) {
            //获取体温单数据
            WRT_e.api.brtwdlb.getTwdData({
              params: obj,
              success(data_2) {
                if (data_2.Code == 1) {
                  // console.log('获取体温单标题',data,'获取体温单数据',data_2);
                  //获取分娩
                  // WRT_e.api.brtwdlb.getMotherFmrq({
                  //   params: {
                  //     as_zyh: data_2.Result.zyh
                  //   },
                  //   success(data_3) {
                  listDate = data_2.Result
                  // if (data_3.Code == 1) listDate.fmrq = data_3.Result
                  //获取体温单数据
                  $("#twd_table").html(
                    new twdData_View().init({
                      data: {
                        list: listDate,
                        title: data.Result,
                      }
                    }).render().$el
                  )
                  //画图
                  cavsTable()
                  // }
                  // })
                }
              }
            })
          }
        }
      })
    }
    //打印
    function preview() {
      window.print();
    }
    // 放大缩小
    function ZoomIt(flag) {
      var zoom = Number($('#twd_table').css('zoom'))
      if (zoom > 0) $('#twd_table').css('zoom', eval('zoom' + flag + '=0.5;'));
    }
    //中文数字
    function chineseDate(date) {
      var hours = date.getHours()
      var minutes = date.getMinutes()
      // 组装小时
      hours = formatNumber(hours) + '时'
      // // 组装分钟
      if (minutes < 10) {
        minutes = '零' + formatNumber(minutes) + '分'
      } else {
        minutes = formatNumber(minutes) + '分'
      }
      return `${hours}${minutes}`
    }

    function formatNumber(n) {
      var arr = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
      if (n <= 10) {
        // 几
        return arr[n]
      } else if (n < 20) {
        // 十几
        return '十' + arr[n - 10]
      } else if (n >= 20 && n % 10 === 0) {
        // 几十
        return arr[n / 10] + '十'
      } else {
        // 几十几
        var a = parseInt(n / 10)
        var b = n % 10

        return arr[a] + '十' + arr[b]
      }
    }
    //时间点
    function getSjd(hour) {
      let Numhour = parseInt(hour)
      if (Numhour < 4) {
        return 2
      } else if (Numhour < 8) {
        return 6
      } else if (Numhour < 12) {
        return 10
      } else if (Numhour < 16) {
        return 14
      } else if (Numhour < 20) {
        return 18
      } else if (Numhour <= 24) {
        return 22
      }
    }
    //对象数组按属性排序
    function compare(key) {
      return function (value1, value2) {
        var val1 = value1[key];
        var val2 = value2[key];
        return val1 - val2;
      }
    }
  </script>
</body>

</html>