
var activeBtnID = 1
var banhxx= {}
//统一页面启动
$(document).ready(() => {
  //初始化
  // let url = window.location.href.split("?") || []
  // let text = url[1].split("&")
  // let params = {}
  // for (let i of text) {
  //   let fd = i.split("=")
  //   params[fd[0]] = fd[1]
  // }
  // WRT_config.url = params
// sessionStorage.setItem("token",'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJ3ZnciLCJ5b25nSHVYTSI6Ijk5IiwieWlMaWFvSkdETSI6IjAxIiwiaXNzIjoi546L5Y2O5pmTIiwieWluZ1lvbmdETSI6IjAyMCIsImV4cCI6MTcxNjg2MTk4MywidXNlcklkIjoxMzEzMSwidXVpZCI6IiJ9.ArYszzSMzcwDRWul-2PjHc2xYLLzoeKLMNottU9NHkk')
console.log('token',sessionStorage.getItem("token"));  
app.init()
})

var app = {
  init: function () {
    activeBtnID = 1
    // 查询条件
    $(".LInputForm").html(
      new kyzbcx_search().init().render().$el
    )
    // 查询结果数据展示
    $(".sTable").html(
      new kyzbcx_table().init({
        data: []
      }).render().$el
    )
  }
}

/********************公用方法********************/

// 打开手术通知单
function openSSTZDWin(item) {
  console.log('打开手术通知单弹窗数据',item);
  let model = $("#checkSSTZDModal").iziModal({ //初始化modal
    overlayClose: false, //点击遮罩关闭框
    width: 1332,
    title: item.xingMing +'的手术通知单',
  })
  model.iziModal('setContent',`
    <div class="ssoverall_table_frame">
      <table id="sstable_internal" class="table_internal" style="align-items: center;text-align: center;">
        <thead>
          <tr class="ssrow_head">
            <th>手术日期</th>
            <th width="90px">手术间名称</th>
            <th width="70px">病案号</th>
            <th width="80px">病人姓名</th>
            <th width="50px">年龄</th>
            <th width="50px">性别</th>
            <th >病区</th>
            <th width="60px">床位号</th>
            <th>拟施手术</th>
            <th width="70px">主刀医生</th>
            <th>预计手术等待时间</th>
            <th>预计时长(分钟)</th>
            <th>科研标识</th>
            <th width="50px">操作</th>
          </tr>
        </thead>
        <tbody class="ssline_nr" id="line_nr">
            <tr>
              <td colspan="16">
                <img class="load_img" src="./images/load.gif" style="width: 160px;" />
              </td>
            </tr>
        </tbody>
      </table>
    </div>
  `)
  //打开
  model.iziModal('open')
  // 1、根据病历ID获取所有手术
  WRT_e.api.kyzbcx.getShouShuTZDByBLID({
    params: item.bingLiID,
    success(data) {
      console.log('打开手术通知单',data,data.data);
      if (data.Code = '1') {
        WRT_config.sstzdTable = data.data
        $('.ssoverall_table_frame').html(
          new ssdtzWin_table().init({
            data: WRT_config.sstzdTable
          }).render().$el
        )
      }
    }
  })
}

// 修改手术通知单科研标识
function changeSSTZDKYBS(item) {
  let selectNow = item.qiTaSXs.filter( e => e.shuXingDM == 'KY')[0]!=undefined?item.qiTaSXs.filter( e => e.shuXingDM == 'KY')[0].shuXingZhi:'-1'
  // console.log(item, item.qiTaSXs, selectNow);
  
  let temp=`
  <div class="kybsWin">
    <label>科研标识：</label>
    <div class="control-inner flex-select control_kybs">
      <select id="tb_bz" name="kybs">
        <option value ="-1" ${selectNow==-1?`selected`:''}></option>
        <option value ="1" ${selectNow==1?`selected`:''}>是</option>
        <option value ="0" ${selectNow==0?`selected`:''}>否</option>
      </select>
    </div>
  </div>
  <div  class="kybsWinBtn">
    <button class="e_btn" onclick='setKybs(${JSON.stringify(item)})' style="margin-right: 5px;">确定</button>
    <button class="e_btn" onclick="$('#changeSSTZDKYBS').iziModal('destroy')">取消</button>
  </div>`
  WRT_e.ui.model({
    id: "changeSSTZDKYBS",
    title: "修改手术通知单科研标识",
    width: "450px",
    content: temp,
    iframe: false,
  })
}
//  修改科研标识
function setKybs(item){
  var val = $('.control_kybs select[name="kybs"]').val()
  // console.log(val,item,item.tongZhiDID);
 
  if (val == -1) {
    WRT_e.ui.hint({msg: '请选择科研标识',type:"info"})
  } else {
    WRT_e.api.kyzbcx.updateKeYanBS({
      params: {
        skeYanBS: val,
        tongZhiDanID: item.tongZhiDID
      },
      success(data) {
        // console.log('修改科研标识',data);
        if (data.Code = '1') {
          $('#changeSSTZDKYBS').iziModal('destroy')
          WRT_e.ui.hint({msg: '科研标识修改成功',type:"success"})
          // 重新绘制表格
          $(".ssoverall_table_frame").html(`
            <table id="sstable_internal" class="table_internal" style="align-items: center;text-align: center;">
              <thead>
                <tr class="ssrow_head">
                  <th>手术日期</th>
                  <th width="90px">手术间名称</th>
                  <th width="70px">病案号</th>
                  <th width="80px">病人姓名</th>
                  <th width="50px">年龄</th>
                  <th width="50px">性别</th>
                  <th >病区</th>
                  <th width="60px">床位号</th>
                  <th>拟施手术</th>
                  <th width="70px">主刀医生</th>
                  <th>预计手术等待时间</th>
                  <th>预计时长(分钟)</th>
                  <th>科研标识</th>
                  <th width="50px">操作</th> 
                </tr>
              </thead>
              <tbody class="ssline_nr" id="line_nr">
                  <tr>
                    <td colspan="16">
                      <img class="load_img" src="./images/load.gif" style="width: 160px;" />
                    </td>
                  </tr>
              </tbody>
            </table>
          `)
          WRT_e.api.kyzbcx.getShouShuTZDByBLID({
            params: item.bingLiID,
            success(data) {
              if (data.Code = '1') {
                WRT_config.sstzdTable = data.data
                // ssdtzWin_table
                $('.ssoverall_table_frame').html(
                  new ssdtzWin_table().init({
                    data: WRT_config.sstzdTable
                  }).render().$el
                )
              }
            }
          })
        } else {
          WRT_e.ui.hint({msg: data.errorMessage,type:"error"})
        }
      }
    })
  }
}

/********************视图********************/
// 查询条件
var kyzbcx_search = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
      <div class='qCondition'>
        <div class='inputSelect'>
          <div class='initCondition'>
            <label class="control-label label-name" style="font-size: 15px;min-width:50px;padding: 0 10px;"> 查询病案号 </label>
            <div class="control-inner flex-select control_bah">
              <input class="form-control" type='text' name='bah'/>
            </div>
          </div>
        </div>
        <div class='allBtn'>
          <button class='e_btn_primary searchT'>
            <i class="glyphicon glyphicon-search"></i>
            查询
          </button>
        </div>
      </div>
    `)
    return this
  },
  events: {
    // 查询
    "click .searchT":function () {
      let bahVal =  $('.control_bah input[name="bah"]').val()
    //   WRT_config.kyzbcxTable = [
    //     {
    //         "zhuYuanHao": "1539913",
    //         "xingMing": "测试病人",
    //         "xingBie": "1",
    //         "chuShengRQ": "1990-09-22 00:00:00",
    //         "bingQuDM": "CS",
    //         "zhuanKeDM": "D3",
    //         "chuangWeiHao": "001",
    //         "ruYuanRQ": "2018-11-26 09:07:40",
    //         "chuYuanRQ": null,
    //         "geShiDM": null,
    //         "zhuangTaiBZ": "1",
    //         "caoZuoZhe": "汤伟项",
    //         "xiuGaiSJ": "2023-07-05 16:05:14",
    //         "ruYuanZD": "高钾血症",
    //         "menZhenHao": "00011411365561",
    //         "bingLiID": 1493631,
    //         "caoZuoZheID": 12319,
    //         "bingQuRYRQ": "2018-11-26 10:33:06",
    //         "bingQuCYRQ": null,
    //         "ruYuanJLRQ": "2018-11-26 15:45:42",
    //         "shouCiJLRQ": "2018-11-26 16:28:13",
    //         "beiZhu": null,
    //         "liYuanZT": null,
    //         "zhuanGuiDW": null,
    //         "zhuanGuiLY": null,
    //         "guiDangBZ": "1",
    //         "zhuanKeID": 3614,
    //         "riJianSS": "0",
    //         "riJianSSXGRY": 12319,
    //         "riJianSSXGSJ": "2023-04-21 14:57:41",
    //         "siWangSJ": null,
    //         "ruYuanZKID": 3614,
    //         "ruYuanBQID": 3615,
    //         "ruYuanCWH": "001",
    //         "bingQuID": 3615,
    //         "xinBanBS": "1",
    //         "fuZhuJCZT": null,
    //         "fuZhuJCSJ": null,
    //         "empi": "0003498036",
    //         "keShiMC": "测试专科",
    //         "fuYinZT": null,
    //         "guiDangYS": null,
    //         "yiShiGDRQ": null,
    //         "guiDangHS": null,
    //         "huShiGDRQ": null,
    //         "jieSuanLXMC": null,
    //         "ziDingYM": null,
    //         "idb": 1493631
    //     }
    // ] 
    //   activeBtnID = 1
    //   $('.sTable').html(
    //     new kyzbcx_table().init({
    //       data: WRT_config.kyzbcxTable
    //     }).render().$el
    //   )

      if (bahVal && bahVal.replace(/\s+/g, '')!='' ) {
        activeBtnID = 2
        // 加载数据
        $(".sTable").html(`
        <div class="overall_table_frame">
          <table id="table_internal" class="table_internal">
            <thead>
              <tr class="row_head">
                <th>病案号</th>
                <th>病人名称</th>
                <th>专科</th>
                <th>病区</th>
                <th>床位号</th>
                <th>入院时间</th>
                <th>出院时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody class="line_nr" id="line_nr" style="text-align: center;">
              <tr>
                <td colspan="8">
                <img class="load_img" src="./images/load.gif" style="width: 160px;" />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      `)
        WRT_e.api.kyzbcx.getEzyblbrByEmpi({
          params: { "empi": bahVal },
          success(data) {
            // console.log(data,data.data);
            if (data.Code = '1') {
              WRT_config.kyzbcxTable = data.data
              activeBtnID = 1
              $('.sTable').html(
                new kyzbcx_table().init({
                  data: WRT_config.kyzbcxTable
                }).render().$el
              )
            }
          },
          error: function (err) {
            activeBtnID = 1
            $('.sTable').html(
              new kyzbcx_table().init({
                data: []
              }).render().$el
            )
          }
        })
      } else {
        WRT_e.ui.hint({msg: '请输入需要查询的病案号',type:"info"})
      }
    }
  }
})

// 展示数据表格
var kyzbcx_table = WRT_e.view.extend({
  render: function () {
    if (this.data.length==0) {
      this.$el.html(`
      ${activeBtnID==1?`
        <div class="overall_table_frame">
          <table id="table_internal" class="table_internal">
            <thead>
              <tr class="row_head">
                <th>病案号</th>
                <th>病人名称</th>
                <th>专科</th>
                <th>病区</th>
                <th>床位号</th>
                <th>入院时间</th>
                <th>出院时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody class="line_nr" id="line_nr">
              <tr>
                <td colspan="8">
                  <div style='position:relative;font-size: 22px;padding: 10px 5px 10px;width: 80%;margin: 0 auto;text-align: center;'> 暂无数据 </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>`:`
        <div class="overall_table_frame">
          <table id="table_internal" class="table_internal">
            <thead>
              <tr class="row_head">
                <th>病案号</th>
                <th>病人名称</th>
                <th>专科</th>
                <th>病区</th>
                <th>床位号</th>
                <th>入院时间</th>
                <th>出院时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody class="line_nr" id="line_nr">
              <tr>
                <td colspan="8">
                  <div style='position:relative;font-size: 22px;padding: 10px 5px 10px;width: 80%;margin: 0 auto;text-align: center;'> 未查询到相关病人数据 </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      `}
      `)
    } else {
      this.$el.html(`
        <div class="overall_table_frame">
          <table id="table_internal" class="table_internal">
            <thead>
              <tr class="row_head">
                <th>病案号</th>
                <th>病人名称</th>
                <th>专科</th>
                <th>病区</th>
                <th>床位号</th>
                <th>入院时间</th>
                <th>出院时间</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody class="line_nr" id="line_nr">
            ${_.map(this.data,(item,index)=>`<tr class="row_nr">
              <td calss="bah${index}">${item.empi}</td>
              <td calss="xm${index}">${item.xingMing}</td>
              <td calss="zk${index}">${item.keShiMC}</td>
              <td calss="bq${index}">${item.bingQuID}</td>
              <td calss="cwh${index}">${item.chuangWeiHao}</td>
              <td calss="rysj${index}">${item.ruYuanRQ}</td>
              <td calss="cysj${index}">${item.chuYuanRQ && item.chuYuanRQ!=null?item.chuYuanRQ:''}</td>
              <td>
              <a herf="javascript:void(0)" calss='checksstzd' onclick='openSSTZDWin(${JSON.stringify(item)})' style='position: relative;padding-right: 36px;cursor: pointer;'>查看手术通知单</a>
              </td>
            </tr>`).join('')}
            </tbody>
          </table>
        </div>
      `)
    }
    return this
  }
})
// 展示手术单弹窗列表
var ssdtzWin_table = WRT_e.view.extend({
  render: function () {
    if (this.data.length==0) {
      this.$el.html(`
        <div class="ssoverall_table_frame">
          <table id="sstable_internal" class="table_internal" style="align-items: center;text-align: center;">
            <thead>
              <tr class="ssrow_head">
                <th>手术日期</th>
                <th width="90px">手术间名称</th>
                <th width="70px">病案号</th>
                <th width="80px">病人姓名</th>
                <th width="50px">年龄</th>
                <th width="50px">性别</th>
                <th >病区</th>
                <th width="60px">床位号</th>
                <th>拟施手术</th>
                <th width="70px">主刀医生</th>
                <th>预计手术等待时间</th>
                <th>预计时长(分钟)</th>
                <th>科研标识</th>
                <th width="50px">操作</th>
              </tr>
            </thead>
            <tbody class="ssline_nr" id="line_nr">
                <tr>
                  <td colspan="16">
                    <div>当前病例暂无手术信息数据</div>
                  </td>
                </tr>
            </tbody>
          </table>
        </div>
      `)
    } else {
      // 手术间，病案号，姓名，年龄，性别，病区，床位，拟施手术，主刀，预计手术等待时间，预计时长
      this.$el.html(`
        <table id="sstable_internal" class="table_internal" style="align-items: center;text-align: center;">
          <thead>
            <tr class="ssrow_head">
              <th>手术日期</th>
              <th width="90px">手术间名称</th>
              <th width="70px">病案号</th>
              <th width="80px">病人姓名</th>
              <th width="50px">年龄</th>
              <th width="50px">性别</th>
              <th >病区</th>
              <th width="60px">床位号</th>
              <th>拟施手术</th>
              <th width="70px">主刀医生</th>
              <th>预计手术等待时间</th>
              <th>预计时长(分钟)</th>
              <th>科研标识</th>
              <th width="50px">操作</th>
            </tr>
          </thead>
          <tbody class="ssline_nr" id="line_nr">
          ${this.data.length==0?`
            <tr>
              <td colspan="8">
                <div style='position:relative;font-size: 22px;padding: 10px 5px 10px;width: 80%;margin: 0 auto;text-align: center;'> 暂无数据 </div>
              </td>
            </tr>`:`
            ${_.map(this.data,(item,index)=>`<tr class="ssrow_nr">
              <td calss="nssrq${index}">${item.niShouShuSJ!=null?item.niShouShuSJ:''}</td>
              <td calss="tzdID${index}">${item.shouShuJianMC!=null?item.shouShuJianMC:''}</td>
              <td calss="bah${index}">${item.bingAnHao}</td>
              <td calss="brxm${index}">${item.bingRenXM}</td>
              <td calss="nl${index}">${item.nianLing} 岁</td>
              <td calss="brxb${index}">${item.bingRenXB}</td>
              <td calss="bqmc${index}">${item.bingQuMC}</td>
              <td calss="cwh${index}">${item.chuangWeiHao}</td>
              <td calss="nsss${index}">${_.map((item.shouShuXM),(e,i)=>`${
                e.shouShuBW!=''&&e.shouShuBW!=null ?e.shouShuMC+'('+e.shouShuBW+')': e.shouShuMC
              }`).join(',')}</td>
              <td calss="zdysxm${index}">${item.zhuDaoYSXM}</td>
              <td calss="yjssddsj${index}">${item.yuJiSSDDSJ}</td>
              <td calss="yjsc${index}">${item.yuJiSSSC}</td>
              <td calss="kybs${index}">${JSON.stringify((item.qiTaSXs).filter(e=>e.shuXingDM =='KY')[0]!=undefined?(item.qiTaSXs).filter(e=>e.shuXingDM =='KY')[0].shuXingZhi=='1'?`是`:`否`:``).replace(/\"/g, '')}</td>
              <td ><a herf="javascript:void(0)" calss='changedkybs' onclick='changeSSTZDKYBS(${JSON.stringify(item)})' >修改</a></td>
            </tr>`).join('')}
          `}
          </tbody>
        </table>
    `)
    }
    return this
  }
})
