var td_index = 0 //当前步骤
var yz_Model = null//当前医嘱
var yz_index = null//当前序号
var yz_key = null//当前序号
var mbindex = 0 //
var dldr_lists = [] //导入队列
var yznr_lists = []//
var YpmbDetail_copy = [] //医嘱内容
var times = null//最晚项目时间
var check_key = 0//右侧勾选序号
//统一页面启动
$(document).ready(() => {
	let url = window.location.href.split("?") || []
	let params = {}
	if (url[1]) {
		let text = url[1].split("&")
		for (let i of text) {
			let fd = i.split("=")
			params[fd[0]] = fd[1]
		}
	}


	WRT_config.url = params || {}
	//获取获取病人信息
	WRT_e.api.lclj.initData({
		params: {
			al_blid: WRT_config.url.al_blid
		},
		success(data) {
			if (data) {
				WRT_config.lclj_init = JSON.parse(data)
				if (WRT_config.lclj_init.TSXX) {
					let per = WRT_config.lclj_init.TSXX.split("  ")
					WRT_config.lclj_init.per = per
				}
				console.log(WRT_config.lclj_init)
				// app.init()
				//用法
				WRT_e.api.yz_sz.getYpyfpl({
					params: { as_lb: 6 },
					success(data) {
						WRT_config.yf = JSON.parse(data.Result)
						if (WRT_config.yf && WRT_config.pl) {
							// app.init()
						}

					}
				})
				//频率
				WRT_e.api.yz_sz.getYpyfpl({
					params: { as_lb: 2 },
					success(data) {
						WRT_config.pl = JSON.parse(data.Result)
						if (WRT_config.yf && WRT_config.pl) {
							// app.init()
						}
						// temp=ypyfplHtml(WRT_config.pl,"pl","pllist")

					}
				})
				WRT_e.api.lclj.GetPaYwgms({
					params: {
						as_brbh: WRT_config.lclj_init.BRBH,
						as_yfdm: WRT_config.lclj_init.YFDM
					},
					success(data) {
						if (data) {
							WRT_config.PaYwgms = JSON.parse(data)
						}
					}
				})
				WRT_e.api.lclj.GetPaLcljData({
					params: {
						al_ljid: WRT_config.lclj_init.LJID,
						al_bzid: WRT_config.lclj_init.DQBZID
					},
					success(data) {
						if (data) {
							WRT_config.paLcljData = JSON.parse(data)
							console.log(WRT_config.paLcljData)
							//左侧
							var Menuleft = new Menuleft_View();
							Menuleft.$el = $("#left_content");
							Menuleft.init({ data: WRT_config.paLcljData }).render();
							left_init(WRT_config.paLcljData[0])

						}
					}
				})

			}
		}
	})
	//子页面监听
	window.addEventListener('message', function (e) {
		if (e.data) {
			switch (e.data.page) {
				case 'kjypgl'://选择抗菌药物使用方法
					SetKjypsyly(e.data)
					break;
				case 'ypsp'://药品审批
					SetSpjg(e.data)
					break;
				case 'tskjywhzd'://抗菌药物限制
					setTskjywhzdid(e.data)
					break;
			}
		}
	}, false);
})

/********** 方法回调 **********/

//药品审批
function SetSpjg(data) {
	let index = mbindex - 1
	if (index < 0) {
		index = 0
	}
	let yemp = 'ypsp'
	if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
		yemp = 'if_ypsp'
	}
	if (data) {
		if (data.sfzf == '9') {
			$(`#${yemp}`).iziModal('destroy')
			removeYp(dldr_lists[index].ZH, dldr_lists[index].MBMC)
			return
		}
		let type = setFylx(WRT_config.lclj_init.JSLX, WRT_config.lclj_init.zykt, data.sfzf, dldr_lists[index].KZJB, data.rowIndex)
		dldr_lists[index].ZF = type
		dldr_lists[index].SFZF = data.sfzf
		dldr_lists[index].splb = data.splb
		dldr_lists[index].xdfw = data.xdfw
		dldr_lists[index].spsl = data.spsl || "" // SQSL改SL
		// exportMBList()
		iskjywpd(dldr_lists[index], dldr_lists[index].ZH, dldr_lists[index].MBMC)
		$(`#${yemp}`).iziModal('destroy')
	} else {
		removeYp(dldr_lists[index].ZH, dldr_lists[index].MBMC)
		$(`#${yemp}`).iziModal('destroy')
		return
	}
}
//抗菌药物限制
function setTskjywhzdid(data) {
	let index = mbindex
	if (data && data.hzdid) {
		dldr_lists[mbindex].kjywhzdid = data.hzdid
	}
	if (!data.hzdid || data.hzdid == 0) {
		$("#if_tskjywhzd").iziModal('destroy')
		WRT_e.ui.message({
			title: '信息窗口',
			content: '紧急情况下使用特殊类抗生素,需填写会诊单,否则不允许下达特殊抗生素医嘱!',
			onOk() {
				removeYp(dldr_lists[index].ZH, dldr_lists[index].MBMC)
			}
		})
		return;
	} else {
		setKjypgl(yp_list,dldr_lists[index].ZH, dldr_lists[index].MBMC);
		// exportMBList()
	}
	$("#if_tskjywhzd").iziModal('destroy')
}
//选择抗菌药物使用方法
function SetKjypsyly(data) {
	let index = mbindex
	if (!dldr_lists[index] && index <= dldr_lists.length) {
		index--
	}
	let rtn = data.returnObj
	$("#if_kjypgl").iziModal('destroy')
	if (rtn == undefined) {
		removeYp(dldr_lists[index].ZH, dldr_lists[index].MBMC)
		return;
	} else if (rtn.SYFF == "0") {
		removeYp(dldr_lists[index].ZH, dldr_lists[index].MBMC)
		return;
	} else if (rtn.SYFF == "2" && rtn.WSW == "2") {
		removeYp(dldr_lists[index].ZH, dldr_lists[index].MBMC)
		return;
	} else {
		dldr_lists[index].kjypsyly = rtn
		// setSflb(dldr_lists[index])
		exportMBList()
	}
}

//刷新
function left_menu() {
	//获取获取病人信息
	WRT_e.api.lclj.initData({
		params: {
			al_blid: WRT_config.url.al_blid
		},
		success(data) {
			if (data) {
				WRT_config.lclj_init = JSON.parse(data)
				if (WRT_config.lclj_init.TSXX) {
					let per = WRT_config.lclj_init.TSXX.split("  ")
					WRT_config.lclj_init.per = per
				}
				console.log(WRT_config.lclj_init)
				// app.init()

				WRT_e.api.lclj.GetPaYwgms({
					params: {
						as_brbh: WRT_config.lclj_init.BRBH,
						as_yfdm: WRT_config.lclj_init.YFDM
					},
					success(data) {
						if (data) {
							WRT_config.PaYwgms = JSON.parse(data)
						}
					}
				})
				WRT_e.api.lclj.GetPaLcljData({
					params: {
						al_ljid: WRT_config.lclj_init.LJID,
						al_bzid: WRT_config.lclj_init.DQBZID
					},
					success(data) {
						if (data) {
							WRT_config.paLcljData = JSON.parse(data)
							console.log(WRT_config.paLcljData)
							//左侧
							var Menuleft = new Menuleft_View();
							Menuleft.$el = $("#left_content");
							Menuleft.init({ data: WRT_config.paLcljData }).render();
							left_init(WRT_config.paLcljData[0])

						}
					}
				})

			}
		}
	})
}

//初始化
var app = {
	init: function () {
		$(document).bind("click", function (e) {
			let t = $(`.td_text`)[0] // 最外层元素
			if (t && e.target.className.indexOf(t.className) < 0) {
				$(`.model_input`).addClass("none")
			}
		})
		$(".load_model").removeClass('load_img')
		//病人基本信息
		let person = new person_View()
		person.$el = $("#person_list");
		person.init({ data: WRT_config.lclj_init }).render();

		//左侧
		let ylhlleft = new ylhl_View();
		ylhlleft.$el = $(`#yljhlrw_lists${td_index}`);
		ylhlleft.init({ data: WRT_config.BzrwDetail }).render();
		$(`#yljhlrw_lists${td_index}`).removeClass("none")

		let Mainleft = new Main_View();
		Mainleft.$el = $(`#yznr_lists${td_index}`);
		Mainleft.init({ data: WRT_config.Bzrw }).render();

		let ypmbDetail = new ypmbDetail_View();
		ypmbDetail.$el = $(`#YpmbDetail`);
		ypmbDetail.init({ data: [] }).render();
		disabled_box()
	}
}

/*********视图 */
//左侧主体
var Menuleft_View = WRT_e.view.extend({
	render: function () {
		let html = `
        <div class="load_model"><img src="./images/loading.gif" style="width: 360px;"></div>
        <ul id="myTabs" class="nav nav-tabs" role="tablist">
        ${_.map(this.data, (obj, index) =>
			`
        <li role="presentation" class=" ${index == 0 ? `active` : ""}" onclick="dbselect(${index})">
            <a href="#step${index + 1}" aria-controls="${obj.BZMC}" role="tab" data-toggle="tab">${obj.BZMC}</a>
        </li>
        `).join('')}
        </ul>
        <div class="tab-content advice">
            ${_.map(this.data, (obj, index) =>
				`
            <div role="tabpanel" class="tab-pane ${index == 0 ? `active` : ""}" id="step${index + 1}">
            <div class="step_content">
                <!--<div class="ryts_title">${obj.BZMC}</div>-->
                <div class="ylhl_box">
                <div style="padding-bottom: 10px;">
                    <span class="small_a orange"></span>
                    <span class="ylhl_title">医疗及护理任务</span>
                </div>
                <div class="ylhl_content flex-row flex-fill">
                    <ul id="yljhlrw_lists${index}" class="none">
                    
                    </ul>
                </div>
                </div>
                <div id="yznr_lists${index}" class="yz_lists">
                
                </div>
            </div>
            </div>
            `).join('')}
        </div>
      `
		this.$el.html(html)
		return this
	},


})

//医疗及护理任务
var ylhl_View = WRT_e.view.extend({
	render: function () {
		let html = `
			${_.map(this.data, (obj, index) =>
			`${obj.LB == 1 || obj.LB == 4 ? `<li>
					${obj.ID_CZZ ? `
					${obj.WCSJ.replace(/\'/g, "") > obj.ZCWCSJ.replace(/\'/g, "") ? `
						<span style="color:Red;">&nbsp;!!&nbsp;</span>`: `<span id="lb_ts" >&nbsp;√&nbsp;</span>
					`}` : `
						${WRT_config.lclj_init.LJZT == '' ? `
						<input type="checkbox" name="rw_lists" value="${index}" />` : `
						<input class="checkBtn" onclick="return false;" type="checkbox" name="rw_lists" value="${index}" />`}
					`}
					<span class="ylhlrw_list ${obj.SHBX = 1 ? 'font_red' : ''}">${obj.RWMC}</span>
					<sapn class="ylhlrw_list ${obj.SHBX = 1 ? 'font_red' : ''}">${gettime(obj.ID_CZZ ? obj.WCSJ : obj.ZCWCSJ)}</sapn>
					${obj.ID_CZZ ? `<sapn>${obj.CZZ}</sapn>` : ''}
				</li>`: ""}`
		).join("")}`
		this.$el.html(html)
		return this
	},
	events: {
		'click .checkBtn': function () {
			WRT_e.ui.message({
				title: '提示',
				content: "该病人已出径，不允许操作!"
			})
		}
	}

})

//左侧医嘱内容
var Main_View = WRT_e.view.extend({
	// ${item.WCSJ<=item.ZCWCSJ}
	// <span id="lb_ts" style="color:Red;">&nbsp;&nbsp;!!</span>
	render: function () {
		let html = `
		${_.map(this.data, (obj, key) => `
		<div style="padding: 20px 10px 5px 10px;">
			<span class="small_a orange"></span>
			<span class="yz_title">${obj.title}</span>
		</div>
		${key == 0 ? `<table style="width:100%">
			<tbody>
				${_.map(WRT_config.BzrwDetail, (item) =>
			`${item.LB != 1 && item.LB != 4 ? `
						<tr style="width: 50%;float: left;">
							<td width="20px"></td>
							<td style="display: flex;align-items: center;">
								${item.WCSJ.replace(/\'/g, "") > item.ZCWCSJ.replace(/\'/g, "") ? `
									<span id="lb_ts" class="lb_ts_tooltip" >
										<span style="color:Red;">&nbsp;&nbsp;!!&nbsp;&nbsp;</span>
										<span class="lb_ts_tooltiptext">
											<span id="lb_ts">完成时间: ${gettime(item.WCSJ)}</span><br/>
											<span id="lb_ts">实际完成时间: ${gettime(item.ZCWCSJ)}</span><br/>
											<span id="lb_ts">超出时间: ${extraTime(Date.parse(new Date(item.WCSJ)), Date.parse(new Date(item.ZCWCSJ)))}</span>
										</span>
									</span>
									`: `
									<span id="lb_ts" >&nbsp;&nbsp;√&nbsp;&nbsp;</span>
								`}
								<span id="lb_mc" style="display:inline-block;color:Black;width:140px;overflow:hidden">${item.RWMC}</span>&nbsp;
								<span id="lb_nwcsj" style="color:Red;">${gettime(item.WCSJ)}</span>&nbsp;
								<span id="lb_sjwcsj">${item.CZZ}</span>
							</td>
						</tr>
					`: ''}`
		).join('')}
			</tbody>
		</table>`: ''}
		<div>
				<table class="yz_table">
				<thead>
						<tr>
						<th width="20"></th>
						<th width="140">项目名称</th>
						<th width="50">类型</th>
						<th width="300">医嘱内容</th>
						<th width="110">最晚完成时间</th>
						</tr>
				</thead>
				<tbody>
				${_.map(obj.children, (item, index) =>
			`
					<tr>
						<td class="first_td"><span class="middle_a crile_${key}_${index} grey"></span></td>
						<td class="two_td" onclick="dbdeail(${key},${index})">${item.MC}</td>
						<td>${getHQTJ(item)}</td>
						<td id="yz_${item.RWID}" class="four_td">
						</td>
						<td>${times}</td>
					</tr>
					`
		).join('')}
				</tbody>
			</table>
		</div>
		`).join("")}
		`
		this.$el.html(html)
		return this
	},
	events: {

	}
})

//病人基本信息
var person_View = WRT_e.view.extend({
	render: function () {
		let html = `
        <span class="small_a blue"></span>
        <h>病人基本信息</h>
        </br></br>
        <sapn>姓名：${this.data.per[0]}</sapn></br>
        <sapn>性别：${this.data.per[1]}</sapn></br>
        <sapn>病案号：${this.data.per[2]}</sapn></br>
        <sapn>床位号：${this.data.per[3]}</sapn></br>
        <span>${this.data.WXTS}</span></br>
      `
		this.$el.html(html)
		return this
	},
})

//右侧医嘱内容详情
var ypmbDetail_View = WRT_e.view.extend({
	render: function () {
		let html = `${this.data.length == 0 ? `<div>暂无医嘱内容</div>` : `
        ${_.map(this.data, (obj, key) =>
			`
            <tr>
                <td width="50"><input type="checkbox" class="checkbox`+ key + `" ${obj.check ? 'checked' : ''} onchange="dbcheck(this,${key})" /></td>
                <td class="table_td">
                    ${obj.children ? `${_.map(obj.children, (item, index) => `
                        <div>${item.MC}/${item.JL}*${item.BZL} / </br>
												<input id="jl${item.ID}" class="td_text" name="calculate" onchange="jl_input(this,${item.ID},${key},${index})" type="text" value="${item.YCYL}" /> ${item.JLDW} 
												<input id="FF${item.ID}" class="td_text" type="text" onclick="xz_input('ff',${item.ID},${key},${index})" value="${item.FF}" />
												<div id="yf${item.ID}" class="yf_input model_input none">
												</div>
												<input id="PL${item.ID}" class="td_text" type="text" onclick="xz_input('pl',${item.ID},${key},${index})" value="${item.PL}" />
												<div id="pl${item.ID}" class="pl_input model_input none">
												</div>
												<input type="text" id="kssj${item.ID}" class="Wdate kssj_wdata" value="${gettime(false)}" onchange="kssj_input(${item.ID},${key},${index})" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',readOnly:false})" autocomplete="off" style="width: 140px;" tag="0" />
                        </div>
                    `).join('')}` : `
                    <div>${obj.MC}/${obj.JL}*${obj.BZL} / 
										</br>
										<input id="jl${obj.ID}" class="td_text" name="calculate" onchange="jl_input(this,${obj.ID},${key},'0')"  type="text" value="${obj.YCYL}" /> ${obj.JLDW}  
										<input id="FF${obj.ID}" class="td_text" type="text" onclick="xz_input('ff',${obj.ID},${key})" value="${obj.FF}" />
										<div id="yf${obj.ID}" class="yf_input model_input none">
										</div>
										<input id="PL${obj.ID}" class="td_text" type="text" onclick="xz_input('pl',${obj.ID},${key})" value="${obj.PL}" />
										<div id="pl${obj.ID}" class="pl_input model_input none">
										</div>
										<input type="text" id="kssj${obj.ID}" class="Wdate kssj_wdata" value="${gettime(false)}" onchange="kssj_input(${obj.ID},${key},'0')" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',readOnly:false})" autocomplete="off" style="width: 140px;" tag="0" />
                    </div>`}
                </td>
            </tr>
            `
		).join('')}`
			}`

		this.$el.html(html)
		return this
	},
	events: {
	},
})


/********公共方法 */
//获取类型
function getHQTJ(obj) {
	let text = '';
	switch (obj.HQTJ) {
		case "zl":
			text = "治疗"
			break;
		case "tj":
			text = "特检"
			break;
		case "hy":
			text = "化验"
			break;
		case "ypcq":
			text = "长期"
			break;
		case "ypls":
			text = "临时"
			break;
		case "mbb":
			text = "慢病包"
			break;
		default:
			text = '病历'
			break;
	}
	return text
}

//药品审批html
function ypsp_html(ypfjxx) {
	let text = ''

	let zxfw = JSON.parse(ypfjxx.xzfw)
	WRT_config.XDFW_list = zxfw.map(function (item) {
		return item.XDFW
	})
	// zxfw=JSON.parse('[{"XDFW":"(国版)限肝功能衰竭"},{"XDFW":"(国版)无法使用甘草酸口服制剂的患者"},{"XDFW":"(省版)限抢救"},{"XDFW":"(省版)限肝病"}]')
	let type = false
	let arr = [...zxfw, { XDFW: '均不符合，需自费，请告知患者' }]
	if (ypfjxx.xsp == '0') {
		text = ''
		type = false
	} else if (ypfjxx.xsp == '1') {
		type = true
		text = '该项目为需【医保窗口】审批项目。'
	} else if (ypfjxx.xsp == '2') {
		type = true
		text = '该项目为需【药房】和【医保窗口】审批项目。'
	} else if (ypfjxx.xsp == '3') {
		type = true
		text = '该项目为需【药房】审批项目。'
	} else if (ypfjxx.xsp) {
		type = true
		text = '该项目为需审批项目。'
	}
	// <textarea name="tb_sbxzfw" rows="2" cols="20" id="tb_sbxzfw" style="height:130px;width:369px;">${ypfjxx.xzfw}</textarea>
	let temp = `
    <div id="XDFW_table">
        <span style="font-weight:bold;">药品名称：${ypfjxx.ypmc}</span><br>
        <span id="Label1" style="font-weight:bold;">医保使用限制范围如下，请根据疾病诊断准确选择：</span><br>
        <ul style="height:130px;width:369px;border: 1px solid;overflow: auto;padding: 3px 5px;">
        ${_.map(arr, (item, index) => `
        <li><input type="checkbox" name="xzfw_check" onclick="editCheckboxTrue(this)" value="${item.XDFW}"><span ${index == arr.length - 1 ? 'style="color:red"' : ''}>${item.XDFW}</sapn></li>
        `).join('')}
        </ul>
        <br>
        <span id="Label2" style="font-weight:bold;">病人诊断</span>&nbsp;<br>
        <textarea name="tb_brzd" rows="2" cols="20" id="tb_brzd" disabled="true" style="height:74px;width:367px;">${WRT_config.lclj_init.BRZD}</textarea><br>
        ${ypfjxx.xsp != 0 ? `<span id="lb_tsxx" style="display:inline-block;height:49px;width:372px;">${text}</span><br>` : ''}
        <span style="display:inline-block;height:49px;width:372px;color:red">注：请根据患者病情如实勾选并在病历中体现！</span><br>
        <table style="width: 370px" cellpadding="0" cellspacing="0">
            <tbody>
                <tr id="tr_spxm">
                    ${ypfjxx.xsp != 0 && ypfjxx.xsp ? `<td style="width: 180px; height: 24px;padding: 3px 0;"><span id="Label3">申请审批数量</span>
                        <input name="tb_spsl" type="text" id="tb_spsl" style="width:59px;border: 1px solid #736b6b;">
                    </td>`: ''}
                </tr>
                <tr align="center">
                    <td style="width: 180px; height: 15px;" align="center">
                        <button class="e_btn" value="确定" onclick="bt_click(1);return false;" id="bt_qd" style="width:160px;">${type ? '确认提交审批' : '确定'}</button>
                    </td>
                    <td style="width: 180px; height: 15px;" align="center">
                        <button class="e_btn" value="取消" onclick="SetSpjg('',true);return false;" id="bt_qx" style="width:160px;">取消</button>
                    </td>
                </tr>
            </tbody>
        </table>
        &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
        <br>
      </div>
    `
	return temp
}

//审批确定
function bt_click(lx) {
	var splb = WRT_config.ypfjxx.xsp;
	var obj = new Object();
	obj.SFZF = "";
	obj.SPLB = splb;
	let check = $('#XDFW_table input[type=checkbox]:checked')
	obj.xdfw = ''
	if (check[0]) {
		obj.xdfw = check[0].value
	} else if (check.length == 0) {
		alert("社保限制范围必须勾选");
		return
	}

	if (lx == '1') {
		if (splb == "0") {
			obj.SFZF = "0";
			if (obj.xdfw == '均不符合，需自费，请告知患者') {
				obj.SFZF = "1";
			}
		}
		else {
			if (splb != "3") {
				if (WRT_config.lclj_init.JSLX == "00")
					obj.SFZF = "0";
				else {
					if (WRT_config.XDFW_list.indexOf(obj.xdfw) >= 0)
						obj.SFZF = "0";
					if (obj.xdfw == '均不符合，需自费，请告知患者')
						obj.SFZF = "1";
				}
				if (obj.SFZF == "") {
					alert("请选择 是否自费还是公费!");
					return;
				}
			}
			else {
				obj.SFZF = 0;
			}
			var spsl = $("#tb_spsl")[0].value;
			if (spsl == "") {
				alert("请输入审批数量");
				return;
			}
			if (isNaN(spsl)) {
				alert("审批数量请输入数字!")
				return;
			}
			if (parseFloat(spsl) <= 0) {
				alert("审批数量不能为零或负数!");
				return;
			}
			obj.SPSL = spsl;
		}
	}

	if (obj.SPSL == "" || obj.SPSL == undefined)
		obj.SPSL = 0;

	SetSpjg({ sfzf: obj.SFZF, spsl: obj.SPSL, splb: obj.SPLB, xdfw: obj.xdfw }, true)
}

//修改复选框，实现单选框效果
function editCheckboxTrue(that) {
	var answer_checkbox_list = $('#XDFW_table input[type=checkbox]');
	var answer_checkbox_lenth = answer_checkbox_list.length;
	//循环得到此次
	for (var i = 0; i < answer_checkbox_lenth; i++) {
		answer_checkbox_list[i].checked = false;
	}
	that.checked = true;
}

//特殊药品会诊单id转化
function StringID(id) {
	if (typeof id != "string") {
		id = JSON.stringify(id)
	}
	return id
}
function disabled_box() {
	//已退出路径不可点击任务
	if (WRT_config.lclj_init.LJZT.indexOf('退出') >= 0) {
		let arr = $("input[name='rw_lists']")
		for (let i in arr) {
			arr[i].disabled = true
		}
	}
}
//清空医嘱内容
function clear_yzView() {
	let ypmbDetail = new ypmbDetail_View();
	ypmbDetail.$el = $(`#YpmbDetail`);
	ypmbDetail.init({ data: [] }).render();
}

//查找回车事件
function calAge() {
	var evt = window.event || e;
	if (evt.keyCode == 13) {
		onSearch()
	}
}
//医嘱内容详情搜索
function onSearch() {
	let text = $("#search_text")[0].value
	let arr = []
	WRT_config.YpmbDetail_copy = YpmbDetail_copy
	WRT_config.YpmbDetail_copy.map(function (item) {
		if (item.children) {
			let lists = []
			item.children.map(function (key) {
				if (key.MC.indexOf(text) >= 0) {
					arr.push(key)
				}
			})
			if (lists.length > 0) {
				arr.push({ children: lists })
			}
		} else if (item.MC.indexOf(text) >= 0) {
			arr.push(item)
		}
	})
	WRT_config.YpmbDetail = arr
	let ypmbDetail = new ypmbDetail_View();
	ypmbDetail.$el = $(`#YpmbDetail`);
	ypmbDetail.init({ data: arr }).render();
}

//设置费用类型
function setFylx(jslx, zykt, sfzf, kzjb) {
	let type = 0
	if (zykt == "0")
		type = 1
	else if (jslx == "00")
		type = 1
	else if (sfzf == "1")
		type = 1
	else {
		type = kzjb
	}
	return type
}
//检查病人是否有过敏
function checkYwgm(ypid) {
	var gmlx;
	var ls_rtn = "";
	let Ywgm_arr = WRT_config.PaYwgms || null
	if (Ywgm_arr == null)
		return "";
	//var now = new Date();
	//var fsdate;// = new Date(Ywgm_arr[i].GMSJ.substr(0,4), parseInt(Ywgm_arr[i].GMSJ.substr(5,2)) - 1, Ywgm_arr[i].GMSJ.substr(8,2), Ywgm_arr[i].GMSJ.substr(11,2), Ywgm_arr[i].GMSJ.substr(14,2))
	for (var i = 0; i < Ywgm_arr.length; i++) {

		if (Ywgm_arr[i].YPID == ypid || Ywgm_arr[i].FZQYPID == ypid) {
			if (Ywgm_arr[i].GMJG == null) {
				gmlx = Ywgm_arr[i].GMYPLX;
				for (j = 0; j < Ywgm_arr.length; j++) {
					if (Ywgm_arr[j].GMYPLX == gmlx && Ywgm_arr[j].GMJG != null) {
						if (Ywgm_arr[j].GMSJ == null)
							//if ( Ywgm_arr[j].GMYPLX == "01")
							ls_rtn = "患者曾经对[" + Ywgm_arr[j].MC + "]药品皮试阳性," + Ywgm_arr[j].GMYPLXMC + "过敏禁止使用!";
						//                            else
						//                                ls_rtn = "患者对[" + Ywgm_arr[j].MC + "]药品皮试阳性,请慎重考虑!";
						else
							ls_rtn = "患者于" + Ywgm_arr[i].GMSJ + "时对[" + Ywgm_arr[j].MC + "]药品皮试阳性," + Ywgm_arr[j].GMYPLXMC + "过敏禁止使用!";
						//                            if ( Ywgm_arr[j].GMYPLX == "01")
						//                                ls_rtn = "患者曾经对[" + Ywgm_arr[j].MC + "]药品皮试阳性,请慎重考虑!";
						//                            else
						//                                ls_rtn = "患者于" + Ywgm_arr[j].GMSJ + "时对[" + Ywgm_arr[j].MC + "]药品皮试阳性,请慎重考虑!";
						return ls_rtn;
					}
				}
			}
			else if (Ywgm_arr[i].GMJG == "1") {
				if (Ywgm_arr[i].GMSJ == null)
					if (Ywgm_arr[i].GMYPLX == "01")
						ls_rtn = "患者曾经对[" + Ywgm_arr[i].MC + "]药品皮试阳性," + Ywgm_arr[j].GMYPLXMC + "过敏禁止使用!";
					else
						ls_rtn = "患者曾经对[" + Ywgm_arr[i].MC + "]药品皮试阳性,禁止使用!";
				else
					if (Ywgm_arr[i].GMYPLX == "01")
						ls_rtn = "患者于" + Ywgm_arr[i].GMSJ + "时对[" + Ywgm_arr[i].MC + "]药品皮试阳性,禁止使用!";
					else
						ls_rtn = "患者于" + Ywgm_arr[i].GMSJ + "时对[" + Ywgm_arr[i].MC + "]药品皮试阳性,禁止使用!";
				return ls_rtn;
			}
		}
	}
	return ls_rtn;
}
//判断
function SetYp_callback_new(yp_list) {
	let zh = "", mbmc = "";
	zh = yp_list.ZH;
	mbmc = yp_list.MBMC;
	if (yp_list.YPID == 0) {
		WRT_e.ui.message({
			title: '提示',
			content: "【" + yp_list.MC + "】已被药房停止使用!"
		})
		removeYp(zh, mbmc);
		return;
	}
	if (yp_list.CLTS == "1") {
		WRT_e.ui.message({
			title: '提示',
			content: "根据医院处方点评工作组反馈和医院规定,您被限制开具药品【" + yp_list.MC + "】"
		})
		removeYp(zh, mbmc);
		return;
	}
	WRT_e.api.yz_sz.getYpFjxx({
		params: { 
			al_ypid: yp_list.YPID, 
			as_yfdm: WRT_config.lclj_init.YFDM || "", 
			as_jslx: WRT_config.lclj_init.JSLX, 
			al_zyid: WRT_config.lclj_init.ZYID, 
			as_yzlx: `${yp_list.CXTS == 0 ? 'ls' : 'cq'}`, 
			al_zkid: WRT_config.lclj_init.ZKID,
			gcp:''
		},
		success(data) {
			if (data.Code == 1) {
				let array = JSON.parse(data.Result)
				WRT_config.ypfjxx = array[0] || {}
				if (WRT_config.ypfjxx.gwyp == 1) {
					WRT_e.ui.message({
						title: '信息窗口',
						content: `${yp_list.MC}是高警示药品`,
						onOk() {
						}
					})
				}
				if (WRT_config.ypfjxx) {
					yp_list.XSP = WRT_config.ypfjxx.xsp || yp_list.xsp || ''
					let index = mbindex - 1
					if (index < 0) {
						index = 0
					}
					dldr_lists[index].xsp = WRT_config.ypfjxx.xsp
					istssysyl(yp_list, zh, mbmc)
				}
			} else {
				WRT_e.ui.message({
					title: '信息窗口',
					content: data.CodeMsg,
					onOk() {
						exportMBList()
					},
				})
				// WRT_e.ui.hint({type:'error',msg:data.CodeMsg})
			}
		}
	})

}

//国家采集提示
function istssysyl(yp_list, zh, mbmc) {
	if (WRT_config.ypfjxx.tssysyl) {
		WRT_e.ui.message({
			title: '信息窗口',
			content: WRT_config.ypfjxx.tssysyl,
			onOk() {
				// isgjcg()
				kzlywqx(yp_list, zh, mbmc)
				// isneednihss()
			},
		})
		return;
	}
	// isgjcg(yp_list, zh, mbmc)
	kzlywqx(yp_list, zh, mbmc)
}
//抗肿瘤药物的处方权限
function kzlywqx(yp_list, zh, mbmc) {
	if (WRT_config.ypfjxx.kzpj == "1" && WRT_config.yz_sz.ptjzlywqx != "1") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `${obj.mc}是普通使用级抗肿瘤药物，你没有开具普通级肿瘤药物的权限！药品ID=${yp_list.YPID}`,
			onOk() {
				removeYp(zh, mbmc);
				exportMBList()
			},
		})
		return
	} else if (WRT_config.ypfjxx.kzxj == "1" && WRT_config.yz_sz.xzjzlywqx != "1") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `${obj.mc}是限制使用级抗肿瘤药物，你没有开具限制级肿瘤药物的权限！药品ID=${yp_list.YPID}`,
			onOk() {
				removeYp(zh, mbmc);
				exportMBList()
			},
		})
		return
	}
	dxypkz(yp_list, zh, mbmc)
}
//毒性药品控制
function dxypkz(yp_list, zh, mbmc) {
	if (yp_list.GLLX == "D" && WRT_config.yz_sz.dxypqx != "0001") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `对不起，你没有毒性药品处方权限，无法开具药品【${yp_list.MC}】！药品ID=${yp_list.YPID} gllx=${yp_list.GLLX}`,
			onOk() {
				removeYp(zh, mbmc);
				exportMBList()
			},
		})
		return
	} else {
		mzypqx(yp_list, zh, mbmc)
	}
}
//麻醉药品控制
function mzypqx(yp_list, zh, mbmc) {
	if ((yp_list.GLLX == "M" || yp_list.GLLX == "1") && WRT_config.yz_sz.mzypqx != "1") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `对不起，你没有麻醉处方权限，无法开具药品【${yp_list.MC}】！药品ID=${yp_list.YPID} gllx=${yp_list.GLLX}`,
			onOk() {
				removeYp(zh, mbmc);
				exportMBList()
			},
		})
		return
	} else {
		iscMzyt(yp_list, zh, mbmc)
		// iscfypd(yp_list, zh, mbmc)
	}
}
//处方药判断
function iscfypd(index, yzlx, obj) {
	let list = null
	if (obj.dataop && obj.dataop.ypid) {
		list = obj.dataop
	} else if (obj.yp_dataop && obj.yp_dataop.gllx) {
		list = obj.yp_dataop
	} else if (obj.dataop) {
		list = getdataop(obj.dataop)
	}
	let fd = yzdata.filter(item => ((item.yp_dataop && item.yp_dataop.mzyp != 1) && item.cfy_Lists && item.cfy_Lists.length > 0))
	if (list && (list.gllx == '1' || list.gllx == 'M')) {
		cfy_func()
	} else if (yzlx == 'cydy' && (fd.length == 0 || !fd)) {
		cfy_func()
	}
	else {
		iscMzyt(index, yzlx, obj)
	}
	function cfy_func() {
		cfy_Lists = []
		WRT_e.api.yz_sz.GetBrzd({
			params: {
				al_blid: WRT_config.url.as_blid,
			},
			success(data) {
				if (data.Code == 1) {
					cfy_Lists = data.Result
					if (data.Result && data.Result.length == 0) {
						WRT_e.ui.message({
							title: '信息窗口',
							content: '未获取到大病历四或五的临床诊断!',
							onOk() {
								delZHTXT(index)
								yzdata.splice(index, 1)
								// isxsp(index, yzlx, obj)
								exportMBList()
								zhmbsrListDr()
							}
						})
						return
					}
					let html = "<div id='cfy_table'>"
					html += open_cfy(cfy_Lists)
					html += "</div>"
					WRT_e.ui.model({
						id: "if_cfy",
						title: "处方诊断",
						width: "650px",
						content: html,
						closeButton: false,
						closeOnEscape: false,
						iframe: false,
					})
				}
			}
		})
	}
}
//麻醉药选择用途
function iscMzyt(yp_list, zh, mbmc) {
	if ((yp_list.GLLX == 'M')) {
		Mzyt_func()
	}
	else {
		setSflb(yp_list, zh, mbmc)
	}
	function Mzyt_func() {
		WRT_e.api.yz_sz.GetMzyt({
			params: {
			},
			success(data) {
				if (data.Code == 1) {
					let arr = data.Result
					// WRT_config.Mzytlists=data.Result
					if (data.Result && data.Result.length == 0) {
						WRT_e.ui.message({
							title: '信息窗口',
							content: '未获取到麻醉药品用途!',
							onOk() {
								removeYp(zh, mbmc);
								exportMBList()
							}
						})
						return
					}
					let html = `
						<div>
							<ul>
								${_.map(arr, obj =>
								`<label><input name="Mzyt_radio" type="radio" value="${obj.DM}" />${obj.MC}</label></br>`
								).join('')}
							</ul>
							<a-button class="e_btn" onclick="save_Mzyt()" style="padding-top: 4px;">保存</a-button>
							<a-button class="e_btn" onclick="close_Mzyt()" style="padding-top: 4px;">关闭</a-button>
						</div>
						`
					WRT_e.ui.model({
						id: "if_Mzyt",
						title: "麻醉药品用途",
						width: "350px",
						content: html,
						closeButton: false,
						closeOnEscape: false,
						iframe: false,
					})
				}
			}
		})
	}
}
//麻醉药选择
function close_Mzyt() {
	$('#if_Mzyt').iziModal('destroy')
	removeYp(zh, mbmc);
	exportMBList()
}
//麻醉药选择用途
function save_Mzyt() {
	let list = $("input[name='Mzyt_radio']:checked").val()
	if (list) {
		dldr_lists[mbindex].mzyt = list
		$("#if_Mzyt").iziModal('destroy')
		setSflb(dldr_lists[mbindex], dldr_lists[mbindex].ZH, dldr_lists[mbindex].MBMC)
	} else {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `麻醉药品用途不能为空！`,
			onOk() {
			}
		})
	}
}

//药品审批
function setSflb(yp_list, zh, mbmc) {
	//自费或者住院未开通
	if (WRT_config.lclj_init.JSLX == '00') {
		//强制审批
		if (yp_list.XSP == '2' || yp_list.XSP == '3') {
			let as_sbxzfw = escape(yp_list.XZFW)
			let as_brzd = escape(WRT_config.lclj_init.BRZD)
			if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
				let html = ypsp_html(WRT_config.ypfjxx)
				WRT_e.ui.model({
					id: "if_ypsp",
					title: "医保限制支付范围提示",
					width: "650px",
					content: html,
					closeButton: false,
					closeOnEscape: false,
					iframe: false,
				})
			} else if (WRT_config.ypfjxx.xzfw_bbzt == 2) {
				let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.lclj_init.JSLX}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=`
				// page_iframe.add("审批", url)
				// yzdata.splice(index, 1)
				WRT_e.ui.model({
					id: "ypsp",
					title: "医保限制支付范围提示",
					width: "650px",
					iframeURL: url,
					closeButton: false,
					closeOnEscape: false,
					iframe: true,
				})
				$("#ypsp iframe").load(function () {
					$("#ypsp iframe").contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
					$("#ypsp iframe").contents().find("#tb_brzd").text(WRT_config.lclj_init.BRZD);
				})
			}
			return;
		}
	} else {
		let xzfw_type = ''
		if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
			xzfw_type = '[]'
		} else if (WRT_config.ypfjxx.xzfw_bbzt == 2) {
			xzfw_type = ''
		}
		if (yp_list.XSP == "0" && WRT_config.ypfjxx.xzfw == xzfw_type) {
			if (yp_list.KZJB.indexOf('自费') >= 0 && parseFloat(yp_list.XZDJ) > 2) {
				WRT_e.ui.message({
					title: '信息窗口',
					content: '该药为自费药品,请务必告知病人签字后方可使用!',
					onOk() {
						// exportMBList()
						iskjywpd(yp_list, zh, mbmc)
					}
				})
				return;
			}
		}
		else if ((yp_list.XSP == '0' && WRT_config.ypfjxx.xzfw == xzfw_type) || (yp_list.XSP == null) || (WRT_config.ypfjxx.xzfw == '')) {
			//无需审批
			// iskjywpd(yp_list, zh, mbmc)
			exportMBList()
			return;
		}
		else {
			let as_sbxzfw = escape(WRT_config.ypfjxx.xzfw)
			// let as_brzd = escape(WRT_config.yz_sz.zd)
			if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
				let html = ypsp_html(WRT_config.ypfjxx)
				WRT_e.ui.model({
					id: "if_ypsp",
					title: "医保限制支付范围提示",
					width: "650px",
					content: html,
					closeButton: false,
					closeOnEscape: false,
					iframe: false,
				})
			} else if (WRT_config.ypfjxx.xzfw_bbzt == 2) {
				let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.lclj_init.JSLX}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=`
				// page_iframe.add("审批", url)
				WRT_e.ui.model({
					id: "ypsp",
					title: "医保限制支付范围提示",
					width: "650px",
					iframeURL: url,
					closeButton: false,
					closeOnEscape: false,
					iframe: true,
				})
				$("#ypsp iframe").load(function () {
					$("#ypsp iframe").contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
					$("#ypsp iframe").contents().find("#tb_brzd").text(WRT_config.lclj_init.BRZD);
				})
			}
			return
		}
	}
	exportMBList()
}
////抗菌药品的判断
function iskjywpd(yp_list, zh, mbmc) {
	//抗菌药品的判断
	let rtn_val, kjjb;
	hzdid = 0;
	rtn_val = yp_list.KJJB || '';
	kjjb = rtn_val.substr(0, 1);
	if (kjjb == "3") {  //特殊类抗生素，走特殊抗菌药物审批流程
		hzdid = rtn_val.substr(rtn_val.indexOf("-") + 1);
	}
	let psjg = checkYwgm(yp_list.YPID);
	if (psjg != "") {
		WRT_e.ui.message({
			title: '提示',
			content: psjg
		})
		removeYp(zh, mbmc);
		return;
	}
	var grzk = WRT_config.lclj_init.GRZKBZ;  //是否感染专科，可以降半级使用特殊抗生素
	if (kjjb != "0" && kjjb != "4") {
		if (yp_list.CXTS != "0") { //长期
			var j_gzdm = WRT_config.lclj_init.GZDM;
			if (j_gzdm == "" || j_gzdm == "0000") {
				WRT_e.ui.message({
					title: '提示',
					content: "工种代码未设定,无法开具抗生素类药品！"
				})
				removeYp(zh, mbmc);
				return;
			}
			else {
				switch (j_gzdm) {
					case "0010":
					case "0019":
					case "0381":
						j_gzdm = "0011";
						break;
					case "0382":
						j_gzdm = "0012";
						break;
					case "0383":
						j_gzdm = "0013";
						break;
					case "0384":
						j_gzdm = "0014";
						break;
					default:
						break;
				}
				if (j_gzdm == "0010")//// || j_gzdm=="0010")  //市级名中医参照主任医师的抗生素级别
					j_gzdm = "0011";
				//ls_gzdm 0011 主任医师   0012 副主任医师  0013主治医师  0014 住院医师  0017未确定医师
				if (j_gzdm != "0014" && j_gzdm != "0013" && j_gzdm != "0012" && j_gzdm != "0011" && j_gzdm != "0017") {
					WRT_e.ui.message({
						title: '提示',
						content: "根据您的职称级别,您没有被允许开具抗菌药品!"
					})
					removeYp(zh, mbmc);
					return;
				}
				if ((j_gzdm == "0014" || j_gzdm == "0017") && kjjb > "1") {
					WRT_e.ui.message({
						title: '提示',
						content: "根据卫生部规定,住院医师不得开具限制类及特殊类抗菌药品,如紧急使用,请开临时医嘱!!"
					})
					removeYp(zh, mbmc);
					return;
				}
				if (j_gzdm == "0013" && kjjb >= "2") {
					WRT_e.ui.message({
						title: '提示',
						content: "根据卫生部规定,主治医师不得开具限制类及特殊类抗菌药品,如紧急使用,请开临时医嘱并填写紧急会诊单!"
					})
					removeYp(zh, mbmc);
					return;
				}
				if (j_gzdm == "0013" && kjjb == "2") {
					if (grzk != "1") {
						WRT_e.ui.message({
							title: '提示',
							content: "根据卫生部规定,主治医师不得开具限制类及特殊类抗菌药品,如紧急使用,请开临时医嘱并填写紧急会诊单!"
						})
						removeYp(zh, mbmc);
						return;
					}
				}
				if (kjjb > "2") {
					var tsqx = WRT_config.lclj_init.TSKSSQX;
					if (tsqx == "1") {
						if (hzdid == "0") {
							WRT_e.ui.message({
								title: '提示',
								content: "请先进行特殊类抗生素药物会诊单会诊通过后再进行医嘱的下达。"
							})
							removeYp(zh, mbmc);
							return;
						}
						// form1.kjyp_hzdid[nowrow].value = hzdid;
					}
					else {
						if (j_gzdm == "0012") {
							if (grzk == "1") {
								if (hzdid == "0") {
									WRT_e.ui.message({
										title: '提示',
										content: "请先进行特殊类抗生素药物会诊单会诊通过后再进行医嘱的下达。"
									})
									removeYp(zh, mbmc);
									return;
								}
								//form1.kjyp_hzdid[currentrow].value = hzdid;
							}
							else {
								WRT_e.ui.message({
									title: '提示',
									content: "根据卫生部规定, 副主任医师不得开具特殊类抗菌药品,如紧急使用,请开临时医嘱并填写紧急会诊单!"
								})
								removeYp(zh, mbmc);
								return;
							}
						}
						if (j_gzdm == "0011") {  //符合开特殊抗生素的权限，但需要有会诊单
							if (hzdid == "0") {
								WRT_e.ui.message({
									title: '提示',
									content: "请先进行特殊类抗生素药物会诊单会诊通过后再进行医嘱的下达。"
								})
								removeYp(zh, mbmc);
								return;
							}
							//form1.kjyp_hzdid[nowrow].value = hzdid;
						}
					}
				}
			}
		}
		else {
			if (kjjb == "3") {   //特殊级抗生素，临时医嘱需填写紧急会诊单
				if (hzdid <= 0) {
					WRT_e.ui.model({
						id: "if_tskjywhzd",
						title: "特殊抗菌药品会诊单",
						width: "700px",
						iframeURL: WRT_config.server + "/zyblhzd/tskjywhzd.aspx?as_blid=" + WRT_config.url.al_blid + "&as_ypid=" + yp_list.YPID + "&as_tmpid=" + Math.random(),
						iframeHeight: '600px',
						iframe: true,
					})
				}
				if (hzdid == 0 || hzdid == undefined) {
					WRT_e.ui.message({
						title: '提示',
						content: "紧急情况下使用特殊类抗生素,需填写会诊单,否则不允许下达特殊抗生素医嘱!"
					})
					removeYp(zh, mbmc);
					return
				}
			}
			setKjypgl(yp_list, zh, mbmc);
		}
	} else {
		WRT_e.ui.message({
			title: '信息窗口',
			content: "你的职称无法开具特殊使用级抗菌药物。临时医嘱中，副高及以上职称才能开具特殊使用级抗菌药物！",
			onOk() {
				removeYp(zh, mbmc);
				exportMBList()
			}
		})

	}
}
//抗菌
function setKjypgl(yp_list,zh, mbmc) {
	let zkid = WRT_config.url.al_zkid;
	let zyid = WRT_config.lclj_init.ZYID;
	let blid = WRT_config.url.al_blid;
	let ypid = yp_list.YPID;
	rtn_val = yp_list.KJJB || '';
	let kjjb = rtn_val.substr(0, 1);
	if (!kjjb) {
		exportMBList()
		// setSflb(yp_list, kjjb)
		return;
	}
	if (yp_list.CXTS != "0")
		WRT_e.ui.model({
			id: "if_kjypgl",
			title: "会诊单",
			width: "650px",
			iframeHeight: '800px',
			iframeURL: WRT_config.server + "/xypyz/kjypgl.aspx?as_kjjb=" + kjjb + "&as_ypid=" + ypid + "&as_zkid=" + zkid + "&as_zyid=" + zyid + "&as_blid=" + blid + "&as_yzlx=cq&as_version=djm&as_rowindex=" + mbindex + "&tmpid=" + Math.random(),
			iframe: true,
			closeButton: false,
			closeOnEscape: false,
		})
	else
		WRT_e.ui.model({
			id: "if_kjypgl",
			title: "抗菌药品管理",
			width: "650px",
			iframeHeight: '800px',
			iframeURL: WRT_config.server + "/xypyz/kjypgl.aspx?as_kjjb=" + kjjb + "&as_ypid=" + ypid + "&as_zkid=" + zkid + "&as_zyid=" + zyid + "&as_blid=" + blid + "&as_yzlx=cq&as_version=djm&as_rowindex=" + mbindex + "&tmpid=" + Math.random(),
			iframe: true,
			closeButton: false,
			closeOnEscape: false,
		})
}


//删除
function removeYp(zh, mbmc, ID) {
	if (dldr_lists.length > 0) {
		for (var i = dldr_lists.length - 1; i >= 0; i--) {
			if (dldr_lists[i].ZH == zh && dldr_lists[i].MBMC == mbmc) {
				dldr_lists[i].DEL = "1";
			}
			else
				break;
		}
	}
	// dealYznr();
	exportMBList()
}

// 病人基本信息侧边按钮
//保存
function onSave() {
	if (WRT_config.lclj_init.LJZT == "") {
		let zkfk = [], ypyz = []
		let checkbox = $("input[name='rw_lists']:checked")
		if (checkbox.length > 0) {
			for (let i in checkbox) {
				if (checkbox[i] && checkbox[i].value) {
					let list = WRT_config.BzrwDetail[checkbox[i].value]
					zkfk.push({
						lb: list.LB,
						bzid: list.BZID,
						rwid: list.RWID,
						hqtj: list.HQTJ,
						zcwcsj: gettime(list.ZCWCSJ || ''),
						hqdm: list.HQDM || ''
					})
				}
			}
		}
		let rtn = false
		WRT_config.Bzrw.map(function (item) {
			if (item.children) {
				item.children.map(function (key) {
					//化验
					if (key.HQTJ == 'hy' && key.hymb) {
						zkfk.push({
							lb: key.LB,
							bzid: key.BZID,
							rwid: key.RWID,
							hqtj: key.HQTJ,
							hqdm: key.HQDM || '',
							zcwcsj: gettime(key.ZCWCSJ || ''),
						})
					}
					//特检
					if (key.HQTJ == 'tj' && key.zytjsqdtx) {
						zkfk.push({
							lb: key.LB,
							bzid: key.BZID,
							rwid: key.RWID,
							hqtj: key.HQTJ,
							hqdm: key.HQDM || '',
							zcwcsj: gettime(key.ZCWCSJ || ''),
						})
					}
					//医嘱
					if (key.yznr_lists && key.yznr_lists.length > 0) {
						zkfk.push({
							lb: key.LB,
							bzid: key.BZID,
							rwid: key.RWID,
							hqtj: key.HQTJ,
							hqdm: key.HQDM || '',
							zcwcsj: gettime(key.ZCWCSJ || ''),
						})
						let arr = []
						key.yznr_lists.map(function (ev) {
							let kjypsyly = ev.kjypsyly || {}
							let kjyp_qjss_spysid = StringID(kjypsyly.QJSS_SPYSID || '')
							arr.push({
								ypid: ev.YPID,
								mbmc: ev.MBMC,
								ypmc: ev.MC,
								zh: ev.ZH,
								// zhstr:'', 
								ycyl: ev.YCYL,
								gysj: ev.GYSJ,
								jldw: ev.JLDW,
								tsyf: ev.TSYF,
								sfzf: `${ev.SFZF ? ev.SFZF : (ev.ZF == "1" || ev.ZF == "0") ? ev.ZF : '0'}`,
								zxff: ev.FF,
								zxpl: ev.PL,
								xsp: ev.xsp || ev.XSP || "",
								sqsl: ev.spsl || '',
								cxts: ev.CXTS,
								kssj: gettime(ev.KSSJ_TXT),
								kjyp_kjjb: kjypsyly.KJJB || '',
								kjyp_syff: kjypsyly.SYFF || '',
								kjyp_fhkj: kjypsyly.FHKJ || '',
								kjyp_fknt: kjypsyly.FKNT || '',
								kjyp_qjss: kjypsyly.QJSS || '',//清洁手术
								kjyp_qjss_spsj: kjypsyly.QJSS_SPSJ || '',//审批时间
								kjyp_qjss_spysid: kjyp_qjss_spysid, // 审批医生ID
								kjyp_qjss_spyj: kjypsyly.QJSS_SPYJ || '', // 审批意见
								kjyp_qjss_gwys: kjypsyly.QJSS_GWYS || '', // 高危因素
								kjyp_sqsy: kjypsyly.SQSY || '',
								kjyp_ssmc: kjypsyly.SSMC || '',
								kjyp_syly: kjypsyly.SYLY || '',
								kjyp_tbjb: kjypsyly.TBJB || '',
								kjyp_wsw: kjypsyly.WSW || '',
								kjyp_wswbz: kjypsyly.WSWBZ || '',
								kjyp_yffl: kjypsyly.YFFL || '',
								xseyf_sfwcgwys: kjypsyly.XSEYF_SFWCGWYS || '', //是否有围产高危因素
								xseyf_wcgwys: kjypsyly.XSEYF_WCGWYS || '', // 围产高危因素
								xseyf_wcgwys_qt: kjypsyly.XSEYF_WCGWYS_QT || '', // 其他高危因素
								xseyf_sfylcbx: kjypsyly.XSEYF_SFYLCBX || '', // 是否有临床表现
								xseyf_lcbx: kjypsyly.XSEYF_LCBX || '', // 临床表现
								del: '',
								zhstr: ev.ZHSTR || '',
								cxts: key.CXTS,
								xdfw: ev.xdfw || '', // 
							})
						})
						ypyz = [...ypyz, ...arr]
					}
					if (key.selected) {
						zkfk.push({
							lb: key.LB,
							bzid: key.BZID,
							rwid: key.RWID,
							hqtj: key.HQTJ,
							hqdm: key.HQDM,
							zcwcsj: gettime(key.ZCWCSJ || ''),
						})
					}
				})
			}
		})
		let stype = ypyz.filter(item => item.ycyl <= 0)
		if (stype && stype.length > 0) {
			WRT_e.ui.message({
				title: '提示',
				content: `${stype[0].ypmc}一次用量不可小于或等于0`
			})
			return
		}
		let params = {
			al_blid: WRT_config.url.al_blid,
			al_ljid: WRT_config.lclj_init.LJID,
			al_zlzid: WRT_config.url.al_zlzid,
			as_yqdm: WRT_config.lclj_init.YQDM,
			zxfkData: zkfk,
			ypyzData: ypyz,
		}
		// alert(JSON.stringify(params))
		if(zkfk.length==0&&ypyz.length==0){
			WRT_e.ui.hint({ msg: '未选中医嘱,无需保存！' })
			return
		}
		WRT_e.api.lclj.Save({
			params: {
				al_blid: WRT_config.url.al_blid,
				al_ljid: WRT_config.lclj_init.LJID,
				al_zlzid: WRT_config.url.al_zlzid,
				as_yqdm: WRT_config.lclj_init.YQDM,
				zxfkData: zkfk,
				ypyzData: ypyz,
			},
			success(data) {
				if (data == 1) {
					WRT_e.ui.hint({ msg: '保存成功', type: 'success' })
					left_init(WRT_config.paLcljData[td_index])
					$(".ylhlrw_list").removeClass("font_red")
					$(".ylhlrw_list").addClass("font_black")
					clear_yzView()
				} else {
					WRT_e.ui.hint({ msg: '保存失败', type: 'error' })
				}
			}
		})

	} else {
		WRT_e.ui.message({
			title: '提示',
			content: "该病人已出径，不允许修改!"
		})
	}
}

//重新评估
function GetEnable() {
	//   if (WRT_config.lclj_init.LJZT =="") {
	var url = "lclj/ljpg.aspx?as_blid=" + WRT_config.url.al_blid + "&as_ljid=" + WRT_config.lclj_init.LJID + "&as_cxpg=1&tmpid=" + Math.random() + '&as_openmode=ehr3';
	parent.e_OpenTab(url, "入径评估");
	//   } else {
	//     WRT_e.ui.message({
	//         title:'提示',
	//         content:"该病人已出径，不允许修改!"
	//     })
	//   }
}
//强制出径
function Getqzcj() {
	if (WRT_config.lclj_init.LJZT == "") {
		// <textarea id="tb_CJLY" rows="3" cols="30" style="vertical-align: top;" value=""></textarea>
		// value="合并并发症" value="病情加重" value="诊断发生变化" value="其他"
		if (WRT_config.lclj_init.ZTBZ == 1) {
			WRT_e.ui.message({
				title: '提示',
				content: "病人病历已经封存了!"
			})
			return;
		}
		let html = `
        <dd style="margin-left:24px;">
          <label> 请填写出径理由：</label>
          <div class="radio">
            <label>
                <input type="radio" name="radio" value="合并并发症" onclick="cRadio(value)">合并并发症</input>
            </label>
            <label>
                <input type="radio" name="radio" value="病情加重" onclick="cRadio(value)">病情加重</input>
            </label>
            <label>
                <input type="radio" name="radio" value="诊断发生变化" onclick="cRadio(value)">诊断发生变化</input>
            </label>
            <label>
                <input type="radio" name="radio" value="其他" onclick="cRadio(value)">其他</input>
            </label>
          </div>
          <textarea id="tb_CJLY" rows="3" cols="30" style="vertical-align: top;" value="" readonly></textarea>
          </br></br>
          <button class="e_btn" onclick="onsaveCjly()">保存</button>
          <button class="e_btn" onclick="$('#if_cjly').iziModal('destroy')">取消</button>
        </dd>`
		WRT_e.ui.model({
			id: "if_cjly",
			title: '出径理由',
			width: "450px",
			content: html,
		})

	} else {
		WRT_e.ui.message({
			title: '提示',
			content: "该病人已出径，不允许修改!"
		})
	}
}
// 病人基本inxi完成按钮 
function taskDone() {
	if (WRT_config.lclj_init.LJZT == '') {
		WRT_e.api.lclj.FinishLclj({
			params: {
				al_blid: WRT_config.url.al_blid,
				al_ljid: WRT_config.lclj_init.LJID,
			},
			success(data) {
				if (data) {
					WRT_e.ui.hint({ msg: '患者完成临床路径的任务，可以出院了', type: 'success' })
					parent.resultData()
					left_menu()
				} else {
					WRT_e.ui.hint({ msg: '未完成临床路径的任务', type: 'error' })
				}
			}
		})
	} else {
		WRT_e.ui.message({
			title: '提示',
			content: "该病人已出径，不允许修改!"
		})
	}
}
// 点击单选框 
function cRadio(val) {
	if (val != '其他') {
		$("#tb_CJLY")[0].value = val
	} else {
		$("#tb_CJLY")[0].value = ""
		$("#tb_CJLY").removeAttr("readonly")
	}
}
//保存理由
function onsaveCjly() {
	let tb_CJLY = $("#tb_CJLY")[0].value;
	WRT_e.api.lclj.Qzcj({
		params: {
			al_blid: WRT_config.url.al_blid,
			al_ljid: WRT_config.lclj_init.LJID,
			as_cjyy: tb_CJLY
		},
		success(data) {
			if (data) {
				$("#if_cjly").iziModal('destroy')
				WRT_e.ui.hint({ msg: '操作成功', type: 'success' })
				parent.resultData()
				left_menu()
				// window.location.reload()
			}
		}
	})
}
//操作说明
function textOut() {
	if (WRT_config.lclj_init.LJZT == "") {
		WRT_e.ui.message({
			title: '提示',
			content: '该功能正在整理中'
		});
		return;
	} else {
		WRT_e.ui.message({
			title: '提示',
			content: "该病人已出径，不允许修改!"
		})
	}
}

//导入队列
function exportMBList() {
	let rtn = false
	if (dldr_lists.length > 0) {
		if (mbindex <= dldr_lists.length) {
			if (mbindex == dldr_lists.length) {
				let arr = []
				for (let i = 0; i < yznr_lists.length; i++) {
					for (let index = 0; index < dldr_lists.length; index++) {
						if (yznr_lists[i].ID == dldr_lists[index].ID) {
							if (dldr_lists[index].DEL == 1) {
								rtn = true
								yznr_lists.splice(i, 1)
								i--;
							}
							dldr_lists.splice(index, 1)
							index = 0
						}
					}

				}
				if (rtn) {
					WRT_config.YpmbDetail[check_key].check = false
					$(`#YpmbDetail .checkbox${check_key}`)[0].checked = false
					$(`.crile_${yz_key}_${yz_index}`).removeClass("crile_blue")
				}
				yznr_lists = [...yznr_lists, ...dldr_lists]
				// WRT_config.Bzrw[key].children[index].yznr_lists=yznr_lists
				dldr_lists = [];
				mbindex = 0;
				dealYznr();
				return
			}
			SetYp_callback_new(dldr_lists[mbindex])
			mbindex++

		}
	}
}
//勾选
function dbcheck(ev, key) {
	let list = WRT_config.YpmbDetail[key]
	check_key = key
	if (list) {
		WRT_config.YpmbDetail[key].check = !list.check
		mbindex = 0
		if (list.check) {
			if (list.children) {
				// 医嘱内容变化left内容变化
				let arr = []
				list.children.map(function (item) {
					item.YCYL = $(`#jl${item.ID}`).val()
					item.KSSJ_TXT = $(`#kssj${item.ID}`).val()
					if (item.DEL) {
						delete item.DEL
					}
					arr.push(item)
				})
				arr.map(function (item, i) {
					if (i == 0)
						item.ZHSTR = "┐";
					else if (i == arr.length - 1)
						item.ZHSTR = "┘";
					else
						item.ZHSTR = "│";
				})
				yznr_lists = [...yznr_lists, ...arr]
				dldr_lists = arr
			} else {
				list.YCYL = $(`#jl${list.ID}`).val()
				list.KSSJ_TXT = $(`#kssj${list.ID}`).val()
				yznr_lists = [...yznr_lists, list]
				dldr_lists = [list]
			}
			console.log(dldr_lists)
			mbindex++
			SetYp_callback_new(dldr_lists[0])
		} else {
			if (list.children) {
				list.children.map(function (ev) {
					yznr_lists.map(function (item, index) {
						if (item.ID == ev.ID) {
							yznr_lists.splice(index, 1)
							return
						}
					})
				})
			} else {
				yznr_lists.map(function (item, index) {
					if (item.ID == list.ID) {
						yznr_lists.splice(index, 1)
						return
					}
				})
			}
			dealYznr()
		}
	}
}

//选中下拉框（用法频率）
function isModel(type, id, value, key, index) {
	$(".model_input").addClass("none")
	if (type == 'ff') {
		type = 'FF'
	} else {
		type = 'PL'
	}
	yz_isModel = false
	if (index >= 0) {
		let target = WRT_config.YpmbDetail[key].children[index]
		WRT_config.YpmbDetail[key].children[index][type] = value
		if (WRT_config.YpmbDetail[key].check) {
			yznr_lists.map(function (item) {
				if (item.ID == target.ID) {
					item[type] = target[type]
				}
			})
			dealYznr()
		}
	} else {
		WRT_config.YpmbDetail[key][type] = value
		if (WRT_config.YpmbDetail[key].check) {
			yznr_lists.map(function (item) {
				if (item.ID == WRT_config.YpmbDetail[key].ID) {
					item[type] = WRT_config.YpmbDetail[key][type]
				}
			})
			dealYznr()
		}
	}
	$(`#${type}${id}`)[0].value = value
}
//处理医嘱内容回写
function dealYznr() {
	if (yznr_lists.length > 0) {
		WRT_config.Bzrw[yz_key].children[yz_index].yznr_lists = yznr_lists
		$(`.crile_${yz_key}_${yz_index}`).addClass("crile_blue")
		// $(`.crile_${yz_key}_${yz_index}`).removeClass("crile_color")
	} else {
		WRT_config.YpmbDetail[check_key].check = false
		$(`#YpmbDetail .checkbox${check_key}`)[0].checked = false
		$(`.crile_${yz_key}_${yz_index}`).removeClass("crile_blue")
	}
	if (yz_Model) {
		let temp = dealHtml(yznr_lists)
		$(`#yz_${yz_Model.RWID}`).html(temp)
		// yz_Model=null
	}
	function dealHtml(arr) {
		if (arr.length == 0) {
			return ''
		} else {
			let html = `${_.map(arr, item =>
				`<div>${item.MC}/${item.JL}*${item.BZL} / </br>
					${item.KSSJ_TXT} ${item.YCYL} ${item.JLDW} ${item.FF} ${item.PL}
					</div>`
			).join('')}`
			return html
		}
	}
}

//弹出下拉框（用法频率）
function xz_input(type, id, key, index) {
	$(".model_input").addClass("none")
	if (type == 'ff') {
		let temp = input_html(WRT_config.yf, type, id, key, index)
		$(`#yf${id}`).html(temp)
		$(`#yf${id}`).removeClass("none")
	} else {
		let temp = input_html(WRT_config.pl, type, id, key, index)
		$(`#pl${id}`).html(temp)
		$(`#pl${id}`).removeClass("none")
	}
	function input_html(target, type, id, key, index) {
		let html = `
        <input type="text" class="li" readonly="" value="" data="" title="">
        ${_.map(target, obj => `
            <input type="text" class="li" readonly="" value="${obj.MC}" data="${obj.DM}" title="${obj.DM}" onclick="isModel('${type}',${id},'${obj.DM}',${key},${index})">
        `).join('')}
        `
		return html
	}
}

//输入框监听
function jl_input(ev, id, key, index) {
	let checkbox = $(`.checkbox${key}`).is(":checked")
	if (checkbox) {
		let list = WRT_config.YpmbDetail[key]
		if (list) {
			// WRT_config.YpmbDetail[key].check=!list.check
			mbindex = 0
			if (list.check) {
				if (list.children) {
					// 医嘱内容变化left内容变化
					list.children.map(function (item) {
						item.YCYL = $(`#jl${item.ID}`).val()
					})
					// yznr_lists=[...yznr_lists,...list.children]
					dldr_lists = list.children
				} else {
					list.YCYL = $(`#jl${list.ID}`).val()
					// yznr_lists=[...yznr_lists,list]
					dldr_lists = [list]
				}
			}
			dealYznr() // //处理医嘱内容回写
		}
	}
	// 
}

//计划开始时间监听
function kssj_input(id, key, index) {
	let checkbox = $(`.checkbox${key}`).is(":checked")
	if (checkbox) {
		let list = WRT_config.YpmbDetail[key]
		if (list) {
			// WRT_config.YpmbDetail[key].check=!list.check
			mbindex = 0
			if (list.check) {
				if (list.children) {
					// 医嘱内容变化left内容变化
					list.children.map(function (item) {
						item.KSSJ_TXT = $(`#kssj${item.ID}`).val()
					})
					// yznr_lists=[...yznr_lists,...list.children]
					dldr_lists = list.children
				} else {
					list.KSSJ_TXT = $(`#kssj${list.ID}`).val()
					// yznr_lists=[...yznr_lists,list]
					dldr_lists = [list]
				}
			}
			dealYznr() // //处理医嘱内容回写
		}
	}else{
		WRT_config.YpmbDetail[key].KSSJ_TXT=$(`#kssj${list.ID}`).val()
	}
	// 
}

//医嘱详情
function dbdeail(key, index) {
	if (WRT_config.lclj_init.LJZT == '') {
		let list = {}
		$(`.middle_a`).removeClass("crile_color")
		if (WRT_config.Bzrw[key]) {
			// $(`.crile_${key}_${index}`).addClass("crile_color")
			yz_Model = WRT_config.Bzrw[key].children[index] || {}
			yz_key = key
			yz_index = index
			if (yz_Model.HQTJ != 'ypcq' && yz_Model.HQTJ != 'ypls' && yz_Model.HQTJ != 'mbb') {
				// WRT_config.Bzrw[key].children[index].zytjsqdtx=false
				if (yz_Model.HQTJ == 'hy') {
					if (!WRT_config.Bzrw[key].children[index].hymb) {
						WRT_e.api.lclj.GetHymbDetail({
							params: {
								al_hyid: yz_Model.HQDM
							},
							success(data) {
								if (data) {
									if (data == 'fail') {
										WRT_config.Bzrw[key].children[index].hymb = false
										$(`.crile_${key}_${index}`).addClass("crile_grey")
										// $(`.crile_${yz_key}_${yz_index}`).addClass("crile_blue")
										WRT_e.ui.hint({ msg: '检验项目未维护', type: 'error' })
									} else {
										$(`#yz_${yz_Model.RWID}`).html(data)
										WRT_config.Bzrw[key].children[index].hymb = data
										$(`.crile_${key}_${index}`).addClass("crile_blue")
										$(`.crile_${key}_${index}`).addClass("crile_color")
									}
								}
							}
						})
					} else {
						$(`#yz_${yz_Model.RWID}`).html('')
						WRT_config.Bzrw[key].children[index].hymb = false
						$(`.crile_${key}_${index}`).removeClass("crile_blue")
						// $(`.crile_${yz_key}_${yz_index}`).addClass("crile_blue")
					}
				} else if (yz_Model.HQTJ == 'tj') {
					if (WRT_config.Bzrw[key].children[index].zytjsqdtx == true) {
						WRT_config.Bzrw[key].children[index].zytjsqdtx = false
						$(`.crile_${key}_${index}`).removeClass("crile_blue")
					} else {
						let bqid = WRT_config.url.BQID || WRT_config.lclj_init.BQID
						if (yz_Model.HQDM == "") {
							url = WRT_config.server + "/zyyz/zytjsqdyzmain.aspxav blid=" + WRT_config.url.al_blid + "&av_bqid=" + bqid + "&av_zkid=" + WRT_config.lclj_init.ZKID + "&av_zllx=11&tmpid=" + Math.random();
						} else {
							url = WRT_config.server + "/zyyz/zytjsqdyzmain.aspx?av_blid=" + WRT_config.url.al_blid + "&av_bqid=" + bqid + "&av_zkid=" + WRT_config.lclj_init.ZKID + "&av_zllx=11&tmpid=" + Math.random() + "&av_mbid=" + yz_Model.HQDM;
						}
						$("#if_zytjsqdtx").iziModal("destroy")
						WRT_e.ui.model({
							id: "if_zytjsqdtx",
							title: "特检医嘱",
							width: "1000px",
							iframeHeight: "700px",
							iframeURL: url,
							iframe: true,
						})
						WRT_config.Bzrw[key].children[index].zytjsqdtx = true
						$(`.crile_${key}_${index}`).addClass("crile_blue")
						$(`.crile_${key}_${index}`).addClass("crile_color")
					}
				} else {
					if (!WRT_config.Bzrw[key].children[index].selected) {
						WRT_config.Bzrw[key].children[index].selected = true
						$(`.crile_${key}_${index}`).addClass("crile_blue")
						$(`.crile_${key}_${index}`).addClass("crile_color")
					} else {
						WRT_config.Bzrw[key].children[index].selected = false
						$(`.crile_${key}_${index}`).removeClass("crile_blue")
					}
				}
				let ypmbDetail = new ypmbDetail_View();
				ypmbDetail.$el = $(`#YpmbDetail`);
				ypmbDetail.init({ data: [] }).render();
			} else {
				yznr_lists = WRT_config.Bzrw[key].children[index].yznr_lists || []
				list = WRT_config.Bzrw[key].children[index] || {}
				$(`.crile_${key}_${index}`).addClass("crile_color")
				WRT_e.api.lclj.GetYpmbDetail({
					params: {
						as_yfdm: WRT_config.lclj_init.YFDM,
						as_mbmc: list.HQDM,
						al_zkid: WRT_config.url.al_zkid,
						al_rwid: list.RWID
					},
					success(data) {
						if (data) {
							let arr = JSON.parse(data)
							let param = {}, lists = []
							arr.map(function (item) {
								if (param[item.ZH]) {
									param[item.ZH].push(item)
								} else {
									param[item.ZH] = [item]
								}
							})
							for (let i in param) {
								if (param[i].length > 1) {
									let fd = param[i].filter(ev => ev.ZTBZ == 0)
									if (fd && fd.length == 0) {
										lists.push({ children: param[i] })
									}
								} else {
									if (param[i][0].ZTBZ != 0) {
										lists.push(param[i][0])
									}
								}
							}
							lists.map(function (item) {
								if (item.children && item.children > 0) {
									let fd = item.children.filter((v) => yznr_lists.indexOf(v) > -1)
									if (fd && fd.length > 0) {
										item.check = true
									}
								} else {
									let fd = yznr_lists.filter(ev => ev.ID == item.ID)
									if (fd && fd.length) {
										item.check = true
									}
								}
							})
							YpmbDetail_copy = lists
							WRT_config.YpmbDetail = lists
							let ypmbDetail = new ypmbDetail_View();
							ypmbDetail.$el = $(`#YpmbDetail`);
							ypmbDetail.init({ data: WRT_config.YpmbDetail }).render();
						}
					}
				})
			}
		}
	} else {
		WRT_e.ui.message({
			title: '提示',
			content: "该病人已出径，不允许操作!"
		})
		// WRT_e.ui.hint({msg:'当前状态不可操作',type:'warning'})
	}
}

//切换标签页
function dbselect(index) {
	$(".load_model").addClass('load_img')
	if (td_index == index) {
		return
	}
	td_index = index
	let list = WRT_config.paLcljData[index]
	if (list) {
		left_init(list)
	}
	// let rtn=false,msg=false
	// let checkbox=$("input[name='rw_lists']:checked")
	// if(checkbox.length>0){
	//     msg=true
	// }

	// WRT_config.Bzrw.map(function(item){
	//     if(item.children){
	//         item.children.map(function(key){
	//             if(key.yznr_lists&&key.yznr_lists.length>0){
	//                 rtn=true
	//             }
	//         })
	//     }
	// })
	// if(msg||rtn){
	//     WRT_e.ui.message({
	//         title:'提示',
	//         content:"界面发生改变，请先保存!",
	//         onOk(){
	//             $(".load_model").addClass('load_img')
	//             if(td_index==index){
	//                 return
	//             }
	//             td_index=index
	//             let list=WRT_config.paLcljData[index]
	//             if(list){
	//                 left_init(list)
	//             }
	//         },
	//         onCancel(){
	//         }
	//     })
	//     return
	// }
}
//刷新
function left_init(target) {
	times = null;
	WRT_e.api.lclj.GetBzrwDetail({
		params: {
			al_blid: WRT_config.url.al_blid,
			al_bzid: target.BZID,
			al_ljid: WRT_config.lclj_init.LJID
		},
		success(data) {
			if (data) {
				WRT_config.BzrwDetailType = true
				WRT_config.BzrwDetail = JSON.parse(data)
				WRT_config.BzrwDetail.sort(function (a, b) {
					return a.WCSJ < b.WCSJ ? 1 : -1
				})
				WRT_config.BzrwDetail.map(function (item) {
					if ((item.LB == 1 || item.LB == 4) && !times) {
						times = gettime(item.ZCWCSJ)
					}
				})
				if (WRT_config.BzrwwType && WRT_config.BzrwDetailType) {
					app.init()
				}
			}
		}
	})
	WRT_e.api.lclj.GetBzrw({
		params: {
			al_blid: WRT_config.url.al_blid,
			al_bzid: target.BZID,
			al_ljid: WRT_config.lclj_init.LJID
		},
		success(data) {
			if (data) {
				WRT_config.BzrwwType = true
				let arr = JSON.parse(data)
				WRT_config.Bzrw = getData(arr)
				console.log(WRT_config.Bzrw)
				if (WRT_config.BzrwwType && WRT_config.BzrwDetailType) {
					app.init()
				}
			}
		}
	})
}

//处理医嘱内容数据
function getData(target) {
	let lists = {}
	target.map(function (item) {
		if (item.LB == 2 || item.LB == 3) {
			if (lists['yp']) {
				lists['yp'].push(item)
			} else {
				lists['yp'] = [item]
			}
		}
		if (item.RWID >= 10000) {
			if (lists['mbb']) {
				lists['mbb'].push(item)
			} else {
				lists['mbb'] = [item]
			}
		}
	})
	let arr = [{ title: "医嘱内容", type: 'yp', children: [] }, { title: "慢病包", type: 'mbb', children: [] }]
	for (let i in lists) {
		if (i == 'yp') {
			arr[0].children = [...lists[i]]
		} else if (i == 'mbb') {
			arr[1].children = [...lists[i]]
		}
	}
	return arr
}

//日期年月日
function gettime(val) {
	var d
	if (!val) {
		d = new Date();
	} else {
		d = new Date(val)
	}
	// var d = new Date(val);
	var year = d.getFullYear();
	var month = change(d.getMonth() + 1);
	var day = change(d.getDate());
	var hour = change(d.getHours());
	var minute = change(d.getMinutes());
	var second = change(d.getSeconds());
	function change(t) {
		if (t < 10) {
			return "0" + t;
		} else {
			return t;
		}
	}
	var time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
	return time
}

function extraTime(SJWCtime, ZCWCtime) {
	// (实际完成时间 > 最晚完成时间)
	//Date.parse() 解析一个日期时间字符串，并返回1970/1/1 午夜距离该日期时间的毫秒数
	var time1 = Date.parse(new Date(SJWCtime)); // 实际完成时间
	var time2 = Date.parse(new Date(ZCWCtime)); // 最晚完成时间
	let dateDiff = (time1 - time2) // 时间相差秒数
	let days = Math.floor(dateDiff / (24 * 3600 * 1000));

	// 计算出小时数
	let residue1 = dateDiff % (24 * 3600 * 1000); // 计算天数后剩余的毫秒数
	let hours = Math.floor(residue1 / (3600 * 1000));// 计算出小时数

	// 计算相差分钟数
	let residue2 = residue1 % (3600 * 1000); // 计算小时数后剩余的毫秒数
	let minutes = Math.floor(residue2 / (60 * 1000));// 计算相差分钟数

	// 计算相差秒数
	let residue3 = residue2 % (60 * 1000); // 计算分钟数后剩余的毫秒数
	let seconds = Math.round(residue3 / 1000);// 计算相差秒数
	let returnVal =
		((days == 0) ? "" : days + "天") +
		((hours == 0) ? "" : hours + "时") +
		((minutes == 0) ? "" : minutes + "分") +
		((seconds == 0) ? "" : seconds + "秒");

	return returnVal;
}
