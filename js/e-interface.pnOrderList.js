WRT_e.api = WRT_e.api || {}

WRT_e.api.pnOrderList = {
	//初始化（ll_blid）有效体重、血糖标准天、病人性别、药房代码<下面使用的yfdm>、姓名床号、是否出院
	getInitList: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
	getInit: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_Init',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
	// 得到奶类型 和医嘱频率（的名称和备注）
	getTypeOfMilk: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_getTypeOfMilk',
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_getTypeOfMilk',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
  // 得到药品  （as_yfdm、 ll_blid）药品信息和医保审批信息
	getNutrition: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_getNutrition',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
  // 根据医嘱单id得到相应药品(zdid\ll_blid)药品信息和医保审批信息
	getNutritionByYzdid: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_getNutritionByYzdid',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
  // 根据医嘱单id获取根据医嘱单信息(as_yzdid)
	getYZDByYzdid: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_getYZDByYzdid',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
  // 得到前一份医嘱单所填的数据（as_blid）
	getPreviousYZDByBlid: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_getPreviousYZDByBlid',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
  // 保存相关数据（as_ekpnyy、as_ekpnyp）
	SaveData: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_SaveData',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
  // 获取化验结果（as_zyh、as_blid）
	getHYJG: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_GetHYJG',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
  // 获取前一天8点到今天8点尿液量（as_blid,as_yzdid）
	getUrine: function(o = {}) {
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				// url: WRT_config.server + '/pnCalculator/pnOrderList.aspx/e_Init',
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_GetUrine',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
  // 提交医嘱（as_blid,as_yzdid,as_zxpl,as_zxts,asYzsj,as_spxx）
	SaveYPYZ: function(o = {}) {	
		o.params = o.params || {}
		if (WRT_config.mockType == '1') {
			if (o.success) o.success({
				Code: "1",
				CodeMsg: "获取数据成功！",
				Result: []
			})
		} else {
			$.ajax({
				url: WRT_config.server + '/pnCalculator/nbPNCalculator.aspx/e_SaveYPYZ',
				type: 'POST',
				data: JSON.stringify(o.params),
				dataType: "json",
				async: false,
				cache:false,
				crossDomain:true, //设置跨域为true
				xhrFields: {
						withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
				},
				contentType: "application/json; charset=utf-8",
				success: function (msg) {
						if (o.success) o.success(msg)
				},
				error: function (error) {
					if (error.statusText == 'error') {
						WRT_e.ui.hint({
							type: 'error',
							msg: "服务器连接失败"
						})
					}
				}
			})
		}
	},
}