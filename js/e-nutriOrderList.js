let url = window.location.href.split("?") || []//console.log
let text = url[1].split("&")
let params = {}
for (let i of text) {
  let fd = i.split("=")
  params[fd[0]] = fd[1]
}
//统一页面启动(新营养医嘱)
$(document).ready(() => {
  // 初始化
	WRT_e.api.nutriOrderList.getInit({
		params: {
			al_blid: params["as_blid"],
    },
		success(data) {
			// console.log('新营养医嘱数据初始化',data.d)
      WRT_config.getNOLInit = JSON.parse(data.d)
			// console.log('新营养医嘱数据初始化',WRT_config.getNOLInit)
      app.init()
			// if (data.Code == 1) {
			// 	WRT_config.init=data.Result
			// 	// console.log(WRT_config.init)
			// 	app.init()
			// }
		}
	})
})
var app = {
  init: function () {
    // 头部基础数据
    var BaseInfo = new BaseInfo_View();
    BaseInfo.$el = $(".nutriOrderListTitle");
    BaseInfo.init({
      //   data: WRT_config.BrMainLeft
      data: WRT_config.getNOLInit
    }).render();
    WRT_e.api.nutriOrderList.getNurtionOrderList({
      params: {
        al_blid: params["as_blid"],
      },
      success(data) {
        WRT_config.nutriOrderList = JSON.parse(data.d)
        console.log('新营养医嘱患者营养医嘱单列表',WRT_config.nutriOrderList)
        // 通过接口获取表内的基本参数有绘制表格，没有不展示
        var Tabels = new Tabel_View();
        Tabels.$el = $(".nol_newTable");
        Tabels.init({
          data: {
            as_data: WRT_config.nutriOrderList // 患者营养医嘱单列表
            // 需要传as_id进来
          }
        }).render();
      }
    })
		

  }
}

/********************公用方法********************/
// 点击新增打开新页面
function addNewNutritionOrder() {
  console.log('新增','是否出院判断是否存在病区出院时间',WRT_config.getNOLInit)
 
  // 已出院的病人不允许新增
  if(WRT_config.getNOLInit.BQCYSJ==null || WRT_config.getNOLInit.BQCYSJ==''){
    var tabelsIframe = new TabelIframe_View();
    tabelsIframe.init({
      data:{
        as_id: '0',
        blid: params["as_blid"]
      }
    }).render();
  } else {
    WRT_e.ui.hint({
      type: 'error',
      msg:'病人已经出院，当前状态下不允许新增!'
    })
  }
  
  // var tabelsIframe = new TabelIframe_View();
  // tabelsIframe.init({
  //   data:{
  //     as_id: '0',
  //     blid: params["as_blid"]
  //   }
  // }).render();
}
// 详情按钮
function openNuOrder(id,blid) {
  var tabelsIframe = new TabelIframe_View();
  tabelsIframe.init({
    data:{
      as_id: id,
      blid: blid
    }
  }).render();
  // var src = "nutritionOrder.aspx?as_id=" + id + "&as_blid=" + document.getElementById("hfd_blid").value + "&tempid=" + Math.random();
  // //window.open(src, "", "height=700px,width=750px,status=no,scrollbars=yes");
  // //window.location.reload(true);
  // $('#ss').window({
  // 		width: 900,
  // 		height: 700,
  // 		title: '营养医嘱单',
  // 		collapsible: false,
  // 		minimizable: false,
  // 		maximizable: false
  // });
  // window.$('#ss').html('<iframe width=100% height=100% src="' + src + '" frameborder="0" scrolling="yes">');
}
// 停止按钮
function stopNuorder(zh, yzdid, yhid) {
  if (zh == 0){
  	// alert("医嘱单未提交,无法暂停,如不需要,请至【长期药品医嘱】下删除!");
    WRT_e.ui.message({
      title: '提示',
      content: `医嘱单未提交,无法暂停,如不需要,请至【长期药品医嘱】下删除!`,
    })
  } else {
  	// StopNuOrder(zh, yzdid, yhid, stopNuOrder_callback);
    WRT_e.api.nutriOrderList.stopNurtionOrder({
      params: {
        // al_blid: params["as_blid"],
        al_zh: zh, 
        al_yzdid: yzdid, 
        al_ysyhid: yhid
      },
      success(data) {
        console.log(data)
        if(data.d == '1'){
          WRT_e.ui.hint({
            msg: `成功停止营养医嘱单`,
          })
        } else {
          stopNuOrder_callback(data)
        }
        // stopNuOrder_callback(data)
        // WRT_config.nutriOrderList = JSON.parse(data.d)
      }
    })
  }
}
// 停止按钮回掉函数
function stopNuOrder_callback(res) {
  if (res == null || res.d == null) {
    // alert("系统出现未知错误");
    WRT_e.ui.message({
      title: '提示',
      content: `系统出现未知错误`,
    })
    return;
  }
  if (res.d == "0"){
    // alert("暂停营养医嘱失败,请联系管理员!");
    WRT_e.ui.message({
      title: '提示',
      content: `暂停营养医嘱失败,请联系管理员!`,
    })
  } else {
      // alert("医嘱已暂停");
      WRT_e.ui.message({
        title: '提示',
        content: `医嘱已暂停`,
      })
      window.location.reload(true);
  }
}
// 打印按钮
function prtNuOrderList(id, blid) {
  var url ="e-nutritionOrderPrt.html?as_id=" + id + "&as_blid=" + blid + "&as_prt=1" 
  // // var url ="e-nutritionOrderPrt.html?as_yzdid=" + id + "&as_blid=" + blid + "/&as_prt=1" 
  // // var url = "nutriOrder.aspx?as_yzdid=" + id + "&as_blid=" + blid + "&as_prt=" + document.getElementById("hfd_prt").value;
  window.open(url, "print", "toolsba=no,width=750,height=800,menubar=no, scollbars=no,resizable=no,location=no,status=no");
}

// 时间比较
function timeCom(eTime){
  let nowTime = new Date()
  let endTime = new Date(eTime)
  if(endTime.getTime()-nowTime.getTime()<0){
    return '已到期'
  } else {
    return ''
  }
}
// 关闭窗口
function closeModalNow() {
  // console.log('closs234567876543')
  $('#tabelIframe').iziModal('destroy')
  app.init()
}
/********************视图********************/
// 顶部数据
var BaseInfo_View = WRT_e.view.extend({
  render: function () {
    // <span class="tBinfoBold" style="padding-left: 12px;min-width: 125px;max-width: 355px;">
    //   身高： <span>170</span> CM
    // </span>
    // <span class="tBinfoBold" style="min-width: 125px; max-width: 355px;">
    //   体重： <span>60</span> KG
    // </span>
    // <div class="nolTBtipL">※病人身高体重信息不全，请您最好先在住院病历二中将病人身高，体重信息补充完整</div>
    // 初始数据this.data.ZYID
    let html = `
			<div class="nolTBinfoR">
				<span style="color:#489CFF;padding-right:20px;font-weight: bolder;">●</span>
				<span class="tBinfoBold">${this.data.BAH}</span>
				<span class="tBinfoBold" ${this.data.BRXM.length>10?`style='min-width:200px'`:''}>${this.data.BRXM}</span>
				<span>${this.data.BRXB=='1'?`男`:`女`}</span>
				<span style="min-width:50px;max-width:90px">${this.data.NL}岁</span>
				<span class="tBinfoBcNum" style="padding: 1px 8px;min-width:64px;max-width:100px">${this.data.BQID}-${this.data.CWH}</span>
			</div>
    `
    this.$el.html(html)
    return this;
  }
})

// 新营养医嘱页面展示表格
var Tabel_View = WRT_e.view.extend({
  render: function () {
    // 初始数据
    // console.log(this.data.as_data)
    let initialData = []
    this.data.as_data.map((item, index)=>{
      initialData.push(item)
    })
    // let initialData = [
		// 	{
		// 		'开始时间':'2023/2/23 15:27:55',
		// 		'持续天数':'2',
		// 		'结束时间':'2023/2/25 15:27:55',
		// 		'开单医师':'王华晓',
		// 		'停止医师':'',
		// 		'医嘱状态':'已到期',
		// 	}
    // ]
    // 行数
    var comlum = $("#table_internal tr").length
		// bgcolor="#A3BEFF" #a3bef080 align="center"   obj.YZYSYHID 
    let html = `
    ${initialData.length>0?`
      <tr align="start" bgcolor="#A3BEFF" class="table_header">
        <td width="210px" class="table-title"><span>开始时间</span></td>
        <td width="160px" class="table-title"><span>持续天数</span></td>
        <td width="210px" class="table-title"><span>结束时间</span></td>
        <td width="160px" class="table-title"><span>开单医师</span></td>
        <td width="160px" class="table-title"><span>停止医师</span></td>
        <td width="160px" class="table-title"><span>医嘱状态</span></td>
        <td width="400px" class="table-title"><span>医嘱状态</span></td>
      </tr>
      ${_.map(initialData, (obj,index)=>`
        <tr align="start"  id="table_data" class="table_data`+index+`">
          <td class="table-nr">${(obj.KSSJ).replace('T',' ')}</td>
          <td class="table-nr">${obj.CXTS}</td>
          <td class="table-nr">${(obj.JSSJ).replace('T',' ')}</td>
          <td class="table-nr">${obj.KDYS}</td>
          <td class="table-nr">${obj.TZYS !=null? obj.TZYS:``}</td>
          <td class="table-nr">${obj.ZTBZ ==0?`停止`:obj.ZTBZ ==1 && timeCom(obj.JSSJ)==''?`正常`:obj.ZTBZ ==2 && timeCom(obj.JSSJ)==''?`已提交`:timeCom(obj.JSSJ)}</td>
          <td class="table-nr">
            <a class="detailed" herf="javascript:void(0)" onclick="openNuOrder(${obj.ID},${obj.BLID})">  详细 </a>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <a class="cease" herf="javascript:void(0)" onclick="stopNuorder(${obj.ZH},${obj.ID},${WRT_config.getNOLInit.CURRENTYHID})">  停止 </a>
            &nbsp;&nbsp;&nbsp;&nbsp;
            <a class="print" herf="javascript:void(0)" onclick="prtNuOrderList(${obj.ID},${obj.BLID})">  打印 </a>
          </td>
        </tr>
      `)}
    `:``}
    `
    this.$el.html(html)
    return this;
  }
})
// 新增按钮展示窗口（iframe）
var TabelIframe_View = WRT_e.view.extend({
  render: function () {
    // console.log(this.data)
    // 需要参数：as_id=0   //  新增保存id会变化 as_blid=1492940  tempid=0.3241901365004203 
    // (新增)e-nutritionOrder.html?as_id=0&as_bd=14929408tempid=0 42697241460810355
    //（数据）e-nutritionOrder.html?as_id=5405&as_blid=1492940&tempid= + Math.random())
    //   <iframe id="tableFr_List" src=""></iframe>
    let html = `
      <iframe id="tableFr_List" src="e-nutritionOrder.html?as_id=${this.data.as_id}&as_blid=${this.data.blid}&tempid=${Math.random()}"></iframe>
    `
    WRT_e.ui.model({
      id: "tabelIframe",
      title: '营养医嘱单',
      width: "915px",
      height:"900px",
      content: html,
      // closeButton:false,
      // closeOnEscape:false,
      iframe: false,
    })
    this.$el.html(html)
    return this;
  }
})