WRT_e.api = WRT_e.api || {}
// 病例知情汇总
WRT_e.api.bzqjllistall = {
  // 1.专科知情记录列表，新增记录列表e_init
  getZqjlByList: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/zqjllistall.aspx/e_init',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 2.根据文书类型获取病人已书写文书列表e_GetBrWssByLx
  getZqjlBrWssByLx: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/zqjllistall.aspx/e_GetBrWssByLx',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 3.更新知情记录列表，新增记录列表e_UpdateZqjlHtml'
  getUpdateZqjlHtml: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/zqjllistall.aspx/e_UpdateZqjlHtml',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // (补充接口)
  // 1.知情记录列表，搜索 e_GetZqjlByKey
  getZqjlByKey: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/zqjllistall.aspx/e_GetZqjlByKey',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 2.知情记录点击编辑e_GetWsUrl
  getWsUrl: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/zqjllistall.aspx/e_GetWsUrl',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },

  
  
  
}