WRT_e.api = WRT_e.api || {}

WRT_e.api.zkgnwh = {
  //初始化
  getinit: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    }else{
      $.ajax({
        url: WRT_config.server2+'/medicaladvice/v1/department/initBuMenSX',
        type: 'get',
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        data: '',
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if(o.success) o.success(msg)
        },
        error:function(errpr){
        }
      })
    }
  },
  //根据部门ID查询所有部门属性
  getSearch: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    }else{
      $.ajax({
        url: WRT_config.server2+'/medicaladvice/v1/department/getBuMenSXListByBMID?buMenID='+o.params.buMenID,
        type: 'get',
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        data: '',
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error:function(error){
        }
      })
    }
  },
  //新增
  addBuMenSXList: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    }else{
      $.ajax({
        url: WRT_config.server2+'/medicaladvice/v1/department/addBuMenSXList',
        type: 'post',
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        data: JSON.stringify([o.params]),
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error:function(error){
        }
      })
    }
  },
  //保存
  save: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    }else{
      $.ajax({
        url: WRT_config.server2+'/medicaladvice/v1/department/updateBuMenSXList',
        type: 'post',
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        data: JSON.stringify([o.params]),
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error:function(error){
        }
      })
    }
  },
}