//测试
var sel_bq = null //选择病区
var sel_zlz = null //选择治疗组
var istab = null//关闭关闭tab
var sel_br=1//按钮
var gxhnr='[]'// 个性化审批通知内容
var clb_index=null//当前传染病
var jb_index //当前传染病
//统一页面启动
$(document).ready(() => {
  let url = window.location.href.split("?") || []
  if(url&&url.length>1){
    let text = url[1].split("&")
    let params = {}
    for (let i of text) {
      let fd = i.split("=")
      if(fd[0]=='auth_token'){
        sessionStorage.setItem('token',fd[1])
      }
      params[fd[0]] = fd[1]
    }
    // return
  }
  WRT_e.api.au.loginToken({
    success(msg){
      if(msg.data&&msg.data.yongHuID){
        WRT_e.api.au.getTokeninfo({
          params:{
            al_yhid:msg.data.yongHuID
          },
          success(data){
            if(data.Code==1){
              let list=JSON.parse(data.Result)
              sessionStorage.setItem("username",list.EHRUser.YHXM)
              sessionStorage.setItem("userid",msg.data.yongHuID)
              sessionStorage.setItem("zkid",list.EHRUser.ZKID)
              sessionStorage.setItem("ipAddress",list.ipAddress)
              let urls = window.location.href;
              if (urls.indexOf("?") != -1) {
                urls = urls.replace(/(\?|#)[^'"]*/, '');
                window.history.pushState({}, 0, urls);
              }
              reset_init()
            }
          }
        })
      }else if(msg.responseText){
        let ev = JSON.parse(msg.responseText)
        WRT_e.ui.message({
          title:"提示",
          content:ev.errorMessage||"cookie失效，请重新登录",
          onOk(){
            sessionStorage.clear()
            let url="http://wsyy.Wzhospital.cn/web/auth/#/logout?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
            window.location.href=url
          }
        })
        // WRT_e.ui.hint({mes:ev.errorMessage,type:'error'})
        // setTimeout(function(){
        //   let url="http://wsyy.Wzhospital.cn/web/auth/#/logout?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
        //   window.location.href=url
        // },1000)
      }
    }
  })
})
function reset_init(){
  let ip = sessionStorage.getItem("ipAddress")
  if (ip) {
    var height = window.screen.height;
    var width = window.screen.width;
    $("#ip_host").css("display", "block")
    $("#ip_host").html(ip + '(' + width + '*' + height + ')')
  } else {
    $("#ip_host").css("display", "none")
  }
  WRT_e.api.au.getQRinit({
    params:{},
    success(data){
      if(data.Code==1){
        WRT_config.url=data.Result
      }
    }
  })
  
  //获取主页内页数据
  WRT_e.api.ehrSz.getInit({
    success(data) {
      let obj = JSON.parse(data.Result)
      if (data.CodeMsg == 1) {
        //用户信息
        obj.KCLZK.map(function(item){
          if(item.TXT.indexOf("  —  ")>=0){
            let arr=item.TXT.split("  —  ")
            item.TXT=arr[1]
          }
        })
        WRT_config.ehrSz_init = obj
        sessionStorage.setItem('yhxm',WRT_config.ehrSz_init.YHXX.YHXM)
        if(obj.showMessage_Switch==1){
          let iWidth = 700;
          let iHeight = 600;
          let iTop = (window.screen.availHeight - 30 - iHeight) / 2;
          let iLeft = (window.screen.availWidth - 10 - iWidth) / 2;
          let ls_url ='./e-showMgs.html'
          window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
        }

        sessionStorage.setItem("zkid",WRT_config.ehrSz_init.YHXX.ZKID)
        if(WRT_config.ehrSz_init.YHXX.ZKID==2961||WRT_config.ehrSz_init.YHXX.ZKID==49){
          $(".sjz_title").removeClass("none")
        }else{
          $(".sjz_title").addClass("none")
        }
        ////处方结果用法用量列表
        WRT_e.api.ehrSz.getYjkYfylxgsqZY({
          params:{
            yiShiID:'',
            zhuanKeID:WRT_config.ehrSz_init.YHXX.ZKID//42//WRT_config.ehrSz_init.YHXX.ZKID
          },
          success(data){
            if(data.data&&data.data.length>0){
              WRT_config.cfjg_lists=data.data||[]
              $("#cfxg_val").html(data.data.length)
            }
          }
        })
        if (WRT_config.AllZkxx && WRT_config.ehrSz_init) {
          //初始化
          app.init()
        }
      }
    }
  })
  //获取获取全部专科信息或当前用户权限范围内的专科信息
  WRT_e.api.ehrSz.GetAllZkxx({
    params: {},
    success(data) {
      if (data.Code == 1) {
        let lists = JSON.parse(data.Result)
        WRT_config.AllZkxx = lists.ZKXX || []
        if (WRT_config.AllZkxx && WRT_config.ehrSz_init) {
          //初始化
          app.init()
        }
      }
    }
  })
  WRT_e.api.ehrSz.info_cookie({
    success(data) {
      if (data.Code == 1) { } else {
        WRT_e.ui.message({
          title: '信息窗口',
          content: 'cookie已失效，请重新登陆',
          onOk() {
            sessionStorage.clear()
            // onout()
            let url="http://wsyy.Wzhospital.cn/web/auth/#/logout?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
            window.location.href=url
            // window.location.href = 'e-login.html'
          },
        })
      }
    },
    error(){
      WRT_e.ui.message({
        title: '信息窗口',
        content: 'cookie已失效，请重新登陆',
        onOk() {
          sessionStorage.clear()
          // onout()
          let url="http://wsyy.Wzhospital.cn/web/auth/#/logout?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
          window.location.href=url
          // window.location.href = 'e-login.html'
        },
      })
    }
  })
}
//
var app = {
  init: function () {
    /********************操作事件********************/
    //选项卡点击删除按钮
    $('#page').on('click', '.nav>li>.close', (e) => {
      page_iframe.del($(e.target).prev().attr("href").substr(1))
    })
    $("#sfjg_val").click(e => {
      page_iframe.add(`if_sfjg_val`, "审方结果", WRT_config.server + `/yz_sz/hlyy/sfxt.aspx?version=djm&r=${Math.random()}`)
    })
    let sq=sessionStorage.getItem("sp_model")
    if(!sq){
      //获取所有消息提示
      getallmessage()
    }
    /********************初始化获取页面数据********************/
    let menu_lists=sessionStorage.getItem("MeunList")
    if(!WRT_config.MeunList){
      WRT_config.MeunList=JSON.parse(menu_lists)
    }
    if(!WRT_config.MeunList){
      WRT_e.api.au.getMeunList({
        params:{yingYongDM:'020',caiDanDMList:[],caiDanCS:1},
        caiDanDMList:[],
        success(data){
          if(data.data){
            // console.log('菜单',data.data);
            WRT_config.MeunList=data.data
            sessionStorage.setItem("MeunList",JSON.stringify(data.data))
            $("#menu").html(
              new Menus_View().init({
                data: WRT_config.ehrSz_init
              }).render().$el
            )
            $('.dropdown-toggle').dropdownHover();
          }else if(msg.data.hasError<0||msg.data.errorMessage){
            WRT_e.ui.hint({mes:msg.data.errorMessage,type:'error'})
          }
        }
      })
    }
    //头部用户信息
    $("#login_info").html(
      new loginInfo_View().init({
        data: WRT_config.ehrSz_init.YHXX
      }).render().$el
    )
    function reset_YHXX(){
      WRT_e.api.ehrSz.getyhxx({
        params:{},
        success(data){
          if(data.Result){
            let list = JSON.parse(data.Result)
            if (list.YHXX.YSYHID != WRT_config.ehrSz_init.YHXX.YSYHID) {
              // window.location.reload()
              WRT_e.ui.hint({ msg:'登录账户'+list.YHXX.YHXM+'与当前使用账户'+ WRT_config.ehrSz_init.YHXX.YHXM+'不一致，请按F5刷新页面或重启浏览器。'})
            }
            // WRT_config.ehrSz_init.YHXX = list.YHXX
            // $("#login_info").html(
            //   new loginInfo_View().init({
            //     data: WRT_config.ehrSz_init.YHXX
            //   }).render().$el
            // )
          }
        }
      })
    }
    // reset_YHXX()
    if(!WRT_config.hmzkZW){
      setInterval(() => reset_YHXX(), 1000*10);
    }
    //设置菜单
    $("#menu").html(
      new Menus_View().init({
        data: WRT_config.ehrSz_init
      }).render().$el
    )
    //设置过滤病人表单
    let brListFilter = new brListFilter_View().init({
      data: WRT_config.ehrSz_init
    }).render().$el
    $("#patient_filter").html(brListFilter)
    //设置点击事件点击个性化记录的表单
    $(`#patient_filter .nav>li>a[href="#GetPatientList_${WRT_config.ehrSz_init.GXH ? WRT_config.ehrSz_init.GXH.GXHNR : 1}"]`).click()
    sel_br=`${WRT_config.ehrSz_init.GXH ? WRT_config.ehrSz_init.GXH.GXHNR : 1}`
    //获取病人日志数据
    WRT_e.api.ehrTx.GetDataSimple({
      params: {
        al_glid: 0,
        as_lb: 0
      },
      success(data) {
        if (data.Code == 1) {
          let arr=JSON.parse(data.Result)
          WRT_config.DataSimple=arr
          WRT_config.UnfilledInfo=[]
            $("#patient_info").html(
              new brPortal_View().init({
                data: {
                  ...arr.e_GetPortal,
                  ...{
                    GetBrcyblbg:arr.e_GetBrcyblbg
                  },
                  ...{
                    WGDBL:arr.e_GetUndocumentedPatInfo
                  },
                  ...{
                    GetUnfilledInfo:arr.e_GetUnfilledInfo
                  },
                }
              }).render().$el
            )
        }
      }
    })
    //滚动公告
    $("#e_notice").html(
      new notice_View().init({
        data: WRT_config.ehrSz_init.GGXX
      }).render().$el
    )

    //启动hover
    $('.dropdown-toggle').dropdownHover();
    
    //标签页右键事件
    var righttab=new BootstrapMenu('#page .page_nav_header li', {
      fetchElementData: function (e) {
        return e
      },
      actions:[{
        name: '关闭标签',
        onClick(e){
          closeTab(e,'close')
        }
      },{
        name: '关闭右侧标签',
        onClick(e){
          closeTab(e,'right')
        }
      },{
        name: '关闭其他标签',
        onClick(e){
          closeTab(e,'other')
        }
      },{
        name: '关闭左侧标签',
        onClick(e){
          closeTab(e,'left')
        }
      }]
    })
    $("#page").on('contextmenu','.nav li',function(e){
      if(e.currentTarget.innerText=='病人一览表'){
        return
      }
      let arr=$(`.tab-content iframe`)
      for(let i in arr){
        if(arr[i]&&arr[i].contentDocument){
          arr[i].contentWindow.listeriframe()
          arr[i].contentDocument.addEventListener("click", dothis, false);
        }
      }
    })
    function closeTab(e,type){
      let rtn=false
      let arr=$(e)[0].parentNode.children
      switch(type){
        case 'close':
          if(e[0]&&e[0].outerText=='病人一览表'){
            rtn=true
          }
          if(rtn){
            WRT_e.ui.hint({msg:'病人一览表不允许关闭'})
            return
          }else{
            let bl_id=$(e).find('a').attr("href").substr(1)
            page_iframe.del(bl_id)
          }
          break;
        case 'right':
          for(let i=0;i<arr.length;i++){
            if(i>arr.length){
              return
            }
            if(rtn){
              let id=arr[i].firstChild.hash.substr(1)
              page_iframe.del(id)
              i--
            }
            if(arr[i].textContent==e[0].textContent){
              rtn=true
            }
          }
          break;
        case 'other':
          for(let i=0;i<arr.length;i++){
            if(i>arr.length){
              return
            }
            if(arr[i].textContent==e[0].textContent){

            }else if(arr[i].textContent!='病人一览表'){
              let id=arr[i].firstChild.hash.substr(1)
              page_iframe.del(id)
              i--
            }
          }
          break;
        case 'left':
          for(let i=0;i<arr.length;i++){
            if(i>arr.length){
              return
            }
            if(arr[i].textContent==e[0].textContent){
              rtn=true
            }
            if(!rtn&&arr[i].textContent!='病人一览表'){
              let id=arr[i].firstChild.hash.substr(1)
              page_iframe.del(id)
              i--
            }
          }
          break;
      }
    }
    //一览表右键菜单
    var patientContextmenu = new BootstrapMenu('#patient_list .item_box', {
      fetchElementData: function (e) {
        return e
      },
      actions: [{
        name: '检验医嘱',
        onClick: function (e) {
          $("#检验医嘱").iziModal('destroy')
          WRT_e.ui.model({
            id: '检验医嘱',
            title: '检验医嘱',
            width: 1200,
            iframeHeight: 800,
            iframe: true,
            iframeURL:`e-bqxz.html?ztbz=nhyd&as_blid=${e.data('blid')}&as_zyid=${e.data('zyid')}&as_zlzid=${e.data('zlzid')}&as_yszkid=${e.data('zkid')}&al_yhid=${WRT_config.ehrSz_init.YHXX.YSYHID}&ys_bmid=${WRT_config.ehrSz_init.ys_bmid}&as_bqid=${e.data('bqid')}&as_tmpid=${Math.random()}`
          })
        }
      }, {
        name: '检查医嘱',
        onClick: function (e) {
          $("#检查医嘱").iziModal('destroy')
          let url = WRT_config.server + `/bqxz.aspx?ztbz=nsqd&as_blid=${e.data('blid')}&as_zyid=${e.data('zyid')}&as_brbqdm=${e.data('bqdm')}&as_yszkid=${e.data('zkid')}`
          WRT_e.ui.model({
            id: '检查医嘱',
            title: '检查医嘱',
            width: 1000,
            iframeHeight: 800,
            iframe: true,
            iframeURL: url
          })
        }
      }, {
        name: '院感监控',
        onClick: function (e) {
          let iWidth = 1000;
          let iHeight = 700;
          let iTop = (window.screen.availHeight - 30 - iHeight) / 2;
          let iLeft = (window.screen.availWidth - 10 - iWidth) / 2;;
          let ls_url =`http://172.16.200.88/nis/cdc?userid=${WRT_config.ehrSz_init.YHXX.YSYHID}&patientid=${e.data('zyh')}`
          window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
        }
      },{
        name: '批量预出院',
        onClick: function (e) {
          if(!WRT_config.ycypoplist){
            WRT_e.api.ehrSz.getPatientList({
              params: {
                as_lb: 3,
                as_lbid: '903457',//WRT_config.ehrSz_init.ZLZ[0].ID
                as_new: '0',
                as_pathpatient: '0'
              },
              success(data) {
                if(data.Code==1){
                  // console.log(JSON.parse(data.Result))
                  WRT_config.ycypoplist=JSON.parse(data.Result)['BRXX']
                }
                ycypopHtml()
              }
            })
          }else{
            ycypopHtml()
          }
        }
      }, {
        name: '备注：无',
        onClick: function (e) {
          let blid = e[0].dataset.blid
          let remark = patientContextmenu.$menu.find('li[data-action="4"]>a')
          let fd = remark[0].text || ""
          let val = fd.split("备注：")[1]
          if (!val || val == "无") {
            val = ""
          }
          let temp = `<div>
              备注：<input id="remark_title" type="text" value="${val}" autocomplete="off" style="border: 1px solid;" />
              <button class="e_btn" onclick="onSave(${blid})">保存</button>
              <button class="e_btn" onclick="$('#if_remark').iziModal('destroy')">取消</button>
            </div>
          `
          WRT_e.ui.model({
            id: "if_remark",
            title: "修改备注",
            iframe: false,
            content: temp
          })
        }
      },{
        name: '排床',
        onClick: function (e) {
          let zydjid = e[0].dataset.zydjid
          if(zydjid&&zydjid!='null'){
            let bqid = e[0].dataset.bqid
            let url=WRT_config.server+`/zydj/pc.aspx?zydjid=${zydjid}&bqid=${bqid}&tempid=${Math.random()}&as_openmode=ehr3`
            WRT_e.ui.model({
              id: 'if_zydj',
              title: '排床',
              width: 1250,
              iframeHeight: 700,
              iframe: true,
              iframeURL:url
            })
          }else{
            WRT_e.ui.hint({ msg:'无需排床！'})
          }
        }
      }]
    });
    $('#patient_list').on('contextmenu', '.item_box', function (e) {
      let remark = patientContextmenu.$menu.find('li[data-action="4"]>a')
      remark.text('备注：加载中')
      //获取备注
      WRT_e.api.ehrSz.getBz({
        params: {
          al_blid: $(e.currentTarget).data('blid')
        },
        success(data) {
          if (data.Code == 1) {
            remark.text(`备注：${data.Result || '无'}`)
          }
        }
      })
    });
    $('#patient_list').on('contextmenu', '.item_box', function (e) {
      let remark = patientContextmenu.$menu.find('li[data-action="5"]>a')
      $("#if_zydj").iziModal("destroy")
      if($(e.currentTarget)&&($(e.currentTarget).data('zydjid')=='null'||$(e.currentTarget).data('zydjid')==null)){
        remark.text('')
      }
    });

    
    /********************判断接口********************/
    //新增住院医技传染病异常值上报流程
    getclbLists()

    //大数据预测
    WRT_e.api.ehrSz.getDSJYC({
      params: {
        al_zkid: parseInt($('#al_zkid option:selected').val())
      },
      success(data) {
        if (data.Code == 1 && data.Result != 0) {
          let res = JSON.parse(data.Result)
          WRT_config.DSJYC = res
          $("#DSJYC_title").css("display", "block")
          // WRT_e.ui.message({
          //   title: '信息窗口',
          //   content: `本病区本周有病人AKI、CKD发生概率超过60%？`,
          //   onOk() {
          //     // page_iframe.add("大数据预测", WRT_config.DSJYC.URL);
          //   },
          //   onCancel() {},
          // })
        }
      }
    })
    //qc
    WRT_e.api.ehrSz.GetZkfkxx({
      params: {
        al_zkid: parseInt($('#al_zkid option:selected').val())
      },
      success(data) {
        if (data.Code == 1 && data.Result > 0) {
          WRT_e.ui.model({
            id: '专科质控反馈报表查询',
            title: '专科质控反馈报表查询',
            width: 1250,
            iframeHeight: 700,
            iframe: true,
            iframeURL:`${WRT_config.server}/qc/qc_fkxx.aspx?as_tmpid=${Math.random()}`
          })
        }
      }
    })
    
    //得到接收的反馈信息数量
    function GetFKCount(isShow) {
      WRT_e.api.ehrSz.GetFKCount({
        params: {},
        success(data) {
          if (data && data.Message) {
            var num = data.Message.split(",");
            let sum = parseInt(num[0])+ parseInt(num[1])+ parseInt(num[2])
            // $("#allCount_span").text("终末质控病历反馈:" + sum + " 份");// $("#red_span").text("危急:" + num[0]);
            // $("#yellow_span").text("紧急:" + num[1]);
            // $("#green_span").text("一般:" + num[2]);
            if (isShow != null && isShow != undefined) {
              var redNum = parseInt(num[0]);
              if (redNum > 0) {
                WRT_e.ui.message({
                  title: '提示',
                  content: "你有" + redNum + "条紧急信息，请点击右上角红色方块,进行回复!",
                  onOk() { },
                  onCancel() { },
                })
              }
            }
          }
        }
      })
    }
    if(!sq){
      setInterval(GetFKCount, 300000);//5分钟
    }
    getWjzMessage()
    //危急值处理弹窗控制
    function getWjzMessage() {
      isCheckLogin()
      WRT_e.api.ehrSz.getWjzMessage({
        params: {
          ll_yhid: WRT_config.ehrSz_init.YHXX.YSYHID,
        },
        success(data) {
          if (data.Code == 1 && data.Result) {
            let obj = JSON.parse(data.Result)
            let url = WRT_config.server + '/' + obj.URL
            if(url.indexOf('?')>=0){
              url=url+'&as openmode=ehr3'
            }else{
              url=url+'?as openmode=ehr3'
            }
            if(WRT_config.ehrSz_init.YHXX.ZKID==49||WRT_config.ehrSz_init.YHXX.YHZKMC=='急诊抢救'){
              WRT_e.ui.model({
                id: "if_WjzMessage",
                title: obj.TITLE,
                width: "900px",
                iframeURL: url,
                iframe: true,
                iframeHeight: "700px"
              })
            }else{
              WRT_e.ui.model({
                id: "if_WjzMessage",
                title: obj.TITLE,
                width: "900px",
                iframeURL: url,
                iframe: true,
                iframeHeight: "700px",
                // closeButton: false,
                // closeOnEscape: false,
              })
            }
          }
        }
      })
    }
    setInterval(() =>getWjzMessage(), 300000);//4分钟 240000 --》改为5分钟 300000
    function isCheckLogin(){
      WRT_e.api.ehrSz.CheckeLogin({
        params: {},
        success(data) {
          if (data.Code == 1&& data.Result==0) {
            WRT_e.ui.message({
              title: '信息窗口',
              content: 'cookie已失效，请重新登陆',
              onOk() {
                sessionStorage.clear()
                // onout()
                let url="http://wsyy.Wzhospital.cn/web/auth/#/logout?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
                window.location.href=url
                // window.location.href = 'e-login.html'
              },
            })
          }
        },error(){
          WRT_e.ui.message({
            title: '信息窗口',
            content: 'cookie已失效，请重新登陆',
            onOk() {
              sessionStorage.clear()
              // onout()
              let url="http://wsyy.Wzhospital.cn/web/auth/#/logout?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
              window.location.href=url
              // window.location.href = 'e-login.html'
            },
          })
        }
      })
    }
    function CheckSjzyz() {
      WRT_e.api.ehrSz.CheckSjzyz({
        params: {},
        success(data) {
          if (data.Code == 1) {
            // data.Result=3
            $("#sjz_val").html(data.Result)
            if (data.Result == 0) {
              $(".sjz_title").removeClass("red")
            } else if (data.Result>0) {
              $(".sjz_title").addClass("red")
              // WRT_e.ui.message({
              //   title: '信息窗口',
              //   content: '当前治疗组有患者需要开具时间针医嘱，是否立即查看？',
              //   onOk() { 
              //     page_iframe.add(`CheckSjzyz`, "时间针医嘱", WRT_config.server + `/yz_sz/tj/sjzyz.aspx`)
              //   },
              //   onCancel() { },
              // })
            }
          }
        }
      })
    }
    CheckSjzyz()
    setInterval(() => CheckSjzyz(), 1000*60*5);//5分钟

    //院感监控预警列表
    function grjkpopinit() {
      WRT_e.api.ehrSz.grjkpopinit({
        params: {
          ll_zkid: WRT_config.ehrSz_init.YHXX.ZKID,
        },
        success(data) {
          if (data.Code == 1 && data.Result) {
            let arr = JSON.parse(data.Result)
            WRT_config.grjkpoplis = arr.DATA
            if(WRT_config.grjkpoplis.length>0){
              $("#grjkpop_title").css("display", "block")
              grjkpopHtml()
            }
          }
        }
      })
    }
    grjkpopinit()

    //母乳喂养情况预警提醒
    function  WeiYangYJinit() {
      WRT_e.api.ehrSz.getWeiYangYJ({
        params: {
          zhuanKeID: WRT_config.ehrSz_init.YHXX.ZKID,
        },
        success(res) {
          if (res.data && res.data.length>0) {
            WRT_config.WeiYangYJlist=res.data
            let temp = `
              <div style="color: red;text-align: center;font-size: 16px;font-weight: 600;">母乳喂养情况预警列表</div>
              <table id="tbgrjkpop" cellspacing="5" cellpadding="10" border="0" class="btable"
                <tr>
                  <td width="90">病案号</td>
                  <td width="80">病区</td>
                  <td width="40">床位</td>
                  <td width="100">姓名</td>
                  <td width="140">记录时间</td>
                  <td width="150">异常喂养情况</td>
                </tr>
                ${_.map(WRT_config.WeiYangYJlist, (obj, index) => `
                <tr>
                  <td width="80">${obj.bingAnHao}</td>
                  <td width="80">${obj.bingQuMC}</td>
                  <td width="40">${obj.chuangWeiHao||''}</td>
                  <td width="80">${obj.xingMing}</td>
                  <td width="140">${obj.chuLiSJ}</td>
                  <td width="150">${obj.weiYangQK}</td>
                </tr>
                `).join("")}
              </table>`
              WRT_e.ui.model({
                id: "if_WeiYangYJ",
                title: "预警",
                width: "650px",
                content: temp,
                iframe: false,
              })
          }
        }
      })
    }
    WeiYangYJinit()
    
    //预出院列表
    // function ycypopinit() {
    //   WRT_e.api.ehrSz.ycypopinit({
    //     params: {
    //       ll_zkid: WRT_config.ehrSz_init.YHXX.ZKID,
    //     },
    //     success(data) {
    //       if (data.Code == 1 && data.Result) {
    //         let arr = JSON.parse(data.Result)
    //         WRT_config.ycypoplist = arr.DATA
    //         if(WRT_config.ycypoplist.length>0){
    //           $("#ycypop_title").css("display", "block")
    //           ycypopHtml()
    //         }
    //       }
    //     }
    //   })
    // }
    // ycypopinit()

    //得到审方结果数量
    function GetSfxtShjgCount() {
      WRT_e.api.ehrSz.GetSfxtShjgCount({
        params: {},
        success(data) {
          if (data && data.Code == 1) {
            let res = JSON.parse(data.Result)
            $("#sfjg_val").html(res.btgSfjgCount)
            // if(res.btgSfjgCount>0){
            //   WRT_e.ui.message({
            //     title: '信息窗口',
            //     content: `您有 ${res.btgBrCount} 个病人的 ${res.btgSfjgCount} 条审方结果待处理，是否立即查看？`,
            //     onOk() {
            //       page_iframe.add(`if_sfjg_val`,"审方结果",WRT_config.server+`/yz_sz/hlyy/sfxt.aspx?version=djm&r=${Math.random()}`)
            //       // $("#sfjg_val").html(res.btgSfjgCount)
            //     },
            //     onCancel(){}
            //   })
            // }
          }
        }
      })
    }
    GetSfxtShjgCount()

    //新生儿电子病历提醒
    function getxsrdata(){
      WRT_e.api.ehrSz.getXinShengErTx({
        params: {},
        success(res) {
          if(res.hasError==0 && res.data!=null && (res.data.kaJieMiaoTXBRs.length>0 || res.data.zuDiXieTXBRs.length>0)) {
            WRT_config.XinShengErTx=res.data
            let temp = `<div style="width:100%;overflow: auto;padding-bottom: 20px;">`
            if(res.data.kaJieMiaoTXBRs.length>0) {
              temp+=`<div>以下患者需开具卡介苗医嘱：</div>
              <table id="ysr_table" border="0" class="btable">
                <tr>
                  <td width="100">床位号</td>
                  <td width="120">姓名</td>
                </tr>
                ${_.map(res.data.kaJieMiaoTXBRs, (obj, index) => `
                <tr onclick="onselectxse("kaJieMiaoTXBRs",${index})">
                  <td width="100">${obj.chuangWeiHao}床</td>
                  <td width="120">${obj.bingRenXM}</td>
                </tr>
                `).join("")}
              </table>`
            }
        
            if(res.data.zuDiXieTXBRs.length>0) {
              temp+=`<div style="padding-top: 10px;">以下患者需开具足底血医嘱：</div>
              <table id="ysr_table" border="0" class="btable">
                <tr>
                  <td width="100">床位号</td>
                  <td width="120">姓名</td>
                </tr>

                ${_.map(res.data.zuDiXieTXBRs, (obj, index) => `
                <tr onclick="onselectxse("zuDiXieTXBRs",${index})">
                  <td width="100">${obj.chuangWeiHao}床</td>
                  <td width="120">${obj.bingRenXM}</td>
                </tr>
                `).join("")}
              </table>`
            }
            temp+=`</div>`
            WRT_e.ui.model({
              id: "if_ysr_lists",
              title: "新生儿电子病历提醒",
              width: "300px",
              content: temp,
              iframe: false,
            })
          }
        }
      })
    }
    getxsrdata()
  }
}

/********************公用方法********************/
function onselectxse(type,index){
  if(WRT_config.XinShengErTx&&WRT_config.XinShengErTx[type]){
    let list =WRT_config.XinShengErTx[type][index]
    if(list){
      page_iframe.add(`bl_${list.bingLiID}`, `${list.chuangWeiHao}-${list.bingRenXM}`, `e-BrMainLeft.html?ls_idb=${list.zhuYuanHao}&al_blid=${list.bingLiID}&${Math.random()}`)
    }
  }
}
//新增住院医技传染病异常值上报流程
function getclbLists(){
  let kaiShiSJ=getYesterday()
  let jieShuSJ=gettime()
  WRT_e.api.ehrSz.getCriticalValueListByUserId({
    params:{
      "jieShuSJ":jieShuSJ,
      "kaiShiSJ":kaiShiSJ,
      // "jieShuSJ":"2009-06-21 14:49:00",
      // "kaiShiSJ":"2009-06-01 14:49:00",
      "kaiDanZKID":WRT_config.ehrSz_init.YHXX.ZKID,
    },
    success(msg){
      if(msg.data&&msg.data.length>0){
        WRT_config.clbLists=msg.data
        let temp = `<div style="width:100%;overflow: auto;padding-bottom: 20px;"><button class="e_btn_primary" onclick="onMinlow()" style="margin-bottom: 5px;">30分钟后继续填报</button>
        <table id="clb_table" cellspacing="5" cellpadding="10" border="0" class="clbtable">
          <tr id="clbtable_tr">
            <td width="120">报告编号</td>
            <td width="100">姓名</td>
            <td width="100">性别</td>
            <td width="140">出生日期</td>
            <td width="100">检查项目</td>
            <td width="120">检查结果</td>
            <td width="100">开单专科</td>
            <td width="100">开单医师</td>
            <td width="140">检查时间</td>
            <td width="100">报告人员</td>
            <td width="140">报告时间</td>
            <td width="100">已阅人员</td>
            <td width="140">已阅时间</td>
            <td width="140">已报告人员</td>
            <td width="140">已报告时间</td>
            <td width="100" class="fix_right_title">操作</td>
          </tr>
          ${_.map(WRT_config.clbLists, (obj, index) => `
          <tr>
            <td width="120">${obj.baoGaoBH}</td>
            <td width="100">${obj.bingRenXM}</td>
            <td width="100">${obj.bingRenXB?'男':'女'}</td>
            <td width="140">${obj.chuShengRQ}</td>
            <td width="100">${obj.jianChaXM}</td>
            <td width="120" class="ts_td" title="${obj.jianChaJG}">${obj.jianChaJG}</td>
            <td width="100">${obj.kaiDanZKMC}</td>
            <td width="100">${obj.kaiDanYSXM}</td>
            <td width="14">${obj.jianChaSJ||''}</td>
            <td width="100">${obj.baoGaoRYXM}</td>
            <td width="140">${obj.baoGaoSJ}</td>
            <td width="100">${obj.yiYueRYXM}</td>
            <td width="140">${obj.yiYueSJ}</td>
            <td width="140">${obj.yiChuanBaoRYXM}</td>
            <td width="140">${obj.yiChuanBaoSJ}</td>
            <td width="100" class="fix_right"><a onclick="onTianBao(${index})">填写传报</a></td>
          </tr>
          `).join("")}
        </table></div>`
        WRT_e.ui.model({
          id: "if_Critical",
          title: "医技异常",
          width: "1250px",
          content: temp,
          iframe: false,
        })
      }
      // else if(msg.hasError==-1){
      //   WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
      // }
    }
  })
}
//住院医技传染病异常值上报流程30分钟后继续填报
function onMinlow(){
  let arr=[]
  WRT_config.clbLists.map(function(item){
    arr.push({
      "baoGaoBH":item.baoGaoBH,
      "fanKuiLB":item.fanKuiLB,
      "jianChaXM":item.jianChaXM,
      "shiFouSB":item.shiFouSB
    })
  })
  WRT_e.api.ehrSz.updateInfectiousDiseaseList({
    params:arr,
    success(msg){
      if(msg.data){
        $("#if_Critical").iziModal("destroy")
        setTimeout(function(){
          getclbLists()
        },1000*60*30)
      }else if(msg.hasError==-1){
        WRT_e.ui.hint({msg:msg.errorlessage,type:'error'})
      }
    }
  })
}
//填写传报
function onTianBao(index){
  let list = WRT_config.clbLists[index]
  clb_index=index
  if(list){
    WRT_e.api.ehrSz.getInfectiousDiagnosesInfoByPinYinOrWuBi({
      params:{
        leiBie:'py',
        pinYinOrWuBi:''
      },
      success(msg){
        if(msg.data&&msg.data.length>=0){
          WRT_config.PyWblists=msg.data
          let temp=`
          <div>
            请选择输入拼音/五笔码:<input id="doc_text" type='text' autocomplete="off" oninput="getPyWb(event)" />
            <button class="e_btn" dbclick="getPyWb()">确定</button>
            <button class="e_btn" onclick="$('#if_PyWbcrb').iziModal('destroy')">取消</button>
          </div>
          <table class="pywb_table">
            <thead>
              <tr>
                <th width="100">ICD/TCD</th>
                <th width="300">检查项目</th>
                <th width="150">法定传染类别</th>
              </tr>
            </thead>
            <tbody id="PyWb_lists">
            ${_.map(WRT_config.PyWblists,(obj,index)=>
              `<tr onclick="dbselectcrb(${index})">
                <td>${obj.icd}</td>
                <td>${obj.mingCheng}</td>
                <td>${obj.zhenDuanLB=='1'?'甲类':obj.zhenDuanLB=='2'?'乙类':obj.zhenDuanLB=='3'?'丙类':obj.zhenDuanLB=='4'?'结核':obj.zhenDuanLB=='5'?'性病':'非'}</td>
              </tr>`
            ).join('')}
            </tbody>
          </table>
          `
          WRT_e.ui.model({
            id: "if_PyWbcrb",
            title: "请选择传染病诊断",
            width: "650px",
            content: temp,
            iframe: false,
          })
        }else if(msg.hasError==-1){
          WRT_e.ui.hint({msg:msg.errorlessage,type:'error'})
        }
      }
    })
  }
}
//
function isFiveStroke(str) {
  const fiveStrokeChars = '一二三四五六七八九十'; // 五笔输入的基本字符
  for (let i = 0; i < str.length; i++) {
    if (fiveStrokeChars.indexOf(str[i]) === -1) {
      return false; // 如果有字符不是五笔字典中的，返回 false
    }
  }
  return true; // 所有字符都是五笔字典中的，返回 true
}
//py/wb获取传染病
function getPyWb(){
  let name=''
  let str = $("#doc_text")[0].value
  let type='py'
  if(isFiveStroke(str)){
    type='wb'
    name=str
  }else{
    type='py'
    name=str.toUpperCase()
  }
  WRT_e.api.ehrSz.getInfectiousDiagnosesInfoByPinYinOrWuBi({
    params:{
      leiBie:type,
      pinYinOrWuBi:name||'',
      pageSize:'100'
    },
    success(msg){
      if(msg.data&&msg.data.list.length>=0){
        WRT_config.PyWblists=msg.data.list||[]
        let temp=`
          ${_.map(WRT_config.PyWblists,(obj,index)=>
            `<tr onclick="dbselectcrb(${index})">
              <td>${obj.icd}</td>
              <td>${obj.mingCheng}</td>
              <td>${obj.zhenDuanLB=='1'?'甲类':obj.zhenDuanLB=='2'?'乙类':obj.zhenDuanLB=='3'?'丙类':obj.zhenDuanLB=='4'?'结核':obj.zhenDuanLB=='5'?'性病':'非'}</td>
            </tr>`
          ).join('')}
        `
        $("#PyWb_lists").html(temp)
      }else if(msg.hasError==-1){
        WRT_e.ui.hint({msg:msg.errorlessage,type:'error'})
      }
    }
  })
  
}
//选择传染病
function dbselectcrb(index){
  jb_index=index
  let obj=WRT_config.clbLists[clb_index]
  let list=WRT_config.PyWblists[index]
  if(list){
    WRT_e.api.ehrSz.infectiousDiseaseReportURL({
      params:{
        bingRenBH:obj.bingRenBH,
        jiBingID:list.jiBingID,
        yiChangFKJLID:obj.jiLuID,
        zhuYuanHao:obj.zhuYuanHao
      },
      success(msg){
        if(msg.data){
          let url=msg.data
          window.open(url)

        }else if(msg.hasError==-1){
          WRT_e.ui.hint({msg:msg.errorlessage,type:'error'})
        }
      }
    })
  }
} 
function onback(){
  $("if_PyWbcrb").iziModal("destroy")
  let obj=WRT_config.clbLists[clb_index]
  let list=WRT_config.PyWblists[jb_index]
  WRT_e.api.ehrSz.shiFouSB({
    params:{
      bingRenBH:obj.bingRenBH,
      jiBingID:list.jiBingID,
    },
    success(msg){
      if(msg.data){
          WRT_e.ui.message({
            title: '提示',
            content: `当前患者已于${msg.data.shangBaoSJ}上报过该传染病，是否再填一份报告卡？`,
            onOk() {
              onreset()
            },
            onCancel() {
              WRT_e.api.ehrSz.updateInfectiousDisease({
                params:{
                  "baoGaoBH":obj.bingRenBH,
                  "fanKuiLB":obj.fanKuiLB,
                  "jianChaXM":obj.jianChaXM,
                  "shiFouSB":false
                },
                success(msg){
                  if(msg.data){
                    onreset()
                  }else if(msg.hasError==-1){
                    WRT_e.ui.hint({msg:msg.errorlessage,type:'error'})
                  }
                }
              })
            },
          })
      }else if(msg.hasError==-1){
        WRT_e.ui.hint({msg:msg.errorlessage,type:'error'})
      }
    }
  })
}
//传染病刷新
function onreset(){
  let kaiShiSJ=gettime()
  let jieShuSJ=getYesterday()
  WRT_e.api.ehrSz.getCriticalValueListByUserId({
    params:{
      "jieShuSJ":jieShuSJ,
      "kaiDanZKID":WRT_config.ehrSz_init.YHXX.ZKID,
      "kaiShiSJ":kaiShiSJ
    },
    success(msg){
      if(msg.data&&msg.data.length>0){
        WRT_config.clbLists=msg.data
        let temp = `<tr id="clbtable_tr">
            <td width="120">报告编号</td>
            <td width="100">姓名</td>
            <td width="100">性别</td>
            <td width="140">出生日期</td>
            <td width="100">检查项目</td>
            <td width="120">检查结果</td>
            <td width="100">开单专科</td>
            <td width="100">开单医师</td>
            <td width="140">检查时间</td>
            <td width="100">报告人员</td>
            <td width="140">报告时间</td>
            <td width="100">已阅人员</td>
            <td width="140">已阅时间</td>
            <td width="140">已报告人员</td>
            <td width="140">已报告时间</td>
            <td width="100" class="fix_right_title">操作</td>
          </tr>
          ${_.map(WRT_config.clbLists, (obj, index) => `
          <tr>
            <td width="120">${obj.baoGaoBH}</td>
            <td width="100">${obj.bingRenXM}</td>
            <td width="100">${obj.bingRenXB?'男':'女'}</td>
            <td width="140">${obj.chuShengRQ}</td>
            <td width="100">${obj.jianChaXM}</td>
            <td width="120" class="ts_td" title="${obj.jianChaJG}">${obj.jianChaJG}</td>
            <td width="100">${obj.kaiDanZKMC}</td>
            <td width="100">${obj.kaiDanYSXM}</td>
            <td width="14">${obj.jianChaSJ||''}</td>
            <td width="100">${obj.baoGaoRYXM}</td>
            <td width="140">${obj.baoGaoSJ}</td>
            <td width="100">${obj.yiYueRYXM}</td>
            <td width="140">${obj.yiYueSJ}</td>
            <td width="140">${obj.yiChuanBaoRYXM}</td>
            <td width="140">${obj.yiChuanBaoSJ}</td>
            <td width="100" class="fix_right"><a onclick="onTianBao(${index})">填写传报</a></td>
          </tr>
          `).join("")}`
          $("#clb_table").html(temp)
        
      }else if(msg.hasError==-1){
        WRT_e.ui.hint({msg:msg.errorlessage,type:'error'})
      }
    }
  })
}
//获取日期前30天
function getYesterday() {
  const today = new Date();
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 30);
  let date=gettime(yesterday)
  return date;
}
//获取日期
function gettime(val) {
  var d=''
  val = val || ''
  if(val){
    d = new Date(val);
  }else{
    d = new Date();
  }
  var year = d.getFullYear();
  var month = change(d.getMonth() + 1);
  var day = change(d.getDate());
  var hour = change(d.getHours());
  var minute = change(d.getMinutes());
  var second = change(d.getSeconds());
  function change(t) {
    if (t < 10) {
      return "0" + t;
    } else {
      return t;
    }
  }
  var time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
  return time
}
//回车事件
function calcb() {
  var evt = window.event || e;
  if (evt.keyCode == 13) {
    getPyWb()
  }
}
////保存个性化记录
function SaveGXHJL(){
  let value=$('#saveGxhjl input[name="radio"]:checked').val()
  let id=`${WRT_config.ehrSz_init.GXH?WRT_config.ehrSz_init.GXH.GXHID:-1}`
  WRT_e.api.ehrSz.SaveGXHJL({
    params: {
      gxhnr: $('#saveGxhjl input[name="radio"]:checked').val(), //个性内容
      gxhjlid: id, //个性化id
    },
    success(data) {
      if (data.Code == 1) {
          WRT_e.ui.hint({
            type: 'success',
            msg: '保存成功！'
          })
          WRT_config.ehrSz_init.GXH.GXHNR=value
          //model.iziModal('close') //关闭modal
      }
    }
  })
}
//移动简易审批通知个性化
function saveYdspGxh(){
  let that = this
  // //console.log(WRT_config.ehrSz_init)
  var checkbox=$("#saveGxhjl input[name='checkbox']:checked")
  var str = '';
  let arr = []
  checkbox.each(function(){
    arr.push($(this).val())
  })
  str = arr.join(",")
  let GXHID=-1
  if(WRT_config.ehrSz_init.ydsp_gxh&&WRT_config.ehrSz_init.ydsp_gxh.GXHID){
    GXHID=WRT_config.ehrSz_init.ydsp_gxh.GXHID
  }
  //保存个性化记录
  WRT_e.api.ehrSz.SaveYdspGXH({
    params: {
      as_gxhnr: str, // 个性内容(string)
      al_gxhjlid: GXHID, //个性化id(int64)怎样算新增（新增穿-1，更新传id）GXHNR还是GXHID为空时新增
    },
    success(data) {
      if (data.Code == 1) {
          WRT_e.ui.hint({
            type: 'success',
            msg: '保存成功！'
          })
          gxhnr = str // 存在本地全局变量里
          //model.iziModal('close') //关闭modal
      }
    }
  })
}
//反馈中心
function fkzx_click(){
  page_iframe.add("if_fkzx",'反馈中心',WRT_config.server + '/fkzxxt/grfk.aspx')
}

// 惠每单点登录的统计端
function SSO_click() {
  let url = ''
  // url = 'http://172.16.202.123:3019/web/auth/loginSSO'
  url = 'http://172.16.202.123:3019/auth/loginSSO'
  let token =  sessionStorage.getItem('token')
  console.log(token);
  window.open(url+'?auth_token='+token)
  // window.open(url,token) // 在惠每质控页面通过 window.name获取token
}

//获取所有消息提示
function getallmessage(){
  WRT_e.api.ehrSz.getMessageAggre({
    params:{
      al_zkid:WRT_config.ehrSz_init.YHXX.ZKID,
      al_ysyhid:WRT_config.ehrSz_init.YHXX.YSYHID
    },
    success(data){
      if(data.Code==1){
        let lists=JSON.parse(data.Result)
        WRT_config.MessageAggre=lists
        let stype=true
        for(let i in lists){
          if(i!='GetFKXXCount'&&i!="grjkpop.aspx/e_init"){
            if(lists[i].code==1){
              stype=false
            }
          }
        }
        let redNum = null;
        //处理得到接收的反馈信息数量
        if(lists.GetFKXXCount.code==1){
          let num = lists.GetFKXXCount.data.split(",");
          let sum = parseInt(num[0])+ parseInt(num[1])+ parseInt(num[2])
          // $("#allCount_span").text("终末质控病历反馈:" + sum + " 份");
          // $("#red_span").text("危急:" + num[0]);
          // $("#yellow_span").text("紧急:" + num[1]);
          // $("#green_span").text("一般:" + num[2]);
          redNum = parseInt(num[0])
        }
        //处理需要医务科参加的会诊通知循环
        if (lists.e_GetHZDNeedYWCJ.code==1) {
          let sq=sessionStorage.getItem("sp_model")
          if(!sq){
            setTimeout("showHZDMsgForYW()", 24 * 300000);
          }
        }
        //特殊抗菌药物会诊单、三联抗菌药物会诊单
        if (!(lists.e_GetKjywhzd.code==1&&lists.e_GetKjywhzd.data == "-1")) {
          let sq=sessionStorage.getItem("sp_model")
          if(!sq){
            setInterval(() => showKjywhzd(), 24 * 300000);//120分钟
          }
        }
        // let grjkpop=false
        // if(lists["grjkpop.aspx/e_init"]&&lists["grjkpop.aspx/e_init"].data&&lists["grjkpop.aspx/e_init"].data.length>0){
        //   WRT_config.grjkpoplis=lists["grjkpop.aspx/e_init"].data
        //   grjkpop=true
        //   $("#grjkpop_title").css("display", "block")
        // }
        if(stype){
          return
        }
        let html=`
        ${redNum?`<div>
          <div>你有${redNum}条紧急信息，请点击右上角红色方块,进行回复!</div>
        </div></br>`:''}
        ${lists.CA_mes.code==1?`<div>
          <div>${lists.CA_mes.data}</div>
        </div></br>`:''}
        ${lists.Undocument&&lists.Undocument.code==1?`<div>
          <div style="padding: 10px 0px;">${lists.Undocument.data}</div>
          <a-button class="e_btn" onclick="showpationLost()" style="float:right">查看归档详情</a-button>
        </div></br>`:''}
        ${lists.wgy&&lists.wgy.code==1?`<div>
          <div style="padding: 10px 0px;">${lists.wgy.data}</div>
        </div></br>`:''}
        ${lists.zkxz.code==1?`<div>
          <div style="padding: 10px 0px;">${lists.zkxz.data}</div>
          <a-button class="e_btn" onclick="messshow('zkxz')" style="float:right">查看</a-button>
        </div></br>`:''}
        ${lists.yzxx.code==1?`<div>
          <div style="padding: 10px 0px;">${lists.yzxx.data}</div>
          <a-button class="e_btn" onclick="messshow('yzxx')" style="float:right">查看</a-button>
        </div></br>`:''}
        ${lists.e_GetHZDNeedYWCJ.code==1&&lists.e_GetHZDNeedYWCJ.data!=-1?`<div>
          <div style="padding: 10px 0px;">${lists.e_GetHZDNeedYWCJ.data}</div>
          <a-button class="e_btn" onclick="HZDNeedYWCJshow()" style="float:right">查看</a-button>
        </div></br>`:''}
        ${lists.e_GetHzdInfo.data?`<div>
          <div style="padding: 10px 0px;">${lists.e_GetHzdInfo.data}条会诊单信息需要立即处理</div>
          <a-button class="e_btn" onclick="HzdInfoshow()" style="float:right">查看</a-button>
        </div></br>`:''}
        ${lists.e_GetKjywhzd.data&&lists.e_GetKjywhzd.data>0?`<div>
          <div style="padding: 10px 0px;">${lists.e_GetKjywhzd.data}</div>
          <a-button class="e_btn" onclick="Kjywhzdshow('ts')" style="float:right">特殊抗菌药物会诊</a-button>
          <a-button class="e_btn" onclick="Kjywhzdshow('sl')" style="float:right">三联抗菌药物会诊</a-button>
        </div></br>`:''}
        ${lists.e_PopTip.data&&lists.e_PopTip.data!=0?`<div>
          <div style="padding: 10px 0px;">有${lists.e_PopTip.data}条电子病历调阅申请待处理</div>
          <a-button class="e_btn" onclick="PopTipshow()" style="float:right">查看</a-button>
        </div>`:''}
        ${lists.e_showCrbfh.code==1&&lists.e_showCrbfh.data?`<div>
          <div style="padding: 10px 0px;">多重耐药菌和传染病隔离警示列表，请查看并落实防控措施</div>
          <a-button class="e_btn" onclick="showCrbfh()" style="float:right">查看</a-button>
        </div></br>`:''}
        ${lists.e_PWDTip.code==1?`<div>
          <div style="padding: 10px 0px;">${lists.e_PWDTip.message}</div>
        </div></br>`:''}
        `
        WRT_e.ui.model({
          id: "if_getMessageAggre",
          title: '提示',
          width: "450px",
          content: html,
          iframe: false,
        })
      }
    }
  })
}
//院感传染列表提示弹窗
function showCrbfh(){
  let json = WRT_config.MessageAggre.e_showCrbfh
  // "<tr><td>病区</td><td>患者</td><td>住院号</td><td>床位</td><td>检验或诊断</td><td>隔离标识</td></tr>"
  WRT_e.ui.model({
    id: "if_Crbfh",
    title: json.title,
    width: "450px",
    content: json.data,
    iframe: false,
  })
  $("#tbCrbglts tbody>tr").click((e) => {
    let obj = e.target.parentElement.dataset || {}
    let jg = obj.jyjg
    if (jg) {
      let nr = `'${jg.split(",")[0]}','${jg.split(",")[1] || ''}'`;
      let url = WRT_config.server + "/hyyz/testresult/viewtestresult.aspx?ybhs=" + nr + "&temp=" + Math.random()
      window.open(url)
    }
  })
}

//特殊抗菌药物会诊单、三联抗菌药物会诊单
function showKjywhzd() {
  WRT_e.api.ehrSz.getKjywhzd({
    params: {
      al_ysyhid: WRT_config.ehrSz_init.YHXX.YSYHID
    },
    success(data) {
      let cxkjywhzd = 1
      if (data.Code == 1) {
        if (data.Result == "-1") {
          cxkjywhzd = 0;
        } else if (data.Result) {
          WRT_e.ui.message({
            title: '抗菌药物会诊单提示',
            content: data.Result,
            onOk() { },
          })
        }
        if (cxkjywhzd == 1) {
          setInterval(() => showKjywhzd(), 24 * 300000);//120分钟
        }
      }
    }
  })
}
//需要医务科参加的会诊通知
function showHZDMsgForYW() {
  WRT_e.api.ehrSz.getHZDNeedYWCJ({
    params: {
      al_ysyhid: WRT_config.ehrSz_init.YHXX.YSYHID
    },
    success(data) {
      if (data.Code == 1 && data.Result) {
        WRT_config.HZDNeedYWCJ = true
        let arr = JSON.parse(data.Result)
        if (arr.res != -1) {
          WRT_e.ui.message({
            title: '信息窗口',
            content: arr.res,
            onOk() {
              WRT_e.api.ehrSz.GetJkurl({
                params: {
                  al_mid: arr.al_mid
                },
                success(data) {
                  var url = data.Result;
                  if (data.Code != "1") {
                    WRT_e.ui.message({
                      title: '信息窗口',
                      content: '菜单地址未维护,请联系信息处',
                      onOk() { },
                    })
                    return;
                  }
                  page_iframe.add(`if_${arr.al_mid}`, arr.TITLE, WRT_config.server + '/' + url);
                }
              })
            },
            onCancel() { },
          })
        }
        if (WRT_config.HZDNeedYWCJ) {
          setTimeout("showHZDMsgForYW()", 24 * 300000);
        }
      }
    }
  })
}
//确定需要医务科参加的会诊通知
function HZDNeedYWCJshow(){
  let arr=WRT_config.MessageAggre.e_GetHZDNeedYWCJ||{}
  WRT_e.api.ehrSz.GetJkurl({
    params: {
      al_mid: arr.al_mid
    },
    success(data) {
      var url = data.Result;
      if (data.Code != "1") {
        WRT_e.ui.message({
          title: '信息窗口',
          content: '菜单地址未维护,请联系信息处',
          onOk() { },
        })
        return;
      }
      page_iframe.add(`if_${arr.al_mid}`, arr.title, WRT_config.server + '/' + url);
      $("#if_getMessageAggre").iziModal('destroy')
    }
  })
}
//特殊抗菌药物会诊
//三联抗菌药物会诊
function Kjywhzdshow(type){
  let url='',title=''
  if(type=='ts'){
    title='特殊抗菌药物会诊'
    url=WRT_config.server+`/zyblhzd/tskjywhzdhz.aspx?as_openmode=ehr3`
  }else if(type=='sl'){
    title='三联抗菌药物会诊'
    url=WRT_config.server+`/zyblhzd/slkjywhzdhz.aspx?as_openmode=ehr3`
  }
  page_iframe.add('if_kjywhzdhz',title,url)
  $("#if_getMessageAggre").iziModal('destroy')
}
//电子病历调阅申请待处理
function PopTipshow(){
  // blqxsq/blqxsq.aspx;
  page_iframe.add('if_kjywhzdhz',title,url)
  $("#if_getMessageAggre").iziModal('destroy')
}
//zkxz转科修正，yzxx医嘱信息
function messshow(type){
  let title='',url=''
  if(type=='zkxz'){
    title="转科修正"
    url='zyblws/blxztzdlist.aspx'
  }else{
    title="医嘱信息"
    url='zyyzdp/yzdpYS.aspx'
  }
  page_iframe.add(`if_message`, title, WRT_config.server + '/' + url+`?tmpid=${Math.random()}&as_openmode=ehr3`);
}
//获取超过两天没有归档的病人、外购药品申请记录、转/跨科病历修正申请的数量
function showpationLost(){
  WRT_e.api.ehrSz.GetUndocumentedPatList({
    params:{
      al_zkid:WRT_config.ehrSz_init.YHXX.ZKID
    },
    success(data){
      if(data.Code==1){
        let arr=JSON.parse(data.Result)
        WRT_config.UndocumentedPatList=arr.list
        let html=`<table id="UndocumentedPatList" cellspacing="5" cellpadding="10" border="0" class="btable"
          <tr><td>病区</td><td>床位号</td><td>姓名</td><td>性别</td><td>病案号</td></tr>
          ${_.map(WRT_config.UndocumentedPatList, (obj, index) => `
          <tr onclick="dbPaList(${index})">
            <td>${obj.BQMC}</td>
            <td>${obj.CWH}</td>
            <td>${obj.XM}</td>
            <td>${obj.XB==1?'男':'女'}</td>
            <td>${obj.EMPI||''}</td>
          </tr>
          `).join("")}
        </table>`
        WRT_e.ui.model({
          id: "if_UndocumentedPatList",
          title: '病历归档',
          width: "450px",
          content: html,
          iframe: false,
        })
      }
    }
  })
}
function dbPaList(index){
  let list=WRT_config.UndocumentedPatList[index]||{}
  if(list){
    page_iframe.add(`bl_${list.BLID}`, `${list.CWH}-${list.XM}`, `e-BrMainLeft.html?ls_idb=${list.IDB}&al_blid=${list.BLID}&${Math.random()}`)
  }
}
//加载动画
function openimg(){
  $("#patient_list").html('<div class="zgc_model"><img src="./images/loading.gif" style="width: 360px;"></div>')
}

//关闭
function dothis() {
  $('.bootstrapMenu').css('display','none')
}
//退出登录
function onout(){
  WRT_e.api.ehrTx.getRelogin({
    success(data) {
      if (data.Code == 1) {
        sessionStorage.clear()
        let url="http://wsyy.Wzhospital.cn/web/auth/#/logout?url_redirect="+encodeURIComponent(window.location.href)+'&app_key=3d6yugnzd8xyf9fqe0oh4gyjuyj3ced46yamxh3ix3cyx0ftyybdvuj4737qz475'
        // WRT_e.api.ehrTx.getRelogin
        // window.location.href = 'e-login.html'
        window.location.href=url
      }
    }
  })
}
//未填报单病种
function getloadData(type){
  if(type=='1'){
    //首页需要增加一个 护理记录
    WRT_e.api.ehrTx.hljlmes({
      params:{},
      success(data){
        if(data.Code==1){
          let res=JSON.parse(data.Result)
          let arr=res.res||[]
          WRT_config.hljlmes=arr
          if(WRT_config.hljlmes.length>0){
            $(".load_img").css("display", "none")
            $("#hljlmes_lists").html(`${_.map(WRT_config.hljlmes,obj=>
              `
              <tr>
                <td style="vertical-align: text-top;">${obj.ZDYM||''}-${obj.CWH||''} ${obj.BRXM||''} ${obj.GZCJ||''} ${obj.GZMC||''}</td>
              </tr>
              `
            ).join("")}`)
          } else {
            $(".load_img").css("display", "none")
          }
          // WRT_config.UnfilledInfo=[{BRXM:'张三',ZYH:'1537823',CYSJ:'2022-3-24 16:46'},{BRXM:'李四',ZYH:'1537823',CYSJ:'2022-3-24 16:46'},{BRXM:'王二五',ZYH:'1537823',CYSJ:'2022-3-24 16:47'},{BRXM:'赵六七',ZYH:'1537823',CYSJ:'2022-3-23 16:46'}]
        }
      }
    })
  }else if(type=='69'){
    //首页需要增加一个 未填报单病种
    WRT_e.api.ehrSz.GetUnfilledInfo({
      params:{},
      success(data){
        if(data.Code==1){
          UnfilledInfo=true
          let res=data.Result
          WRT_config.UnfilledInfo=[]
          if(res){
            WRT_config.UnfilledInfo=res.sort(function(a, b) {
              return b.CYSJ < a.CYSJ ? -1 : 1
            })
          }
          $(".load_img").css("display", "none")
          $("#wtbdbz_lists").html(`${_.map(WRT_config.UnfilledInfo,obj=>
            `
            <tr>
              <td style="width:60px;vertical-align: text-top;">${obj.EMPI||''} </td><td>${obj.BRXM}</td><td>${obj.CYSJ}</td>
            </tr>
            `
            ).join("")}`)
          // WRT_config.UnfilledInfo=[{BRXM:'张三',ZYH:'1537823',CYSJ:'2022-3-24 16:46'},{BRXM:'李四',ZYH:'1537823',CYSJ:'2022-3-24 16:46'},{BRXM:'王二五',ZYH:'1537823',CYSJ:'2022-3-24 16:47'},{BRXM:'赵六七',ZYH:'1537823',CYSJ:'2022-3-23 16:46'}]
        }
      }
    })
  }else if(type=='7'){
    //超2天未归档的病人
    WRT_e.api.ehrTx.getUndocumentedPatInfo({
      success(data_2) {
        if (data_2.Code == 1) {
          WGDBL = data_2.Result || []
          $(".load_img").css("display", "none")
          $("#wgdbr_lists").html(`${_.map(WGDBL, obj => `
          <tr>
            <td style="border:none">
              <a href="javascript:;" class="flex-row" data-jslx="${obj.JSLX}" data-zdym="${obj.BQDM}" data-CWH="${obj.CWH}" data-brxm="${obj.XM}" data-blid="${obj.BLID}" data-zyid="${obj.IDB}">
                <span style="olor: #3D6CC8;flex: 0 0 80px;font-family: Microsoft YaHei;font-style: normal;font-weight: normal;"><span style="background: rgb(61 108 200 / 15%);">${obj.BQDM}-${obj.CWH}</span></span>
                <span style="flex:1;color:#555;">${obj.XM}</br>${obj.EMPI||''}</span>
                <span style="color:#555;">${obj.XB == 1 ? '男' : '女'}</span>
              </a>
            </td>
          </tr>
        `).join('')}`)
        }
      }
    })
  }else if(type=='9'){
    //乙级病历列表
    WRT_e.api.ehrTx.GetYjblFromLtzk({
      success(data) {
        // if (data.Code == 1) {
          let arr = data.Result || {}
          
          $(".load_img1").css("display", "none")
          let html=`
          <div>
          <ul id="jblFromLtzk" class="nav nav-tabs" role="tablist">
            <li role="presentation" class="active">
              <a href="#有驳回" aria-controls="有驳回" role="tab" data-toggle="tab" aria-expanded="false">有驳回${arr.ybh?arr.ybh.length:0}份</a>
            </li>
            <li role="presentation" class="">
              <a href="#无驳回" aria-controls="无驳回" role="tab" data-toggle="tab" aria-expanded="true">无驳回${arr.wbh?arr.wbh.length:0}份</a>
            </li>
          </ul>
          <div class="tab-content advice">
            <div role="tabpanel" class="tab-pane active" id="有驳回">
              <table style="width: -webkit-fill-available;">
              ${_.map(arr.ybh,(obj)=>
                `
                <tr>
                  <td style="border:none">
                    <a href="javascript:;" class="flex-row" data-jslx="${obj.JSLX}" data-zdym="${obj.BQZDYM}" data-CWH="${obj.CWH}" data-brxm="${obj.XM}" data-blid="${obj.BLID}" data-zyid="${obj.IDB}" style="padding:5px">
                      <span style="flex:1;color:#555;">${obj.XM}&nbsp;&nbsp;<span>${obj.EMPI||''}</span></span>
                      <span style="color: #555;;flex: 0 0 80px;font-family: Microsoft YaHei;font-style: normal;font-weight: normal;"><span style="background: rgb(61 108 200 / 15%);">(${obj.BQZDYM}-${obj.CWH})</span></span>
                    </a>
                  </td>
                </tr>
                `).join('')}
              </table>
            </div>
            <div role="tabpanel" class="tab-pane" id="无驳回">
              <table style="width: -webkit-fill-available;">
              ${_.map(arr.wbh,(obj)=>
                `
                <tr>
                  <td style="border:none">
                    <a href="javascript:;" class="flex-row" data-jslx="${obj.JSLX}" data-zdym="${obj.BQZDYM}" data-CWH="${obj.CWH}" data-brxm="${obj.XM}" data-blid="${obj.BLID}" data-zyid="${obj.IDB}" style="padding:5px">
                      <span style="flex:1;color:#555;">${obj.XM}&nbsp;&nbsp;<span>${obj.EMPI||''}</span></span>
                      <span style="color: #555;;flex: 0 0 80px;font-family: Microsoft YaHei;font-style: normal;font-weight: normal;"><span style="background: rgb(61 108 200 / 15%);">(${obj.BQZDYM}-${obj.CWH})</span></span>
                    </a>
                  </td>
                </tr>
                `).join('')}
              </table>
            </div>
          </div>
          </div>
          `
          $("#YjblFromLtzk_lists").html(html)
        // }
      }
    })
  }
}
//处方结果
function cfjg_change(){
  if(WRT_config.cfjg_lists){
    // <div style="color: red;text-align: center;font-size: 16px;font-weight: 600;">院感监控预警列表</div>
    let temp=`
    <table id="cfjg_table" cellspacing="5" cellpadding="10" border="0" class="btable"
      <tr><td>姓名</td><td>性别</td><td>手机号</td><td>地址</td><td>诊断</td><td>操作</td></tr>
      ${_.map(WRT_config.cfjg_lists, (obj, index) => `
      <tr>
        <td>${obj.bingRenXM||''}</td>
        <td>${obj.bingRenXB||''}</td>
        <td>${obj.lianXiDH||''}</td>
        <td>${obj.lianXiDZ||''}</td>
        <td>${obj.linChuangZD||''}</td>
        <td><a onclick="onselectcfjg(${index})">查看</a></td>
      </tr>
      `).join("")}
    </table>
    `
    WRT_e.ui.model({
      id: "if_cfjglists",
      title: "处方列表",
      width: "850px",
      content: temp,
      iframe: false,
    })
  }
}
//处方结果双击事件
function onselectcfjg(index){
  let target = WRT_config.cfjg_lists[index]||{}
  WRT_e.api.ehrSz.getYjkYfylxgsqWithBRXXByCFH({
    params:{
      chuFangHao:target.chuFangHao
    },
    success(data){
      if(data.data&&data.data.usageDosageUpdateVoList){
        WRT_config.usageDosageUpdateVoList=data.data.usageDosageUpdateVoList

        let temp=`
        <table id="cfjgdeail_table" cellspacing="5" cellpadding="10" border="0" class="btable"
          <tr>
            <td>状态</td>
            <td>名称</td>
            <td>规格</td>
            <td></td>
            <td>组号</td>
            <td>一次用量</td>
            <td>单位</td>
            <td>频率</td>
            <td>用药方法</td>
            <td>给药时间</td>
            <td>用法补充说明</td>
            <td>申请人</td>
          </tr>
          ${_.map(WRT_config.usageDosageUpdateVoList, (obj, index) => `
          <tr>
            <td>${obj.zhuangTaiMC||''}</td>
            <td>${obj.mingCheng||''}</td>
            <td>${obj.guiGe||''}</td>
            <td>修改为</td>
            <td>${obj.yzuHao||''}</br>${obj.zuHao||''}</td>
            <td>${obj.yyiCiYL||''}</br>${obj.yiCiYL||''}</td>
            <td>${obj.yjiLiangDW||''}</br>${obj.jiLiangDW||''}</td>
            <td>${obj.ypinLu||''}</br>${obj.pinLu||''}</td>
            <td>${obj.yfangFa||''}</br>${obj.fangFa||''}</td>
            <td>${obj.ygeiYaoSJ||''}</br>${obj.geiYaoSJ||''}</td>
            <td>${obj.yteShuYF||''}</br>${obj.teShuYF||''}</td>
            <td>${obj.shenQingRYXM||''}</td>
          </tr>
          `).join("")}
        </table>
        </br>
        <div class="cfjgdeail_foolter">
          <h>病人信息：</h>
          <h>${target.bingRenXM}</h>
          <h>${target.bingRenXB}</h>
          <h>${target.bingRenNL}</h>
          <h>${target.linChuangZD}</h>
          <button class="e_btn" onclick="$('#if_cfjgdeaillists').iziModal('destroy')">取消</button>
          <button class="e_btn" onclick="ondeateil(2)">拒绝</button>
          <button class="e_btn" onclick="ondeateil(1)">同意</button>
        </div>
        `
        WRT_e.ui.model({
          id: "if_cfjgdeaillists",
          title: "处方列表",
          width: "950px",
          content: temp,
          iframe: false,
        })
      }
    }
  })
}
//处方结果审批
function ondeateil(type){
  if(WRT_config.usageDosageUpdateVoList&&WRT_config.usageDosageUpdateVoList.length>0){
    let list=WRT_config.usageDosageUpdateVoList[0]
    WRT_e.api.ehrSz.usageDosageListUpdate({
      params:{
        caoZuoLX:type,
        jiLuID:list.jiLuID
      },
      success(msg){
        if(msg.data){
          WRT_e.ui.hint({msg:'操作成功',type:'success'})
          $('#if_cfjgdeaillists').iziModal('destroy')
        }else if(msg.errorMessage){
          WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
        }
      }
    })
  }

}
//时间针
function sjz_change(){
  let url=`/yz_sz/tj/sjzyz.aspx?tmpid=${Math.random()}&openmode=ehr3&as_openmode=ehr3`
  page_iframe.add("iframe_sjzyz", "时间针", WRT_config.server + url)
}
//打印我的病人
function printMYPat() {
  window.open(WRT_config.server+"/print/mypat.aspx?as_yhid=" + WRT_config.ehrSz_init.YHXX.YSYHID + "&as_zkid=" + WRT_config.ehrSz_init.YHXX.ZKID + "&print=1&temp=" + Math.random(), "我的病人", "width:730px,height:560px,menubar=no,status=no,resizable=yes,scrollbars=yes");
}
//打印治疗组病人
function printMYPat1() {
  window.open(WRT_config.server+"/print/printZlzPat.aspx?as_yhid=" + WRT_config.ehrSz_init.YHXX.YSYHID + "&as_zkid=" + WRT_config.ehrSz_init.YHXX.ZKID + "&print=1&temp=" + Math.random(), "我的病人", "width:730px,height:560px,menubar=no,status=no,resizable=yes,scrollbars=yes");
}
//菜单点击
function changeitem(index) {
  let div_lists = $("#accordion").find(".panel")
  for (let i = 0; i < div_lists.length; i++) {
    $(`#heading${i} .glyphicon-chevron-down`).css("display", "block")
    $(`#heading${i} .glyphicon-chevron-up`).css("display", "none")
  }
  if (index == undefined) {
    return
  }
  if ($(`#panel${index}`)[0].className.indexOf("in") >= 0) {
    $(`#heading${index} .glyphicon-chevron-down`).css("display", "block")
    $(`#heading${index} .glyphicon-chevron-up`).css("display", "none")
    $(".load_img").css("display", "block")
  } else {
    if(index==69||index==7||index==9||index==1){
      getloadData(index)
    }
    $(`#heading${index} .glyphicon-chevron-down`).css("display", "none")
    $(`#heading${index} .glyphicon-chevron-up`).css("display", "block")
  }
}
//判断住院号
function getRYSJ(k, x, y) {
  let rtn = 'none'
  if (k && (!x || x == '')) {
    rtn = 'green'
  }
  if(x=='0001-01-01T00:00:00'){
    rtn='green'
  }
  if (y && y.indexOf("WSH") >= 0) {
    [
      rtn = 'red'
    ]
  }
  return rtn
}
// 判断字符长度
function getStringLength(str) {
  let length = 0;
  for (let i = 0; i < str.length; i++) {
      const c = str.charCodeAt(i);
      // 判断是否为中文字符，中文字符的Unicode编码范围大致为[0x4e00, 0x9fa5]
      if ((c >= 0x4e00 && c <= 0x9fa5) || (c >= 0x3400 && c <= 0x4dbf) ||
          (c >= 0x20000 && c <= 0x2a6df) || (c >= 0x2a700 && c <= 0x2b73f)) {
          length += 2; // 中文字符长度计2
      } else {
          length += 1; // 其他字符长度计1
      }
  }
  return length;
}

function checkStringLength(str, maxLength) {
  const length = getStringLength(str);
  return length <= maxLength;
}
//备注保存
function onSave(id) {
  let val = $("#remark_title")[0].value
  const maxLength = 20;
  if (checkStringLength(val, maxLength)) {
    WRT_e.api.ehrSz.addBz({
      params: { al_blid: id, as_bz: val },
      success(data) {
        if (data && data.Code == 1) {
          WRT_e.ui.hint({ type: "success", msg: "修改备注成功" })
          $('#if_remark').iziModal('destroy')
        }
      }
    })
  } else{
    $("#remark_title").focus();
    WRT_e.ui.hint({ type: "warning", msg: "字数过多，备注修改失败!" })
  }
}
//质控
function qc_message(obj) {
  if (obj.QC_URL) {
    page_iframe.add("质控信息", "质控信息", WRT_config.server + '/' + obj.QC_URL)
  }
}
//预出院
function ycypopHtml() {
  let temp = `
  <div>
    <label>请选择组</label>
    <select  id="ycy_zlzid" placeholder="请选择治疗组" onchange="ycy_change()">
    ${_.map(WRT_config.ehrSz_init.ZLZ, (obj) => `<option value=${obj.ID}>${obj.MC}</option>`)}
    </select>
    <button class="e_btn" onclick="alleditycy()">批量预出院</button>
  </div>
  <table id="tbycypop" cellspacing="5" cellpadding="10" border="0" class="btable"
    <tr><td><input id="all" type="checkbox" onclick="myAll()"></td><td>病区</td><td>患者</td><td>病案号</td><td>床位</td><td>操作</td></tr>
    ${_.map(WRT_config.ycypoplist, (obj, index) => `
    <tr>
      <td><input type="checkbox" name="onelist" value="${obj.ZYID}"></td>
      <td>${obj.BQDM}</td>
      <td>${obj.BRXM}</td>
      <td>${obj.EMPI||''}</td>
      <td>${obj.CWH}</td>
      <td><a onclick="addycy(${index})">填写</a></td>
    </tr>
    `).join("")}
  </table>`
  WRT_e.ui.model({
    id: "if_ycypop",
    title: "预出院列表",
    width: "450px",
    content: temp,
    iframe: false,
  })
}
//
function myAll(){
  let all= document.getElementById("all");
  let list = document.getElementsByName("onelist");
  for(let i=0; i<list.length; i++){
    list[i].checked = all.checked;
  }
}
//切换组
function ycy_change(){
  WRT_e.api.ehrSz.getPatientList({
    params: {
      as_lb: '3',
      as_lbid: $('#ycy_zlzid option:selected').val(),
      as_new: '0',
      as_pathpatient: '0'
    },
    success(data) {
      if(data.Code==1){
        console.log(JSON.parse(data.Result))
        WRT_config.ycypoplist=JSON.parse(data.Result)['BRXX']
        let html=`
          <tr style="height: 40px;color: #000;background: #b0c4de;text-align: center;"><td><input id="all" type="checkbox" onclick="myAll()"></td><td>病区</td><td>患者</td><td>病案号</td><td>床位</td><td>操作</td></tr>
        ${_.map(WRT_config.ycypoplist, (obj, index) => `
        <tr>
          <td><input type="checkbox" name="onelist" value="${obj.ZYID}"></td>
          <td>${obj.BQDM}</td>
          <td>${obj.BRXM}</td>
          <td>${obj.EMPI||''}</td>
          <td>${obj.CWH}</td>
          <td><a onclick="addycy(${index})">填写</a></td>
        </tr>
        `).join("")}`
        $("#tbycypop").html(html)
      }
    }
  })
}

//保存计划出院时间
function addycy(index){
  let list = WRT_config.ycypoplist[index]
  WRT_e.api.BrMainLeft.getJhcysj({
    params:{
      as_zyid:list.ZYID
    },
    success(data){
      var objSj = JSON.parse(data)
      if(objSj.Message == '0'){
        if(objSj.date != '0001/01/01'){
          //查询计划出院时间存在
          // let timeNow = objSj.date.replace(/(\/)/g,'-') +'T00:00'
          let timeNow = objSj.date.replace(/(\/)/g,'-')
          // <input class="jhcysj" type="datetime-local" value="${timeNow}"></input>
          WRT_e.ui.model({
            id: 'saveJhcysj',
            title: '计划出院时间',
            width: 500,
            content: `
            <dl>
              <div class="jhcysjNr">
                <strong>计划出院时间：</strong>
                <span>
                <input class="jhcysj" type="date" value="${timeNow}"></input>
                </span>
                <span style="color:#0fad0f;padding-left: 3px;font-size: 15px;">已预出院(${getDaysBetweenDates(timeNow)})</span>
                <span style="color:#3d6cc8;padding-left: 3px;font-size: 15px;cursor:pointer" onclick='clearJHCY(${list.ZYID})'>清空</span>
              </div>
            </dl>
            `,
            onOk() {
              var v = $(`.jhcysj`).val()
              // .replace('T',' ');
              // var v = $(`.jhcysj`).datetimebox('getValue');
              // var dateNow = v.replace(/\-/g,'\/')
              //保存计划出院时间
              WRT_e.api.BrMainLeft.saveJhcysj({
                params: {
                  as_zyid:list.ZYID,
                  as_sj: v
                },
                success(data) {
                  var saveData = JSON.parse(data)
                  if(saveData.Message == '0'){
                    WRT_e.ui.hint({msg:'计划出院安排成功',type:'success'})
                    $('#saveJhcysj').iziModal("destroy")
                  } else {
                    WRT_e.ui.hint({msg:'计划出院安排失败',type:'error'})
                    $('#saveJhcysj').iziModal("destroy")
                  }
                },
                error: function (err) {
                  WRT_e.ui.hint({msg:err,type:'error'})
                }
              })
            }
          })
        } else {
          //查询计划出院时间不存在（出现输入时间弹窗强制调用保存接口成功后进行下一步）
          // 获取第二天时间
          let dayTwo = formatDate(1) 
          WRT_e.ui.model({
            id: 'saveJhcysj',
            title: '保存计划出院时间',
            width: 500,
            content: `
            <dl>
              <div class="jhcysjNr">
                <strong>计划出院时间：</strong>
                <span>
                  <input class="jhcysj" type="date" value="${dayTwo}"></input>
                </span>
                <span style="color:red;padding-left: 3px;font-size: 15px;">未保存</span>
              </div>
            </dl>
            `,
            onOk() {
              var v = $(`.jhcysj`).val()
              // var v = $(`.jhcysj`).datetimebox('getValue');
              //保存计划出院时间
              WRT_e.api.BrMainLeft.saveJhcysj({
                params: {
                  as_zyid:list.ZYID,
                  as_sj: v
                },
                success(data) {
                  var saveData = JSON.parse(data)
                  if(saveData.Message == '0'){
                    WRT_e.ui.hint({msg:'计划出院安排成功',type:'success'})
                    $('#saveJhcysj').iziModal("destroy")
                  } else {
                    WRT_e.ui.hint({msg:'计划出院安排失败',type:'error'})
                    $('#saveJhcysj').iziModal("destroy")
                  }
                },
                error: function (err) {
                  WRT_e.ui.hint({msg:err,type:'error'})
                }
              })
            }
          })
        }
      }
    }
  })
}
// 计划出院（预出院）弹窗清空按钮  接口调用示例：   
function clearJHCY(id) {
  WRT_e.api.BrMainLeft.clearJhcysj({
    params: {
      as_zyid: id
    },
    success(data) {
      // console.log('计划出院（预出院）弹窗清空按钮',data);
      var val = JSON.parse(data);
      if (val.Message == "0") {
        WRT_e.ui.hint({msg:'清空成功',type:'success'})
        $('#saveJhcysj').iziModal("destroy")
      } else {
        WRT_e.ui.hint({msg:'清空失败',type:'error'})
        $('#saveJhcysj').iziModal("destroy")
      }
    },
    error: function (err) {
      WRT_e.ui.hint({msg:'问题' + err.status + '\nresponseText：' + err.responseText, type:'error'})
    }
  })
}
//差值
function getDaysBetweenDates(date1) {
  let oneDay = 24 * 60 * 60 * 1000;
  let origin = new Date(date1).getTime();
  let finish = new Date().getTime();
  let diffDays = Math.round(Math.abs((origin - finish) / oneDay));
  return diffDays;
}
// 获取当前日期后一天时间
function formatDate(n){
  var date = new Date();
  var year,month,day;
  date.setDate(date.getDate()+n);
  year = date.getFullYear();
  month = date.getMonth()+1;
  day = date.getDate();
  s = year  + '-' + (month < 10 ? ( '0'+ month) : month) + '-' + (day<10 ? ('0'+day) : day);
  return s 
}
//批量修改
function alleditycy(){
  let arr=[]
  let list = document.getElementsByName("onelist");
  for(let i=0; i<list.length; i++){
    if(list[i].checked){
      arr.push(list[i].value)
    }
  }
  if(arr.length==0){
    WRT_e.ui.hint({msg:'至少选择一条！'})
    return
  }
  // 获取第二天时间
  let dayTwo = formatDate(1) 
  WRT_e.ui.model({
    id: 'saveJhcysj',
    title: '保存计划出院时间',
    width: 500,
    content: `
    <dl>
      <div class="jhcysjNr">
        <strong>计划出院时间：</strong>
        <span>
          <input class="jhcysj" type="date" value="${dayTwo}"></input>
        </span>
        <span style="color:red;padding-left: 3px;font-size: 15px;">批量</span>
      </div>
    </dl>
    `,
    onOk() {
      var v = $(`.jhcysj`).val()
      // var v = $(`.jhcysj`).datetimebox('getValue');
      //保存计划出院时间
      for(let i=0;i<arr.length;i++){
        WRT_e.api.BrMainLeft.saveJhcysj({
          params: {
            as_zyid:arr[i],
            as_sj: v
          },
          success(data) {
            if(i==arr.length-1){
              var saveData = JSON.parse(data)
              if(saveData.Message == '0'){
                WRT_e.ui.hint({msg:'批量设置成功',type:'success'})
                $('#saveJhcysj').iziModal("destroy")
              } else {
                WRT_e.ui.hint({msg:'批量设置失败',type:'error'})
                $('#saveJhcysj').iziModal("destroy")
              }
            }
          },
        })
      }
    }
  })
}


//
function grjkpopHtml() {
  let temp = `
  <div style="color: red;text-align: center;font-size: 16px;font-weight: 600;">院感监控预警列表</div>
  <table id="tbgrjkpop" cellspacing="5" cellpadding="10" border="0" class="btable"
    <tr><td>病区</td><td>患者</td><td>病案号</td><td>床位</td><td>填写院感监控</td></tr>
    ${_.map(WRT_config.grjkpoplis, (obj, index) => `
    <tr>
      <td>${obj.BQMC}</td>
      <td>${obj.BRXX}</td>
      <td>${obj.EMPI||''}</td>
      <td>${obj.CWH}</td>
      <td><a onclick="addgrjk(${index})">填写</a></td>
    </tr>
    `).join("")}
  </table>`
  WRT_e.ui.model({
    id: "if_grjkpop",
    title: "预警",
    width: "450px",
    content: temp,
    iframe: false,
  })
}
//院感专科列表
function addgrjk(index) {
  let list = WRT_config.grjkpoplis[index]
  if (list) {
    window.open(list.GRJKURL)
  }
}
//处理URL拼接参数
function GetParam(paramlist) {
  if (paramlist.indexOf(",") >= 0) {
    var j_param = paramlist.split(',');
    var plist = "";
    var p_tmp = "";
    for (var i = 0; i < j_param.length; i++) {
      if (j_param[i] == "") continue;
      p_tmp = GetParamDz(j_param[i]);
      if (plist == "")
        plist = p_tmp;
      else
        plist += "&" + p_tmp;
    }
    return plist;
  } else {
    if (paramlist.indexOf("=") >= 0)
      return paramlist;
    else
      return GetParamDz(paramlist);
  }
}
//判断参数并拼接
function GetParamDz(param) {
  var ls_rtn;
  switch (param) {
    case "as_zkid":
    case "zkid":
    case "al_zkid":
    case "av_zkid":
    case "as_zkid":
      ls_rtn = param + "=" + WRT_config.ehrSz_init.YHXX.ZKID;
      break;
    case 'al_yhid':
      ls_rtn = param + "=" + WRT_config.ehrSz_init.YHXX.YSYHID;
      break;
    default:
      param = "";
      break;
  }
  return ls_rtn;
}

function e_close() {
  $("#if_WjzMessage").iziModal("destroy")
}
//麻醉接口点击回调
function e_OpenTab(url, title) {
  page_iframe.add(title, title, WRT_config.server + '/' + url)
  
}
//刷新回调
function OpenTab(url, title) {
  // let u=url.split('.')||[]
  // page_iframe.add(`if_${u[0]}`,title, url)
  page_iframe.add(title,title, url)
}
//子界面调用父界面增加标签
function addPage(title, type, paramList) {
  let lit = ""
  let page = type.split(".")[0]
  let params = {}, url = "",
    id = "";
  let text = paramList.split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  switch (page) {
    case 'e-BrMainLeft':
      id = params["ls_idb"]
      url = type + '?' + paramList
      lit = `bl_${id}`
      break;
    default:
      let val = text[0].split("=")
      id = val[1]
      lit = `if_${val[1]}`
      break;
  }
  page_iframe.add(lit, title, url)
}

//处理VTE标题
function gettitle(title) {
  let name = ""
  if (title && title.indexOf("科") >= 0) {
    name = title.split("科")[1]
  }
  return name
}

//渲染病人一览表
function showPatient(data, del = false) {
  if (data.Code == 0) {
    //空
    $("#patient_list").html(
      new empty_View().init().render().$el
    )
  } else if (data.Code == 1) {
    let type=$(".e_btn_group li.active a")[0].text
    if(type=='治疗组病人'){
      $("#patient_list").html(
        new brzlzList_View().init({
          data: {
            del, //是否可删除
            list: JSON.parse(data.Result)['BRXX'],
            zlz_ar:JSON.parse(data.Result)['ZLZXX']||[]
          }
        }).render().$el
      )
    }else if(type=='我的病人'){
      let list=JSON.parse(data.Result)['BRXX']
      let arr=[]
      list.map(function(item){
        let fd=arr.filter(e=>e.BQID==item.BQID)
        if(fd.length==0){
          arr.push(item)
        }
      })
      $("#patient_list").html(
        new brbqList_View().init({
          data: {
            del, //是否可删除
            list: list,
            bq_ar:arr
          }
        }).render().$el
      )
    }else{
      //列表
      $("#patient_list").html(
        new brList_View().init({
          data: {
            del, //是否可删除
            list: JSON.parse(data.Result)['BRXX']
          }
        }).render().$el
      )
    }
  }
}
//专科日志选择渲染
function selectPortal() {
  let id = 0,
    lb = 0
  sel_bq = null
  sel_zlz = null
  if ($("#sel_bq option:selected").val()) {
    lb = 1
    id = $("#sel_bq option:selected").val()
    sel_bq = id
  } else if ($("#sel_zlz option:selected").val()) {
    lb = 2
    id = $("#sel_zlz option:selected").val()
    sel_zlz = id
  }
  //获取病人日志数据
  WRT_e.api.ehrTx.getPortal({
    params: {
      al_glid: id,
      as_lb: lb
    },
    success(data) {
      if (data.Code == 1) {
          $("#patient_info").html(
            new brPortal_View().init({
              data: {
                ...JSON.parse(data.Result).e_GetPortal||{},
                ...{
                  GetBrcyblbg:WRT_config.DataSimple.e_GetBrcyblbg
                },
                ...{
                  WGDBL:WRT_config.DataSimple.e_GetUndocumentedPatInfo
                },
                ...{
                  GetUnfilledInfo:WRT_config.DataSimple.e_GetUnfilledInfo
                },
              }
            }).render().$el
          )
        }
    }
  })
}

//标签页式iframe
var page_iframe = {
  //添加选项卡页面
  add(id, name, url) {
    //添加dom
    //判断标签项最多为8个
    if ($('#page>.nav>li').length >= 8) {
      WRT_e.ui.hint({
        type: 'info',
        msg: '工作标签页太多,请适当删减！'
      })
      return
    }
    if(url.indexOf('frbb.aspx')<0){
      if (url.indexOf("tmpid") == -1) {
        if (url.indexOf("?") >= 0) {
          url = url + `&tmpid=${Math.random()}`
        } else {
          url = url + `?tmpid=${Math.random()}`
        }
      }
      if (url.indexOf("openmode") == -1) {
        if (url.indexOf("?") >= 0) {
          url = url + `&openmode=ehr3&as_openmode=ehr3`
        } else {
          url = url + `?openmode=ehr3&as_openmode=ehr3`
        }
      }
    }
    //判断是否有该标签
    if ($('#page>.nav>li').find(`a[href="#${id}"]`).length == 0) {
      $('#page>.nav').append(`<li><a href="#${id}" data-toggle="tab">${name}</a><i class="iconfont icon-cuo close"></i></li>`)
      $('#page>.tab-content').append(`<div class="tab-pane fade" id="${id}"><iframe src="${url}"  frameborder="0" scrolling="auto" style="width:100%;height:100%"></iframe></div>`)
    }
    //激活当前选项卡
    $(`#page a[href="#${id}"]`).tab('show')
  },
  //删除选项卡页面
  del(id) {
    if (id.indexOf("bl") >= 0) {
      istab = id
      var childWindow = $(`#${id} iframe`)[0].contentWindow;
      childWindow.delTab();
      return
    }
    let el = $(`#page a[href="#${id}"]`).parent()
    //激活下一个选项卡
    if (el[0].className == 'active') {
      if (el.prev().length == 0) {
        el.parent().find('li:first-child a').tab('show')
      } else {
        el.prev().children('a').tab('show')
      }
    }
    // //删除dom
    el.remove()
    setTimeout(() => {
      $(`#page #${id}`).remove()
    }, 200)
  }
}
//病程子页面调用
function delBlws() {
  if (!istab) {
    return
  }
  // if(WRT_config.deltab){

  // }
  let el = $(`#page a[href="#${istab}"]`).parent()
  if (el[0].className == 'active') {
    if (el.prev().length == 0) {
      el.parent().find('li:first-child a').tab('show')
    } else {
      el.prev().children('a').tab('show')
    }
  }
  // //删除dom
  el.remove()
  setTimeout(() => {
    $(`#page #${istab}`).remove()
  }, 200)
}
//计算日期差
function daysBetween(sDate1, sDate2, type, CWRYSJ) {
  if (new Date(sDate1).getTime() <= 0) {
    sDate1 = CWRYSJ
  }
  var d = new Date(sDate2);
  var year = d.getFullYear();
  var month = change(d.getMonth() + 1);
  var day = change(d.getDate());
  var hour = change(d.getHours());
  function change(t) {
    if (t < 10) {
      return "0" + t;
    } else {
      return t;
    }
  }
  var time = year + '-' + month + '-' + day+' '+hour+':00:00'
  //Date.parse() 解析一个日期时间字符串，并返回1970/1/1 午夜距离该日期时间的毫秒数
  var time1 = Date.parse(new Date(sDate1));
  var time2 = Date.parse(new Date(time));
  //sel_br
  switch (type) {
    case 'day':
      let yyy = (time2 - time1) / 1000 / 3600 / 24 
      if (String(yyy).indexOf(".") >= 0) {
        yyy = Math.abs(parseInt(yyy)) + 1
      }

      // 修改展示效果变为天数 + 小时
      // let msss = Math.abs(parseInt((time2 - time1))) % (24 * 3600 * 1000) // 相差毫秒数
      // let yyy = Math.abs(parseInt((time2 - time1) / 1000 / 3600 / 24 ))// 相差天数
      // let hhh = parseInt(msss / (3600 * 1000)) // 相差小时数
      // // 相差分钟数
      // let h_mmm = msss % (3600 * 1000)
      // let mmm = parseInt(h_mmm / (60 * 1000))
      // // 相差秒数
      // let m_sss = h_mmm % (60 * 1000)
      // let sss = parseInt(m_sss / 1000) 
      // // eg. 298 '天' 3 '小时' 3 '分钟' 10 '秒' -> 298 '天' 4 '小时'
      // // 298 '天' 3 '小时' 0 '分钟' 10 '秒' -> 298 '天' 4 '小时'
      // if(parseInt(sss) > 0){
      //   if (parseInt(hhh) <= 22){
      //     hhh = Math.abs(parseInt(hhh)) + 1
      //   } else {
      //     hhh = 0
      //     yyy = Math.abs(parseInt(yyy)) + 1
      //   }
      // } 
      return yyy+'天';
    case 'hour':
      // if(parseInt(time2 - time1)>1000 * 3600 * 24){
      //   let yyy = (time2 - time1) / 1000 / 3600 / 24
      //   if (String(yyy).indexOf(".") >= 0) {
      //     yyy = Math.abs(parseInt(yyy)) + 1
      //   }
      //   return yyy+'天';
      // }
      return Math.abs(parseInt((time2 - time1) / 1000 / 3600))+'小时';
  }
}

//保存专科修改弹框
function saveInfo() {
  let id = $("#DropDownList1 option:selected").val()
  if (id) {
    WRT_e.api.ehrSz.ChangeZK({
      params: {
        ll_zkid: id
      },
      success(data) {
        if (data.Code == 1) {
          // WRT_e.ui.hint({
          //   type: 'success',
          //   msg: '修改专科成功'
          // })
          $("#if_toInfo").iziModal("close")
          // window.location.reload()
          //console.log('用户切换目标专科',id,'用户切换前专科',WRT_config.ehrSz_init.YHXX.ZKID)
          WRT_e.api.ehrSz.getSaveQhzkRz({
            params: {
              al_qhq_zkid: id,
              al_qhh_zkid: WRT_config.ehrSz_init.YHXX.ZKID
            },
            success(data) {
              if (data.Code == 1) {
                WRT_e.ui.hint({
                  type: 'success',
                  msg: '修改专科成功'
                })
                window.location.reload()
              }
            }
          })
        }
      }
    })
  }
}
//菜单处理
function getMenuData(target){
  //console.log(WRT_config.MeunList)
  let arr=[]
  if(WRT_config.MeunList){
    WRT_config.MeunList.map(function(item){
      if(item.meta.title==target){
        arr=item
        return
      }
    })
  }
  return arr
}
//是否显示子集
function isshow(target){
  let show=true
  if(target.caiDanLX=='M'){
    show=false
    if(target.children&&target.children.length>0){
      show=true
    }
  }
  return show
}

// 移动简易审批通知个性
// 全选
function f_selectAll() {
  // $("input[name='checkbox']").attr('checked','true')
  $(".checkboxNow").prop("checked", true)
}
// 全不选
function f_unselectAll() {
  // $("input[name='checkbox']").removeAttr('checked')
  $(".checkboxNow").removeAttr('checked')
}

/********************视图********************/
let menu_list=[]
//菜单栏
var Menus_View = WRT_e.view.extend({
  render: function () {
    menu_list=[]
    // WRT_config.MeunList.map(function(item){
    //   if(item.systemMuneTypeVoList&&item.systemMuneTypeVoList.length>0){
    //     item.systemMuneTypeVoList.map(function(k){
    //       menu_list.push(k.caiDanMC)
    //     })
    //   }
    // })
    // menu_list.push(this.data.WDCD)
    this.data.WDCD.map(function(item){
      let getlist=function(k){
        let list={
          "path": k.M_URL,
          "hidden": false,
          "caiDanLX": "",
          "meta": {
            "title": k.M_NAME,
            "caiDanID": k.M_ID,
            "paramVos": [{canShuMing:k.M_PARAMLIST}]
          },
        }
        return list
      }
      let params=getlist(item)
      if(item.children){
        let arr=[]
        item.children.map(function(key){
          let active=getlist(key)
          arr=[...arr,active]
        })
        params.children=arr
      }
      menu_list=[...menu_list,params]
    })
    let menu = [{
      label: '我的菜单',
      value: 'WDCD',
      children:menu_list,
      // ...getMenuData("我的菜单"),
      // type:getMenuData(this.data.WDCD).type
    }, {
      label: '专科菜单',
      value: 'ZKCD',
      // multiCol: true,
      ...getMenuData("专科菜单"),
      // type:getMenuData(this.data.ZKCD).type
    }, {
      label: '科室医疗质量指标查询',
      value: 'KSZLZBCD',
      ...getMenuData("科室医疗质量指标查询"),
      // type:getMenuData(this.data.KSZLZBCD).type
    }, {
      label: '信息查询',
      value: 'XXCXCD',
      multiCol: true,
      ...getMenuData("信息查询"),
      // type:getMenuData(this.data.XXCXCD).type
    }, {
      label: '系统维护',
      value: 'XTWH',
      ...getMenuData("系统维护"),
      // type:getMenuData(this.data.XTWH).type
    }]
    //多级菜单
    let list = (e) => {
      let list = ""
      if (e.children&&e.children.length>0) {
        list += `<li class="dropdown-submenu  ${e.children?"":"disnone"}"><a href="#">${e.meta.title}</a><ul class="dropdown-menu">`
        for (let key of e.children) {
          let params=[]
          if(key.meta.paramVos){
            key.meta.paramVos.map(function(item){
              params.push(item.canShuMing)
            })
          }
          let json=params.join(",")
          list += `<li class=""><a class="goURL ${isshow(key)?"":"disnone"}" data-MLIST="${json}" data-caidan="${key.caiDanLX}" data-id="${key.meta.caiDanID}" data-self="${key.path}" data-page="${key.component}">${key.meta.title}</a></li>`
        }
        list += `</ul></li>`
      } else {
        let params=[]
          if(e.meta.paramVos){
            e.meta.paramVos.map(function(item){
              params.push(item.canShuMing)
            })
          }
          let json=params.join(",")
        list += `<li><a class="goURL ${isshow(e)?"":"disnone"}" data-MLIST="${json}" data-caidan="${e.caiDanLX}" data-id="${e.meta.caiDanID}" data-self="${e.path}" data-page="${e.component}">${e.meta.title}</a></li>`
      }
      // if (e.subMenus) {
      //   list += `<li class="dropdown-submenu"><a href="#">${e.M_NAME}</a><ul class="dropdown-menu">`
      //   for (let key of e.subMenus) {
      //     list += `<li><a class="goURL" data-MLIST="${key.M_PARAMLIST}" data-id="${key.M_ID}" data-self="${key.M_URL}">${key.M_NAME}</a></li>`
      //   }
      //   list += `</ul></li>`
      // } else {
      //   list += `<li><a class="goURL" data-MLIST="${e.M_PARAMLIST}" data-id="${e.M_ID}" data-self="${e.M_URL}">${e.M_NAME}</a></li>`
      // }
      return list
    }

    this.$el.html(`
    <div class="menu_bottom flex-row justify-content-end">
      ${_.map(menu, (obj) =>
      `<div class="dropdown ${obj.children?obj.children.length>20 ? 'multiCol' : '':''}">
          <div class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown">${obj.label}<span class="caret"></span></div>
          <ul class="dropdown-menu ${obj.children?"":"disnone"}">
          ${_.map(obj.children, (e) => list(e)).join('')}
          </ul>
        </div>`).join('')}
    </div>
    `);
    // this.$el.html(`
    // <div class="menu_bottom flex-row justify-content-end">
    //   ${_.map(menu, (obj) =>
    //   `<div class="dropdown ${obj.multiCol ? 'multiCol' : ''}">
    //       <div class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown">${obj.label}<span class="caret"></span></div>
    //       <ul class="dropdown-menu">
    //       ${_.map(this.data[obj.value], (e) => list(e)).join('')}
    //       </ul>
    //     </div>`).join('')}
    // </div>
    // `);
    return this
  },
  events: {
    "click .goURL": "goURL",
  },
  goURL(e) {
    let target = $(e.target)
    let menu=target.attr("data-caidan")
    if(menu=='M'){
      return
    }
    
    let url=target.attr("data-self")
    let paramList=target.attr("data-MLIST")
    let componentNow=target.attr("data-page")
    var param = "";
    if (paramList != "") {
      param = GetParam(paramList);
    }
    
    if ((url.indexOf("?") >= 0)&&url.indexOf("frbb.aspx")<0){
      url += "&" + param+`&as_openmode=ehr3`;
    }
    else if(url.indexOf("frbb.aspx")<0){
      url += "?" + param+`&as_openmode=ehr3`;
    }
    if (target.text() == "按病人基本信息查询病历(高级)") {
      page_iframe.add(`menu_${target.attr("data-id")}`, target.text(), `e-blcxgj.html`)
    } else if(menu =='F'){ // 原本有 && target.text() == "全院清点单自查表"
      WRT_e.api.ehrSz.GetFMenuUrl({
        params: {
          component: componentNow,
          url: target.attr("data-self")
        },
        success(data) {
          page_iframe.add(`menu_${target.attr("data-id")}`, target.text(), `${data}`)
        }
      })
    } else if (target.text()) {
      page_iframe.add(`menu_${target.attr("data-id")}`, target.text(), `${WRT_config.server}/${url}`)
      setTimeout(function () {
        $(`#menu_${target.attr("data-id")} iframe`)[0].src = `${WRT_config.server}/${url}`
      }, 100)
    } else {
      page_iframe.add(`menu_${target.attr("data-id")}`, target.text(), `${WRT_config.server}/${url}`)
    }
  }
})
//头部用户信息view
var loginInfo_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    <div class="login_info">
      <div class="info ">
        <img src="./images/医生.png" alt="">
        <span>${this.data.YHZKMC}-${this.data.YHXM}</span>
      </div>
      <div class="icon config">
        <div class="dropdown">
          <div class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown">
            <i class="iconfont icon-shezhi"></i>
          </div>
          <ul class="dropdown-menu dropdown-menu-right">
            <li><a href="#">问题反馈</a></li>
            <li><a href="#">反馈列表</a></li>
            <li id="relogin"><a href="#">重新登陆</a></li>
            <li id="exit"><a href="#">退出系统</a></li>
          </ul>
        </div>
      </div>
    </div>
    `)
    return this
  },
  events: {
    "click #relogin": "toRelogin",
    "click .login_info>.info": "toInfo",
    "click #exit": "toRelogin",
  },
  toRelogin(ev) {
    //登出操作
    onout()
  },
  toInfo() {
    let temp = `
      <table cellpadding="1" cellspacing="0" bordercolordark="white" bordercolorlight="#bbbbbb" border="1">
      <tbody>
          <tr>
              <td colspan="2" bgcolor="#4278B4" style="font-weight: bold;color: white">医生临时轮转专科调动
                  </td>
          </tr>
          <tr>
              <td style="width: 100px; height: 26px; font-size:12px">
                  请选择轮转专科：</td>
              <td style=" height: 26px;width: 246px;">
                  <select name="DropDownList1" id="DropDownList1" style="width:170px;">
                  ${_.map(WRT_config.AllZkxx, (obj) =>
      `<option value="${obj.KEY}" ${WRT_config.ehrSz_init.YHXX.ZKID == obj.KEY ? "selected='selected'" : ""}>${obj.VAL}</option>`
    )}
                  </select>
              </td>
          </tr>
          <tr>
              <td style="width: 100px">&nbsp;</td>
              <td style=" height: 26px;width: 246px;"> <input type="submit" class="e_btn" name="Button2" value="保存" onclick="saveInfo()" id="Button2"></td>
          </tr>
          <tr>
              <td style=" height:30px; font-size:12px" colspan="2">&nbsp;</td>
          </tr>
      </tbody>
  </table>`
    WRT_e.ui.model({
      id: 'if_toInfo',
      title: '修改个人信息',
      width: 450,
      content: temp,
      iframe: false,
      // iframeURL: `${WRT_config.server}/blcx/xggrxx.aspx?cbfuncname=openwin_easyuiCb`
    })
  },
  toRelogin(ev) {
    //登出操作
    onout()
    // WRT_e.api.ehrTx.getRelogin({
    //   success(data) {
    //     if (data.Code == 1) {
    //       window.location.href = 'e-login.html'
    //     }
    //   }
    // })
  }
})
//<button class="e_btn_primary gxhbtn" style="height:30px;margin-right:20px;" name="saveYdspGxh"><i class="glyphicon glyphicon-sort"></i> 移动简易审批通知个性化</button>
//病人一览表切换过滤表单
var brListFilter_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    <div class="patient_type">
    <div class="flex-row justify-content-between">
      <div class="flex-row align-items-center">
        <button class="e_btn_primary gxhbtn" style="height:30px;margin-right:20px;" name="saveGxhjl"><i class="glyphicon glyphicon-sort"></i> 个性化</button>
        <ul class="nav e_btn_group">
          <li><a href="#GetPatientList_4">我的病人</a></li>
          <li><a href="#GetPatientList_1">治疗组病人</a></li>
          <li><a href="#GetPatientList_3">专科治疗组</a></li>
          <li><a href="#GetPatientList_2">专科病区病人</a></li>
          <li><a href="#GetPatientList_5">公共床位</a></li>
        </ul>
      </div>
      <div class="tab-content">
        <div class="tab-pane fade" id="GetPatientList_4">
          <label class="radio-inline">
            <input type="radio" name="as_lx" value="2" checked>按病人
          </label>
          <label class="radio-inline">
            <input type="radio" name="as_lx" value="1">按床位
          </label>
          <button class="e_btn_primary" style="height:30px;margin-left:10px;" type="button" onclick="clearAllMyPat">清空我的病人</button>
          <button class="e_btn_primary" style="height:30px;margin-left:10px;" type="button" onclick="printMYPat()">打印我的病人</button>
        </div>
        <div class="tab-pane fade" id="GetPatientList_1">
          <button class="e_btn_primary" style="height:30px;margin-left:10px;" type="button" onclick="printMYPat1()">打印治疗组病人</button>
        </div>
        <div class="tab-pane fade" id="GetPatientList_3">
          <form class="form-inline">
            <div class="form-group">
              <label for="exampleInputName2">请选择组</label>
              <select  id="al_zlzid"  class="form-control input-sm" placeholder="请选择治疗组">
              ${_.map(this.data.ZLZ, (obj) => `<option value=${obj.ID}>${obj.MC}</option>`)}
              </select>
            </div>
          </form>
        </div>
        <div class="tab-pane fade" id="GetPatientList_2"></div>
        <div class="tab-pane fade" id="GetPatientList_5"></div>
      </div>
    </div>
  </div>
  `)
    // 病区模块
    this.$el.find('#GetPatientList_2').append(new brListFilterBq_View().init({
      data: WRT_config.ehrSz_init
    }).render().$el)
    // 公共床位病区模块
    this.$el.find('#GetPatientList_5').append(new brListFilterBq_View().init({
      data: WRT_config.ehrSz_init
    }).render().$el)
    //渲染过滤表单，科室默认值最后一个
    this.$el.find('#al_zkid>option:last-child').attr("selected", true)
    return this
  },
  events: {
    "click .e_btn_group>li>a": "changePatient", 
    "click button[name='saveGxhjl']": "saveGxhjl", 
    "click button[name='clearAllMyPat']": "clearAllMyPat", 
    "change #GetPatientList_4 input[name='as_lx']": "changeMyPatByLX", 
    "change #al_zlzid": "changeZlzid", 
  },
  //切换病人
  changePatient(e) {
    //防止avtive再次点击
    if (!$(e).parents().hasClass('active')) {
      let gxhnr = e.target.hash.split('_')[1] //4我的病人 1治疗组病人 3专科治疗组 2专科病区病人 5公共床位
      sel_br=gxhnr
      //加载动画
      openimg()
      switch (gxhnr) {
        case '4': //治疗组病人
          WRT_e.api.ehrSz.getMyPatByLX({
            params: {
              as_lx: $('#GetPatientList_4 input[name="as_lx"]:checked').val(),
            },
            success(data) {
              showPatient(data, true)
            }
          })
          break;
        case '1': //治疗组病人
          WRT_e.api.ehrSz.getPatientList({
            params: {
              as_lb: gxhnr,
              as_lbid: '0',
              as_new: '0',
              as_pathpatient: '0'
            },
            success(data) {
              showPatient(data)
            }
          })
          break;
        case '3': //专科治疗组
          WRT_e.api.ehrSz.getPatientList({
            params: {
              as_lb: gxhnr,
              as_lbid: $('#al_zlzid option:selected').val(),
              as_new: '0',
              as_pathpatient: '0'
            },
            success(data) {
              showPatient(data)
            }
          })
          break;
        case '2': // 专科病区病人
          if(WRT_config.ehrSz_init.KCLBQ.length==0){
            showPatient({Code:0})
            return
          }
          WRT_e.api.ehrSz.getPatientListByZkBqid({
            params: {
              al_zkid: parseInt($('#al_zkid option:selected').val()),
              al_bqid: parseInt($('#al_bqid option:selected').val()),
              as_new: '0',
              as_pathpatient: '0'
            },
            success(data) {
              showPatient(data)
            }
          })
          break;
        case '5': // 公共床位
          WRT_e.api.ehrSz.getPatientListByZkBqidGgcw({
            params: {
              al_zkid: parseInt($('#al_zkid option:selected').val()),
              al_bqid: parseInt($('#al_bqid option:selected').val())==null?`0`:parseInt($('#al_bqid option:selected').val()),
              as_new: '0',
              as_pathpatient: '0'
            },
            success(data) {
              showPatient(data)
            }
          })
          break;
      }
      $(e.currentTarget).tab('show')
    }
  },
  //清空我的病人
  clearAllMyPat() {
    WRT_e.ui.message({
      title: '信息窗口',
      content: `是否清空我的病人?`,
      onOk() {
        WRT_e.api.ehrSz.clearAllMyPat({
          success(data) {
            if (data.Code == 0) {
              WRT_e.ui.hint({
                type: 'error',
                msg: '清空失败，请重试或重新登录'
              })
            } else if (data.Code == 1) {
              // 空
              $("#patient_list").html(
                new empty_View().init().render().$el
              )
            }
          }
        })
      },
      onCancel() { },
    })
  },
  //切换按病人按床位
  changeMyPatByLX(e) {
    //加载动画
    openimg()
    WRT_e.api.ehrSz.getMyPatByLX({
      params: {
        as_lx: $(e.target).val(),
      },
      success(data) {
        showPatient(data, true)
      }
    })
  },
  //切换治疗组
  changeZlzid(e) {
    //加载动画
    openimg()
    WRT_e.api.ehrSz.getPatientList({
      params: {
        as_lb: '3',
        as_lbid: $('#al_zlzid option:selected').val(),
        as_new: '0',
        as_pathpatient: '0'
      },
      success(data) {
        showPatient(data)
      }
    })
  },
  //保存个性化记录
  saveGxhjl() {
    let arr = [{
      label: '我的病人',
      value: '4'
    }, {
      label: '治疗组病人',
      value: '1'
    }, {
      label: '专科治疗组',
      value: '3'
    }, {
      label: '专科病区病人',
      value: '2'
    }, {
      label: '公共床位',
      value: '5'
    }]
    let jy_arr = [{
      label: '手术通知单',
      value: '04'
    }, {
      label: '特殊抗菌药物会诊',
      value: '05'
    }, {
      label: '三联抗菌药物会诊',
      value: '06'
    }, {
      label: '用血审批',
      value: '07'
    }, {
      label: '会诊审批',
      value: '08'
    }]
    WRT_e.ui.model({
      id: 'saveGxhjl',
      title: '保存个性化记录',
      width: 300,
      content: `
      <dl>
        <dt>请选择手机端接收移动审批消息类型</dt>
        <dt>默认选择:</dt>
        <dt>
          <input id="btn_selectAll" class="e_btn" type="button" value="全选" onclick="f_selectAll()"></input>
          <input id="btn_unselectAll" class="e_btn" type="button" value="全不选" onclick="f_unselectAll()"></input>
        </dt>
        ${_.map(jy_arr, obj => `
        <dd style="margin-left:24px;">
          <div class="checkbox">
            <label>
              <input class="checkboxNow" type="checkbox" name="checkbox" value="${obj.value}" 
              ${gxhnr == '[]'?
              WRT_config.ehrSz_init.ydsp_gxh && WRT_config.ehrSz_init.ydsp_gxh.GXHNR !=null && WRT_config.ehrSz_init.ydsp_gxh.GXHNR.indexOf(obj.value)!=-1 ? 'checked' : '':
              gxhnr.indexOf(obj.value)!=-1 ? 'checked' : ''
              }>${obj.label}
            </label>
          </div>
        </dd>`).join('')}
        <button class="e_btn" onclick="saveYdspGxh()">保存</button>
      </dl>
      <dl>
      <dt>请选择个性化记录</dt>
        <dt>默认选择:</dt>
        ${_.map(arr, obj => `
        <dd style="margin-left:24px;">
          <div class="radio">
            <label>
              <input type="radio" name="radio" value="${obj.value}" ${WRT_config.ehrSz_init.GXH && WRT_config.ehrSz_init.GXH.GXHNR == obj.value ? 'checked' : ''}>${obj.label}
            </label>
          </div>
        </dd>`).join('')}
        <button class="e_btn" onclick="SaveGXHJL()">保存</button>
      </dl>
      
      `,
    })
  },
})
//病人一览表切换过滤表单的科室病区选择模块
var brListFilterBq_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    <div class="form-inline">
      <div class="form-group">
        <label for="exampleInputName2">请选择科室：</label>
        <select id="al_zkid"  class="form-control input-sm" placeholder="请选择科室">
        ${_.map(this.data.KCLZK, (obj, index) => `<option value=${obj.VAL}>${obj.TXT}</option>`)}
        </select>
      </div>
      <div class="form-group">
        <label for="exampleInputEmail2">请选择病区：</label>
        <select id="al_bqid"  class="form-control input-sm" placeholder="请选择病区">
        ${_.map(this.data.KCLBQ, (obj, index) => `<option  value=${obj.bqid}>${obj.bmmc}</option>`)}
        </select>
        <button class="e_btn_primary search1">检索</button>
      </div>
    </div>`)
    return this
  },
  events: {
    "change #al_zkid": "changeZkid",
    "change #al_bqid": "changeBqid",
    "click .search1": "clicksearch"
  },
  //切换科室
  changeZkid(e) {
    let _this = this
    if(WRT_config.ehrSz_init.KCLBQ.length==0){
      showPatient({Code:0})
      return
    }
    //加载动画
    openimg()
    if(sel_br == 5){
      //获取病区信息
      WRT_e.api.ehrSz.getZkdyBq({
        params: {
          al_zkid: parseInt(_this.$el.find("#al_zkid>option:selected").val()),
        },
        success(data) {
          if (data.Code == 1) {
            //渲染页面
            _this.data.KCLBQ = JSON.parse(data.Result)
            _this.render()
            //记录科室id
            _this.$el.find(`#al_zkid>option[value="${$(e.target).val()}"]`).attr("selected", true)
            //获取渲染病人
            WRT_e.api.ehrSz.getPatientListByZkBqidGgcw({
              params: {
                al_zkid: parseInt(_this.$el.find("#al_zkid>option:selected").val()),
                al_bqid: parseInt(_this.$el.find("#al_bqid>option:selected").val())==null ?`0`:parseInt(_this.$el.find("#al_bqid>option:selected").val()),
                as_new: '0',
                as_pathpatient: '0'
              },
              success(data) {
                showPatient(data)
              }
            })
          }
        }
      })
    } else{
      //获取病区信息
      WRT_e.api.ehrSz.getZkdyBq({
        params: {
          al_zkid: parseInt($('#al_zkid option:selected').val()),
        },
        success(data) {
          if (data.Code == 1) {
            //渲染页面
            _this.data.KCLBQ = JSON.parse(data.Result)
            _this.render()
            //记录科室id
            _this.$el.find(`#al_zkid>option[value="${$(e.target).val()}"]`).attr("selected", true)
            //获取渲染病人
            WRT_e.api.ehrSz.getPatientListByZkBqid({
              params: {
                al_zkid: parseInt($('#al_zkid option:selected').val()),
                al_bqid: parseInt($('#al_bqid option:selected').val()),
                as_new: '0',
                as_pathpatient: '0'
              },
              success(data) {
                showPatient(data)
              }
            })
          }
        }
      })
    }
  },
  //切换病区
  changeBqid(e) {
    let _this = this
    if(WRT_config.ehrSz_init.KCLBQ.length==0){
      showPatient({Code:0})
      return
    }
    //加载动画
    openimg()
    if(sel_br == 5){
      //获取渲染病人(公共床位)
      WRT_e.api.ehrSz.getPatientListByZkBqidGgcw({
        params: {
          al_zkid: parseInt(_this.$el.find("#al_zkid>option:selected").val()),
          al_bqid: parseInt(_this.$el.find("#al_bqid>option:selected").val())==null ?`0`:parseInt(_this.$el.find("#al_bqid>option:selected").val()),
          as_new: '0',
          as_pathpatient: '0'
        },
        success(data) {
          showPatient(data)
        }
      })
    } else{
      WRT_e.api.ehrSz.getPatientListByZkBqid({
        params: {
          al_zkid: parseInt($('#al_zkid option:selected').val()),
          al_bqid: parseInt($('#al_bqid option:selected').val()),
          as_new: '0',
          as_pathpatient: '0'
        },
        success(data) {
          showPatient(data)
        }
      })
    }
  },
  //点击检索
  clicksearch(e) {
    let _this = this
    if(WRT_config.ehrSz_init.KCLBQ.length==0){
      showPatient({Code:0})
      return
    }
    //加载动画
    openimg()
    //获取病区信息
    if(sel_br == 5){
      let zkid = parseInt(_this.$el.find("#al_zkid>option:selected").val())
      let zqid = parseInt(_this.$el.find("#al_bqid>option:selected").val())
    
      WRT_e.api.ehrSz.getZkdyBq({
        params: {
          al_zkid: zkid
        },
        success(data) {
          if (data.Code == 1) {
            //渲染页面
            _this.data.KCLBQ = JSON.parse(data.Result)
            _this.render()
            //记录科室id
            _this.$el.find(`#al_zkid>option[value="${zkid}"]`).attr("selected", true)
            _this.$el.find(`#al_bqid>option[value="${zqid}"]`).attr("selected", true)
            //获取渲染病人(公共床位)
            WRT_e.api.ehrSz.getPatientListByZkBqidGgcw({
              params: {
                al_zkid: parseInt(_this.$el.find("#al_zkid>option:selected").val()),
                al_bqid: parseInt(_this.$el.find("#al_bqid>option:selected").val())==null ?`0`:zqid,
                as_new: '0',
                as_pathpatient: '0'
              },
              success(data) {
                showPatient(data)
              }
            })
          }
        }
      })
    } else {
      let zkid = $('#al_zkid option:selected').val()
      let zqid= $('#al_bqid option:selected').val()
      WRT_e.api.ehrSz.getZkdyBq({
        params: {
          al_zkid: parseInt($('#al_zkid option:selected').val()),
        },
        success(data) {
          if (data.Code == 1) {
            //渲染页面
            _this.data.KCLBQ = JSON.parse(data.Result)
            _this.render()
            //记录科室id
            _this.$el.find(`#al_zkid>option[value="${zkid}"]`).attr("selected", true)
            _this.$el.find(`#al_bqid>option[value="${zqid}"]`).attr("selected", true)
            //获取渲染病人
            WRT_e.api.ehrSz.getPatientListByZkBqid({
              params: {
                al_zkid: parseInt($('#al_zkid option:selected').val()),
                al_bqid: parseInt($('#al_bqid option:selected').val()),
                as_new: '0',
                as_pathpatient: '0'
              },
              success(data) {
                showPatient(data)
              }
            })
          }
        }
      })
    }
  }
})

//病人一览表（专科治疗，专科病区）
var brList_View = WRT_e.view.extend({
  render: function () {
    
    this.$el.html(`
    <div class="patient_list">
      ${_.map(this.data.list, (obj) =>
      //循环病人列表
      `<div class="item">
        <div class="item_box ${obj.ZFYE < 0 ? "ZFYE_box" : ""}" data-zydjid="${obj.ZYDJID}" data-zlzid="${obj.ZLZID}" data-BQDM="${obj.BQDM}" data-jslx="${obj.JSLX}" data-zyh="${obj.ZYH}" data-CWH="${obj.CWH}" data-zdym="${obj.ZDYM}" data-brxm="${obj.BRXM}" data-zkid="${obj.ZKID}" data-bqid="${obj.BQID}" data-blid="${obj.BLID}" data-zyid="${obj.ZYID}">
          <div class="tag flex-row justify-content-between">
            <div>
              <span class="e-tag margin_1 ${obj.CRBS ? 'e_custom_red' : 'e_custom_black'}" style="font-size: 14px;padding: 0 2px;">
              ${obj.ZDYM}-${obj.CWH}
              </span>
              ${obj.ZKID!=49&&obj.ZKID!=2961&&sel_br!='5'?`<span class="e-tag margin_1 e_custom_red" style="font-size: 14px;padding: 0 2px;">
                ${daysBetween(obj.BQRYSJ, new Date(),'day', obj.CWRYSJ)}
              </span>`:
              `<span class="e-tag margin_1 e_custom_red" style="font-size: 14px;padding: 0 2px;">
                ${daysBetween(obj.BQRYSJ, new Date(),'hour', obj.CWRYSJ)}
              </span>`}
              ${obj.QCSL && obj.QCSL != 0 ? `<span class="e-tag e_custom_circle_red" style="font-size: 14px;">${obj.QCSL}</span>` : ''}
            </span>
            </div>
            <div>
              ${obj.Limited ? `<span class="e-tag e_custom_red" style="font-size: 14px;padding: 0 2px;">限</span>`:''}
              ${Number(obj.YYFX)<3?``:(Number(obj.YYFX)>=3&&Number(obj.YYFX)<5)?`<span class="e-tag e_custom_orange" style="font-size: 14px;padding: 0 2px;">营</span>`:Number(obj.YYFX)>=5?`<span class="e-tag e_custom_red" style="font-size: 14px;padding: 0 2px;">营</span>`:``}
              ${obj.VTETX ? `<span class="e-tag ${obj.VTETX.indexOf("高危") >= 0 ? 'e_custom_red' : obj.VTETX.indexOf("中危") >= 0 ? "e_custom_orange" : ""}  margin_1" style="font-size: 10px;padding: 0 2px;">${gettitle(obj.VTETX)}</span>` : ""} 
            </div>
          </div>
          <div class="flex-row align-items-center">
            <img src="${obj.BRXB == 1 ? './images/man.png' : './images/woman.png'}" width="24" alt="">
            <div class="flex-fill user_text" style="margin-bottom:0px">
              <strong style="font-size: 13px;color:${getRYSJ(obj.CWRYSJ, obj.BQRYSJ, obj.BKIMG)}">${obj.EMPI}</strong>
              <span class="e-tag e_custom_black margin_1" style="float: right;font-size: 10px;padding: 0 2px;font-weight: 500;">
                ${obj.JSLX == 00 ? '自费' : obj.JSLX == 01 ? '社保' : obj.JSLX == 02 ? '农保' : '工费'}
              </span>
              <br />
              <span style="display: flex; flex-direction: row; flex-wrap: nowrap; width: 180px;align-items: center;">
                <b style="font-size: 13px;">${obj.BRXM}</b>
                <span>&nbsp;&nbsp;${obj.BRXB == 1 ? '男' : '女'}</span>
              </span>
            </div>
            <div style="float:right;margin-left: auto;margin-bottom: 3px;min-width: 30px;min-height:20px;">
              ${obj.DRGZT == "0" ? `<img src="./images/drg/DRG-1-1.png" width=15 height=15 />` : obj.DRGZT == "1"?`<img src="./images/drg/DRG-1-4.png" width=15 height=15 />`:obj.DRGZT == "2"?`<img src="./images/drg/DRG-1-2.png" width=15 height=15 />`:""}
              ${obj.TrafficPatient ? `<img src="./images/道路交通患者.png" width=15 height=15 />` : ``}
              ${obj.BKIMG.indexOf("BL") >= 0 ? `<img src="./images/bl.png" />` : ``}
              ${obj.BKIMG.indexOf("SS") >= 0 ? `<img src="./images/sstzd.png" />` : ``}
              ${obj.BKIMG.indexOf("LCLJ") >= 0 ? `<img src="./images/lclj.jpg" />` : ``}
              ${obj.RJSS == "1" ? `<img src="./images/rjss2.png" width=15 height=15 />` : obj.RJSS == "2" ? `<img src="./images/rjss1.png" width=15 height=15 />`:``}
              ${obj.MDRO ? `<img src="./images/mdro.png" width=17 height=17 />` : ``}
            </div>
          </div>
          <div class="status" style="background:${function () {
            switch (obj.HLJB) {
              case "1"://特级护理
                return '#6000DB'
              case "2"://一级护理
                return '#E20000'
              case "3"://二级护理
                return '#F1D900'
              case "4"://三级护理
                return '#31D742'
              default://无护理级别
                return '#dae6fc'
            }
          }()};"></div>
          <div class="message">${obj.RYZD}<br />${obj.DWMC || ''}</div>
          ${//我的病人选区有删除按钮
            this.data.del ? '<div class="del" type="button" name="deleteMyPatByBlid"><span class="glyphicon glyphicon-trash"></span></div>' : ''
          }
          </div>
        </div>
      `).join('')}
    </div>
    `)
    return this
  },
  events: {
    "click .item_box": "selectBr",
    "click div[name='deleteMyPatByBlid']": "deleteMyPatByBlid",
  },
  selectBr(e) {
    let target = $(e.currentTarget)
    let jslx = `${target.attr('data-jslx') == "00" ? "自费" : target.attr('data-jslx') == "01" ? "社保" : target.attr('data-jslx') == "02" ? "农保" : "公费"}`
    page_iframe.add(`bl_${target.attr("data-blid")}`, `${jslx} ${target.attr('data-zdym')}-${target.attr('data-CWH')} ${target.attr('data-brxm')}`, `e-BrMainLeft.html?ls_idb=${target.attr("data-zyid")}&al_blid=${target.attr("data-blid")}`)
  },
  //通过blid去除我的病人某个病人
  deleteMyPatByBlid(e) {
    let _this = this;
    //阻止事件冒泡
    e.stopPropagation()
    let target = $(e.target).parents('.item_box')
    WRT_e.ui.message({
      title: '信息窗口',
      content: `是否在我的病人里去除${target.attr('data-brxm')}这位病人?`,
      onOk() {
        WRT_e.api.ehrSz.deleteMyPatByBlid({
          params: {
            blid: target.attr('data-blid')
          },
          success(data) {
            if (data.Code == 0) {
              WRT_e.ui.hint({
                type: 'error',
                msg: '去除失败，请重试或重新登录'
              })
            } else if (data.Code == 1) {
              $(`.item_box[data-blid=${target.attr('data-blid')}]`).parent().remove()
            }
          }
        })
      },
      onCancel() { },
    })
  },
})

//病人一览表（治疗组）
var brzlzList_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    ${_.map(this.data.zlz_ar,(item)=>`
    <p style="font-size:16px;font-weight:bold;text-align: center;">${item.MC}</p>
    <div class="patient_list">
      ${_.map(this.data.list, (obj) =>
      //循环病人列表
      obj.ZLZID==item.ZLZID?`<div class="item">
          <div class="item_box ${obj.ZFYE < 0 ? "ZFYE_box" : ""}" data-zydjid="${obj.ZYDJID}" data-BQDM="${obj.BQDM}" data-jslx="${obj.JSLX}" data-zyh="${obj.ZYH}" data-CWH="${obj.CWH}" data-zdym="${obj.ZDYM}" data-brxm="${obj.BRXM}" data-zkid="${obj.ZKID}" data-bqid="${obj.BQID}" data-blid="${obj.BLID}" data-zyid="${obj.ZYID}">
            <div class="tag flex-row justify-content-between">
              <div>
                <span class="e-tag margin_1 ${obj.CRBS ? 'e_custom_red' : 'e_custom_black'}" style="font-size: 14px;padding: 0 2px;">
                ${obj.ZDYM}-${obj.CWH}
                </span>
                ${obj.ZKID!=49?`<span class="e-tag margin_1 e_custom_red" style="font-size: 14px;padding: 0 2px;">
                  ${daysBetween(obj.BQRYSJ, new Date(),'day', obj.CWRYSJ)}
                </span>`:
                `<span class="e-tag margin_1 e_custom_red" style="font-size: 14px;padding: 0 2px;">
                  ${daysBetween(obj.BQRYSJ, new Date(),'hour', obj.CWRYSJ)}
                </span>`}
                ${obj.QCSL && obj.QCSL != 0 ? `<span class="e-tag e_custom_circle_red" style="font-size: 14px;">${obj.QCSL}</span>` : ''}
              </span>
              </div>
              <div>
              ${obj.Limited ? `<span class="e-tag e_custom_red" style="font-size: 14px;padding: 0 2px;">限</span>`:''}
                ${obj.VTETX ? `<span class="e-tag ${obj.VTETX.indexOf("高危") >= 0 ? 'e_custom_red' : obj.VTETX.indexOf("中危") >= 0 ? "e_custom_orange" : ""}  margin_1" style="font-size: 10px;padding: 0 2px;">${gettitle(obj.VTETX)}</span>` : ""}
                
              </div>
            </div>
            <div class="flex-row align-items-center">
              <img src="${obj.BRXB == 1 ? './images/man.png' : './images/woman.png'}" width="24" alt="">
              <div class="flex-fill user_text">
                <strong style="font-size: 13px;color:${getRYSJ(obj.CWRYSJ, obj.BQRYSJ, obj.BKIMG)}">${obj.EMPI||''}</strong>
                <span class="e-tag e_custom_black margin_1" style="float: right;font-size: 10px;padding: 0 2px;font-weight: 500;">
                ${obj.JSLX == 00 ? '自费' : obj.JSLX == 01 ? '社保' : obj.JSLX == 02 ? '农保' : '工费'}
                </span>
                <br />
                <b style="font-size: 13px;">${obj.BRXM}</b>&nbsp;&nbsp;${obj.BRXB == 1 ? '男' : '女'}
              </div>
              <div>
              ${obj.DRGZT == "0" ? `<img src="./images/drg/DRG-1-1.png" width=15 height=15 />` : obj.DRGZT == "1"?`<img src="./images/drg/DRG-1-4.png" width=15 height=15 />`:obj.DRGZT == "2"?`<img src="./images/drg/DRG-1-2.png" width=15 height=15 />`:""}
              ${obj.TrafficPatient ? `<img src="./images/道路交通患者.png" width=15 height=15 />` : ``}
              ${obj.BKIMG.indexOf("BL") >= 0 ? `<img src="./images/bl.png" />` : ``}
              ${obj.BKIMG.indexOf("SS") >= 0 ? `<img src="./images/sstzd.png" />` : ``}
              ${obj.BKIMG.indexOf("LCLJ") >= 0 ? `<img src="./images/lclj.jpg" />` : ``}
              ${obj.RJSS == "1" ? `<img src="./images/rjss2.png" width=15 height=15 />` : obj.RJSS == "2" ? `<img src="./images/rjss1.png" width=15 height=15 />`:``}
              ${obj.MDRO ? `<img src="./images/mdro.png" width=17 height=17 />` : ``}
              </div>
            </div>
            <div class="status" style="background:${function () {
        switch (obj.HLJB) {
          case "1"://特级护理
            return '#6000DB'
          case "2"://一级护理
            return '#E20000'
          case "3"://二级护理
            return '#F1D900'
          case "4"://三级护理
            return '#31D742'
          default://无护理级别
            return '#dae6fc'
        }
      }()};"></div>
            <div class="message">${obj.RYZD}<br />${obj.DWMC || ''}</div>
            ${//我的病人选区有删除按钮
      this.data.del ? '<div class="del" type="button" name="deleteMyPatByBlid"><span class="glyphicon glyphicon-trash"></span></div>' : ''
      }
          </div>
        </div>
      `:'').join('')}
    </div>
    `)}
    `)
    return this
  },
  events: {
    "click .item_box": "selectBr",
    "click div[name='deleteMyPatByBlid']": "deleteMyPatByBlid",
  },
  selectBr(e) {
    let target = $(e.currentTarget)
    let jslx = `${target.attr('data-jslx') == "00" ? "自费" : target.attr('data-jslx') == "01" ? "社保" : target.attr('data-jslx') == "02" ? "农保" : "公费"}`
    page_iframe.add(`bl_${target.attr("data-blid")}`, `${jslx} ${target.attr('data-zdym')}-${target.attr('data-CWH')} ${target.attr('data-brxm')}`, `e-BrMainLeft.html?ls_idb=${target.attr("data-zyid")}&al_blid=${target.attr("data-blid")}`)
  },
  //通过blid去除我的病人某个病人
  deleteMyPatByBlid(e) {
    let _this = this;
    //阻止事件冒泡
    e.stopPropagation()
    let target = $(e.target).parents('.item_box')
    WRT_e.ui.message({
      title: '信息窗口',
      content: `是否在我的病人里去除${target.attr('data-brxm')}这位病人?`,
      onOk() {
        WRT_e.api.ehrSz.deleteMyPatByBlid({
          params: {
            blid: target.attr('data-blid')
          },
          success(data) {
            if (data.Code == 0) {
              WRT_e.ui.hint({
                type: 'error',
                msg: '去除失败，请重试或重新登录'
              })
            } else if (data.Code == 1) {
              $(`.item_box[data-blid=${target.attr('data-blid')}]`).parent().remove()
            }
          }
        })
      },
      onCancel() { },
    })
  },
})

//病人一览表（我的）
var brbqList_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    ${_.map(this.data.bq_ar,(item)=>`
    <p style="font-size:16px;font-weight:bold;text-align: center;">${item.BQMC}</p>
    <div class="patient_list">
      ${_.map(this.data.list, (obj) =>
      //循环病人列表
      obj.BQID==item.BQID?`<div class="item">
          <div class="item_box ${obj.ZFYE < 0 ? "ZFYE_box" : ""}" data-zydjid="${obj.ZYDJID}" data-BQDM="${obj.BQDM}" data-jslx="${obj.JSLX}" data-zyh="${obj.ZYH}" data-CWH="${obj.CWH}" data-zdym="${obj.ZDYM}" data-brxm="${obj.BRXM}" data-zkid="${obj.ZKID}" data-bqid="${obj.BQID}" data-blid="${obj.BLID}" data-zyid="${obj.ZYID}">
            <div class="tag flex-row justify-content-between">
              <div>
                <span class="e-tag margin_1 ${obj.CRBS ? 'e_custom_red' : 'e_custom_black'}" style="font-size: 14px;padding: 0 2px;">
                ${obj.ZDYM}-${obj.CWH}
                </span>
                ${obj.ZKID!=49?`<span class="e-tag margin_1 e_custom_red" style="font-size: 14px;padding: 0 2px;">
                  ${daysBetween(obj.BQRYSJ, new Date(),'day', obj.CWRYSJ)}
                </span>`:
                `<span class="e-tag margin_1 e_custom_red" style="font-size: 14px;padding: 0 2px;">
                  ${daysBetween(obj.BQRYSJ, new Date(),'hour', obj.CWRYSJ)}
                </span>`}
                ${obj.QCSL && obj.QCSL != 0 ? `<span class="e-tag e_custom_circle_red" style="font-size: 14px;">${obj.QCSL}</span>` : ''}
              </span>
              </div>
              <div>
              ${obj.Limited ? `<span class="e-tag e_custom_red" style="font-size: 14px;padding: 0 2px;">限</span>`:''}
                ${obj.VTETX ? `<span class="e-tag ${obj.VTETX.indexOf("高危") >= 0 ? 'e_custom_red' : obj.VTETX.indexOf("中危") >= 0 ? "e_custom_orange" : ""}  margin_1" style="font-size: 10px;padding: 0 2px;">${gettitle(obj.VTETX)}</span>` : ""}
                
              </div>
            </div>
            <div class="flex-row align-items-center">
              <img src="${obj.BRXB == 1 ? './images/man.png' : './images/woman.png'}" width="24" alt="">
              <div class="flex-fill user_text">
                <strong style="font-size: 13px;color:${getRYSJ(obj.CWRYSJ, obj.BQRYSJ, obj.BKIMG)}">${obj.EMPI||''}</strong>
                <span class="e-tag e_custom_black margin_1" style="float: right;font-size: 10px;padding: 0 2px;font-weight: 500;">
                ${obj.JSLX == 00 ? '自费' : obj.JSLX == 01 ? '社保' : obj.JSLX == 02 ? '农保' : '工费'}
                </span>
                <br />
                <b style="font-size: 13px;">${obj.BRXM}</b>&nbsp;&nbsp;${obj.BRXB == 1 ? '男' : '女'}
              </div>
              <div>
              ${obj.DRGZT == "0" ? `<img src="./images/drg/DRG-1-1.png" width=15 height=15 />` : obj.DRGZT == "1"?`<img src="./images/drg/DRG-1-4.png" width=15 height=15 />`:obj.DRGZT == "2"?`<img src="./images/drg/DRG-1-2.png" width=15 height=15 />`:""}
              ${obj.TrafficPatient ? `<img src="./images/道路交通患者.png" width=15 height=15 />` : ``}
              ${obj.BKIMG.indexOf("BL") >= 0 ? `<img src="./images/bl.png" />` : ``}
              ${obj.BKIMG.indexOf("SS") >= 0 ? `<img src="./images/sstzd.png" />` : ``}
              ${obj.BKIMG.indexOf("LCLJ") >= 0 ? `<img src="./images/lclj.jpg" />` : ``}
              ${obj.RJSS == "1" ? `<img src="./images/rjss2.png" width=15 height=15 />` : obj.RJSS == "2" ? `<img src="./images/rjss1.png" width=15 height=15 />`:``}
              ${obj.MDRO ? `<img src="./images/mdro.png" width=17 height=17 />` : ``}
              </div>
            </div>
            <div class="status" style="background:${function () {
        switch (obj.HLJB) {
          case "1"://特级护理
            return '#6000DB'
          case "2"://一级护理
            return '#E20000'
          case "3"://二级护理
            return '#F1D900'
          case "4"://三级护理
            return '#31D742'
          default://无护理级别
            return '#dae6fc'
        }
      }()};"></div>
            <div class="message">${obj.RYZD}<br />${obj.DWMC || ''}</div>
            ${//我的病人选区有删除按钮
      this.data.del ? '<div class="del" type="button" name="deleteMyPatByBlid"><span class="glyphicon glyphicon-trash"></span></div>' : ''
      }
          </div>
        </div>
      `:'').join('')}
    </div>
    `)}
    `)
    return this
  },
  events: {
    "click .item_box": "selectBr",
    "click div[name='deleteMyPatByBlid']": "deleteMyPatByBlid",
  },
  selectBr(e) {
    let target = $(e.currentTarget)
    let jslx = `${target.attr('data-jslx') == "00" ? "自费" : target.attr('data-jslx') == "01" ? "社保" : target.attr('data-jslx') == "02" ? "农保" : "公费"}`
    page_iframe.add(`bl_${target.attr("data-blid")}`, `${jslx} ${target.attr('data-zdym')}-${target.attr('data-CWH')} ${target.attr('data-brxm')}`, `e-BrMainLeft.html?ls_idb=${target.attr("data-zyid")}&al_blid=${target.attr("data-blid")}`)
  },
  //通过blid去除我的病人某个病人
  deleteMyPatByBlid(e) {
    let _this = this;
    //阻止事件冒泡
    e.stopPropagation()
    let target = $(e.target).parents('.item_box')
    WRT_e.ui.message({
      title: '信息窗口',
      content: `是否在我的病人里去除${target.attr('data-brxm')}这位病人?`,
      onOk() {
        WRT_e.api.ehrSz.deleteMyPatByBlid({
          params: {
            blid: target.attr('data-blid')
          },
          success(data) {
            if (data.Code == 0) {
              WRT_e.ui.hint({
                type: 'error',
                msg: '去除失败，请重试或重新登录'
              })
            } else if (data.Code == 1) {
              $(`.item_box[data-blid=${target.attr('data-blid')}]`).parent().remove()
            }
          }
        })
      },
      onCancel() { },
    })
  },
})


//病人日志
// <i class="glyphicon glyphicon-list-alt"></i><i class="glyphicon glyphicon-chevron-down"></i>
var brPortal_View = WRT_e.view.extend({
  render: function () {
    let BRCY = 0
    if (this.data.GetBrcyblbg) {
      BRCY = this.data.GetBrcyblbg
      if (Array.isArray(this.data.GetBrcyblbg)) {
        BRCY = this.data.GetBrcyblbg.length
      }
    }
    let ICUBRQK=[]
    if(this.data.ICUBRQK==null){
      this.data.ICUBRQK="#病人病情已稳定，请于12小时内转回本专科，逾期扣罚绩效500元/例"
    }
    if(this.data.ICUBRQK){
      let fd=this.data.ICUBRQK.split("##")
      fd.map(function(item){
        const td=item.split("#")
        if(td[0]){
          ICUBRQK.push({name:td[0],zyh:td[1],cwh:td[2]})
        }else{
          ICUBRQK.push({name:td[1],zyh:td[2],cwh:td[3]})
        }
      })
    }
    let str=this.data.YXPSJG.replace(/[+][#]$/g, '').replace(/[+][#]/g, ';').replace(/[+]/g, ',')
    let arr=str.split(";")
    let YXPSRS=[]
    arr.map(function(item){
      if(item){
        let fd=item.split(" ")
        YXPSRS.push({no:fd[0],text:fd[1]})
      }
    })
    // 原3216行现3222   <span class="panel-title-text">终末质控病历反馈列表<b>${WRT_config.DataSimple.e_GetYjblFromLtzk ? WRT_config.DataSimple.e_GetYjblFromLtzk : 0}人</b></span>
    let html = `
      <div class="patient_info">
        <div class="panel-group" id="accordion">
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title"
                data-toggle="collapse" data-parent="#accordion" style="padding:0px 10px">
                <span class="" stype="margin-left: 0px;">专科日志</span>
                <select id="sel_bq" placeholder="选择病区" onchange="selectPortal()" style="width:78px;color: #555555;font-size: 14px;height: 22px;">
                <option value="">选择病区</option>
                ${_.map(WRT_config.ehrSz_init.KCLBQ, (obj, index) => `<option  value=${obj.bqid} ${sel_bq == obj.bqid ? 'selected' : ''}>${obj.zdym}</option>`)}
                </select>
                <select id="sel_zlz" placeholder="选择治疗组" onchange="selectPortal()" style="width:123px;color: #555555;font-size: 14px;height: 22px;">
                <option  value="">选择治疗组</option>
                ${_.map(WRT_config.ehrSz_init.ZLZ, (obj) => `<option value=${obj.ID} ${sel_zlz == obj.ID ? 'selected' : ''}>${obj.MC}</option>`)}
                </select>
              </a>
            </div>
            <div style="display: block;max-height: 345px;">
              <div class="panel-body" style="height: 100%;">
                <div class="patient_info_one">
                  <div class="row">
                    <div class="col-md-4">
                      <div class="box">
                        <div class="box_label">在院</div>
                        <div class="box_value">${this.data.ZYRS}</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="box">
                        <div class="box_label">新入院</div>
                        <div class="box_value">${this.data.XRYRS}</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="box">
                        <div class="box_label">出院</div>
                        <div class="box_value">${this.data.CYRS}</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="box">
                        <div class="box_label">转入</div>
                        <div class="box_value">${this.data.ZRRS}</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="box">
                        <div class="box_label">转出</div>
                        <div class="box_value">${this.data.ZCRS}</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="box">
                        <div class="box_label">病危病人</div>
                        <div class="box_value red">${this.data.WZBRRS}</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="box">
                        <div class="box_label">特级护理</div>
                        <div class="box_value">${this.data.TJHLRS}</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="box">
                        <div class="box_label">一级护理</div>
                        <div class="box_value">${this.data.YJHLRS}</div>
                      </div>
                    </div>
                    <div class="col-md-4">
                      <div class="box">
                        <div class="box_label">二级护理</div>
                        <div class="box_value">${this.data.EJHLRS}</div>
                      </div>
                    </div>
                  </div>
                  <div style="font-weight: bold;text-align: left;margin-top: 3px;"><a class="title_hover all_message">消息提示</a></div>
                  <div style="font-weight: bold;text-align: left;"><a class="title_hover brcy">出院病人病理结果提醒(${BRCY||''})</a></div>
                  <div id="grjkpop_title" style="font-weight: bold;text-align: left;${WRT_config.grjkpoplis ? "" : "display:none"}"><a class="title_hover grjkpop">院感监控预警(${WRT_config.grjkpoplis ? WRT_config.grjkpoplis.length : 0})</a></div>
                  <div id="DSJYC_title" style="font-weight: bold;text-align: left;${WRT_config.DSJYC&&WRT_config.DSJYC.res==1 ? "" : "display:none"}"><a class="title_hover DSJYC">本病区本周有病人AKI、CKD发生概率超过90%</a></div>
                </div>
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel1" id="heading1" onclick="changeitem('1')">
                <span>
                  <i class="glyphicon glyphicon-bell"></i>
                  <span class="panel-title-text">临床辅助决策系统提示：<b>${WRT_config.DataSimple.e_hljlmes}条</b></span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel1" class="panel-collapse collapse">
              <div class="panel-body">
                <table id="hljlmes_lists" style="font-weight: 700;">
                  ${WRT_config.DataSimple.e_hljlmes == 0 ?'':`<img  class="load_img" src="./images/load.gif" style="width: 160px;" />`}                
                </table>
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel2" id="heading2" onclick="changeitem('2')">
                <span>
                  <i class="iconfont icon-shoushurenci"></i>
                  <span class="panel-title-text">今日手术病人：<b>${this.data.DTSSRS}人</b></span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel2" class="panel-collapse collapse">
              <div class="panel-body">
                <b>
                ${this.data.DTSSBR.replace(/[#][#]/g, '<br/>').replace(/^[#]/g, '').replace(/[#]$/g, '').replace(/[#]/g, '<br/><span style="display:inline-block;width:63px;"></span>')}
                </b>
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel3" id="heading3" onclick="changeitem('3')">
                <span>
                  <i class="iconfont icon-yiliao2"></i>
                  <span class="panel-title-text">今日到期抗生素病人：<b>${this.data.DQKSSRS}人</b></span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel3" class="panel-collapse collapse">
              <div class="panel-body">
                <b>
                ${this.data.DQKSSBR.replace(/[#][#]/g, '<br/>').replace(/^[#]/g, '').replace(/[#]$/g, '').replace(/[#]/g, '<br/><span style="display:inline-block;width:63px;"></span>')}
                </b>
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel4" id="heading4" onclick="changeitem('4')">
                <span>
                  <i class="iconfont icon-mianxingbingrentubiao3"></i>
                  <span class="panel-title-text">阳性皮试结果病人：<b>${this.data.YXPSRS}人</b></span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel4" class="panel-collapse collapse">
              <div class="panel-body">
              <table style="font-weight: 700;">
              ${_.map(YXPSRS,obj=>
                `
                <tr>
                  <td style="width:60px;vertical-align: text-top;">${obj.no} </td><td>${obj.text}</td>
                </tr>
                `
                ).join("")}
                
              </table>
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel5" id="heading5" onclick="changeitem('5')">
                <span>
                  <i class="iconfont icon-huizhen"></i>
                  <span class="panel-title-text">申请会诊病人：<b>${this.data.SQHZRS}人</b></span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel5" class="panel-collapse collapse">
              <div class="panel-body">
                <a id="showHzd" href="javascript:;"><b>${this.data.SQHZQK}</b></a> 
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel6" id="heading6" onclick="changeitem('6')">
                <span>
                  <i class="iconfont icon-chuyuanrenci"></i>
                  <span class="panel-title-text">ICU转出病人：<b>${this.data.ICUBRSL}人</b></span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel6" class="panel-collapse collapse">
              <div class="panel-body">
                <table>
                ${_.map(ICUBRQK,(obj,index)=>
                  `${ICUBRQK.length-1===index?``:`<tr><td style="width:60px;vertical-align: text-top;">${obj.name} </td><td style="width:80px;vertical-align: text-top;">${obj.zyh} </td><td>${obj.cwh}</td></tr>`}`
                  ).join("")}
                  </table>
                  <div>${ICUBRQK[ICUBRQK.length-1].name}</div>
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel69" id="heading69" onclick="changeitem('69')">
                <span>
                  <i class="iconfont icon-chuyuanrenci"></i>
                  <span class="panel-title-text">未填报单病种：<b>${this.data.GetUnfilledInfo}</b></span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel69" class="panel-collapse collapse">
              <div class="panel-body">
              <button class="wtbbd e_btn">跳转</button>
              <table id="wtbdbz_lists" style="font-weight: 700;">
                <img  class="load_img" src="./images/load.gif" style="width: 160px;" />
              </table>
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel7" id="heading7" onclick="changeitem('7')">
                <span>
                  <i class="iconfont icon-zhuyuanjilu"></i>
                  <span class="panel-title-text">超2天医生未归档的病人：<b>${this.data.WGDBL ? this.data.WGDBL : 0}人</b></span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel7" class="panel-collapse collapse">
              <div class="panel-body">
                <table class="table">
                  <tbody id="wgdbr_lists">
                  <img class="load_img" src="./images/load.gif" style="width: 160px;" />
                  </tbody>
                </table>
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel9" id="heading9" onclick="changeitem('9')">
                <span>
                  <i class="iconfont icon-zhuyuanjilu"></i>
                  <span class="panel-title-text" style="cursor: pointer;" onclick="SSO_click()">病历质控</b></span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel9" class="panel-collapse collapse">
              <div class="panel-body" style="padding:5px;">
                <div id="YjblFromLtzk_lists">
                  <img class="load_img1" src="./images/load.gif" style="width: 160px;" />
                </div>
              </div>
            </div>
          </div>
          <div class="panel">
            <div class="panel-heading">
              <a class="flex-row align-items-center justify-content-between panel-title "
                data-toggle="collapse" data-parent="#accordion" href="#panel8">
                <span>
                  <i class="iconfont icon-ziyuan"></i>
                  <span class="panel-title-text">图标说明</span>
                </span>
                <i class="glyphicon glyphicon-chevron-down"></i>
                <i class="glyphicon glyphicon-chevron-up" style="display:none"></i>
              </a>
            </div>
            <div id="panel8" class="panel-collapse collapse">
              <div class="panel-body">
                <button class="e_btn ImageDlg">查看图片</button>
                <img src="./images/说明1X.png" width="300px">
              </div>
            </div>
          </div>
        </div>
      </div>`
    this.$el.html(html)
    return this
  },
  events: {
    "click  .brcy": "panel1_brcy",
    "click #panel7 table tr>td>a": "clickPanel7",
    "click #panel9 table tr>td>a": "clickPanel9",
    "click #showHzd": "showHzd",
    "click .ImageDlg": "openImageDlg",
    "click .DSJYC": "DSJYC",
    "click .grjkpop": "grjkpop",
    "click .wtbbd": "wtbbd",
    "click .all_message": "all_message"
  },
  //获取所有消息提示
  all_message(){
    getallmessage()
  },
  //未填报单病种
  wtbbd(){
      let iWidth = 1000;
      let iHeight = 800;
      let iTop = (window.screen.availHeight - 30 - iHeight) / 2;
      let iLeft = (window.screen.availWidth - 10 - iWidth) / 2;
      let ls_url =`${WRT_config.server}/newdbz/JumpToDBZ.aspx?openmode=ehr3&as_openmode=ehr3`
      window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
        
    // page_iframe.add("wtbbd", `未填报单病种`, `${WRT_config.server}/newdbz/JumpToDBZ.aspx?openmode=ehr3&as_openmode=ehr3`)
  },
  //院感监控预警列表·
  grjkpop() {
    grjkpopHtml()
  },
  //本病区本周有病人AKI、CKD发生概率超过90
  DSJYC() {
    if (WRT_config.DSJYC.res==1) {
      // page_iframe.add('if_DSJYC',"大数据预测", WRT_config.DSJYC.URL);
      
      page_iframe.add('if_DSJYC',"大数据预测", '../'+WRT_config.DSJYC.Url);
    }
  },
  panel1_brcy() {
    // var j_url = WRT_config.server+"/zybl_tjsj/zybrjctx.aspx?as_new=1";
    page_iframe.add("zybrjctx", `出院病人病理结果提醒`, `${WRT_config.server}/zybl_tjsj/zybrjctx.aspx?as_new=1`)
  },
  showHzd() {
    page_iframe.add(`hzsqd`, "会诊单管理", WRT_config.server + `/zyblhzd/hzsqd.aspx?openmode=ehr3&as_openmode=ehr3`)
  },
  clickPanel7(e) {
    let target = $(e.currentTarget)
    let jslx = `${target.attr('data-jslx') == "00" ? "自费" : target.attr('data-jslx') == "01" ? "社保" : target.attr('data-jslx') == "02" ? "农保" : "公费"}`
    page_iframe.add(`bl_${target.attr("data-blid")}`, `${jslx} ${target.attr('data-zdym')}-${target.attr('data-CWH')} ${target.attr('data-brxm')}`, `e-BrMainLeft.html?ls_idb=${target.attr("data-zyid")}&al_blid=${target.attr("data-blid")}&${Math.random()}`)
  },
  clickPanel9(e) {
    let target = $(e.currentTarget)
    page_iframe.add(`bl_${target.attr("data-blid")}`, `${target.attr('data-zdym')}-${target.attr('data-CWH')} ${target.attr('data-brxm')}`, `e-BrMainLeft.html?ls_idb=${target.attr("data-zyid")}&al_blid=${target.attr("data-blid")}&${Math.random()}`)
  },
  openImageDlg() {
    WRT_e.ui.model({
      id: 'if_ImageDlg',
      title: '图片',
      width: "550px",
      content: `<img src="./images/说明2X.png" width="500px" height="500px">`,
      iframe: false,
    })
  }
})

//空状态
var empty_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    <div class="e_empty">
      <img class="e_empty_image" src="https://user-images.githubusercontent.com/507615/54591670-ac0a0180-4a65-11e9-846c-e55ffce0fe7b.png" alt="">
      <p class="e_empty_description">没有我的病人</p>
    </div>
    `)
    return this
  },
  events: {

  },
})



//滚动公告
var notice_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
      <div class="notice_inner">
        <div class="text">
          <marquee onmouseover="this.stop()" onmouseout="this.start()" scrollamount="3" scrolldelay="1" direction="left" align="center">
          ${_.map(this.data, (obj, index) =>
      `<span style="margin-left:15px;">${index + 1}、${obj.XXBT}<span style="font-size:16px;">[${obj.G_MC}](${obj.XGSJ.split('T')[0]})</span></span>`
    ).join('')}
          </marquee>
        </div>
        <a href="#" class="more">【查看更多】</a>
      </div>
    `)
    return this
  },
  events: {
    'click .notice_inner>.more': "toMore",
  },
  toMore() {
    page_iframe.add(`MessageList`, `消息列表`, `${WRT_config.server}/MessageList.aspx`)
  }
})