WRT_e.api = WRT_e.api || {}
WRT_e.api.blnrjghcxNew = {
  // 住院文书
  // 初始化接口
  getData:function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/blnrjghcx_New.aspx/e_init',
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }

  },
  // 获取文书列表
  getWsListData: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/blnrjghcx_New.aspx/e_GetWs',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          } else {
            if (error.statusText == 'Internal Server Error') {
              WRT_e.ui.hint({
                type: 'error',
                msg: JSON.parse(error.responseText).Message
              })
            }
          }
        }
      })
    }
  },  
  // 获取某份文书的字段列表(住院和门诊通用)
  getWsgs: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/blnrjghcx_New.aspx/e_GetWsgs',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  }, 
  // 查寻数据
  getBrLb: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/blnrjghcx_New.aspx/e_GetBrLb',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  }, 

  // 门诊文书
  // 初始化接口
  getDataMz:function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/blnrjghcx_New.aspx/e_initMz',
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }

  },
  // 获取文书列表
  getMzWsListData: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/blnrjghcx_New.aspx/e_GetWsMz',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          } else {
            if (error.statusText == 'Internal Server Error') {
              WRT_e.ui.hint({
                type: 'error',
                msg: JSON.parse(error.responseText).Message
              })
            }
          }
        }
      })
    }
  },  
  // 查寻数据
  getBrLbMz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/blnrjghcx_New.aspx/e_GetBrLbMz',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  }, 
}