WRT_e.api = WRT_e.api || {}

WRT_e.api.yz_sz = {
  //初始化
  getinit: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:JSON.stringify(yz_sz)
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_init',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  getDateString: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:JSON.stringify(yz_sz)
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_GetDateString',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取频率、用法、给药方法
  getYpyfpl: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_GetYpyfpl',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        async:false,
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取专科药房
  getZkYfdms: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_GetZkYfdms',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取药物过敏信息
  getYwgm: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_GetYwgm',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取执行病区列表
  getZxbqs: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_GetZxbqs',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取手术通知单列表
  getSstzd: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_getSstzd',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取社保限量支付药品
  getSbXlzfYp: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_getSbXlzfYp',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //特殊药物备案
  getBrxzqh: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_getBrxzqh',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //校验一次用量
  CheckYcyl: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_CheckYcyl',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取搜索医嘱项目
  getItemList: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_getItemList',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取药品常用量
  getYpJbsyff: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:"[]"
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_getYpJbsyff',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取已提交医嘱
  getBrYz: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz_arr
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_getBrYz',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取未提交医嘱
  getWtjYz: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_GetWtjYz',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //药品附加信息
  getYpFjxx: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_GetYpFjxx',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //药品常用量
  getYpJbsyff: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_GetYpJbsyff',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //有无出院录
  checkCyl: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_checkCyl',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //保存医嘱
  SaveYz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_SaveYz',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //校验医嘱的病区
  checkZxbq: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_checkZxbq',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //提交医嘱
  SubmitYz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result:yz_sz
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_SubmitYz',
        type: 'POST',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //全停医嘱
  stopCqYz: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_StopCqYz',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //停止医嘱
  stopYz: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_StopYz',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //明日8点停医嘱
  stopAt8Tomorrow: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_StopAt8Tomorrow',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //撤回医嘱
  disSubmitYz: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_DisSubmitYz',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //最后一次抗菌药物是否是术后预防
  checkYFYY: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_checkYFYY',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //5天内开过的草药
  getBrLsCy: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yzmb.aspx/e_GetBrLsCy',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //历史用药模板
  getLsyyMb: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/getBrlsyy.aspx/e_GetLsyyMb',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取药品模板
  getYpMbByLb: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yzmb.aspx/e_GetYpMbByLb',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  
  //获取药品模板搜索
  GetNewWgyp: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_GetNewWgyp',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //对应中选药品
  getZxypByFzxyp: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_getZxypByFzxyp',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //计算持续天数
  GetYYTS: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/ypfjxx.aspx/e_GetYYTS',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //合理用药检查
  hlyyCheck2: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/hlyy/hlyy.aspx/e_hlyyCheck2',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //药品模板明细
  GetYpMbDetail: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yzmb.aspx/e_GetYpMbDetail',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //通用汤剂模板
  GetTytjMb: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/getTytj.aspx/e_GetTytjMb',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取治疗模板
  getZlMb: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/zyyz/zlyzsr_mbnewSZ.aspx/e_getZlMb',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取治疗模板
  getZlMbMx: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/zyyz/zlyzsr_mbnewSZ.aspx/e_getZlMbMx',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取最近的药品医嘱（模板）
  GetYpByTime: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yzmb.aspx/e_GetYpByTime',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //保存为药品模板
  SaveAsYpMb: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yzmb.aspx/e_SaveAsYpMb',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //临时医嘱执行
  Lsyzzx: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_Lsyzzx',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //撤销医嘱执行
  yzzxcx: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yz_sz.aspx/e_yzzxcx',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //审方双签名
  submitSqm: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      
    }else{
      $.ajax({
        url: WRT_config.server+'/yz_sz/yzmb.aspx/e_submitSqm',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).Message
          })
        }
      })
    }
  },
  //获取综合模板
  getZhmbByLb: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/zhmbsr.aspx/e_getZhmbByLb',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取综合模板明细
  getZhMbmx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/zhmbsr.aspx/e_getZhMbmx',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //保存排序接口
  saveYzbtGxh: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_saveYzbtGxh',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取中成药辩证信息
  GetZybzxx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/ypfjxx.aspx/e_GetZybzxx',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //术前医嘱自动开皮试医嘱
  checkSqyz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_checkSqyz',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //开药时需选择处方诊断获取病人诊断接口
  GetBrzd: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/ypfjxx.aspx/e_GetBrzd',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //出院带药预计出院修改
  checkJrcy: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_checkJrcy',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //麻醉药品用途
  GetMzyt: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/ypfjxx.aspx/e_GetMzyt',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //草药带药新增快递功能
  getZykddz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_getZykddz',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取省
  getProvince: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_getProvince',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取市
  getCity: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_getCity',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取区
  getCounty: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_getCounty',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //保存快递地址
  saveZykddz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_saveZykddz',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //康复治疗医嘱开立提醒
  GetZlFjxx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/ypfjxx.aspx/e_GetZlFjxx',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //判断是否已有饮食医嘱
  GetYsFjxx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/ypfjxx.aspx/e_GetYsFjxx',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //医嘱已收费医生执行撤销时给予弹窗提醒
  checkYpSfzt: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_checkYpSfzt',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //规范禁食患者用药
  checkKfy: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_checkKfy',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //长期新增自动生成临时医嘱功能
  copyToLsyz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/copyToLsyz',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //血透闭环
  getXtbhdz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/txjlbycqzlyzid',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        crossDomain: true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //医嘱立即停时检查是否已冲配
  getJingPeiZtByZh: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/medicaladvice/v1/DrugInpatient/getJingPeiZtByZh',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
  },
  //获取病人最新身高体重
  getLastSGTZ: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/nursingservice/v1/ShengMingTZ/getLastSGTZ?bingLiID='+o.params.bingLiID,
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
  },
  //保存病人最新身高体重
  svaeShenGaoTZ: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+'/nursingservice/v1/ShengMingTZ/svaeShenGaoTZ',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
  },
  //保存病人最新身高体重
  getLiShiXdfw: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+`/medicaladvice/v1/DrugInpatient/getLiShiXdfw?bingAnHao=${o.params.bingAnHao}&jieShuSJ=${o.params.jieShuSJ}&&kaiShiSJ=${o.params.kaiShiSJ}&waiGouYP=${o.params.waiGouYP}&yaoPinID=${o.params.yaoPinID}`,
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
  },
  //保存病人最新身高体重
  getDrugUsageById: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+`/drugs/v1/drugInfo/getDrugUsageById?yaoPinID=${o.params.yaoPinID}`,
        type: 'get',
        dataType: "json",
        // data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
  },
  //根据病人基本信息查询患者（前端只取第2至4条记录）
  getBinRenXXNoQuanXianByParam: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+`/patient/v1/ezyblbr/getBinRenXXNoQuanXianByParam?param=${o.params.param}&type=1`,
        type: 'get',
        dataType: "json",
        // data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
  },
  saveCrrtYz: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+`/medicaladvice/v1/AdviceInpatient/saveCrrtYz`,
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
  },
  //提交时校验执行病区
  getZhuanChuangJL: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.server2+`/medicaladvice/v1/AdviceInpatient/getZhuanChuangJL?bingLiID=`+o.params.bingLiID,
        type: 'get',
        dataType: "json",
        // data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: JSON.parse(error.responseText).errorMessage
          })
        }
      })
  },
  //
  //提交时校验执行病区
  getResults: function (o = {}) {
    o.params = o.params || {}
      $.ajax({
        url:  WRT_config.serverhm+`/cdss/api/outer/mc/intelligent/rule/results`,
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          // XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("Huimei_id", "7195F12825788F09375C2DB1E922F108");
        },
        // cache:false,
        // crossDomain:true, //设置跨域为true
        // xhrFields: {
        //   withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        // },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.error) o.error(error)
          WRT_e.ui.hint({
            type: 'error',
            msg: error.message||"惠美请求失败，请联系信息处！"
          })
        }
      })
  },
}