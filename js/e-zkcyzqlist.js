let medol_form={}//修改
var input_index=null
var all_menu=[]
let url = window.location.href.split("?") || []
let text = url[1].split("&")
let params = {}
for (let i of text) {
  let fd = i.split("=")
  params[fd[0]] = fd[1]
}
// console.log(params)
var obj1 = []
// 统一页面启动
$(document).ready(() => {
  // $(".title").html(decodeURI(params["as_zkmc"])||"")
  WRT_e.api.zkcyzqlist.getAllWenShu({
    params:{},
    success(msg){
      if(msg.data){
        WRT_config.allmenu=msg.data
        all_menu=msg.data
        if(WRT_config.allmenu&&WRT_config.ListByZkid){
          init()
        }
      }else{
        WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
      }
    }
  })
  WRT_e.api.zkcyzqlist.getListByZkid({
    zhuanKeID:params["as_zkid"],
    params:{
    },
    success(msg){
      if(msg.data){
        WRT_config.ListByZkid=msg.data.sort(function(a,b){
          return a.paiXu<b.paiXu ? -1 : 1
        })
        if(WRT_config.allmenu&&WRT_config.ListByZkid){
          init()
        }
      }else{
        WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
      }
    }
  })
  // init()
})
/********************初始化********************/
function init() {
  //左侧
  var Menuleft = new Menuleft_View();
  Menuleft.$el = $("#zkcyzqlist_lists");
  Menuleft.init({ data: WRT_config.allmenu }).render();
  //右侧
  var Menuright = new Menuright_View();
  Menuright.$el = $("#zkxyws_lists");
  Menuright.init({ data: WRT_config.ListByZkid }).render();
}

var Menuleft_View = WRT_e.view.extend({
  render: function () {
    let html=`
    ${_.map(this.data,(obj,index)=>`
      <li><input type="checkbox" name="checkbox" id="${obj.wsmc}"  value="${index}"><label for="${obj.wsmc}">${obj.wsmc}</label></li>
    `
    ).join('')}`
    this.$el.html(html)
  return this;
  },
})

var Menuright_View = WRT_e.view.extend({
  render: function () {
    let html=`
    ${_.map(this.data,(obj,index)=>`
    <tr>
      <td width="50"><input id="checkbox${index}" name="box_list" type="checkbox" value="${index}" /></td>
      <td width="100" onclick="dbhang(${index})">${obj.leiXingMC}</td>
      <td width="350" onclick="dbhang(${index})">${obj.wenShuMC}</td>
      <td width="100" id="box_check${index}" onclick="dbhang(${index})">${obj.paiXu}</td>
      <td width="100" id="box_input${index}" class="none"><input type="number" class="model_text model_text${index}" value="${obj.paiXu}" onkeyup="input_index=${index};" /></td>
    </tr>
    `
    ).join('')}`
    this.$el.html(html)
  return this;
  },

})

/**
 * 方法
 */

//查找回车事件
function calAge() {
  var evt = window.event || e;
  if (evt.keyCode == 13) {
    left_init()
  }
}
function left_init(){
  let search=$(".mc_search")[0].value
  let arr=[]
  WRT_config.allmenu=all_menu
  if(search){
    let py=search.trim().toUpperCase()
    WRT_config.allmenu.map(function(item){
      console.log(item.wsmc,item.py)
      if(item.wsmc&&item.py&&(item.wsmc.indexOf(search)>=0||item.py.indexOf(py))>=0){
        arr.push(item)
      }
    })
  }else{
    arr=WRT_config.allmenu
  }
  WRT_config.allmenu=arr
  var Menuleft = new Menuleft_View();
  Menuleft.$el = $("#zkcyzqlist_lists");
  Menuleft.init({ data: arr }).render();
}


//右侧刷新
function reset_init(){
  WRT_e.api.zkcyzqlist.getListByZkid({
    zhuanKeID:params["as_zkid"],
    params:{
    },
    success(msg){
      if(msg.data){
        WRT_config.ListByZkid=msg.data.sort(function(a,b){
          return a.paiXu<b.paiXu ? -1 : 1
        })
        // WRT_config.ListByZkid=msg.data
        var Menuright = new Menuright_View();
        Menuright.$el = $("#zkxyws_lists");
        Menuright.init({ data: WRT_config.ListByZkid }).render();
      }else{
        WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
      }
    }
  })
}
//点击行事件
function dbhang(index){
  let css=$("#update_btn")[0].className
  if(css.indexOf("none")>=0){
    return
  }
  if($(`#checkbox${index}`).prop('checked')){
    $(`#checkbox${index}`).prop("checked",false);
  }else{
    $(`#checkbox${index}`).prop("checked",true);
  }
}
//新增
function add(){
  var checked = $("input[name=checkbox]:checked");
  if(checked.length!=1){
    WRT_e.ui.hint({msg:'左侧文书必须选择且只能选中一条'})
  }else{
    let index=checked[0].value
    let row=WRT_config.allmenu[index]
    // console.log(row)
    if(row){
      let td=WRT_config.ListByZkid
      let lists=td.sort(function(a,b){
        return a.paiXu<b.paiXu ? -1 : 1
      })
      let num;
      if(lists.length==0){
        num=1
      }else{
        num=parseInt(lists[lists.length-1].paiXu)+1
      }
      WRT_e.api.zkcyzqlist.addSingle({
        params:{
            caoZuoZheID: row.czzid,
            geShiDM: row.gsdm,
            paiXu: num,
            xiuGaiSJ: row.xgsj||'',
            wenShuLX: row.wslx||'',
            zhuanKeID: params["as_zkid"],
            zhuangTaiBZ: row.ztbz,
        },
        success(msg){
          if(msg.data){
            WRT_e.ui.hint({msg:'新增成功',type:'success'})
            left_init()
            reset_init()
          }else{
            WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
          }
        }
      })
    }
  }
}
//取消
function onCancel(){
  var box_list = $("input[name=box_list]:checked");
  if(box_list.length!=1){
    WRT_e.ui.hint({msg:'专科常用文书必须选择且只能选中一条'})
    return
  }
  let index=box_list[0].value
  $("#update_btn").removeClass("none")
  $("#cancel_btn").addClass("none")
  $("#save_btn").addClass("none")
  $(`#box_check${index}`).removeClass("none")
  $("input[name=checkbox]").disabled = false
  $(`#box_input${index}`).addClass("none")
  let checked=$("input[name=box_list]")
  for(let i in checked){
    checked[i].disabled = false
  }
}
//修改
function update(){
  // var checked = $("input[name=checkbox]:checked");
  // if(checked.length!=1){
  //   WRT_e.ui.hint({msg:'文书必须选择且只能选中一条',type:"error"})
  //   return
  // }
  input_index=null
  var box_list = $("input[name=box_list]:checked");
  if(box_list.length!=1){
    WRT_e.ui.hint({msg:'专科常用文书必须选择且只能选中一条'})
    return
  }
  let index=box_list[0].value
  $("#update_btn").addClass("none")
  $("#cancel_btn").removeClass("none")
  $("#save_btn").removeClass("none")
  $(`#box_check${index}`).addClass("none")
  $(`#box_input${index}`).removeClass("none")
  let checked=$("input[name=box_list]")
  for(let i in checked){
    checked[i].disabled = true
  }
}
//保存
function onSave(){
  var checked = $("input[name=checkbox]:checked");
  // if(checked.length!=1){
  //   WRT_e.ui.hint({msg:'左侧文书必须选择且只能选中一条'})
  //   return
  // }
  var box_list = $("input[name=box_list]:checked");
  if(box_list.length!=1){
    WRT_e.ui.hint({msg:'专科常用文书必须选择且只能选中一条'})
    return
  }
  let row={}
  if(checked[0]){
    let index=checked[0].value
    row=WRT_config.allmenu[index]
  }
  let key=box_list[0].value
  let list=WRT_config.ListByZkid[key]
  if(row&&list){
    let td=WRT_config.ListByZkid
    let lists=td.sort(function(a,b){
      return a.paiXu<b.paiXu ? -1 : 1
    })
    let num=parseInt(lists[lists.length-1].paiXu)+1
    let paiXu=$(`.model_text${input_index}`)[0].value||num
    WRT_e.api.zkcyzqlist.updateSingle({
      params:{
        caoZuoZheID: row.czzid||list.caoZuoZheID||'',
        geShiDM: row.gsdm||list.geShiDM||'',
        paiXu: paiXu,
        xiuGaiSJ: row.xgsj||'',
        wenShuLX: row.wslx||list.wenShuLX||'',
        zhuanKeID: params["as_zkid"],
        zhuangTaiBZ: row.ztbz||list.zhuangTaiBZ||'',
        geShiDMOLD:list.geShiDM
      },
      success(msg){
        if(msg.data){
          WRT_e.ui.hint({msg:'修改成功',type:'success'})
          $("#update_btn").removeClass("none")
          $("#save_btn").addClass("none")
          $("#cancel_btn").addClass("none")
          reset_init()
        }else{
          WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
        }
      }
    })
  }
}
//删除
function del(){
  var checked = $("input[name=box_list]:checked");
  if(checked.length!=1){
    WRT_e.ui.hint({msg:'专科常用文书必须选择且只能选中一条'})
  }else{
    let index=checked[0].value
    let row=WRT_config.ListByZkid[index]
    // console.log(row)
    if(row){
      let url='?geShiDM='+row.geShiDM+'&zhuanKeID='+JSON.parse(params["as_zkid"])
      WRT_e.api.zkcyzqlist.deleteSingle({
        params:url,
        success(msg){
          if(msg.data){
            WRT_e.ui.hint({msg:'删除成功',type:'success'})
            reset_init()
          }else{
            WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
          }
        }
      })
    }
  }
}