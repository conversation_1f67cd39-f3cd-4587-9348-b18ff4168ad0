
//统一页面启动
$(document).ready(() => {
  let params = {}
  // //获取关键字数据
  // let url = window.location.href.split("?") || []
  // let text = url[1].split("&")
  // for (let i of text) {
  //   let fd = i.split("=")
  //   params[fd[0]] = fd[1]
  // }
  // WRT_config.url = params
  app.init()
})
var gsdmWs = ''
var count = 0
var countArr = []
var activeBtnID = 1

var app = {
  init: function () {
    WRT_e.api.blnrjghcxNew.getData({
      // params: { "ll_blid": WRT_config.url["al_blid"] },
      success(data) {
        if (data.Code == 1) {
          WRT_config.blnrjghcxNew = data.Result
          // 上方输入框
          $("#topNow").html(
            new blnrjghcx_top().init({
              data: WRT_config.blnrjghcxNew
            }).render().$el
          )
        }
      },
      error: function (err) {
        WRT_e.ui.hint({msg:err,type:"error"})
      }
    })
  }
}

/***********  公共方法  ***********/

function cyrqStartNow(time) {
  var date = new Date(time);
  date.setDate(date.getDate() - 7);
  var Y = date.getFullYear()
  var M = (date.getMonth() +1)<10?'0'+(date.getMonth() +1):(date.getMonth() +1)
  var D = date.getDate()<10?'0'+ date.getDate(): date.getDate()
  var timeNow = Y +'-'+ M +'-'+ D
  return timeNow
}

// 查看病历
function openBlWin(item) {
  let model = $("#checkBlModal").iziModal({ //初始化modal
    overlayClose: false, //点击遮罩关闭框
    width: 1332,
    title: item.xm +'的病历',
  })
  //设置自定义内容
  model.iziModal('setContent',`
    <div>
      <iframe class="dynr1" id="if_${item.blid}" src="${item.open_wsUrl}" rameborder="0" width="100%" style='height:80vh'></iframe>
    </div>
  `);
  //打开
  model.iziModal('open')
}
// 打开病人
function openBrTab(item) {
  let url = `e-BrMainLeft.html?ls_idb=`+item.idb+`&al_blid=`+item.blid+``
  let jflx = `${item.jslx == "00" ? "自费" : item.jslx == "01" ? "社保" : item.jslx == "02" ? "农保" : "公费"}`
    
  parent.page_iframe.add(`bl_${item.blid}`,`${jflx} ${item.zdym}-${item.cwh} ${item.xm}`,url)
}

/***********  视图  ***********/
// 上方
var blnrjghcx_top = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
      <div class="topLineNow">
        <div class="topInput">
          <div class="flex-line">
            <label class="control-label label-name"> 文书类型 </label>
            <div class="control-inner flex-select control_ws">
              <select class="form-control" name="wslx" style="width:186px;">
              ${_.map(this.data.WSlBLIST, (item)=> `
                <option value=${item.wslx}>${item.lxmc}</option>
              `).join('')}
              </select>
            </div>
          </div>

          <div class="flex-line">
            <label class="control-label label-name"> 专科 </label>
            <div class="control-inner flex-select control_zk">
              <select class="form-control" name="zkdm" style="width:186px;">
              ${_.map(this.data.ZKlBLIST, (item)=> `
                <option value=${item.bmdm}>${item.bmmc}</option>
              `).join('')}
              </select>
            </div>
          </div>
          
          <div class="flex-line">
            <button class="e_btn e-btn-list ensureWsAZk" style="line-height:31px;font-weight: 600">确定</button>
          </div>
        </div>
        <div class="btnCz">
          <div class='btnItemNow'>
            <a ${activeBtnID == 1 ? `class='group_titleBtn_active'` : `class='group_titleBtn'`} data-bh='1'>
              住院病例
            </a>
            <a ${activeBtnID == 2 ? `class='group_titleBtn_active'` : `class='group_titleBtn'`} data-bh='2'>
              门诊病历
            </a>
          </div>
        </div>
      </div>
    `)
    return this
  },
  events: {
    "click .group_titleBtn":function (e) {
      // console.log(e.target.dataset.bh);
      activeBtnID = e.target.dataset.bh
      $('.btnCz').html(`
      <div class='btnItemNow'>
        <a ${activeBtnID == 1 ? `class='group_titleBtn_active'` : `class='group_titleBtn'`} data-bh='1'>
          住院病例
        </a>
        <a ${activeBtnID == 2 ? `class='group_titleBtn_active'` : `class='group_titleBtn'`} data-bh='2'>
          门诊病历
        </a>
      </div>`)
      if (activeBtnID == 1) {
        app.init()
      } else if(activeBtnID == 2){
        WRT_e.api.blnrjghcxNew.getDataMz({
          // params: { "ll_blid": WRT_config.url["al_blid"] },
          success(data) {
            if (data.Code == 1) {
              WRT_config.blnrjghcxNewMz = data.Result
              // console.log(data.Result);
              // 上方输入框
              $("#topNow").html(
                new blnrjghcx_top().init({
                  data: WRT_config.blnrjghcxNewMz
                }).render().$el
              )
            }
          },
          error: function (err) {
            WRT_e.ui.hint({msg:err,type:"error"})
          }
        })
      }

    },
    "click .ensureWsAZk": function () {
      var wslx = $('.control_ws select[name="wslx"]').val()
      var zkdm = $('.control_zk select[name="zkdm"]').val()
      // 清空右下全部
      $(".RInputForm").html(``)
      $(".Rtable").html(``)
      if (activeBtnID == 1) {
        WRT_e.api.blnrjghcxNew.getWsListData({
          params: { "as_wslx":wslx, "as_zkdm":zkdm },
          success(data) {
            if (data.Code == 1) {
              WRT_config.blnrjghcxList = data.Result
              // 左下列表
              $(".Llist").html(
                new blnrjghcx_bottomL().init({
                  data: WRT_config.blnrjghcxList
                }).render().$el
              )
            }
          },
          error: function (err) {
            WRT_e.ui.hint({msg:'当前选项无数据，请切换其他病例或专科',type:"error"})
          }
        })
      } else if(activeBtnID == 2){
        WRT_e.api.blnrjghcxNew.getMzWsListData({
          params: { "as_wslx":wslx, "as_zkdm":zkdm },
          success(data) {
            if (data.Code == 1) {
              WRT_config.blnrjghcxListMz = data.Result
              // 左下列表
              $(".Llist").html(
                new blnrjghcx_bottomL().init({
                  data: WRT_config.blnrjghcxListMz
                }).render().$el
              )
            }
          },
          error: function (err) {
            WRT_e.ui.hint({msg:'当前选项无数据，请切换其他病例或专科',type:"error"})
          }
        })
      }
    }
  }
})

// 左下
var blnrjghcx_bottomL = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
      <div class="lData">
        <ul class='list_ul'>
        ${_.map(this.data, (item)=> `
          <li class='list_name'>
            <a href="javascript:void(0)" class="btnlist" name=${item.dm} id="type_title${item.dm}">${item.mc}（${item.dm}）</a>
          </li>
        `).join('')}
        </ul>
      </div>
    `)
    return this
  },
  events: {
    "click .btnlist": function (idname) {
      gsdmWs = idname.target.name
      WRT_e.api.blnrjghcxNew.getWsgs({
        params: { "as_gsdm": idname.target.name },
        success(data) {
          WRT_config.blnrjghcxTJ = data.Result // 查询条件下拉选项
          // 查询条件
          $(".RInputForm").html(
            new blnrjghcx_bottomR().init({
              // data: WRT_config.blnrjghcxTJ
              data: {
                cxTJ: WRT_config.blnrjghcxTJ,
                baseData: WRT_config.blnrjghcxNew
              }
            }).render().$el
          )
          count = 0
        }
      })
    }
  }
})

// 右下（查询条件）  <button class='e_btn delTj'> 删除 </button>

/* <label class="control-label label-name" style="font-size: 15px;min-width:60px"> 出生日期 </label>
<div class="control-inner flex-select control_cyrq">
  <input class="form-control" type="date" id="srstart" name="srstart" style='padding: 6px 4px;width: 102px'>
  <span style='padding:0 10px'> - </span>
  <input class="form-control" type="date" id="srend" name="srend" style='padding: 6px 4px;width: 102px'>
</div> */
//
var blnrjghcx_bottomR = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
      <div class='qCondition'>
        <div class='inputSelect'>
          <div class='initCondition'>
            ${activeBtnID==1?`
              ${ this.data.baseData.YHXX.DYXT && this.data.baseData.YHXX.DYXT=='MZ'?`
                <label class="control-label label-name" style="font-size: 15px;min-width:60px"> 就诊时间 </label>`:`
                <label class="control-label label-name" style="font-size: 15px;min-width:60px"> 出院日期 </label>
              `}`:`
              <label class="control-label label-name" style="font-size: 15px;min-width:60px"> 记录时间 </label>
            `}
            <div class="control-inner flex-select control_cyrq">
              <input class="form-control" type="date" id="start" name="start" value='${cyrqStartNow(this.data.baseData.TIME)}'>
              <span style='padding:0 10px'> - </span>
              <input class="form-control" type="date" id="end" name="end" value='${this.data.baseData.TIME}'>
            </div>
            <label class="control-label label-name" style="font-size: 15px;min-width:30px"> 性别 </label>
            <div class="control-inner flex-select control_xb">
              <select class="form-control" name="xb" style="width:45px;padding: 6px 4px;">
                <option value='1'>男</option>
                <option value='2'>女</option>
              </select>
            </div>
            
            ${activeBtnID == 1?`
              ${ this.data.baseData.YHXX.DYXT && this.data.baseData.YHXX.DYXT=='MZ'?`
                <label class="control-label label-name" style="font-size: 15px;min-width:60px"> 诊断 </label>`:`
                <label class="control-label label-name" style="font-size: 15px;min-width:60px"> 入院诊断 </label>
              `}`:`
              <label class="control-label label-name" style="font-size: 15px;min-width:60px"> 门诊诊断 </label>
            `}

            <div class="control-inner flex-select control_ryzd">
              <input class="form-control" type='text' name='ryzd'/>
            </div>
          </div>
          <div class='addCondition'></div>
        </div>
        <div class='allBtn'>
          <div class='btnLine'>
            <button class='e_btn_primary searchT'>
              <i class="glyphicon glyphicon-search"></i>
              查询
            </button>
            <button class='e_btn reset'>
              <i class="glyphicon glyphicon-repeat"></i>
              重置
            </button>
          </div>
          <div class='btnLine' style='right: 90px;'>
            <button class='e_btn addTj'> 新增 </button>
          </div>
        </div>
      <div>
    `)
    return this
  },
  events: {
    // 查询
    "click .searchT":function () {
      if (this.data.baseData.TIME && ($('.control_cyrq input[name="start"]').val()=='' || $('.control_cyrq input[name="end"]').val()=='')) {
        WRT_e.ui.hint({msg:'请在查询前选择出院时间范围',type:"error"})
      } else {
        // if (this.data.baseData.TIME!=undefined) {}
        // var searchData = {
        //   "as_json": {
        //     "jbxx": {
        //       "brxb": "1",
        //       "ryzd": "脑肿瘤",
        //       "cyrq_kssj": "2023-01-01",
        //       "cyrq_jssj": "2023-03-01",
        //       "zkdm": "34",
        //       "gsdm": "0493",
        //       "logical": "or"
        //     },
        //     "nrList": [{
        //         "dm": "0018",
        //         "nrb": "e_zyblnr50",
        //         "nr": "肿瘤",
        //         "operate": "like",
        //         "jglx": "E"
        //       },
        //       {
        //         "dm": "0019",
        //         "jglx": "",
        //         "nrb": "e_zyblnrl1",
        //         "nr": "癫痫发作",
        //         "operate": "like",
        //         "jglx": "E"
        //       }
        //     ]
        //     门诊
        //     jbxx: {
        //       brxb: "1",
        //       ryzd: "脑肿瘤",
        //       cyrq_kssj: "2023-01-01",
        //       cyrq_jssj: "2023-03-01",
        //       zkdm: "34",
        //       gsdm: "8732",
        //       logical: "or"
        //     },
        //     nrList: [
        //       {
        //         dm:"102184",
        //         nrb:"yl_mzbgknr200",
        //         nr:"提高",
        //         operate:"like",
        //         jglx:"E"
        //       },
        //       {
        //         dm: "102184",
        //         nrb: "yl_mzbgknr200",
        //         nr: "诊断",
        //         operate: "like",
        //         jglx: "E"
        //       }
        //     ]
        //   }
        // }

        var listLine = $(".addCondition")[0].children      
        var searchData = {
          // as_json:{
            jbxx: {
              brxb: $('.control_xb select[name="xb"]').val(),
              ryzd: $('.control_ryzd input[name="ryzd"]').val(),
              csrq_kssj: '',
              csrq_jssj: '',
              cyrq_kssj: $('.control_cyrq input[name="start"]').val() ||'',
              cyrq_jssj: $('.control_cyrq input[name="end"]').val() ||'',
              zkdm: $('.control_zk select[name="zkdm"]').val(),
              logical: $('#ljf1 select[name="ljf"]').val(), // and ||or
              gsdm: gsdmWs,
            },
            nrList: []
          // }
        }
        
        if ( listLine.length!=0) {
          for (let index = 0; index < listLine.length; index++) {
            const element = listLine[index];
            if (element.children.length!=0) {
              let idBh = element.children[0].attributes['data-ls'].value
              searchData.nrList.push({
                dm: $('#zdm'+idBh + " select[name='zdm'] option:selected").val(),
                jglx: $('#zdm'+idBh + " select[name='zdm'] option:selected").attr('name'),
                nrb: $('#zdm'+idBh + " select[name='zdm'] option:selected").attr('data-nrb'),
                nr: $('#cxz'+idBh + " [name='cxz']").val(),
                operate: $('#bjf'+idBh + " select[name='bjf'] option:selected").val(),
              })
            }
          }
        }
        // console.log(searchData);
        
        $('.Rtable').html(`
          <div class="overall_table_frame">
            <table id="table_internal" class="table_internal">
              <thead>
                <tr class="row_head">
                  <th>序号</th>
                  <th>姓名</th>
                  <th>性别</th>
                  <th>年龄</th>
                  <th>病案号</th>
                  ${activeBtnID==2?`<th>就诊日期</th>`:`<th>入院日期</th>`}
                  ${activeBtnID==2?``:`<th>出院日期</th>`}
                  <th>操作</th>
                </tr>
              </thead>
              <tbody class="line_nr" id="line_nr">
                <tr>
                  <td colspan="8" style='text-align: center;'>
                    <img  class="load_img" src="./images/load.gif" style="width: 160px;" />
                  </td>
                </tr>
              </tbody>
            </table>
          </div>`)
        if (activeBtnID==1) {
          WRT_e.api.blnrjghcxNew.getBrLb({
            params: { as_json: JSON.stringify(searchData)},
            success(data) {
              if (data.Code = '1') {
                WRT_config.blnrjghcxTable = data.Result
              }
              $('.Rtable').html(
                new blnrjghcx_table().init({
                  data: WRT_config.blnrjghcxTable
                }).render().$el
              )
            },
            error: function (err) {
              WRT_e.ui.hint({msg:err,type:"error"})
            }
          })
        } else if (activeBtnID==2) {
          WRT_e.api.blnrjghcxNew.getBrLbMz({
            params: { as_json: JSON.stringify(searchData)},
            success(data) {
              if (data.Code = '1') {
                WRT_config.blnrjghcxTableMz = data.Result
              }
              $('.Rtable').html(
                new blnrjghcx_table().init({
                  data: WRT_config.blnrjghcxTableMz
                }).render().$el
              )
            },
            error: function (err) {
              WRT_e.ui.hint({msg:err,type:"error"})
            }
          })
        }
      }
    },
    // 重置
    "click .reset":function () {
      count = 0
      $(".RInputForm").html(
        new blnrjghcx_bottomR().init({
          // data: WRT_config.blnrjghcxTJ
          data: {
            cxTJ: WRT_config.blnrjghcxTJ,
            baseData: WRT_config.blnrjghcxNew
          }
        }).render().$el
      )
    },
    // 新增
    "click .addTj":function () {
      count++
      countArr.push(count)
      // console.log(this.data);
      $(".addCondition").append(
        new blnrjghcx_add().init({
          // data: WRT_config.blnrjghcxTJ
          data: this.data.cxTJ
        }).render().$el
      )
    },
    // 删除
    "click .delTj":function () {
      if (count!=0) count--
      var tjLine = $(".addCondition .addLine:last");
      // 删除此行
      tjLine.remove();
    }
  }
})

var blnrjghcx_add = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
      <div class='addLine' id='addLine${count}' data-lsbh='0' data-ls=${count}>
        <label class="control-label label-name" style='font-size:14px;min-width:42px'> 字段名 </label>
        <div class="control-inner flex-select control_zdm" id='zdm${count}'>
          <select class="form-control" id='changezdm' name='zdm' style="width:186px;padding:6px 4px" data-bh=${count}>
          ${_.map(this.data, (item,index)=> `
            <option class='zdmChange${index}' value=${item.DM} name=${item.JGLX} data-jglx=${item.JGLX} data-nrb=${item.NRB} data-jg=${item.JG} data-bh=${count}>${item.JDMC}</option>
          `).join('')}
          </select>
        </div>
        
        <label class="control-label label-name" style='font-size:14px;min-width:42px'> 比较符 </label>
        <div class="control-inner flex-select control_bjf" id='bjf${count}'>
          <select class="form-control" name='bjf' style="width:86px;padding:6px 4px">
            <option value="=">等于</option>
            <option value="!=">不等于</option>
            <option value=">">大于</option>
            <option value=">=">大于等于</option>
            <option value="<">小于</option>
            <option value="<=">小于等于</option>
            <option value="like">包含</option>
          </select>
        </div>

        <label class="control-label label-name" style='font-size:14px;min-width:42px'> 查询值 </label>
        <div class="control-inner flex-select control_cxz" id='cxz${count}'>
          ${this.data[0].JGLX=='C'?`<select class="form-control" name='cxz' style="width:100px;padding:6px 4px">
              <option value="0"></option>
              <option value="1">是</option>
            </select>`:
            this.data[0].JGLX=='N'?`<input class="form-control" type='number' id='searchVal' name='cxz' style=";padding:6px 4px"/>${this.data[0].JG?this.data[0].JG:''}`:
            (this.data[0].JGLX=='R' || this.data[0].JGLX=='R0' || this.data[0].JGLX=='DD'|| this.data[0].JGLX=='RE')?`<select class="form-control" name='cxz' style="width:100px;padding:6px 4px">
              ${_.map(this.data[0].JG.split(','),item=>`
                  <option value="${item.split('=')[1]}">${item.split('=')[0]}</option>
              `).join('')}
            </select>`:
            (this.data[0].JGLX=='D'||this.data[0].JGLX=='DT'||this.data[0].JGLX=='YM'||this.data[0].JGLX=='YMM'||this.data[0].JGLX=='YMMN'||this.data[0].JGLX=='NF'||this.data[0].JGLX=='NYR')?`
            <input class="form-control" type='datetime-local' id='searchVal' name='cxz' style="padding:6px 4px"/>`:`<input class="form-control" type='text' id='searchVal' name='cxz' style="padding:6px 4px"/>`
          }
        </div>

        <label class="control-label label-name" style='font-size:14px;min-width:42px'> 逻辑符 </label>
        <div class="control-inner flex-select control_ljf" id='ljf${count}'>
          <select class="form-control" id='changeljf' name='ljf' style="width:58px;padding:6px 4px" ${count==1?``:`disabled=true`}>
            <option value="and">且</option>
            <option value="or">或者</option>
          </select>
        </div>
        
        <div class="control-inner flex-select control_del" id='del${count}' style='padding: 0 10px 0 0px'>
          <span id='changeremove' style="cursor:pointer" data-bh=${count}>  
            <i class="glyphicon glyphicon-remove"></i>
          </span>
        </div>
      </div>
    `)
    return this
  },
  events: {
    "change #changezdm":function (zdmNow) {   
      // let jglxNow = $('#changezdm option:selected').attr('name')
      let jglxNum = zdmNow.target[0].attributes['data-bh'].value
      let jglxNow = $('#zdm'+jglxNum+" select[name='zdm'] option:selected").attr('data-jglx')
      // let sjdw =$('#changezdm option:selected').attr('data-jg')
      let sjdw =$('#zdm'+jglxNum+" select[name='zdm'] option:selected").attr('data-jg')
      if (jglxNow == 'C') {
        $('#cxz'+jglxNum).html(`<select class="form-control" name='cxz' style="width:100px;padding:6px 4px">
          <option value="0"></option>
          <option value="1">是</option>
        </select> `)
      } 
      else if (jglxNow == 'N') {
        $('#cxz'+jglxNum).html(`<input class="form-control" type='number' id='searchVal' name='cxz'style="padding:6px 4px"/>  <span style='padding-left:5px'>${sjdw || ''}</span>`)
      } 
      else if(jglxNow == 'R' || jglxNow == 'R0' || jglxNow == 'DD' || jglxNow == 'RE'){
        $('#cxz'+jglxNum).html(`<select class="form-control" name='cxz' style="width:100px;padding:6px 4px">
          ${_.map(sjdw.split(','),item=>`
            <option value="${item.split('=')[1]}">${item.split('=')[0]}</option>
          `).join('')}
        </select>`)
      } 
      else if(jglxNow == 'D' || jglxNow == 'DT' || jglxNow == 'YM' || jglxNow == 'YMM' || jglxNow == 'YMMN' || jglxNow == 'NF' || jglxNow == 'NYR'){
        $('#cxz'+jglxNum).html(`<input class="form-control" type='datetime-local' id='searchVal' name='cxz' style='padding:6px 4px'/>`)
      } 
      else {
        $('#cxz'+jglxNum).html(`<input class="form-control" type='text' id='searchVal' name='cxz' style='padding:6px 4px'/>`)
      }
    },
    "change #changeljf":function (ljfNow) {
      for (let index = 1; index <= count; index++) {
        let nameid ='#ljf'+index
        let valNow  = $('#ljf1 select[name="ljf"]').val()
        if (index!=1) {
          $(nameid+' select[name="ljf"]').val(valNow)
        }
      }
      // $('.control_ljf select[name="ljf"]').val()
    },
    // 删除
    "click #changeremove":function (removeNow) {
      let bhNum = removeNow.currentTarget.attributes['data-bh'].value
      let lsbh = $('#addLine'+bhNum).attr('data-lsbh')
      if (count>0) {
        count-- 
        countArr[bhNum-1] = 0
        if ((bhNum==1 || lsbh==1) && count != 0) {
          let ljfNow = countArr.find(item => item !== 0)
          $('#addLine'+bhNum).parent().remove();
          $('#addLine'+ljfNow).attr('data-lsbh',1)
          $('#ljf'+ljfNow).html(`<select class="form-control" id='changeljf' name='ljf' style="width:58px;padding:6px 4px" >
            <option value="and">且</option>
            <option value="or">或者</option>
          </select>`)
        } else {
          $('#addLine'+bhNum).parent().remove();
        }
      } else {
        count = 0
        $('.addCondition').remove();
        countArr=[]
      }
    }
  }
})

var blnrjghcx_table = WRT_e.view.extend({
  render: function () {
    if (this.data.length==0) {
      this.$el.html(`
      <div class="overall_table_frame">
        <table id="table_internal" class="table_internal">
          <thead>
            <tr class="row_head">
              <th>序号</th>
              <th>姓名</th>
              <th>性别</th>
              <th>年龄</th>
              <th>病案号</th>
              ${activeBtnID==2?`<th>就诊日期</th>`:`<th>入院日期</th>`}
              ${activeBtnID==2?``:`<th>出院日期</th>`}
              <th>操作</th>
            </tr>
          </thead>
          <tbody class="line_nr" id="line_nr">
            <tr>
              <td colspan="8">
                <div style='position:relative;font-size: 22px;padding: 10px 5px 10px;width: 80%;margin: 0 auto;text-align: center;'> 未查询到相关病人数据 </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>`)
    } else {
      // let zPage = Math.ceil(this.data.length/10)
      // let cPage = 1
      this.$el.html(`
      <div class="overall_table_frame">
        <table id="table_internal" class="table_internal">
          <thead>
            <tr class="row_head">
              <th>序号</th>
              <th>姓名</th>
              <th>性别</th>
              <th>年龄</th>
              <th>病案号</th>
              ${activeBtnID==2?`<th>就诊日期</th>`:`<th>入院日期</th>`}
              ${activeBtnID==2?``:`<th>出院日期</th>`}
              <th>操作</th>
            </tr>
          </thead>
          <tbody class="line_nr" id="line_nr">
            ${_.map(this.data,(item,index)=>`<tr class="row_nr">
              <td calss="xh${index}">${index+1}</td>
              <td calss="xm${index}">${item.xm}</td>
              <td calss="xb${index}" >${item.xb}</td>
              <td calss="nl${index}">${item.nl}</td>
              <td calss="bah${index}">${item.empi}</td>
              <td calss="ryrq${index}">${item.ryrq}</td>
              ${activeBtnID==2?``:`<td calss="cyrq${index}">${item.cyrq}</td>`}
              <td>
                <a herf="javascript:void(0)" calss='checkBl' onclick='openBlWin(${JSON.stringify(item)})' style='position: relative;padding-right: 36px;cursor: pointer;'>查看病历</a>
                ${activeBtnID==2?``:`<a herf="javascript:void(0)" calss='openBR' onclick='openBrTab(${JSON.stringify(item)})' style='cursor: pointer;'>打开病人</a>`}
              </td>
            </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
      `)
    }
    return this
  },
})

