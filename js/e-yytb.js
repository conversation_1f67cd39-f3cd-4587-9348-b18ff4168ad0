
//统一页面启动
$(document).ready(() => {
  let params = {}
  //获取关键字数据
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  WRT_config.url = params
  app.init()
})

var app = {
  init: function () {
    WRT_e.api.yytb.getData({
      params: { "ll_blid": WRT_config.url["al_blid"] },
      success(data) {
        if (data.Code == 1) {
          WRT_config.yytb = JSON.parse(data.Result)
          console.log(WRT_config.yytb);
          const dataYyfxsc = WRT_config.yytb.yyfxsc
          yyfxsc_Chart(dataYyfxsc)

          const dataYyzlzxd = WRT_config.yytb.yyzlzxd
          yyzlzxd_Chart(dataYyzlzxd)

          const dataBmi = WRT_config.yytb.bmi
          bmi_Chart(dataBmi)

          const dataWeight = WRT_config.yytb.tz
          qzqWeight_Chart(dataWeight)

          const dataBdb = WRT_config.yytb.bdb
          bdb_Chart(dataBdb)

          const dataXhdb = WRT_config.yytb.xhdb
          xhdb_Chart(dataXhdb)
        }
      }
    })
  }
}


/***********  视图  ***********/
// 展示营养风险筛查评分趋势图（yyfxsc）
function yyfxsc_Chart(dataYyfxsc) {
  const times = []
  const list = []
  dataYyfxsc.map(function (item) {
    return times.push(item['评估日期'].replace('T', '\n'));
  });
  dataYyfxsc.map(function (item) {
    return list.push(item['总评分']);
  });

  var myChart = echarts.init(document.getElementById('yyfxscTb'));
  // 指定图表的配置项和数据
  var option = {
    // Make gradient line here
    visualMap: [
      {
        show: false,
        type: 'continuous',
        seriesIndex: 0,
        // dimension: 0,
        min: 0,
        max: times.length - 1
      }
    ],
    title: [
      {
        left: 'center',
        text: '营养风险筛查评分趋势图',
        textStyle: {
          fontSize: 22
        }
      },
    ],
    
    tooltip: {
      trigger: 'axis'
    },
    // dataset: {
    //   source: [
    //     ['时间范围', ...list],
    //   ]
    // },
    xAxis: [
      {
        name: '评估日期',
        type: 'category',
        data: times
      },
    ],
    yAxis: [
      {
        name: '总评分',
        type: 'value'
      },
    ],
    grid: [
      {
        bottom: '20%'
      },
      {
        top: '20%'
      }
    ],
    series: [
      {
        name: '总评分',
        type: 'line',
        // showSymbol: false,
        data: list
      },
    ]
  }

  option && myChart.setOption(option);
}

// 营养诊疗执行单每日热量蛋白质摄入趋势图（yyzlzxd）
function yyzlzxd_Chart(dataYyzlzxd) {
  const times = []
  const list1 = []
  const list2 = []
  const list3 = []
  const list4 = []
  const list5 = []
  const list6 = []
  dataYyzlzxd.map(function (item) {
    let timeNow = item['时间范围'].split('-')
    Ztime = timeNow[0]+'-'+timeNow[1]+'-'+timeNow[2]+'\n~\n'+ timeNow[3]+'-'+timeNow[4]+'-'+timeNow[5]
    // console.log(timeNow,Ztime);
    return times.push(Ztime.replace(/\s/g, '\n'));
  });
  dataYyzlzxd.map(function (item) {
    list1.push(item['其他能量']);
    list2.push(item['其他蛋白质']);
    list3.push(item['肠外营养合计能量']);
    list4.push(item['肠外营养合计蛋白质']);
    list5.push(item['营养支持总量合计能量']);
    list6.push(item['营养支持总量合计蛋白质']);
  });
  // console.log(times, '其他能量',list1, '其他蛋白质',list2, '肠外营养合计能量',list3, '肠外营养合计蛋白质',list4, '营养支持总量合计能量',list5, '营养支持总量合计蛋白质',list6);
  var myChart = echarts.init(document.getElementById('yyzlzxdTb'));
  var option = {
    title: [
      {
        left: 'center',
        text: '营养诊疗执行单每日热量蛋白质摄入趋势图',
        textStyle: {
          fontSize: 22
        }
      },
    ],
    legend: {
      // left: '10%'
      // left: 'left'
      right: 'right',
      textStyle: {
        fontSize: 14
      }
    },
    
    grid: {
      top: '20%',
      bottom: '20%'
    },
    tooltip: {
      trigger: 'axis',
      // showContent: false
    },
    dataset: {
      source: [
        ['时间范围', ...times],
        ['其他能量', ...list1],
        ['其他蛋白质', ...list2],
        ['肠外营养合计能量', ...list3],
        ['肠外营养合计蛋白质', ...list4],
        ['营养支持总量合计能量', ...list5],
        ['营养支持总量合计蛋白质', ...list6]
      ]
    },
    xAxis: { 
      name:'时间范围',
      type: 'category'
    },
    yAxis: { 
      // name:'蛋白质(kDa) || 能量(J)',
      gridIndex: 0 
    },
    series: [
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' }
      },
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' }
      },
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' }
      },
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' }
      },
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' }
      },
      {
        type: 'line',
        smooth: true,
        seriesLayoutBy: 'row',
        emphasis: { focus: 'series' }
      },
    ]
  };
  myChart.on('updateAxisPointer', function (event) {
    const xAxisInfo = event.axesInfo[0];
    if (xAxisInfo) {
      const dimension = xAxisInfo.value + 1;
      myChart.setOption({
        series: {
          id: 'pie',
          label: {
            formatter: '{b}: {@[' + dimension + ']} ({d}%)'
          },
          encode: {
            value: dimension,
            tooltip: dimension
          }
        }
      });
    }
  });
  myChart.setOption(option);
}

// bmi指数趋势图（BMI）
function bmi_Chart(dataBmi) {
  const times = []
  const list = []
  const listMax = []
  const listMin = []
  dataBmi.map(function (item) {
    return times.push(item['记录时间'].replace('T', '\n'));
  });
  dataBmi.map(function (item) {
    WRT_e.api.nutriOrderList.getBMI({
      params: {
        ad_sg: item['身高'],
        ad_tz: item['体重']
      },
      success(data) {
        // console.log('计算（BMI）',data.d)
        let bmiData = data.d
        list.push(bmiData);
        listMax.push(24)
        listMin.push(18.5)
      }
    })
  });
  // console.log(888,list);
  
  var myChart = echarts.init(document.getElementById('bmiTb'));
  // 指定图表的配置项和数据
  var option = {
    title: {
      left: 'center',
      text: 'bmi指数趋势图',
      textStyle: {
        fontSize: 22
      }
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['BMI', '消瘦界限(18.5kg/m²)', '超重界限(24kg/m²)'],
      // left: 'left',
      right: 'right',
      textStyle: {
        fontSize: 14
      },
      // orient: 'vertical',
      // align: 'left',
      // top: 'middle',
      // padding: [
      //   5, // 上
      //   0, // 右
      //   5, // 下
      //   10 // 左
      // ]
      padding: 5
    },
    
    grid: {
      top: '20%',
      bottom: '20%'
      // left: '15%',
      // right: '4%',
      // bottom: '10%',
      // containLabel: true
    },
    // grid: [
    //   {
    //     bottom: '20%'
    //   },
    //   {
    //     top: '30%'
    //   }
    //   // left: '15%',
    //   // right: '4%',
    //   // bottom: '10%',
    //   // containLabel: true
    // ],
    // toolbox: { // 之前的
    //   feature: {
    //     // saveAsImage: {}
    //   }
    // },
    // toolbox: { // 临时改的，还不确定效果
    //   show: true,
    //   feature: {
    //     dataZoom: {
    //       yAxisIndex: 'none'
    //     },
    //     dataView: { readOnly: false },
    //     magicType: { type: ['line', 'bar'] },
    //     restore: {},
    //     saveAsImage: {}
    //   }
    // },
    xAxis: {
      name: '记录时间',
      type: 'category',
      // boundaryGap: false,
      data: times
    },
    yAxis: {
      name: 'BMI',
      type: 'value',
      // min: 0,
      // max: 15,
      // data: ['0', '2', '3', '5', '6', '8', '9', '11', '12', '14', '15']
      // data: [0, 2, 3, 5, 6, 8, 9, 11, 12, 14, 15]
    },
    series: [
      {
        name: '消瘦界限(18.5kg/m²)',
        type: 'line',
        // stack: '18.5',
        data: listMin,
        markPoint: {
          data: [{ name: '消瘦', value: 18.5, xAxis: 0, yAxis: 18.5 }],
          symbolRotate:'180',
          // symbolOffset:[0, '50%'],
          label: {
            offset: [0, 10]
          }
        },
      },
      {
        name: 'BMI',
        type: 'line',
        // stack: 'Total',
        data: list
      },
      {
        name: '超重界限(24kg/m²)',
        type: 'line',
        // stack: '24',
        data: listMax,
        markPoint: {
          data: [{ name: '超重', value: 24, xAxis: listMax.length-1, yAxis: 24 }],
        },
      },
    ]
  };

  option && myChart.setOption(option);
}

// 全周期体重趋势图(Weight)
function qzqWeight_Chart(dataWeight) {
  const times = []
  const list = []
  // x轴
  dataWeight.map(function (item) {
    return times.push(item['记录时间'].replace('T', '\n'));
  });
  // y轴
  dataWeight.map(function (item) {
    return list.push(item['体重']);
  });

  var myChart = echarts.init(document.getElementById('qzqweightTb'));
  // 指定图表的配置项和数据
  var option = {
    // Make gradient line here
    // visualMap: [
    //   {
    //     show: false,
    //     type: 'continuous',
    //     seriesIndex: 0,
    //     // dimension: 0,
    //     min: 0,
    //     max: times.length - 1
    //   }
    // ],
    title: [
      {
        left: 'center',
        text: '全周期体重趋势图',
        textStyle: {
          fontSize: 22
        }
      },
    ],
    
    tooltip: {
      trigger: 'axis'
    },
    // dataset: {
    //   source: [
    //     ['时间范围', ...list],
    //   ]
    // },
    xAxis: [
      {
        name: '记录时间',
        type: 'category',
        data: times
      },
    ],
    yAxis: [
      {
        name: '体重',
        type: 'value'
      },
    ],
    grid: [
      {
        bottom: '20%'
      },
      {
        top: '20%'
      }
    ],
    series: [
      {
        name: '体重',
        type: 'line',
        // showSymbol: false,
        data: list
      },
    ]
  }

  option && myChart.setOption(option);
  
}

// 白蛋白趋势图（bdb）
function bdb_Chart(dataBdb) {
  // console.log(dataBdb);
  const times = []
  const list = []
  // x轴
  dataBdb.map(function (item) {
    return times.push(item['CHECKTIME'].replace('T', '\n'));
  });
  // y轴
  dataBdb.map(function (item) {
    return list.push(item['TESTRESULT']);
  });

  var myChart = echarts.init(document.getElementById('bdbTb'));
  // 指定图表的配置项和数据
  var option = {
    // Make gradient line here
    // visualMap: [
    //   {
    //     show: false,
    //     type: 'continuous',
    //     seriesIndex: 0,
    //     // dimension: 0,
    //     min: 0,
    //     max: times.length - 1
    //   }
    // ],
    title: [
      {
        left: 'center',
        text: '白蛋白趋势图',
        textStyle: {
          fontSize: 22
        }
      },
    ],
    
    tooltip: {
      trigger: 'axis'
    },
    // dataset: {
    //   source: [
    //     ['时间范围', ...list],
    //   ]
    // },
    xAxis: [
      {
        name: '记录时间',
        type: 'category',
        data: times
      },
    ],
    yAxis: [
      {
        name: '白蛋白',
        type: 'value'
      },
    ],
    grid: [
      {
        bottom: '20%'
      },
      {
        top: '20%'
      }
    ],
    series: [
      {
        name: '白蛋白',
        type: 'line',
        // showSymbol: false,
        data: list
      },
    ]
  }

  option && myChart.setOption(option);
  
}

// 血红蛋白趋势图（xhdb）
function xhdb_Chart(dataXhdb) {
  console.log(dataXhdb);
  const times = []
  const list = []
  // x轴
  dataXhdb.map(function (item) {
    return times.push(item['CHECKTIME'].replace('T', '\n'));
  });
  // y轴
  dataXhdb.map(function (item) {
    return list.push(item['TESTRESULT']);
  });

  var myChart = echarts.init(document.getElementById('xhdbTb'));
  // 指定图表的配置项和数据
  var option = {
    title: [
      {
        left: 'center',
        text: '血红蛋白指标趋势图',
        textStyle: {
          fontSize: 22
        }
      },
    ],
    
    tooltip: {
      trigger: 'axis'
    },
    // dataset: {
    //   source: [
    //     ['时间范围', ...list],
    //   ]
    // },
    xAxis: [
      {
        name: '记录时间',
        type: 'category',
        data: times
      },
    ],
    yAxis: [
      {
        name: '血红蛋白',
        type: 'value'
      },
    ],
    grid: [
      {
        bottom: '20%'
      },
      {
        top: '20%'
      }
    ],
    series: [
      {
        name: '白蛋白',
        type: 'line',
        // showSymbol: false,
        data: list
      },
    ]
  }

  option && myChart.setOption(option);
  
}
