let params = {}
let url = window.location.href.split("?") || []
if(url[1]){
  let text = url[1].split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
}

WRT_config.url = params || {} // 需要传blid，
var keyid = 0
var ypListAdd = [] //新增药品列表
var yzdata = [] //药品审批
var yzypdata = [] //药品审批
WRT_config.ResData = []
var yzindex = null  //当前编辑行
var yyyzdTb1 = []
var ypindex = 0

// 统一页面启动
$(document).ready(() => {
  
  window.addEventListener('message', function (e) {
    if (e.data) {
      switch (e.data.page) {
        // case `ypsp${ypindex}`://药品审批
        case 'ypsp'://药品审批
					SetSpjg(e.data, false, ypindex)
					break;
      }
    }
  }, false);
  init()
})

/********************初始化********************/
function init() {
  // 展示营养医嘱单  { data: WRT_config.yzTbData }
  WRT_e.api.cwYyyzNew.getYyyzdList({
    params:{ 
      al_blid: parseInt(params["as_blid"])
    },
    success(msg){
      // console.log(JSON.parse(msg.d), '获取患者营养医嘱单',msg);
      WRT_config.yyyzInitData = JSON.parse(msg.d)
      var XYYYZListTb = new XYYYZListTb_View()
      XYYYZListTb.$el = $(".yyyzdTB");
      XYYYZListTb.init({data: WRT_config.yyyzInitData}).render();
    }
  })
  WRT_e.api.yz_sz.getYpyfpl({
    params: { as_lb: 2 },
    success(data) {
      WRT_config.yyplArr = JSON.parse(data.Result)
    }
  })
}


/********************公共方法********************/
// 新增营养医嘱单（打开弹窗） / 详情（只读）
function addYyyz(yzdid,ZTBZ) { // 弹出框 （模块1 + 模块2 + 模块3 + 模块4 ）
  keyid = yzdid
  if (keyid == 0) {
    WRT_e.api.cwYyyzNew.getYhBcQx({
      params:{ 
        al_yhid: sessionStorage.getItem("userid")//（当前用户id获取方式）
      },
      success(msg){
        if (msg.d == '1') {
          Modelshow(yzdid)
        } else {
          WRT_e.ui.hint({
            type: 'info',
            msg:'当前用户无保存无权限!'
          })
        }
      }
    })
  } else {
    Modelshow(yzdid,ZTBZ)
  }
}
function Modelshow(yzdid,ZTBZ) {
   // 获取患者信息数据 （新增情况 模块1）
   WRT_e.api.cwYyyzNew.getInit({
    params:{ 
      al_blid: parseInt(params["as_blid"]),
      al_yzdid: yzdid
    },
    success(msg){
      console.log(JSON.parse(msg.d)[0], '获取dantiao患者营养医嘱单');
      WRT_config.brInfo = JSON.parse(msg.d)[0]
      // // 弹窗
      var addYYYZModal = new addYYYZModal_View();
      addYYYZModal.init().render();
      // if (yzdid!=0) {
        // 医嘱单id，可以为0, 不为0则身高体重，NRS之类的信息从e_GetYyyzd中获取
        // （点击详情 模块1+模块3）
        WRT_e.api.cwYyyzNew.getYyyzd({
          params:{ 
            al_yzdid: yzdid
          },
          success(msg){
            // console.log(JSON.parse(msg.d), '获取身高体重，NRS之类的信息');
            WRT_config.yyyzInitTb = JSON.parse(msg.d)
            // 相关检验项目表  模块4
            WRT_e.api.cwYyyzNew.getHyjg({
              params:{ 
                al_blid: parseInt(params["as_blid"]),
                al_yzdid: keyid,
                as_zyh: WRT_config.brInfo.ZYH
              },
              success(msg){
                // console.log(JSON.parse(msg.d), '获取检验结果',msg);
                WRT_config.noxgjcHyjg = JSON.parse(msg.d)
                // 获取所有营养药品
                WRT_e.api.cwYyyzNew.getYyypList({
                  params:{ 
                    as_yfdm: WRT_config.brInfo.YFDM,
                  },
                  success(msg){
                    // console.log(JSON.parse(msg.d), '所有营养药品',msg);
                    WRT_config.yyydAllForm = JSON.parse(msg.d)
                    // 模块2 营养医嘱单Form表单
                    WRT_e.api.cwYyyzNew.getYyyzd_yp({
                      params:{ 
                        al_yzdid: keyid,
                        al_blid: parseInt(params["as_blid"])
                      },
                      success(msg){
                        // console.log(JSON.parse(msg.d), '药品form对应数据',msg);
                        WRT_config.yyydForm = JSON.parse(msg.d)
                        if (WRT_config.yyydForm.length!=0) {
                          ypListAdd = [] // 打开弹窗清空数据
                          WRT_config.yyydForm.forEach(item=>{
                            ypListAdd.push({
                              YZDID: keyid,  // 医嘱单id
                              YPID: item.YPID, // 药品id
                              YCYL: Number(item.YCYL), // 一次用量
                              YPMC: item.YPMC, // 药品名称
                              JL: item.JL, // 剂量
                              BZL: item.BZL,// 包装量
                              JLDW: item.JLDW, // 剂量单位
                              XSP: null, // 审批类型  保存时必须赋值，不需审批的赋值0
                              SQSL: null, // 申请数量
                              XDFW: null, // 限定范围
                              ZIFEI: null // 1代表自费 -0 公费  保存时此字段必须赋值
                            })
                          })
                        }
                        // 弹窗
                        var addYYYZModal = new addYYYZModal_View();
                        addYYYZModal.init({
                          data:{
                            brInfo: WRT_config.brInfo || {}, // 病人基本信息 （有数据）
                            yyYzdFormData: WRT_config.yyydForm || [], // 营养医嘱单Form表单数据
                            yyYzdFormAllData: WRT_config.yyydAllForm || [], // 营养医嘱单Form表单数据
                            yyYzJSJG: WRT_config.yyyzInitTb || {}, // 计算结果+诊断结果等数据展示（eg. 基础能量消耗: 。。。+ 临床诊断:端坐呼吸:呼吸意吗，未特指.开始时间:2024-07-15 08:28:22持璞输液:）
                            yyYzXGJYTb: WRT_config.noxgjcHyjg || [], // 相关检验项目表 模块4
                            isNew: yzdid == 0?true: false, // 是否新增
                            BTNZT: ZTBZ, // 按钮状态控制
                            PL: WRT_config.yyplArr // 用药频率
                          }
                        }).render();
                      }
                    })
                  }
                })
              }
            })
          }
        })
      // }
    }
  })
}
// 停止（点击停止按钮重新绘制获取数据没变）
function stopBtn(param) {
  WRT_e.api.cwYyyzNew.stopYyyzd({
    params:{ 
      al_zh: param.ZH,
      al_yzdid: param.ID,
      al_yzyhid: sessionStorage.getItem("userid")//（当前用户id获取方式）
    },
    success(msg){
      if (msg.d == '1') {
        // 重新绘制页面表格
        WRT_e.ui.hint({
          type: 'success',
          msg:'停止成功!'
        })
        init()
      } else {
        WRT_e.ui.hint({
          type: 'error',
          msg:'停止失败!'
        })
      }
    },
    catch(err){
      WRT_e.ui.hint({
        type: 'error',
        msg:'停止失败!'
      })
    }
  })
}
// 打印（不确定是否调用接口）
function dyBtn(item) {
  let iframeUrlI = WRT_config.server + `/xypyz/nutriOrder_prt.aspx?as_yzdid=${item.ID}&as_blid=${WRT_config.url["as_blid"]}&as_prt=1`
  var top = (window.screen.availHeight - 30 - height) / 2; //获得窗口的垂直位置;
  var left = (window.screen.availWidth - 10 - width) / 2; //获得窗口的水平位置;
  var height = 800
  var width = 820
  var winOption = "top=" + top + ",left=" + left + ",height=" + height + ",width=" + width + ",resizable=no,scrollbars=auto,status=no,toolbar=no,location=no,directories=no,menubar=no,help=no";
  window.open(iframeUrlI, "", winOption);
}

// 计算数据
function getCalcYywz (item) {
  WRT_config.ResData = []
  $('.saveClick').prop("disabled", false);
  let inputValue =  Number($(`input[name=inputYpVal`+item.YPID+`]`).val());
  let jlCC = ''
  if (ypListAdd.filter(e=>e.YPID == item.YPID).length==0) {
    ypListAdd.push({
      YZDID: keyid,  // 医嘱单id
      YPID: item.YPID, // 药品id
      YCYL: inputValue, // 一次用量
      YPMC: item.MC, // 药品名称
      JL: item.JL, // 剂量
      BZL: item.BZL,// 包装量
      JLDW: item.JLDW, // 剂量单位
      XSP: null, // 审批类型  保存时必须赋值，不需审批的赋值0
      SQSL: null, // 申请数量
      XDFW: null, // 限定范围
      ZIFEI: null // 1代表自费 -0 公费  保存时此字段必须赋值
    })
  } else { 
    let index = ypListAdd.findIndex(e=>e.YPID == item.YPID)
    if (inputValue == '') {
      ypListAdd.splice(index, 1)
    } else {
      ypListAdd[index].YCYL = inputValue
    }
  }
  jlCC = ypListAdd.map(item=>{
    return item.YPID + '-' + item.YCYL
  }).join('^')
  WRT_e.api.cwYyyzNew.CalcYywz({
    params:{ 
      as_ypsllist: jlCC//（当前用户id获取方式）
    },
    success(msg){
      WRT_config.ResData = JSON.parse(msg.d)
      if(WRT_config.ResData.BEE!=null){
        $("input[name='bee']").val(WRT_config.ResData.BEE)// 基础能量消耗
      } else {
        // Beecalumet(WRT_config.nutriOrderListOne.BRXB,WRT_config.nutriOrderListOne.SG,WRT_config.nutriOrderListOne.TZ,Number(WRT_config.nOList_Init.NL))
      }
      if(WRT_config.ResData.FDBNL!=null){
        $("input[name='fdbnl']").val(WRT_config.ResData.FDBNL)// 非蛋白能量
      }
      if(WRT_config.ResData.ZDL!=null){
        $("input[name='zdl']").val(WRT_config.ResData.ZDL)// 总氮量
      }
      if(WRT_config.ResData.ZYTL!=null){
        $("input[name='zytl']").val(WRT_config.ResData.ZYTL)// 总液体量
      }
      if(WRT_config.ResData.T_YDS!=null){
        $("input[name='t_yds']").val(WRT_config.ResData.T_YDS)// 糖:胰岛素
      }
      if(WRT_config.ResData.NL_D!=null){
        $("input[name='nl_d']").val(WRT_config.ResData.NL_D)// 能量:氮
      }
      if(WRT_config.ResData.T_ZF!=null){
        $("input[name='t_zf']").val(WRT_config.ResData.T_ZF)// 糖:脂
      }
      if(WRT_config.ResData.NRS!=null){
        $("input[name='nrs']").val(WRT_config.ResData.NRS)// NRS2002
      }
      if(WRT_config.ResData.BMI!=null){
        $("input[name='bmi']").val(WRT_config.ResData.BMI)// BMI
      } else {
        // BMIcalumet(WRT_config.nutriOrderListOne.SG,WRT_config.nutriOrderListOne.TZ)
      }
      if(WRT_config.ResData.STY!=null){
      $("input[name='sty']").val(WRT_config.ResData.STY)// 渗透压
      }
    }
  })
}
// 计算（基础能量消耗bee———— 获取性别 1男 2女，身高，体重，年龄 ）
function Beecalumet(xb,nl) {
  // WRT_e.api.cwYyyzNew.getBEE({})
  let sg = Number($(".brBaseInfo input[name='brsg']").val())
  let tz = Number($(".brBaseInfo input[name='brtz']").val())
  if(sg==0 || tz==0){
    WRT_e.ui.hint({
      type: 'warning',
      msg:'请输入身高体重!'
    })
  } else {
    WRT_e.api.cwYyyzNew.getBEE({
      params: {
        as_xb: xb,
        ad_sg: sg,
        ad_tz: tz,
        ad_nl: nl
      },
      success(data) {
        calXH_callback(data)
      }
    })
  }
}
function calXH_callback(res) {
  if (res == null) {
      return;
  }
  $("input[name='bee']").val(res.d)
}
// 计算（BMI————获取身高，体重）
function BMIcalumet(sg, tz) {
  // WRT_e.api.cwYyyzNew.getBMI({})
  let bmisg = sg || Number($(".brBaseInfo input[name='brsg']").val())
  let bmitz = tz || Number($(".brBaseInfo input[name='brtz']").val())
  if(bmisg==0 || bmitz==0){
    WRT_e.ui.hint({
      type: 'warning',
      msg:'请输入身高体重!'
    })
  } else {
    WRT_e.api.cwYyyzNew.getBMI({
      params: {
        ad_sg: bmisg,
        ad_tz: bmitz
      },
      success(data) {
        calBMI_callback(data)
      }
    })
  }
}
function calBMI_callback(res) {
  if (res == null) {
      return;
  }
  $("input[name='bmi']").val(res.d)
}

function validateNumber(el) {
  $('.saveClick').prop("disabled", false);
  var value = el.value;
  var valid = /^[0-9]*\.?[0-9]*$/.test(value); // 正则表达式判断是否为数字或小数
  if (!valid) {
    // 如果输入无效，则设置为上一个有效的值
    el.value = el.value.replace(/[^0-9.]/g, ''); 
  } else {
    const numStr = value.toString();
    // 分割小数点前后的字符串
    const parts = numStr.split('.');
    // 如果小数点后面有部分，则返回小数位数，否则返回0
    const partsLong =  parts.length === 2 ? parts[1].length : 0;
    if (partsLong>2) {
      el.value=el.value.toString().match(/^\d+(?:\.\d{0,2})?/)
    } else {
      if (partsLong == 0 && (el.value[0]=='0' && el.value[1])) {
        el.value =  el.value.replace(/^0+([1-9]+)$/, '$1')
      }
    }
  } 
}

//修改复选框，实现单选框效果
function editCheckboxTrue(that,type) {
	let text='XDFW_table'
	if(type){
		text='LiShiXDFW_table'
	}
	var answer_checkbox_list = $(`#${text} input[type=checkbox]`);
	var answer_checkbox_lenth = answer_checkbox_list.length;
	//循环得到此次
	for (var i = 0; i < answer_checkbox_lenth; i++) {
		answer_checkbox_list[i].checked = false;
	}
	that.checked = true;
}

//药品审批
function isxsp(index, yzlx, obj) {
	let list = ypListAdd[index]
  ypindex = index
  // 本院药品审批，临床路径就是走这里
  // WRT_config.brInfo.jslx == '00'// 要删的
  let xzfw_type = ''
  if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
    xzfw_type = '[]'
  } else if (WRT_config.ypfjxx.xzfw_bbzt == 2) {
    xzfw_type = ''
  }
  if (WRT_config.ypfjxx.xsp == "0" && WRT_config.ypfjxx.xzfw == xzfw_type) {//xzfw==[]
  //   if (WRT_config.brInfo.jslx!='00') {
  //     WRT_e.ui.message({
  //       title: '信息窗口',
  //       content: WRT_config.ypfjxx.ypmc+'药为自费药品,请务必告知病人签字后方可使用!',
  //       onOk() {
  //         if (ypindex != yzdata.length-1) {
  //           ypFjxxNow(ypindex+1)
  //         } else {
  //           satrSave(yzdata)
  //         }
  //       }
  //     })
  //     return;
  //   }
    //  继续下一个药品
    if (ypindex != yzdata.length-1) {
      ypFjxxNow(ypindex+1)
    } else {
      satrSave(yzdata)
    }
    return;
  }
  else if (WRT_config.ypfjxx.xsp == '0' && WRT_config.ypfjxx.xzfw != xzfw_type && (WRT_config.ypfjxx.zhbz == "1300" && (yzlb == "1" || yzlb == "3")) ) {
    //无需审批
    // 继续下一个药品
    if (ypindex != yzdata.length-1) {
      ypFjxxNow(ypindex+1)
    } else {
      satrSave(yzdata)
    }
    return;
  }
  else {
    if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
      // 保存参数写入
      ypListAdd[index].XSP = WRT_config.ypfjxx.xsp
      // 审批弹窗
      let html = ypsp_html(WRT_config.ypfjxx, index)
      WRT_e.ui.model({
        // id: `if_ypsp${index}`,
        id: 'if_ypsp',
        title: "医保限制支付范围提示",
        width: "550px",
        content: html,
        closeButton: false,
        closeOnEscape: false,
        iframe: false,
      })
    } else if (WRT_config.ypfjxx.xzfw_bbzt == 2) {
      let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.brInfo.jslx || '00'}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=${ypindex}`
      // 保存参数写入
      ypListAdd[index].XSP = WRT_config.ypfjxx.xsp

      // 审批弹窗
      // console.log(ypindex,index,'审批', url,222,WRT_config.ypfjxx,333,WRT_config.ypfjxx.xzfw,999,WRT_config.brInfo.LCZD);
      WRT_e.ui.model({
        // id: `ypsp${index}`,
        id: 'ypsp',
        title: "医保限制支付范围提示",
        width: "650px",
        iframeURL: url,
        closeButton: false,
        closeOnEscape: false,
        iframe: true,
      })
      $(`#ypsp iframe`).load(function () {
        $(`#ypsp iframe`).contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
        $(`#ypsp iframe`).contents().find("#tb_brzd").text(WRT_config.brInfo.LCZD);
      })
    } else {
      ypListAdd[index].XSP = 0
    }
    return
  }
}
//药品审批
function SetSpjg(data, type,indexDQYP) {
	let index = indexDQYP
  // let name = `ypsp${index}`
  let name = 'ypsp'
  ypindex = index
	if (type) {
		// name = `if_ypsp${index}`
		name = 'if_ypsp'
	}
	if (data) {
		if (data.sfzf == '9') {
			$(`#${name}`).iziModal('destroy')
			// delZHTXT(index)
			yzdata.splice(index, 1)
			return
		}
    // console.log(yzdata[index],data,WRT_config.ypfjxx);
		yzdata[index].XSP = data.splb || data.xsp // 审批类型  保存时必须赋值，不需审批的赋值0
    yzdata[index].SQSL = data.spsl || data.SPSL || 0 // 申请数量
    // yzdata[index].SQSL = data.SPSL // 申请数量
		yzdata[index].XDFW = data.xdfw || WRT_config.ypfjxx.xzfw // 限定范围
    if (name ==  'if_ypsp') {
      if (data.xdfw.indexOf('自费')== -1) {
        yzdata[index].ZIFEI = '0'
      } else { // 自费
        yzdata[index].ZIFEI = '1'
      }
    } else{
      yzdata[index].ZIFEI = data.sfzf // 1代表自费 -0 公费  保存时此字段必须赋值
    }
    // console.log('结果',yzdata[index]);
    if (ypindex != yzdata.length-1) {
      ypFjxxNow(ypindex+1)
    } else {
      satrSave(yzdata)
    }
		$(`#${name}`).iziModal('destroy')
	} else {
		// delZHTXT(index) // 原本删除组好，这里没有组号
    // ypListAdd.splice(index, 1)
		yzdata.splice(index, 1)
    if (ypindex-1 != yzdata.length-1) {
      ypFjxxNow(ypindex)
    } else {
      satrSave(yzdata)
    }
		$(`#${name}`).iziModal('destroy')
		return
	}
}
//药品审批html
function ypsp_html(ypfjxx, indexNow) {
	let text = ''
	let zxfw = JSON.parse(ypfjxx.xzfw)
	let type = false
	let arr = []
	WRT_config.XDFW_list=null
	if(zxfw){
		WRT_config.XDFW_list = zxfw.map(function (item) {
			return item.XDFW
		})
    arr = [...zxfw, { XDFW: '均不符合，需自费，请告知患者' }]
	} else {
    arr = [{ XDFW: '均不符合，需自费，请告知患者' }]
	}
	
	if (ypfjxx.xsp == '0') {
		text = ''
		type = false
	} else if (ypfjxx.xsp == '1') {
		type = true
		text = '该项目为需【医保窗口】审批项目。'
	} else if (ypfjxx.xsp == '2') {
		type = true
		text = '该项目为需【药房】和【医保窗口】审批项目。'
	} else if (ypfjxx.xsp == '3') {
		type = true
		text = '该项目为需【药房】审批项目。'
	} else if (ypfjxx.xsp) {
		type = true
		text = '该项目为需审批项目。'
	}
	ybxz_list = arr
	// <textarea name="tb_sbxzfw" rows="2" cols="20" id="tb_sbxzfw" style="height:130px;width:369px;">${ypfjxx.xzfw}</textarea>
	let temp = `
    <div id="XDFW_table">
        <span style="font-weight:bold;float: left; text-align: start;">药品名称：${ypfjxx.ypmc}</span><br>
        ${arr.length>0?`<span id="Label1" style="font-weight:bold;float: left; text-align: start;">医保使用限制范围如下，请根据疾病诊断准确选择：</span><br>
        <ul id="xdfw_lists" style="height:130px;width:369px;border: 1px solid;overflow: auto;padding: 3px 5px;text-align: start;">
        ${_.map(arr, (item, index) => `
        <li><input type="checkbox" name="xzfw_check" onclick="editCheckboxTrue(this)" value="${item.XDFW}" data><span ${index == arr.length - 1 ? 'style="color:red"' : ''}>${item.XDFW}</sapn></li>
        `).join('')}
        </ul>`:''}
        <br>
        <span id="Label2" style="font-weight:bold;float: left; text-align: start;">病人诊断</span>&nbsp;<br>
        <textarea name="tb_brzd" rows="2" cols="20" id="tb_brzd" disabled="true" style="height:74px;width:367px;float: left; text-align: start;">${WRT_config.brInfo.LCZD}</textarea><br>
        ${ypfjxx.xsp != 0&&arr.length>0 ? `<span id="lb_tsxx" style="display:inline-block;height:49px;width:372px;">${text}</span><br>` : ''}
        <span style="display:inline-block;height:49px;width:372px;color:red;float: left; text-align: start;">注：请根据患者病情如实勾选并在病历中体现！</span><br>
        <table style="width: 370px" cellpadding="0" cellspacing="0">
            <tbody>
                <tr id="tr_spxm">
                    ${ypfjxx.xsp != 0 && ypfjxx.xsp ? `<td style="width: 180px; height: 24px;padding: 3px 0;"><span id="Label3">申请审批数量</span>
                        <input name="tb_spsl" type="text" id="tb_spsl" style="width:59px;border: 1px solid #736b6b;">
                    </td>`: ''}
                </tr>
                <tr align="center">
                    <td style="width: 180px; height: 15px;" align="center">
                        <button class="e_btn" value="确定" onclick='bt_xdfwclick(${JSON.stringify(ypfjxx)},1,${indexNow});return false;' id="bt_qd" style="width:160px;">${type ? '确认提交审批' : '确定'}</button>
                    </td>
                    <td style="width: 180px; height: 15px;" align="center">
                        <button class="e_btn" value="取消" onclick='SetSpjg("",true,${indexNow});return false;' id="bt_qx" style="width:160px;">取消</button>
                    </td>
                </tr>
            </tbody>
        </table>
        &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
        <br>
      </div>
    `
	return temp
}
//审批确定
function bt_xdfwclick(itemNow,lx, indexNow) {
	let check = $('#XDFW_table input[type=checkbox]:checked')
	let xdfw = ''
	if (check[0]) {
		xdfw = check[0].value
	// } else if (check.length == 0 && (WRT_config.XDFW_list && WRT_config.XDFW_list.length>0) || ybxz_list.length>0) {
	} else if (check.length == 0 && (WRT_config.XDFW_list && WRT_config.XDFW_list.length>0)) {
		alert("社保限制范围必须勾选");
		return
	}
	// if(WRT_config.ypfjxx.shangCiXdfw!=xdfw&&xdfw){
	// 	WRT_e.ui.message({
	// 		title: '提示信息',
	// 		content: '当前选择的限制范围与历史选择不一致!',
	// 		onOk() {
	// 			bt_click(lx,xdfw)
	// 		},
	// 		onCancel() { }
	// 	})
	// }else{
		bt_click(lx,xdfw,indexNow, itemNow)
	// }

}
function bt_click(lx,xdfw,indexNow, itemNow) {
	var splb = WRT_config.ypfjxx.xsp;
	var obj = new Object();
	obj.SFZF = "";
	obj.SPLB = splb;
	obj.xdfw = xdfw
	if (lx == '1') {
		if (splb == "0") {
			obj.SFZF = "0";
			if (obj.xdfw == '均不符合，需自费，请告知患者') {
				obj.SFZF = "1";
			}
		}
		else {
			// if (ybxz_list.length>0) {
			// 	if (splb != "3") {
			// 		if (WRT_config.yz_sz.jslx == "00")
			// 			obj.SFZF = "0";
			// 		else {
			// 			if ((WRT_config.XDFW_list && WRT_config.XDFW_list.length>0) && WRT_config.XDFW_list.indexOf(obj.xdfw) >= 0)
			// 				obj.SFZF = "0";
			// 			if (obj.xdfw == '均不符合，需自费，请告知患者')
			// 				obj.SFZF = "1";
			// 		}
			// 		if (obj.SFZF == "") {
			// 			alert("请选择 是否自费还是公费!");
			// 			return;
			// 		}
			// 	}
			// 	else {
			// 		obj.SFZF = 0;
			// 	}
			// }
			// if(ybxz_list.length==0){
			// 	obj.SFZF = 0;
			// }
			// 审批数量
			var spsl = $("#tb_spsl")[0].value;
			if (spsl == "") {
				alert("请输入审批数量");
				return;
			}
			if (isNaN(spsl)) {
				alert("审批数量请输入数字!")
				return;
			}
			if (parseFloat(spsl) <= 0) {
				alert("审批数量不能为零或负数!");
				return;
			}
			if (obj.xdfw.indexOf('需自费')==-1) {
				if (parseFloat(spsl) > parseFloat(WRT_config.ypfjxx.wgypsqsl)) {
					alert("申请数量最大只能填30!");
					return;
				}
			}
			obj.SPSL = spsl;
		}
	}
	if (obj.SPSL == "" || obj.SPSL == undefined){
		obj.SPSL = 0;
	}
  let itemObj = {...itemNow,...obj}
  // console.log(3333,itemObj);
	SetSpjg(itemObj, true, indexNow)
}

// 保存
function save(allData) {
  // console.log('9999suan',ypListAdd,yzdata);
  // 表1参数
  if (yzdata.length==0) {
    getCalcYywz (ypListAdd[0])
  }
  yyyzdTb1 = []
  if (($('input[name=cxts]').val().trim() && $('input[name=cxts]').val().trim()!='') && 
  ($('input[name=zxsysj]').val().trim() && $('input[name=zxsysj]').val().trim()!='') && 
  ($('input[name=brsg]').val().trim() && $('input[name=brsg]').val().trim()!='') && 
  ($('input[name=brtz]').val().trim() && $('input[name=brtz]').val().trim()!='')) {
    yyyzdTb1.push({
      ID: keyid, //	Long	医嘱单id
      BLID: parseInt(WRT_config.url["as_blid"]), //	Long	患者blid
      BRXM: allData.brInfo.BRXM, // String	患者姓名
      BRXB: allData.brInfo.BRXB, //	String	患者性别
      ZYH: allData.brInfo.ZYH, //	String	住院号
      ZKID: Number(allData.brInfo.ZKID), //	Long	专科id
      BQID: Number(allData.brInfo.BQID), //	Long 	病区id
      CWH: allData.brInfo.CWH, //	String	床位号
      // YZSJ: '', // Datetime	医嘱时间 不传
      YZYSYHID: allData.yyYzJSJG.YZYSYHID!=null?Number(allData.yyYzJSJG.YZYSYHID):Number(sessionStorage.getItem('userid')), // Long	医嘱用户id
      ZTBZ: '1', //	String	状态标志 0停止  1未提交  2已提交 默认传1
      SG: Number($('.brInfoForm input[name=brsg]').val()), //	Double	身高
      TZ: Number($('.brInfoForm input[name=brtz]').val()), //	Double	体重
      LCZD: $('input[name=lczd]').val(), //	String	临床诊断
      BEE: Number($("input[name='bee']").val()), //	Double	Bee
      FDBNL: Number($('input[name=fdbnl]').val()), //	Double	非蛋白能量
      ZDL: Number($('input[name=zdl]').val()), //	Double	总氮量
      ZYTL: Number($('input[name=zytl]').val()), //	Double	总液体量
      T_YDS: $('input[name=t_yds]').val(), //	String	糖：胰岛素
      NL_D: $('input[name=nl_d]').val(), //	String	能量：氮
      T_ZF: $('input[name=t_zf]').val(), //	String	糖：脂肪
      NRS: Number($('input[name=nrs]').val()), // 	Double	NRS
      BMI: Number($('input[name=bmi]').val()), //	Double	BMI （体重指数）
      KSSJ: $('input[name=kssj]').val().replace('T',' '), //	Datetime	开始时间
      CXTS: Number($('input[name=cxts]').val()), //	Int	持续天数
      ZXPL: $('.changeYypl option:selected').val(), // 	String	执行频率
      STY: Number($('input[name=sty]').val()), //	Double	渗透压
      // ZH: '', //	Long	药品组号 不传
      // JSSJ: '', //	Datetime	结束时间 小于当前时间显示”已到期” 不传
      XGYSYHID: sessionStorage.getItem('userid'), // Long	修改医师用户id
      ZXSYSJ: Number($('input[name=zxsysj]').val()), // Double	最小输液时间
      ZXZXSJ: Number($('input[name=zxsysj]').val()), // Double	最小输液时间
      JIGOUDM: '01', //	String	机构代码
      YFDM: WRT_config.brInfo.YFDM, 
    })
    // 表2参数
    // yzdata = [...ypListAdd] // 药品审批
    yzdata = ypListAdd // 药品审批
    ypFjxxNow(0)
  } else {
    if ($('input[name=cxts]').val().trim()=='') {
      WRT_e.ui.hint({
        type: 'warning',
        msg:'请先填写持续输液天数!'
      })
    }
    if ( $('input[name=zxsysj]').val().trim()=='') {
      WRT_e.ui.hint({
        type: 'warning',
        msg:'请先填写最小输液时间!'
      })
    }
    if ( $('input[name=brsg]').val().trim()=='') {
      WRT_e.ui.hint({
        type: 'warning',
        msg:'请先填写身高!'
      })
    }
    if ( $('input[name=brtz]').val().trim()=='') {
      WRT_e.ui.hint({
        type: 'warning',
        msg:'请先填写体重!'
      })
    }
  }
}
function ypFjxxNow(index) {
  ypindex = index
  WRT_e.api.yz_sz.getYpFjxx({
    params: { 
      al_ypid: yzdata[ypindex].YPID, 
      as_yfdm: WRT_config.brInfo.YFDM || "", 
      as_jslx: WRT_config.brInfo.jslx || "00", 
      al_zyid: WRT_config.brInfo.ZYID, 
      as_yzlx: 'yp', 
      al_zkid: WRT_config.brInfo.ZKID,
      gcp: ''
    },
    success(data) {
      if (data.Code == 1) {
        // console.log(999,JSON.parse(data.Result));
        WRT_config.ypfjxx = JSON.parse(data.Result)[0]
        // yzdata[ypindex].SQSL = WRT_config.ypfjxx
        // yzdata[ypindex].XDFW = 
        yzdata[ypindex].XSP = WRT_config.ypfjxx.xsp
        isxsp(ypindex, 'yp', WRT_config.ypfjxx)
      }
    }
  })
}
function satrSave(saveData) {
  var params = {
    am_yyyzd: yyyzdTb1,  // 医嘱单（表格一）新增id传0，不要传空
    ast_yplist: saveData, // 医嘱单中选择的药品列表（表格二）
    ast_hyjglist:	WRT_config.noxgjcHyjg, //	医嘱单中的检验结果列表（表格三）
    as_yfdm: WRT_config.brInfo.YFDM,	//	药房代码 数据来源
  }
  // console.log('保存',params,ypListAdd,saveData);
  WRT_e.api.cwYyyzNew.yyyzSave({
    params: {
      am_yyyzd: yyyzdTb1, // 医嘱单（表格一）新增id传0，不要传空
      ast_yplist: saveData, // 医嘱单中选择的药品列表（表格二）
      ast_hyjglist:	WRT_config.noxgjcHyjg, //	医嘱单中的检验结果列表（表格三）
      as_yfdm: WRT_config.brInfo.YFDM,	//	药房代码 数据来源
    },
    success(msg){
      // console.log('保存',msg,Number(msg.d) && Number(msg.d)!=0);
      if (Number(msg.d) && Number(msg.d)!=0) {
        keyid = msg.d
        ypListAdd = []
        WRT_e.ui.hint({
          type: 'success',
          msg:'保存成功!'
        })
        $('.saveClick').prop("disabled", true);
        init()
      } else {
        ypListAdd = []
        WRT_e.ui.hint({
          type: 'error',
          msg:'保存失败!'
        })
      }
    }
  })
}

// 发送
function subForm(allData) {
  WRT_e.api.cwYyyzNew.getYyyzd({
    params:{ 
      al_yzdid: keyid
    },
    success(msg){
      // console.log(JSON.parse(msg.d), '获取身高体重，NRS之类的信息');
      WRT_config.yyyzInitTb = JSON.parse(msg.d)
      var time1 = Date.parse(new Date()); // 实际时间时间
      var time2 = Date.parse(new Date(WRT_config.yyyzInitTb.KSSJ)); // 开始时间
      let dateDiff = (time1 - time2) // 时间相差秒数
      let hours = Math.floor(dateDiff / (3600 * 1000));
      // console.log(keyid,allData,WRT_config.yyyzInitTb.KSSJ,hours);
      if(hours>1){
        WRT_e.ui.message({
          title: '信息窗口',
          content: '医嘱单开始时间不可比当前时间提前一个小时以上',
          onOk() {}
        })
      } 
      else {
        WRT_e.api.cwYyyzNew.getYhTjQx({
          params:{ 
            al_yzdid: keyid,
            al_yhid: sessionStorage.getItem("userid")//（当前用户id获取方式）
          },
          success(msg){
            if (msg.d == '1') { // 1 有权限 其他无权限，直接提示
              if (keyid==0) { // 新增
                WRT_e.ui.hint({
                  type: 'info',
                  msg:'请先保存后再发送!'
                })
              } else{
                WRT_e.api.cwYyyzNew.yyyzSubmit({
                  params:{ 
                    al_yzdid: keyid,
                    al_zyid: WRT_config.brInfo.ZYID,
                    as_yfdm: WRT_config.brInfo.YFDM,
                    al_yzyhid: sessionStorage.getItem("userid")//（当前用户id获取方式）
                  },
                  success(msg){
                    if (msg.d = '1') {
                      WRT_e.ui.hint({
                        type: 'success',
                        msg:'药品已成功提交,如该病区已启用医嘱确认,请及时审核医嘱!'
                      })
                      init()
                      $('#addYYYZD').iziModal('destroy')
                    } else {
                      WRT_e.ui.hint({
                        type: 'error',
                        msg: msg.d
                      })
    
                    }
                  }
                })
              }
            } else {
              WRT_e.ui.hint({
                type: 'error',
                msg:'当前账号无发送权限!'
              })
            }
          }
        })
      }
    }
  })
  // $('#addYYYZD').iziModal('destroy')
}
function btnShow() {
  $('.saveClick').prop("disabled", false);

}
/******************** 视图 ********************/
// 营养医嘱表格
var XYYYZListTb_View = WRT_e.view.extend({
  render: function () {
    let html=`
    <div class="overall_table_frame">
      <table id="yyyz_table">
        <thead>
          <tr class="row_head">
            <th style="text-align: center;min-width: 100px;">开始时间</th>
            <th style="text-align: center;min-width: 100px;">持续天数</th>
            <th style="text-align: center;min-width: 64px;">结束时间</th>
            <th style="text-align: center;min-width: 146px;">开单医师</th>
            <th style="text-align: center;min-width: 64px;">停止医师</th>
            <th style="text-align: center;min-width: 146px;">医嘱状态</th>
            <th style="text-align: center;min-width: 200px;">医嘱状态</th>
          </tr>
        </thead>
        <tbody class="line_nr" id="line_nr">
        ${this.data.length>0?`
          ${_.map(this.data,(item,index)=>`
          <tr class="row_nr">
            <td calss="kssj">${item.KSSJ?item.KSSJ.replace('T',' '):''}</td>
            <td calss="cxts">${item.CXTS?item.CXTS:''}</td>
            <td calss="jssj">${item.JSSJ?item.JSSJ.replace('T',' '):''}</td>
            <td calss="kdys">${item.KDYS?item.KDYS:''}</td>
            <td calss="tzys">${item.TZYS?item.TZYS:''}</td>
            <td calss="yzzt">${item.ZTBZ?item.ZTBZ==0?'停止':item.ZTBZ==1?'未提交':item.ZTBZ==2?'已提交':'':''}</td>
            <td>
              <a herf="javascript:void(0)" calss='checkXQ' onclick='addYyyz(${item.ID},${item.ZTBZ})' style='position: relative;padding-right: 36px;cursor: pointer;'>详细</a>
              <a herf="javascript:void(0)" calss='checkXQ' onclick='stopBtn(${JSON.stringify(item)})' style='position: relative;padding-right: 36px;cursor: pointer;'>停止</a>
              <a herf="javascript:void(0)" calss='checkXQ' onclick='dyBtn(${JSON.stringify(item)})' style='position: relative;cursor: pointer;'>打印</a>
            </td>
          </tr>
          `).join('')}
        `:``}
        </tbody>
      </table>
    </div>`
    this.$el.html(html)
    return this;
  }
})
// 新增弹窗
var addYYYZModal_View = WRT_e.view.extend({
  render: function () {
    console.log(this.data); // ZTBZ
    let html=`
    <div class="addModelW">
      <h3>肠外营养医嘱单</h3>
      <hr>
      ${this.data?`
        <div class="brBaseInfo">
        ${this.getBrInfo()}
        </div>
        <div class="yyyzFormBD">
          <div class="Ltitle label-name">
            <span style="font-weight: bold;position: relative;float: left;">营养医嘱单</span>
          </div>
          <div class="yzdForm">
            ${this.getYyyzd()}
          </div>
        </div>
        <div class="yyyzFormTb">
          <div class="Ltitle label-name">
            <span style="font-weight: bold;position: relative;float: left;">营养医嘱结果汇总</span>
          </div>
          <div class="sjhzTb">
          ${this.getYyyzResData()}
          </div>
        </div>
        <div class="yyyzXGJYXM">
          <div class="Ltitle label-name">
            <span style="font-weight: bold;position: relative;float: left;">相关检验项目</span>
          </div>
          <div class="XGJYXMTb">
            ${this.getXGJYXM()}
          </div>
        </div>
        <div>
          <button class="e_btn subClick" onclick='subForm(${JSON.stringify(this.data)})' ${this.data.isNew?``:this.data.BTNZT!=1?`disabled`:``}>发送</button>
          <button class="e_btn e_btn_primary saveClick" onclick='save(${JSON.stringify(this.data)})' ${this.data.isNew?``:this.data.BTNZT==1?`disabled`:`style="display:none"`}>保存</button>
        </div>
      `:`
        <div class="zgc_model">
          <img src="./images/loading.gif" style="width: 360px;">
        </div>
      `}
    </div>
    `
    WRT_e.ui.model({
      id: "addYYYZD",
      title: '肠外营养医嘱单',
      width: "1200px",
      height:"400px",
      content: html,
      // closeButton:false,
      // closeOnEscape:false,
      iframe: false,
    })
    this.$el.html(html)
    return this;
  },
  // 病人基本信息
  getBrInfo: function () { // 详情yyYzJSJG；新增brInfo
    // console.log('信息',this.data);
    return `
    <div class="Ltitle label-name">
      <span style="font-weight: bold;position: relative;float: left;">患者基本信息</span>
    </div>
    <div class="brInfoForm"> 
      <div class='infoCondition' style='width:8%;min-width:100px;'>
        <div class="control-inner flex-select control_xm"> 
          ${this.data.isNew && this.data.BTNZT==1?this.data.brInfo.BRXM || '':this.data.yyYzJSJG.BRXM || ''}
        </div>
      </div>
      <div class='infoCondition'>
        <div class="control-inner flex-select control_xb"> 
          ${this.data.isNew && this.data.BTNZT==1?this.data.brInfo.BRXB==1?'男':'女':this.data.yyYzJSJG.BRXB==1?'男':'女'}
        </div>
      </div>
      <div class='infoCondition' style='width:6%;min-width:80px;'>
        <div class="control-inner flex-select control_nl"> 
          ${this.data.brInfo.NL || ''} 
        </div>
      </div>
      <div class='infoCondition' style='width:10%;min-width:160px;'>
        <label class="radio-inline" style="padding-left: 2px;">
          <span> 身高： </span>
          &nbsp;&nbsp;
          <input class="allInput" type="number" oninput="validateNumber(this)" name="brsg" value="${this.data.isNew && this.data.BTNZT==1?this.data.brInfo.SG?this.data.brInfo.SG:'':this.data.yyYzJSJG.SG?this.data.yyYzJSJG.SG:''}" style="width:55px" ${!this.data.isNew?`readonly`:``} /> &nbsp;
          CM
        </label>
      </div>
      <div class='infoCondition' style='width:10%;min-width:160px;'>
        <label class="radio-inline" style="padding-left: 2px;">
          <span> 体重： </span>
          &nbsp;&nbsp;
          <input class="allInput" type="number" name="brtz" oninput="validateNumber(this)" value="${this.data.isNew && this.data.BTNZT==1?this.data.brInfo.TZ?this.data.brInfo.TZ:'':this.data.yyYzJSJG.TZ?this.data.yyYzJSJG.TZ:''}" style="width:55px" ${!this.data.isNew?`readonly`:``} /> &nbsp;
          KG
        </label>
      </div>
      <div class='infoCondition' style='width:10%;min-width:100px;'>
        <div class="control-inner flex-select control_bq"> 
          ${this.data.brInfo.BQMC}
        </div>
      </div>
      <div class='infoCondition' style='width:10%;min-width:72px;'>
        <div class="control-inner flex-select control_cwh"> 
          ${this.data.isNew && this.data.BTNZT==1?this.data.brInfo.CWH+' 床' || '':this.data.yyYzJSJG.CWH+' 床' || ''}
        </div>
      </div>
      <div class='infoCondition' style='width:12%;min-width:176px;'>
        <label class="radio-inline" style="padding-left: 2px;">
          <span> 病案号： </span>
          &nbsp;&nbsp;
          <div class="control-inner flex-select control_bah"> 
            ${this.data.brInfo.GLOBAL_PID}
          </div>
        </label>
      </div>
    </div>
    ${(this.data.brInfo.SG==undefined || this.data.brInfo.SG=='') && (this.data.brInfo.TZ==undefined || this.data.brInfo.TZ=='') ?`
      <div style="text-align: center;color: red; font-weight:bold;">(病人身高体里信息不全，请您虽好先在住院病历二中将病人身高，体里信息补充完整)</div>
    `:``}
    `  
  },
  // 营养医嘱单Form表单 
  getYyyzd: function () {
    return `
    ${_.map(this.data.yyYzdFormAllData,(item)=>`
      <label class="radio-inline1">
        <span class="formLName" style="max-width: 300px;width: 300px;text-align: start;">${item.MC}</span>
        <span class="formLName" style="max-width: 220px;width: 220px;display: flex;align-items: center;justify-content: space-between;border-right: 1px solid #d9d9d9;"> 
          <span>${item.JL} *&nbsp;</span>
          <span style="width:75px;" class="inputNowRed">
            ${this.data.yyYzdFormData.filter(e=>e.YPID == item.YPID).length!=0?`
              <input class="allInput showData" oninput="validateNumber(this)" type="number" name="inputYpVal${item.YPID}" data-ypid="${item.YPID}" onchange='getCalcYywz(${JSON.stringify(item)})' value="${this.data.yyYzdFormData.filter(e=>e.YPID == item.YPID)[0].YCYL}" style="width:50px;height:24px;color: red !important;" ${!this.data.isNew && this.data.BTNZT!=1?`readonly`:``} /> &nbsp;${item.JLDW}
            `:`
              <input class="allInput editData" oninput="validateNumber(this)" type="number" name="inputYpVal${item.YPID}" data-ypid="${item.YPID}" onchange='getCalcYywz(${JSON.stringify(item)})' style="width:50px;height:24px;color: red !important;" ${!this.data.isNew && this.data.BTNZT!=1?`readonly`:``} /> &nbsp;${item.JLDW}
            `}
          </span>
        </span>
      </label>
    `).join('')}
    `
  },
  // 营养医嘱结果汇总
  getYyyzResData: function () { // yyYzJSJG
    // console.log(this.data.yyYzJSJG);
    return `
    <table border="1" align="start" class="show_Table">
      <tbody>
        <tr>
          <td>
            <tr align="start">
              <td class="table-nr">
                <span>
                  <span>基础能量消耗：
                    <input class="allInput" style="width:50px;height:24px;color: red !important;" name="bee" value="${this.data.yyYzJSJG.BEE || 0}" type="text" readonly/>
                  </span>
                  <span>Kcal/d
                    <a herf="javascript:void(0)" onclick="Beecalumet(${this.data.brInfo.BRXB},${this.data.brInfo.NL?Number(this.data.brInfo.NL.replace('岁','')):0})">(计算)</a></span>
                </span>
              </td>
              <td class="table-nr">
                <span>
                  <span>非蛋白能量：
                    <input class="allInput" style="width:50px;height:24px;color: red !important;" name="fdbnl" value="${this.data.yyYzJSJG.FDBNL || 0}" type="text" readonly/>
                  </span>
                  <span>Kcal</span>
                </span>
              </td>
              <td class="table-nr">
                <span>
                  <span>总氮量：
                    <input class="allInput" style="width:50px;height:24px;color: red !important;" name="zdl" value="${this.data.yyYzJSJG.ZDL || 0}" type="text" readonly/>
                  </span>
                  <span>g</span>
                </span>
              </td>
              <td class="table-nr">
                <span>
                  <span>总液体量：
                    <input id="zytlVal" style="width:50px;height:24px;color: red !important;" name="zytl" class="allInput" value="${this.data.yyYzJSJG.ZYTL || 0}" type="text" readonly/>
                  </span>
                  <span>ml</span>
                </span>
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr">
                <span>
                  <span>糖:胰岛素: 
                    <input class="allInput" style="width:70px;height:24px;color: red !important;" name="t_yds" value="${this.data.yyYzJSJG.T_YDS || 0}" type="text" readonly/>
                  </span>
                <span>
              </td>
              <td class="table-nr">
                <span>能量:氮: 
                  <input class="allInput" style="width:70px;height:24px;color: red !important;" name="nl_d" value="${this.data.yyYzJSJG.NL_D || 0}" type="text" readonly/>
                </span>
              </td>
              <td class="table-nr">
                <span>糖:脂: 
                  <input class="allInput" style="width:70px;height:24px;color: red !important;" name="t_zf" value="${this.data.yyYzJSJG.T_ZF || 0}" type="text" readonly/>
                </span>
              </td>
              <td class="table-nr">
                <span>NRS2002: 
                  <input class="allInput" style="width:70px;height:24px;color: red !important;" name="nrs" value="${this.data.yyYzJSJG.NRS || 0}" type="text" readonly/>
                </span>
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr">
                <span>BMI: 
                  <input class="allInput" style="width:50px;height:24px;color: red !important;" name="bmi" value="${this.data.yyYzJSJG.BMI || 0}" type="text" readonly/>
                </span>
                <span><a herf="javascript:void(0)" onclick="BMIcalumet(${this.data.brInfo.SG},${this.data.brInfo.TZ})" >(计算)</a></span>
              </td>
              <td class="table-nr">
                <span>渗透压: 
                  <input class="allInput" style="width:50px;height:24px;color: red !important;" name="sty" value="${this.data.yyYzJSJG.STY || 0}" type="text" readonly/>
               </span>
              </td>
              <td colspan="2"></td>
            </tr>
            <tr align="start">
              <td class="table-nr" colspan="4">
                <span>临床诊断: 
                <input class="allInput" style="position: relative;width: calc(100% - 70px);color: red !important;" name="lczd" value="${this.data.isAdd?'':this.data.yyYzJSJG.LCZD || ''}" type="text" ${!this.data.isNew && this.data.BTNZT!=1?`readonly`:``}/>
                </span>
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr1" colspan="4" style="padding:5px 10px">
                <span>
                  <span style="min-width:245px">开始时间: 
                    <input class="allInput" name="kssj" onchange="btnShow()" value="${this.data.yyYzJSJG.KSSJ?(this.data.yyYzJSJG.KSSJ):new Date().Format("yyyy-MM-ddTHH:mm:ss")}" type="datetime-local" ${!this.data.isNew && this.data.BTNZT!=1?`readonly`:``}/>
                  </span>
                  <span style="padding-right:20px">持续输液: 
                    <input class="allInput" name="cxts" type="number" oninput="validateNumber(this)" value="${this.data.yyYzJSJG.CXTS || ''}" style="width:48px" type="text"  ${!this.data.isNew && this.data.BTNZT!=1?`readonly`:``}/>&nbsp;&nbsp;天
                  </span>
                  <span style="padding-right:20px">用药频率:
                    <select ${!this.data.isNew && this.data.BTNZT!=1?`disabled="disabled" `:""} class="changeYypl Btn_nrInput" name="jjjb" style="width:80px;"> 
                      ${!this.data.isNew && this.data.BTNZT!=1 && this.data.yyYzJSJG.ZXPL?`
                        <option value="${this.data.yyYzJSJG.ZXPL}" name="yypl" class="yypl" ${!this.data.isNew && this.data.BTNZT!=1?`readonly`:``}>${this.data.yyYzJSJG.ZXPL}</option>`:`
                        ${_.map(this.data.PL,(item,index)=>`
                          <option value="${item.DM}" name="yypl" class="yypl`+index+`"${item.DM == 'qd'?'selected':''}>${item.DM}</option>
                        `)}
                      `}
                    </select>
                  </span>
                  <span style="padding-right:20px"> 用法:&nbsp;营养静脉滴注</span>
                  <span style="padding-right:20px">最小输液时间: 
                    <input class="allInput" name="zxsysj" type="number" oninput="validateNumber(this)" value="${this.data.yyYzJSJG.ZXSYSJ || ''}" style="width:48px" type="text"  ${!this.data.isNew && this.data.BTNZT!=1?`readonly`:``}/>&nbsp;&nbsp;小时
                  </span>
                </span>
                <br/>
                <span>
                  温馨提示:按六级要求必须走审方系统，营养医嘱【保存】 【发送至医嘱】后，请至药品医嘱界面提交医嘱。
                </span>
              </td>
            </tr>
          </td>
        <tr>
      </tbody>
    </table>
    `
  },
  getXGJYXM: function () {
    return `
    <table border="1" align="start" class="show_Table">
      <tbody>
        <tr>
          <td>
            <tr align="start">
              <td class="table-nr"> 总胆红素: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总胆红素')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总胆红素')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总胆红素')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总胆红素')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 直接胆红素: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('直接胆红素')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('直接胆红素')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('直接胆红素')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('直接胆红素')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 总蛋白: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总蛋白')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总蛋白')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总蛋白')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总蛋白')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 白蛋白: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总胆红素')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总胆红素')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('总胆红素')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('白蛋白')!=-1)[0].HYJG}</span>`:'':``}
              </td>
            </tr>
            <tr align="start" colspan="4">
              <td class="table-nr"> 前白蛋白: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('前白蛋白')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('前白蛋白')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('前白蛋白')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('前白蛋白')!=-1)[0].HYJG}</span>`:'':``}
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr"> 葡萄糖: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('葡萄糖')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('葡萄糖')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('葡萄糖')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('葡萄糖')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 尿素氮: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('尿素氮')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('尿素氮')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('尿素氮')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('尿素氮')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 肌酐: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('肌酐')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('肌酐')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('肌酐')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('肌酐')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 尿酸: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('尿酸')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('尿酸')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('尿酸')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('尿酸')!=-1)[0].HYJG}</span>`:'':``}
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr"> 甘油三脂: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('甘油三脂')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('甘油三脂')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('甘油三脂')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('甘油三脂')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 丙氨酸氨基转移酶: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('丙氨酸氨基转移酶')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('丙氨酸氨基转移酶')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('丙氨酸氨基转移酶')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('丙氨酸氨基转移酶')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 天冬酸氨酸氨基转移酶: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('天冬酸氨酸氨基转移酶')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('天冬酸氨酸氨基转移酶')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('天冬酸氨酸氨基转移酶')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('天冬酸氨酸氨基转移酶')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 碱性磷酸酶: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('碱性磷酸酶')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('碱性磷酸酶')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('碱性磷酸酶')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('碱性磷酸酶')!=-1)[0].HYJG}</span>`:'':``}
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr"> 血清钾: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钾')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钾')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钾')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钾')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 血清钠: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钠')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钠')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钠')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钠')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr">	血清氯: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清氯')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清氯')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清氯')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清氯')!=-1)[0].HYJG}</span>`:'':``}
              </td>
              <td class="table-nr"> 血清钙: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钙')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钙')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钙')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清钙')!=-1)[0].HYJG}</span>`:'':``}
              </td>
            </tr>
            <tr align="start" colspan="4">
              <td class="table-nr"> 血清磷: 
                ${this.data.yyYzXGJYTb.length>0?WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清磷')!=-1).length>0?`<span ${(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清磷')!=-1)[0].HYJG).indexOf('↑')!=-1 ||(WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清磷')!=-1)[0].HYJG).indexOf('↓')!=-1?style="color:red":''}>${WRT_config.noxgjcHyjg.filter(e=>e.HYMC.indexOf('血清磷')!=-1)[0].HYJG}</span>`:'':``}
              </td>
            </tr>
          </td>
        <tr>
      <tbody>
    </table>
    `
  }
})
