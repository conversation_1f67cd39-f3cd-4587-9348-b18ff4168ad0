
$(document).ready(() => {
  
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  let params = {}
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  WRT_config.url = params
})

// var btnTrue = 
function btnTrue() {
  const yyhz = $('input[name=fqhz0]:checked').val()
  if(WRT_config.url["as_openmodel"]=='wzfyy'){
    parent.setlcyyzxhz(yyhz);
    return
  }
  if (yyhz == '发起肠外营养会诊') {
    // console.log(WRT_config.url,88,yyhz);
    parent.page_iframe.add(yyhz, `${WRT_config.server}/zyblhzd/addhzd.aspx?as_blid=${WRT_config.url.as_blid}&as_zkid=${WRT_config.url.as_zkid}&as_hzbz=9`);
    parent.page_iframe.del('临床营养中心会诊')
  } else if (yyhz == '发起营养会诊') {
    // console.log(WRT_config.url,99,yyhz);
    parent.page_iframe.add(yyhz, `${WRT_config.server}/zyblhzd/hzdlist.aspx?as_hzdlb=1&as_blid=${WRT_config.url.as_blid}&as_zkid=${WRT_config.url.as_zkid}&as_mrzk=56`);
    parent.page_iframe.del('临床营养中心会诊')
  } else {
    WRT_e.ui.hint({msg:'请选择发起营养科会诊'})
  }
}