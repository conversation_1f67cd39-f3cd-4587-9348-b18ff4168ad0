/******************公共对象 ******************/
var
  interCount=null,//定时器
  ZJshow=true//总价是否显示
  maxTime=0,//秒表
  ls_yqdm = '', //院区代码
  qtlist = [], //其他医嘱目录
  grzklist = [], //专科及个人模板目录
  tylist = [], //各科通用模板目录
  yblx = [], //所有样本类型
  hyxmxxAll = [], //所有项目数据
  hyxmsFindAll = [], //获取所有目录查找数据
  hyfjbz = [], //需要填写备注的化验项目
  bqxzItem = '', //化验项目的el
  bqxzSave = '', //已选医嘱的el
  yxyz = [], //已选医嘱
  rgb = [], //关联号颜色
  mlmc = '', //目录名称
  params = {}, //url带参
  Hykz = [], //包含关系
  Zdbqxm = [], //指定病区，所有项目的包含关系
  bqxz_lists=[],//中间列表
  bqxzarr=[], //勾选队列
  bqxz_index=null, //勾选队列序号
  all_arr=[], //全选队列
  all_index=null, //全选队列序号
  kssj = new Date().Format("yyyy-MM-dd HH:mm:ss").slice(0, -3), //当前时间
  zxbqArr= [],
  lsSelectData = [],//暂存选中数组
  lsshownowData = [],//左侧数据
  saveBool = false,//左侧数据
  allChecked = false,//是否全选
  // lsNum = 0 // 当前选中数据
  // lsNumS = 0 // 上一轮进行选中数据
  // lsNumZ = 0 // 第几次进行选中
  checkNow = [], // 弹出框接口获取值（eg.生殖激素常规检查）
  hmzkUrl = WRT_config.server + '/zyblws/masonQC/wyyyMason.js',
  linShiTJ_array=[],//规则引擎提示数组
  tssm_array=[] //规则引擎特殊说明提示数组
  tssm_index = 0
/******************统一页面启动 ******************/
$(document).ready(() => {
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  qtlist = JSON.parse(sessionStorage.getItem("qtlist")) || []
  grzklist = JSON.parse(sessionStorage.getItem("grzklist")) || []
  tylist = JSON.parse(sessionStorage.getItem("tylist")) || []
  // zxbqArr.push(params["as_bqid"])
  
  if(params["as_switch"] == '1'){
    // addScript('http://**************/cdss/jssdk?v=4.0&ak=CCD9D75192ACC4529888523BCD5AB9E2')
    // addScript(hmzkUrl)
    setTimeout(() => {
      // 调用HMCdssInit
      HMCdssInit(params["as_blid"], params["al_yhid"])
    }, 2000);
  }
  //判断是否为预住院病人
  WRT_e.api.bqxz.checkYzyPat({
    params:{
      al_blid: params["as_blid"]
    },
    success(data){
      if(data.Code==1){
        $("#btn_song").removeClass("none")
      }
    }
  })
  //获取所有需要弹出申请单的化验项目
  WRT_e.api.bqxz.CheckHySqd({
    params:{},
    success(data){
      if(data.Code==1){
        WRT_config.CheckHySqd=JSON.parse(data.Result)
      }
    }
  })

  WRT_e.api.bqxz.getZxbq({
    params: {},
    success(data) {
      if (data.Code == 1) {
        $("#bqxz_radio").html(
          new zxbq_View().init({
            data: data.Result
          }).render().$el
        )
      }
    }
  })
  WRT_e.api.bqxz.GetHyyzYdr({
    params: {
      al_blid: params["as_blid"]
    },
    success(data) {
      if (data && data.Result) {
        let arr=JSON.parse(data.Result)
        WRT_config.HyyzYdr = arr.sort(function(a, b) {
          return b.DCSJ < a.DCSJ ? -1 : 1
        })
        let html = `<table>
          <tbody>
            <tr class="foot_trc">
              <td style="width: 200px; height: 19px">导出时间</td>
              <td style="width: 150px; height: 19px">导出人员</td>
              <td style="width: 350px; height: 19px">项目名称<span style="color: Red"> (5天内已导出医嘱)</span></td>
              <td style="width: 180px; height: 19px">样本类型</td>
              <td style="width: 150px; height: 19px" align="left">医嘱时间</td>
              <td style="width: 100px; height: 19px">医嘱医师</td>
            </tr>
            ${_.map(WRT_config.HyyzYdr, obj =>
          `<tr>
                <td style="width: 200px;">
                  <span id="rptDchyyz_ctl04_lblDcsj">${gettime(obj.DCSJ)}</span>
                </td>
                <td style="width: 150px; height: 19px">
                  <span id="rptDchyyz_ctl04_lblDcry">${obj.DCYHXM}</span></td>
                <td style="width: 350px;">
                  <span id="rptDchyyz_ctl04_lblMc">${obj.HYMC}</span>
                </td>
                <td>
                  <span id="rptDchyyz_ctl04_dlbYblx" value="C" style="display:inline-block;width:180px;">${obj.YBLX}</span>
                </td>
                <td style="width: 150px;" align="left">
                  <span id="rptDchyyz_ctl04_lblYzsj">${gettime(obj.YZSJ)}</span>
                </td>
                <td style="width: 100px;">
                  <span id="rptDchyyz_ctl04_lblYsxm">${obj.YZYHXM}</span>
                  <input type="hidden" name="rptDchyyz$ctl04$hfid" id="rptDchyyz_ctl04_hfid" value="36316">
                  <input type="hidden" name="rptDchyyz$ctl04$hfmlxh" id="rptDchyyz_ctl04_hfmlxh" value="26494">
                  <input type="hidden" name="rptDchyyz$ctl04$hfsjc" id="rptDchyyz_ctl04_hfsjc">
                </td>
              </tr>`
        ).join('')}
          <tbody>
        <table>`
        $("#contain_footer").html(html)
      }
    }
  })
  //读取院区代码，接下来需要传参过来
  WRT_e.api.bqxz.getYqdm({
    params: {
      al_blid: params["as_blid"]
    },
    success(data) {
      if (data.Code == 1) {
        //绑定其他医嘱目录
        ls_yqdm = data.Result
        if (qtlist.length > 0 &&  tylist.length > 0) {
          left_menu()
        } else {
          WRT_e.api.bqxz.getYl_HYyzmls({
            params: {
              as_yqdm: ls_yqdm
            },
            success(data) {
              if (data.Code == 1) {
                qtlist = data.Result
                sessionStorage.setItem("qtlist", JSON.stringify(qtlist))
                //设置树杈目录
                if (qtlist.length > 0 && WRT_config.grzklist && tylist.length > 0) {
                  left_menu()
                }
              }
            }
          })
        }
        //获取所有项目数据
        WRT_e.api.bqxz.getYl_hyxmxxAll({
          params: {
            as_yqdm: ls_yqdm
          },
          success(data) {
            hyxmxxAll = JSON.parse(data.Result)
          }
        })
      }
    }
  })
  if (qtlist.length > 0  && tylist.length > 0) {
    left_menu()
  } else {
    //zkid,yhid,’1’获取本专科及个人模板	
    WRT_e.api.bqxz.getYl_hymbs({
      params: {
        al_zkid: params["as_yszkid"],
        al_yhid: params["al_yhid"],
        as_lb: 1
      },
      success(data) {
        if (data.Code == 1) {
          WRT_config.grzklist=true
          grzklist = data.Result
          sessionStorage.setItem("grzklist", JSON.stringify(grzklist))
          //设置树杈目录
          if (qtlist.length > 0 && WRT_config.grzklist && tylist.length > 0) {
            left_menu()
          }
        }
      }
    })
    //数字0,yhid,’3’获取各科通用模板
    WRT_e.api.bqxz.getYl_hymbs({
      params: {
        al_zkid: 0,
        al_yhid: params["al_yhid"],
        as_lb: 3
      },
      success(data) {
        if (data.Code == 1) {
          tylist = data.Result
          sessionStorage.setItem("tylist", JSON.stringify(tylist))
          //设置树杈目录
          if (qtlist.length > 0 && WRT_config.grzklist && tylist.length > 0) {
            left_menu()
          }
        }
      }
    })
  }
  //获取所有样本类型
  WRT_e.api.bqxz.getYl_hyYblx({
    success(data) {
      if (data.Code == 1) {
        for (let obj of JSON.parse(data.Result)) {
          yblx[obj.SAMPLETYPE] = obj.SAMPLEDESCRIBE
        }
      }
    }
  })
  //获取所有需要填写备注的化验项目
  WRT_e.api.bqxz.getYl_hyfjbz({
    success(data) {
      if (data.Code == 1) {
        hyfjbz = JSON.parse(data.Result)
      }
    }
  })
  //化验项目之间，所有项目间的包含关系
  WRT_e.api.bqxz.getHykz({
    success(data) {
      if (data.Code == 1) {
        Hykz = JSON.parse(data.Result)
      }
    }
  })
  //指定病区，所有项目的包含关系
  WRT_e.api.bqxz.getZdbqxm({
    success(data) {
      if (data.Code == 1) {
        Zdbqxm = data.Result
      }
    }
  })
  //获取所有目录查找数据
  WRT_e.api.bqxz.getYl_hyxms({
    params: {
      al_mlid: 0
    },
    success(data) {
      if (data.Code == 1) {
        hyxmsFindAll = JSON.parse(data.Result)
      }
    }
  })
  //初始化
  right_menu()

})

/******************公共方法 ******************/
//一键清空
function allClear(){
  for(let i=0;i<yxyz.length;i++){
    if(yxyz[i].YZID){}else{
      yxyz.splice(i, 1)
      i--
    }
  }
}
// 引用js
function addScript(url){
	var script = document.createElement('script');
	script.setAttribute('type','text/javascript');
	script.setAttribute('src',url);
	document.getElementsByTagName('head')[0].appendChild(script);
	// document.write(`<script src='${url}'></script>`);
}
//设置树杈目录
function left_menu() {
  //目录
  let zNodes = [{
    id: -5,
    name: "个人模板",
    pId: 0,
  }, {
    id: -10,
    name: "专科模板",
    pId: 0,
  }]
  //专科个人目录
  let arrGr = []
  let arrZk = []
  //判断专科还是个人
  _.map(grzklist, obj => {
    if (obj.YSYHID == null) {
      arrZk.push({
        id: obj.ID,
        pId: -10,
        name: obj.MC,
      })
    } else {
      arrGr.push({
        id: obj.ID,
        pId: -5,
        name: obj.MC,
      })
    }
  })
  //在专科模板中添加各科通用模板
  arrZk = [...arrZk, ..._.map(tylist, e => ({
    id: e.ID,
    pId: -10,
    name: e.MC,
  }))]
  arrGr.length == 0 ? '' : zNodes[0].children = arrGr
  arrZk.length == 0 ? '' : zNodes[1].children = arrZk
  //其他目录
  let arrAY = []
  arrAY = trans_tree(qtlist)
  zNodes = [...zNodes, ...arrAY]
  let html = `
  <ul>
    ${_.map(zNodes, (obj, index) => `
      <li>
        <div class="firstli" onclick="treechange(this,${index},${obj.id},${obj.pId},'${obj.name}',${obj.children ? "true" : "false"})">${obj.name}${obj.children ? `<i class="glyphicon glyphicon-menu-down"></i>` : ""}</div>
        <ul class="boxul" style="display:none;padding-left: 5px;">${treeDataHtml(obj.children)}</ul>
      </li>`
  ).join('')}
  </ul>`
  $("#treeDemo").html(html)
  // var setting = {
  //   data: {
  //     key: {
  //       name: "name"
  //     },
  //     simpleData: {
  //       enable: true, //如果设置为 true，请务必设置 setting.data.simpleData 内的其他参数: idKey / pIdKey / rootPId，并且让数据满足父子关系。
  //       idKey: "id",
  //       pIdKey: "pId",
  //       rootPId: ''
  //     }
  //   },
  //   view: {
  //     showIcon: false,
  //     dblClickExpand: false
  //   },
  //   callback: {
  //     onClick: zTreeOnClick
  //   }
  // };
  // $.fn.zTree.init($("#treeDemo"), setting, zNodes);
}
//绘制树结构
function treeDataHtml(target) {
  if (!target || (target && target.length == 0)) {
    return ''
  }
  let temp = `
  
    ${_.map(target, (obj, key) =>
    `<li>
        <div class="boxli" onclick="treetwochange(this,${key},${obj.id},${obj.pId},'${obj.name}',${obj.children ? "true" : "false"})">${obj.children ? `<i class="iconfont icon-xiala" style="font-size: 10px;"></i>` : `<span style="padding-left:12px"></span>`}${obj.name}</div>
        <ul class="boxul" style="display:none;padding-left: 5px;">${treeDataHtml(obj.children)}</ul>
      </li>
        `
  ).join("")}
  
  `
  return temp
}
//树点击
function treechange(ev, index, id, pId, name, type) {
  let el = ev.className.split(" ")[0]
  if (!type) {
    zNodeOnClick({ id: id, pId: pId, name: name })
  }
  let div = $(`.${el}`).parent()
  for (let i = 0; i < div.length; i++) {
    let mc = ""
    if (i != index) {
      let td = div[i].children[0].className
      if (td.indexOf("firstactive") >= 0) {
        let tf = td.split("firstactive")
        mc = tf[0] + tf[1]
        div[i].children[0].className = mc
        if (div[i].children[0].children[0] != undefined) {
          div[i].children[0].children[0].className = "glyphicon glyphicon-menu-down"
        }
      }
    }
    div[i].children[1].style.display = 'none'
  }
  let td = div[index].children[0].className
  if (td.indexOf("firstactive") >= 0) {
    let tf = td.split("firstactive")
    div[index].children[0].className = tf[0] + tf[1]
    if (div[index].children[0].children[0] != undefined) {
      div[index].children[1].style.display = 'none'
      div[index].children[0].children[0].className = "glyphicon glyphicon-menu-down"
    }
  } else {
    div[index].children[0].className += " firstactive"
    if (div[index].children[0].children[0] != undefined) {
      div[index].children[0].children[0].className = "glyphicon glyphicon-menu-up"
      div[index].children[1].style.display = 'block'
    }
  }
}
//二级点击事件
function treetwochange(ev, index, id, pId, name, type) {
  let el = ev.className.split(" ")[0]
  if (!type) {
    zNodeOnClick({ id: id, pId: pId, name: name })
  }
  let div = ev.parentElement.parentNode.children
  for (let i = 0; i < div.length; i++) {
    let mc = ""
    let td = div[i].children[0].className
    if (td.indexOf("boxactive") >= 0) {
      let tf = td.split("boxactive")
      mc = tf[0] + tf[1]
      div[i].children[0].className = mc
      if (div[i].children[0].children[0].localName != 'span') {
        div[i].children[0].children[0].className = "iconfont icon-xiala"
      }
    }

    div[i].children[1].style.display = 'none'
  }
  if (ev.className.indexOf("boxactive") >= 0) {
    let di = ev.nextElementSibling
    let tf = ev.className.split("boxactive")
    let text = tf[0] + tf[1]
    ev.className = text
    if (ev.children[0].className != "") {
      ev.children[0].className = "iconfont icon-xiala"
      di.style.display = 'none'
    }
    return
  }
  if (ev.className.indexOf("boxli") >= 0) {
    let di = ev.nextElementSibling
    ev.className += " boxactive"
    if (ev.children[0].className != "") {
      ev.children[0].className = "iconfont icon-xiangshang"
      di.style.display = 'block'
    }
    return
  }
}
//点击事件
function zNodeOnClick(obj) {
  if (obj.id == -5 || obj.pId == -5 || obj.pId == -10 || obj.id == -10) {
    WRT_e.api.bqxz.getYl_hymbsDT({
      params: {
        as_yqdm: ls_yqdm,
        al_mlid: obj.id
      },
      success(data) {
        if (data.Code == 1) {
          //设置化验目录
          mlmc = obj.name
          getXmxx(JSON.parse(data.Result))
        }
      }
    })
  } else {
    WRT_e.api.bqxz.getYl_hyxms({
      params: {
        al_mlid: obj.id
      },
      success(data) {
        if (data.Code == 1) {
          //设置化验目录
          mlmc = obj.name
          getXmxx(JSON.parse(data.Result), obj)
        }
      }
    })
  }

}
//日期年月日
function gettime(val, type) {
  var d
  if (!val) {
    d = new Date();
  } else {
    d = new Date(val)
  }
  // var d = new Date(val);
  var year = d.getFullYear();
  var month = change(d.getMonth() + 1);
  var day = change(d.getDate());
  var hour = change(d.getHours());
  var minute = change(d.getMinutes());
  var second = change(d.getSeconds());
  function change(t) {
    if (t < 10) {
      return "0" + t;
    } else {
      return t;
    }
  }
  var time = ""
  if (!type) {
    time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
  } else {
    time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute
  }
  return time
}

//设置已选医嘱
function right_menu(arr) {
  WRT_e.api.bqxz.getInit({
    params: {
      al_blid: params["as_blid"]
    },
    success(data) {
      if (data.Code == 1) {
        //已选医嘱视图
        let arr = data.Result.map(e => {
          let obj = {}
          for (let idx in e) {
            switch (idx) {
              case "KSSJ":
                obj[idx] = e[idx] ? ChangeDateFormat(e[idx]).slice(0, -3) : '0000-00-00 00:00'
                break
              case "SCLRSJ":
                obj[idx] = ChangeDateFormat(e[idx]).split(' ')[0]
                break
              case "YZSJ":
                obj[idx] = ChangeDateFormat(e[idx]).split(' ')[0]
                break
              case "YZBZ":
                obj[idx] = (e[idx] || '')
                break;
              default:
                obj[idx] = (e[idx] || 0)
                break
            }
            let fd=yxyz.filter(item=>e.HYID==item.HYID)
            if(fd&&fd.length>0){
              obj.DJ=fd[0].DJ||""
            }
            //加上社保标识
            // obj['SBSP'] = 0
          }
          return obj
        })
        // 监听已选遗数组(当前注释代码)
        yxyz = deepProxy(arr, (type, data) => {
          // 当前选中数组长度 bqxzarr.length；实际选中数据长度 arr.length
          //渲染已选医嘱
          if (bqxzSave) bqxzSave.render()
          //渲染化验项目
          if (bqxzItem) bqxzItem.render()
          // lsNumS = bqxzarr.length
          // if(yxyz.length==0){
          //   $(".btn_save1").addClass("none");
          //   $(".btn_save").removeClass("none");
          // }
          return arr;
        });
        
        //初始化
        bqxzSave = new bqxzSave_View().init({
          data: yxyz
        })
        $("#bqxz_save").html(bqxzSave.render().$el)
      }
    },
    error(msg){
      let data=JSON.parse(msg)
      if(data.Message){
        WRT_e.ui.hint({ msg:data.Message, type:'error' })
      }
    }
  })

}
//json数据转化为树状结构
function trans_tree(target) {
  let resData = JSON.parse(JSON.stringify(target));
  let tree = [];
  for (let i = 0; i < resData.length; i++) {
    if (resData[i].FMLID === 9) {
      resData[i].children = []
      let obj = {
        id: resData[i].YZMLID,
        pId: 0,
        name: resData[i].MC,
        // children:[]
      }
      tree.push(obj);
      resData.splice(i, 1);
      i--;
    }
  }
  run(tree);

  function run(chiArr) {
    if (resData.length !== 0) {
      for (let i = 0; i < chiArr.length; i++) {
        for (let j = 0; j < resData.length; j++) {
          if (chiArr[i].id === resData[j].FMLID) {
            let obj = {
              id: resData[j].YZMLID,
              pId: chiArr[i].id,
              name: resData[j].MC,
            }
            if (chiArr[i].children) {
              chiArr[i].children.push(obj);
            } else {
              chiArr[i].children = [obj];
            }
            resData.splice(j, 1);
            j--;
          }
        }
        run(chiArr[i].children || []);
      }
    }
  }
  return tree;
}
//树单机事件
function zTreeOnClick(event, id, obj, index) {
  //判断是否为叶目录
  if (!(obj.parentTId == null && obj.isParent == true)) {
    if (obj.id == -5 || obj.pId == -5 || obj.pId == -10 || obj.id == -10) {
      WRT_e.api.bqxz.getYl_hymbsDT({
        params: {
          as_yqdm: ls_yqdm,
          al_mlid: obj.id
        },
        success(data) {
          if (data.Code == 1) {
            //设置化验目录
            mlmc = obj.name
            getXmxx(JSON.parse(data.Result))
          }
        }
      })
    } else {
      WRT_e.api.bqxz.getYl_hyxms({
        params: {
          al_mlid: obj.id
        },
        success(data) {
          if (data.Code == 1) {
            //设置化验目录
            mlmc = obj.name
            getXmxx(JSON.parse(data.Result), obj)
          }
        }
      })
    }
  } else {
    var zTree = $.fn.zTree.getZTreeObj("treeDemo");
    zTree.expandNode(obj);
  }
}
//获取项目数据
function getXmxx(arr, hyObj) {
  let newArr = JSON.parse(JSON.stringify(arr))

  //获取具体项目信息
  for (let [idx, item] of arr.entries()) {
    obj = hyxmxxAll.find(e => e.ID == item.HYID)
    //==0不是套餐
    if (item.SFTC == 0) {
      let kzjbtype=''
      if(obj.KZJB&&obj.KZJB!='null'){
        if(['自', '甲', '乙'][parseInt(obj.KZJB)]){
          kzjbtype = ['自', '甲', '乙'][parseInt(obj.KZJB)]
        }
      }
      newArr[idx] = {
        ...newArr[idx],
        ...{
          //消除null
          KZJB: kzjbtype, //控制级别
          DJ: obj.DJ, //单价
          SFSXD: obj.SFSXD, //输血单
          SFJZ: obj.SFJZ, //是否急诊
          YBLX: yblx[item.YBLX], //样本类型
          SFSL:obj.SFSL//数量
        }
      }
      //==1是套餐
    } else {
      WRT_e.api.bqxz.getYl_hyxmxx({
        params: {
          as_yqdm: ls_yqdm,
          as_sftc: item.SFTC,
          al_hyid: item.HYID,
        },
        success(data) {
          newArr[idx] = {
            ...newArr[idx],
            ...((item) => {
              let bz = `【${newArr[idx].MC}】 包括\n`
              let dj = 0
              _.each(JSON.parse(data.Result), itemChildren => (dj += parseInt(itemChildren.DJ)*itemChildren.SFSL, bz += `${itemChildren.MC} ￥${itemChildren.DJ}\n`))
              return {
                BZ: bz,
                DJ: dj,
                SFSXD: JSON.parse(data.Result)[0].SFSXD, //输血单
                SFJZ: JSON.parse(data.Result)[0].SFJZ, //是否急诊
              }
            })() //备注
          }
          if (newArr.length == arr.length) {
            //初始化
            lsshownowData = [...newArr]
            bqxzItem = new bqxzItem_View().init({
              data: newArr
            })
            $("#bqxz_item").html(bqxzItem.render().$el)
            //添加颜色
            if ($("input[name=searchBqxz]").val().trim().toUpperCase()) {
              $(`#bqxz_item table tr[data-hyid=${hyObj.hyid}]`).css('background-color', 'SkyBlue')
              $('#table_lists').animate({scrollTop: $(`#bqxz_item table tr[data-hyid=${hyObj.hyid}]`).offset().top-100},500);
            }
          }
        }
      })
    }
  }

  if (newArr.length == arr.length) {
    //初始化
    lsshownowData = [...newArr]
    bqxzItem = new bqxzItem_View().init({
      data: newArr
    })
    $("#bqxz_item").html(bqxzItem.render().$el)
    //添加颜色
    if ($("input[name=searchBqxz]").val().trim().toUpperCase()) {
      $(`#bqxz_item table tr[data-hyid=${hyObj.hyid}]`).css('background-color', 'SkyBlue')
      // $('#table_lists').animate({scrollTop: $(`#bqxz_item table tr[data-hyid=${hyObj.hyid}]`).offset().top},500);
    }
  }
}
//保存
// 去重
function uniqueByField(array, field) {
  return array.reduce((acc, current) => {
    if (!acc.some(item => item[field] === current[field])) {
      acc.push(current);
    }
    return acc;
  }, []);
}

//判断CDSS
function getCDSS(){
  if(params["as_brzt"]&&params["as_brzt"]==1){
    WRT_e.ui.hint({msg:'病人已护士病区出院，不能开医嘱！'})
    return
  }
  let myvalue = $('input:radio[name="rbl_sss"]:checked').val();
  // let saveBtn=  document.getElementsByClassName('btn_save')[0].disabled
  let saveBtn =  document.getElementsByClassName('btn_save')[0].getAttribute('readonly');
  // 1、如果全选判断左侧是否全部勾选，只要全部勾选就允许保存
  let form = $('.bqxz_table_jtnr')
  let yzmlmx = []
  form.find('[name="checkedBox"]:checked').each(function () {
    // console.log($(this)[0].parentElement.parentElement.dataset.hyid);
    yzmlmx.push({
      ...lsshownowData.find(e => e.HYID == $(this)[0].parentElement.parentElement.dataset.hyid)
    })
  })
  
  // console.log(yxyz,saveBool,'保存', saveBtn == 'readonly',yzmlmx)
  if (allChecked) { // 全选
    // yzmlmx
    // console.log(yxyz,saveBool,'保存',lsSelectData, saveBtn == 'readonly',lsshownowData,333,yzmlmx)
    if (yzmlmx.length!=0) {
      saveBool = true
    }
    //  else {
    //   // WRT_e.ui.hint({msg: '左侧勾选项目'})
    //   // WRT_e.ui.message({
    //   //   title: '提示信息',
    //   //   content: '左侧存在未勾选项目，是否继续保存',
    //   //   onOk() {
    //   //     saveBool = true
    //   //   },
    //   //   onCancel() { 
    //   //     saveBool = false
    //   //   }
    //   // })
    // }
  } else{
    saveBool = true
  }
  if(bqxzarr.length>0&&bqxz_index&&bqxz_index<=bqxzarr.length){
    WRT_e.ui.hint({msg:'请等待项目加载完成后点击保存！'})
    return
  }
  if (saveBool) {
    $(".btn_save").removeAttr("readonly");
    saveBtn =  document.getElementsByClassName('btn_save')[0].getAttribute('readonly');
    // WRT_e.ui.hint({type:'success',msg:'已成功点击保存按钮'})
    WRT_e.api.au.getCdssSwitch({
      params:{al_zkid:params["as_yszkid"]},
      success(data){
        if(data.Code==0){
          saveBefor()
        }else if(data.Code==1){
          GetHyxx()
          // saveBefor()
        }
      }
    })
  } else {
    if (yxyz && yxyz.length==0) {
      WRT_e.ui.hint({msg:'请勾选化验项目！'})
    }
  }
  // if (saveBtn == 'readonly' && yxyz.length != bqxzarr.length && bqxzarr.length!=0 && (bqxzarr.length<bqxz_index&&bqxzarr.length>0)) {
  // if (saveBtn == 'readonly' && (lsSelectData && lsSelectData.length==0)) {
  //   if (yxyz && yxyz.length==0) {
  //     WRT_e.ui.hint({msg:'请勾选化验项目！'})
  //   } else {
  //     WRT_e.ui.hint({msg:'请等待项目加载完成后点击保存！'})
  //   }
  //   return
  // } else {
  //   $(".btn_save").removeAttr("readonly");
  //   saveBtn =  document.getElementsByClassName('btn_save')[0].getAttribute('readonly');
  //   // WRT_e.ui.hint({type:'success',msg:'已成功点击保存按钮'})
  //   WRT_e.api.au.getCdssSwitch({
  //     params:{al_zkid:params["as_yszkid"]},
  //     success(data){
  //       // console.log('保存判断CDSS',data);
  //       if(data.Code==0){
  //         // saveHyyz()
  //         saveBefor()
  //       }else if(data.Code==1){
  //         GetHyxx()
  //         // saveBefor()
  //       }
  //     }
  //   })
  // }
}
//cdss校验   data.Code==1
function GetHyxx(){
  let hyid=[]
  yxyz.map(function(item){
    hyid.push(item.HYID)
  })
  let url=`method=GetHyxx&hyid=${hyid.join('^')}&blid=${params["as_blid"]}`
  WRT_e.api.au.getHyxx({
    params:url,//"method=GetHyxx&hyid=32809^35552&blid=2220305",
    success(data){
      // data='{"hasError":8,"errorMessage":"请求成功","data":{"ruleTopics":[{"ruleTopicName":"检验医嘱开立","rules":[{"ruleName":"男性禁止开立女性检验项目(宫颈、阴道)","ruleResult":"患者为男性，禁止开立女性检验","ruleType":"]Z"}]}],"showFlag":"Y","statistic":{"time":"16","total":"1"},"xx1x":"JZ"}}'
      // if(data&&data.length>0){
      //   saveBefor()
      //   return;
      // }
      try {
        let res=JSON.parse(data)
        if(res.data&&res.data.ruleTopics){
          maxTime=1000*60*10
          let arr=res.data.ruleTopics||[]
          let html=`
          <ul>
            ${_.map(arr,(item)=>
            `
            <li style="padding: 3px 0;">
              <div style="background: #fff;width: 250px;">${item.ruleTopicName}</div>
                <ul style="margin-left: 5px;">
                ${_.map(item.rules,(key)=>
                  `<li style="background: #fff;margin-top: 2px;">
                    <div style="height: 25px;color: red;padding-right: 5px;float: left;">${res.data.xxlx=='JZ'?"禁止":res.data.xxlx=='JG'?"警告":res.data.xxlx=='TS'?"提示":""}</div>
                    <span>${key.ruleName}</span>
                  </li>
                  <li style="background: #fff;margin-top: 2px;">
                    <span>详情：</span><span>${key.ruleResult}</span>
                  </li>`
                )}
                </ul>
            </li>
            `
            )}
          </ul> 
          `
          $("#box_lists").html(html)
          $(".box_foot").removeClass("none");
          if(res.data.xxlx=='JZ'){
            WRT_e.ui.message({
              title:'信息窗口',
              content:'保存失败，有禁止开立的项目，请删除禁止项再保存！',
              onOk(){}
            })
          }else if(res.data.xxlx=='JG'||res.data.xxlx=='TS'){
            // saveHyyz()
            saveBefor()
            return;
          }
        }else{
          // saveHyyz()
          saveBefor()
          return;
        }
        $(".btn_save").addClass("none");
        $(".btn_save1").removeClass("none");
      } catch (e) {
        // 转换出错，抛出异常
        WRT_e.ui.hint({type:'error',msg:data[0].errorMsg})
      }
    },
    error(msg){
      if(msg){
        saveBefor()
      }
    }
  })
}
// 判断校验医嘱病区（保存前调用）  data.Code==0
function saveBefor() {
  let myvalue = $('input:radio[name="rbl_sss"]:checked').val();
  zxbqArr=[]
  zxbqArr.push(myvalue)
  WRT_e.api.bqxz.checkZxbq({
    params:{
      ast_zxbq: zxbqArr,//医嘱的执行病区，无需去重,
      al_blid: params["as_blid"]
    },
    success(data) {
      if(data.Code == '1'){
        let bamc = JSON.parse(data.Result)
        if(bamc.zxbqmc!='') {
          WRT_e.ui.message({
            title:'提示',
            content:"医嘱开到的病区" + bamc.zxbqmc +"与病人当前病区" + bamc.xbqmc +"不一致，是否继续保存?",
            onOk(){
              saveBeforgzyq()
              //saveHyyz()
            },
            onCancel(){

            }
          })
        } else {
          saveBeforgzyq()
          //saveHyyz()
        }
      }
    }
  })
}
//规则引擎
function saveBeforgzyq(){
  //规则引擎
  let arr_zgyq=[]
  tssm_index = 0
  yxyz.map(function(item){
    if(item.SFXMID){
      let list={
        shouFeiXMID:item.SFXMID,
        xiangMuMC:item.HYMC,
        shuLiang:item.SFSL,
        xiangMuLX:'zl'
      }
      arr_zgyq.push(list)
    }
  })
  tssm_array=[]
  WRT_e.api.au.getApprovalProjectByRule({
    params:{
        "biaoShiHao": params["as_blid"],
        "bingAnHao": params["empi"],
        // "biaoShiHao": "2817729",
        // "bingAnHao": "0014607518",
        "shiFouMZ": false,
        "shiFouTJ": false,
        "shouFeiXMList": arr_zgyq,
        "yongHuID": params["al_yhid"],
        "changJingDM": "cwsf_zyyz"
    },
    success(msg){
      if(msg.data&&msg.data.linShiTJ){
        if(msg.data.linShiTJ.length>0){
          linShiTJ_array=msg.data.linShiTJ
          let fd=msg.data.linShiTJ.filter(item=>item.tiaoJianLB=='7')
          if(fd&&fd.length>0){
            let name=arr_zgyq[fd[0].chaRuSX-1].xiangMuMC
            WRT_e.ui.message({
              title: '提示信息',
              content:`【${name}】${fd[0].xunWenNR}`,
              onOk(){
              }
            })
            return
          }
          let text=''
          let LBtype=false
          msg.data.linShiTJ.map(function(item){
            let name=arr_zgyq[item.chaRuSX-1].xiangMuMC
            if(item.tiaoJianLB=='6'){
              if(item.xunWenNR){
                LBtype=true
                text+=`【${name}】${item.xunWenNR},是否自费`+`</br></br>`
              }
            }else if(item.tiaoJianLB=='3'){
              if(item.xunWenNR){
                text+=`【${name}】${item.xunWenNR}`+`</br></br>`
              }
            }else if(item.tiaoJianLB=='4'){
              if(item.xunWenNR){
                text+=`【${name}】${item.xunWenNR},修改为自费`+`</br></br>`
              }
            }else if(item.tiaoJianLB=='10'){
              tssm_array.push(item)
            }
          })
          if(text){
            if(LBtype){
              WRT_e.ui.message({
                title: '提示信息',
                content:text,
                onOk(){
                  checktype()
                },
                onCancel(){
                  // SavecheckTssm()
                }
              })
            }else{
              WRT_e.ui.message({
                title: '提示信息',
                content:text,
                onOk(){
                  saveHyyz()
                }
              })
            }
          }else{
            SavecheckTssm()
          }

        }else{
          saveHyyz()
        }
      }else{
        // WRT_e.ui.hint({msg:'规则引擎连接失败，请联系信息科！'})
        saveHyyz()
      }
    },
    error(msg){
      saveHyyz()
    }
  })
}
//消息提醒
function checktype(){
	linShiTJ_array.forEach((item)=>{
		if(item.tiaoJianLB=='6'||item.tiaoJianLB=='3'||item.tiaoJianLB=='4'||item.tiaoJianLB=='10'){
      try{
        if(yxyz.length>0){
          yxyz[item.chaRuSX-1].SBSP=1
        }
      }catch{
        SavecheckTssm()
      }
		}
	})
	SavecheckTssm()
}
//规则引擎特殊说明
function SavecheckTssm(){
	if(tssm_array.length>0&&tssm_index<tssm_array.length){
		tssmhtml(tssm_array[tssm_index])
	}else{
    saveHyyz()
  }
}
//特殊说明提醒
function tssmhtml(obj){
	let temp=`
	<div>${obj.xunWenNR}</div>
	<div><input id="tssm_input" class="none" type="text" autocomplete="off" placeholder="特殊说明输入"></div>
	<div>
		<button class="e_btn" onclick="tssm_click()">特殊说明</button>
		<button class="e_btn" onclick="check_click(${obj.chaRuSX})">确认</button>
		<button class="e_btn" onclick="$('#tssmhtml').iziModal('destroy')">取消</button>
	</div>
	`
	WRT_e.ui.model({
		id: 'tssmhtml',
		title: '信息提示',
		width: "650px",
		content: temp,
		iframe: false,
	})
}
//特殊说明点击
function tssm_click(){
	$("#tssm_input").removeClass("none")
}
//特殊说明确定
function check_click(index){
	let tssm=$("#tssm_input")[0].value
	if(!tssm){
		if(yxyz[index-1]){
			yxyz[index-1].SBSP=1
		}else if(yxyz[index]){
			yxyz[index].SBSP=1
		}
    $('#tssmhtml').iziModal('destroy')
		tssm_index++
		if(tssm_array.length>0&&tssm_index<tssm_array.length){
			tssmhtml(tssm_array[tssm_index])
		}else{
			saveHyyz()
		}
	}else{
		WRT_e.api.au.saveShuoMing({
			params:{
				"biaoShi": params["as_blid"], //诊疗活动ID/病例ID
				"bingAnHao": params["empi"], //病案号
				"changJingDM": "cwsf_zyyz", //场景代码
				"yingYongDM": "020", //应用代码
				"yongHuID": params["al_yhid"], //用户ID
				"teShuSM": tssm, //特殊说明
				"jiLuSJ": gettime(), //记录时间
				"shiFouMZ": "2" //是否门诊，1门诊，2住院
			},
			success(msg){
				if(msg.hasError=='0'){
					WRT_e.ui.hint({type:'success',msg:'记录成功！'})
				}
				$('#tssmhtml').iziModal('destroy')
				tssm_index++
				if(tssm_array.length>0&&tssm_index<tssm_array.length){
					tssmhtml(tssm_array[tssm_index])
				}else{
					saveHyyz()
				}
			}
		})
	}
	
}

//保存已选择数据
function saveHyyz() {
  //获取患者信息
  let myvalue = $('input:radio[name="rbl_sss"]:checked').val();
  WRT_e.api.bqxz.getHzxx({
    params: {
      al_blid: params["as_blid"]
    },
    success(data) {
      if (data.Code == 1) {
        //保存数据
        WRT_e.api.bqxz.saveHyyz({
          params: {
            val: JSON.stringify([...[{
              blid: data.Result.BLID,
              bqid: myvalue||params["as_bqid"],
              zlzid: 0
            }], ...yxyz.map(e => ({
              yzid: e.YZID, //医嘱ID
              mbid: e.MBID, //模板ID
              sftc: e.SFTC, //是否套餐
              mlid: e.MLID, //目录ID
              mlxh: e.MLXH, //目录序号
              hyxmid: e.HYID, //化验项目ID
              yzbz: e.YZBZ, //医嘱备注
              sbsp: e.SBSP, //社保审批标志
              kssj: e.KSSJ, //开始时间
              ztbz: e.ZTBZ //状态标志
            }))]),
            yqdm: ls_yqdm
          },
          success(data) {
            // console.log('返回数据决定提示框显示',data);
            interCount=null;
            switch (data) {
              case "0":
                WRT_e.ui.hint({
                  type: 'error',
                  msg: '保存失败'
                })
                break;
              case "1":
                WRT_e.ui.hint({
                  type: 'success',
                  msg: '保存成功'
                })
                ZJshow=false
                maxTime=1000*60*10
                interCount=setInterval(function(){
                  maxTime--
                  if(maxTime==0){
                    $(".box_foot").addClass("none");
                  }
                },1000)
                // $(".box_foot").addClass("none");
                right_menu()
                break;
              case "2":
                WRT_e.ui.hint({
                  msg: '项目停用'
                })
                break;
              case "9":
                WRT_e.ui.hint({
                  msg: '已病区出院不允许操作'
                })
                break;
              default:
                // （周一筛查）
                WRT_e.ui.hint({
                  type: 'success',
                  msg: '保存成功!'+data
                })
                maxTime=1000*60*10
                interCount=setInterval(function(){
                  maxTime--
                  if(maxTime==0){
                    $(".box_foot").addClass("none");
                  }
                },1000)
                right_menu()
            }
            // 给惠每质控加判断WRT_config.hmzkZW专网不走
            if (WRT_config.hmzkZW==undefined || !WRT_config.hmzkZW) {
              // params["as_switch"](hfd_qc_switch)为1的时候，引用http://**************/cdss/jssdk?v=4.0&ak=CCD9D75192ACC4529888523BCD5AB9E2，保存医嘱的时候调用GetMasonString(j_blid, j_yhid) 
              var promise = new Promise((resolve, reject) => {
                if(params["as_switch"] == '1'){
                  resolve(params["as_switch"])
                } else {
                  reject(params["as_switch"])
                }
                //可以传参,如：resolve(1111)，那么.then的response可以接受这个参数。
                //reject(2222)，那么.catch的error可以接受这个参数。
              })
              promise.then(res => { //调用resolve()时执行
                if(res=="1"){
                  // GetMasonString(j_blid, j_yhid)
                  GetMasonString(params["as_blid"], params["al_yhid"]);
                }
              }).catch(error =>{         
                WRT_e.ui.hint({msg:'惠每质控调用失败，请联系信息处',type:'error'})
              })
            }
          }
        })
      }
    }
  })
}

//发送短信
function song(){
  WRT_e.ui.message({
    title:'提示',
    content:'是否确定要给预住院病人发送短信通知？',
    onOk(){
      WRT_e.api.bqxz.sendMessage({
        params:{
          Blid:params["as_blid"]
        },
        success(data){
          if(data.Code==1){
            WRT_e.ui.hint({msg:'发送成功',type:'success'})
          }
        }
      })
    },
    onCancel(){}
  })
}

//查找回车事件
function calAge() {
  var evt = window.event || e;
  if (evt.keyCode == 13) {
    searchBqxz()
  }
}
//查找
function searchBqxz() {
  let searchText = $("input[name=searchBqxz]").val().trim().toUpperCase()
  let list = []
  for (let item of hyxmsFindAll) {
    if (item.PY.indexOf(searchText) > -1||item.MC.indexOf(searchText) > -1) { //首写拼音与中文
      let obj = qtlist.find(e => e.YZMLID == item.MLID)
      if (obj) { //当前目录是否存在
        list.push({
          ...item,
          ...{
            MLMC: obj.MC
          }
        })
      }
    }
  }
  if (searchText && list.length > 0) {
    if (this.oldSearchText == searchText && this.hyxmsFindAllList.length - 1 > this.searchIndex) { //点击是否相同
      this.searchIndex++
    } else {
      this.oldSearchText = searchText
      this.searchIndex = 0 //索引
      this.hyxmsFindAllList = list
    }
    zTreeOnClick(undefined, undefined, {
      parentTId: undefined,
      isParent: undefined,
      pId: undefined,
      id: this.hyxmsFindAllList[this.searchIndex].MLID,
      name: this.hyxmsFindAllList[this.searchIndex].MLMC,
      hyid: this.hyxmsFindAllList[this.searchIndex].HYID,
    }, undefined)
  }
}

//日期格式化
function ChangeDateFormat(jsondate) {
  jsondate = jsondate.replace("/Date(", "").replace(")/", "");
  if (jsondate.indexOf("+") > 0) {
    jsondate = jsondate.substring(0, jsondate.indexOf("+"));
  } else if (jsondate.indexOf("-") > 0) {
    jsondate = jsondate.substring(0, jsondate.indexOf("-"));
  }
  var date = new Date(parseInt(jsondate, 10));
  var month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
  var currentDate = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  var hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  var minutes = date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  var second = date.getMilliseconds() / 1000 < 10 ? "0" + parseInt(date.getMilliseconds() / 1000) : parseInt(date.getMilliseconds() / 1000);
  return date.getFullYear() + "-" + month + "-" + currentDate + " " + hours + ":" + minutes + ":" + second;
}
//日期删除和添加
function istime(str) {
  var a = str.match(/^(\d{0,2}):(\d{0,2}):(\d{0,2})$/);
  if (a == null) return false;
  if (a[1] >= 24 || a[2] >= 60 || a[3] >= 60) return false;
  return true;
}

function isdatetime(str) {
  //var a = str.match(/^(\d{0,4})-(\d{0,2})-(\d{0,2}) (\d{0,2}):(\d{0,2}):(\d{0,2})$/); 
  var a = str.match(/^(\d{0,4})-(\d{0,2})-(\d{0,2}) (\d{0,2}):(\d{0,2})$/);
  if (a == null) return false;
  //if ( a[2]>=13 || a[3]>=32 || a[4]>=24 || a[5]>=60 || a[6]>=60) return false; 
  if (a[2] >= 13 || a[3] >= 32 || a[4] >= 24 || a[5] >= 60) return false;
  return true;
}

function isdate(str) {
  var a = str.match(/^(\d{0,4})-(\d{0,2})-(\d{0,2})$/);
  if (a == null) return false;
  if (a[2] >= 13 || a[3] >= 32 || a[4] >= 24) return false;
  return true;
}

//计划开始时间修改
function changekssj(e,key){
  if(e.attributes[2]){
    let index=e.attributes[2].value
    let list=yxyz[index]
    if(list){
      yxyz.forEach(function(item){
        if(item.MLXH==list.MLXH&&item.KSSJ!=e.value){
          item.KSSJ=e.value
        }
      })
     
    }
  }
  
}

function validate(obj, type) {
  var range;
  var text;

  if (obj.selectionStart == undefined || obj.selectionStart == null) {
    range = obj.createTextRange();
    text = range.text;
    var selrange = document.selection.createRange();
    var seltext = selrange.text;
    var startpos = 0,
      endpos = 0;

    while (selrange.compareEndPoints("StartToStart", range) > 0) {
      selrange.moveStart("character", -1);
      startpos++;
    }
    while (selrange.compareEndPoints("EndToStart", range) > 0) {
      selrange.moveEnd("character", -1);
      endpos++;
    }
  } else {
    startpos = obj.selectionStart;
    endpos = obj.selectionEnd;
    if (endpos != startpos) {
      text = obj.value.substring(startpos, endpos);
    } else {
      text = obj.value;
    }
    // range = obj.setSelectionRange(startpos, obj.value.length);

  }

  if (event.keyCode >= 48) {
    var keytext;
    if (event.keyCode >= 96 && event.keyCode <= 105)
      keytext = event.keyCode - 96;
    else
      keytext = String.fromCharCode(event.keyCode);
    text = text.substring(0, startpos) + keytext + text.substring(endpos + 1, text.length);
  } else if (event.keyCode == 46) {
    if (startpos == endpos)
      text = text.substring(0, startpos) + "0" + text.substring(startpos + 1, text.length);
    else
      text = text.substring(0, startpos) + "0" + text.substring(endpos, text.length);
  } else if (event.keyCode == 8) {
    if (startpos == endpos)
      text = text.substring(0, startpos - 1) + "0" + text.substring(startpos, text.length);
    else
      text = text.substring(0, startpos) + "0" + text.substring(endpos, text.length);
  }
  if (event.keyCode == 45) { //-
    event.returnvalue = false;
    return;
  }
  var valid;
  switch (type) {
    case 1:
      valid = isdate(text);
      break;
    case 2:
      valid = istime(text);
      break;
    case 3:
      valid = isdatetime(text);
      break;
    default:
      valid = false;
  }
  if (!valid) {
    event.returnValue = false;
  } else {
    if (event.keyCode != 37 && event.keyCode != 36) {
      event.returnValue = false;
      if (event.keyCode != 8) {
        var nowpos;
        nowpos = endpos + 1;
        switch (endpos) {
          case 3:
          case 6:
          case 9:
          case 12:
            //case 15:
            nowpos++;
            break;
          default:
            break;
        }
      } else {
        var nowpos;
        nowpos = endpos - 1;
        switch (endpos) {
          case 3:
          case 6:
          case 9:
          case 12:
          case 15:
            //case 18:
            nowpos--;
            break;
          default:
            break;

        }
      }
      obj.value = text;
      var selectstart = obj.onselectstart;
      if (selectstart != null)
        obj.onselectstart = function () {
          return true;
        }
      if (obj.selectionStart == undefined || obj.selectionStart == null) {
        range = obj.createTextRange();
        range.collapse(true);
        range.moveStart('character', nowpos);
        range.select();
      } else {
        obj.selectionStart = nowpos;
        obj.selectionEnd = nowpos;
      }
      if (selectstart != null)
        obj.onselectstart = function () {
          return false;
        }
    }
  }
}

function copyDate(obj) {
  var trSeq = $(".csskssj").index($(obj));
  var kssj = $(".csskssj").eq(trSeq - 1).val();
  $(obj).val(kssj);
}

//对象、数组变化监听(增删改)
function deepProxy(obj, cb) {
  if (typeof obj === 'object') {
    for (let key in obj) {
      if (typeof obj[key] === 'object') {
        obj[key] = deepProxy(obj[key], cb);
      }
    }
  }
  return new Proxy(obj, {
    set: function (target, key, value, receiver) {
      if (typeof value === 'object') {
        value = deepProxy(value, cb);
      }
      let cbType = target[key] == undefined ? 'create' : 'modify';
      //排除数组修改length回调
      if (!(Array.isArray(target) && key === 'length')) {
        setTimeout((() => cb(cbType, {
          target,
          key,
          value
        })), 50)
      }
      return Reflect.set(target, key, value, receiver);
    },
    deleteProperty(target, key) {
      setTimeout((() => cb('delete', {
        target,
        key
      })), 50)
      return Reflect.deleteProperty(target, key);
    }
  });
}
//队列循环
function export_Lists(){
  lsSelectData = []
  if(bqxz_index!=null&&bqxz_index<bqxzarr.length){
    AddSaveRepeat(bqxzarr[bqxz_index])
    bqxz_index++
  }else if(all_index!=null&&all_index<all_arr.length-1){
    all_index++
    let list=all_arr[all_index]
    if(list.LDH>0){
      let arr=bqxz_lists.filter(e=>e.LDH == list.LDH&&list.LDH>0)
      if(arr&&arr.length>0){
        bqxzarr=arr
        let activeData=bqxzarr[0]
        bqxz_index=1
        MLXH = yxyz.length ? Math.max.apply(Math, yxyz.map(item => { return item.MLXH })) + 1 : 1 //目录序号增加1
        AddSaveRepeat(activeData)
      }
    }else{
      bqxzarr=[];
      bqxz_index=null;
      MLXH = yxyz.length ? Math.max.apply(Math, yxyz.map(item => { return item.MLXH })) + 1 : 1 //目录序号增加1
      AddSaveRepeat(list)
    }
  }else{
    bqxzarr=[];
    bqxz_index=null;
    lsSelectData = [...yxyz]
    // console.log(lsSelectData,yxyz,'队列数据(export_Lists)5',(bqxz_index!=null&&bqxz_index<bqxzarr.length),(all_index!=null&&all_index<all_arr.length-1));
    
  }
}
// 处理勾选化学项目列表前(处理重复项目控制)
function AddSaveRepeat(activeData){
  if(activeData.SFTC==1){
    let arr=[]
    yxyz.forEach(function(item){
      arr.push(item.HYID)
    })
    WRT_e.api.bqxz.checkHytcRepeatItem({
      params:{
        ll_hyid:activeData.HYID,
        li_hyids:arr
      },
      success(data){
        if(data.Code==1){
          WRT_e.ui.message({
            title: '信息窗口',
            content: `套餐包含重复项目${data.result && data.result!=''?`【${data.result}】`:``}已开，是否继续添加？`,
            onOk() {
              AddSave(activeData)
            },
            onCancel() {
              export_Lists()
            },
          })
        }else{
          AddSave(activeData)
        }
      }
    })
  }else{
    AddSave(activeData)
  }
}
//处理勾选化学项目列表
function AddSave(activeData){
  ZJshow=true
  let addData = (obj = {}) => { //新增数据
    getCheckHySqd(activeData)
    //添加颜色
    let rtn=true
    yxyz.forEach((e, idx) => {
      if (e.HYID == activeData.HYID && !e.SHOWCOLOE){
        yxyz[idx].SHOWCOLOE = true
        if(rtn){
          rtn=false
          WRT_e.ui.message({
            title: '信息窗口',
            content: `【${yxyz[idx].HYMC}】重复`,
            onOk() {
            }
          })
        }
      } 
    })
    if (activeData.SFSXD != 1) {

      if (activeData.LDH > 0) { //关联号ldh要大于零
        let e=activeData
        // this.data.forEach(e => {
        //   if (e.LDH == activeData.LDH) {
        yxyz.push({
          YZID: 0, //医嘱ID
          BLID: params["as_blid"], //病历ID
          MBID: e.MBID, //模板ID
          HYMC: e.MC, //化验名称
          MLMC: mlmc, //目录名称
          SFTC: e.SFTC, //是否套餐
          SFSL:e.SFSL||'',//数量
          MLID: e.MLID, //目录ID
          MLXH, //目录序号
          HYID: e.HYID, //化验项目ID
          YZBZ: e.YZBZ||"", //医嘱备注
          LDH: e.LDH || 0, //关联号
          SBSP: obj.sbsp || 0, //社保审批标志
          KSSJ: e.SFJZ == 1 ? kssj : '0000-00-00 00:00', //开始时间
          ZTBZ: 0, //状态标志
          DJ:e.DJ||"",
          ZCX:e.ZCX||"",
          SFXMID:e.SFXMID||''//财务ID
        })
        //   }
        // })
      } else {
        yxyz.push({
          YZID: 0, //医嘱ID
          BLID: params["as_blid"], //病历ID
          MBID: activeData.MBID, //模板ID
          HYMC: activeData.MC, //化验名称
          MLMC: mlmc, //目录名称
          SFTC: activeData.SFTC, //是否套餐
          SFSL:activeData.SFSL||'',//数量
          MLID: activeData.MLID, //目录ID
          MLXH, //目录序号
          HYID: activeData.HYID, //当前化验项目ID
          YZBZ: activeData.YZBZ || '', //医嘱备注
          LDH: activeData.LDH || 0, //关联号
          SBSP: obj.sbsp || 0, //社保审批标志
          KSSJ: activeData.SFJZ == 1 ? kssj : '0000-00-00 00:00', //开始时间
          ZTBZ: 0, //状态标志
          DJ:activeData.DJ||"",
          ZCX:activeData.ZCX||"",
          SFXMID:activeData.SFXMID||''//财务ID
        })
      }
    }
    lsSelectData=[]
      // console.log(lsSelectData,yxyz,'队列数据123',(bqxz_index!=null&&bqxz_index<bqxzarr.length),(all_index!=null&&all_index<all_arr.length-1));
   
    export_Lists()
  }
  //判断项目是否需要弹出审批窗口
  WRT_e.api.bqxz.sfxsSbsp({
    params: {
      ll_hyid: activeData.HYID,
      li_sftc: activeData.SFTC,
      al_blid: params["as_blid"],
    },
    success(data) {
      // data={"CodeMsg": "1","Result": [{"xzfw": "","sfxmid": "7657","sfxmmc": "染色体检查(不分带)　","zfbz": "1","zllx": "11","sbsp": "1","lczd": null}]}
      // data.CodeMsg = '1'
      switch (data.CodeMsg) {
        case "0":
          //部分项目必须填写备注信息 ('备注信息',hyfjbz,activeData)
          let fjbz = hyfjbz.filter(e => e.HYID == activeData.HYID)
          let cont=0
          let c = 0
          let key = -1 // 标记备注数据下标
          let xqfx = false // 血气分析
          let szjs = false // 生殖激素
          let nsjg = false // 内生肌酐
          fjbz.map((e,index) => {
            if(e.BT == '体温' || e.BT == '吸氧'){
              cont++
            }
            if(e.BT.indexOf('备注') !=-1 ){
              key = index
            }
            if(e.BT.indexOf('预期抽血的生理周期') !=-1){
              // e.BT == '预期抽血的生理周期'
              szjs = true
            }
            if(activeData.MC.indexOf('内生肌酐') !=-1){
              if(e.BT == '身高(cm)' || e.BT == '体重(Kg)') {
                c++
              }
              if(index == fjbz.length-1 && c==2) {
                nsjg = true
              }
            }
            if(index == fjbz.length-1 && cont==2) {
              return xqfx = true
            } else {
              return xqfx = false
            }
          })
          // 如果有备注字段将备注移到数组最后 ('备注信息',hyfjbz,fjbz,activeData)
          if(key != -1){
            fjbz.push(...fjbz.splice(fjbz.findIndex( (el,i) => i === key), 1))
          }
          // onblur="xqfxNum(this,value,${index})"
          //备注信息
          let addFjbz = () => {
            if (fjbz.length > 0 ) {
              WRT_e.ui.model({
                id: 'fjbz',
                title: '备注信息',
                closeButton: false,
                closeOnEscape: false,
                content: `
              <div>
                ${xqfx && (activeData.MC.indexOf('血气分析')>=0 || activeData.MC.indexOf('全血乳酸测定(静脉血气)')>=0)?`
                  <div class="form-group">
                    <label class="control-label" style="text-align: left;">【${activeData.MC}】体温35-42，小数点后一位；&nbsp;&nbsp;吸氧0.21-1，小数点后两位;【与L之间转换公式：吸氧量[L/min]*4/100+0.21】!</label>
                    <br/>
                    ${_.map(fjbz, (e,index) => `${e.BT}${e.BT.indexOf(':')==-1 ||e.BT.indexOf('：')==-1?`:    ${e.BT.indexOf('吸氧')!=-1?`(输入值大于1默认为吸氧量将进行自动计算,小于1默认为输入吸氧结果)`:``}`:``}
                    ${e.BT.indexOf('备注')==-1?` 
                      <input 
                        name="fjbz${index}" 
                        class="form-control" 
                        autocomplete="off" 
                        style="width:100%;" 
                        type="number" 
                        step="${e.BT=='体温'?0.1:0.01}" 
                        min="${e.BT=='体温'?35.0:0.21}" 
                        max="${e.BT=='体温'?42:1}" 
                        ${e.BT.indexOf('吸氧')!=-1?`onblur='xqfxCalculator(value)'`:``}
                      />`:`
                      <textarea 
                        name="fjbz${index}" 
                        rows="2" 
                        class="form-control" 
                        autocomplete="off" 
                        style="width:100%;"
                      />
                    `}`).join('')}
                  </div>`:`
                  ${szjs && activeData.MC.indexOf('生殖激素常规检查')>=0?`
                    <div class="form-group">
                      <label class="control-label" style="white-space:nowrap;">【${activeData.MC}】需要以下参数,请填写!</label>
                      <br/>
                      ${_.map(fjbz, e => `
                        <span style="padding-right:8px">${e.BT}:     </span>
                        ${e.ZQLX=='' || e.ZQLX==0?`
                          <label class="radio-inline">
                            <input name="fjbz" type="radio" value="卵泡期" checked >卵泡期</input>
                          </label>
                          <label class="radio-inline" style="margin-left:8px;">
                            <input name="fjbz" type="radio" value="排卵期" >排卵期</input>
                          </label>
                          <label class="radio-inline" style="margin-left:8px;">
                            <input name="fjbz" type="radio" value="黄体期" >黄体期</input>
                          </label>
                          <label class="radio-inline" style="margin-left:8px;">
                            <input name="fjbz" type="radio" value="绝经期" >绝经期</input>
                          </label>
                        `:`
                          ${_.map(checkNow, item => `
                            <label class="radio-inline" style="margin-left:8px;">
                              <input name="fjbz" type="radio" value="${item.DMMC}" >${item.DMMC}</input>
                            </label>
                          `)}
                          <label class="radio-inline" style="margin-left:8px;">
                            <input name="fjbz" type="radio" value="无" >无</input>
                          </label>
                        `}
                      `).join('')}
                    </div>
                  `:`
                  ${nsjg && activeData.MC.indexOf('内生肌酐') >=0?`
                  <div class="form-group">
                    <label class="control-label" style="text-align: left;">【${activeData.MC}】需要以下参数,请填写!</label>
                    <br/>
                    ${_.map(fjbz, (e,index) => `${e.BT}${e.BT.indexOf(':')==-1 ||e.BT.indexOf('：')==-1?`:    `:``}
                    <input 
                      name="fjbz${index}" 
                      class="form-control" 
                      autocomplete="off" 
                      style="width:100%;" 
                      type="text"  
                    />`).join('')}
                  </div>
                  `:`
                    <div class="form-group">
                      <label class="control-label" style="white-space:nowrap;">【${activeData.MC}】需要以下参数,请填写!</label>
                      <input name="fjbz" class="form-control" style="width:100%;" value="${_.map(fjbz, e => `${e.BT}:     `).join('')}"/>
                    </div>
                  `}`}`}
              </div>
              `,
                onOk(model) {
                  if(xqfx){
                    activeData.YZBZ=''
                    const valtw = model.find('input[name=fjbz0]').val()
                    const valxy = model.find('input[name=fjbz1]').val()
                    const valbz = model.find('textarea[name=fjbz2]').val() || ''
                    // const valtw = parseInt(model.find('input[name=fjbz0]').val(), 10) != model.find('input[name=fjbz0]').val() ? model.find('input[name=fjbz0]').val(): model.find('input[name=fjbz0]').val() +'.0'
                    // const valxy = parseInt(model.find('input[name=fjbz1]').val(),10)!=model.find('input[name=fjbz1]').val() && model.find('input[name=fjbz1]').val()!=''?model.find('input[name=fjbz1]').val().split('.')[1].length==1?model.find('input[name=fjbz1]').val() +'0':model.find('input[name=fjbz1]').val().split('.')[1].length==2?model.find('input[name=fjbz1]').val():'': model.find('input[name=fjbz1]').val()==''?'':model.find('input[name=fjbz1]').val() +'.00'
                    fjbz.map((e)=>{
                      if(e.BT == '体温'){
                        activeData.YZBZ += e.BT + ':' + Number(valtw).toFixed(1) + ' '
                      } else if(e.BT == '吸氧') {
                        activeData.YZBZ += e.BT + ':' + Number(valxy).toFixed(2)
                      } else if(e.BT == '备注') {
                      // else if(e.BT == '备注' && valbz!='') {
                        activeData.YZBZ += ' ' + e.BT + ':' + valbz
                      }
                    })
                    if(activeData.YZBZ=='体温: 吸氧:' ||valtw=='' || valxy==''){
                      WRT_e.ui.hint({
                        msg: '请将数据填写完整',
                        type:'error'
                      })
                    } else if((valtw<35 ||valtw>42) && (valxy<0.21 ||valxy>1)){
                      WRT_e.ui.hint({
                        msg: '请将根据要求填写体温和吸氧数据',
                        type:'error'
                      })
                    } else if((valtw<35 ||valtw>42)){
                      WRT_e.ui.hint({
                        msg: '请根据要求填写体温数据',
                        type:'error'
                      })
                    } else if(valxy<0.21 ||valxy>1){
                      WRT_e.ui.hint({
                        msg: '请根据要求填写吸氧数据',
                        type:'error'
                      })
                    } else if(valbz!=undefined && valbz.length>=50){
                      WRT_e.ui.hint({
                        msg: '备注字段不能超过50',
                        type:'error'
                      })
                    } else {
                      model.iziModal('destroy') //关闭modal
                      addData()
                    }
                  } else if(szjs){
                    activeData.YZBZ = model.find('input[name=fjbz]:checked').val()=='无'?``:'预期抽血的生理周期：'+model.find('input[name=fjbz]:checked').val()
                    model.iziModal('destroy') //关闭modal
                    addData()
                  } else if(nsjg){
                    activeData.YZBZ=''
                    const valsg = model.find('input[name=fjbz0]').val() || ''
                    const valtz = model.find('input[name=fjbz1]').val() || ''
                    // activeData.YZBZ = model.find('input[name=fjbz]').val()
                    fjbz.map((e)=>{
                      if(e.BT == '身高(cm)'){
                        activeData.YZBZ += e.BT + ':' + valsg + ' '
                      } else if(e.BT == '体重(Kg)') {
                        activeData.YZBZ += e.BT + ':' + valtz
                      }
                    })
                    if(activeData.YZBZ=='身高(cm): 体重(Kg):' || (valsg=='' && valtz=='')){
                      WRT_e.ui.hint({
                        msg: '请将数据填写完整',
                        type:'error'
                      })
                    } else if(valsg==''){
                      WRT_e.ui.hint({
                        msg: '请将身高数据填写完整',
                        type:'error'
                      })
                    } else if(valtz==''){
                      WRT_e.ui.hint({
                        msg: '请将体重数据填写完整',
                        type:'error'
                      })
                    } else {
                      model.iziModal('destroy') //关闭modal
                      addData()
                    }
                  } else {
                    activeData.YZBZ = model.find('input[name=fjbz]').val()
                    // activeData.YZBZ = model.find('input[name=fjbz]:checked').val()
                    model.iziModal('destroy') //关闭modal
                    addData()

                  }
                },
              })
            } else {
              addData()
            }
          }
          if (fjbz.length > 0  && (fjbz[0].ZQLX!=undefined && fjbz[0].ZQLX!=null && fjbz[0].ZQLX!='')) {
            WRT_e.api.bqxz.getZqlbByZqlx({
              params: {
                ll_zqlx: fjbz[0].ZQLX,
              },
              success(data) {
                checkNow = JSON.parse(data.Result)
                addFjbz()
              }
            })
          }
          //输血单项目退出
          if (activeData.SFSXD == 1) {
            WRT_e.ui.hint({
              msg: '交叉配血请在输血单中选择医嘱（输血单已提交也可补填）'
            })
            // lsSelectData = [...yxyz]
            // export_Lists() // ？不确定有没有增加的必要
            // return // 尝试提示后去除该数据继续增加医嘱,在1362行增加判断，不确定是否会冲突，还需要测试
          }
          //指定病区，所有项目的包含关系
          //该HYID包容数字
          let HYID1 = Hykz.filter(e => e.HYID1 == activeData.HYID)
          let HYID2 = Hykz.filter(e => e.HYID2 == activeData.HYID)
          for (let [idx, obj] of yxyz.entries()) { //循环已添加的医嘱
            if (HYID1.find(e => e.HYID2 == obj.HYID)||HYID2.find(e => e.HYID1 == obj.HYID)) { //有包容在
              yxyz[idx].SHOWCOLOE = true //添加颜色
              WRT_e.ui.message({
                title: '信息窗口',
                content: `【${activeData.MC}】已开,并且项目【${activeData.MC}】包含【${obj.HYMC}】,是否仍要添加？`,
                onOk() {
                  addFjbz()
                },
                onCancel() {
                  export_Lists()
                },
              })
              return
            }
          }
          //是否存在相同
          if(bqxzarr.length>0&&bqxz_index==1){
            for (let obj of yxyz) {
              if (obj.HYID == activeData.HYID) { //相同
                WRT_e.ui.message({
                  title: '信息窗口',
                  content: `项目【${obj.HYMC}】已开，是否仍需添加？`,
                  onOk() {
                    addFjbz()
                  },
                  onCancel() {
                    // export_Lists()
                    bqxzarr=[];
                    bqxz_index=null;
                    export_Lists()
                  },
                })
                return
              }
            }
          }
          //新增
          addFjbz()
          break;
        case "1":
          let obj = data.Result[0]
          // iframeURL: `${WRT_config.server}/newhyyz/zlsbsp.aspx?av_xzfw=${obj.xzfw}&av_sfxmid=${obj.sfxmid}&av_sfxmmc=${obj.sfxmmc}&av_zfbz=${obj.zfbz}&av_zllx=${obj.zllx}&av_splb=${obj.sbsp}&av_lczd=${obj.lczd}`,
          if (obj.sbsp == 1) {
            WRT_e.ui.model({
              id: '社保审批',
              title: '社保审批',
              width: 500,
              closeButton: false,
              closeOnEscape: false,
              content: `
              <div class="form-horizontal">
                <div class="form-group">
                  <label class="col-sm-4 control-label">${obj.sfxmmc.trim()}\n社保使用范围限制:</label>
                  <div class="col-sm-8">
                    <textarea class="form-control" rows="3" readonly style="margin-bottom:5px;">${obj.xzfw}</textarea>
                    <textarea class="form-control" rows="3" readonly>${obj.lczd}</textarea>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-4 control-label"></label>
                  <div class="col-sm-8">
                    <p style="margin-bottom:0;" class="bg-danger">该项目需审批后才能记账。</p>
                  </div>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-4 control-label"></label>
                  <div class="col-sm-8">
                    <label class="radio-inline">
                      <input type="radio" name="zfbz" value="4" ${obj.zfbz == 4 ? 'checked' : ''}>用于普通疾病
                    </label>
                    <label class="radio-inline">
                      <input type="radio" name="zfbz" value="1" ${obj.zfbz == 1 ? 'checked' : ''}>自费
                    </label>
                  </div>
                </div>
              </div>
              `,
              onOk(model) {
                //新增
                addData({
                  sbsp: model.find('input[name="zfbz"]:checked').val()
                })
                model.iziModal('destroy') //关闭modal
              },
            })
          }else if(obj.sbsp==0){
            WRT_e.ui.model({
              id: '社保审批',
              title: '社保审批',
              width: 500,
              closeButton: false,
              closeOnEscape: false,
              content: `
              <div class="form-horizontal">
                <div class="form-group">
                  <label class="col-sm-4 control-label">${obj.sfxmmc.trim()}\n社保使用范围限制:</label>
                  <div class="col-sm-8">
                    <textarea class="form-control" rows="3" readonly style="margin-bottom:5px;">${obj.xzfw}</textarea>
                    <textarea class="form-control" rows="3" readonly>${obj.lczd}</textarea>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-4 control-label"></label>
                  </div>
                </div>
                <div class="form-group">
                  <label class="col-sm-2 control-label"></label>
                  <div class="col-sm-10">
                    <label class="radio-inline">
                      <input type="radio" name="zfbz" value="0" ${obj.zfbz == 0 ? 'checked' : ''}>我知道了，符合条件
                    </label>
                    <label class="radio-inline">
                      <input type="radio" name="zfbz" value="1" ${obj.zfbz == 1 ? 'checked' : ''}>不符合情况，自费
                    </label>
                  </div>
                </div>
              </div>
              `,
              onOk(model) {
                //新增
                addData({
                  sbsp: model.find('input[name="zfbz"]:checked').val()
                })
                model.iziModal('destroy') //关闭modal
              },
            })
          }
          break;
      }
    }
  })

}
//获取所有需要弹出申请单的化验项目
function getCheckHySqd(activeData){
  if(WRT_config.CheckHySqd&&WRT_config.CheckHySqd.length>0){
    isCheckHySqd(activeData)
  }else{
    WRT_e.api.bqxz.CheckHySqd({
      params:{},
      success(data){
        if(data.Code==1){
          WRT_config.CheckHySqd=JSON.parse(data.Result)
          isCheckHySqd(activeData)
        }
      }
    })
  }
}
//判断是否需要特检申请单
function isCheckHySqd(activeData){
  let fd=WRT_config.CheckHySqd.filter(e=>e.NBLB==activeData.HYID)
  if(fd&&fd.length>0){
    let myvalue = $('input:radio[name="rbl_sss"]:checked').val();
    let iWidth = 700;
    let iHeight = 700;
    let iTop = (window.screen.availHeight - 30 - iHeight) / 2;
    let iLeft = (window.screen.availWidth - 10 - iWidth) / 2;
    let ls_url =WRT_config.server +`/zyyz/zytjsqdtx.aspx?ly=hyyz&av_blid=${params["as_blid"]}&av_bqid=${myvalue}&av_zkid=${params["as_yszkid"]}&av_zllx=11&as_zlzid=${params["as_zlzid"]}&av_yzmlid=${fd[0].DM}&av_sqdstate=0&av_zyid=${params["as_zyid"]}&av_bmid=${params["ys_bmid"]}`
    window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
  }
}
// 对血气分析数字进行校验
function xqfxNum(ev, val,name){
  if(name==0){
    // 体温
    if(parseInt(val)<35){
      return ev.value = 35.0;
    } else if(parseInt(val)>42) {
      return ev.value = 42.0;
    } 
  } else if(name==1){
    // 吸氧
    if(parseInt(val)<0.21){
      return ev.value = 0.21;
    } else if(parseInt(val)>1) {
      return ev.value = 1.00;
    } else {
      return ev.value = Number(val);
    }
  }
}
// 血气分析吸氧计算
function xqfxCalculator(val) {
  let xyVal = Number(val)
  if(xyVal>1){
    let xyResult = (xyVal*4/100+0.21)
    return $("input[name='fjbz1']").val(xyResult.toFixed(2))
  }
}

/********************视图********************/
// <div class="form_search">
//   <label for="searchBqxz">输入拼音首字母查找：</label>
//   <input type="text" class="form_input" name="searchBqxz" onkeydown="calAge(event)">
//   <button style="height: 34px;margin-left: 6px;" type="submit" class="e_btn" onclick="searchBqxz()">查找</button>
// </div>
//病区切换
var zxbq_View = WRT_e.view.extend({
  render: function () {
    var html = `
		<div class="bqxz-radio" style="padding-top: 5px;">
			<label class="radio-inline">
					<input type="radio" name="rbl_sss" value="${params["as_bqid"]}" checked="checked">病人当前病区
			</label>
			${_.map(this.data, obj => `
			<label class="radio-inline">
				<input type="radio" name="rbl_sss" value="${obj.MC}">${obj.BZ}
			</label>
			`).join('')}
		</div>
    `
    this.$el.html(html)
    return this;
  },
})
//中间的化学项目列表
var bqxzItem_View = WRT_e.view.extend({
  render: function () {
    var html = `
		<div class="bqxz_item">
			<div class="bqxz_header">
				<span class="title">${mlmc}</span>
			</div>
			<table>
				<tr class="bqxz_table_tr">
					<th>
						<label>
							<input class="allcheck" type="checkbox" value="0">
						</label>
					</th>
					<th>控制级别</th>
					<th>化验名称</th>
					<th>单价</th>
					<th>样本类型</th>
				</tr>
				${_.map(this.data, obj => {
      //随机颜色
      //同样联动号颜色一样
      (obj.LDH > 0 && rgb[obj.LDH] == undefined) ? rgb[obj.LDH] = `rgb(${Math.floor(Math.random() * 256)},${Math.floor(Math.random() * 256)},${Math.floor(Math.random() * 256)})` : ""
      return `<tr class="bqxz_table_jtnr" data-hyid=${obj.HYID} >
            <td><input name="checkedBox" type="checkbox" ${yxyz.find(e => (e.HYID == obj.HYID)) ? 'checked' : ''}></td>
            <td class="kzjb">${obj.KZJB || ''}<span class="icon_circular" style="background:${rgb[obj.LDH]};"></span></td>
            <td title="${obj.BZ || ''}" class="hymc">
              <span style="background:${yxyz.find(e => (e.HYID == obj.HYID)) ? '#FFCBFF' : ''}" >
                ${obj.BZ ? '?' : ''}${obj.MC}
              </span>
            </td>
            <td class="dj">￥${obj.DJ || ''}${obj.SFSL>1?` * ${obj.SFSL}`:''}</td>
            <td class="yblx">${obj.YBLX || ''}</td>
            </tr>`
    }).join('')}
				</table>
			</div>`
    this.$el.html(html)
    return this;
  },
  events: {
    "click .bqxz_item table tr:not(:first-child)": "toAddSave",
    "click .allcheck":"allcheck",
  },
  allcheck(event){ // 全选
    event.preventDefault()
    bqxz_lists=this.data||[]
    all_arr=[]
    all_index=0
    bqxzarr=[]
    bqxz_index=0
    let xh=false
    allChecked = true
    $(".btn_save").attr("readonly",'readonly'); // 禁止点击readonly
    this.data.map(function(item){
      if(item.LDH>0&&xh!=item.LDH){
        xh=item.LDH
        all_arr.push(item)
      }else if(!item.LDH){
        all_arr.push(item)
      }
    })
    
    if(all_arr[0].LDH>0){
      let arr=this.data.filter(e=>e.LDH == all_arr[0].LDH&&all_arr[0].LDH>0)
      if(arr&&arr.length>0){
        bqxzarr=arr
        let activeData=bqxzarr[0]
        bqxz_index=1
        // $(".btn_save").attr("readonly",'readonly'); // 禁止点击readonly
        MLXH = yxyz.length ? Math.max.apply(Math, yxyz.map(item => { return item.MLXH })) + 1 : 1 //目录序号增加1
        AddSaveRepeat(activeData)
      }
    }else{
      // $(".btn_save").attr("readonly",'readonly'); // 禁止点击readonly
      MLXH = yxyz.length ? Math.max.apply(Math, yxyz.map(item => { return item.MLXH })) + 1 : 1 //目录序号增加1
      AddSaveRepeat(all_arr[0])
    }
  },
  toAddSave(event) { // 点击选中选
    event.preventDefault()
    all_arr=[]
    all_index=null
    allChecked = false
    let activeData = this.data.find(e => e.HYID == $(event.currentTarget).data("hyid"))
    bqxzarr=[]
    let arr=this.data.filter(e=>e.LDH == activeData.LDH&&activeData.LDH>0)
    if(arr&&arr.length>0){
      bqxzarr=arr
      activeData=bqxzarr[0]
      bqxz_index=1
      // lsNumZ++
      // lsNum = bqxzarr.length
      // export_Lists()
      // let lsList = yxyz.filter(item=>item.LDH == bqxzarr[0].LDH)

      // $(".btn_save").attr("disabled",'disabled'); // 禁止点击
      $(".btn_save").attr("readonly",'readonly'); // 禁止点击readonly
      // $(".btn_save").addClass("none");
    }else{
      // 单条数据
      bqxz_index=null
    }
    MLXH = yxyz.length ? Math.max.apply(Math, yxyz.map(item => { return item.MLXH })) + 1 : 1 //目录序号增加1
    AddSaveRepeat(activeData)
  
  },
})
//右侧已选医嘱
var bqxzSave_View = WRT_e.view.extend({
  render: function () {
    // 已选医嘱右边展示数据
    let sum=0
    this.data.map(function(item){
      if(item.DJ){
        let JG=0
        if(item.SFSL){
          JG=item.SFSL*item.DJ
        }else{
          JG=item.DJ
        }
        sum=sum+JG
      }
    })
    if(sum>0&&ZJshow){
      $("#sel_title").html(`已选择医嘱 （总价：<span style="fone-size:15px">${sum}元</span>） 不包含上一次打开病人保存时的化验价格`)
    }else{
      $("#sel_title").html(`已选择医嘱`)
    }
    var MLXH = [] //已选里的关联号集合
    var ZCX = [] //已选里的主次项集合
    var html = `
		<div class="bqxz_save">
			<table border="1" cellspacing="0">
				<tr class="bqxz_table_tr">
					<th style="width:50px;"></th>
					<th style="width:140px;">目录</th>
					<th style="width:190px;">名称(<span class="red">双击项目名称填写备注</span>)</th>
					<th style="width:150px;">计划开始时间</th>
					<th style="width:100px;">医嘱备注</th>
					<th style="width:100px;">首次录入时间</th>
					<th style="width:100px;">医嘱用户</th>
					<th style="width:100px;">医嘱修改时间</th>
					<th style="width:100px;">医嘱修改用户</th>
					<th style="width:100px;">医嘱类别代码</th>
					<th style="width:60px;">化验ID</th>
					<th style="width:60px;">目录ID</th>
					<th style="width:60px;">联动号</th>
					<th style="width:60px;">序号</th>
					<th style="width:60px;">套餐</th>
					<th>模板</th>
				</tr>
				${_.map(this.data, (obj, index) => {
      return `
						<tr data-yzid=${obj.YZID} data-index="${index}">
						${function () {
          //判断要不要删除键
          let del = ''
          if (MLXH.indexOf(obj.MLXH) > -1&&(ZCX.indexOf(obj.ZCX)> -1||!obj.ZCX)) (del = `<td></td>`)
          else if(MLXH.indexOf(obj.MLXH) > -1&&obj.ZCX&&ZCX.indexOf(obj.ZCX)== -1)(del = `<td class="${obj.SFKS==2?'del_not':'del'}" data-mlxh=${obj.MLXH} data-zcx=${obj.ZCX} data-yzid=${obj.YZID} style="cursor:pointer;padding:0 5px"><span class="${obj.SFKS==2?'delbtn_not':'delbtn'}" ${obj.SFKS==2?'title="该医嘱是医务处要求不可删除"':''}>${obj.SFKS==2?'说明':'删除'}</span></td>`)
          else (MLXH.push(obj.MLXH), del = `<td class="${obj.SFKS==2?'del_not':'del'}" data-mlxh=${obj.MLXH} data-yzid=${obj.YZID} style="cursor:pointer;padding:0 5px"><span class="${obj.SFKS==2?'delbtn_not':'delbtn'}" ${obj.SFKS==2?'title="该医嘱是医务处要求不可删除"':''}>${obj.SFKS==2?'说明':'删除'}</span></td>`)
          return del
        }()} 
						<td>${obj.MLMC}</td>
						<td class="hymc">
              <span style="background:${obj.SHOWCOLOE ? 'yellow' : ''}" >
                <span title="知识库弹窗" class="glyphicon glyphicon-question-sign" id="questionTip" data-name="${obj.HYID}" style="color:#85A2DB"></span>
                ${obj.HYMC}
              </span>
            </td>
						<td><input class="form-control csskssj" type="text" data="${index}" value="${obj.KSSJ}" onkeydown="validate(this,3)" ondblclick="copyDate(this)" /></td>
						<td class="bz_text">${obj.YZBZ||''}</td>
						<td>${obj.SCLRSJ || ''}</td>
						<td>${obj.YZYHID || ''}</td>
						<td>${obj.YZSJ || ''}</td>
						<td>${obj.XGYHID || ''}</td>
						<td>${obj.YZLBDM || ''}</td>
						<td>${obj.HYID}</td>
						<td>${obj.MLID}</td>
						<td>${obj.LDH}</td>
						<td>${obj.MLXH}</td>
						<td>${obj.SFTC}</td>
						<td>${obj.MBID}</td>
						</tr>`
    }).join('')}
			</table>
		</div>`
    this.$el.html(html)
    return this;
  },
  events: {
    "blur .bqxz_save .csskssj": "saveDate",
    "click .bqxz_save .del": "toDel",
    "click .bqxz_save .del_not": "toDel_not",
    "dblclick .bqxz_save .hymc": "editBz",
    "dblclick .bqxz_save .bz_text": "editBz",
    "click #questionTip": "zskTip",
  },
  saveDate(event) {
    let index = $(event.currentTarget).parent().parent().data('index')
    let list=yxyz[index]
    const val=$(event.currentTarget).val()
    this.data=this.data.map((item)=>{
      if(item.MLXH==list.MLXH&&item.KSSJ!=val){
        return {...item,KSSJ:val};
      }
      return item;
    })
    //监听已选遗数组(当前注释代码)————不知道什么情况触发
    yxyz = deepProxy(this.data, (type, data) => {
      //渲染已选医嘱
      if (bqxzSave) bqxzSave.render()
      //渲染化验项目
      if (bqxzItem) bqxzItem.render()
      // if(yxyz.length==0){
      //   $(".btn_save1").addClass("none");
      //   $(".btn_save").removeClass("none");
      // }
    });
    this.render()
   // yxyz[index].KSSJ = $(event.currentTarget).val()
  },
  toDel(event) { //删除
    let MLXH = $(event.currentTarget).data("mlxh")
    let YZID = $(event.currentTarget).data("yzid")
    let ZCX = $(event.currentTarget).data("zcx")
    if (YZID > 0) { //已保存的询问删除
      WRT_e.ui.message({
        title: '信息窗口',
        content: `该医嘱已经保存过，是否删除？`,
        
        onOk() {
          if(ZCX){
            arr=yxyz.filter(e => e.ZCX == ZCX&& e.MLXH == MLXH).map(e => e.YZID)
          }else{
            arr=yxyz.filter(e => e.MLXH == MLXH).map(e => e.YZID)
          }
          WRT_e.api.bqxz.deleteHYYZ({
            params: {
              yzidStr: JSON.stringify(arr)
            },
            success(data) { //删除成功
              if (data.indexOf("1")) {
                $(".btn_save").removeAttr("readonly");
                $(".btn_save1").addClass("none");
                $(".btn_save").removeClass("none");
                WRT_e.ui.hint({
                  type: 'success',
                  msg: '删除成功。'
                })
                // lsNumZ--
              } else {
                WRT_e.ui.hint({
                  type: 'success',
                  msg: '删除失败，可能已经导出或删除!'
                })
              }
              if(ZCX){
                for (var i = yxyz.length - 1; i >= 0; i--) {
                  if (yxyz[i].ZCX == ZCX&& yxyz[i].MLXH == MLXH) yxyz.splice(i, 1)
                }
              }else{
                for (var i = yxyz.length - 1; i >= 0; i--) {
                  if (yxyz[i].MLXH == MLXH) yxyz.splice(i, 1)
                }
              }
            }
          })
        },
        onCancel() { },
      })
    } else { //未保存直接删除
      if(ZCX){
        for (var i = yxyz.length - 1; i >= 0; i--) {
          // console.log(yxyz[i].ZCX , ZCX, yxyz[i].MLXH , MLXH)
          if (yxyz[i].ZCX == ZCX&& yxyz[i].MLXH == MLXH) {
            let idx = lsshownowData.findIndex(e=>e.LDH == yxyz[i].LDH && e.HYID == yxyz[i].HYID)
            yxyz.splice(i, 1)
            lsSelectData.splice(i, 1)
            lsshownowData.splice(idx, 1)
            // console.log(idx,lsshownowData);
          }
        }
      }else{
        for (var i = yxyz.length - 1; i >= 0; i--) {
          if (yxyz[i].MLXH == MLXH) {
            let idx = lsshownowData.findIndex(e=>e.LDH == yxyz[i].LDH && e.HYID == yxyz[i].HYID)
            yxyz.splice(i, 1)
            lsSelectData.splice(i, 1)
            lsshownowData.splice(idx, 1)
            // console.log(8888,idx,lsshownowData);
          }
        }
      }
      
      // lsNumZ--
      // lsNum = yxyz.length
    }
    if(yxyz.length==0){
      $(".btn_save1").addClass("none");
      $(".btn_save").removeClass("none");
    }
  },
  toDel_not(){
    WRT_e.ui.message({
      title: '提示',
      content: `该医嘱是医务处要求不可删除`,
      onOk() {}
    })
  },
  editBz(event) { //编辑备注
    let index = $(event.currentTarget).parent().data('index')
    let obj = yxyz[index] || {}
    let yzbzAll = obj.YZBZ.split(' ') // YZBZ是右侧备注展示参数eg.体温:35.0 吸氧:1.00
    let fjbz = []
    let cont=0
    let c=0
    let key = -1 // 标记备注数据下标
    yzbzAll.map(e=>{
      const item = e.split(':')
      fjbz.push({BT: item[0],val: item[1]})
    })
    let xqfx = false // 用于判断血气分析（判断fjbz返回参数是否只有体温和吸氧）
    let szjs = false // 生殖激素
    let nsjg = false // 内生肌酐
    fjbz.map((e,index) => {
      // if((e.BT == '体温' || e.BT == '吸氧') && fjbz.length >= 2){
      if(e.BT == '体温' || e.BT == '吸氧'){
        cont++
      }
      if(e.BT.indexOf('备注') !=-1 ){
        key = index
      }
      if(e.BT.indexOf('预期抽血的生理周期') !=-1){
        // e.BT == '预期抽血的生理周期'
        szjs = true
      }
      if(obj.HYMC.indexOf('内生肌酐')!=-1){
        if(e.BT == '身高(cm)' || e.BT == '体重(Kg)') {
          c++
        }
        if(index == fjbz.length-1 && c==2) {
          nsjg = true
        }
      }
      if(index == fjbz.length-1 && cont==2) {
        return xqfx = true
      } else {
        return xqfx = false
      }
    })
    // 如果有备注字段将备注移到数组最后
    if(key != -1){
      fjbz.push(...fjbz.splice(fjbz.findIndex( (el,i) => i === key), 1))
    }
    WRT_e.ui.model({
      id: 'editBz',
      title: '化验备注',
      width: xqfx?520:400,
      content: `
      <div class="form-horizontal">
        <div class="form-group">
        ${xqfx && (obj.HYMC.indexOf('血气分析')>=0 || obj.HYMC.indexOf('全血乳酸测定(静脉血气)')>=0)?`<label class="control-label" style="text-align: left;">【${obj.HYMC}】体温35-42，小数点后一位；&nbsp;&nbsp;吸氧0.21-1，小数点后两位【与L之间转换公式：吸氧量[L/min]*4/100+0.21】!</label>
          <br/>`:`
          ${szjs && obj.HYMC.indexOf('生殖激素常规检查')>=0 ?``:`
            <label class="col-sm-3 control-label" style="white-space:nowrap;">备注信息：</label>
          `}
          <br/>`}
          ${xqfx && (obj.HYMC.indexOf('血气分析')>=0 || obj.HYMC.indexOf('全血乳酸测定(静脉血气)')>=0)?`
          <div class="col-sm-9">
          ${_.map(fjbz, (e,index) => `
            <div>${e.BT}${e.BT.indexOf(':')==-1 ||e.BT.indexOf('：')==-1?`:    ${e.BT.indexOf('吸氧')!=-1?`(输入值大于1默认为吸氧量将进行自动计算,小于1默认为输入吸氧结果)`:``}`:``}
              ${e.BT.indexOf('备注')==-1?`
                <input 
                  name="fjbz${index}" 
                  class="form-control" 
                  autocomplete="off" 
                  style="width:100%;" 
                  type="number" 
                  value="${e.val}"
                  step="${e.BT=='体温'?0.1:0.01}" 
                  min="${e.BT=='体温'?35.0:0.21}" 
                  max="${e.BT=='体温'?42:1}" 
                  ${e.BT.indexOf('吸氧')!=-1?`onblur='xqfxCalculator(value)'`:``}
                /> `:`
                <textarea 
                  name="fjbz${index}" 
                  rows="2" 
                  class="form-control" 
                  autocomplete="off" 
                  style="width:100%;"
                >${e.val}</textarea>
              `}
            </div>
          `).join('')}
          </div>`:`
          ${nsjg && obj.HYMC.indexOf('内生肌酐')>=0?`
          <div class="col-sm-9">
          ${_.map(fjbz, (e,index) => `
            <div>${e.BT}${e.BT.indexOf(':')==-1 ||e.BT.indexOf('：')==-1?`:    `:``}
              <input 
                name="fjbz${index}" 
                class="form-control" 
                autocomplete="off" 
                style="width:100%;" 
                type="text" 
                value="${e.val}"
              />
            </div>
          `).join('')}
          </div>
          `:`
            <div class="col-sm-9">
              <textarea name="yzbz" rows="3" class="form-control" style="width:100%;"/>
            </div>
          `}`}
        </div>
      </div>
      `,
      onOk(model) {
        if(xqfx){
          const valtw = model.find('input[name=fjbz0]').val()
          const valxy = model.find('input[name=fjbz1]').val()
          const valbz = model.find('textarea[name=fjbz2]').val() || ''
          // const valtw = parseInt(model.find('input[name=fjbz0]').val(), 10) != model.find('input[name=fjbz0]').val() ? model.find('input[name=fjbz0]').val(): model.find('input[name=fjbz0]').val() +'.0'
          // const valxy = parseInt(model.find('input[name=fjbz1]').val(),10)!=model.find('input[name=fjbz1]').val() && model.find('input[name=fjbz1]').val()!=''?model.find('input[name=fjbz1]').val().split('.')[1].length==1?model.find('input[name=fjbz1]').val() +'0':model.find('input[name=fjbz1]').val().split('.')[1].length==2?model.find('input[name=fjbz1]').val():'': model.find('input[name=fjbz1]').val()==''?'':model.find('input[name=fjbz1]').val() +'.00'
          const zYZBZ = '体温:' + Number(valtw).toFixed(1) + ' ' + '吸氧:' +  Number(valxy).toFixed(2) + ' ' + '备注:' +  valbz || ''
          if(zYZBZ=='体温: 吸氧:' ||valtw=='' || valxy==''){
            WRT_e.ui.hint({
              msg: '请将数据填写完整',
              type:'error'
            })
          } else if((valtw<35 ||valtw>42) && (valxy<0.21 ||valxy>1)){
            WRT_e.ui.hint({
              msg: '请将根据要求填写体温和吸氧数据',
              type:'error'
            })
          } else if((valtw<35 ||valtw>42)){
            WRT_e.ui.hint({
              msg: '请根据要求填写体温数据',
              type:'error'
            })
          } else if(valxy<0.21 ||valxy>1){
            WRT_e.ui.hint({
              msg: '请根据要求填写吸氧数据',
              type:'error'
            })
          } else if(valbz!=undefined && valbz.length>=50){
            WRT_e.ui.hint({
              msg: '备注字段不能超过50',
              type:'error'
            })
          } else {
            obj.YZBZ = '体温:' + Number(valtw).toFixed(1) +' '+ '吸氧:' +  Number(valxy).toFixed(2) + ' ' + '备注:' + valbz || ''
             // obj.YZBZ = '体温:' + Number(model.find('input[name=fjbz0]').val()).toFixed(1) +' '+ '吸氧:' +  Number(model.find('input[name=fjbz1]').val()).toFixed(2) + ' ' + '备注:' + model.find('textarea[name=fjbz2]').val()
            model.iziModal('destroy')
          }
          // model.iziModal('destroy')
        } else if(nsjg){
          const valsg = model.find('input[name=fjbz0]').val() || ''
          const valtz = model.find('input[name=fjbz1]').val() || ''
          const zYZBZ = '身高(cm): '+valsg + ' '+ '体重(Kg): ' + valtz
          if(zYZBZ=='身高(cm): 体重(Kg):' || (valsg=='' && valtz=='')){
            WRT_e.ui.hint({
              msg: '请将数据填写完整',
              type:'error'
            })
          }else if(valsg==''){
            WRT_e.ui.hint({
              msg: '请将身高数据填写完整',
              type:'error'
            })
          } else if(valtz==''){
            WRT_e.ui.hint({
              msg: '请将体重数据填写完整',
              type:'error'
            })
          } else {
            obj.YZBZ = '身高(cm): '+valsg + ' '+ '体重(Kg): ' + valtz
             // obj.YZBZ = '体温:' + Number(model.find('input[name=fjbz0]').val()).toFixed(1) +' '+ '吸氧:' +  Number(model.find('input[name=fjbz1]').val()).toFixed(2) + ' ' + '备注:' + model.find('textarea[name=fjbz2]').val()
            model.iziModal('destroy')
          }
        } else {
          let text=  model.find('textarea[name=yzbz]').val()==''?'': model.find('textarea[name=yzbz]').val() || '预期抽血的生理周期：'+model.find('input[name=fjbz]:checked').val() || ''
          // let text= model.find('textarea[name=yzbz]').val() || '预期抽血的生理周期：'+model.find('input[name=fjbz]:checked').val() || ''
          if(text=='' || text.length<50){
            obj.YZBZ=text
            model.iziModal('destroy') //关闭modal
          }else{
            WRT_e.ui.hint({msg:'备注字段不能超过50'})
          }
        }
      },
    })
    //输入框设置默认值
    $('#editBz textarea[name=yzbz]').val(obj.YZBZ)
  },
  zskTip(event){
    event.preventDefault()
    let obj = yxyz.filter(item=> item.HYID==event.target.dataset.name)
    WRT_e.api.bqxz.getStaticCdssUrl({
      params: {
        as_xmmc: obj[0].HYMC
      },
      success(data) {
        if (data.Code =='1') {
          let iframeUrlI = ''
          if(data.Result.indexOf('?')>=0){
            iframeUrlI = data.Result+'&tmpid='+Math.random()
          }else{
            iframeUrlI = data.Result+'?tmpid='+Math.random()
          }
          WRT_e.ui.model({
            id: "if_questionTip",
            title: "知识库",
            width: "650px",
            iframeHeight: '800px',
            iframeURL: iframeUrlI,
            iframe: true,
            closeButton: true,
            closeOnEscape: false,
          })
        }
      }
    })
    

  }
})