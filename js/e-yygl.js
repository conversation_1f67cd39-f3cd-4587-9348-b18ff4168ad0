
$(document).ready(() => {
  
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  let params = {}
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  WRT_config.url = params
})

//弹出框关闭
function CloseModalWin(url, name, type) {
  if(url){
    if(type == 1){
      parent.e_OpenTab(url, name);
    } else {
      parent.OpenTab(url, name);
    }
  }
  $("#lb_sfhz").iziModal('destroy')
}

// 修改按钮
function btnChange() {
  WRT_e.ui.model({
    id: "lb_sfhz",
    title: "病人有营养风险",
    iframeHeight: "200px",
    iframe: true,
    iframeURL: `${WRT_config.server}/yyfxsc/yyfxsfhz.aspx?as_blid=${WRT_config.url.as_blid}&as_zyid=${WRT_config.url.as_zyid}}&as_zkid=${WRT_config.url.as_zkid}}&isnew=1&as_tmpid=0.3480926212701332&as_openmode=ehr3`
  })
}
// 营养风险筛查
function btnYyfxsc() {
  let times = new Date().getTime();
  let url = WRT_config.server + `/yyfxsc/yyfxsc.aspx?as_blid=${WRT_config.url.as_blid}&as_temp=${times}&as_openmode=ehr3`
  parent.page_iframe.add("营养风险筛查", url)
}
// 营养诊疗执行单
function btnYyzlzxd (){
  let url = WRT_config.server+'/'+`yyfxsc/yyzlzxd_list.aspx?as_blid=${WRT_config.url.as_blid}&as_temp=${Math.random()}`
  parent.page_iframe.add("营养诊疗执行单", url)
}
// 营养病例及营养食谱
function btnYybl (){
  window.open(`http://172.16.203.20:8091/ScreenUI.aspx?isHis=1&patientID=${WRT_config.url.patientID}&visitID=${WRT_config.url.visitID}&inpNO=${WRT_config.url.inpNO}&userNO=${WRT_config.url.userNO}&userName=${WRT_config.url.userName}&unitCode=${WRT_config.url.unitCode}&unitName=${WRT_config.url.unitName}&as_openmode=ehr3`)
}
// 营养评估单
function btnYypgd (){
  let url =  WRT_config.server+'/'+`yyfxsc/yypg_list.aspx?al_blid=${WRT_config.url.as_blid}&as_temp=${Math.random()}`
  parent.page_iframe.add("营养评估单", url)

}

// 营养图表
function btnyytb(){
  let url = `e-yytb.html?al_blid=${WRT_config.url.as_blid}&as_temp=${Math.random()}`
  parent.page_iframe.add("营养图表", url)
}