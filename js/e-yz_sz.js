var yzdata = []  //医嘱数据
var yzindex = null  //当前编辑行
var yz_isModel = null  //下拉菜单选中
var yz_JSDW = [] //计量单位
var as_delidlist = [] //删除队列
var yd_dataop = {} //药品附加信息
var yz_submit = []//提交医嘱队列
var yz_Lbmit = []
var yp_mb = [] //药品模板队列
var currentLine = 0 //滑动条序号
var al_ypZh = ''//双签名确认
var MBDRList = []//模板导入队列
var mbindex = 0 //模板导入序号
var zlyz_list = []//治疗模板队列
var defaultValue = null//上一个值
var clientX = null //偏移量x
var clientY = null //偏移量y
var inputX = null //偏移量x
var inputY = null //偏移量y
var ZhmbSrindex = 0 //综合模板序号
var ZhmbSrdata = [] //综合模板队列
var imgshow = false //医嘱输入框展开关闭控制
var ZlmbMxLbList = [] //治疗模板队列
var ZhmbByLbList = []//综合模板展示队列
var YZ_logicindex = -1
var YZ_logicList = []//执行队列
var yz_yzlx = '' //医嘱类型
var sfsl_type = null;//数量修改
var cfy_index//选中处方诊断序号
var cfy_Lists = []//处方诊断
var ybxz_list =[] // 医保使用限制范围如下，请根据疾病诊断准确选择：
var Tbmjtype = false//体表面积计算药品 1=是，0=否
var yzlbhtml = {
	'cq': '长期医嘱',
	'ls': '临时医嘱',
	'cy': '草药医嘱',
	'cydy': '出院带药',
	'zcydy': '草药带药',
}
var yz_lb = ['', '治疗', '饮食', '药品', '嘱托']
var yz_obj = { 'XH': '', 'KSSJ_TXT': '', 'MC': '', 'ZHTXT': '', 'YCYL': '', 'JLDW': '', 'GG': '', 'LB': 0, 'JINRILT':'', 'TSYF': '', "BQID": '', 'SFSL': '', 'CXTS': '', 'YYTS': '', 'DW': '', 'CS': '', 'JRCY': '', 'ZSBQ': '', 'JSSJ_TXT': '' }
var yz_szlist = ["SFZF", "SQSL", "splb", "XSP", "kjywhzdid", "TSKJYWHZDID", "ypfjxx", "SLKJYWHZDID", "TZDID", "JPYP", "kjypsyly", "KJYP_SYFF", "KJYP_YYFL", "KJYP_KJFL", "KJYP_SQSYFL", "KJYP_TBJB", "KJYP_FHKJ", "KJYP_FKNT", "KJYP_SYYY", "KJYP_TZSJ_STR", "KJYP_SSMC", "KJYP_QKLB", "KJYP_WSW", "KJYP_WSWBZ", "KJYP_QJSS", "KJYP_QJSS_SPSJ", "KJYP_QJSS_SPYSID", "KJYP_QJSS_SPYJ", "KJYP_QJSS_GWYS"]

//回车光标排序
var _SYS_YP_SORT = ["YZLX", 'KSSJ_TXT', 'MC', 'YCYL', 'ZXFF', 'ZXPL', 'GYSJ', "LB", 'tsyf', 'JSSJ_TXT'];//"kssj",
var _SYS_DY_SORT = ["YZLX", 'KSSJ_TXT', 'MC', 'YCYL', 'ZXFF', 'ZXPL', 'GYSJ', "LB", 'tsyf', "SFSL"];
var _SYS_ZL_SORT = ["YZLX", 'KSSJ_TXT', 'MC', 'ZXPL', "LB", "tsyf", "SFSL"];// "ff",
var _SYS_YS_SORT = ["YZLX", 'KSSJ_TXT', "MC", "LB", "tsyf", "JSSJ_TXT"];
var _SYS_CY_SORT = ["YZLX", 'KSSJ_TXT', "MC", "YCYL", 'ZXFF', 'ZXPL', 'GYSJ', "tsyf", "LB", "SFSL"];
var order = []
// var order_list = ['KSSJ_TXT', 'MC', 'YCYL', 'ZXFF', 'ZXPL', 'GYSJ','tsyf', 'JSSJ_TXT']
let move_index = ''//表格自定义序号
//排序列表 --表格生成时会根据该列表的排序渲染字段
let sortList = [
	{ show: true, key: '选择' },
	{ show: true, key: '组号' },
	{ show: true, key: '医嘱状态' },
	{ show: true, key: '医嘱类型' },
	{ show: true, key: '开始日期' },
	{ show: true, key: '开始时间' },
	{ show: true, key: '医嘱名称' },
	{ show: true, key: '组' },
	{ show: true, key: '一次用量' },
	{ show: true, key: '剂量单位' },
	{ show: true, key: '用法' },
	{ show: true, key: '频率' },
	{ show: true, key: '时间针频率' },
	{ show: true, key: '用药执行时间' },
	{ show: true, key: '规格/单价' },
	{ show: true, key: '医师姓名' },
	{ show: true, key: '类别' },
	{ show: true, key: '费用类型' },
	{ show: true, key: '自备' },
	{ show: true, key: '今日临停' },
	{ show: true, key: '特殊用法/备注说明' },
	{ show: true, key: '执行病区' },
	{ show: true, key: '数量剂数' },
	{ show: true, key: '持续天数' },
	{ show: true, key: '用药天数' },
	{ show: true, key: '单位' },
	{ show: true, key: '次数' },
	{ show: true, key: '预计出院' },
	{ show: true, key: '是否代煎' },
	{ show: true, key: '滴速' },
	{ show: true, key: '结束时间' },
	{ show: true, key: '手术通知单' },
	{ show: true, key: '医嘱时间' },
	{ show: true, key: '导入人员' },
	{ show: true, key: '导入时间' },
	{ show: true, key: '医嘱ID' },
	{ show: true, key: '闭环' },
	{ show: true, key: '不良反应上报' },
]
let arr_zgyq={
	Drugs:[],
	Charges:[]
}//规则引擎数组
var isZFType=false//触发规则引擎逻辑
let linShiTJ_array=[]//规则引擎提示数组
let tssm_array=[]//规则引擎特殊说明提示数组
let tssm_index=0//规则引擎特殊说明提示序号
let tssm_model={} //规则引擎特殊说明
let keyCode = ''
var ZlMb = false
var interCount = null
var ypfjxx_modal=null //药品附加信息模型
var ZiFjxx_modal=null //治疗附加信息模型
//暂存的排序列表
let cacheList = [];
// 惠每质控
// document.write('<script src="http://**************/cdss/jssdk?v=4.0&ak=CCD9D75192ACC4529888523BCD5AB9E2" type="text/javascript"></script>')
var hmzkUrl = WRT_config.server + '/zyblws/masonQC/wyyyMason.js'
//统一页面启动
$(document).ready(() => {
	//app.init()
	//
	// $(document).click(function(e){
	//     var a = $('#newyz_pad'); //设置空白以外的目标区域
	//     if(!a.is(e.target) && a.has(e.target).length === 0){
	//         keyCode=''
	//     }
	//     $(`${keyCode}`)
	// });
	//每3分钟获取服务器时间
	getKSSJ_TXT()
	// setInterval(() => {
	// 	getKSSJ_TXT()
	// }, 1000 * 60 * 3)
	//时间计时器
	interCount = setInterval(() => {
		WRT_config.defaultKSSJ_TXT = dateAdd(WRT_config.defaultKSSJ_TXT, 60);
	}, 1000 * 60)
	function dateAdd(d, num) {
		var d = new Date(d.substring(0, 4),
			d.substring(5, 7) - 1,
			d.substring(8, 10),
			d.substring(11, 13),
			d.substring(14, 16),
			d.substring(17, 19));
			d.setTime(d.getTime() + num * 1000);
		var year = d.getFullYear();
		var month = change(d.getMonth() + 1);
		var day = change(d.getDate());
		var hour = change(d.getHours());
		var minute = change(d.getMinutes());
		var second = change(d.getSeconds());
		function change(t) {
			if (t < 10) {
				return "0" + t;
			} else {
				return t;
			}
		}
		var time = time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute
		return time
	}
	$(document).mouseup(function (e) {
		var child = $('.sfsl_change');   // 设置目标区域  
		if (!child.is(e.target) && child.has(e.target).length === 0) {
			// 功能代码
			keyCode = ''
		}
	});
	//快捷键
	let alttimer = null;
	altKey = null
	$(document).keyup(function (event) { //监听键盘按下时的事件
		if (event.keyCode == 18) {
			alttimer = setTimeout(function (ev) {
				alttimer = null
				altKey = null
			}, 500);
			altKey = true
		}
		//按下不同的按键，对应的event.keyCode也不同
		if (event.altKey || altKey) {
			let foolfer = new footbtn_View()
			let header = new btnlist_View()
			switch (event.keyCode) {
				case 27:
					window.event.keyCode = 0;
					window.event.returnValue = false;
					break;
				case 84://提交
					foolfer.submit()
					break;
				case 83://保持
					foolfer.SaveYz()
					break;
				case 97:
				case 49://新增组
					foolfer.addgroup()
					break;
				case 98:
				case 50://新增项
					foolfer.add()
					break;
				case 99:
				case 51://删除组
					foolfer.delgroup()
					break;
				case 100:
				case 52://删除项
					foolfer.del()
					break;
				case 101:
				case 53://药品个人模板
					foolfer.ypgrmb()
					break;
				case 102:
				case 54://常用药
					goModelhtml('常用药模板', 0)
					break;
				case 103:
				case 55://治疗医嘱模板
					foolfer.zlyzmb()
					break;
				case 104:
				case 56://综合医嘱模板
					foolfer.zhyzmb()
					break;
				case 90://独立成组
					foolfer.bt_dlcz()
					break;
				case 105:
				case 57://停止
					header.stopYz()
					break;
				case 48:
				case 96://撤回
					header.disSubmitYz()
					break;
				case 80://全停
					header.stopCqYz()
					break;
				case 77://明日8点停止
					header.stopAt8Tomorrow()
					break;
			}
		} else {
			switch (event.keyCode) {
				case 13:
					if ($("#iziModel") && $("#iziModel").length > 0) {
						return
					}
					let foolfer = new footbtn_View()
					let id = document.activeElement.id
					let key = yzindex
					if (document.activeElement.dataset && document.activeElement.dataset.index) {
						key = document.activeElement.dataset.index
						yzindex = key
					}
					// let params=delindex(id)
					let div = id.split(key)[0]
					if (order.length == 0) {
						SetNextFocusObj(yzdata[key])
					}
					keyCode = ''
					function _liKeyup(div, key) {
						let title = div
						if (order.indexOf(title) < 0) {
							keyCode = div
							return
						}
						if (div == 'MC' && yzdata[key].MC == "" && (yzdata[key].YZLX == "cy" || yzdata[key].YZLX == "zcydy")) {
							keyCode = 'MC'
							$(`#${keyCode}${key}`).focus()
							return
						}
						for (let i = 0, j = order.length; i < j; i++) {
							if (i == j - 1) {
								yzindex = parseInt(yzindex) + 1
								keyCode = "MC"
								foolfer.add()
								return
							} else if (order[i] == title && order[i + 1] == 'LB' && yzdata[key].LB) {
								title = 'LB'
							} else if (order[i] == title) {
								let rtn = false
								if (yzdata[key].YZLX == "cy" || yzdata[key].YZLX == "zcydy") {
									let list = yzdata[key]
									let yc = list.YCYL || event.target.value || ''
									if (list.ZHTXT != "┐" && yc) {
										keyCode = "MC"
										foolfer.add()
										rtn = true
									} else if (title == 'YCYL') {
										keyCode = 'YCYL'
										rtn = true
									}
								}
								if (rtn) {
									return
								}
								let el = order[i + 1]

								keyCode = el
								if (title == "ZXFF" || title == "ZXPL" || title == "GYSJ" || title == "LB") {
									$(`#${id}`)[0].value = $(`#${id}`)[0].ov || yzdata[key][div] || ""
								}
								if ($(`#${el}${key}`)[0]) {
									$(`#${el}${key}`)[0].ov = $(`#${el}${key}`)[0].value
									if (el == "ZXFF" || el == "ZXPL" || el == "GYSJ" || el == "LB") {
										model_input(el, key)
										$(`#${el}${key}`)[0].value = ""
									}
									$(`#${el}${key}`).focus()
								}
								return
							}
						}
					}
					function getStyle(id, attr) {
						var element = document.getElementById(id);
						if (getComputedStyle) {//code  for chrome  Firefox
							var compStyle = document.defaultView.getComputedStyle(element, false);
							var tyle = compStyle[attr];
						} else {//code  for ie
							var comstyle = div.currentStyle;
							tyle = comstyle[attr];
						}
						return tyle;
					}
					if (div && div != "MC") {
						if (currentLine > 0) {
							let d = $(`#${id}_list`).find(".changeItem")
							if (d && d[0]) {
								let piv = div
								for (let i = 0, j = order.length; i < j; i++) {
									if (order[i] == piv) {
										let el = order[i + 1]
										if ((el == "LB" && yzdata[key].LB)) {
											piv = 'LB'
										} else {
											keyCode = el
										}
									}
								}
								yzdata[key][div] = d[0].title
							}
							return
						}
						let type = ""
						if (id.indexOf('ZXFF') >= 0 || id.indexOf('ZXPL') >= 0 || id.indexOf('GYSJ') >= 0 || id.indexOf('LB') >= 0) {
							type = getStyle(`${id}_list`, "display")
						}
						if (type == "block") {
							let d = $(`#${id}_list`).find(".li")
							if (d && d[1]) {
								let piv = div
								for (let i = 0, j = order.length; i < j; i++) {
									if (order[i] == piv) {
										let el = order[i + 1]
										// keyCode=el
										if ((el == "LB" && yzdata[key].LB)) {
											piv = 'LB'
										} else {
											keyCode = el
										}
									}
								}
								// if($(`#${div+key}`)[0].value){
								//     yzdata[key][div] = d[1].title
								//     xz_Same(div, key, d[1].title)
								//     // if(!yzdata[key][div]){
								//     //     yzdata[key][div]=d[1].title
								//     // }
								//     return
								// }
								yzdata[key]["keyCode"] = piv
							}
							return
						}
						$(".isModel").css("display", "none")
						_liKeyup(div, key)
					} else if (div == "MC") {

						let type = getStyle(`${id}_list`, "display")
						if (type == "none") {
							_liKeyup(div, key)
							return
						}
						if (currentLine > 0) {
							let d = $(`#${id}_list`).find(".changeItemMC")
							if (d && d[0]) {
								yzSelect(d[0])
								for (let i = 0, j = order.length; i < j; i++) {
									if (order[i] == div) {
										let el = order[i + 1]
										keyCode = el
									}
								}
							}
						} else {
							currentLine++
							sroll()
						}
					}
					break;
				case 37://左
					break;
				case 38://上
					currentLine--;
					if (currentLine < 0) {
						currentLine = 0
					}
					sroll(event.target.id)
					break;
				case 39://右
					break;
				case 40://下
					let title = $(`#${event.target.id}_list`)
					if (title) {
						currentLine++;
						let length = $(`#${event.target.id}_list`).find(".li").length
						if (yz_isModel && yz_isModel.indexOf("MC") >= 0) {
							length = $(`#${event.target.id}_list`).find("tr").length
						}
						if (length > 0 && currentLine >= length) {
							currentLine = length - 1
						}
						// let scrollTop=parseInt(currentLine/5)
						sroll(event.target.id)
						// [currentLine].focus()
					}
					break;
			}
		}
	});
	//子页面监听
	window.addEventListener('message', function (e) {
		if (e.data) {
			switch (e.data.page) {
				case 'kjypgl'://选择抗菌药物使用方法
					SetKjypsyly(e.data)
					break;
				case 'ypsp'://药品审批
					SetSpjg(e.data)
					break;
				case 'tskjywhzd'://抗菌药物限制
					setTskjywhzdid(e.data)
					break;
				case 'tztybab'://特治特药备案
					setTztybab(e.data)
					break;
				case 'tsywba'://特殊病备案
					setTsywba(e.data)
					break;
				case 'confirmPWD'://双签名
					setconfirmPWD(e.data)
					break;
				case 'zhmbsr'://综合模板
					setZhmbSr(e.data)
					break;
				case 'cwyytc':// 肠外营养套餐
					setCwyytc(e.data)
					break;
				case 'getICDDM':
					setCfypd(e.data)//处方诊断选择
					break;
				case "rykxx":
					setRykxx(e.data)//医嘱执行增加执行人员
					break;
			}
		}
	}, false);
	let url = window.location.href.split("?") || []
	let params = {}
	if (url[1]) {
		let text = url[1].split("&")
		for (let i of text) {
			let fd = i.split("=")
			params[fd[0]] = fd[1]

		}
	}
	params.yzlb = sortyzlb(params.as_yzlb) || {}
	WRT_config.url = params
	Main_init()
})

/**********初始化 ***/

function Main_init() {
	// WRT_e.api.yz_sz.getYwgm({
	//     params: { al_blid: WRT_config.url["as_blid"] },
	//     success(data) {
	//         WRT_config.ywgm = data.Result
	//     }
	// })
	WRT_e.api.yz_sz.getinit({
		params: { al_blid: WRT_config.url["as_blid"] },
		success(data) {
			if (data.Code == 1) {
				console.log('初始化数据', data.Result)
				WRT_config.yz_sz = data.Result
				WRT_config.yzbtgxh = sortList
				let swapColumn = WRT_config.yz_sz.yzbtgxh
				// WRT_config.yz_sz.hmzk="1" 给惠每质控加判断WRT_config.hmzkZW专网不走
				if (WRT_config.yz_sz.hmzk == "1" && (WRT_config.hmzkZW==undefined || !WRT_config.hmzkZW)) {
					// document.write('<script src="http://**************/cdss/jssdk?v=4.0&ak=CCD9D75192ACC4529888523BCD5AB9E2" type="text/javascript"></script>')
					// document.write(`<script src='${hmzkUrl}' type="text/javascript"></script>`)
					// addScript('http://**************/cdss/jssdk?v=4.0&ak=CCD9D75192ACC4529888523BCD5AB9E2')
					// addScript(hmzkUrl)
					setTimeout(() => {
						// 惠每质控
						HMCdssInit(WRT_config.url["as_blid"], WRT_config.yz_sz.yhid);
					}, 2000);
				}
				if (swapColumn) {
					WRT_config.yzbtgxh = JSON.parse(swapColumn)
				}
				if (((WRT_config.yz_sz.zkid == 49 || WRT_config.yz_sz.zkid == 2961) && WRT_config.url.as_yzlb == 'ls')) {
					if (WRT_config.yzbtgxh[12] && WRT_config.yzbtgxh[12].key == "时间针频率") {
						WRT_config.yzbtgxh[12].show = true
					} else if (WRT_config.yzbtgxh[12] && WRT_config.yzbtgxh[12].key == "时间针频率") {
						WRT_config.yzbtgxh.splice(2, 0, { key: "时间针频率", show: true })
					}
				}
				WRT_e.api.yz_sz.getSbXlzfYp({
					params: { al_zyid: data.Result.zyid },
					success(data) {
						WRT_config.SbXlzfYp = data.Result
					}
				})
				WRT_e.api.yz_sz.getZkYfdms({
					params: { al_zkid: data.Result.zkid, as_yqdm: data.Result.yqdm },
					success(data) {
						if (data.Result) {
							if(data.Result==null||data.Result.length!=3){
								WRT_e.ui.hint({msg:'未维护专科药房'})
							}
							data.Result.sort(function (a, b) {
								if (a.YZLB < b.YZLB) {
									return -1
								} else {
									return 1
								}
							})
							let arr = []
							data.Result.map(function (item) {
								arr.push(item.YFDM)
							})
							WRT_config.zkyf = arr.join("^")
						}
					}
				})
				WRT_e.api.yz_sz.getSstzd({
					params: { al_blid: WRT_config.url["as_blid"] },
					success(data) {
						WRT_config.sstzd = data.Result
						if (WRT_config.Province && WRT_config.sstzd && WRT_config.zxbq && WRT_config.yf && WRT_config.cyts && WRT_config.cyyf && WRT_config.pl && WRT_config.gysj) {
							app.init()
						}
					}
				})
				WRT_e.api.yz_sz.getYpyfpl({
					params: { as_lb: 6 },
					success(data) {
						WRT_config.yf = JSON.parse(data.Result)
						if (WRT_config.Province && WRT_config.sstzd && WRT_config.zxbq && WRT_config.yf && WRT_config.cyts && WRT_config.cyyf && WRT_config.pl && WRT_config.gysj) {
							app.init()
						}

					}
				})
				//草药特殊用法
				WRT_e.api.yz_sz.getYpyfpl({
					params: { as_lb: 4 },
					success(data) {
						WRT_config.cyts = JSON.parse(data.Result)
						if (WRT_config.Province && WRT_config.sstzd && WRT_config.zxbq && WRT_config.yf && WRT_config.cyts && WRT_config.cyyf && WRT_config.pl && WRT_config.gysj) {
							app.init()
						}
					}
				})
				//草药用法
				WRT_e.api.yz_sz.getYpyfpl({
					params: { as_lb: 5 },
					success(data) {
						WRT_config.cyyf = JSON.parse(data.Result)
						if (WRT_config.Province && WRT_config.sstzd && WRT_config.zxbq && WRT_config.yf && WRT_config.cyts && WRT_config.cyyf && WRT_config.pl && WRT_config.gysj) {
							app.init()
						}
					}
				})
				//血糖频率
				WRT_e.api.yz_sz.getYpyfpl({
					params: { as_lb: 'xtpl' },
					success(data) {
						WRT_config.xtpl = JSON.parse(data.Result)
						// if (WRT_config.sstzd && WRT_config.zxbq && WRT_config.yf && WRT_config.cyts && WRT_config.cyyf && WRT_config.pl && WRT_config.gysj) {
						//     app.init()
						// }
					}
				})
				//频率
				WRT_e.api.yz_sz.getYpyfpl({
					params: { as_lb: 2 },
					success(data) {
						WRT_config.pl = JSON.parse(data.Result)
						if (WRT_config.Province && WRT_config.sstzd && WRT_config.zxbq && WRT_config.yf && WRT_config.cyts && WRT_config.cyyf && WRT_config.pl && WRT_config.gysj) {
							app.init()
						}
						// temp=ypyfplHtml(WRT_config.pl,"pl","pllist")

					}
				})
				//用药时间
				WRT_e.api.yz_sz.getYpyfpl({
					params: { as_lb: 3 },
					success(data) {
						WRT_config.gysj = JSON.parse(data.Result)
						if (WRT_config.Province && WRT_config.sstzd && WRT_config.zxbq && WRT_config.yf && WRT_config.cyts && WRT_config.cyyf && WRT_config.pl && WRT_config.gysj) {
							app.init()
						}
						// temp=ypyfplHtml(WRT_config.gysj,"gysj","gysjlist")

					}
				})
				//执行病区
				WRT_e.api.yz_sz.getZxbqs({
					params: { al_bqid: WRT_config.yz_sz.bqid, al_yszkid: WRT_config.yz_sz.curyszkid },
					success(data) {
						if (data.Result) {
							WRT_config.zxbq = data.Result
							if (WRT_config.Province && WRT_config.sstzd && WRT_config.zxbq && WRT_config.yf && WRT_config.cyts && WRT_config.cyyf && WRT_config.pl && WRT_config.gysj) {
								app.init()
							}
						}
					}
				})
				//省
				WRT_e.api.yz_sz.getProvince({
					params: {},
					success(data) {
						if (data.Result) {
							WRT_config.Province = data.Result
							if (WRT_config.Province && WRT_config.sstzd && WRT_config.zxbq && WRT_config.yf && WRT_config.cyts && WRT_config.cyyf && WRT_config.pl && WRT_config.gysj) {
								app.init()
							}
						}
					}
				})
				// app.init()
			}
		}
	})
}
// 引用js
// function addScript(url){
// 	var script = document.createElement('script');
// 	script.setAttribute('type','text/javascript');
// 	script.setAttribute('src',url);
// 	document.getElementsByTagName('head')[0].appendChild(script);
// 	// document.write(`<script src='${url}'></script>`);
// }

/********************弹出界面回调方法************/
function closeCRRT(obj){
	if(obj){
		WRT_e.api.yz_sz.saveCrrtYz({
			params:{
				"bingLiID":WRT_config.url.as_blid,
  			"crrtMuBanMC":obj.crrtMuBanMC //取crrt处方单上的抗凝方案名称
			},
			success(msg){
				if(msg.data){
					yzdata[yzindex].crrtYz=JSON.stringify(msg.data)
					yzdata[yzindex].CRRTID= obj.CRRTID
				}
			}
		})
	}else{
		delZHTXT(yzindex)
		yzdata.splice(yzindex, 1)
		exportMBList()
		zhmbsrListDr()
	}
	$("#if_icuCRRT").iziModal("destroy")
}
var al_yhid=''
//选择医嘱执行人员
function onselectZXRY(){
	WRT_e.ui.model({
		id:'if_ZXRY',
		title: '执行人员',
		width: "645px",
		iframeHeight:'570px',
		iframeURL:`${WRT_config.server}/newsstzd/rykxx.aspx?as_version=3&av_tx=0&temp=${Math.random()}&as_openmode=ehr3`,
		iframe: true,
	})
}
////医嘱执行增加执行人员回调
function setRykxx(data){
	let json=data.returnObj.split('^')
	if(json){
		$("#yzzx_text")[0].value=	json[0]
		al_yhid=json[1]||WRT_config.yz_sz.yhid
		$("#if_ZXRY").iziModal("destroy")
	}
}
//确定医嘱执行增加执行人员
function onzxyzadd(){
	let val = getYzDl()
	WRT_e.api.yz_sz.Lsyzzx({
		params: {
			as_yzlst: val.ids,
			al_zyid: WRT_config.yz_sz.zyid,
			al_zkid: WRT_config.yz_sz.zkid,
			al_bqid: WRT_config.yz_sz.bqid,
			al_blid: JSON.parse(WRT_config.url.as_blid),
			as_zxsj: $("#datetimebox1")[0].value || "",
			al_yhid: al_yhid||WRT_config.yz_sz.yhid //新增参数
		},
		success(data) {
			if (data.Code == 1) {
				getBrYz()
				refresh_init()
				WRT_e.ui.hint({
					type: "success",
					msg: '医嘱执行成功'
				})
				$("#if_yzzx").iziModal("destroy")
			} else {
				WRT_e.ui.message({
					title: '提示信息',
					content: data.CodeMsg,
					onOk() { },
				})
			}
		}
	})
}
//处方诊断选择
function setCfypd(data) {
	let arr = []
	if (data.value) {
		arr = data.value.split("^") || []
	}
	$("#if_selectcfy").iziModal('destroy')
	let list = cfy_Lists[cfy_index]
	if (list) {
		cfy_Lists[cfy_index].MC = arr[0]
		cfy_Lists[cfy_index].ICD = arr[2]
		cfy_Lists[cfy_index].JBID = arr[4]
		$(`#cfy_${cfy_index}`)[0].value = arr[0]
	}
}
//肠外营养套餐
function setCwyytc() {
	$("#Cwyytc").iziModal('destroy')
	getBrYz()
}
//综合模板
function setZhmbSr(e) {
	ZhmbSrdata = e.data || []
	if (ZhmbSrdata.length > 0) {
		ZhmbSrindex = 0
		zhmbsrDr(0)
	}
}

//药品审批
function SetSpjg(data, type) {
	let index = yzindex, name = 'ypsp'
	if (type) {
		name = "if_ypsp"
	}
	if (data) {
		if (data.sfzf == '9') {
			$(`#${name}`).iziModal('destroy')
			delZHTXT(index)
			yzdata.splice(index, 1)
			return
		}
		yzdata[index].SFZF = data.sfzf
		yzdata[index].ZF = yzdata[index].SFZF //同个字段
		yzdata[index].splb = data.splb
		yzdata[index].XSP = yzdata[index].splb //同个字段
		yzdata[index].SQSL = data.spsl
		yzdata[index].xdfw = data.xdfw
		if(WRT_config.url.as_yzlb=='cq'){
			sumxspjssj(index)
		}else if(WRT_config.url.as_yzlb=='ls'){
			yzdata[index].JSSJ_TXT = gettime('')
		}
		// yzdata[index].ZF = data.type
		// yzdata[index].spjg = data
		$(`#${name}`).iziModal('destroy')
		istygzdm(index, yzdata[index].YZLX, yzdata[index].obj)
	} else {
		delZHTXT(index)
		yzdata.splice(index, 1)
		$(`#${name}`).iziModal('destroy')
		return
	}
}
//抗菌药物限制
function setTskjywhzdid(data) {
	let index = yzindex
	if (data && data.hzdid) {
		yzdata[yzindex].kjywhzdid = data.hzdid
	}
	if (!data.hzdid || data.hzdid == 0) {
		$("#kjyplx").iziModal('destroy')
		WRT_e.ui.message({
			title: '信息窗口',
			content: '紧急情况下使用特殊类抗生素,需填写会诊单,否则不允许下达特殊抗生素医嘱!',
			onOk() {
				delZHTXT(index)
				yzdata.splice(index, 1)
			}
		})
		return;
	} else {
		iskjypgl(yzdata[yzindex].obj)
	}
	$("#kjyplx").iziModal('destroy')
}
//选择抗菌药物使用方法
function SetKjypsyly(data) {
	let rtn = data.returnObj
	let list = yzdata[yzindex]
	let index = yzindex
	$("#kjypgl").iziModal('destroy')
	if (rtn == undefined) {
		delZHTXT(index)
		yzdata.splice(index, 1)
		return;
	} else if (rtn.SYFF == "0") {
		delZHTXT(index)
		yzdata.splice(index, 1)
		return;
	} else if (rtn.SYFF == "2" && rtn.WSW == "2") {
		delZHTXT(index)
		yzdata.splice(index, 1)
		return;
	} else {
		yzdata[index].kjypsyly = rtn
		if (list.YZLX == "cq") {//yz_sz.aspx?as_yzlb=XXX
			let curDate = new Date(yzdata[index].KSSJ_TXT)
			if (rtn.QKLB == "1" || rtn.YFFL == "2" || rtn.YFFL == "7") {
				yzdata[index].KSSJ_TXT = gettime(curDate)
				let stringDate = gettime(new Date(curDate.getTime() + 24 * 60 * 60 * 1000))
				yzdata[index].JSSJ_TXT = stringDate
			}
		}
	}
	istzyp_isNeedBa(list.obj)
}
//特治特药备案
function setTztybab(data) {
	let index = yzindex
	$("#NeedBa").iziModal('destroy')
	if (data && (!data.ztbz || data.ztbz == 0)) {
		delZHTXT(index)
		yzdata.splice(index, 1)
		// delZHTXT(parseInt(yzindex)-1)
		if (ZhmbSrdata && ZhmbSrdata.length > 0 && ZhmbSrindex < ZhmbSrdata.length) {
			ZhmbSrindex++
			zhmbsrDr(ZhmbSrindex)
			return
		}
	} else {
		yzdata[yzindex].tztybab = data
		issfzf(yzdata[yzindex].obj)
	}
}
//特殊病备案
function setTsywba(data) {
	$("#cwjsdmsx").iziModal('destroy')
	if (data) {
		yzdata[yzindex].tsywba = data
	}
	if (ZhmbSrdata && ZhmbSrdata.length > 0 && ZhmbSrindex < ZhmbSrdata.length) {
		ZhmbSrindex++
		zhmbsrDr(ZhmbSrindex)
		return
	}
	exportMBList()
}
//治疗\饮食模板回调
function SetZlysmb(yzStr) {
	if (WRT_config.url.as_yzlb == 'jcyy') {
		WRT_e.ui.message({
			title: '提示信息',
			content: '必须从检查用药列表中选择药品！',
			onOk() {
			},
			onCancel() { }
		})
		return;
	}
	if (!yzStr) {
		return;
	}
	var yzLst = yzStr.split('^');
	for (var i = 0, cnt = yzLst.length; i < cnt; i++) {
		if (yzStr[i] == "") continue;
		var yzDetail = yzLst[i].split('~');
		var foolfer = new footbtn_View()
		foolfer.addgroup()
		var currentRow = yzdata.length - 1;
		let obj = {}
		obj.YZLX = WRT_config.url.as_yzlb
		// yzdata[currentRow]=obj
		if (WRT_config.url.as_yzlb == 'cq') {
			obj.CXTS = yzDetail[4]
			// if (yzDetail[10] == 'y') {//饮食
			// 	WRT_e.api.yz_sz.GetYsFjxx({
			// 		params: {
			// 			as_ysdm: yzDetail[1],
			// 			al_blid: WRT_config.url["as_blid"]
			// 		},
			// 		success(data) {
			// 			if (data.Code == 1) {
			// 				if (data.Result.zyysyz) {
			// 					WRT_e.ui.message({
			// 						title: '提示信息',
			// 						content: data.Result.zyysyz,
			// 						onOk() {
			// 						}
			// 					})
			// 				}
			// 				if (data.Result.zykfy) {
			// 					WRT_e.ui.message({
			// 						title: '提示信息',
			// 						content: data.Result.zykfy,
			// 						onOk() {
			// 						}
			// 					})
			// 				}
			// 			}
			// 		}
			// 	})
			// }
		} else {
			if (yzDetail[10] == 'y') {
				WRT_e.ui.message({
					title: '提示信息',
					content: '临时医嘱里不能开饮食医嘱！',
					onOk() {
					}
				})
				continue;
			}
			obj.CXTS = ''
		}
		if (yzDetail[10] == "z") {
			obj.LB = '1'
		} else if (yzDetail[10] == "t") {
			obj.LB = '4'
		}
		else if (yzDetail[10] == "y") {
			obj.LB = '2'
		}
		// obj.ZH = ""
		obj.GG = yzDetail[9]
		obj.YPID = yzDetail[10] + yzDetail[1]
		obj.MC = yzDetail[2]
		if (yzDetail[10] == "z" && (yzDetail[1] == "6755" || yzDetail[1] == "25155")) {//6755转科，25155转科医嘱
			WRT_e.api.yz_sz.checkCyl({
				params: { al_blid: WRT_config.url.as_blid },
				success(data) {
					if (data.Code == 1) {
						if (data.Result == 1) {
							WRT_e.ui.message({
								title: '提示信息',
								content: '临时医嘱里不能开饮食医嘱！',
								onOk() {
									delZHTXT(currentRow)
									yzdata.splice(currentRow, 1)
								}
							})
						}
					}
				}
			})
		}
		obj.SFSL = yzDetail[3]
		obj.ZXFF = yzDetail[6]
		obj.ZXPL = yzDetail[5]
		obj.TSYF = yzDetail[7]
		obj.BQID = `${obj.BQID ? obj.BQID : WRT_config.yz_sz.bqid}`
		// obj.KSSJ_TXT = gettime('', true)
		// obj.KSSJ_TXT = getKSSJ_TXT() 不确定是否重新调用
		obj.KSSJ_TXT = WRT_config.defaultKSSJ_TXT
		for (let i in yz_obj) {
			if (!obj[i] || obj[i] == 'null') {
				obj[i] = ''
			}
		}
		isypcyl(obj, currentRow)
		yzdata[currentRow] = obj
		let obj1 = {
			lx: `${obj.LB==1?'治':obj.LB==2?'食':obj.LB==4?'嘱':'药'}`,
			mc: obj.MC,
			ypid: obj.YPID,
			gg: obj.GG,
			sj: obj.SJ,
			bz: obj.ZTBZ || '',
			dataop: obj.yp_dataop||''
		}
		yzindex=currentRow
		yzlogic("MC" + yzindex, obj1, yzindex)
	}
	sortData()
	// if (ZhmbSrdata && ZhmbSrdata.length > 0 && ZhmbSrindex >= 0) {
	// 	ZhmbSrindex++
	// 	zhmbsrDr(ZhmbSrindex)
	// }
	// $("#if_zlyzmb").iziModal('destroy')
}

function SetYp(dataop, z, type) {
	if (WRT_config.url.as_yzlb == 'jcyy' && type != 'jcyy') {//jx
		WRT_e.ui.message({
			title: '提示信息',
			content: '必须从检查用药列表中选择药品！',
			onOk() {
			}
		})
		return;
	}
	let yp = {}
	let dataoplist = ['ypid', 'ycyl', 'jldw', 'ff', 'pl', 'gysj', 'tsyf', 'mc', 'gg', 'yfdm', 'yzlb', 'jx', 'cfyp', 'rmyp', 'dw', 'sl', 'cqcy', 'yplb', 'kzjb', 'gllx', 'gjcg', 'canprescribe_zy', 'canprescribe_cydy', '3000', 'fzqypid', 'sj']
	var j_data = dataop.split(',');
	j_data.map(function (item, index) {
		if (item) {
			yp[dataoplist[index]] = item
		} else {
			yp[dataoplist[index]] = ''
		}
	})
	if (WRT_config.url.as_yzlb == 'cq' && (j_data[11] == "Ypj" || j_data[11] == "Mjkl")) {//jx
		WRT_e.ui.message({
			title: '提示信息',
			content: '必须从检查用药列表中选择药品！',
			onOk() {
			}
		})
		return;
	}
	let foot = new footbtn_View()
	if (z == 'z') {
		foot.addgroup()
	} else if (z === '0') {
		foot.add()
	}
	yzindex = yzdata.length - 1
	if (yzdata.length == 0) {
		yzindex = 0
	}
	let list = yzdata[yzindex]
	// list.ZH = ""
	list.BQID = `${list.BQID ? list.BQID : WRT_config.yz_sz.bqid}`
	list.MC = j_data[7]
	list.JX = j_data[11]
	list.CFYP = j_data[12]
	list.YCYL = j_data[1]
	list.RMYP = j_data[13]
	list.GLLX = j_data[19]
	list.YPID = j_data[0]
	list.KSSJ_TXT = WRT_config.defaultKSSJ_TXT
	// list.YZLX=j_data[10]||WRT_config.url.as_yzlb
	list.JLDW = j_data[2]
	list.GG = j_data[8]
	list.ZXFF = j_data[3]
	list.TSYF = j_data[6]
	list.ZXPL = j_data[4]
	list.GYSJ = j_data[5]
	list.YPGG = j_data[8]
	list.SJ = j_data[25]
	setTime(yzindex)
	list.YFDM = j_data[9]
	list.DW = j_data[14]
	list.yp_dataop = yp
	list.YPLB = j_data[17]
	list.LB = 3
	// list.YZLX = isyzlb(list)
	if (yzdata[yzindex].YZLX == '') {
		list.YZLX = isyzlb(list)
	} else if (list.YZLB == 3) {
		list.YZLX = yzdata[yzindex].YZLX || ""
		if (!(yzdata[yzindex].YZLX != 'cy' && yzdata[yzindex].YZLX != 'zcydy')) {
			list.YZLX = 'cy'
		}
	} else if (list.YZLB != 3) {
		list.YZLX = yzdata[yzindex].YZLX || ""
		if (yzdata[yzindex].YZLX == 'cq') {
			list.YZLX = 'cq'
		} else if (yzdata[yzindex].YZLX != 'ls' && yzdata[yzindex].YZLX != 'cydy') {
			list.YZLX = 'ls'
		}
	}
	if (WRT_config.url.as_yzlb == 'jcyy') {
		list.SFSL = j_data[15]
	}
	for (let i in yz_obj) {
		if (!list[i] && list[i] != '') {
			list[i] = ""
		}
	}
	var j_alert_cfyz = CheckCfyz(j_data[0], j_data[7]);//ypid,mc
	if (j_alert_cfyz != "") {
		WRT_e.ui.message({
			title: '提示信息',
			content: j_alert_cfyz,
			onOk() {
			}
		})
		if (j_data[10] == "3") {//yzlb
			footbtn_View.del()//中药在新增时不可重复用药
			return;
		}
	}

	if (list.YPLB == 0) {
		let obj = {
			lx: "药",
			mc: list.MC,
			ypid: list.YPID,
			gg: list.YPGG,
			sj: list.SJ,
			bz: list.ZTBZ || '',
			dataop: list.yp_dataop
		}
		yzlogic("MC" + yzindex, obj, yzindex)
	}
	sortData()
}

//双签名
function setconfirmPWD(rtn) {
	if (rtn) {
		WRT_config.Sqm = rtn
	}
}
/********************公用方法*******************/
function getlczyhtml(arr){
	let temp=`
	<table id="tb_yplistdetail" border="0" width="600px" cellspacing="0" cellpadding="0">
		<tbody>
			<tr>
					<td width="80px">选择</td>
					<td width="80px">姓名</td>
					<td width="100px">科室名称</td>
					<td width="250px">病区入院时间</td>
			</tr>
			${_.map(arr, (obj, index) =>
			`<tr>
				<td width="80px">
					<input type="button" class="e_btn" value="选择" onclick="onselectlsyy(${index})">
				</td>
				<td width="80px">${obj.xingMing}</td>
				<td width="100px">${obj.keShiMC}</td>
				<td width="250px">${obj.bingQuRYRQ}</td>
		</tr>`
		).join('')}
		</tbody>
	</table>`
	return temp
}
//返回历次住院
function golczy(){
	let temp=getlczyhtml(WRT_config.lczy_lists)
	$("#lczyyy_lists").html(temp)
}
//选择历次住院
function onselectlsyy(index){
	let list=WRT_config.lczy_lists[index]
	if(list){
		let stime=list.bingQuRYRQ
		let etime=list.bingQuCYRQ
		WRT_e.api.yz_sz.getLsyyMb({
			params: { al_blid: list.bingLiID, as_lsyylb: 'lscq', as_kssj: stime, as_jssj: etime },
			success(data) {
				if (data.Code == 1) {
					let arr = data.Result
					yp_mb = arr
					let temp = getMBhtml(arr)
					let html = `
						<div>
							<tr>
								<td>
									<label for="rbl_lsyy_1"onclick="lczyybtn(${index},'lscq')"><input id="rbl_lsyy_1" type="radio" name="rbl_lsyy" value="lscq" checked="checked">历史用药（长期）</label>
								</td>
								<td>
									<label for="rbl_lsyy_2" onclick="lczyybtn(${index},'lsls')"><input id="rbl_lsyy_2" type="radio" name="rbl_lsyy" value="lsls">历史用药（临时）</label>
								</td>
								<td> 
									<label for="rbl_lsyy_0" onclick="lczyybtn(${index},'cydy')"><input id="rbl_lsyy_0" type="radio" name="rbl_lsyy" value="cydy">出院带药</label>
								</td>
							</tr>
						</div>
						<div style="height: 35px;">
						时间:<input type="date" id="txt_kssj" type="text" value="${getdate(stime)}" style="margin: 0 5px;border: 1px solid #000;">-
						<input type="date" id="txt_jssj" type="text" value="${getdate(etime)}" style="margin: 0 5px;border: 1px solid #000;"">     
						<button class="e_btn" onclick="searchlczyyy(${index})">查询</button><button class="e_btn" onclick="golczy()">返回</button>
						</div>
						<div id="yplistls_table">
						${temp}
						</div>
						`
						$("#lczyyy_lists").html(html)
				}
			}
		})
	}
}
//查询历史用药
function searchlczyyy(index) {
	let list=WRT_config.lczy_lists[index]
	if(list){
		let name = getRadioButtonCheckedValue('rbl_lsyy')
		let start = $("#txt_kssj")[0].value || getNowFormatDate()
		let end = $("#txt_jssj")[0].value || getNowFormatDate()
		WRT_e.api.yz_sz.getLsyyMb({
			params: { al_blid: list.bingLiID, as_lsyylb: name, as_kssj: start, as_jssj: end },
			success(data) {
				if (data.Code == 1) {
					let arr = data.Result
					yp_mb = arr
					let temp = getMBhtml(arr)
					$("#yplistls_table").html(temp)
				}
			}
		})
	}
}

//历史用药切换
function lczyybtn(index, name) {
	let list=WRT_config.lczy_lists[index]
	if(list){
		let start = $("#txt_kssj")[0].value || getNowFormatDate()
		let end = $("#txt_jssj")[0].value || getNowFormatDate()
		WRT_e.api.yz_sz.getLsyyMb({
			params: { al_blid:list.bingLiID, as_lsyylb: name, as_kssj: start, as_jssj: end },
			success(data) {
				if (data.Code == 1) {
					let arr = data.Result
					yp_mb = arr
					let temp = getMBhtml(arr)
					$("#yplistls_table").html(temp)
				}
			}
		})
	}
}
//选择GCP药物试验
function dbYwbbh(index){
	let list=WRT_config.ywbbh_Lists[index]
	let { el, obj, key, iskyType }= ypfjxx_modal
	if(list){
		obj.ywbh=list.yaoWuBH
		ypway(el, obj, key, false, iskyType)
		$("#id_ywbbh").iziModal("destroy")
	}
}
//
function issstzd() {
	let list = yzdata[yzindex] || {}
	if (WRT_config.ypfjxx.sstzd) {
		let ss_arr = JSON.parse(WRT_config.ypfjxx.sstzd)
		if (ss_arr.length == 1) {
			list.TZDID = ss_arr[0].TZDID
			if (list.ZHTXT) {
				let XH = list.XH;
				let fd = yzdata.filter(item => item.XH == list.XH)
				if (list.YZLX == 'cy' || list.YZLX == 'zcydy') {
					XH = list.XH.split('-')[0]
					fd = yzdata.filter(item => (list.YZLX == 'cy' || list.YZLX == 'zcydy') && item.XH.split('-')[0] == list.XH)
				}
				if (fd && fd.length > 0) {
					fd.map(function (key) {
						if (!key.TZDID) {
							yzdata.map(function (ev) {
								if (ev.XH == key.XH) {
									ev.TZDID = list.TZDID
								}
							})
						}
					})
				}
			}
		}
		if (ss_arr.length >= 1) {
			ss_arr.map(function (key) {
				let fd = WRT_config.sstzd.filter(item => item.TZDID == key.TZDID)
				if (fd && fd.length == 0) {
					WRT_e.api.yz_sz.getSstzd({
						params: { al_blid: WRT_config.url["as_blid"] },
						success(data) {
							WRT_config.sstzd = data.Result
						}
					})
				}
			})
		}
	} else {
		if (list.ZHTXT) {
			let XH = list.XH;
			let fd = yzdata.filter(item => item.XH == list.XH)
			if (list.YZLX == 'cy' || list.YZLX == 'zcydy') {
				XH = list.XH.split('-')[0]
				fd = yzdata.filter(item => (list.YZLX == 'cy' || list.YZLX == 'zcydy') && item.XH.split('-')[0] == list.XH)
			}
			if (fd && fd.length > 0) {
				fd.map(function (key) {
					if (key.TZDID) {
						yzdata.map(function (ev) {
							ev.TZDID = key.TZDID
						})
					}
					return;
				})
			}
		}
	}
}
//药品审批html
function ypsp_html(ypfjxx,listIsXSP) {
	$("#if_LiShi").iziModal("destroy")
	let text = ''
	let zxfw = JSON.parse(ypfjxx.xzfw)
	let type = false
	let arr = []
	WRT_config.XDFW_list=null
	if(zxfw){
		WRT_config.XDFW_list = zxfw.map(function (item) {
			return item.XDFW
		})
		// zxfw=JSON.parse('[{"XDFW":"(国版)限肝功能衰竭"},{"XDFW":"(国版)无法使用甘草酸口服制剂的患者"},{"XDFW":"(省版)限抢救"},{"XDFW":"(省版)限肝病"}]')
		// 外购药品  listIsXSP.YPLB == 1
		if (listIsXSP && listIsXSP.YPLB == 1) {
			
		} else {
			arr = [...zxfw, { XDFW: '均不符合，需自费，请告知患者' }]
		}
	} else {
		// 	// 外购药品  listIsXSP.YPLB == 1
		if (listIsXSP && listIsXSP.YPLB == 1) {
			
		} else {
			arr = [{ XDFW: '均不符合，需自费，请告知患者' }]
		}
	}
	
	if (ypfjxx.xsp == '0') {
		text = ''
		type = false
	} else if (ypfjxx.xsp == '1') {
		type = true
		text = '该项目为需【医保窗口】审批项目。'
	} else if (ypfjxx.xsp == '2') {
		type = true
		text = '该项目为需【药房】和【医保窗口】审批项目。'
	} else if (ypfjxx.xsp == '3') {
		type = true
		text = '该项目为需【药房】审批项目。'
	} else if (ypfjxx.xsp) {
		type = true
		text = '该项目为需审批项目。'
	}
	ybxz_list = arr
	// <textarea name="tb_sbxzfw" rows="2" cols="20" id="tb_sbxzfw" style="height:130px;width:369px;">${ypfjxx.xzfw}</textarea>
	let temp = `
    <div id="XDFW_table">
        <span style="font-weight:bold;">药品名称：${ypfjxx.ypmc}</span><button class="e_btn" onclick="onlsSelect()" style="margin-left: 10px;">历史限定范围查询</button><br>
        ${arr.length>0?`<span id="Label1" style="font-weight:bold;">医保使用限制范围如下，请根据疾病诊断准确选择：</span><br>
        <ul id="xdfw_lists" style="height:130px;width:369px;border: 1px solid;overflow: auto;padding: 3px 5px;">
        ${_.map(arr, (item, index) => `
        <li><input type="checkbox" name="xzfw_check" onclick="editCheckboxTrue(this)" value="${item.XDFW}"><span ${index == arr.length - 1 ? 'style="color:red"' : ''}>${item.XDFW}</sapn></li>
        `).join('')}
        </ul>`:''}
        <br>
        <span id="Label2" style="font-weight:bold;">病人诊断</span>&nbsp;<br>
        <textarea name="tb_brzd" rows="2" cols="20" id="tb_brzd" disabled="true" style="height:74px;width:367px;">${WRT_config.yz_sz.zd}</textarea><br>
        ${ypfjxx.xsp != 0&&arr.length>0 ? `<span id="lb_tsxx" style="display:inline-block;height:49px;width:372px;">${text}</span><br>` : ''}
        <span style="display:inline-block;height:49px;width:372px;color:red">注：请根据患者病情如实勾选并在病历中体现！</span><br>
        <table style="width: 370px" cellpadding="0" cellspacing="0">
            <tbody>
                <tr id="tr_spxm">
                    ${ypfjxx.xsp != 0 && ypfjxx.xsp ? `<td style="width: 180px; height: 24px;padding: 3px 0;"><span id="Label3">申请审批数量</span>
                        <input name="tb_spsl" type="text" id="tb_spsl" style="width:59px;border: 1px solid #736b6b;">
                    </td>`: ''}
                </tr>
                <tr align="center">
                    <td style="width: 180px; height: 15px;" align="center">
                        <button class="e_btn" value="确定" onclick="bt_xdfwclick(1);return false;" id="bt_qd" style="width:160px;">${type ? '确认提交审批' : '确定'}</button>
                    </td>
                    <td style="width: 180px; height: 15px;" align="center">
                        <button class="e_btn" value="取消" onclick="SetSpjg('',true);return false;" id="bt_qx" style="width:160px;">取消</button>
                    </td>
                </tr>
            </tbody>
        </table>
        &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
        <br>
      </div>
    `
	return temp
}

//审批确定
function bt_xdfwclick(lx) {
	let check = $('#XDFW_table input[type=checkbox]:checked')
	let xdfw = ''
	if (check[0]) {
		xdfw = check[0].value
	} else if (check.length == 0 && (WRT_config.XDFW_list && WRT_config.XDFW_list.length>0) || ybxz_list.length>0) {
		alert("社保限制范围必须勾选");
		return
	}
	if(WRT_config.ypfjxx.shangCiXdfw&&WRT_config.ypfjxx.shangCiXdfw!=xdfw&&xdfw){
		WRT_e.ui.message({
			title: '提示信息',
			content: '当前选择的限制范围与历史选择不一致!',
			onOk() {
				bt_click(lx,xdfw)
			},
			onCancel() { }
		})
	}else{
		bt_click(lx,xdfw)
	}

}
function bt_click(lx,xdfw) {
	var splb = WRT_config.ypfjxx.xsp;
	var obj = new Object();
	obj.SFZF = "";
	obj.SPLB = splb;
	obj.xdfw = xdfw
	if (lx == '1') {
		if (splb == "0") {
			obj.SFZF = "0";
			if (obj.xdfw == '均不符合，需自费，请告知患者') {
				obj.SFZF = "1";
				isZFType=true
			}
		}
		else {
			if (ybxz_list.length>0) {
				if (splb != "3") {
					if (WRT_config.yz_sz.jslx == "00")
						obj.SFZF = "0";
					else {
						if ((WRT_config.XDFW_list && WRT_config.XDFW_list.length>0) && WRT_config.XDFW_list.indexOf(obj.xdfw) >= 0)
							obj.SFZF = "0";
						if (obj.xdfw == '均不符合，需自费，请告知患者')
							obj.SFZF = "1";
					}
					if (obj.SFZF == "") {
						alert("请选择 是否自费还是公费!");
						return;
					}
				}
				else {
					obj.SFZF = 0;
				}
			}
			if(ybxz_list.length==0){
				obj.SFZF = 0;
			}
			// 审批数量
			var spsl = $("#tb_spsl")[0].value;
			if (spsl == "") {
				alert("请输入审批数量");
				return;
			}
			if (isNaN(spsl)) {
				alert("审批数量请输入数字!")
				return;
			}
			if (parseFloat(spsl) <= 0) {
				alert("审批数量不能为零或负数!");
				return;
			}
			if (obj.xdfw.indexOf('需自费')==-1) {
				if (parseFloat(spsl) > parseFloat(WRT_config.ypfjxx.wgypsqsl)) {
					alert("申请数量最大只能填30!");
					return;
				}
			}
			obj.SPSL = spsl;
		}
	}
	if (obj.SPSL == "" || obj.SPSL == undefined){
		obj.SPSL = 0;
	}
	SetSpjg({ sfzf: obj.SFZF, spsl: obj.SPSL, splb: obj.SPLB, xdfw: obj.xdfw }, true)
}
//历史限定范围查询
function onlsSelect(){
	let list = yzdata[yzindex]
	if(list){
		let num_t=subtractDays(90)
		let time=getNowFormatDate(num_t)
		WRT_e.api.yz_sz.getLiShiXdfw({
			params:{
				yaoPinID:list.YPID,
				waiGouYP:'',
				kaiShiSJ:time,
				jieShuSJ:getNowFormatDate(),
				// kaiShiSJ:'2024-01-01',
				// jieShuSJ:'2025-01-01',
				bingAnHao:WRT_config.yz_sz.empi
			},
			success(data){
				if(data.hasError==0){
					let arr = data.data
					WRT_config.LiShiTemp=data.data
					// let temp=getLiShiTemp(arr)
					let html=`
					<div style="height: 35px;">
						时间:<input type="date" id="lishi_kssj" type="text" value="${time}" style="margin: 0 5px;border: 1px solid #000;">-
						<input type="date" id="lishi_jssj" type="text" value="${getNowFormatDate()}" style="margin: 0 5px;border: 1px solid #000;"">     
						<button class="e_btn" onclick="searchlishi()">查询</button>
            </div>
					<div id="LiShiXDFW_table">
					<table id="lishilistdetail" border="0" width="600px" cellspacing="0" cellpadding="0">
						<thead>
							<tr>
								<th width="120px">医嘱时间</th>
								<th width="250px">限定范围</th>
							</tr>
						</thead>
						<tbody id="lishi_xdfw">
							${_.map(arr, (obj, index) =>
							`<tr>
									<td width="120px">${obj.shouCiLRSJ}</td>
									<td width="250px">${obj.xianZhiFW}</td>
								</tr>`
								).join('')}
						</tbody>
					</table>
					</div>
					`
					WRT_e.ui.model({
						id: "if_LiShi",
						title: "历史限定范围查询",
						width: "650px",
						content: html,
						iframe: false,
					})
				}else if(data.hasError==-1){
					WRT_e.ui.hint({msg:data.errorMessage})
				}
			}
		})
	}
}
//历史查询
function searchlishi(){
	let list = yzdata[yzindex]
	if(list){
		let kssj=$("#lishi_kssj")[0].value
		let jssj=$("#lishi_jssj")[0].value
		let num_t=subtractDays(90)
		let time=getNowFormatDate(num_t)
		WRT_e.api.yz_sz.getLiShiXdfw({
			params:{
				yaoPinID:list.YPID,
				waiGouYP:'',
				kaiShiSJ:kssj||time,
				jieShuSJ:jssj||getNowFormatDate(),
				bingAnHao:WRT_config.yz_sz.empi
			},
			success(data){
				if(data.hasError==0){
					let arr = data.data
					WRT_config.LiShiTemp=data.data
					// let temp=getLiShiTemp(arr)
					let html=`
					${_.map(arr, (obj, index) =>
						`<tr>
								<td width="120px">${obj.shouCiLRSJ}</td>
								<td width="250px">${obj.xianZhiFW}</td>
							</tr>`
							).join('')}
					`
					// html+='<tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr><tr> <td width="120px">2024-05-27 15:28:03</td> <td width="250px">符合重症患者，且白蛋白低于30g/L</td></tr>'
					$("#lishi_xdfw").html(html)
				}else if(data.hasError==-1){
					WRT_e.ui.hint({msg:data.errorMessage})
				}
			}
		})
	}
}


//修改复选框，实现单选框效果
function editCheckboxTrue(that,type) {
	let text='XDFW_table'
	if(type){
		text='LiShiXDFW_table'
	}
	var answer_checkbox_list = $(`#${text} input[type=checkbox]`);
	var answer_checkbox_lenth = answer_checkbox_list.length;
	//循环得到此次
	for (var i = 0; i < answer_checkbox_lenth; i++) {
		answer_checkbox_list[i].checked = false;
	}
	that.checked = true;
}

//
function close_Mzyt() {
	$('#if_Mzyt').iziModal('destroy')
	delZHTXT(yzindex)
	yzdata.splice(yzindex, 1)
	exportMBList()
	zhmbsrListDr()
}
//麻醉药选择用途
function save_Mzyt() {
	let list = $("input[name='Mzyt_radio']:checked").val()
	if (list) {
		yzdata[yzindex].mzyt = list
		$("#if_Mzyt").iziModal('destroy')
		isxsp(yzindex, yzdata[yzindex].YZLX, yzdata[yzindex].obj)
	} else {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `麻醉药品用途不能为空！`,
			onOk() {
			}
		})
	}
}

//保存时处方诊断数据处理
function dealCfy(obj) {
	let arr = obj.YPZD || []
	if (obj.cfy_Lists) {
		let list = JSON.parse(obj.cfy_Lists)
		arr = []
		list.map(function (item, index) {
			arr.push({
				"yzid": 0,
				"xh": parseInt(index) + 1,
				"blid": WRT_config.url["as_blid"],
				"jbid": item.JBID,
				"icd": item.ICD,
				"mc": item.MC,
				"xgsj": null,
				"xgyhid": null
			})
		})
	}
	return arr
}

//打开处方药诊断
function open_cfy(target) {
	// <button class="e_btn" onclick="addcfy()">新增</button>
	let html = `

        <ul style="padding: 5px 0;">
        ${_.map(target, (obj, index) =>
		`
            <li style="padding: 2px 0;">
            <button onclick="upcfy(${index})"><img src="./images/Frame2.png" style="width: 20px;" /></button>
            <button onclick="downcfy(${index})"><img src="./images/Frame1.png" style="width: 20px;" /></button>
            <img src="./images/Frame4.png" onclick="addcfy(${index})" style="width: 20px;" />
            <input id="cfy_${index}" class="cfy_text"  onclick="onselectcfy(${index})" value="${obj.MC}" readonly>
            <img src="./images/Frame3.png" onclick="delcfy(${index})" style="width: 20px;" />
            </li>
            `
	).join('')}
        </ul>
        <button class="e_btn" onclick="savecfy()">保存</button><button class="e_btn" onclick="cancelcfy()">关闭</button>
    
    `
	return html
}

//选择处方诊断
function onselectcfy(index) {
	cfy_index = index
	WRT_e.ui.model({
		id: `if_selectcfy`,
		title: '选择处方诊断',
		width: "745px",
		iframeURL: `${WRT_config.server}/basy/getICDDM.aspx?as_openmode=ehr3&tempid=` + Math.random(),
		iframeHeight: '600px',
		iframe: true,
	})
}
//新增处方诊断
function addcfy(index) {
	if (index == undefined) {
		cfy_Lists.push({ MC: '' })
	} else {
		cfy_Lists.splice(parseInt(index) + 1, 0, { MC: '' })
	}
	let html = open_cfy(cfy_Lists)
	$("#cfy_table").html(html)
}
//删除处方诊断
function delcfy(index) {
	cfy_Lists.splice(index, 1)
	if (cfy_Lists.length == 0) {
		cfy_Lists = [{ MC: '' }]
	}
	let html = open_cfy(cfy_Lists)
	$("#cfy_table").html(html)
}
//上移处方诊断
function upcfy(index) {
	if (index > 0) {
		let list = cfy_Lists[parseInt(index) - 1]
		cfy_Lists[parseInt(index) - 1] = cfy_Lists[index]
		cfy_Lists[index] = list
		let html = open_cfy(cfy_Lists)
		$("#cfy_table").html(html)
	}
}
//下移处方诊断
function downcfy(index) {
	if (index < cfy_Lists.length - 1) {
		let list = cfy_Lists[parseInt(index) + 1]
		cfy_Lists[parseInt(index) + 1] = cfy_Lists[index]
		cfy_Lists[index] = list
		let html = open_cfy(cfy_Lists)
		$("#cfy_table").html(html)
	}
}
//保存
function savecfy() {
	if (cfy_Lists.length > 0) {
		let arr = []
		cfy_Lists.map(function (item) {
			if (item.MC) {
				arr.push(item)
			}
		})
		if (arr.length == 0) {
			WRT_e.ui.message({
				title: '信息窗口',
				content: `处方诊断不能为空！`,
				onOk() {
				}
			})
			return;
		}
		yzdata[yzindex].cfy_Lists = JSON.stringify(arr)
		// yzdata[yzindex].cfzd_type=true
		keyCode = 'YCYL'
		iscMzyt(yzindex, yzdata[yzindex].YZLX, yzdata[yzindex].obj)
		$("#if_cfy").iziModal('destroy')
	}
}
//取消
function cancelcfy() {
	keyCode = 'YCYL'
	$('#if_cfy').iziModal('destroy')
	delZHTXT(yzindex)
	yzdata.splice(yzindex, 1)
	exportMBList()
	zhmbsrListDr()
}

//特殊药品会诊单id转化
function StringID(id) {
	if (typeof id != "string") {
		id = JSON.stringify(id)
	}
	return id
}

//中成药辩证取消
function CancelZybzxx() {
	let index = yzindex
	let obj = yzdata[yzindex] || {}
	$("#if_zybzxx").iziModal('destroy')
	WRT_e.ui.message({
		title: '提示信息',
		content: `中成药【${obj.MC || ''}】必须选择中医辩证信息！`,
		onOk() {
			delZHTXT(index)
			yzdata.splice(index, 1)
			zhmbsrListDr()
			exportMBList()
		}
	})

}
//中成药辩证成功
function saveZybzxx() {
	let obj = yzdata[yzindex] || {}
	let zdxx = [], zzxx = [], zdlist = [], zzlist = []
	var chkBoxes = $('.zd_list').find('input:checked');
	var chkBoxes1 = $('.zz_list').find('input:checked');
	$(chkBoxes).each(function () {
		zdxx.push(this.value)
		zdlist.push(this.dataset["zh"])
	});
	$(chkBoxes1).each(function () {
		zzxx.push(this.value)
		zzlist.push(this.dataset["zh"])
	});
	let rtn = true, title = ''
	zdlist.map(function (item) {
		if (zzlist.indexOf(item) < 0) {
			rtn = false
			title = item
		}
	})
	zzlist.map(function (item) {
		if (zdlist.indexOf(item) < 0) {
			rtn = false
			title = item
		}
	})
	if (rtn) {
		yzdata[yzindex].ZDXX = zdxx.join(',')
		yzdata[yzindex].ZZXX = zzxx.join(',')
		$("#if_zybzxx").iziModal('destroy')
		kzlywqx(yzindex, yzdata[yzindex].YZLX, yzdata[yzindex])
	} else {
		WRT_e.ui.message({
			title: '提示信息',
			content: `中成药第${title}组必须选择诊断与症状同时选中！`,
			onOk() {
			}
		})
	}
}
//撤销执行
function submityzcxzx() {
	let title = $("#tb_cxzxbz")[0].value || ""
	let val = getYzDl()
	if (title == "") {
		$("#yzcxzx_title").html("理由不能为空")
		$("#yzcxzx_title").css("display", "block")
		return
	} else
		if (title.length > 50) {
			$("#yzcxzx_title").html("理由长度不能超过50")
			$("#yzcxzx_title").css("display", "block")
			return
		}
	WRT_e.api.yz_sz.yzzxcx({
		params: {
			as_yzlst: val.ids,
			al_zyid: WRT_config.yz_sz.zyid,
			al_blid: JSON.parse(WRT_config.url.as_blid),
			as_cxly: escape(title)
		},
		success(data) {
			if (data.Code == 1) {
				getBrYz()
				refresh_init()
				WRT_e.ui.hint({
					type: "success",
					msg: '医嘱撤销执行成功'
				})
				$('#if_yzcxzx').iziModal('destroy')
			} else {
				WRT_e.ui.message({
					title: '提示信息',
					content: data.CodeMsg,
					onOk() { },
				})
			}
		}
	})
}
//时间控件失去焦点
function mouseout(ev, name, index) {
	if (ev.value && yzdata[index][name] != ev.value) {
		keyCode = ''
		if (order.length == 0) {
			SetNextFocusObj(yzdata[index])
		}
		for (let i = 0, j = order.length; i < j; i++) {
			if (order[i] == name) {
				keyCode = order[i + 1]
			}
		}
		yzdata[index][name] = ev.value || ""
		xz_Same(name, index, ev.value)
	}
}
//时间控件实例
function changeFunc(ev, name, index) {
	var pickedFunc = function (e) {
		yzdata[index][name] = e.el.value || ""
		xz_Same(name, index, e.el.value || "")
		
	}
	var clearedFunc = function () {
		yzdata[index][name] = ""
		keyCode = name
	}
	WdatePicker({ el: ev, onpicked: pickedFunc, oncleared: clearedFunc, dateFmt: 'yyyy-MM-dd HH:mm', readOnly: false })
}
//医嘱输入框收放
function imgtab(type) {
	if (type == "open") {
		imgshow = true
		$("#box_pad").html(`<img id="img_menu" src="./images/arrow_open.png" onclick="imgtab('close')" style="transform: translate(670px, 10px)">`)
		$("#yz_pad").css("display", "none")
		$("#yztitle_pad").css("display", "none")
		$("#newyz_pad").css("max-height", "540px")
	} else {
		imgshow = false
		$("#box_pad").html(`<img id="img_menu" src="./images/arrow_close.png" onclick="imgtab('open')" style="transform: translate(670px, 10px)">`)
		$("#yz_pad").css("display", "block")
		$("#yztitle_pad").css("display", "block")
		let height = window.screen.height;
		let width = window.screen.width;
		let num = "330px"
		if (height == '1024') {

			num = '265px'
		}
		$("#newyz_pad").css("max-height", num)
	}
}
//综合模板
function zhmbsrListDr() {
	if (ZhmbSrdata.length > 0 && ZhmbSrindex <= ZhmbSrdata.length - 1) {
		ZhmbSrindex++;
		zhmbsrDr(ZhmbSrindex)
		return
	}
	// exportMBList()
}
//综合模板队列
var zh, j_bz = "";//仅供药品使用
function zhmbsrDr(i) {
	if (i > ZhmbSrdata.length - 1) {
		ZhmbSrdata = []
		ZhmbSrindex = 0
		return
	}
	if (ZhmbSrdata[i].LB == "1" || ZhmbSrdata[i].LB == "4" || ZhmbSrdata[i].LB == "2") {//1治疗 4嘱托 2饮食 拼成特定字符串供SetZlysmb函数使用
		var ls_yzStr = "";
		ls_yzStr += ZhmbSrdata[i].MBID + "~";//mbid
		ls_yzStr += ZhmbSrdata[i].XMID.substr(1) + "~";//yzmlid
		ls_yzStr += ZhmbSrdata[i].MC + "~";//yzmlmc
		ls_yzStr += ZhmbSrdata[i].SFSL + "~";//数量
		ls_yzStr += ZhmbSrdata[i].CXTS + "~";//持续天数
		ls_yzStr += ZhmbSrdata[i].ZXPL + "~";//执行频率
		ls_yzStr += ZhmbSrdata[i].ZXFF + "~"; //执行方法
		ls_yzStr += (ZhmbSrdata[i].TSSM || ZhmbSrdata[i].TSYF) + "~"; //特殊用法
		ls_yzStr += "" + "~"; //开始时间
		if (ZhmbSrdata[i].LB == "2") //饮食
			ls_yzStr += ZhmbSrdata[i].DJ + "~";//规格、单价
		else
			ls_yzStr += ZhmbSrdata[i].GG + "~";//规格、单价
		ls_yzStr += ZhmbSrdata[i].XMID.substring(0, 1);//z治疗，t嘱托，y饮食
		SetZlysmb(ls_yzStr);
	}
	else if (ZhmbSrdata[i].LB == "3") {//3药品     下面的方法模仿SetYpZu函数
		var j_data = ZhmbSrdata[i].DATAOP.split(',');
		if (WRT_config.url.as_yzlb == 'cq' && (j_data[11] == "Ypj" || j_data[11] == "Mjkl")) {//jx
			WRT_e.ui.message({
				title: '提示信息',
				content: "长期医嘱里不能开草药和颗粒！",
				onOk() {
				}
			})
			return
		}
		if (zh != ZhmbSrdata[i].ZH)
			SetYp(ZhmbSrdata[i].DATAOP, "z", "zhmb");//新增组
		else
			SetYp(ZhmbSrdata[i].DATAOP, "0", "zhmb");//新增项
		zh = ZhmbSrdata[i].ZH;
	}

}

//计算处理下拉框位置
function inputClent(event, ev, index) {
	if (!event[0]) {
		return
	}
	if (!imgshow) {
		let top = event[0].offsetTop - $("#newyz_pad")[0].scrollTop
		let left = event[0].offsetLeft - $("#container-yz_sz")[0].scrollLeft
		if (top == 330) {
			top = top + 50
			$(`#${ev}_list`).css("top", top)
		} else if (top > 330) {
			top = 380
			// top=top-(top-330)+30
			$(`#${ev}_list`).css("top", top)
		} else if (top + 265 >= 330) {
			top = top + 100
			// top=top+90
			$(`#${ev}_list`).css("top", top)
		} else if ($("#newyz_pad")[0].scrollTop != 0) {
			top = top + 370
			//top=top+380
			$(`#${ev}_list`).css("top", top)
		}
		$(`#${ev}_list`).css("left", left)
	} else {
		let top = event[0].offsetTop - $("#newyz_pad")[0].scrollTop
		let left = event[0].offsetLeft - $("#container-yz_sz")[0].scrollLeft
		if (top == 540) {
			top = top + 50
			$(`#${ev}_list`).css("top", top)
		} else if (top > 540) {
			top = 380
			// top=top-(top-330)+30
			$(`#${ev}_list`).css("top", top)
		} else if (top + 265 >= 540) {
			// top=top+90
			top = top - 115
			$(`#${ev}_list`).css("top", top)
		} else if ($("#newyz_pad")[0].scrollTop != 0) {
			top = top + 168
			$(`#${ev}_list`).css("top", top)
		}
		$(`#${ev}_list`).css("left", left)
	}
}
//滚动条下拉
function sroll(id) {
	if (yz_isModel && yz_isModel.indexOf("MC") >= 0) {
		$(`#${yz_isModel}_list`).find(".changeItemMC").removeClass("changeItemMC")
		$(`#${yz_isModel}_list`).animate({ scrollTop: currentLine * 33 }, 100);
		if ($(`#${yz_isModel}_list`).find("tr")[currentLine])
			$(`#${yz_isModel}_list`).find("tr")[currentLine].className += ' changeItemMC'
	} else {
		$(`#${id}_list`).find(".changeItem").removeClass("changeItem")
		$(`#${id}_list`).animate({ scrollTop: currentLine * 17 }, 100);
		$(`#${id}_list`).find(".li")[currentLine].className += ' changeItem'
	}
}
//选中医嘱
function yzSelect(ev) {
	WRT_config.ypfjxx = "";
	WRT_config.ypsyff = ""
	let el = yz_isModel
	if (!el) {
		yz_isModel = "MC" + yzindex
	}
	// let key = yz_isModel.substr(yz_isModel.length - 1, 1)
	// let list = yzdata[key]
	let json = ev.children
	if (json) {
		let obj = {
			lx: json[0].innerHTML,
			mc: json[1].innerText,
			ypid: json[2].id,
			// gjm: json[3].innerHTML,
			gg: json[4].innerHTML,
			sj: json[5].innerHTML,
			bz: json[6].innerHTML,
			dataop: json[7].title,
			gcp:ev.dataset?ev.dataset.gcp:''
		}
		yzlogic(el, obj, yzindex, true)
	}
}
//开医嘱逻辑
function yzlogic(el, obj, key, iskyType) {
	$("#mclist").hide()
	WRT_config.ZiFjxx=''
	WRT_config.ypfjxx = ""
	WRT_config.ypsyff = ""
	let list = yzdata[key]
	if (obj.dataop && obj.dataop.ypid) {
		yp_dataop = obj.dataop
	} else {
		yp_dataop = getdataop(obj.dataop)
	}
	if(yp_dataop.gg){
		obj.gg = yp_dataop.gg || ""
	}
	list.yp_dataop=yp_dataop
	if (yzdata[key].cfy_Lists) {
		yzdata[key].cfy_Lists = ''
	}
	// if(obj.gjm){
	// 	yzdata[key].GJM=obj.gjm
	// }
	if (obj.lx.indexOf("药") >= 0) {
		obj.lb = 3
		if (WRT_config.url.as_lclj == 1 && WRT_config.url.as_yzlb == "cq") {
			WRT_e.ui.message({
				title: '信息窗口',
				content: `病人已进入临床路径，不能在长期医嘱里开药品医嘱，只能提交！`,
				onOk() {
					delZHTXT(key)
					yzdata.splice(key, 1)
					zhmbsrListDr()
					exportMBList()
				}
			})
			return
		}
		let mess = ismzjs(yp_dataop)
		if (mess) {
			WRT_e.ui.message({
				title: '信息窗口',
				content: mess,
				onOk() {
				},
			})
		}

		if (yp_dataop.cfyp == 1 || yp_dataop.rmyp == 1) {

		} else {
			let fd = WRT_config.BrYz.filter((item, i) => item.XMID == obj.ypid && item.COLOR != 'red' && item.YZZTMC != '已执行')
			let td = yzdata.filter((item, i) => (item.YPID == obj.ypid || item.XMID == obj.ypid) && i != key)
			if (td.length > 0) {
				if (yp_dataop.yzlb == 3) {
					WRT_e.ui.message({
						title: '信息窗口',
						content: `中药${obj.mc}重复`,
						closeOnEscape: false,
						onOk() {
							delZHTXT(key)
							yzdata.splice(key, 1)
							exportMBList()
							// yzindex=parseInt(key)-1
						}
					})
					return
				}
			}
			if (fd.length > 0 || td.length > 0) {
				if (yp_dataop.yzlb != 3) {
					WRT_e.ui.message({
						title: '信息窗口',
						content: `列表里${obj.mc}重复`,
						closeOnEscape: false,
						onOk() {
							// exportMBList()
							// yzdata.splice(key, 1)
						}
					})
				}
			}
		}
		//药物过敏
		// let alsert_ywgm = CheckYwgm(obj.ypid)
		if (WRT_config.ywgm) {
			WRT_e.ui.message({
				title: '信息窗口',
				content: WRT_config.ywgm,
				onOk() {
					// delZHTXT(key)
					// yzdata.splice(key, 1)
					// // key=parseInt(key)-1
					// if (ZhmbSrdata && ZhmbSrdata.length > 0 && ZhmbSrindex < ZhmbSrdata.length) {
					//     ZhmbSrindex++
					//     zhmbsrDr(ZhmbSrindex)
					//     return
					// }
					// exportMBList()
				},
			})
			// return
		}
		//中成药分餐
		var lb_zcyfc = zcyfc(key, list.YZLX, obj.lb, obj.mc, yp_dataop.yzlb, yp_dataop.cqcy);
		if (lb_zcyfc == false) {
			delZHTXT(key)
			yzdata.splice(key, 1)
			exportMBList()
			return;
		}

		//封装前药品id
		let is_ypid = obj.ypid
		if (yp_dataop.fzqypid != 0) { //如果是封装药，则取封装前的。
			is_ypid = yp_dataop.fzqypid;
		}

		if (obj.ypid == '96906' || obj.ypid == '99077' || obj.ypid == '4761') {
			WRT_e.ui.message({
				title: '信息窗口',
				content: `为遏制细菌耐药，请谨慎选择该种抗菌药物，使用前请明确细菌耐药谱！`,
				onOk() {
				},
			})
		}
		if (!(yp_dataop.yplb == 0 || yp_dataop.yplb == 1 || !yp_dataop.yplb)) {
			WRT_config.ypfjxx = {}
			WRT_config.ypsyff = {}
			ypway(el, obj, key, true, iskyType)
		} else {
			ypfjxx_modal=null
			WRT_e.api.yz_sz.getYpFjxx({
				params: { 
					al_ypid: obj.ypid, 
					as_yfdm: yp_dataop.yfdm || "", 
					as_jslx: WRT_config.yz_sz.jslx, 
					al_zyid: WRT_config.yz_sz.zyid, 
					as_yzlx: list.YZLX, 
					al_zkid: WRT_config.yz_sz.zkid,
					gcp:obj.gcp||''
				},
				success(data) {
					if (data.Code == 1) {
						let array = JSON.parse(data.Result)
						WRT_config.ypfjxx = array[0] || {}
						if (WRT_config.ypfjxx.gmywtx) {
							WRT_e.ui.message({
								title: '信息窗口',
								content: WRT_config.ypfjxx.gmywtx,
								onOk() {
								},
							})
						}
						if (WRT_config.ypfjxx.gwyp == 1) {
							WRT_e.ui.message({
								title: '信息窗口',
								content: `${obj.mc}是高警示药品`,
								onOk() {
								}
							})
						}
						// WRT_config.ypfjxx.ywbh=[{  "yaoPinID": null,  "yaoPinMC": '测试',  "yaoWuBH": '1235151'}]
						// yp_dataop.gcp=1
						if (WRT_config.ypfjxx && (WRT_config.ypsyff || !iskyType)) {
							IsGcp(el, obj, key, false, iskyType)
						}
						
						// 新增了2个回参  wgypsqsl(最大申请数量)、wgypxltx
						// wgypxltx字段不为空则弹窗提醒，选【是】继续开医嘱；选【否】不开。
						if (WRT_config.ypfjxx.wgypxltx && WRT_config.ypfjxx.wgypxltx!="") {
							WRT_e.ui.message({
								title: '信息窗口',
								content: WRT_config.ypfjxx.wgypxltx,
								okText: '是',
								cancelText: '否',
								onOk() {},
								onCancel(){
									$('#wgyp').iziModal('destroy')
									// 删除刚刚添加的那条医嘱
									let index = key
									// if (index < 0 || !index || yzindex > yzdata.length - 1) {
									// 	index = yzdata.length - 1
									// }
									if (yzdata[index]) {
										index = parseInt(index)
										if (yzdata[index].YZID) {
											as_delidlist.push(yzdata[index].YZID)
										}
										if (yzdata[index].ZHTXT) {
											if (yzdata[index].ZHTXT == '┐') {
												if (yzdata[index + 1] && yzdata[index + 1].ZHTXT == '|') {
													yzdata[index + 1].ZHTXT = '┐'
												} else if (yzdata[index + 1] && yzdata[index + 1].ZHTXT == '┘') {
													yzdata[index + 1].ZHTXT = ''
												}
											} else if (yzdata[index].ZHTXT == '┘') {
												if (yzdata[index - 1] && yzdata[index - 1].ZHTXT == '|') {
													yzdata[index - 1].ZHTXT = '┘'
												} else if (yzdata[index - 1] && yzdata[index - 1].ZHTXT == '┐') {
													yzdata[index - 1].ZHTXT = ''
												}
												// yzdata[index-1].ZHTXT='┘'
											}
										}
										yzdata.splice(index, 1)
										yzindex = index - 1
									}
									sortData()
								}
							})
						}
						// (不确定其它情况触发会不会有影响)
						// if ((obj.dataop.yplb!=undefined && obj.dataop.yplb == 1) && WRT_config.ypfjxx.xsp!='1') {
						// 	SetSpjg({ sfzf: '1', spsl: 0, splb: 0, xdfw: '' },true)
						// }
					} else {
						WRT_e.ui.message({
							title: '信息窗口',
							content: data.CodeMsg,
							onOk() {
								delZHTXT(key)
								yzdata.splice(key, 1)
							},
						})
						// WRT_e.ui.hint({type:'error',msg:data.CodeMsg})
					}
				}
			})
			if (iskyType) {
				WRT_e.api.yz_sz.getYpJbsyff({
					params: { al_ypid: is_ypid },
					success(data) {
						if (data.Code == 1) {
							WRT_config.ypsyff = data.Result
							if (WRT_config.ypfjxx && (WRT_config.ypsyff || !iskyType)) {
								IsGcp(el, obj, key, false, iskyType)
							}
						} else {
							WRT_e.ui.hint({ type: 'error', msg: data.CodeMsg })
						}
					}
				})
			} else {
				// WRT_config.ypsyff={}
				if (WRT_config.ypfjxx) {
					IsGcp(el, obj, key, false, iskyType)
				}
			}
		}

	} else if (obj.lx.indexOf("治") >= 0) {
		obj.lb = 1
		let mlid = obj.ypid.slice(1, obj.ypid.length)
		WRT_e.api.yz_sz.GetZlFjxx({
			params: {
				al_mlid: mlid,
				al_blid: WRT_config.url.as_blid,
				as_yzlx: list.YZLX,
			},
			success(data) {
				if (data.Code == 1) {
					WRT_config.ZiFjxx=data.Result
					// obj.crrt=data.Result.crrt
					if (data.Result && data.Result.error) {
						WRT_e.ui.message({
							title: '提示',
							content: `无法开具治疗医嘱${obj.mc}，${data.Result.error}`
						})
						delZHTXT(key)
						yzdata.splice(key, 1)
						exportMBList()
						zhmbsrListDr()
						return;
					} else if(data.Result&&data.Result.jxyfjj){
						WRT_e.ui.message({
							title: '提示',
							content: data.Result.jxyfjj,
							onOk(){
								zl_check()
							},
							onCancel(){
								delZHTXT(key)
								yzdata.splice(key, 1)
								exportMBList()
								zhmbsrListDr()
							}
						})
					}else{
						zl_check()
					}
					if (data.Result && data.Result.kfxltx) {
						WRT_e.ui.message({
							title: '提示',
							content: data.Result.kfxltx
						})
					}
					if(data.Result && data.Result.icu_zk_nutric){
						WRT_e.ui.message({
							title: '提示',
							content: data.Result.icu_zk_nutric
						})
					}
					if (data.Result && data.Result.icu_zk_apacheii) {
						WRT_e.ui.message({
							title: '提示',
							content: data.Result.icu_zk_apacheii
						})
					}
				}
			}
		})
		function zl_check(ev) {
			if ((obj.ypid.indexOf("6755") >= 0 || obj.ypid.indexOf("25155") >= 0)) {
				WRT_e.api.yz_sz.checkCyl({
					params: { al_blid: WRT_config.url.as_blid },
					success(data) {
						if (data.Code == 1) {
							if (data.Result == 1) {
								WRT_e.ui.hint({ type: 'error', msg: '开转科医嘱,请删除出院录后才能开医嘱' })
							} else {
								iszlxsp(el, obj, key, iskyType)
							}
						} else {
							WRT_e.ui.hint({ type: 'error', msg: data.CodeMsg })
						}
					}
				})
				// if ((obj.ypid.indexOf("25155") >= 0) && WRT_config.yz_sz.zkid == "42") {
				// 	WRT_e.ui.hint({ msg: 'ICU开具转科医嘱时请及时完成ApacheII评估表，如已完成请忽略！' })
				// }
			}else {
				iszlxsp(el, obj, key, iskyType)
			}
		}
		// obj.gg=`${obj.gg?obj.gg:yp_dataop.gg||""}`
	} else if (obj.lx.indexOf("食") >= 0) {
		obj.lb = 1
		// obj.gg=`${obj.gg?obj.gg:yp_dataop.gg||""}`
		let ysdm = obj.ypid.slice(1, obj.ypid.length)
		WRT_e.api.yz_sz.GetYsFjxx({
			params: {
				as_ysdm: ysdm,
				al_blid: WRT_config.url["as_blid"]
			},
			success(data) {
				if (data.Code == 1) {
					if (data.Result.zyysyz) {
						WRT_e.ui.message({
							title: '提示信息',
							content: data.Result.zyysyz,
							onOk() {
							}
						})

					} else {
						if (data.Result.zykfy) {
							WRT_e.ui.message({
								title: '提示信息',
								content: data.Result.zykfy,
								onOk() {
								}
							})
						}
						selectMC(el, obj, key, iskyType,true)
					}
				}
			}
		})

		// if (WRT_config.yz_sz.lstys != 1) {
		//     show = false
		//     WRT_e.ui.hint({
		//         type: 'error',
		//         msg: "该病人不允许开饮食"
		//     })
		// } else {
		//     selectMC(el, obj, key,iskyType)
		//     // WRT_config.yz_sz.lstys_copy = WRT_config.yz_sz.lstys
		//     // WRT_config.yz_sz.lstys = 0
		// }
	}
}
//精神药品判断
function ismzjs(yp_dataop) {
	let message = ""
	if (!yp_dataop) {
		return null
	}
	if (yp_dataop.mzyp == 1 && yp_dataop.xzyp == 1) {
		message = "此药为麻醉药品或一类精神药品！,进口药、贵重药、用量较大的药品，控制使用。建议选择同类低价药品!"
		return message
	}
	if (yp_dataop.mzyp == 1) {
		message = "此药为麻醉药品或一类精神药品！"
	}
	if (yp_dataop.xzyp == 1) {
		message = "此药为进口药、贵重药、用量较大的药品，控制使用。建议选择同类低价药品!"
	}
	return message
}

//获取医嘱类型
function sortyzlb(pramas) {
	let rtn = {}
	switch (pramas) {
		case 'cq':
			rtn.name = '长期医嘱'
			rtn.value = 1
			break;
		case 'ls':
			rtn.name == '临时医嘱'
			rtn.value = 0
			break;
		default:
			rtn.name == '检查用药'
			rtn.value = 'jcyy'
			break;
	}
	return rtn;
}
//判断查询医嘱时医嘱种类
function gettype(index) {
	// let id = ev.target.id;
	// let params=delindex(id)
	let key = index
	let yzlb = WRT_config.url.as_yzlb
	let list = yzdata[key]
	if (list) {
		yzlb = yzdata[key].YZLX
	}
	let type = ''
	if (WRT_config.yz_sz.zkid == 49 && WRT_config.url.as_yzlb == "cq") {
		type = '0011';
		return type
	}
	switch (list.LB) {
		case 1:
		case '1':
			type = '0010'
			break;
		case 2://饮食
		case '2':
			if (yzlb == 'cq') {
				type = '0001'
			} else {
				type = '0000'
			}
			break;
		case 3:
		case '3':
			if (yzlb == 'cq') {
				type = '1000'
			} else if (yzlb == 'cy' || yzlb == 'zcydy') {
				type = '0100'
			} else if (yzlb == 'ls' || yzlb == 'cydy') {
				type = '1000'
			}
			break;
		case 4:
		case '4':
			if (yzlb == 'cq') {
				type = '1000'
			} else if (yzlb == 'cy' || yzlb == 'zcydy') {
				type = '0100'
			} else if (yzlb == 'ls' || yzlb == 'cydy') {
				type = '1000'
			}

			break;
		default:
			if (yzlb == 'cq') {
				type = '1011'
			} else {
				type = '1010'
			}
	}
	return type
}
//判断医嘱类    
function isyzlb(object) {
	let yzlb = ''
	if (WRT_config.url.as_yzlb == 'cq') {
		yzlb = 'cq'
	} else {//药品
		if (object.LB == "3") {
			if (object.YZLB == "3") {
				if (object.CXTS == 0) {
					yzlb = 'cy'
				} else {
					yzlb = 'zcydy'
				}
			} else {//西药、中成药
				if (object.CXTS == 0) {
					yzlb = 'ls'
				} else {
					yzlb = 'cydy'
				}
			}
		} else {
			yzlb = 'ls'
		}
	}
	return yzlb
}
//获取已提交医嘱数据
function getBrYz() {
	let syyz = "", xstzyz = ""
	if ($("#syyz").is(":checked")) {
		syyz = "0"
	} else {
		syyz = "1"
	}
	if ($("#xstzyz").is(":checked")) {
		xstzyz = "1"
	} else {
		xstzyz = "0"
	}
	//获取已提交医嘱
	let params = {
		al_blid: parseInt(WRT_config.url.as_blid),
		as_yzlb: JSON.stringify(WRT_config.url.yzlb.value),
		as_zy: syyz,
		as_tz: xstzyz,
		as_lb: $("#as_lb option:selected").val()||'',
		as_fylx: $("#as_fylx option:selected").val()||'',
	}
	WRT_e.api.yz_sz.getBrYz({
		params: params,
		success(data) {
			if (data.Code == 1) {
				WRT_config.BrYz = data.Result;
				var yzsz = new yzsz_View();
				yzsz.$el = $("#data_list");
				yzsz.init({
					data: XHData(data.Result)
				}).render();

			}
		}
	})
}
//不良反应上报
function OnBlfy(index) {
	let list = WRT_config.BrYz[index]
	let ls_url = '';
	let iWidth = 1000;
	let iHeight = 700;
	let iTop = (window.screen.availHeight - 30 - iHeight) / 2;
	let iLeft = (window.screen.availWidth - 10 - iWidth) / 2;
	let url = WRT_config.yz_sz.sbblsj.split("?")
	WRT_e.ui.message({
		title: '提示',
		content: '是否上报该药品的不良反应？',
		onOk() {
			ls_url = url[0] + `?uid=${WRT_config.yz_sz.yhid}&patient_id=${WRT_config.url.as_blid}&patient_type=ZY&yz_id=${list.YZID}`
			window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
		},
		onCancel() {

		}
	})
}
//左键闭环点击
function OnLeft(key) {
    let list = WRT_config.BrYz[key]
	if (list) {
		var ls_url = '';
		var iWidth = 800;
		var iHeight = 700;
		var iTop = (window.screen.availHeight - 30 - iHeight) / 2;
		var iLeft = (window.screen.availWidth - 10 - iWidth) / 2;
		if(list.BHLX=='XTYZ'){
			WRT_e.api.yz_sz.getXtbhdz({
				params:{
					yzid:list.YZID
				},
				success(res){
				    res=JSON.parse(res)
					if(res&&res.body){
						WRT_config.Xtbhdz_lists=res.body||[]
						let temp = `
							<table id="tb_Xtbhdz" border="0" cellspacing="0" cellpadding="0">
								<tbody>
									<tr>
										<td width="80px">血透唯一号</td>
										<td width="140px">预约时间</td>
										<td width="80px">医嘱ID</td>
										<td width="120px">操作</td>
									</tr>
								${_.map(WRT_config.Xtbhdz_lists, (obj, index) =>
								    `<tr>
										<td width="80px">${obj.dialyserecordid}</td>
										<td width="140px">${obj.setuptime}</td>
										<td width="80px">${obj.zlyzid}</td>
										<td width="120px"><button class="e_btn" onclick="dbXtbhdz(${index},${key})">查看</button></td>
									</tr>`
								).join('')}
								</tbody>
							</table>`
							WRT_e.ui.model({
								id: "id_Xtbhdz",
								title: "血透闭环列表",
								width: "450px",
								content: temp,
								iframe: false,
							})
					}
				}
			})
			return
		}
		if (list.YZLX == 1 || list.YZLX == -1) {
			ls_url = WRT_config.yz_sz.ypbhdz + `zhuYuanID=${WRT_config.yz_sz.zyid}&yiZhuZH=${list.ZH}&pageNum=1&pageSize=10`
			window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
		} else if (list.YZLX == -1) {
			ls_url = WRT_config.yz_sz.bhjddz + '?bhdm=' + WRT_config.yz_sz.zyypbhdm + '&ywzj=' + list.YZID + "&av_templocaltime=" + Math.random();
			window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
		} else if (list.YZLX == 0) {
			ls_url = WRT_config.yz_sz.bhjddz + '?bhdm=' + WRT_config.yz_sz.ysbhdm + '&ywzj=' + list.YZID + "&av_templocaltime=" + Math.random();
			window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
		} else if (list.YZLX == 2) {
			if(list.BHLX=="KFZL"){
				let url=WRT_config.yz_sz.kfzlbhdz+`?zhuYuanID=${WRT_config.yz_sz.zyid}&yiZhuID=${list.YZID}`
				window.open(url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
				return
			}
			ls_url = WRT_config.yz_sz.bhjddz + '?bhdm=' + WRT_config.yz_sz.zlbhdm + '&ywzj=' + list.YZID + "&av_templocaltime=" + Math.random();
			window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
		}
	}
	return false
}
//
function dbXtbhdz(index,key){
    let list = WRT_config.Xtbhdz_lists[index]
    let list1 = WRT_config.BrYz[key]
	if(list){
		var ls_url = '';
		var iWidth = 800;
		var iHeight = 700;
		var iTop = (window.screen.availHeight - 30 - iHeight) / 2;
		var iLeft = (window.screen.availWidth - 10 - iWidth) / 2;
		ls_url = WRT_config.yz_sz.bhjddz + '?bhdm=' + list1.BHDM + '&ywzj=' + list.dialyserecordid + "&av_templocaltime=" + Math.random();
		window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
	}
}
//左键闭环点击 oncontextmenu="OnRight()"
function OnRight(index) {
	let list = WRT_config.BrYz[index]
	if (list && list.MC) {
		var ls_url = '';
		var iWidth = 1000;
		var iHeight = 700;
		var iTop = (window.screen.availHeight - 30 - iHeight) / 2;
		var iLeft = (window.screen.availWidth - 10 - iWidth) / 2;;
		let url = WRT_config.yz_sz.hlyySms.split("@ypid@")
		let ypid = list.YPID || list.XMID
		if (list.YZLX == 1 || list.YZLX == -1) {
			ls_url = url[0] + ypid + url[1]
			window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
		}
	}

}
//左键闭环点击 未提交
function OnRightdata(index) {
	let list = yzdata[index]
	if (list && list.MC) {
		var ls_url = '';
		var iWidth = 1000;
		var iHeight = 700;
		var iTop = (window.screen.availHeight - 30 - iHeight) / 2;
		var iLeft = (window.screen.availWidth - 10 - iWidth) / 2;;
		let url = WRT_config.yz_sz.hlyySms.split("@ypid@")
		let ypid = list.YPID || list.XMID
		if (list.LB == '3') {
			ls_url = url[0] + ypid + url[1]
			window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
		}
	}

}

//获取名称
function getName(obj, type, lb, list) {
	let name = '', arr = []
	if (type == 'ZXFF') {
		if (lb == 'cy' || lb == 'zcydy') {
			arr = WRT_config.cyyf
		} else if (list.MC && list.MC.indexOf('葡萄糖测定') >= 0) {
			arr = WRT_config.xtpl
		} else {
			arr = WRT_config.yf
		}
	} else if (type == 'ZXPL') {
		arr = WRT_config.pl
	} else if (type == 'GYSJ') {
		arr = WRT_config.gysj
	}
	let fd = arr.filter(item => item.DM == obj)
	if (fd.length) {
		name = fd[0].MC
	}
	return name
}
//获取当前时间
function getNowFormatDate(d) {
	var date
	if(d){
		date=new Date(d);
	}else{
		date=new Date();
	}
	var seperator1 = "-";
	var year = date.getFullYear();
	var month = date.getMonth() + 1;
	var strDate = date.getDate();
	if (month >= 1 && month <= 9) {
		month = "0" + month;
	}
	if (strDate >= 0 && strDate <= 9) {
		strDate = "0" + strDate;
	}
	var currentdate = year + seperator1 + month + seperator1 + strDate;
	return currentdate;
}
//往前几天
function subtractDays(days) {
  let date = new Date(); // 当前日期
  date.setDate(date.getDate() - days); // 往前推几天
  return date;
}
//日期年月日时分秒
function gettime(val, type) {
	var d
	if (!val) {
		d = new Date();
	} else {
		d = new Date(val)
	}
	// var d = new Date(val);
	var year = d.getFullYear();
	var month = change(d.getMonth() + 1);
	var day = change(d.getDate());
	var hour = change(d.getHours());
	var minute = change(d.getMinutes());
	var second = change(d.getSeconds());
	function change(t) {
		if (t < 10) {
			return "0" + t;
		} else {
			return t;
		}
	}
	var time = ""
	if (!type) {
		time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
	} else {
		time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute
	}
	return time
}
//日期年月日
function getdate(val, type) {
	var d
	if (!val) {
		d = new Date();
	} else {
		d = new Date(val)
	}
	// var d = new Date(val);
	var year = d.getFullYear();
	var month = change(d.getMonth() + 1);
	var day = change(d.getDate());
	var hour = change(d.getHours());
	var minute = change(d.getMinutes());
	var second = change(d.getSeconds());
	function change(t) {
		if (t < 10) {
			return "0" + t;
		} else {
			return t;
		}
	}
	var time = year + '-' + month + '-' + day
	return time
}

//获取带药天数
function getCXTS(obj) {
	let val = 0
	if (obj.CXTS && obj.CXTS > 0) {
		val = obj.CXTS
	} else if (obj.YYTS) {
		val = obj.YYTS
	}
	return val
}

//计算日期差
function daysBetween(sDate1, sDate2, type) {
	//Date.parse() 解析一个日期时间字符串，并返回1970/1/1 午夜距离该日期时间的毫秒数
	var time1 = Date.parse(new Date(sDate1));
	var time2 = Date.parse(new Date(sDate2));
	switch (type) {
		case 'year':
			return Math.abs(parseInt((time2 - time1) / 1000 / 3600 / 24));
		case 'day':
			return Math.abs(parseInt((time2 - time1) / 1000 / 3600 / 24 / 365));
	}
}
//获取勾选列表数据
function getYzDl() {
	let ids = []
	let list = []
	var chkBoxes = $('#data_list').find('input:checked');
	$(chkBoxes).each(function () {
		// ids.push($(this).val());
		let check = $(this).attr("check")
		if (check != "undefined") {
			let ZH = $(this).attr("ZH")
			let fd = WRT_config.BrYz.filter(item => item.ZH == ZH)
			if (fd && fd.length > 0) {
				fd.map(function (ev) {
					ids.push(ev.VAL);
					list.push({ type: ev.YZZTMC, LXMC: ev.LXMC, color: ev.COLOR, YZLX: ev.YZLX, ZH: ev.ZH })
				})
			}
		} else {
			if (!($(this)[0] && $(this)[0].value)) {
				WRT_e.ui.hint({ msg: '请重新勾选' })
				return
			}
			let val = $(this)[0].value
			// ids.push($(this).val());
			let fd = WRT_config.BrYz.filter(item => item.VAL == val)
			if (fd && fd.length > 0) {
				fd.map(function (ev) {
					ids.push(ev.VAL);
					list.push({ type: ev.YZZTMC, LXMC: ev.LXMC, color: ev.COLOR, YZLX: ev.YZLX, ZH: ev.ZH })
				})
			}
		}
	});
	function unique(arr) {
		return arr.filter(function (item, index, arr) {
			//当前元素，在原始数组中的第一个索引==当前索引值，否则返回当前元素
			return arr.indexOf(item, 0) === index;
		});
	}
	let arr = unique(ids)
	return { ids: arr.join(","), list: list }
}

//机械预防html
function getJxzlHtml(target){
	const left=JSON.parse(target.leftType)||[]
	const right=JSON.parse(target.rightType)||[]
	let html=`
	<h>*请选择气压泵执行部位：</h>
	<div style="padding-bottom:10px">
		<label>左腿：</label><select id="jxyf_select_left" class="yz_sz_jxzl_select">
		${_.map(left,(item)=>
			`<option value="${item.BZ}" ${item.BZ=="LEFT_LOW_LEG"?"selected":""}>${item.MC}</option>`
			).join('')}
		</select>
		<label>右腿：</label><select id="jxyf_select_right" class="yz_sz_jxzl_select">
		${_.map(right,(item)=>
			`<option value="${item.BZ}" ${item.BZ=="RIGHT_LOW_LEG"?"selected":""}>${item.MC}</option>`
			).join('')}
		</select>
	</div>
	<div style="padding-bottom:10px">请执行时长：<input id="jxyf_input_hour" autocomplete="off" class="yz_sz_jxzl_text" oninput="getjxzlinput('jxyf_input_hour')" value="18">小时<input id="jxyf_input_min" class="yz_sz_jxzl_text" autocomplete="off" oninput="getjxzlinput('jxyf_input_min')" type="text">分钟</div>
	<button class="e_btn" onclick="onjxzlSave()">保存</button>
	<button class="e_btn" onclick="onjxzlClose()">关闭</button>
	`
	return html
}
function getjxzlinput(ev){
	let tt=$(`#${ev}`)[0].value.replace(/\D/g,'')
	if(ev=='jxyf_input_hour'){
		if(tt>9999){
			tt=9999
		}
	}
	if(ev=='jxyf_input_min'){
		let re=/^\d$|^[0-5][0-9]$|60/ig
		if(tt>60){
			tt=60
		}
	}
	$(`#${ev}`)[0].value=tt
}
//机械预防保存
function onjxzlSave(){
	let left=$("#jxyf_select_left option:selected")[0].value
	let right=$("#jxyf_select_right option:selected")[0].value
	let hour=$("#jxyf_input_hour")[0].value
	let min=$("#jxyf_input_min")[0].value
	if(!left||!right){
		WRT_e.ui.hint({msg:'左腿、右腿是必填项。',type:"error"})
		return
	}else if((hour==''||hour=='0')&&min<30){
		WRT_e.ui.hint({msg:'执行时长最小为30分钟',type:"error"})
		return
	}else{
		$("#if_Jxzl").iziModal('destroy')
		let list= yzdata[yzindex]
		list.leftType=left
		list.rightType=right
		list.xs=hour
		list.fz=min
	}
}
function onjxzlClose(){
	$('#if_Jxzl').iziModal('destroy')
	delZHTXT(yzindex)
	yzdata.splice(yzindex, 1)
	exportMBList()
	zhmbsrListDr()
}
//取消

//下拉菜单过滤
function islv(arr, type, index) {
	let target = []
	if (type == "ZXPL") {
		let obj = yzdata[index]
		if (obj.YZLX == "cydy" || obj.YZLX == "cy" || obj.YZLX == "zcydy") {
			target = arr
		} else if (obj.YZLX == "cq") {
			arr.map(function (item) {
				if (item.LB == "1" || item.LB == "" || item.LB == null) {
					target.push(item)
				}
			})
		} else if (obj.YZLX == "ls") {
			arr.map(function (item) {
				if (item.LB == "0" || item.LB == "" || item.LB == null) {
					target.push(item)
				}
			})
		}
	} else {
		target = arr
	}
	return target
}


//下拉菜单html
function ypyfplHtml(arr, type, index) {
	let array = islv(arr, type, index)
	var html = `<input type="text" class="li" readonly="" value="" data="" title="">`
	if (array && array.length > 0) {
		array.map(function (obj) {
			html += `<input type="text" class="li" readonly="" value="${obj.MC}" data="${obj.DM}" title="${obj.DM}">`
		})
	}
	return html
}
//处理下拉菜单事件
function isModel(el, id) {
	$(".isModel").css("display", "none")
	yz_isModel = el
	$(`#${el}_list`).css("display", "block")
}
//处理类型
function lbHtml(name) {
	let arr = []
	switch (name) {
		case 'cq':
			// arr = ["",'治疗', '饮食', '药品', '嘱托']
			arr = [{ title: '药品', value: '3' }, { title: '治疗', value: '1' }, { title: '饮食', value: '2' }, { title: '嘱托', value: '4' }]
			// arr=[{title:'药品',value:"药品"},{title:'饮食',value:"饮食"},{title:'治疗',value:"治疗"},{title:'嘱托',value:"嘱托"}]
			break;
		case 'ls':
			// arr = ["","治疗", "", "药品", "嘱托"]
			arr = [{ title: '药品', value: '3' }, { title: '治疗', value: '1' }, { title: '嘱托', value: '4' }]
			// arr=[{title:'药品',value:"药品"},{title:'治疗',value:"治疗"},{title:'嘱托',value:"嘱托"}]
			break;
		default:
			// arr = ["","", "", "药品", ""]
			arr = [{ title: '药品', value: '3' }]
		// arr=[{title:'药品',value:"药品"}]
	}
	let temp = ""
	arr.map(function (item, index) {
		if (item) {
			temp += `<input type="text" class="li" readonly="" title="${item.value}" value="${item.title}" val="${item.title}">`
		}
	})
	return temp
}
//处理行号
function delindex(id) {
	if (id.indexOf("_list") >= 0) {
		id = id.split("_list")[0]
	}
	let params = {
		name: "",
		index: yzdata.length - 1
	}
	if (yzdata.length >= 100) {
		params.name = id.substr(0, id.length - 2)
		params.index = id.substr(id.length - 3)
		yzindex = params.index
	} else if (yzdata.length >= 10) {
		params.name = id.substr(0, id.length - 1)
		params.index = id.substr(id.length - 2)
		yzindex = params.index
	} else {
		params.name = id.substr(0, id.length - 1)
		params.index = id.substr(id.length - 1, 1)
		yzindex = params.index
	}
	return params
}

var refresh = 0
//对象、数组变化监听(增删改)
function deepProxy(obj, cb) {
	if (typeof obj === 'object') {
		for (let key in obj) {
			if (typeof obj[key] === 'object') {
				obj[key] = deepProxy(obj[key], cb);
			}
		}
	}
	return new Proxy(obj, {
		set: function (target, key, value, receiver) {
			if (typeof value === 'object') {
				value = deepProxy(value, cb);
			}
			let cbType = target[key] == undefined ? 'create' : 'modify';
			//排除数组修改length回调
			if (!(Array.isArray(target) && key === 'length') && refresh < 1) {
				refresh++
				setTimeout((() => cb(cbType, {
					target,
					key,
					value
				})), 50)
			}
			// if(WRT_config.mbdr&&mbindex<MBDRList.length-1){
			//     target[key]=value
			//     return true
			// }
			return Reflect.set(target, key, value, receiver);
		},
		deleteProperty(target, key) {
			setTimeout((() => cb('delete', {
				target,
				key
			})), 50)
			return Reflect.deleteProperty(target, key);
		}
	});
}
//计算用药天数
function sumyyts(index) {
	if (yzdata[index]) {
		let obj = yzdata[index]
		let yp = obj.yp_dataop || {}
		let yfdm = obj.YFDM || yp.yfdm || ""
		if (obj && obj.YZLX == 'cydy' && obj.YCYL && obj.JLDW && obj.ZXFF && obj.ZXPL && obj.SFSL && (obj.YPID || obj.XMID)) {
			WRT_e.api.yz_sz.GetYYTS({
				params: {
					al_ypid: obj.YPID || obj.XMID,
					ad_ycyl: obj.YCYL,
					as_jldw: obj.JLDW,
					as_ff: obj.ZXFF,
					as_pl: obj.ZXPL,
					ad_sl: obj.SFSL,
					as_yfdm: yfdm,
				},
				success(data) {
					if (data.Code == 1) {

						yzdata[index].YYTS = data.Result
					} else if (data.CodeMsg) {
						WRT_e.ui.hint({ type: "error", msg: data.CodeMsg })
					}
				}
			})
		}
	}
}
//计算外配处方信息结束时间修改
function sumxspjssj(index) {
	if (yzdata[index]) {
		let obj = yzdata[index]
		let yp = obj.yp_dataop || {}
		let yfdm = obj.YFDM || yp.yfdm || ""
		
		if (obj && obj.YCYL && obj.JLDW && obj.ZXFF && obj.ZXPL && (obj.SQSL||obj.SFSL) && (obj.YPID || obj.XMID)) {
			WRT_e.api.yz_sz.GetYYTS({
				params: {
					al_ypid: obj.YPID || obj.XMID,
					ad_ycyl: obj.YCYL,
					as_jldw: obj.JLDW,
					as_ff: obj.ZXFF,
					as_pl: obj.ZXPL,
					ad_sl: obj.SQSL||obj.SFSL,
					as_yfdm: yfdm,
				},
				success(data) {
					if (data.Code == 1) {
						let curDate = new Date(yzdata[index].KSSJ_TXT)
						yzdata[index].YYTS = data.Result==0?1:data.Result
						yzdata[index].CXTS = data.Result+1
						let num=parseInt(data.Result)
						if(num<=0){ num=1 }
						let vl=gettime(new Date(curDate.getTime() + num*24 * 60 * 60 * 1000))
						yzdata[index].JSSJ_TXT=vl
						let xh = yzdata[index].XH
						let zhbs = yzdata[index].ZHTXT
						if (typeof xh == 'number') {

						}else {
							xh = parseInt(xh)
						}
						if (zhbs) {
							yzdata.map(function (item, key) {
								let t = item.XH
								if (typeof t == 'number') {

								}else {
									t = parseInt(t)
								}
								// let xh1=xh.split("-")
								// let xh2=t.split("-")
								if (xh === t && key != index) {
									yzdata[key]['JSSJ_TXT'] = vl
									yzdata[key]['CXTS'] = data.Result+1
								}
							})
						}
					} else if (data.CodeMsg) {
						WRT_e.ui.hint({ type: "error", msg: data.CodeMsg })
					}
				}
			})
		}
	}
}

//输入框事件
function xz_click(ev, name, index, type) {
	// keyCode=''
	if (order.indexOf(name) < 0) {
		keyCode = name
	} else if (!keyCode && order.indexOf(name) >= 0) {
		keyCode = name
	}
	yzindex = index
	let val = ''
	if (type == 'check') {
		yzdata[index][name] = ev.checked
		if (yzdata[index].ZHTXT && name == "check") {
			yzdata.map(function (item, key) {
				if ((item.ZH == yzdata[index].ZH&&item.ZH!=undefined)||(item.XH!=undefined&&item.XH == yzdata[index].XH)) {
					yzdata[key]["check"] = ev.checked
				}
			})
		}
	} else if (type == 'SFZB') {
		yzdata[index][name] = `${ev.checked ? "1" : "0"}`
	} else if (type == 'times') {
		yzdata[index][name] = ev
	} else if (type == "select") {
		val = ev[ev.selectedIndex].value
		yzdata[index][name] = ev[ev.selectedIndex].value
		// if(WRT_config.yz_sz.zkid == 33){ // 在预计出院选择草药快递所有药品一起控制批量修改
		// 	yzdata.map(item=>{
		// 		item[name] = ev[ev.selectedIndex].value
		// 	})
		// }
	} else if (type == "input") {
		val = ev.value
		if (name == 'YCYL') {
			val = val.replace(/^\D*(\d*(?:\.\d{0,})?).*$/g, '$1')
		}
		yzdata[index][name] = val
	}
	if(name=='JINRILT'){
		if(yzdata[index][name]==1){
			yzdata[index][name]=''
		}else{
			WRT_e.api.yz_sz.getDateString({
				params: { days: 0 }, success(data) {
					if (data.Result) {
						if(dealtime(yzdata[index].KSSJ_TXT)==dealtime(data.Result)){
							if(!yzdata[index][name]){
								yzdata[index][name]='1'
							}else{
								yzdata[index][name]=''
							}
						}else{
							yzdata[index][name]=''
							WRT_e.ui.hint({msg:'计划开始时间非当日时不允许勾【今日临停】',type:'error'})
						}
						function dealtime(val){
							var d
							if (!val) {
								d = new Date();
							} else {
								d = new Date(val)
							}
							var year = d.getFullYear();
							var month = change(d.getMonth() + 1);
							var day = change(d.getDate());
							var time= year + '-' + month + '-' + day
							function change(t) {
								if (t < 10) {
									return "0" + t;
								} else {
									return t;
								}
							}
							return time
						}
					}
				}
			})
		}
	}
	xz_Same(name, index, val)
	if (name == "SFSL") {
		// keyCode = 'SFSL'
		if (yzdata[index].YZLX == "cydy") {
			let XL = ''
			if (yzdata[index].ypfjxx) {
				let ypfjxx = JSON.parse(yzdata[index].ypfjxx)
				XL = ypfjxx.xl
			} else {
				XL = yzdata[index].XL
			}
			if (yzdata[index][name] && (parseInt(yzdata[index][name]) > parseInt(XL))) {
				WRT_e.ui.message({
					title: '提示',
					content: `该${yzdata[index].MC}的限量为${XL}，您所开数量超过限量，系统将默认将其修改为合法值！`,
					onOk() {
						keyCode = "SFSL"
						yzdata[index].SFSL = XL
					},
				})
			}
		} else if ((yzdata[index].YZLX == "cy" || yzdata[index].YZLX == "zcydy")) {
			if (yzdata[index][name] && yzdata[index][name] > 30) {
				WRT_e.ui.message({
					title: '提示',
					content: `帖数不得超过30帖！`,
					onOk() {
						keyCode = "SFSL"
						yzdata[index].SFSL = 30
						xz_Same(name, index, 30)
					},
				})
			}
		}
	}

}

//一次用量
function xz_ycly(ev, name, index, type) {
	if (type == "input") {
		val = ev.value
		if (name == 'YCYL') {
			val = val.replace(/^\D*(\d*(?:\.\d{0,})?).*$/g, '$1')
		}
		yzdata[index][name] = val
		// setTimeout(function (ev) {
		// 	yzdata[index][name] = val
		// }, 120);
	}
	let list = yzdata[index] || {}, jx = ""
	if (list.yp_dataop && list.yp_dataop.jx) {
		jx = list.yp_dataop.jx || ""
	}
	// //校验一次用量
	if(yzdata[index].ZXFF&&yzdata[index].ZXPL){
		WRT_e.api.yz_sz.getDrugUsageById({
			params:{
				yaoPinID:yzdata[index].YPID||yzdata[index].XMID
			},
			success(msg){
				if(msg.data&&(msg.data[0].fangFa||msg.data[0].pinLu)&&(val>msg.data[0].changYongLiang*2)&&(msg.data[0].changYongLiang!=null&&msg.data[0].changYongLiang!=''&&msg.data[0].changYongLiang!=0)){
					WRT_e.ui.message({
						title: '提示',
						content: `【${yzdata[index].MC}】您的一次用量超过常用量的2倍，请注意！`,
						onOk() {
							zybzl_fun(ev, name, index,jx)
						}
					})
				}else{
					zybzl_fun(ev, name, index,jx)
				}
			}
		})
	}
	if (yzdata[index].YZLX == 'cq'&&yzdata[index].iswpy) {
		if (name == 'YCYL') {
			val = val.replace(/^\D*(\d*(?:\.\d{0,})?).*$/g, '$1')
			yzdata[index][name] = val
		}
		sumxspjssj(index)
	}
}
//中药检查包装量
function zybzl_fun(ev, name, index,jx){
	if (name == 'YCYL' && jx == 'Ypj' && (yzdata[index].YZLX == "cy" || yzdata[index].YZLX == "zcydy")) {
		WRT_e.api.yz_sz.CheckYcyl({
			params: { al_ypid: yzdata[index].YPID, as_ypmc: yzdata[index].MC, as_jldw: yzdata[index].JLDW, ad_ycyl: ev.value },
			success(data) {
				if (!(data.Code == 1 && data.CodeMsg)) {
					WRT_e.ui.message({
						title: '信息窗口',
						content: data.CodeMsg,
						onOk() {
							// keyCode='YCYL'
							let value1 = $(`#YCYL${index}`).val();
							setTimeout(function () {
								$(`#YCYL${index}`).val('').focus().val(value1);
							}, 500)
						},
					})
				}
			}
		})
	}
}
//出院带药数量
function xz_sfsl(value, name, index) {

	yzdata[index][name] = value
	xz_Same(name, index, value)
	if (yzdata[index].YZLX == 'cydy') {
		// keyCode = "SFSL"
		sumyyts(index)
	}
	if (name == "SFSL") {
		// keyCode = 'SFSL'
		if (yzdata[index].YZLX == "cydy") {
			let XL = ''
			if (yzdata[index].ypfjxx) {
				let ypfjxx = JSON.parse(yzdata[index].ypfjxx)
				XL = ypfjxx.xl
			} else {
				XL = yzdata[index].XL
			}
			if (yzdata[index][name] && (parseInt(yzdata[index][name]) > parseInt(XL))) {
				WRT_e.ui.message({
					title: '提示',
					content: `该${yzdata[index].MC}的限量为${XL}，您所开数量超过限量，系统将默认将其修改为合法值！`,
					onOk() {
						keyCode = "SFSL"
						yzdata[index].SFSL = XL
						sumyyts(index)
					},
				})
			}
		} else if ((yzdata[index].YZLX == "cy" || yzdata[index].YZLX == "zcydy")) {
			if (yzdata[index][name] && yzdata[index][name] > 30) {
				WRT_e.ui.message({
					title: '提示',
					content: `帖数不得超过30帖！`,
					onOk() {
						keyCode = "SFSL"
						yzdata[index].SFSL = 30
						xz_Same(name, index, 30)
					},
				})
			}
		}
	}
}
//同一组数组数据统一
function xz_Same(name, index, val) {
	if (name == 'JRCY'&&(yzdata[index].YZLX == 'zcydy'||yzdata[index].YZLX == 'cydy')) {
		yzdata.map(function (ev, key) {
			if((yzdata[index].YZLX == 'zcydy'||yzdata[index].YZLX == 'cydy')){
				yzdata[key][name] = val
			}
		})
	}
	if (name == 'YZLX' || name == 'ZXFF' || name == 'ZXPL' || name == 'SJZZXPL' || name == 'GYSJ' || name == 'KSSJ_TXT' || name == 'JSSJ_TXT' || name == 'BQID' || name == 'TZDID' || name == 'SSTZD' || name == 'ZSBQ' || (name == 'SFSL' && (yzdata[index].YZLX == 'cy' || yzdata[index].YZLX == 'zcydy'))) {
		let xh = yzdata[index].XH
		let zhbs = yzdata[index].ZHTXT
		if (typeof xh == 'number') {

		} else if (xh.indexOf("-") >= 0) {
			xh = xh.split("-")[0]
		} else {
			xh = parseInt(xh)
		}
		if (zhbs) {
			yzdata.map(function (item, key) {
				let t = item.XH
				if (typeof t == 'number') {

				} else if (t.indexOf("-") >= 0) {
					t = t.split("-")[0]
				} else {
					t = parseInt(t)
				}
				// let xh1=xh.split("-")
				// let xh2=t.split("-")
				if (xh === t && key != index) {
					yzdata[key][name] = val
				}
				if(xh === t &&(name == 'KSSJ_TXT' ||name == 'ZXFF' || name == 'ZXPL'||name == 'GYSJ')&&yzdata[key].YZLX == 'cq'&&yzdata[key].iswpy){
					sumxspjssj(key)
				}
			})
		}
	}
}
//计算草药总价
function getcysum() {
	let arr = []
	if (yzdata.length > 0) {
		arr = yzdata
	} else if (WRT_config.WtjYz && WRT_config.WtjYz.length > 0) {
		arr = WRT_config.WtjYz
	}
	let sum = 0
	if (WRT_config.url.as_yzlb != "ls") {
		return 0;
	}
	arr.map(function (item) {
		let list = item.yp_dataop || {}
		let sfsl = setSFSL(item)
		if ((item.YZLB == '3' || list.yzlb == "3") && (item.YZLX == "cy" || item.YZLX == "zcydy")) {
			if (item.YCYL != '' && (item.SJ != '' || list.sj != "") && (sfsl != '')) {
				sum += parseFloat(item.YCYL) * parseFloat(item.SJ || list.sj || 0) * parseFloat(sfsl);
			}
		}
	})
	let tatol = sum.toFixed(2) || 0;
	return tatol
}
//绘制界面
function refresh_init() {
	isSubbmit = false
	//绘制表格头部
	var head = new head_View();
	head.$el = $("#th_list");
	head.init().render();
	//绘制底部按钮列表
	var footbtn = new footbtn_View();
	footbtn.$el = $("#dv_newButton");
	footbtn.init().render();
	let json = []
	//获取已提交医嘱
	WRT_e.api.yz_sz.getBrYz({
		params: { al_blid: WRT_config.url.as_blid, as_yzlb: WRT_config.url.yzlb.value, as_zy: 1, as_tz: 1, as_lb: "", as_fylx: WRT_config.yz_sz.jslx },
		success(data) {
			if (data.Code == 1) {
				WRT_config.BrYz = data.Result;
				//绘制医嘱展示
				var yzsz = new yzsz_View();
				yzsz.$el = $("#data_list");
				yzsz.init({
					data: XHData(data.Result)
				}).render();
				// imgtab(false)
			}
		}
	})
	//绘制病人信息模块（未提交医嘱）
	var person = new person_View();
	person.$el = $("#container-person");
	person.init({
		data: WRT_config.yz_sz
	}).render();
	var yzhead = new yzhead_View()
	yzhead.$el = $("#yz_list");
	yzhead.init().render();
	WRT_e.api.yz_sz.getWtjYz({
		params: { al_blid: WRT_config.url.as_blid, as_lx: WRT_config.url.as_yzlb, al_sqdsjid:  WRT_config.url.as_sqdsjid || 0 },
		success(data) {
			if (data.Code == 1) {
				WRT_config.WtjYz = data.Result
				if (data.Result.length == 0) {
					$("#newyztitle_pad").css('display', 'none')
					$("#newyz_pad").css('display', 'none')
					$("#yz_pad").css('height', 'calc(100% - 100px)')
					$("#box_pad").css('display', 'none')
				} else {
					$("#newyztitle_pad").css('display', 'block')
					$("#newyz_pad").css('display', 'block')
					$("#yz_pad").css('height', '170px')
					$("#box_pad").css('display', 'block')
				}
				let mb = null; mb_xh = 0
				for (let i = 0; i < data.Result.length; i++) {
					if (data.Result[i].ZHTXT) {
						mb = data.Result[i].ZH
						if (i + 1 < data.Result.length && mb == data.Result[i + 1].ZH) {
							mb_xh++;
							if (mb_xh == 1) {
								data.Result[i].ZHTXT = '┐'
							} else {
								data.Result[i].ZHTXT = '|'
							}
						} else {
							data.Result[i].ZHTXT = '┘'
							mb = null; mb_xh = 0
						}
					} else {
						mb = null; mb_xh = 0
					}
				}
				let index = 0, ZH = 0
				let arr = data.Result.map(function (item, key) {
					for (let i in item) {
						if (item[i] == null) {
							item[i] = ""
						}
						if (i == 'WGYPSQJLID' && item[i]) {
							item[i] = item[i].toString()
						}
					}
					item.ZLYZMXS=JSON.stringify(item.ZLYZMXS)||''
					item.SJZZXPL = item.ZXPL2
					item.YZLX = isyzlb(item)
					if (item.LB == 2) {
						item.GG = item.DJ
					}
					if (item.YZLX == 'cy' || item.YZLX == 'zcydy') {

						if (item.ZHTXT == '┐') {
							index++;
							ZH = 0
							ZH++;
							item.XH = index + '-' + ZH
						} else if (item.ZHTXT == '|') {
							ZH++;
							item.XH = index + '-' + ZH
						} else if (item.ZHTXT == '┘') {
							ZH++;
							item.XH = index + '-' + ZH
						} else {
							index++;
							ZH = 0
							ZH++;
							item.XH = index + '-' + ZH
						}
					} else {
						if (item.ZHTXT == '┐') {
							index++;
							item.XH = index
						} else if (item.ZHTXT == '|' || item.ZHTXT == '|') {
							item.XH = index
						} else if (item.ZHTXT == '┘') {
							item.XH = index
						} else {
							index++;
							item.XH = index
						}
					}
					// if (item.YZLX == 'cydy') {
					//     sumyyts(key)
					// }
					let fd = data.Result.filter(k => k.ZH == item.ZH)
					if (fd && fd.length == 1) {
						item.ZHTXT = ""
						return item
					}
					return item
				})
				let total = getcysum()
				$("#cysum_title").html(total)
				var yzlist = new yzlist_View()
				//监听已选遗数组
				yzdata = deepProxy(arr, (type, data) => {
					refresh = 0
					if (data.target.length > 0) {
						data.target.map(function (item, index) {
							if (item.KSSJ_TXT == '' && item.MC) {
								item.KSSJ_TXT = WRT_config.defaultKSSJ_TXT
							}
						})
					}
					if (data.target.KSSJ_TXT == '') {
						data.target.KSSJ_TXT = WRT_config.defaultKSSJ_TXT
					}
					yzlist.render()
					if (yzdata.length == 0) {
						$("#newyztitle_pad").css('display', 'none')
						$("#newyz_pad").css('display', 'none')
						$("#yz_pad").css('height', 'calc(100% - 100px)')
						$("#box_pad").css('display', 'none')
					} else {
						$("#newyztitle_pad").css('display', 'block')
						$("#newyz_pad").css('display', 'block')
						$("#yz_pad").css('height', '170px')
						$("#box_pad").css('display', 'block')
					}
					let total = getcysum()
					$("#cysum_title").html(total)
					if (keyCode) {
						if ($(".izi_message").length > 0) {
							return
						}
						currentLine = 0
						let el = ""
						if (order.length == 0) {
							SetNextFocusObj(yzdata[yzindex])
						}
						if ((keyCode == "KSSJ_TXT" || keyCode == "JSSJ_TXT") && yzdata[yzindex][keyCode]) {
							keyCode = 'MC'
						}
						// for(let i=0,j=order.length;i<j;i++){
						if (keyCode == "MC") {
							el = "MC"
							if (yzindex < 0 || yzindex == null || yzindex >= yzdata.length) {
								yzindex = yzdata.length - 1
							}
							if (yzdata[yzindex].MC) {
								$(`#${el}${yzindex}`)[0].ov = yzdata[yzindex].MC
							}
							$(`#${el}${yzindex}`)[0].value = ''
							$(`#${el}${yzindex}`).focus()
							return
						}
						el = keyCode
						if (el == "ZXFF" || el == "ZXPL" || el == "GYSJ" || el == "LB") {
							if (el == "LB" && yzdata[yzindex].LB >= 0) {
							} else {
								model_input(el, yzindex)
								$(`#${el}${yzindex}`)[0].value = ""
							}
							$(`#${el}${yzindex}`).focus()
						} else {
							let val = $(`#${el}${yzindex}`).val()
							$(`#${el}${yzindex}`).focus().val('').val(val)
						}
						return
						// if($(`#${keyCode}${yzindex}`)[0]){
						//     $(`#${el}${yzindex}`)[0].ov=$(`#${el}${yzindex}`)[0].value
						// }
						// }
					}
					// }
				});
				//初始化
				yzlist.$el = $("#yzdata_list");
				yzlist.init({ data: yzdata }).render();
			}
		}
	})
	let height = window.screen.height;
	let num = "330px"
	if (height == '1024') {
		num = '265px'
	}
	if (imgshow) {
		num = '540px'
	}
	$("#newyz_pad").css("max-height", num)
	// var yzlist=new yzlist_View()
	// //监听已选遗数组
	// yzdata= deepProxy(WRT_config.arr, (type, data) => {
	//     yzlist.render()
	// });
	// //初始化
	// yzlist.$el=$("#yzdata_list");
	// yzlist.init({data:yzdata}).render();
	$('.btn-refresh').focus();
}

//药品是否可编辑
function isLB(index) {
	let obj = yzdata[index]
	let rtn = true
	if (obj && !obj.LB && obj.MC) {
		rtn = false
	}
	if (obj && obj.ZHTXT) {
		rtn = false
		if (obj && obj.ZHTXT == '┘') {
			rtn = true
		}
	}
	return rtn
}
//是否编辑数量/剂数
function getSFSLHtml(obj, index) {
	let show = false;
	if (obj.LB == 3 && (obj.YZLX == 'cq' || obj.YZLX == 'ls')) {
		show = false;
	}
	else if (obj.YZLX == 'cydy') {
		show = true
	}
	else if (obj.YZLX == 'cy' || obj.YZLX == 'zcydy') {
		show = true
	} else if ((obj.LB == 1 || obj.LB == 4) && (obj.YZLX == 'cq' || obj.YZLX == 'ls')) {
		show = true
	} else if (obj.LB == 2) {
		show = false;
	}
	return show
}

//处理数量/剂数数据
function setSFSL(obj, index) {
	let num = '';
	if (obj.SFSL) {
		num = obj.SFSL
	} else {
		if (obj.YZLX == 'cy' || obj.YZLX == 'zcydy') {
			num = obj.SFCS || 7
		} else if (obj.LB == '1' || obj.LB == '4') {
			num = obj.SFSL || 1
		}
	}
	return num
}

//是否显示
function isCXTS(obj, index) {
	let num = false;
	if (obj.YZLX == 'cydy' && obj.SFSL) {
		num = true
	}
	return num
}

//处理药品附加信息
function getdataop(obj) {
	let arr = obj.split(",") || []
	let params = {}
	arr.map(function (item) {
		let fd = item.split('|')
		params[fd[0]] = fd[1]
	})
	return params
}
//日期
function setTime(index) {
	let list = yzdata[index]
	if (list.KSSJ_TXT == "" || !list.KSSJ_TXT) {
		if (list.ZHTXT) {
			let fd = yzdata.filter(e => e.ZH == list.ZH)
			if (fd && fd.length > 1) {
				yzdata[index].KSSJ_TXT = fd[0].KSSJ_TXT || WRT_config.defaultKSSJ_TXT
			}
		} else {
			yzdata[index].KSSJ_TXT = WRT_config.defaultKSSJ_TXT
		}
	} else {
		yzdata[index].KSSJ_TXT = WRT_config.defaultKSSJ_TXT
	}
}
//获取日期
function getKSSJ_TXT() {
	WRT_e.api.yz_sz.getDateString({
		params: { days: 0 }, success(data) {
			if (data.Result) {
				WRT_config.defaultKSSJ_TXT = gettime(data.Result, true)
			}
		}
	})
}

//手术通知单
function getsstzd(val) {
	let title = ''
	if (val) {
		let td = val.split("(")
		let fd = td[1].split(")")
		let time = parseInt(fd[0])
		var d = new Date(time);
		var year = d.getFullYear();
		var month = change(d.getMonth() + 1);
		var day = change(d.getDate());
		function change(t) {
			if (t < 10) {
				return "0" + t;
			} else {
				return t;
			}
		}
		title = year + '-' + month + '-' + day + '手术'
	}
	return title
}
//药品常用量
function isypcyl(obj, index) {
	let ff = [], pl = []
	if (obj.YZLX == 'cy' || obj.YZLX == 'zcydy') {
		ff = WRT_config.cyyf
	} else {
		ff = WRT_config.yf
	}
	if (obj.YPID == "z" + WRT_config.yz_sz.xtmlid) {
		pl = islv(WRT_config.xtpl, "ZXPL", index)
	} else {
		pl = islv(WRT_config.pl, "ZXPL", index)
	}
	let fd = ff.filter(item => item.DM == obj.ZXFF)
	if (fd && fd.length == 0) {
		yzdata[index].ZXFF = ""
	}
	let td = pl.filter(item => item.DM == obj.ZXPL)
	if (td && td.length == 0) {
		yzdata[index].ZXPL = ""
	}
	let gd = WRT_config.gysj.filter(item => item.DM == obj.GYSJ)
	if (gd && gd.length == 0) {
		yzdata[index].GYSJ = ""
	}
}
//
//选择药品
function selectMC(el, obj, index, iskyType,stype) {
	WRT_config.defaultValue = obj.mc
	let list = yzdata[index]
	if (list.KSSJ_TXT == "") {
		setTime(index)
	}
	list.MC = obj.mc
	list.GG = obj.gg?obj.gg:''
	list.YPID = obj.ypid || ''
	// list.crrt=obj.crrt||''
	// list.LB =obj.lb
	if (list.LB != 4) {
		let yid = obj.ypid
		if (typeof obj.ypid === 'number' && !isNaN(obj.ypid)) {
			yid = JSON.stringify(obj.ypid)
		}
		let id = yid.substr(0, 1)
		if (id == 'y') {
			list.LB = 2
		} else if (id == 'z') {
			list.LB = 1
		} else {
			list.LB = 3
		}
	}
	SetNextFocusObj(list)
	for (let i = 0, j = order.length; i < j; i++) {
		if (order[i] == "MC") {
			let el = order[i + 1]
			keyCode = el
		}
	}
	// list.MC_list = obj
	if (list.YZLX == 'cq' && (list.ZXPL == '' || !list.ZXPL)) {
		list.ZXPL = 'qd'
	}
	else if (list.YZLX == 'ls' && iskyType) {
		if (list.ZHTXT == '' || list.ZHTXT == '┐') {
			list.ZXPL = 'st'
		}
	}
	if ((list.LB == 1 || list.LB == 2) && list.SFSL == '') {
		list.SFSL = 1
	}
	if (list.YZLX == "cydy" && (list.JRCY == '')) {
		list.JRCY = "2"
	}
	if (list.YZLX == "zcydy" && (list.ZSBQ == '') && WRT_config.yz_sz.curyszkid == 33) {
		list.JRCY = "1"
		list.ZSBQ = "4"
	}
	let zl_arr=[]
	if(WRT_config.ZiFjxx) {
		list.zlyzmxs=''
		if(WRT_config.ZiFjxx.zlyzmxs){
			zl_arr=WRT_config.ZiFjxx.zlyzmxs
			list.zlyzmxs=JSON.stringify(WRT_config.ZiFjxx.zlyzmxs)
		}
	}
	yzindex = index
	let type = setFylx(WRT_config.yz_sz.jslx, WRT_config.yz_sz.zykt, WRT_config.yz_sz.sfzf, list.KZJB,list.YPLB,null,list.LB,zl_arr)
	list.ZFtitle = type
	yzdata[index] = list
	$(`#${el}_list`).css("display", "none")
	yz_isModel = false
	// //校验一次用量
	if(yzdata[index].ZXFF&&yzdata[index].ZXPL){
		WRT_e.api.yz_sz.getDrugUsageById({
			params:{
				yaoPinID:yzdata[index].YPID||yzdata[index].XMID
			},
			success(msg){
				let val=yzdata[index].ycyl||yzdata[index].YCYL
				if(msg.data&&(msg.data[0].fangFa||msg.data[0].pinLu)&&(val>msg.data[0].changYongLiang*2)&&(msg.data[0].changYongLiang!=null&&msg.data[0].changYongLiang!=''&&msg.data[0].changYongLiang!=0)){
					WRT_e.ui.message({
						title: '提示',
						content: `【${yzdata[index].MC}】您的一次用量超过常用量的2倍，请注意！`,
						onOk() {
						}
					})
				}
			}
		})
	}
	if(WRT_config.ZiFjxx&&WRT_config.ZiFjxx.isjxyfyz=="1") {
		let html=getJxzlHtml(WRT_config.ZiFjxx)
		WRT_e.ui.model({
			id: "if_Jxzl",
			title: "机械预防",
			width: "650px",
			content: html,
			iframe: false,
			closeButton: false,
			closeOnEscape: false,
		})
	}else if(stype){
		if (ZhmbSrdata && ZhmbSrdata.length > 0 && ZhmbSrindex >= 0) {
			ZhmbSrindex++
			zhmbsrDr(ZhmbSrindex)
		}
	}
}

//选中药品后事件处理
function ypway(el, obj, index, type, iskyType) {
	// 
	let arr = [], list = yzdata[index]
	gcptype=false
	// yzdata[index].KSSJ_TXT=WRT_config.defaultKSSJ_TXT
	if (WRT_config.url.as_yzlb == 'cq') {
		list.YZLX = 'cq'
	}
	list.gcp=obj.gcp||''
	if(list.gcp=='null'){
		list.gcp=''
	}
	list.ywbh=obj.ywbh||''
	list.YZLB = list.yp_dataop.yzlb
	list.KZJB = list.yp_dataop.kzjb
	list.DW = list.yp_dataop.dw
	obj.yzlb = list.yp_dataop.yzlb
	list.obj = obj
	yp_dataop = list.yp_dataop
	if(list.SBDM || list.sbdm){//重置国家码
		list.SBDM=''
		list.sbdm=''
	}
	if(list.ZF){//重置费用类型
		list.ZF=''
	}
	if(list.JLDW){//重置剂量单位
		list.JLDW=''
	}
	if (list.YZLX == "cy" || list.YZLX == "zcydy") {
		list.SFSL = list.SFSL || "7"
	}
	if (type) {
		list.JLDW_list = []
		if (list.JLDW) {
			list.JLDW_list = [{ JLDW: list.JLDW }]
		}
		selectMC(el, obj, index, iskyType,true)
		return
	}
	list.ypfjxx = JSON.stringify(WRT_config.ypfjxx)
	if (WRT_config.ypfjxx) {
		list.flm = WRT_config.ypfjxx.flm || ""
		list.sbdm = WRT_config.ypfjxx.sbdm || ""
		list.sbdm2 = WRT_config.ypfjxx.sbdm2 || ""

		list.jpyp = WRT_config.ypfjxx.jpyp || ""
		list.YPDSBZ = WRT_config.ypfjxx.ypdsbz || ""
		list.cwyyhzdid = WRT_config.ypfjxx.cwyyhzdid || ""
		if (WRT_config.ypfjxx.nylts) {
			WRT_e.ui.message({
				title: '信息窗口',
				content: WRT_config.ypfjxx.nylts,
				onOk() {
				}
			})
		}
		if (WRT_config.ypfjxx.txxzp) {
			WRT_e.ui.message({
				title: '信息窗口',
				content: WRT_config.ypfjxx.txxzp,
				onOk() {
				}
			})
		}
		if (WRT_config.ypfjxx.jesgtx) {
			WRT_e.ui.message({
				title: '信息窗口',
				content: WRT_config.ypfjxx.jesgtx,
				onOk() {
				}
			})
		}

	}
	if (WRT_config.ypsyff) {
		let rn=true
		WRT_config.ypsyff.map(function (item) {
			// if (item.CYL !== null) {
			// 	list.JLDW = item.JLDW || ""
			// }

			if (item.CYL === null) {
				arr.push({ JLDW: item.JLDW })
			} else{
				let pl
				if(rn&&iskyType){
					rn=false
					list.JLDW = item.JLDW?item.JLDW:list.JLDW || ""
					list.ZXFF = item.FF?item.FF:list.ZXFF || ""
					list.GYSJ = item.GYSJ?item.GYSJ:list.GYSJ || ""
					list.YCYL = item.CYL?item.CYL:list.YCYL || ''
					pl = item.PL?item.PL:""
				}
				if (!pl) {
					pl = `${WRT_config.url.as_yzlb == "ls" ? "st" : "qd"}`
					if (list.YZLX == "cy" || list.YZLX == "zcydy") {
						pl = 'bid'
					}
				}
				yzdata[index].ZXPL = pl || ""
			}
		})
		if (list.JLDW == '' || (WRT_config.ypsyff && WRT_config.ypsyff.length == 1)) {
			let jl = arr[0] || {}
			list.JLDW = jl.JLDW || ''
		}
		list.JLDW_list = arr
	} else {
		console.log('11111:'+list.JLDW)
		list.JLDW_list = []
		if (list.JLDW) {
			list.JLDW_list = [{ JLDW: list.JLDW }]
		}else{
			list.JLDW=list.yp_dataop.jldw
			list.JLDW_list = [{ JLDW: list.yp_dataop.jldw }]
		}
	}
	if (list.ZHTXT && list.ZHTXT != '┐') {
		let j = parseInt(index) - 1
		if (j < 0) {
			j = 0
		}
		list.ZXPL = yzdata[j].ZXPL || ""
		list.ZXFF = yzdata[j].ZXFF || ""
		list.GYSJ = yzdata[j].GYSJ || ""
	}
	// yzdata[index] = list
	isypcyl(list, index)
	selectMC(el, obj, index, iskyType)
	let yzlx = yzdata[index].YZLX

	// if (yp_dataop.yplb != 0) {
	//     return
	// }
	if (WRT_config.ypfjxx.jpyp == 1 || WRT_config.ypfjxx.jpyp == 2) {
		isjpyp(index, yzlx, obj)
	} else if (WRT_config.ypfjxx.tssysyl) {
		istssysyl(index, yzlx, obj)
	} else if (list.yp_dataop.gjcg) {
		isgjcg(index, yzlx, obj)
	} else if (WRT_config.ypfjxx.neednihss == 1) {
		isneednihss(index, yzlx, obj)
	} else if (yzlx == 'cydy' && (WRT_config.ypfjxx.kjyplx == 1 || WRT_config.ypfjxx.kjyplx == 2 || WRT_config.ypfjxx.kjyplx == 3)) {
		iskjyplx(index, yzlx, obj)
	} else if (WRT_config.ypfjxx.zoyptx) {
		iszoyptx(index, yzlx, obj)
	} else if (WRT_config.ypfjxx.etyy == 1 && (WRT_config.yz_sz.brnl <= WRT_config.yz_sz.etyynl)) {
		iseyyts(index, yzlx, obj)
	} else if (list.yp_dataop) {
		isZybxx(index, yzlx, obj)
	} else if (WRT_config.yz_sz.ptjzlywqx != 1 || WRT_config.yz_sz.xzjzlywqx != 1) {
		kzlywqx(index, yzlx, obj)
	} else if (WRT_config.ypfjxx.dataop) {
		dxypkz(index, yzlx, obj)
	} else if (WRT_config.ypfjxx.dataop) {
		mzypqx(index, yzlx, obj)
	} else if (WRT_config.ypfjxx.xsp) {
		isxsp(index, yzlx, obj)
	} else if (WRT_config.ypfjxx.kjyplx == "1" || WRT_config.ypfjxx.kjyplx == "2" || WRT_config.ypfjxx.kjyplx == "3") {
		istygzdm(index, yzlx, obj)
	} else if (WRT_config.ypfjxx.tzyp_isNeedBa == "1") {
		istzyp_isNeedBa(obj, index)
	} else {
		issfzf(obj, index)
	}
}
let gcptype=false //是否已执行
//gcp判断
function IsGcp(el, obj, key, type, iskyType){
	if (WRT_config.ypfjxx&& (WRT_config.ypsyff || !iskyType)&&!gcptype) {
		gcptype=true
		let ywbh=JSON.parse(WRT_config.ypfjxx.ywbh)
		if(obj.gcp==1&&(ywbh&&ywbh.length>0)){
			ypfjxx_modal={ el, obj, key, iskyType }
			WRT_config.ywbbh_Lists=ywbh||[]
			let temp = `
			<table id="tb_Xtbhdz" border="0" cellspacing="0" cellpadding="0">
				<tbody>
					<tr>
						<td width="140px">药物编号</td>
						<td width="140px">药品名称</td>
						<td width="100px">操作</td>
					</tr>
				${_.map(WRT_config.ywbbh_Lists, (obj, index) =>
						`<tr>
						<td width="140px">${obj.yaoWuBH||''}</td>
						<td width="140px">${obj.yaoPinMC}</td>
						<td width="100px"><button class="e_btn" onclick="dbYwbbh(${index})">确定</button></td>
					</tr>`
				).join('')}
				</tbody>
			</table>`
			WRT_e.ui.model({
				id: "id_ywbbh",
				title: "选择药物编号",
				width: "450px",
				content: temp,
				iframe: false,
				closeButton: false,
				closeOnEscape: false,
			})
		}else{
			ypway(el, obj, key, type, iskyType)
		}
	}
}

//“ICU-床旁CRRT
function icuCRRT(el, obj, key, iskyType){
	if(WRT_config.ZiFjxx.crrt==1){
		WRT_e.ui.model({
			id: "if_icuCRRT",
			title: "CRRT治疗",
			width: "1250px",
			iframeURL: `e-xyjhForm.html?blid=${WRT_config.url.as_blid}&yhid=${WRT_config.yz_sz.yhid}`,
			iframeHeight:900,
			closeButton: false,
			closeOnEscape: false,
			iframe: true,
		})
	}
	iszlxsp(el, obj, key, iskyType)
}
//治疗医嘱明细及社保限制范围
function iszlxsp(el, obj, key, iskyType){
	ZiFjxx_modal=null
	if(WRT_config.ZiFjxx.zlyzmxs){
		ZiFjxx_modal={ el, obj, key, iskyType }
		WRT_config.ZiFjxx.zlyzmxs.forEach((item,index)=>{
			if(item.SBXZFW&&item.SBXZFW.type==0){
				selectMC(el, obj, key, iskyType,true)
				return
			}else if(item.SBXZFW){
				let temp = `
				<div id="zlxsp_table">
						<span style="font-weight:bold;">项目名称：${item.SBXZFW.mingCheng}</span><br>
						<span id="Label1" style="font-weight:bold;">医保使用限制范围：</span><br>
						<ul style="height:130px;width:369px;border: 1px solid;overflow: auto;padding: 3px 5px;">
						<li><span>${item.SBXZFW.limit}</sapn></li>
						</ul>
						<br>
						<span id="Label2" style="font-weight:bold;">当前诊断</span>&nbsp;<br>
						<textarea name="tb_brzd" rows="2" cols="20" id="tb_brzd" disabled="true" style="height:74px;width:367px;">${item.SBXZFW.diagnose}</textarea><br>
						${item.SBXZFW.type==1?``:`<span style="display:inline-block;height:49px;width:372px;color:red">该项目须审批后才能记账。选中后该项目会自动插入审批队列，等待【社保窗口】审批</span><br>`}
						${item.SBXZFW.type==1?`<button class="e_btn" onclick="btn_zlxsp(false,0)">我知道了，符合条件</button>
						<button class="e_btn" onclick="btn_zlxsp(false,1)">不符合情况，自费</button>`:`
							<label for="sfzf_zf">
								<input id="sfzf_zf" type="radio" name="sfzf_xsp" value="0">普通记账
							</label>
							<label for="sfzf_jz">
								<input id="sfzf_jz" type="radio" name="sfzf_xsp" value="1">自费
							</label></br>
							<button class="e_btn" onclick="btn_zlxsp(true)">提交审批</button>
						`}
						&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
						<br>
					</div>
				`
				WRT_e.ui.model({
					id: "if_zlxsp",
					title: "医保限制支付范围提示",
					width: "650px",
					content: temp,
					closeButton: false,
					closeOnEscape: false,
					iframe: false,
				})
				return
			}else if(index>=WRT_config.ZiFjxx.zlyzmxs.length-1){
				selectMC(el, obj, key, iskyType,true)
			}
		})
	}else{
		selectMC(el, obj, key, iskyType,true)
	}
}
//治疗社保确定
function btn_zlxsp(list,value){
	let {el, obj, key, iskyType}=ZiFjxx_modal
	if(list){
		WRT_config.ZiFjxx.zlyzmxs.forEach(ev=>{
			if(ev.SBXZFW){
				let zf=$("input[name='sfzf_xsp']:checked").val()
				ev.ZIFEI=zf
				return
			}
		})
		selectMC(el, obj, key, iskyType,true)
		$('#if_zlxsp').iziModal('destroy')
	}else{
		WRT_config.ZiFjxx.zlyzmxs.forEach(ev=>{
			if(ev.SBXZFW){
				ev.ZIFEI=value
				return
			}
		})
		selectMC(el, obj, key, iskyType,true)
		$('#if_zlxsp').iziModal('destroy')
	}
}
//静脉营养药品
function isjpyp(index, yzlx, obj) {
	if (WRT_config.ypfjxx.jpyp == 1 || WRT_config.ypfjxx.jpyp == 2) {
		WRT_e.ui.message({
			title: '信息窗口',
			content: '须完成“营养风险筛查”（本次住院期间完成即可）',
			onOk() {
				istssysyl(index, yzlx, obj)
			}
		})
		return;
	}
	istssysyl(index, yzlx, obj)
	// istssysyl()
}
//国家采集提示
function istssysyl(index, yzlx, obj) {
	if (WRT_config.ypfjxx.tssysyl) {
		WRT_e.ui.message({
			title: '信息窗口',
			content: WRT_config.ypfjxx.tssysyl,
			onOk() {
				isgjcg(index, yzlx, obj)
				// isneednihss()
			},
		})
		return;
	}
	isgjcg(index, yzlx, obj)
}
//中选药品列表
function isgjcg(index, yzlx, obj) {
	if (yp_dataop.gjcg == 12 || yp_dataop.gjcg == 13 || yp_dataop.gjcg == 14) {
		WRT_e.api.yz_sz.getZxypByFzxyp({
			params: { al_fzxypid: obj.ypid, al_zkid: WRT_config.yz_sz.zkid, as_yfdm: yp_dataop.yfdm, as_yzlx: WRT_config.url.as_blid },
			success(data) {
				if (data.Code == 1 && data.Result) {
					let arr = JSON.parse(data.Result)
					WRT_config.gjcg = arr||[]
					if(WRT_config.gjcg.length>0){
						let temp = `
											<table id="tb_yplistdetail" border="0" width="600px" cellspacing="0" cellpadding="0">
													<tbody>
															<tr>
																	<td width="80px">选择</td>
																	<td width="250px">药品名称</td>
																	<td width="60px"></td>
																	<td width="80px">规格</td>
																	<td width="80px">单价</td>
																	<td width="120px">用法用量</td>
															</tr>
															${_.map(arr, (obj, index) =>
							`<tr>
																			<td width="80px">
																					<input type="button" class="e_btn" value="选择" onclick="selectgjcg(this,${index})" />
																			</td>
																			<td width="250px">${obj.MC||''}</td>
																			<td width="60px">${obj.GJCG||''}</td>
																			<td width="80px">${obj.GG||''}</td>
																			<td width="40px">${obj.SJ||''}</td>
																			<td width="80px">${obj.KZJB||''}</td>
																	</tr>`
						).join('')}
													</tbody>
											</table>`
						WRT_e.ui.model({
							id: "isgjcg",
							title: "中选药品列表",
							width: "650px",
							content: temp,
							iframe: false,
						})
					}

				}
			}
		})

	}
	isneednihss(index, yzlx, obj)
}
//中选药品选择
function selectgjcg(ev, index) {
	let list = WRT_config.gjcg[index]
	let obj = {
		lx: "药",
		mc: list.MC,
		ypid: list.YPID,
		gg: list.GG || "",
		sj: list.SJ || "",
		bz: list.BZ || "",
		dataop: list.DATAOP
	}
	yzlogic("MC" + yzindex, obj, yzindex, true)
	// let list = yzdata[yzindex]
	// for (let i in yzdata[yzindex]) {
	//     list[i] = ""
	// }
	// list.YZLX = yzdata[yzindex].YZLX
	// list.MC = obj.MC
	// list.GG = obj.GG
	// list.SJ = obj.SJ
	// list.LB = 3
	// yzdata[yzindex] = list
}
//是否需要填写NIHSS评分表
function isneednihss(index, yzlx, obj) {
	if (WRT_config.ypfjxx.neednihss == 1) {
		WRT_e.ui.message({
			title: '信息窗口',
			content: '必须完成美国国立卫生院神经功能缺损评分表(NIHSS)评分后才可开溶栓药',
			onOk() {
				delZHTXT(index)
				yzdata.splice(index, 1)
				let url = `${WRT_config.server}/pfbgl/pfbym.aspx?as_blid=${WRT_config.url.as_blid}&as_pfid=17&as_pfzhid=0&as_openmode=ehr3`
				parent.page_iframe.add("评分表", url)
			},
		})
	}
	iskjyplx(index, yzlx, obj)
}
//禁止出院带药开抗菌药物
function iskjyplx(index, yzlx, obj) {
	if (yzlx == 'cydy' && (WRT_config.ypfjxx.kjyplx == 1 || WRT_config.ypfjxx.kjyplx == 2 || WRT_config.ypfjxx.kjyplx == 3)) {
		WRT_e.api.yz_sz.checkYFYY({
			params: { al_blid: WRT_config.url.as_blid },
			success(data) {
				if (data.Result == 1) {

					WRT_e.ui.message({
						title: '信息窗口',
						content: '最后一次抗菌药物医嘱为预防用药，禁止出院带药开具抗菌药物！',
						onOk() {
							delZHTXT(index)
							yzdata.splice(index, 1)
						},
					})
				} else {
					iszoyptx(index, yzlx, obj)
				}
			}
		})
	} else {
		iszoyptx(index, yzlx, obj)
	}
}

//呕吐分级提醒
function iszoyptx(index, yzlx, obj) {
	if (WRT_config.ypfjxx.zoyptx) {
		WRT_e.ui.message({
			title: '信息窗口',
			content: WRT_config.ypfjxx.zoyptx,
			onOk() {
				iseyyts(index, yzlx, obj)
			},
		})
		return
	}
	iseyyts(index, yzlx, obj)
}

//儿童用药提示
function iseyyts(index, yzlx, obj) {
	if (WRT_config.ypfjxx.etyy == 1 && (WRT_config.yz_sz.brnl <= WRT_config.yz_sz.etyynl)) {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `药品名称是儿童${WRT_config.ypfjxx.etyyts}药，请谨慎使用此药。`,
			onOk() {
				isxsp(index, yzlx, obj)
			},
			onCancel() { }
		})
	} else {
		// kzlywqx(index, yzlx, obj)
		isZybxx(index, yzlx, obj)
	}
}
//中成药辩证
function isZybxx(index, yzlx, obj) {
	WRT_e.api.yz_sz.GetZybzxx({
		params: { al_ypid: obj.ypid },
		success(data) {
			if (data.Result && data.Result.zzxx.length > 0 && data.Result.zdxx.length > 0) {
				let lists = {}, list = {}
				data.Result.zdxx.map(function (item) {
					if (lists[item.ZH]) {
						lists[item.ZH].push(item)
					} else {
						lists[item.ZH] = [item]
					}
				})
				data.Result.zzxx.map(function (item) {
					if (list[item.ZH]) {
						list[item.ZH].push(item)
					} else {
						list[item.ZH] = [item]
					}
				})
				let arr = []
				for (let i in lists) {
					arr.push({ key: i, zdxx: lists[i] || [], zzxx: list[i] || [] })
				}
				let html = `<div id="dv_zybzxx" style="width: 586px; height: 587px;">
                    <button value="保存" onclick="saveZybzxx()">保存</button>
                    <button value="取消" onclick="CancelZybzxx()">取消</button>
                    <h1>药名：<label id="dv_zybzxx_ypmc">${obj.mc}</label></h1>
                    <table id="tb_zybzxx" style="width:580px;" border="1" cellspacing="0" cellpadding="0">
                        <caption></caption>
                        <thead>
                            <tr>
                                <th style="text-align: center;">组</th>
                                <th style="width:270px;text-align: center;">诊断</th>
                                <th style="width:270px;text-align: center;">症状</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${_.map(arr, (obj) =>
					`<tr>
                                <td style="text-align: center;">${obj.key}</td>
                                <td class="zd_list" style="padding-left: 5px">
                                    ${_.map(obj.zdxx, (k, index) =>
						`<input type="checkbox" name="zdzh_${index}" jlid="${k.JLID}" data-ZH="${k.ZH}" value="${k.ZDID}">${k.ZDMC}<br>`
					).join('')}
                                </td>
                                <td class="zz_list" style="padding-left: 5px;">
                                    ${_.map(obj.zzxx, (key, index) =>
						`<input type="checkbox" name="zzzh_${index}" jlid="${key.JLID}" data-ZH="${key.ZH}" value="${key.ZZID}">${key.ZZMC}<br>`
					).join('')}
                                </td>
                            </tr>`
				).join('')}
                            
                        </tbody>
                    </table>
                </div>`
				WRT_e.ui.model({
					id: "if_zybzxx",
					title: "中医辩证信息",
					width: "650px",
					content: html,
					iframe: false,
					closeButton: false,
					closeOnEscape: false,
				})
			} else {
				kzlywqx(index, yzlx, obj)
			}
		}
	})

}
//抗肿瘤药物的处方权限
function kzlywqx(index, yzlx, obj) {
	if (WRT_config.ypfjxx.kzpj == "1" && WRT_config.yz_sz.ptjzlywqx != "1") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `${obj.mc}是普通使用级抗肿瘤药物，你没有开具普通级肿瘤药物的权限！药品ID=${obj.ypid}`,
			onOk() {
				delZHTXT(index)
				yzdata.splice(index, 1)
				zhmbsrListDr()
			},
		})
		exportMBList()
		return
	} else if (WRT_config.ypfjxx.kzxj == "1" && WRT_config.yz_sz.xzjzlywqx != "1") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `${obj.mc}是限制使用级抗肿瘤药物，你没有开具限制级肿瘤药物的权限！药品ID=${obj.ypid}`,
			onOk() {
				delZHTXT(index)
				yzdata.splice(index, 1)
				zhmbsrListDr()
			},
		})
		exportMBList()
		return
	}
	dxypkz(index, yzlx, obj)
}
//毒性药品控制
function dxypkz(index, yzlx, obj) {
	if (yp_dataop.gllx == "D" && WRT_config.yz_sz.dxypqx != "0001") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `对不起，你没有毒性药品处方权限，无法开具药品【${obj.mc}】！药品ID=${obj.ypid} gllx=${yp_dataop.gllx}`,
			onOk() {
				delZHTXT(index)
				yzdata.splice(index, 1)
				zhmbsrListDr()
			},
		})
		exportMBList()
		return
	} else {
		mzypqx(index, yzlx, obj)
	}
}
//麻醉药品控制
function mzypqx(index, yzlx, obj) {
	if ((yp_dataop.gllx == "M" || yp_dataop.gllx == "1") && WRT_config.yz_sz.mzypqx != "1") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `对不起，你没有麻醉处方权限，无法开具药品【${obj.mc}】！药品ID=${obj.ypid} gllx=${yp_dataop.gllx}`,
			onOk() {
				delZHTXT(index)
				yzdata.splice(index, 1)
				zhmbsrListDr()
			},
		})
		exportMBList()
		return
	} else {
		iscfypd(index, yzlx, obj)
	}
}
//处方药判断
function iscfypd(index, yzlx, obj) {
	let list = null
	if (obj.dataop && obj.dataop.ypid) {
		list = obj.dataop
	} else if (obj.yp_dataop && obj.yp_dataop.gllx) {
		list = obj.yp_dataop
	} else if (obj.dataop) {
		list = getdataop(obj.dataop)
	}
	let fd = yzdata.filter(item => ((item.yp_dataop && item.yp_dataop.mzyp != 1) && item.cfy_Lists && item.cfy_Lists.length > 0))
	if (list && (list.gllx == '1' || list.gllx == 'M')) {
		cfy_func()
	} else if (yzlx == 'cydy' && (fd.length == 0 || !fd)) {
		cfy_func()
	}
	else {
		iscMzyt(index, yzlx, obj)
	}
	function cfy_func() {
		cfy_Lists = []
		WRT_e.api.yz_sz.GetBrzd({
			params: {
				al_blid: WRT_config.url.as_blid,
			},
			success(data) {
				if (data.Code == 1) {
					cfy_Lists = data.Result
					if (data.Result && data.Result.length == 0) {
						WRT_e.ui.message({
							title: '信息窗口',
							content: '未获取到大病历四或五的临床诊断!',
							onOk() {
								delZHTXT(index)
								yzdata.splice(index, 1)
								// isxsp(index, yzlx, obj)
								exportMBList()
								zhmbsrListDr()
							}
						})
						return
					}
					let html = "<div id='cfy_table'>"
					html += open_cfy(cfy_Lists)
					html += "</div>"
					WRT_e.ui.model({
						id: "if_cfy",
						title: "处方诊断",
						width: "650px",
						content: html,
						closeButton: false,
						closeOnEscape: false,
						iframe: false,
					})
				}
			}
		})
	}
}
//麻醉药选择用途
function iscMzyt(index, yzlx, obj) {
	let list = null
	if (obj.dataop && obj.dataop.ypid) {
		list = obj.dataop
	} else if (obj.yp_dataop && obj.yp_dataop.gllx) {
		list = obj.yp_dataop
	} else if (obj.dataop) {
		list = getdataop(obj.dataop)
	}
	if ((list.gllx == 'M')) {
		Mzyt_func()
	}
	else {
		isxsp(index, yzlx, obj)
	}
	function Mzyt_func() {
		WRT_e.api.yz_sz.GetMzyt({
			params: {
			},
			success(data) {
				if (data.Code == 1) {
					let arr = data.Result
					// WRT_config.Mzytlists=data.Result
					if (data.Result && data.Result.length == 0) {
						WRT_e.ui.message({
							title: '信息窗口',
							content: '未获取到麻醉药品用途!',
							onOk() {
								delZHTXT(index)
								yzdata.splice(index, 1)
								// isxsp(index, yzlx, obj)
								exportMBList()
								zhmbsrListDr()
							}
						})
						return
					}
					let html = `
                    <div>
                        <ul>
                        ${_.map(arr, obj =>
						`<label><input name="Mzyt_radio" type="radio" value="${obj.DM}" />${obj.MC}</label></br>`
					).join('')}
                        </ul>
                        <a-button class="e_btn" onclick="save_Mzyt()" style="padding-top: 4px;">保存</a-button>
                        <a-button class="e_btn" onclick="close_Mzyt()" style="padding-top: 4px;">关闭</a-button>
                    </div>
                    `
					WRT_e.ui.model({
						id: "if_Mzyt",
						title: "麻醉药品用途",
						width: "350px",
						content: html,
						closeButton: false,
						closeOnEscape: false,
						iframe: false,
					})
				}
			}
		})
	}
}
//药品审批
function isxsp(index, yzlx, obj) {
	let list = yzdata[index]
	// let html=ypsp_html(WRT_config.ypfjxx)
	// WRT_e.ui.model({
	//     id: "ypsp",
	//     title: "审批",
	//     width: "650px",
	//     content: html,
	//     closeButton: false,
	//     closeOnEscape: false,
	//     iframe: false,
	// })
	let yzlb = list.YZLB
	let yplb = list.YPLB
	if (list.yp_dataop && list.yp_dataop.yplb) {
		yplb = list.yp_dataop.yplb
	}
	if (list.yp_dataop && list.yp_dataop.yzlb) {
		yzlb = list.yp_dataop.yzlb
	}
	if (WRT_config.ypfjxx.existZysp == 1) {
		istygzdm(index, yzlx, obj)
		list.SFZF = 0
		list.SQSL = 0
		list.splb = 0
		yzdata[index] = list
		return
	} else if ((list.YZLB == "3" || list.yp_dataop.yzlb == "3") && list.ZHTXT) {
		// istygzdm(index, yzlx, obj)
		if (list.ZHTXT != '┐') {
			let XH = list.XH.split("-")[0]
			yzdata.map(function (item) {
				let t = item.XH
				if (typeof t == 'number') {
					t = JSON.stringify(t)
				}
				let td = t.split("-")
				let xh1 = td[0]
				if (xh1 == XH && obj.ypid && (list.YZLB == "3" || list.yp_dataop.yzlb == "3") && list.ZHTXT == "┐") {
					list.SFZF = item.SFZF || 0
					list.SQSL = item.SQSL || 0
					list.splb = item.splb || 0
					return
				}
			})
			yzdata[index] = list
			exportMBList()
			return
		}
	}
	//// 外购药品
	// WRT_config.ypfjxx.xzfw=null
	// WRT_config.ypfjxx.xsp = '1'
	if (yplb == 1) {
		
		if (WRT_config.ypfjxx.xsp == '1') {
			if(WRT_config.ypfjxx.xzfw==null){
				list.SFZF = 0
				let html = ypsp_html(WRT_config.ypfjxx,list)
				// let html = ypsp_html(WRT_config.ypfjxx)
				WRT_e.ui.model({
					id: "if_ypsp",
					title: "医保限制支付范围提示",
					width: "650px",
					content: html,
					closeButton: false,
					closeOnEscape: false,
					iframe: false,
				})
				// list.SQSL = 1
				// list.splb = WRT_config.ypfjxx.xsp
				// yzdata[index] = list
			} else{
				if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
					let html = ypsp_html(WRT_config.ypfjxx)
					WRT_e.ui.model({
						id: "if_ypsp",
						title: "医保限制支付范围提示",
						width: "650px",
						content: html,
						closeButton: false,
						closeOnEscape: false,
						iframe: false,
					})
				} else if (WRT_config.ypfjxx.xzfw_bbzt == 2) {
					let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.yz_sz.jslx}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=${yzindex}`
					// page_iframe.add("审批", url)
					// yzdata.splice(index, 1)
					WRT_e.ui.model({
						id: "ypsp",
						title: "医保限制支付范围提示",
						width: "650px",
						iframeURL: url,
						closeButton: false,
						closeOnEscape: false,
						iframe: true,
					})
					$("#ypsp iframe").load(function () {
						$("#ypsp iframe").contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
						$("#ypsp iframe").contents().find("#tb_brzd").text(WRT_config.yz_sz.zd);
					})
				}
			}
			return
		} else {
			//WRT_config.ypfjxx.xsp != '1' 回参增加fstdypwgts字段，如果外购药品xsp!=“1”，增加弹窗提醒fstdypwgts字段的内容。
			// WRT_e.ui.message({
			// 	title: '信息窗口',
			// 	content: WRT_config.ypfjxx.fstdypwgts,
			// 	onOk() {
			// 	}
			// })
			SetSpjg({ sfzf: '1', spsl: 0, splb: 0, xdfw: '' },true)
			return
		}
		istygzdm(index, yzlx, obj)
	} else
		//本院药品审批，临床路径就是走这里
		//自费或者住院未开通
		// if (WRT_config.yz_sz.jslx == '00' || WRT_config.yz_sz.zykt == 0) {
			//强制审批
			// if (WRT_config.ypfjxx.xsp == '2' || WRT_config.ypfjxx.xsp == '3') {
			// 	let as_sbxzfw = escape(WRT_config.ypfjxx.xzfw)
			// 	let as_brzd = escape(WRT_config.yz_sz.zd)
			// 	if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
			// 		let html = ypsp_html(WRT_config.ypfjxx)
			// 		WRT_e.ui.model({
			// 			id: "if_ypsp",
			// 			title: "医保限制支付范围提示",
			// 			width: "650px",
			// 			content: html,
			// 			closeButton: false,
			// 			closeOnEscape: false,
			// 			iframe: false,
			// 		})
			// 	} else if (WRT_config.ypfjxx.xzfw_bbzt == 2) {
			// 		let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.yz_sz.jslx}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=${yzindex}`
			// 		// page_iframe.add("审批", url)
			// 		// yzdata.splice(index, 1)
			// 		WRT_e.ui.model({
			// 			id: "ypsp",
			// 			title: "医保限制支付范围提示",
			// 			width: "650px",
			// 			iframeURL: url,
			// 			closeButton: false,
			// 			closeOnEscape: false,
			// 			iframe: true,
			// 		})
			// 		$("#ypsp iframe").load(function () {
			// 			$("#ypsp iframe").contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
			// 			$("#ypsp iframe").contents().find("#tb_brzd").text(WRT_config.yz_sz.zd);
			// 		})
			// 	}
			// 	return;
			// }
		// } else 
		{
			let xzfw_type = ''
			if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
				xzfw_type = '[]'
			} else if (WRT_config.ypfjxx.xzfw_bbzt == 2) {
				xzfw_type = ''
			}
			if (WRT_config.ypfjxx.xsp == "0" && WRT_config.ypfjxx.xzfw == xzfw_type) {//xzfw==[]
				if (WRT_config.yz_sz.jslx!='00'&& yp_dataop.kzjb.indexOf('自费') >= 0 && parseFloat(yp_dataop.xzdj) > 2) {
					WRT_e.ui.message({
						title: '信息窗口',
						content: '该药为自费药品,请务必告知病人签字后方可使用!',
						onOk() {
							istygzdm(index, yzlx, obj)
						}
					})
					return;
				}
			}
			else if (WRT_config.ypfjxx.xsp == '0' && WRT_config.ypfjxx.xzfw != xzfw_type && (WRT_config.ypfjxx.zhbz == "1300" && (yzlb == "1" || yzlb == "3")) ) {
				//无需审批
				istygzdm(index, yzlx, obj)
				return;
			}
			else {
				let as_sbxzfw = escape(WRT_config.ypfjxx.xzfw)
				let as_brzd = escape(WRT_config.yz_sz.zd)
				if (WRT_config.ypfjxx.xzfw_bbzt == 1) {
					let html = ypsp_html(WRT_config.ypfjxx)
					WRT_e.ui.model({
						id: "if_ypsp",
						title: "医保限制支付范围提示",
						width: "650px",
						content: html,
						closeButton: false,
						closeOnEscape: false,
						iframe: false,
					})
				} else if (WRT_config.ypfjxx.xzfw_bbzt == 2) {
					let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.yz_sz.jslx}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=${yzindex}`
					// page_iframe.add("审批", url)
					WRT_e.ui.model({
						id: "ypsp",
						title: "医保限制支付范围提示",
						width: "650px",
						iframeURL: url,
						closeButton: false,
						closeOnEscape: false,
						iframe: true,
					})
					$("#ypsp iframe").load(function () {
						$("#ypsp iframe").contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
						$("#ypsp iframe").contents().find("#tb_brzd").text(WRT_config.yz_sz.zd);
					})
				}

				return
			}
	}
	istygzdm(index, yzlx, obj)
	// let type=setFylx(WRT_config.yz_sz.jslx,WRT_config.yz_sz.zykt,WRT_config.yz_sz.sfzf,yp_dataop.kzjb)
	// yzdata[index].ZF=type
}
//抗菌药物限制
function istygzdm(index, yzlx, obj) {
	//抗菌药品
	if (WRT_config.ypfjxx.kjyplx == "1" || WRT_config.ypfjxx.kjyplx == "2" || WRT_config.ypfjxx.kjyplx == "3") {
		var tygzdm = "";//通用工种代码
		var tygzmc = "";//职位
		//没有主任医生，行政主任可使用特殊抗生素
		switch (WRT_config.yz_sz.gzdm) {//医生工种（职称）
			case "0010"://市级名中医参照主任医师的抗生素级别
			case "0011"://主任医师
			case "0381"://主任中医师
				tygzdm = "0011";//主任医师
				tygzmc = "主任医师";
				break;
			case "0382"://副主任中医师
			case "0012"://副主任医师
				tygzdm = "0012";//副主任医师
				tygzmc = "副主任医师";
				break;
			case "0383"://主治中医师
			case "0013"://主治医师
				tygzdm = "0013";//主治医师
				tygzmc = "主治医师";
				break;
			case "0384"://中医师
			case "0014"://住院医师
				tygzdm = "0014";//住院医师
				tygzmc = "住院医师";
				break;
			case "0385"://中医士
			case "0015"://医士
				tygzdm = "0015";//医士 级别5
				tygzmc = "医士";
			default:
				tygzdm = WRT_config.yz_sz.gzdm
				break;
		}
		if (tygzdm == "" || tygzdm == "0000") {
			WRT_e.ui.message({
				title: '信息窗口',
				content: '工种代码未设定,无法开具抗生素类药品！',
				onOk() {
					delZHTXT(index)
					yzdata.splice(index, 1)
					zhmbsrListDr()
					exportMBList()
				}
			})
		} else {
			if (WRT_config.ypfjxx.kjyplx == 1) {
				//非限制使用级抗菌药物，各级医师都可以开
				iskjypgl(obj)
			} else if (WRT_config.ypfjxx.kjyplx == 2) {//限制使用级抗菌药物
				//副高及以上职称
				//无副高及以上职称的科室，科室主任职务
				//特殊科室（感染科、血液内科、急诊科、呼吸内科、ICU和CCU）的医师，中级及以上职称
				if ((tygzdm == "0012" || tygzdm == "0011" || WRT_config.yz_sz.existFgzc == "0" && WRT_config.yz_sz.isZxzr == "1") || (WRT_config.yz_sz.grzkbz == "1" && tygzdm == "0013")) {
					iskjypgl(obj)
				}
				else {
					WRT_e.ui.message({
						title: '信息窗口',
						content: "你的职称（" + tygzmc + "）无法开具限制使用级抗菌药物。根据规定，副高及以上职称才能开具限制使用级抗菌药物！",
						onOk() {
							delZHTXT(index)
							yzdata.splice(index, 1)
							zhmbsrListDr()
							exportMBList()
						}
					})
				}
			} else if (WRT_config.ypfjxx.kjyplx == 3) {//特殊使用级抗菌药物
				if (yzlx == 'cq') {
					//正高级职称
					//无正高职称医师的科室，科室主任职务
					//特殊科室（感染科、血液内科、急诊科、呼吸内科、ICU和CCU）的医师，副高及以上职称
					if (tygzdm == "0011" || (WRT_config.yz_sz.existZgzc == "0" && WRT_config.yz_sz.isZxzr == "1") || (WRT_config.yz_sz.grzkbz == "1" && tygzdm == "0012")) {
						if (WRT_config.ypfjxx.hzdid == 0) {
							WRT_e.ui.message({
								title: '信息窗口',
								content: "请先进行特殊类抗生素药物会诊单会诊通过后再进行医嘱的下达。",
								onOk() {
									delZHTXT(index)
									yzdata.splice(index, 1)
									zhmbsrListDr()
									exportMBList()

								}
							})
						}
						else {
							iskjypgl(obj)
						}
					}
					else {
						WRT_e.ui.message({
							title: '信息窗口',
							content: "你的职称（" + tygzmc + "）无法开具特殊使用级抗菌药物。长期医嘱中，正高级职称才能开具特殊使用级抗菌药物！",
							onOk() {
								delZHTXT(index)
								yzdata.splice(index, 1)
								zhmbsrListDr()
								exportMBList()
							}
						})
					}
				} else {
					//临时
					//副高及以上职称
					//无副高及以上职称的科室，科室主任职务
					//特殊科室（感染科、血液内科、急诊科、呼吸内科、ICU和CCU）的医师，中级及以上职称
					if (tygzdm == "0012" || tygzdm == "0011" || (WRT_config.yz_sz.existFgzc == "0" && WRT_config.yz_sz.isZxzr == "1") || (WRT_config.yz_sz.grzkbz == "1" && tygzdm == "0013") || WRT_config.yz_sz.zkid == 49) {
						if (WRT_config.ypfjxx.hzdid == 0) {
							let url = `${WRT_config.server}/zyblhzd/tskjywhzd.aspx?as_blid=${WRT_config.url.as_blid}&as_ypid=${obj.ypid}&as_rowindex=${yzindex}&as_version=djm&as_tmpid=${Math.random()}`
							// page_iframe.add("会诊单", url)
							WRT_e.ui.model({
								id: "kjyplx",
								title: "会诊单",
								width: "850px",
								iframeURL: url,
								closeButton: false,
								closeOnEscape: false,
								iframeHeight: '800px',
								iframe: true,
							})
						}
						else {
							iskjypgl(obj)
						}
					}
					else {
						WRT_e.ui.message({
							title: '信息窗口',
							content: "你的职称（" + tygzmc + "）无法开具特殊使用级抗菌药物。临时医嘱中，副高及以上职称才能开具特殊使用级抗菌药物！",
							onOk() {
								delZHTXT(index)
								yzdata.splice(index, 1)
								zhmbsrListDr()
								exportMBList()
							}
						})
					}
				}
			}
		}
	} else {
		istzyp_isNeedBa(obj)
	}
}
//选择抗菌药物使用方法
function iskjypgl(obj) {
	let arr =	yzdata.filter(item=>{
		// item.YPID? item.kjypsyly?item.kjypsyly.SYFF =='2':'' : item.KJYP_SYFF =='2' || item.kjyp_syff =='2'
		if (item.YPID) {
			if (item.kjypsyly && item.kjypsyly.SYFF =='2') {
				return item
			}
		} else {
			if (item.KJYP_SYFF =='2' || item.kjyp_syff =='2') {
				return item
			}
		}
	})
	let as_zlyps = ''
	if (arr.length!=0) {
		let zlypsArr =[]
		arr.forEach(item=>{
			if (item.YPID) { // 新增药品
				if (item.kjypsyly.SYFF =='2') {
					zlypsArr.push(item.YPID)
				}
			} else { // 已保存药品
				if (item.KJYP_SYFF =='2' || item.kjyp_syff =='2') {
					zlypsArr.push(item.XMID)
				}
			}
		})
		if (zlypsArr.length>0) {
			as_zlyps = zlypsArr.join('^')
		} else {
			as_zlyps = zlypsArr[0]
		}
	}

	let url = `${WRT_config.server}/xypyz/kjypgl.aspx?as_kjjb=${WRT_config.ypfjxx.kjyplx}&as_ypid=${obj.ypid}&as_blid=${WRT_config.url.as_blid}&as_zkid=${WRT_config.yz_sz.zkid}&as_zyid=${WRT_config.yz_sz.zyid}&as_yzlx=${WRT_config.url.as_yzlb}&as_version=djm&as_rowindex=${yzindex}&as_zlyps=${as_zlyps}`
	WRT_e.ui.model({
		id: "kjypgl",
		title: "抗菌药物使用方法",
		width: "650px",
		iframeHeight: '800px',
		closeButton: false,
		closeOnEscape: false,
		iframeURL: url,
		iframe: true,
	})
	// istzyp_isNeedBa(obj)
}
//特治特药备案
function istzyp_isNeedBa(obj) {
	if (WRT_config.ypfjxx.tzyp_isNeedBa == "1") {
		let url = `${WRT_config.server}/xypyz/tztybab.aspx?as_brbh=${WRT_config.yz_sz.brbh}&as_ypid=${obj.ypid}&as_yhid=${WRT_config.yz_sz.yhid}&as_zkid=${WRT_config.yz_sz.zkid}&as_version=djm&tmpid="${Math.random()}`
		WRT_e.ui.model({
			id: "NeedBa",
			title: "特治特药备案",
			width: "850px",
			iframeHeight: '850px',
			closeButton: false,
			closeOnEscape: false,
			iframeURL: url,
			iframe: true,
		})
		return
	}
	issfzf(obj)
	// page_iframe.add("特治特药备案", url)
}
//特殊病备案
function issfzf(obj) {
	let type = WRT_config.yz_sz.sfzf
	if (yzdata[yzindex].ZF != undefined) {
		type = yzdata[yzindex].ZF
	}
	if (type == '0' && WRT_config.yz_sz.cwjsdmsx && WRT_config.yz_sz.cwjsdmsx.substr(0, 1) != "0" && WRT_config.yz_sz.jslx == '01') {
		let fd = WRT_config.SbXlzfYp.filter(item => item.YPID == obj.ypid)
		if (fd && fd.length > 0) {
			let list = fd[0]
			WRT_e.api.yz_sz.getBrxzqh({
				params: { al_zyid: obj.zyid },
				success(data) {
					if (data.Result == 1) {
						let url = `${WRT_config.server}/xypyz/tsywba.aspx?as_xmlb=${list.flm}&as_version=djm&tmpid=" + ${Math.random()}`
						// page_iframe.add("特殊病备案", url)
						WRT_e.ui.model({
							id: "cwjsdmsx",
							title: "特殊病备案",
							width: "850px",
							iframeHeight: '850px',
							closeButton: false,
							closeOnEscape: false,
							iframeURL: url,
							iframe: true,
						})
					}
				}
			})
			return
		}
	}
	if (ZhmbSrdata && ZhmbSrdata.length > 0 && ZhmbSrindex < ZhmbSrdata.length) {
		ZhmbSrindex++
		zhmbsrDr(ZhmbSrindex)
		return
	}
	exportMBList()
}
//模板导入队列
function exportMBList() {
	if (WRT_config.mbdr && MBDRList.length > 0) {
		if (mbindex <= MBDRList.length - 1) {
			mbindex++
			GetYpMbMx(MBDRList[mbindex], mbindex)
			if (mbindex == MBDRList.length - 1) {
				MBDRList = []
				mbindex = 0
			}
		}
	}
}
//执行逻辑导入队列
function exportYZ_logic() {
	if (YZ_logicList.length > 0) {
		if (YZ_logicindex <= YZ_logicList.length - 1) {
			YZ_logicindex++
			let list = YZ_logicList[YZ_logicindex]
			yzlogic(list.el, list.obj, list.key)
			if (YZ_logicindex == YZ_logicList.length - 1) {
				YZ_logicList = []
				YZ_logicindex = -1;
			}
		} else {
			YZ_logicindex = -1;
			YZ_logicList = []
		}
	}
}

//校验重复用药
function CheckCfyz(j_val, j_yzmc) {
	var j_id, j_mc;
	var j_cfyp = yzdata[yzindex].CFYP//1为可重复药品
	var j_rmyp = yzdata[yzindex].RMYP//1为溶媒药品
	//开具溶媒类药品，要求不要弹出重复药的提示
	if (j_rmyp == "1" || j_cfyp == "1") {
		//不弹出重复药品提醒
	}
	else {
		for (var i = 0, cnt = yzdata.length; i < cnt; i++) {//新开医嘱
			if (i == yzindex)
				continue;
			j_id = yzdata[yzindex].ypid
			j_mc = yzdata[yzindex].MC
			if (j_id == j_val && j_mc == j_yzmc) {
				return "[" + j_yzmc + "]存在重复医嘱!";
			}
		}
		for (var i = 0, c = WRT_config.BrYz.length; i < c; i++) {//在用医嘱
			if (WRT_config.BrYz[i] == j_val) {
				WRT_e.ui.message({
					title: '信息窗口',
					content: "[" + j_yzmc + "]存在重复医嘱!",
					onOk() {
					}
				})
				break;//【Q092】同一个药品医嘱，开n次，开n+1次时，会弹框n次
			}
		}
	}
	return "";
}
//药物过敏判断
function CheckYwgm(ypid) {
	let _YWGM = WRT_config.ywgm
	let gmlx, jwyplx;
	let ls_rtn = "";
	if (_YWGM == null) return "";
	for (let i = 0; i < _YWGM.length; i++) {
		if (_YWGM[i].YPID == ypid || _YWGM[i].FZQYPID == ypid) {
			if (_YWGM[i].GMJG == null) {
				gmlx = _YWGM[i].GMYPLX;
				jwyplx = _YWGM[i].JWYPLX
				for (j = 0; j < _YWGM.length; j++) {
					if ((_YWGM[j].GMYPLX == gmlx || _YWGM[j].GMYPLX == jwyplx) && _YWGM[j].GMJG != null) {
						if (_YWGM[j].GMSJ == null) {
							if (_YWGM[j].GMYPLX == "01")
								ls_rtn = "患者曾经对[" + _YWGM[j].MC + "]药品皮试阳性,请慎重考虑!";
							else
								ls_rtn = "患者对[" + _YWGM[j].MC + "]药品皮试阳性,请慎重考虑!";
						} else {
							if (_YWGM[j].GMYPLX == "01")
								ls_rtn = "患者曾经对[" + _YWGM[j].MC + "]药品皮试阳性,请慎重考虑!";
							else
								ls_rtn = "患者于" + _YWGM[j].GMSJ + "时对[" + _YWGM[j].MC + "]药品皮试阳性,请慎重考虑!";
							break;
						}
					}
				}
			} else if (_YWGM[i].GMJG == "1") {
				if (_YWGM[i].GMSJ == null)
					if (_YWGM[i].GMYPLX == "01")
						ls_rtn = "患者曾经对[" + _YWGM[i].MC + "]药品皮试阳性,青霉素类过敏,请慎重考虑!";
					else
						ls_rtn = "患者曾经对[" + _YWGM[i].MC + "]药品皮试阳性,请慎重考虑!";
				else
					if (_YWGM[i].GMYPLX == "01")
						ls_rtn = "患者于" + _YWGM[i].GMSJ + "时对[" + _YWGM[i].MC + "]药品皮试阳性,请慎重考虑!";
					else
						ls_rtn = "患者于" + _YWGM[i].GMSJ + "时对[" + _YWGM[i].MC + "]药品皮试阳性,请慎重考虑!";
			}
		}
	}
	return ls_rtn;
}
//中成药分餐
function zcyfc(rowIdx, yzlx, lb, ypmc, yzlb, cqcy) {
	if ((lb == 'yp' || lb == 3) && yzlb == '2') {//中成药
		//长期医嘱：可以开目录内的（可以分餐的）药品
		//临时医嘱：可以开目录外的（不能分餐的）药品
		//出院带药：可以开所有的中成药
		if (cqcy == '0' && yzlx != "cydy") {
			// $.messager.alert("提示", "[" + ypmc + "] 不是中成药分餐药品，只能开出院带药！", 'error');
			WRT_e.ui.message({
				title: '信息窗口',
				content: "[" + ypmc + "] 不是中成药分餐药品，只能开出院带药！",
				onOk() {
				}
			})
			return false;
		}
		if (cqcy == '1' && yzlx != "cq" && yzlx != "cydy") {//1=目录内
			// DelX(_YZ_CURRENTROW);
			// $.messager.alert("提示", "[" + ypmc + "] 是中成药分餐目录内药品，只能开长期医嘱或出院带药！", 'error');
			WRT_e.ui.message({
				title: '信息窗口',
				content: "[" + ypmc + "] 是中成药分餐目录内药品，只能开长期医嘱或出院带药！",
				onOk() {
				}
			})
			return false;
		}
		if (cqcy == '2' && yzlx != "ls" && yzlx != "cydy") {//2=目录外可用
			// DelX(_YZ_CURRENTROW);
			// $.messager.alert("提示", "[" + ypmc + "] 是中成药分餐目录外可用药品，只能开临时医嘱或出院带药！", 'error');
			WRT_e.ui.message({
				title: '信息窗口',
				content: "[" + ypmc + "] 是中成药分餐目录外可用药品，只能开临时医嘱或出院带药！",
				onOk() {
				}
			})
			return false;
		}
	}
	return true;
}
//获取费用类型
function getFylx(obj) {
	let zl=[]
	if(obj.ZLYZMXS){
		zl=JSON.parse(obj.ZLYZMXS)
	}else if(obj.zlyzmxs){
		zl=JSON.parse(obj.zlyzmxs)
	}
	// console.log('获取费用类型',obj,obj.KZJB);if(obj.YPLB == "1") { // 外购药品 YPLB == "1" 打印不出来不确定是大写还是小写
	let type = setFylx(obj.jslx || WRT_config.yz_sz.jslx, obj.zykt || WRT_config.yz_sz.zykt, obj.ZF, obj.KZJB,obj.YPLB,obj.XSP,obj.LB,zl)
	return type
}
//设置费用类型
function setFylx(jslx, zykt, sfzf, kzjb,YPLB,XSP,lb,zlyzmxs) {
	let type = 0 
	// 设置费用类型（YPLB）
	if(lb == '3'){//药品
		if (YPLB == "1") {
				if (XSP == "1" && sfzf == "0") {
						type = "甲类" //写死
			} else {
						type = 1 //自费
			}
		} else {
			if (zykt == "0")
				type = 1
			else if (jslx == "00")
				type = 1
			else if (sfzf == "1")
				type = 1
			else {
				type = kzjb
			}
		}
	}else if(lb == 1){//治疗
		if (jslx == "00")//自费病人
			type = 1;
		else //社保病人
		{
			//有部分自费的 显示:记账(含自费)
			//全自费 显示:自费
			//全记账 显示: 记账
			zlyzmxs = zlyzmxs||[]
			let ll_zlyzmx_zf_cnt = zlyzmxs.filter(o=>o.ZIFEI==1)
			let ll_zlyzmx_jz_cnt = zlyzmxs.filter(o=>o.ZIFEI==0)
			if (ll_zlyzmx_zf_cnt&&ll_zlyzmx_zf_cnt.length==0||!ll_zlyzmx_zf_cnt)
				type = 0;
			else if (ll_zlyzmx_jz_cnt&&ll_zlyzmx_jz_cnt.length==0||!ll_zlyzmx_jz_cnt)
				type = 1;
			else
				type = "记账(含自费)";
		}
	}
	return type
}
//模板选中
function selectMB(ev, list, i) {
	let key = yzdata.length - 1
	let obj = {
		YZLB: WRT_config.url.as_yzlb,
		MC: list.MC,
		JLDW: list.JLDW,
		YCYL: list.YCYL,
		ZXPL: list.PL,
		GYSJ: list.GYSJ,
		ZXFF: list.FF,
		GG: list.YPGG,
		SJ: list.SJ,
		LB: 3
	}
	if (yzdata[key].YZLB != RT_config.url.as_yzlb && !yzdata[key].MC) {
		yzdata.splice(key, 0, obj);
	} else {
		yzdata[key] = obj
	}
}

//
function deal_yzdata() {
	let arr = [], type = true
	yzdata.map(function (item) {
		if (item.MC) {
			type = true
			arr.push(item)
		} else {
			if (item.ZHTXT) {
				if (item.ZHTXT == '┐') {
					type = false
				} else if (item.ZHTXT == '┘' && type) {
					let zh = arr[arr.length - 1].ZHTXT
					if (zh == '┐') {
						arr[arr.length - 1].ZHTXT = ''
					} else {
						arr[arr.length - 1].ZHTXT = '┘'
					}
				} else if (type) {
					let zh = arr[arr.length - 1].ZHTXT
					if (zh == '┐') {
						arr[arr.length - 1].ZHTXT = ''
					}
				}
			}
		}
	})
	return arr
}

let isSubbmit = false //防止多次点击
//保存功能baocun
function getyzData(type) {
	if(isSubbmit){
		return
	}
	isSubbmit = true
	keyCode = ""
	let ast_zxbq = []
	var rtn = true;
	var zxff_type = false
	Tbmjtype = false
	// let array=deal_yzdata()
	let ar = yzdata.filter(e => e.LB == 3)
	if (WRT_config.yz_sz.zkid == 49 && ar && ar.length > 0 && WRT_config.url.as_yzlb == "cq") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `急诊抢救长期医嘱不能开药品`,
			onOk() {
			},
		})
		isSubbmit = false
		return
	}
	// if (WRT_config.url.as_lclj == 1 && WRT_config.url.as_yzlb == "cq" && type == "submit") {
	// 	yz_submit = []
	// 	yz_Lbmit = []
	// 	let msg = true
	// 	yzdata.forEach(item => {
	// 		if (item.check || item.check == undefined) {
	// 			yz_submit.push(item.YZID)
	// 			if (item.LB == 3) {
	// 				yz_Lbmit.push(item.YZID)
	// 			}
	// 			if ((item.LB != 3) && !item.YZID) {
	// 				msg = false
	// 			}
	// 		}
	// 	})
	// 	if (msg) {
	// 		getSumTbmj()
	// 	} else {
	// 		let tn = true, name = "";
	// 		if (WRT_config.url.as_lclj == 1 && WRT_config.url.as_yzlb == "cq") {
	// 			yzdata.map(function (item) {
	// 				if (item.LB == 3) {
	// 					tn = false
	// 					return
	// 				}
	// 				if (item.LB == 1) {
	// 					name = item.MC
	// 				}
	// 			})
	// 		}
	// 		if (tn) {
	// 			getyzData('savesubmit')
	// 		} else {
	// 			WRT_e.ui.message({
	// 				title: '信息窗口',
	// 				content: `${name}未保存，无法提交`,
	// 				onOk() {
	// 				},
	// 			})
	// 		}
	// 	}
	// 	return
	// }
	if (WRT_config.url.as_yzlb == "cq" && WRT_config.yz_sz.bqryrq == "") {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `病人未病区入院，不能开长期医嘱，可以开临时、化验、特检医嘱！`,
			onOk() {
			}
		})
		rtn = false
		isSubbmit = false
		return
	}
	let arr = [], as_delid = ""
	if (as_delidlist.length > 0) {
		as_delid = as_delidlist.join(",")
	}
	let as_grzd = '', ao_tsywba = {}, yp_dataop = {}, listsP = {}
	let YPDSBZNum = 0, YPDSBZRtn = false
	arr_zgyq={
		Drugs:[],
		Charges:[]
	}
	let arr_sskg=[]
	yzdata.map(function (item) {
		if (!rtn) {
			isSubbmit = false
			return
		}
		if (item.MC) {
			//
			if(item.JINRILT==1){
				if(dealtime(item.KSSJ_TXT)!=dealtime()){
					WRT_e.ui.message({
						title: '信息窗口',
						content: `【${item.MC}】计划开始时间非当日时不允许勾【今日临停】！`,
						onOk() {
						}
					})
					rtn = false
					return
				}
				function dealtime(val){
					var d
					if (!val) {
						d = new Date();
					} else {
						d = new Date(val)
					}
					var year = d.getFullYear();
					var month = change(d.getMonth() + 1);
					var day = change(d.getDate());
					var time= year + '-' + month + '-' + day
					function change(t) {
						if (t < 10) {
							return "0" + t;
						} else {
							return t;
						}
					}
					return time
				}

			}
			YPDSBZNum = 0
			if((item.YZLX == 'cy' || item.YZLX == 'zcydy')&&(item.YCYL==0||item.YCYL=='')){
				WRT_e.ui.message({
					title: '信息窗口',
					content: `草药的一次用量不能为空和0`,
					onOk() {
					}
				})
				rtn = false
				return
			}
			if (item.ZHTXT) {
				let cy = false, cunum = false, SFSLtype = false
				let th1 = "", th2 = ""
				if (item.YZLX == 'cy' || item.YZLX == 'zcydy') {
					th1 = item.XH.split("-")[0]
				} else {
					th1 = parseInt(item.XH)
				}
				yzdata.forEach(function (e) {
					if (e.YPDSBZ || e.ypdsbz) {
						YPDSBZNum++
						if (YPDSBZNum >= 2) {
							YPDSBZRtn = true
						}
					}
					if (e.YZLX == 'cy' || e.YZLX == 'zcydy') {
						th2 = e.XH.split("-")[0]
					} else {
						th2 = parseInt(e.XH)
					}
					if ((th1 == th2) && (item.LB == "3")) {
						if (item.ZXFF != e.ZXFF || item.ZXPL != e.ZXPL || item.GYSJ != e.GYSJ) {
							if (cy) {
								return
							}
							WRT_e.ui.message({
								title: '信息窗口',
								content: `用法、频率、用药时间必须与同组药品保持一致`,
								onOk() {
								}
							})
							cy = true
							rtn = false
							return
						} else if (item.SFSL != e.SFSL && (item.SFSL != '' && e.SFSL != '') && (item.YZLX == 'cy' || item.YZLX == 'zcydy')) {
							if (cunum) {
								return
							}
							WRT_e.ui.message({
								title: '信息窗口',
								content: `草药的数量必须与同组药品保持一致！`,
								onOk() {
								}
							})
							cunum = true
							rtn = false
							return
						} else if (item.JRCY != e.JRCY  && (item.YZLX == 'zcydy'||item.YZLX == 'cydy')) {
							if (cunum) {
								return
							}
							WRT_e.ui.message({
								title: '信息窗口',
								content: `预计出院必须与同组药品保持一致！`,
								onOk() {
								}
							})
							cunum = true
							rtn = false
							return
						} else if ((!item.SFSL || !e.SFSL) && item.YZLX == 'cydy') {
							if (SFSLtype) {
								return
							}
							WRT_e.ui.message({
								title: '信息窗口',
								content: `出院带药数量不可为空`,
								onOk() {
								}
							})
							SFSLtype = true
							rtn = false
						}
					}
				})
			} else {
				if ((!item.SFSL || item.SFSL == 0) && item.YZLX == 'cydy') {
					WRT_e.ui.message({
						title: '信息窗口',
						content: `出院带药数量不可为空`,
						onOk() {
						}
					})
					rtn = false
				}
			}
			if (item.kjypsyly) { as_grzd = item.kjypsyly.GRZD }
			if (item.yp_dataop) { yp_dataop = item.yp_dataop || {} }
			if (item.tsywba) {
				ao_tsywba = item.tsywba
				ao_tsywba.YPID = ao_tsywba.YPID || item.ypid || ''
			}
			// if (WRT_config.url.as_lclj == 1 && type == 'save' && item.LB == 3 && WRT_config.url.as_yzlb == "cq") {
			// 	WRT_e.ui.message({
			// 		title: '信息窗口',
			// 		content: `病人已进入临床路径，不能在长期医嘱里开药品医嘱，只能提交！`,
			// 		onOk() {
			// 		}
			// 	})
			// 	rtn = false
			// 	return
			// }
			if (WRT_config.url.as_yzlb == "cq") {//长期医嘱页面
				if (yp_dataop.yzlb == "3" || item.YZLB == "3") { //草药
					WRT_e.ui.message({
						title: '信息窗口',
						content: `${item.MC}是草药，长期医嘱里不能开草药！`,
						onOk() {
						}
					})
					rtn = false
					return
				}
			} else {
				if (yp_dataop.yzlb == "3" || item.YZLB == "3") { //草药
					if (item.YZLX != 'zcydy' && item.YZLX != 'cy') {
						WRT_e.ui.message({
							title: '信息窗口',
							content: `${item.MC}是草药，请选择正确的医嘱类型（草药医嘱、草药带药）！`,
							onOk() {
							}
						})
						rtn = false
						return
					}
				} else if (yp_dataop) {
					if (item.YZLX != 'ls' && item.YZLX != 'cydy') {
						WRT_e.ui.message({
							title: '信息窗口',
							content: `${item.MC}不是草药，请选择正确的医嘱类型（临时医嘱、出院带药）！`,
							onOk() {
							}
						})
						rtn = false
						return
					}
				}
			}
			//中成药分餐校验
			if (item.LB == "3") {
				if (yp_dataop["3000"] == '1' && item.YZLX == 'cydy') {//附加属性3000
					WRT_e.ui.message({
						title: '信息窗口',
						content: `${item.MC}药品有分装药品，出院带药请使用分装药品`,
						onOk() {
						}
					})
					rtn = false
					return
				}
				let fzqyid = yp_dataop["fzqypid"] || item.FZQYPID
				// if (fzqyid != '0' && item.YZLX != 'cydy') {//附加属性fzqypid
				//     WRT_e.ui.message({
				//         title: '信息窗口',
				//         content: `${item.MC}是分装药品，只能开在出院带药里。`,
				//         onOk() {
				//         }
				//     })
				//     rtn = false
				//     return
				// }
			}
			if (item.TSYF.length > 30) {
				WRT_e.ui.message({
					title: '信息窗口',
					content: `字数限制`,
					onOk() {
					}
				})
				rtn = false
				return
			}
			if (item.YZLX == 'cydy' && (!item.JRCY)) {
				WRT_e.ui.message({
					title: '信息窗口',
					content: `必选“预计出院”`,//、“是否代煎”
					onOk() {
					}
				})
				rtn = false
				return
			}
			if (item.LB == 1 && !item.ZXPL) {
				rtn = false
				WRT_e.ui.message({
					title: '信息窗口',
					content: `治疗医嘱${item.MC}必填执行频率`,
					onOk() {
					}
				})
				return
			}
			if (item.LB == 3) {
				if (!item.KSSJ_TXT || item.YCYL === '' || !item.JLDW || !item.ZXFF || !item.ZXPL) {
					rtn = false
					WRT_e.ui.message({
						title: '信息窗口',
						content: `${item.MC}必填${!item.KSSJ_TXT ? "计划开始时间、" : ""}${item.YCYL === '' ? "一次用量、" : ""}${!item.JLDW ? "剂量单位、" : ""}${!item.ZXFF ? "执行方法、" : ""}${!item.ZXPL ? "执行频率、" : ""}`,
						onOk() {
						}
					})
					return
				}
				let fd = [], Tsyf = item.TSYF || ''
				if (item.ZHTXT) {
					fd = yzdata.filter(ev => ev.ZH == item.ZH)
					Tsyf = item.TSYF || fd[0].TSYF
				}
				if (item.ZXFF == 'iv-vp') {
					if (!(Tsyf.indexOf('速度') >= 0 || Tsyf.indexOf('流速') >= 0)) {
						WRT_e.ui.message({
							title: '信息窗口',
							content: `微泵维持必须在医嘱说明中输入中文速度或流速!(例如：流速:3ml/h)`,
							onOk() {
							}
						})
						rtn = false
						return
					}
				}
				if (item.ZXFF == '自定义' && !Tsyf) {
					WRT_e.ui.message({
						title: '信息窗口',
						content: `请填写${item.MC}医嘱的医嘱说明/备注说明！!(例如：流速:3ml/h)`,
						onOk() {
						}
					})
					rtn = false
					return
				}
			}
			if (item.YZLX == 'cy' || item.YZLX == 'zcydy') {
				let fd = yzdata.filter(ev => ev.XH == item.XH)
				if (fd && fd.length > 1) {
					fd.map(function (k) {
						if (!(item.ZXFF == k.ZXFF && item.ZXPL == k.ZXPL && item.GYSJ == k.GYSJ && item.SFSL == k.SFSL)) {
							WRT_e.ui.message({
								title: '信息窗口',
								content: `草药医嘱、草药带药的用药方法、频率、执行时间、数量必须保持同组一致`,
								onOk() {
								}
							})
							rtn = false
							return
						}
					})
				}
			}
			if (item.KSSJ_TXT) {
				let times = new Date(item.KSSJ_TXT)
				// let cha = times.getTime() - (new Date()).getTime()
				let getApiTime = getKSSJ_TXT()
				//let cha = times.getTime() - getApiTime// 不确定是否重新获取
				 let cha = times.getTime() - new Date(WRT_config.defaultKSSJ_TXT).getTime()
				if (cha < 0 && Math.abs(cha) > 1000 * 60 * 60 * 6 && item.TSYF == "") {
					WRT_e.ui.message({
						title: '信息窗口',
						content: `${item.MC}医嘱的计划开始时间往前超过6小时，请在备注里说明原因！ ${item.KSSJ_TXT}`,
						onOk() {
						}
					})
					rtn = false
					return
				} else if (Math.abs(cha) > 1000 * 60 * 60 * 24 * 365) {
					WRT_e.ui.message({
						title: '信息窗口',
						content: `${item.MC}医嘱的计划开始时间有误，请修改！ ${item.KSSJ_TXT}`,
						onOk() {
						}
					})
					rtn = false
					return
				}
			}
			let ypfjxx = {}
			if (item.ypfjxx) { ypfjxx = JSON.parse(item.ypfjxx) };
			if (WRT_config.yz_sz.jslx != '00' && (yp_dataop.kzjb == '自费' || yp_dataop.kzjb == '纯自费')) {
				item.SFZf = 1
			}
			//体表面积计算药品
			if((ypfjxx&&ypfjxx.tbmj==1)||item.TBMJ==1){
				Tbmjtype = true
			}
			if (item.SFZB != 1) {
				let msg = checkGjcg(item.YZLX, item.LB, item.MC, ypfjxx.canprescribe_zy, ypfjxx.canprescribe_cydy, yp_dataop.gjcg, item.SFSL)
				if (!msg) {
					rtn = false
					return
				}
			}
			// if (WRT_config.yz_sz.lstys != 1) {
			//     let fd = yzdata.filter(key => key.LB == 2)
			//     if (fd && fd.length > 0) {
			//         WRT_e.ui.hint({
			//             type: 'error',
			//             msg: "该病人不允许开饮食"
			//         })
			//         rtn = false
			//         return
			//     }
			// } else {
			//     let fd = yzdata.filter(key => key.LB == 2)
			//     if (fd && fd.length >= 2) {
			//         WRT_e.ui.hint({
			//             type: 'error',
			//             msg: "最多只能有1条饮食"
			//         })
			//         rtn = false
			//         return
			//     }
			// }
			// if (item.MC) {
			// let ypfjxx = {}
			// if (item.ypfjxx) { ypfjxx = JSON.parse(item.ypfjxx) };
			let kjypsyly = item.kjypsyly || {}
			let XMID = item.YPID || item.XMID
			if (item.LB == 4) {
				XMID = 't6712'
			}
			let cxts = `${isCXTS(item) ? item.CXTS : ''}`
			let JSSJ_TXT = `${item.JSSJ_TXT == "0000-00-00 00:00" ? "" : item.JSSJ_TXT}`
			let ZH = JSON.stringify(item.XH)
			if (item.YZLX == "cy" || item.YZLX == "zcydy") {
				let x = item.XH.split("-")
				ZH = x[0]
			}
			// let ypds = item.YPDS || ypfjxx.ypds || null
			// if (ypds == 0) {
			//     ypds = null
			// }
			let kjyp_hzdid = StringID(item.kjywhzdid || ypfjxx.hzdid || item.TSKJYWHZDID) || ''
			let kjyp_slhzdid = StringID(ypfjxx.slhzdid || item.SLKJYWHZDID) || ''
			let cwyyhzdid = StringID(item.cwyyhzdid || item.CWYYHZDID) || '0'
			let sqsl_ID = StringID(item.SQSL)
			let kjyp_qjss_spysid = StringID(kjypsyly.QJSS_SPYSID || item.KJYP_QJSS_SPYSID || '')
			let num_sl = `${item.YZLX == 'cydy' || ((item.YZLX == 'ls' || item.YZLX == 'cq') && (item.LB == '1' || item.LB == '4')) ? item.SFSL || 1 : ''}`
			let ypzd = dealCfy(item)
			let tzdid = StringID(item.TZDID || '')
			let zlyzmxs=''
			let yyts=StringID(item.YYTS || '')
			if(item.ZLYZMXS){
				zlyzmxs=JSON.parse(item.ZLYZMXS)||''
			}
			if(item.zlyzmxs){
				zlyzmxs=JSON.parse(item.zlyzmxs)
			}
			// if(item.cfzd_type&&ypzd.length==0){
			//     rtn=false
			//     WRT_e.ui.message({
			//         title: '信息窗口',
			//         content: `${item.MC}未选择处方诊断！`,
			//         onOk() {
			//         }
			//     })
			//     return;
			// }
			if (item.ZXFF == 'po') {
				zxff_type = true
			}
			// let crrtYz=''
			// if(item.crrtYz){
			// 	crrtYz=JSON.parse(item.crrtYz||"")
			// }
			// let crrt_arr=''
			// if(crrtYz.length>0){
			// 	crrt_arr=[{
			// 		"ZHILIAOYZID": '', //CRRT治疗医嘱ID（新增时为空）
			// 		"CRRTID":crrtYz.CRRTID,//crrt处方单的ID
			// 		"CRRTXH": 1,//crrt处方单的序号
			// 		"BINGLIID": WRT_config.url.as_blid,//病人病历ID
			// 		"ZHUYUANID": WRT_config.yz_sz.zyid,//病人住院ID
			// 		"YIZHUID": ''
			// 	}]
			// }
			
			ast_zxbq.push(item.BQID)
			let ggid = StringID(item.GG || item.DJ)
			arr.push({
				mc: item.MC,
				id: XMID,
				lb: `${item.LB == 1 ? 'zl' : item.LB == 2 ? 'ys' : item.LB == 3 ? 'yp' : 'zt'}`,
				yzlx: item.YZLX,
				yzlb: yp_dataop.yzlb || item.YZLB || '',
				sfzb: `${item.SFZB == "1" ? '1' : '0'}`,
				gg: ggid,
				zxff: item.ZXFF || "",
				zxpl: item.ZXPL || "",
				zxpl2: item.SJZZXPL || "",
				jx: yp_dataop.jx || item.JX || '',
				cxts: `${cxts > 0 ? cxts : ''}`,
				yyts:yyts,
				dw: item.DW || '',
				tsyf: item.TSYF || "",
				jrcy: item.JRCY || "",
				zsbq: item.ZSBQ || "",
				ycyl: `${typeof item.YCYL == 'number' ? JSON.stringify(item.YCYL) : item.YCYL}`,
				jldw: item.JLDW,
				yfdm: yp_dataop.yfdm || item.YFDM || '',
				zh: ZH,
				zszh: `${item.ZH ? JSON.stringify(item.ZH) : ''}`,
				sfzf: `${item.SFZF ? item.SFZF : (item.ZF == "1" || item.ZF == "0") ? item.ZF : '0'}`,
				splb: item.splb || item.XSP || '0',
				sqsl: sqsl_ID || "",
				kjyp_hzdid: kjyp_hzdid,//特殊抗菌药物会诊单
				kjyp_slhzdid: kjyp_slhzdid,//
				tzdid: tzdid || '',
				jpyp: ypfjxx.jpyp || item.JPYP || '',
				kjyp_syff: kjypsyly.SYFF || item.KJYP_SYFF || '',//抗菌药品使用方法
				kjyp_yffl: kjypsyly.YFFL || item.KJYP_YYFL || '',//预防分类
				kjyp_sqsy: kjypsyly.SQSY || item.KJYP_SQSYFL || '',//
				kjyp_kjjb: kjypsyly.KJJB || item.KJYP_KJFL || '',//
				kjyp_tbjb: kjypsyly.TBJB || item.KJYP_TBJB || '',//头孢级别
				kjyp_fhkj: kjypsyly.FHKJ || item.KJYP_FHKJ || '',//复合抗菌药品
				kjyp_fknt: kjypsyly.FKNT || item.KJYP_FKNT || '',//氟奎诺同类药品
				kjyp_syly: kjypsyly.SYLY || item.KJYP_SYYY || '',//使用理由
				kjyp_tzsj: kjypsyly.TZSJ || item.KJYP_TZSJ_STR || '',//停止时间
				kjyp_ssmc: kjypsyly.SSMC || item.KJYP_SSMC || '',//手术名称
				kjyp_qklb: kjypsyly.QKLB || item.KJYP_QKLB || '',//切口类别
				kjyp_wsw: kjypsyly.WSW || item.KJYP_WSW || '',//微生物送检
				kjyp_wswbz: kjypsyly.WSWBZ || item.KJYP_WSWBZ || '',//微生物无样可采备注
				kjyp_qjss: kjypsyly.QJSS || item.KJYP_QJSS || '',//清洁手术
				kjyp_qjss_spsj: kjypsyly.QJSS_SPSJ || item.KJYP_QJSS_SPSJ_STR || '',//审批时间
				kjyp_qjss_spysid: kjyp_qjss_spysid,//审批医生ID
				kjyp_qjss_spyj: kjypsyly.QJSS_SPYJ || item.KJYP_QJSS_SPYJ || '',//审批意见
				kjyp_qjss_gwys: kjypsyly.QJSS_GWYS || item.KJYP_QJSS_GWYS || '',//高危因素
				xseyf_sfwcgwys: kjypsyly.XSEYF_SFWCGWYS || item.XSEYF_SFWCGWYS || '',
				xseyf_wcgwys: kjypsyly.XSEYF_WCGWYS || item.XSEYF_WCGWYS || '',
				xseyf_wcgwys_qt: kjypsyly.XSEYF_WCGWYS_QT || item.XSEYF_WCGWYS_QT || '',
				xseyf_sfylcbx: kjypsyly.XSEYF_SFYLCBX || item.XSEYF_SFYLCBX || '',
				xseyf_lcbx: kjypsyly.XSEYF_LCBX || item.XSEYF_LCBX || '',
				yzid: `${item.YZID ? JSON.stringify(item.YZID) : 0}`,
				gysj: item.GYSJ || "",
				kssj: gettime(item.KSSJ_TXT),
				jssj: `${JSSJ_TXT ? gettime(JSSJ_TXT) : '0000-00-00 00:00:00'}`,
				bqid: item.BQID || WRT_config.yz_sz.bqid,
				sl: (num_sl),
				cs: `${item.YZLX == 'cy' || item.YZLX == 'zcydy' ? item.SFSL || item.SFCS || 7 : ""}`,
				gjcg: yp_dataop.gjcg || item.GJCG | "",//
				zhtxt: item.ZHTXT || "",
				bmlx: '',
				xmbm: '',
				sfbl: '',
				mps: '',
				sqdsjid: item.SQDSJID || WRT_config.url.as_sqdsjid || "",//
				yplb: yp_dataop.yplb || item.YPLB || '',//
				jbdm: ao_tsywba.JBDM || item.JBDM || '',//
				jbmc: ao_tsywba.JBMC || item.JBMC || '',//
				sqyy: ao_tsywba.SQYY || item.SQYY || '',//
				yysq: ao_tsywba.YYSQ || item.YYSQ || '',//
				ypds: null,
				ypdsbz: item.ypdsbz || item.YPDSBZ || "",
				cwyyhzdid: cwyyhzdid || "0",
				zcyzd: item.ZDXX || item.ZCYZD || "",
				zcyzz: item.ZZXX || item.ZCYZZ || "",
				wgypsqjlid: yp_dataop.wgypsqjlid || item.WGYPSQJLID || '',
				origin: '3',
				mzyt: item.mzyt || item.MZYT || '',
				ypzd: JSON.stringify(ypzd),
				xdfw: item.XDFW || item.xdfw || '',
				flm: item.FLM || item.flm || '',
				sbdm: item.SBDM || item.sbdm || '',
				leftType:item.leftType||item.LEFTTYPE||"",
				rightType:item.rightType||item.RIGHTTYPE||"",
				xs:parseInt(item.xs)||item.XS||null,
				fz:parseInt(item.fz)||item.FZ||null,
				lylb:item.lylb||item.LYLB||'',
				JINRILT:item.JINRILT||'',
				gcp:item.gcp||item.GCP||'',
				gcpywbh:item.ywbh||item.GCPYWBH||'',
				zlyzmxs:JSON.stringify(zlyzmxs),
				// crrt:item.crrt||item.CRRT||'',
				// crrtyz:crrt_arr?JSON.stringify(crrt_arr):item.CRRTYZ||''
			})
			const list={
				yiShiID:WRT_config.yz_sz.yhid,
				zhuanKeID:WRT_config.yz_sz.zkid,
				fenLeiMa:item.FLM || item.flm || '',
				yaoPinID:parseInt(XMID),
				mingCheng:item.MC,
				jiXing:yp_dataop.jx || item.JX || '',
				yiCiYL:parseInt(item.YCYL),
				jiLiangDW:item.JLDW,
				pinLv:item.ZXPL,
				fangFa:item.ZXFF,
				geiYaoSJ:item.GYSJ,//item.GYSJ
				teShuYF:item.TSYF,
				yiZhuSJ:item.YZSJ,
				// yiZhuID:item.YZID,
				guoJiaBM:item.SBDM || item.sbdm || ''//item.GJM
			}
			// arr_zgyq.push(list)
			
			let xm=''
			if(item.LB==1&&XMID.indexOf('z')>=0){
				xm=ypfjxx.zlsfxmid||item.ZLSFXMID
			}else{
				xm=XMID
			}
			let sskg={}
			if(item.LB==1){
				let zl=zlyzmxs||[]
				let arr=[]
				zl.forEach(ev=>{
					if(ev.ZIFEI==0){
						let zl_list={
							xiangMuMC:item.MC,
							shengJiBM:ev.SBDM2 || item.SBDM2 || '',//item.GJM,
							guoJiaBM:ev.SBDM || item.SBDM || '',
							shuLiang:parseInt(num_sl)
						}
						sskg={
							xiangMuID:parseInt(xm),
							leiBie:1,
							guoJiaBM:ev.SBDM || item.SBDM || ''
						}
						arr.push(zl_list)
						arr_sskg.push(sskg)
					}
				})
				arr_zgyq.Charges=[...arr_zgyq.Charges,...arr]
			}else if(item.LB==3){
				arr_zgyq.Drugs.push(list)
				sskg={
					xiangMuID:parseInt(xm),
					leiBie:1,
					guoJiaBM:item.SBDM || item.sbdm || ''
				}
				arr_sskg.push(sskg)
			}
			
		}
	})

	if (!rtn) {
		isSubbmit = false
		return;
	}
	if (YPDSBZRtn) {
		WRT_e.ui.message({
			title: '信息窗口',
			content: `同一组药品里只能有一个滴速内容，请删除`,
			onOk() {
			}
		})
		isSubbmit = false
		return
	}
	isSubbmit = false
	let tsyba = []
	if (JSON.stringify(ao_tsywba) != "{}") {
		tsyba.push(ao_tsywba)
	}
	//惠美开关
	WRT_e.api.BrMainLeft.GetHmzkKg({
		params:{},
		success(data){
			if(data!=1){
				gzjy_fun()
				return
			}
			//惠美
			WRT_e.api.yz_sz.getResults({
				params:{
					userGuid:WRT_config.yz_sz.empi,
					serialNumber:WRT_config.url.as_blid,
					pageSource:2,
					customerid:1898
				},
				success(msg){
					if(msg.hasError){
						WRT_e.ui.hint({ msg:msg.message, type:"error"})
					}else if(msg.body&&msg.body.failureRules){
						let text=''
						msg.body.failureRules.map(function(item){
							if(item.recommendLevel==3){
								text+=item.ruleName+"</br>"
							}
						})
						if(text){
							WRT_e.ui.message({
								title: '提示信息',
								content:text,
								onOk(){
								}
							})
						}else{
							gzjy_fun()
						}
					}else{
						gzjy_fun()
					}
				},error(){
					gzjy_fun()
				}
			})
		}
	})
	function gzjy_fun(){
		if(isZFType){
			Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
		}else{
			//规则引擎开关，条件
			WRT_e.api.au.getRuleEngine({
				params:arr_sskg,
				success(msg){
					if(msg.data){
						if(msg.data.kaiGuan){
							if(msg.data.itemVoList.length>0){
								// arr_zgyq.Drugs.map(function(key,index){
								// 	let fd=msg.data.itemVoList.filter(item=>item.guoJiaBM==key.guoJiaBM)
								// 	if(fd&&fd.length==0){
								// 		arr_zgyq.Drugs.splice(index,1)
								// 	}
								// 	let td = arr.filter(ev=>ev.guoJiaBM==key.guoJiaBM&&ev.mc==key.mingCheng)
								// 	if(td&&td.length>0){
								// 		if(!(WRT_config.yz_sz.jslx != '00' &&td[0].sfzf=='0')){
								// 			arr_zgyq.Drugs.splice(index,1)
								// 		}
								// 	}
								// })
								// arr_zgyq.Charges.map(function(key,index){
								// 	let fd=msg.data.itemVoList.filter(item=>item.guoJiaBM==key.guoJiaBM)
								// 	if(fd&&fd.length==0){
								// 		arr_zgyq.Charges.splice(index,1)
								// 	}
								// 	let td = arr.filter(ev=>ev.guoJiaBM==key.guoJiaBM&&ev.mc==key.xiangMuMC)
								// 	if(td&&td.length>0){
								// 		if(!(WRT_config.yz_sz.jslx != '00' &&td[0].sfzf=='0')){
								// 			arr_zgyq.Charges.splice(index,1)
								// 		}
								// 	}
								// })
								if(arr_zgyq.Charges.length==0&&arr_zgyq.Drugs.length==0){
									Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
									return
								}else{
									Savegzyq(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
								}
							}else{
								Savegzyq(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
							}
							// arr_zgyq=arr
						}else{
							Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
						}
					}else{
						Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
					}
				},
				error(){
					Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
				}
			})
		}
	}
	

	// Savecheck(zxff_type,arr, as_delid, as_grzd, tsyba, type)
}

//规则引擎判断
function Savegzyq(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type){
	//
	linShiTJ_array=[]
	let linShiSJ=arr_zgyq
	tssm_array=[]
	tssm_index=0
	// iSgzyq(WRT_config.url.as_blid,'0003498036',linShiSJ,'yp')
	let params={
		"biaoShiHao": WRT_config.url.as_blid, //诊疗活动ID/病例ID
		"bingAnHao": WRT_config.yz_sz.empi, //病案号
		"changJingDM": "cwsf_zyyz", //场景代码
		"linShiSJ": linShiSJ, //临时数据
		"shiFouMZ": false, //是否门诊
		// "biaoShiHao": 2440921,
    // "bingAnHao": "0013634025",
    // "changJingDM": "cwsf_zyyz",
    // "linShiSJ": {
    //     "Charges": [{
		// 		"guoJiaBM": "XB03BBY050A001020100886",
		// 		"xiangMuMC": "肌肉注射",
		// 		"shuLiang": 1
		// 	}]
    // },
    // "linShiZD": {},
    // "shiFouMZ": false
	}
	WRT_e.api.au.getTRBSCode({
		params:params,
		success(msg){
			if(msg.data&&msg.data.linShiTJ){
				if(msg.data.linShiTJ.length>0){
					linShiTJ_array=msg.data.linShiTJ
					let fd=msg.data.linShiTJ.filter(item=>item.tiaoJianLB=='7')
					if(fd&&fd.length>0){
						let name=arr_zgyq[fd[0].shuJuLeiDM][fd[0].chaRuSX-1].mingCheng?arr_zgyq[fd[0].shuJuLeiDM][fd[0].chaRuSX-1].mingCheng:arr_zgyq[fd[0].shuJuLeiDM][fd[0].chaRuSX-1].xiangMuMC
						WRT_e.ui.message({
							title: '提示信息',
							content:`【${name}】${fd[0].xunWenNR}`,
							onOk(){
							}
						})
						return
					}
					let text=''
					let LBtype=false
					msg.data.linShiTJ.map(function(item){
						let name=arr_zgyq[item.shuJuLeiDM][item.chaRuSX-1].mingCheng?arr_zgyq[item.shuJuLeiDM][item.chaRuSX-1].mingCheng:arr_zgyq[item.shuJuLeiDM][item.chaRuSX-1].xiangMuMC
						if(item.tiaoJianLB=='6'){
							if(item.xunWenNR){
								LBtype=true
								text+=`【${name}】${item.xunWenNR},是否自费`+`</br></br>`
								// WRT_e.ui.message({
								// 	title: '提示信息',
								// 	content:`【${name}】${item.xunWenNR},是否自费`,
								// 	onOk(){
								// 		arr[item.chaRuSX-1].sfzf=1
								// 		Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
								// 	},
								// 	onCancel(){}
								// })
							}
						}else if(item.tiaoJianLB=='3'){
							if(item.xunWenNR){
								text+=`【${name}】${item.xunWenNR}`+`</br></br>`
								// WRT_e.ui.message({
								// 	title: '提示信息',
								// 	content:`【${name}】${item.xunWenNR}`,
								// 	onOk(){
								// 		arr[item.chaRuSX-1].sfzf=1
								// 		Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
								// 	}
								// })
							}
						}else if(item.tiaoJianLB=='4'){
							if(item.xunWenNR){
								text+=`【${name}】${item.xunWenNR},修改为自费`+`</br></br>`
								// WRT_e.ui.message({
								// 	title: '提示信息',
								// 	content:`【${name}】${item.xunWenNR},修改为自费`,
								// 	onOk(){
								// 		arr[item.chaRuSX-1].sfzf=1
								// 		Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
								// 	}
								// })
							}
						}else if(item.tiaoJianLB=='10'){
							tssm_array.push(item)
						}
					})
					if(text){
						if(LBtype){
							WRT_e.ui.message({
								title: '提示信息',
								content:text,
								onOk(){
									checktype(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
								},
								onCancel(){
									//SavecheckTssm(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
								}
							})
						}else{
							WRT_e.ui.message({
								title: '提示信息',
								content:text,
								onOk(){
									checktype(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
								}
							})
						}
					}else{
						SavecheckTssm(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
					}

				}else{
					Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
				}
			}else{
				// WRT_e.ui.hint({msg:'规则引擎连接失败，请联系信息科！'})
				Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
			}
		},
		error(){
			Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
		}
	})
}
//特殊说明提醒
function tssmhtml(obj){
	let temp=`
	<div>${obj.xunWenNR}</div>
	<div><input id="tssm_input" class="none" type="text" autocomplete="off" placeholder="特殊说明输入"></div>
	<div>
		<button class="e_btn" onclick="tssm_click()">特殊说明</button>
		<button class="e_btn" onclick="check_click(${obj.chaRuSX})">确认</button>
		<button class="e_btn" onclick="$('#tssmhtml').iziModal('destroy')">取消</button>
	</div>
	`
	WRT_e.ui.model({
		id: 'tssmhtml',
		title: '信息提示',
		width: "650px",
		content: temp,
		iframe: false,
	})
}
//特殊说明点击
function tssm_click(){
	$("#tssm_input").removeClass("none")
}
//特殊说明确定
function check_click(ev){
	let tssm=$("#tssm_input")[0].value
	let {zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type}=tssm_model
	let list=tssm_array[tssm_index]
	if(!tssm){
		let gjm=''
		// if(!WRT_config.gzyqType){
			if(arr&&arr.length<=1){
				gjm=arr_zgyq[list.shuJuLeiDM][list.chaRuSX-1].guoJiaBM
			}else{
				gjm=arr_zgyq[list.shuJuLeiDM][list.chaRuSX-1].guoJiaBM
			}
		// }else{
		// 	gjm=arr_zgyq[list.shuJuLeiDM][list.chaRuSX-1].guoJiaBM
		// }
		arr.map(function(item){
			if(item.LB==1||(item.lb=='zl')){
				let zl=[]
				if(item.ZLYZMXS){
					zl=JSON.parse(item.ZLYZMXS)||[]
				}
				if(item.zlyzmxs){
					zl=JSON.parse(item.zlyzmxs)||[]
				}
				zl.map((ev)=>{
					if(ev.SBDM==gjm){
						ev.ZIFEI=1
					}
				})
				item.zlyzmxs=JSON.stringify(zl)
			}else if(item.sbdm==gjm){
				item.sfzf=1
			}
		})
		$('#tssmhtml').iziModal('destroy')
		tssm_index++
		if(tssm_array.length>0&&tssm_index<tssm_array.length){
			SavecheckTssm(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
		}else{
			Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
		}
	}else{
		WRT_e.api.au.saveShuoMing({
			params:{
				"biaoShi": WRT_config.url.as_blid, //诊疗活动ID/病例ID
				"bingAnHao": WRT_config.yz_sz.empi, //病案号
				"changJingDM": "cwsf_zyyz", //场景代码
				"yingYongDM": "020", //应用代码
				"yongHuID": WRT_config.yz_sz.yhid, //用户ID
				"teShuSM": tssm, //特殊说明
				"jiLuSJ": gettime(), //记录时间
				"shiFouMZ": "2" //是否门诊，1门诊，2住院
			},
			success(msg){
				if(msg.hasError=='0'){
					WRT_e.ui.hint({type:'success',msg:'记录成功！'})
				}
				$('#tssmhtml').iziModal('destroy')
				tssm_index++
				if(tssm_array.length>0&&tssm_index<tssm_array.length){
					SavecheckTssm(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
				}else{
					Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
				}
			}
		})
	}
	
}
//消息提醒
function checktype(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type){
	linShiTJ_array.forEach((item)=>{
		if(item.tiaoJianLB=='6'||item.tiaoJianLB=='3'||item.tiaoJianLB=='4'||item.tiaoJianLB=='10'){
			let gjm=''
			// if(!WRT_config.gzyqType){
				if(arr.length<=1){
					gjm=arr_zgyq[item.shuJuLeiDM][item.chaRuSX-1].guoJiaBM
				}else{
					gjm=arr_zgyq[item.shuJuLeiDM][item.chaRuSX-1].guoJiaBM
				}
			// }else{
			// 	gjm=arr_zgyq[item.shuJuLeiDM][item.chaRuSX-1].guoJiaBM
			// }
			arr.map(function(k){
				if(k.LB==1||(k.lb=='zl')){
					let zl=[]
					if(k.ZLYZMXS){
						zl=JSON.parse(k.ZLYZMXS)||[]
					}
					if(k.zlyzmxs){
						zl=JSON.parse(k.zlyzmxs)||[]
					}
					zl.map((ev)=>{
						if(ev.SBDM==gjm){
							ev.ZIFEI=1
						}
					})
					k.zlyzmxs=JSON.stringify(zl)
				}else if(k.sbdm==gjm){
					k.sfzf=1
				}
			})
		}
	})
	SavecheckTssm(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
}
//保存前特殊说明判断
function SavecheckTssm(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type){
	tssm_model={
		zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type
	}
	if(tssm_array.length>0&&tssm_index<tssm_array.length){
		tssmhtml(tssm_array[tssm_index])
	}else{
		Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type)
	}
}

//保存前
function Savecheck(zxff_type,ast_zxbq,arr, as_delid, as_grzd, tsyba, type){
	if (zxff_type) {
		WRT_e.api.yz_sz.checkKfy({
			params: {
				"al_blid": WRT_config.url.as_blid
			},
			success(data) {
				if (data.Code == 1) {
					if (data.Result) {
						WRT_e.ui.message({
							title: '信息窗口',
							content: data.Result,
							okText: '是', //取消回调函数
							cancelText: '否', //取消回调函数
							onOk() {
								WRT_e.api.yz_sz.checkZxbq({
									params: {
										"ast_zxbq": ast_zxbq,//医嘱的执行病区，无需去重。
										"al_blid": WRT_config.url.as_blid
									},
									success(data) {
										if (data.Code == 1) {
											let msg = JSON.parse(data.Result)
											if (msg.zxbqmc) {
												WRT_e.ui.message({
													title: '信息窗口',
													content: `医嘱开到的病区(${msg.zxbqmc})与病人当前病区(${msg.xbqmc})不一致，是否继续保存？`,
													onOk() {
														SaveYz(arr, as_delid, as_grzd, tsyba, type)
													},
													onCancel() {
														isSubbmit = false
													}
												})
											} else {
												SaveYz(arr, as_delid, as_grzd, tsyba, type)
											}
										}
									}
								})
							},
							onCancel() {
								isSubbmit = false
							}
						})
					} else {
						SaveYz(arr, as_delid, as_grzd, tsyba, type)
					}
				}
			}
		})
	} else {
		WRT_e.api.yz_sz.checkZxbq({
			params: {
				"ast_zxbq": ast_zxbq,//医嘱的执行病区，无需去重。
				"al_blid": WRT_config.url.as_blid
			},
			success(data) {
				if (data.Code == 1) {
					let msg = JSON.parse(data.Result)
					if (msg.zxbqmc) {
						WRT_e.ui.message({
							title: '信息窗口',
							content: `医嘱开到的病区(${msg.zxbqmc})与病人当前病区(${msg.xbqmc})不一致，是否继续保存？`,
							onOk() {
								SaveYz(arr, as_delid, as_grzd, tsyba, type)
							},
							onCancel() {
								isSubbmit = false
							}
						})
					} else {
						SaveYz(arr, as_delid, as_grzd, tsyba, type)
					}
				}
			}
		})
	}
}

//保存中
function SaveYz(arr, as_delid, as_grzd, tsyba, type) {
	let params = {
		ao_yzarr: arr,
		as_delid: as_delid,
		al_blid: WRT_config.url.as_blid,
		as_grzd: as_grzd || "",
		ao_tsywba: tsyba,
		as_yzlbtype: WRT_config.url.as_yzlb
	}
	WRT_e.api.yz_sz.SaveYz({
		params: params, 
		success(data) {
			isSubbmit = false
			if (data.Code == 1) {
				if (data.Result != "") {
					let arr = []
					let lists = data.Result.split(",")
					lists.map(function (item) {
						if (item.indexOf("yzid") >= 0) {
							let val = item.split(":")
							arr.push({ yzid: val[1] })
						}
					})
					// yz_submit = []

					arr.map(function (item, i) {
						// yzdata[i].YZID=item.yzid
						// yz_submit.push(item.yzid)
						yzdata[i].YZID = parseInt(item.yzid)
					})
					yz_submit = []
					yz_Lbmit = []

					// arr_zgyq=[]
					yzdata.forEach(item => {
						
						if (item.check || item.check == undefined) {
							yz_submit.push(item.YZID)
							if (item.LB == 3) {
								yz_Lbmit.push(item.YZID)
							}
						}
					})
				}
				if (type == 'submit' || type == "savesubmit") {
					getSumTbmj()
					return
				}
				isZFType=false
				WRT_e.ui.message({
					title: '信息窗口',
					content: '医嘱保存成功',
					onOk() {
						refresh_init()
					}
				})
			} else {
				WRT_e.ui.message({
					title: '信息窗口',
					content: data.CodeMsg,
					onOk() {
						// yzdata.splice(yzindex,1)
					}
				})
			}
		},error(){
			isSubbmit = false
		}
	})
}

//提交时弹出体重身高
function getSumTbmj(){
	if(Tbmjtype){
		WRT_e.api.yz_sz.getLastSGTZ({
			params:{
				bingLiID:WRT_config.url.as_blid
			},
			success(msg){
				if(msg.hasError==0){
					WRT_config.Lastsgtz=msg.data
					getLastSGTZHtml()
				}
			}
		})
	}else{
		getsubmit()
	}
}

//身高体重html
function getLastSGTZHtml(){
	let sum='',tz='',sg='',text='',time='',name=''
	if(WRT_config.Lastsgtz&&WRT_config.Lastsgtz.shenGao){
		sg = parseInt(WRT_config.Lastsgtz.shenGao.zhi)
		if(isNaN(sg)){
			sg=''
		}
		time = WRT_config.Lastsgtz.shenGao.ceLiangSJ
		name = WRT_config.Lastsgtz.shenGao.caoZuoZhe
	}
	if(WRT_config.Lastsgtz&&WRT_config.Lastsgtz.tiZhong){
		tz=parseInt(WRT_config.Lastsgtz.tiZhong.zhi)
		if(isNaN(tz)){
			tz=''
		}
	}
	if(WRT_config.yz_sz.brnl>16){
		sum=(0.0061*sg+0.0128*tz-0.1259).toFixed(4);
		text='0.0061*身高(cm)+0.0128*体重(kg)-0.1259'
	}else if(tz<=30){
		sum=(0.035*tz+0.1).toFixed(4);
		text='体重(kg)*0.035+0.1'
	}else{
		sum=(0.02*(tz-30)+1.05).toFixed(4);
		text='1.05+(体重(kg)-30)*0.02'
	}
	let html=`
	<div>
		<label>患者姓名：</label><span>${WRT_config.yz_sz.brxm}</span>
		<label style="margin-left: 55px;">年龄：</label><span>${WRT_config.yz_sz.brnl}岁（月、天）</span>
	</div>
	<div>
		<label>身高(H)：</label><input id="sg_text" class="text_class" type="text" value="${sg}" onchange='changesum()' onkeyup="onKeysum('sg')" style="width: 50px;" autocomplete="off" /><span>cm</span>
		<label style="margin-left: 10px;">体重(W)：</label><input id="tz_text" class="text_class" type="text" onchange='changesum()' value="${tz}" onkeyup="onKeysum('tz')" autocomplete="off" style="width: 50px;" /><span>kg</span>
	</div>
	<div>
		<label>体表面积(S)：</label><span id="tbmj_sum">${sum}</span>
	</div>
	<div>
		<label>体表面积计算公式：</label><span>${text}9</span>
	</div>
	<div>
		<label>上次记录时间：</label><span>${time}</span>
		<label>记录人：</label><span>${name}</span>
	</div>
	<div>
		<button class="e_btn" onclick="onSetLastsgtz()">确认</button>
	</div>
	`
	WRT_e.ui.model({
		id: "if_LastSGTZ",
		title: "体表面积",
		width: "550px",
		iframeHeight: "500px",
		content: html,
		iframe: false,
	})
}
//
function onKeysum(name){
	let val = $(`#${name}_text`)[0].value
	val=val.replace(/[^\d]/g,'')
	if(val==0){
		val=''
	}
	$(`#${name}_text`)[0].value=val
}
//体表面积计算
function changesum(){
	let tz='',sg='',sum=''
	tz = parseFloat($("#tz_text")[0].value)
	sg = parseFloat($("#sg_text")[0].value)
	if(WRT_config.yz_sz.brnl>16){
		sum=(0.0061*sg+0.0128*tz-0.1259).toFixed(4);
	}else if(tz<=30){
		sum=(0.035*tz+0.1).toFixed(4);
	}else{
		sum=(0.02*(tz-30)+1.05).toFixed(4);
	}
	$("#tbmj_sum").html(sum)
}
//保存身高体重
function onSetLastsgtz(){
	let tz='',sg=''
	if($("#tz_text")[0].value==''||$("#sg_text")[0].value==''){
		WRT_e.ui.hint({msg:'身高体重不能为空',type:'error'})
		return
	}
	if(WRT_config.Lastsgtz&&WRT_config.Lastsgtz.shenGao){
		sg = parseInt(WRT_config.Lastsgtz.shenGao.zhi)
	}
	if(WRT_config.Lastsgtz&&WRT_config.Lastsgtz.tiZhong){
		tz=parseInt(WRT_config.Lastsgtz.tiZhong.zhi)
	}
	if($("#sg_text")[0].value>300||$("#sg_text")[0].value<30||$("#tz_text")[0].value>300||$("#tz_text")[0].value<10){
		WRT_e.ui.message({
			title: '提示',
			content: '身高/体重异常是否确定保存',
			onOk() {
				savesgtz()
			},
			onCancel() {
				$("#if_LastSGTZ").iziModal("destroy")
			}
		})
	}else{
		savesgtz()
	}
	function savesgtz(){
		if(parseInt($("#tz_text")[0].value)==tz&&parseInt($("#sg_text")[0].value)==sg){
			$("#if_LastSGTZ").iziModal("destroy")
			getsubmit()
			return
		}
		WRT_e.api.yz_sz.svaeShenGaoTZ({
			params:{
				"bingLiID":WRT_config.url.as_blid,
				"zhuYuanID": WRT_config.yz_sz.zyid,
				"bingQuID": WRT_config.yz_sz.bqid,
				"bingRenXM": WRT_config.yz_sz.brxm,
				"ceLiangSJ": gettime(),
				"tiZhong": parseFloat($("#tz_text")[0].value),
				"shenGao": parseFloat($("#sg_text")[0].value)
			},
			success(msg){
				if(msg.hasError==0){
					$("#if_LastSGTZ").iziModal("destroy")
					getsubmit()
				}else{
					WRT_e.ui.hint({msg:msg.errorMessage,type:'error'})
				}
			}
		})
	}
}

//提交医嘱逻辑判断
function getsubmit() {
	WRT_config.al_cy = ""
	let rtn = true
	yzdata.map(function (item) {
		if (!rtn) {
			return
		}
		if (item.YZLB == 3) {
			if (item.JX == 'Mjkl') {
				WRT_config.al_cy = 0
				return
			}
			if ((WRT_config.yz_sz.zkid == 33 && item.YZLX == 'zcydy')) {
				WRT_config.al_cy = 0
				return
			}
			if (item.JX != 'Mjkl' || (WRT_config.yz_sz.zkid != 33 && item.YZLX != 'zcydy')) {
				if (WRT_config.al_cy >= 0 && WRT_config.al_cy != "") {
					return
				}
				rtn = false
				WRT_e.ui.message({
					title: '信息窗口',
					content: '是否由我院代煎',
					onOk() {
						WRT_config.al_cy = 1
						hlyyCheck()
					},
					onCancel() {
						WRT_config.al_cy = 0
						hlyyCheck()
					}
				})
				return
			}
		} else
			if (!item.YZID) {
				WRT_e.ui.message({
					title: '信息窗口',
					content: item.MC + '医嘱未保存,无法提交',
					onOk() {
					},
					onCancel() { }
				})
				rtn = false
				return
			} else
				if (WRT_config.yz_sz.curyszkid != WRT_config.yz_sz.zkid) {
					WRT_e.ui.message({
						title: '信息窗口',
						content: '你当前的专科与病人所在专科不一致，是否继续提交？',
						onOk() {
							hlyyCheck()
						},
						onCancel() { }
					})
					rtn = false
					return
				}

	})
	if (rtn) {
		hlyyCheck()
	}
}
//合理校验
function hlyyCheck() {
	WRT_config.as_psyz = ''
	if (yz_Lbmit.length != 0) {
		//术前医嘱自动开皮试医嘱
		WRT_e.api.yz_sz.checkSqyz({
			params: {
				as_yzlst: yz_submit.join(","),
				al_blid: WRT_config.url.as_blid
			},
			success(data) {
				if (data.Code == 1) {
					if (data.Result == 1) {
						WRT_e.ui.message({
							title: '信息窗口',
							content: '需皮试医嘱开至手术室是否自动生成一条皮试医嘱到本病区？',
							onOk() {
								WRT_config.as_psyz = 1
								yjcycheck()
							},
							onCancel() {
								WRT_config.as_psyz = 0
								yjcycheck()
							}
						})
					} else {
						yjcycheck()
					}
				}
			}
		})
	} else {
		setsubmit()
	}
}

//出院带药预计出院
function yjcycheck() {
	WRT_e.api.yz_sz.checkJrcy({
		params: {
			as_yzlst: yz_submit.join(","),
			al_blid: WRT_config.url.as_blid
		},
		success(data) {
			if (data.Code == 1) {
				WRT_e.ui.message({
					title: "提示窗口",
					content: data.CodeMsg,
					onOk() {
						// hlyyCheck2()
						cykd_check()
					},
					onCancel() { }
				})
			} else {
				cykd_check()
				// hlyyCheck2()
			}
		}
	})
}

//草药快递
function cykd_check() {
	let fd = yzdata.filter(item => item.JRCY == '3' && item.YZLX == 'zcydy')
	if (fd && fd.length > 0) {
		WRT_e.api.yz_sz.getZykddz({
			params: {
				al_blid: WRT_config.url.as_blid
			},
			success(data) {
				if (data.Code == 1) {
					WRT_config.zyddz = data.Result
					let temp = ZykddzHtml(data.Result)
					WRT_e.ui.model({
						id: "if_Zykddz",
						title: "草药快递",
						width: "550px",
						iframeHeight: "500px",
						content: temp,
						closeButton: false,
						closeOnEscape: false,
						iframe: false,
					})
					if (!WRT_config.zyddz.PROVINCE) {
						db_dzselct()
					} else
						if (WRT_config.zyddz.CITY) {
							let fd = WRT_config.Province.filter(item => item.JGMC == WRT_config.zyddz.PROVINCE)
							if (fd && fd.length == 1) {
								dbZykddz('Province', fd[0].JGDM, true)
							}
						}

				}
			}
		})
		return;
	}
	hlyyCheck2()
}
//选择省市县Html
function ZykddzHtml(target) {
	let html = `
    <div id="zykdd_table">
    <label>收货人 ：</label><input id="td_SHRXM" class="text_class" type="text" value="${target.SHRXM}" /></br>
    <label>手机号码：</label><input id="td_LXDH" class="text_class" type="text" value="${target.LXDH}" /></br>
    <label>所在地区：</label>
    <select id="Province" onchange="dbZykddz('Province')">
        <option value="">选择省</option>
    ${_.map(WRT_config.Province, item =>
		`
        <option value="${item.JGDM}" title="${item.JGMC}" ${target.PROVINCE == item.JGMC ? "selected='selected'" : ""}>${item.JGMC}</option>
        `).join('')}</select>
    <select id="City" onchange="dbZykddz('City')"><option>选择市</option></select>
    <select id="County"><option>选择区</option></select>
    </br>
    <label>详情地址：</label><textarea id="tb_LXDZ" rows="3" cols="30" style="vertical-align: top;" value="">${target.LXDZ}</textarea></br>
    <label>备注：</label><textarea id="tb_bz" rows="2" cols="30" style="vertical-align: top;" value="">${target.BZ || ''}</textarea></br>
    </br>
    <button class="e_btn" onclick="Zykddz_save()">保存</button>
    <button class="e_btn" onclick="$('#if_Zykddz').iziModal('destroy')">取消</button>
    </div>
    `
	return html;
}
//默认选中地址
function db_dzselct() {
	WRT_e.api.yz_sz.getCity({
		params: {
			as_province: 330000
		},
		success(data) {
			if (data.Code == 1) {
				WRT_config.City = data.Result
				$("#Province").html(`
                    <option value="">选择省</option>
                    ${_.map(WRT_config.Province, item => `
                        <option value="${item.JGDM}" title="${item.JGMC}" ${item.JGMC == '浙江省' ? "selected='selected'" : ""}>${item.JGMC}</option>
                    `).join('')}
                `)
				$("#City").html(`
                    <option value="">选择市</option>
                    ${_.map(data.Result, item => `
                        <option value="${item.JGDM}" title="${item.JGMC}" ${item.JGMC == '温州市' ? "selected='selected'" : ""}>${item.JGMC}</option>
                    `).join('')}
                `)
			}
		}
	})
	WRT_e.api.yz_sz.getCounty({
		params: {
			as_city: 330300
		},
		success(data) {
			if (data.Code == 1) {
				$("#County").html(`
                    <option value="">选择区</option>
                    ${_.map(data.Result, item => `
                        <option value="${item.JGDM}" title="${item.JGMC}" ${WRT_config.zyddz.COUNTY == item.JGMC ? "selected='selected'" : ""}>${item.JGMC}</option>
                    `).join('')}
                `)
			}
		}
	})
}

//选择省市县
function dbZykddz(type, value, show) {
	if (type == 'Province') {
		let val = value || $("#Province option:selected")[0].value
		WRT_e.api.yz_sz.getCity({
			params: {
				as_province: val
			},
			success(data) {
				if (data.Code == 1) {
					WRT_config.City = data.Result
					$("#City").html(`
                        <option value="">选择市</option>
                        ${_.map(data.Result, item => `
                            <option value="${item.JGDM}" title="${item.JGMC}" ${WRT_config.zyddz.CITY == item.JGMC ? "selected='selected'" : ""}>${item.JGMC}</option>
                        `).join('')}
                    `)
					$("#County").html(`
                        <option value="">选择区</option>
                    `)
					if (show) {
						let fd = WRT_config.City.filter(item => item.JGMC == WRT_config.zyddz.CITY)
						if (fd && fd.length == 1) {
							dbZykddz('City', fd[0].JGDM)
						}
					}
				}
			}
		})
	} else if (type == 'City') {
		let val = value || $("#City option:selected")[0].value
		WRT_e.api.yz_sz.getCounty({
			params: {
				as_city: val
			},
			success(data) {
				if (data.Code == 1) {
					$("#County").html(`
                        <option value="">选择区</option>
                        ${_.map(data.Result, item => `
                            <option value="${item.JGDM}" title="${item.JGMC}" ${WRT_config.zyddz.COUNTY == item.JGMC ? "selected='selected'" : ""}>${item.JGMC}</option>
                        `).join('')}
                    `)
				}
			}
		})
	}

}

//草药快递保存
function Zykddz_save() {
	let name = $("#td_SHRXM")[0].value;
	let phone = $("#td_LXDH")[0].value;
	let val1 = $("#Province option:selected")[0].title;
	let val2 = $("#City option:selected")[0].title;
	let val3 = $("#County option:selected")[0].title;
	let tb_LXDZ = $("#tb_LXDZ")[0].value;
	let tb_bz = $("#tb_bz")[0].value;
	if (name == '') {
		WRT_e.ui.message({
			title: '信息窗口',
			content: '收件人不可为空',
			onOk() {
			}
		})
		return;
	} else if (phone == '') {
		WRT_e.ui.message({
			title: '信息窗口',
			content: '联系号码不可为空',
			onOk() {
			}
		})
		return;
	} else if (val1 == '') {
		WRT_e.ui.message({
			title: '信息窗口',
			content: '省份不可为空',
			onOk() {
			}
		})
		return;
	} else if (val2 == '') {
		WRT_e.ui.message({
			title: '信息窗口',
			content: '城市不可为空',
			onOk() {
			}
		})
		return;
	} else if (val3 == '') {
		WRT_e.ui.message({
			title: '信息窗口',
			content: '区（县）不可为空',
			onOk() {
			}
		})
		return;
	} else if (tb_LXDZ == '') {
		WRT_e.ui.message({
			title: '信息窗口',
			content: '详细地址不可为空',
			onOk() {
			}
		})
		return;
	}
	WRT_e.api.yz_sz.saveZykddz({
		params: {
			am_zykddz: {
				SHRXM: name,
				PROVINCE: val1,
				CITY: val2,
				COUNTY: val3,
				LXDZ: tb_LXDZ,
				LXDH: phone,
				BZ: tb_bz

			},
			al_blid: WRT_config.url.as_blid
		},
		success(data) {
			if (data.Code == 1) {
				WRT_e.ui.hint({ msg: '保存成功', type: "success" })
				$('#if_Zykddz').iziModal('destroy')
				hlyyCheck2()
			}
		}
	})

}


//合理用药
function hlyyCheck2() {
	WRT_e.api.yz_sz.hlyyCheck2({

		params: {
			as_action: "add",
			as_yzlst: yz_submit.join(","),
			al_blid: WRT_config.url.as_blid
		},
		success(data) {
			if (data.Code == 1) {
				let gyjg = data.Result
				
				let rtn = true
				if (gyjg.result) {
					let temp = "<div>"
					WRT_config.sms = gyjg.SMS
					let arr = gyjg.result.message
					let LJDJ = false
					let XSDJ = true
					let infos=[]
					arr.map(function (item) {
						infos = [...infos,...item.infos]
					})
					infos.map(function (key) {
						if (key.severity >= gyjg.LJDJ || key.severity >= gyjg.XSDJ) {
							rtn = false
							temp += `
								<div>
								<span>●</span><a id="${key.drug_id}" class="drug_name" onclick="goyp(${key.drug_id})">${key.drug_name}</a>(${key.severity})：<span style="padding-left: 10px;">${key.error_info + key.advice}</span>
								</div>
								`
						}
						if (key.severity >= gyjg.LJDJ) {
							LJDJ = true
						}
						if (key.severity < gyjg.XSDJ) {
							XSDJ = false
						}
					})
					temp += `<div style="padding: 10px 0;">
                            <button class=""e_btn" onclick="$('#hlyyCheck2').iziModal('destroy')">返回修改</button>
                            ${LJDJ ? `` : XSDJ ? `<button class=""e_btn" onclick="jxtjsub()">继续提交</button>` : ''}
                        </div>
                        <div>*显示规则：警示等级≥${gyjg.XSDJ}级</div>
                        <div>拦截规则：警示等级≥${gyjg.LJDJ}级，被拦截的医嘱必须修改，否则无法提交！！</div>
                        <div>如果没有拦截信息且没有警示信息，本窗口将不显示</div>
                    </div>`
					if (rtn) {
						setsubmit()
						return
					}
					WRT_e.ui.model({
						id: "hlyyCheck2",
						title: '医嘱警示信息查看',
						width: "650px",
						content: temp,
						closeButton: true,
						closeOnEscape: false,
						iframe: false,
					})
				} else {
					setsubmit()
				}

			} else {
				let temp = `
                <div><span style="color:red">合理用药程序出错</span></div>
                <div><p>为了不影响正常业务，你可以继续提交,那么本次医嘱将不会进行合理用药干预;也可以联系信息科</p></div>
                <div><span>程序错误信息：</span>${data.CodeMsg}</div>
                <div><button class=""e_btn" onclick="jxtjsub()">继续提交</button></div>
                `
				WRT_e.ui.model({
					id: "hlyyCheck2",
					title: '医嘱警示信息查看',
					width: "650px",
					content: temp,
					closeButton: true,
					closeOnEscape: false,
					iframe: false,
				})
			}
		}
	})
}
//继续提交
function jxtjsub() {
	$("#hlyyCheck2").iziModal('destroy');
	setsubmit()
}
//提交
function setsubmit() {
	let al_cy = `${WRT_config.al_cy == "" ? 0 : WRT_config.al_cy}`
	let params = {
		as_yzlst: yz_submit.join(","),
		al_blid: WRT_config.url.as_blid,
		al_cy: al_cy,
		as_psyz: WRT_config.as_psyz || ''
	}
	WRT_e.api.yz_sz.SubmitYz({
		params: params, success(data) {
			if (data.Code == 1) {
				WRT_e.ui.hint({ msg: "提交成功", type: "success" })
				// 医嘱提交成功后，hmzk="1"时调用JS：
				if (WRT_config.yz_sz.hmzk == "1" && typeof (masonStartCheckYz) == 'function') {
					// masonStartCheckYz(BLID, YHID, as_yzlst, 'submit');//as_yzlst同医嘱提交
					masonStartCheckYz(WRT_config.url["as_blid"], WRT_config.yz_sz.yhid, yz_submit.join(","), 'submit');//as_yzlst同医嘱提交
				} else if (typeof (masonStartCheckYz) != 'function') {
					WRT_e.ui.hint({ msg: '惠每质控调用失败，请联系信息处', type: 'error' })
				}
				if (data.Result) {
					WRT_e.ui.message({
						title: '信息窗口',
						content: data.Result,
						onOk() {
						}
					})
				}
				
				getBrYz()
				refresh_init()
				WRT_e.api.yz_sz.getSbXlzfYp({
					params: { al_zyid: WRT_config.yz_sz.zyid },
					success(data) {
						WRT_config.SbXlzfYp = data.Result
					}
				})
				imgtab(false)
				parent.getbrmrjmsyl()
			} else {
				WRT_e.ui.message({
					title: '信息窗口',
					content: data.CodeMsg,
					onOk() {
					}
				})
			}
		}
	})
}
//药品说明书
function goyp(yaid) {
	let rtn = []
	if (WRT_config.sms) {
		rtn = WRT_config.sms.split("@ypid@")
		let iWidth = 1000;
		let iHeight = 700;
		let iTop = (window.screen.availHeight - 30 - iHeight) / 2;
		let iLeft = (window.screen.availWidth - 10 - iWidth) / 2;;
		let ls_url = rtn[0] + yaid + rtn[1]
		window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
	}
}

//国家采购判断
function checkGjcg(yzlx, lb, ypmc, canprescribe_zy, canprescribe_cydy, gjcg, sfsl) {
	var lb_return = true;
	if (gjcg == '0') {
		return true;
	}
	if (yzlx == "cydy") {
		if (canprescribe_cydy == '0') {
			// $.messager.alert("国家集采提示信息", '国家集采：无法开具' + ypmc + '，请选择同一通用名的集采药品替代！', 'info');
			WRT_e.ui.message({
				title: '国家集采提示信息',
				content: '国家集采：无法开具' + ypmc + '，请选择同一通用名的集采药品替代！',
				onOk() {
				}
			})
			lb_return = false;
		}
		else if (gjcg == '13') {
			if (sfsl != '1') {//收费数量传进来是字符串格式
				// $.messager.alert("国家集采提示信息", "药品【" + ypmc + "】的出院带药仅限开一盒!", "warning");
				WRT_e.ui.message({
					title: '国家集采提示信息',
					content: "药品【" + ypmc + "】的出院带药仅限开一盒!",
					onOk() {
					}
				})
				lb_return = false;
			}
		}
	}
	else {
		//if (gjcg == '12' || gjcg == '14') {
		if (canprescribe_zy == '0') {
			// $.messager.alert("国家集采提示信息", '国家集采：无法开具' + ypmc + '，请选择同一通用名的集采药品替代！', 'info');
			WRT_e.ui.message({
				title: '国家集采提示信息',
				content: '国家集采：无法开具' + ypmc + '，请选择同一通用名的集采药品替代！',
				onOk() {
				}
			})
			lb_return = false;
		}
		//}
	}
	return lb_return;
}
//模板处理html(中成药分餐)
function getZCYhtml(arr) {
	WRT_config.mbdr = null
	MBDRList = []
	let ZH = ""
	arr.map(function (item) {
		if (ZH == '') {
			ZH = item.ZH
			item.selectOw = true
		} else if (ZH == item.ZH) {
			item.selectOw = false
		} else if (ZH != item.ZH) {
			ZH = item.ZH
			item.selectOw = true
		}
	})
	var html = `
    <table id="tb_yplistdetail" border="0" width="600px" cellspacing="0" cellpadding="0">
			<tbody>
				<tr>
						<td width="80px">选择</td>
						<td width="250px">药品名称</td>
						<td width="80px">规格</td>
						<td width="80px">单价</td>
				</tr>
				${_.map(arr, (obj, index) =>
			`<tr>
				<td width="80px">
						<input type="button" class="e_btn" value="选择" onclick="onselectMBall(this,${index})">
				</td>
				<td width="250px">${obj.MC}</td>
				<td width="80px">${obj.YPGG}</td>
				<td width="40px">${obj.SJ || ''}</td>
		</tr>`
		).join('')}
			</tbody>
    </table>`
	return html
}

//模板处理html(外购药品,赠送药品)
function getXNhtml(arr, title) {
	WRT_config.mbdr = null
	MBDRList = []
	let ZH = ""
	arr.map(function (item) {
		if (ZH == '') {
			ZH = item.ZH
			item.selectOw = true
		} else if (ZH == item.ZH) {
			item.selectOw = false
		} else if (ZH != item.ZH) {
			ZH = item.ZH
			item.selectOw = true
		}
	})
	var html = `
    <table id="tb_yplistdetail" border="0" width="600px" cellspacing="0" cellpadding="0">
        <tbody>
            <tr>
                <td width="80px">选择</td>
                <td width="250px">药品名称</td>
                <td width="80px">规格</td>
                <td width="80px">剂型</td>
                ${title=='外购药品'?`<td width="250px">社保代码</td>`:``}
            </tr>
            ${_.map(arr, (obj, index) =>
		`<tr>
                    <td width="80px">
                        <input type="button" class="e_btn" value="选择" onclick="onselectYGYP(this,${index})">
                    </td>
                    <td width="250px">${obj.MC}</td>
                    <td width="80px">${obj.YPGG}</td>
                    <td width="40px">${obj.JX || ''}</td>
										${title=='外购药品'?`<td width="250px">${obj.SBDM && obj.SBDM!=null ? obj.SBDM : ''}</td>`:``}				
                </tr>`
	).join('')}
        </tbody>
    </table>`
	return html
}

//模板处理html(新版外购药品)
function getXBXNhtml(arr, title) {
	WRT_config.mbdr = null
	MBDRList = []
	let ZH = ""
	// arr = [...ssWGYP]
	arr.map(function (item) {
		if (ZH == '') {
			ZH = item.ZH
			item.selectOw = true
		} else if (ZH == item.ZH) {
			item.selectOw = false
		} else if (ZH != item.ZH) {
			ZH = item.ZH
			item.selectOw = true
		}
	})
	// console.log('新版外购药品',arr,arr.length==0);
	// ${name}
	if (arr.length==0) {
		var html = `
			<div class="search_K">
				<input type="secrch" class="form-control left_Search_input" id="left_Search_input" placeholder="请输入药品拼音首字母或者中文" onkeydown="wgypChange(event)" autocomplete="off" style="width:70%">
				<span class="input-group-btn">
					<button class="btn btn-default glyphicon glyphicon-search" type="button" onclick="onSearchWGYP()" ></button>
				</span>
			</div>
		  <table id="tb_yplistdetail" class="xbwgyTable" border="0" width="1000px" cellspacing="0" cellpadding="0">
				<tbody>
					<tr>
						<td width="80px">选择</td>
						<td width="250px">药品名称</td>
						<td width="80px">规格</td>
						<td width="40px">剂型</td>
						<td width="250px">社保代码</td>
						<td width="150px">厂家信息</td>
						<td width="120px">是否国谈药品</td>
					</tr>
					${_.map(arr, (obj, index) =>
					`<tr>
						<td width="80px">
							<input type="button" class="e_btn" value="选择" onclick="onselectYGYP(this,${index})">
						</td>
						<td width="250px">${obj.MC}</td>
						<td width="80px">${obj.YPGG}</td>
						<td width="40px">${obj.JX || ''}</td>
						<td width="250px">${obj.SBDM}</td>
						<td width="150px" title="${obj.CDMC||''}" style="white-space: normal;">${obj.CDMC||''}</td>			
						<td width="120px">${obj.GTYP}</td>			
					</tr>`).join('')}
				</tbody>
		  </table>`
		var XBXNhtml = new getXBXNhtml_View();
		let titleId = '.'+title
		XBXNhtml.$el = $(titleId);
		XBXNhtml.init({
			data: arr
		}).render();
		return html
	} else {
		var XBXNhtml = new getXBXNhtml_View();
		let titleId = '.'+title
		XBXNhtml.$el = $(titleId);
		XBXNhtml.init({
			data: arr
		}).render();
	}
}

// 模板处理html
function getMBhtml(arr, title) {
	WRT_config.mbdr = null
	MBDRList = []
	let ZH = ""
	if (title != '常用药模板') {
		arr.map(function (item) {
			if (ZH == '') {
				ZH = item.ZH
				item.selectOw = true
			} else if (ZH == item.ZH) {
				item.selectOw = false
			} else if (ZH != item.ZH) {
				ZH = item.ZH
				item.selectOw = true
			}
		})
	} else {
		arr.map(function (item) {
			item.selectOw = true
		})
	}
	var html = `
    <table id="tb_yplistdetail" border="0" width="600px" cellspacing="0" cellpadding="0">
        <tbody>
            <tr>
                <td width="80px">选择</td>
                <td width="250px">药品名称</td>
                <td width="80px">规格</td>
                <td width="80px">单价</td>
                <td width="40px">数量</td>
                <td width="120px">用法用量</td>
            </tr>
            ${_.map(arr, (obj, index) =>
		`<tr>
                    <td width="80px">
                        ${obj.selectOw ? `<input type="button" class="e_btn" value="选择" onclick="onselectMBall(this,${index},'${title}')">` : ``}
                    </td>
                    <td width="250px">${obj.MC}</td>
                    <td width="80px">${obj.JL}</td>
                    <td width="80px">${obj.YPGG}</td>
                    <td width="40px">${obj.SL || ''}</td>
                    ${obj.YZLB != 3 ? `<td width="80px">${obj.YCYL && obj.YCYL > 0 ? obj.YCYL : "适量"} ${obj.JLDW} ${obj.PL} ${obj.FF}</td>` : `<td width="80px">${obj.FF}</td>`}
                </tr>`
	).join('')}
        </tbody>
    </table>`
	return html
}
// /模板处理html（检查用药）
function getJCYYhtml(arr, title) {
	WRT_config.mbdr = null
	MBDRList = []
	let ZH = ""
	arr.map(function (item) {
		if (ZH == '') {
			ZH = item.ZH
			item.selectOw = true
		} else if (ZH == item.ZH) {
			item.selectOw = false
		} else if (ZH != item.ZH) {
			ZH = item.ZH
			item.selectOw = true
		}
	})
	var html = `
    <table id="tb_yplistdetail" border="0" width="600px" cellspacing="0" cellpadding="0">
        <tbody>
            <tr>
                <td width="80px">选择</td>
                <td width="250px">药品名称</td>
                <td width="80px">规格</td>
                <td width="80px">数量</td>
            </tr>
            ${_.map(arr, (obj, index) =>
		`<tr>
                    <td width="80px">
                        <input type="button" class="e_btn" value="选择" onclick="onselectMBall(this,${index},'检查用药')">
                    </td>
                    <td width="250px">${obj.MC}</td>
                    <td width="80px">${obj.YPGG}</td>
                    <td width="80px">${obj.SL}</td>
                </tr>`
	).join('')}
        </tbody>
    </table>`
	return html
}

//模板详情html
function getMBdetilhtml(arr, title) {
	var html = `
    <table id="tb_yplistdetail" border="0" width="600px" cellspacing="0" cellpadding="0">
        ${title ? `<button class="e_btn" onclick="goback(this)">${title}</button>` : ''}
        <tbody>
            <tr>
                <td width="80px">选择</td>
                <td width="250px">药品名称</td>
                <td width="80px">规格</td>
                <td width="80px">单价</td>
                <td width="40px">数量</td>
                <td width="120px">用法用量</td>
            </tr>
            ${_.map(arr, (obj, index) =>
		`<tr>
                    <td width="80px">
                        ${obj.show == true ? `<input type="button" class="e_btn" value="选择" onclick="onselectMBall(this,${index},'药品个人模板')">` : ''}
                    </td>
                    <td width="250px">${obj.MC}</td>
                    <td width="80px">${obj.JL}</td>
                    <td width="80px">${obj.YPGG}</td>
                    <td width="40px">${obj.SL || ''}</td>
                    ${obj.YZLB != 3 ? `<td width="80px">${obj.YCYL && obj.YCYL > 0 ? obj.YCYL : "适量"} ${obj.JLDW} ${obj.PL} ${obj.FF}</td>` : `<td width="80px">${obj.FF}</td>`}
                </tr>`
	).join('')}
        </tbody>
    </table>`
	return html
}

//外购药品模板选择
function onselectYGYP(ev, index){
	let list = yp_mb[index]
	list.ZHTXT = ''
	if(yzindex-1>=0){
		if(yzdata[yzindex]&&yzdata[yzindex].MC=='' && (yzdata[yzindex-1].ZHTXT=='┐'|| yzdata[yzindex-1].ZHTXT=='|')){
			list.ZHTXT='┘'
		}
	}
	// if(yzdata[yzindex]&&yzdata[yzindex].MC=='' && (yzdata[yzindex-1].ZHTXT=='┐'|| yzdata[yzindex-1].ZHTXT=='|')){
	// 	list.ZHTXT='┘'
	// }
	GetYpMbMx(list, 0,true)
}
//模板选中
function onselectMB(ev, index) {
	let list = yp_mb[index]
	GetYpMbMx(list, 0)
}
//点击模板（组）选择
function onselectMBall(ev, index, title) {
	WRT_config.mbdr = null
	// MBDRList = []
	let list = yp_mb[index]
	let fd = []
	if (title == '常用药模板') {
		GetYpMbMx(list, 0)
		return
	}
	if (!(list.ZH > 0)) {
		if (MBDRList.length > 0 && mbindex != null) {
			WRT_config.mbdr = true
			MBDRList.push(list)
			exportMBList()
			return
		}
		
		GetYpMbMx(list, 0)
		return
	}
	fd = yp_mb.filter(item => list.ZH && item.ZH == list.ZH)
	getKSSJ_TXT() // 不确定会不会导致时间获取过于频繁
	let sj = $("#ck_blyz").is(":checked")
	if (fd && fd.length > 1) {
		// MBDRList = fd || []
		fd.map(function (item, idx) {
			if (sj && ((yzdata.length-1)-fd.length>0)) {//-fd.length
				fd[idx].KSSJ_TXT = yzdata[(yzdata.length-1)].KSSJ_TXT
			}
			if (idx == 0) {
				item.ZHTXT = "┐"
				// 同组获取相同时间
				if (sj && ((yzdata.length-1)-fd.length>=0)) {
					fd[idx].KSSJ_TXT = yzdata[(yzdata.length-1)].KSSJ_TXT
				} else {
					// Jhkssj = gettime(item.KSSJ_TXT, true)
					getKSSJ_TXT() // 不确定会不会导致时间获取过于频繁
					item.KSSJ_TXT =  WRT_config.defaultKSSJ_TXT
				}
			} else if (idx == fd.length - 1) {
				item.ZHTXT = "┘"
				if (sj && ((yzdata.length-1)-fd.length>=0)) {
					fd[idx].KSSJ_TXT = yzdata[(yzdata.length-1)].KSSJ_TXT
				}else if (fd[idx].KSSJ_TXT == '' || fd[idx].KSSJ_TXT == undefined) {
					item.KSSJ_TXT =  WRT_config.defaultKSSJ_TXT

				}
			} else {
				item.ZHTXT = '|'
				if (sj && ((yzdata.length-1)-fd.length>=0)) {
					fd[idx].KSSJ_TXT = yzdata[(yzdata.length-1)].KSSJ_TXT
				}else if (fd[idx].KSSJ_TXT == '' || fd[idx].KSSJ_TXT == undefined) {
					item.KSSJ_TXT =  WRT_config.defaultKSSJ_TXT
				}
			}
		})
		if (MBDRList.length != 0 || mbindex < MBDRList.length) {
			WRT_config.mbdr = true
			MBDRList = [...MBDRList, ...fd]
			// exportMBList()
		} else {
			WRT_config.mbdr = true
			MBDRList = fd || []
			mbindex = 0
			GetYpMbMx(MBDRList[0], 0)
		}
		// mbindex = 0
		// GetYpMbMx(MBDRList[0], 0)
	} else {
		if (MBDRList.length > 0 && mbindex != null) {
			WRT_config.mbdr = true
			MBDRList.push(list)
			exportMBList()
			return
		}
		GetYpMbMx(fd[0], 0)
	}
}

//模板事件处理
function GetYpMbMx(list, idx,name) {
	let yp = {}
	if (!list) {
		return
	}
	list.iswpy=name
	let sj = $("#ck_blyz").is(":checked")
	if (sj) {
		list.KSSJ_TXT = yzdata[(yzdata.length-1)].KSSJ_TXT
	}
	if (list.YZLB == "3" && WRT_config.url.as_yzlb == 'cq') {
		WRT_e.ui.message({
			title: '提示',
			content: '长期医嘱不能导入草药模板',
			onOk() {
				delZHTXT(yzindex)
				yzdata.splice(yzindex, 1)
				exportMBList()
			}
		})
		return
	}
	// let yzlx=''
	if (yzdata[yzindex] && !yzdata[yzindex].MC) {
		yz_yzlx = yzdata[yzindex].YZLX
		yzdata.splice(yzindex, 1)
	}
	let foot = new footbtn_View()
	if (idx == 0 || list.ZHTXT == '┐' || list.ZHTXT == '' || list.ZHTXT == null) {
		yzindex = yzdata.length - 1
		foot.addgroup()
	} else if (list.ZHTXT == '|' || list.ZHTXT == '┘') {
		yzindex = yzdata.length - 1
		foot.add()
	}
	let arr = list.DATAOP.split(",") || []
	let dataop = ['ypid', 'ycyl', 'jldw', 'ff', 'pl', 'gysj', 'tsyf', 'mc', 'gg', 'yfdm', 'yzlb', 'jx', 'cfyp', 'rmyp', 'dw', 'sl', 'cqcy', 'yplb', 'kzjb', 'gllx', 'gjcg', 'canprescribe_zy', 'canprescribe_cydy', '3000', 'fzqypid', 'sj', 'wgypsqjlid']
	arr.map(function (item, index) {
		if (item) {
			yp[dataop[index]] = item
		} else {
			yp[dataop[index]] = ''
		}
	})
	list.lb_copy = list.LB
	let yid = list.YPID
	if (typeof list.YPID === 'number' && !isNaN(list.YPID)) {
		yid = JSON.stringify(list.YPID)
	}
	list.YPID = yid
	let id = yid.substr(0, 1)
	if (id == 'y') {
		list.LB = 2
	} else if (id == 'z') {
		list.LB = 1
	} else {
		list.LB = 3
		list.SFSL = ""
	}
	// list.ZH = ""
	list.CXTS = 0
	// list.XH = list
	// list.YZLX = isyzlb(list)
	list.ZXFF = list.FF
	list.ZXPL = list.PL
	// list.LB = 3
	list.YCYL = list.YCYL
	list.BQID = `${list.BQID ? list.BQID : WRT_config.yz_sz.bqid}`

	if (WRT_config.url.as_yzlb == 'jcyy') {
		list.BQID = WRT_config.yz_sz.ryzbzxxnbq
	}
	// console.log(list,list.MC,list.YZLX,yzdata[yzindex]);
	yp_dataop = yp
	for (let i in list) {
		if (!list[i]) {
			list[i] = ""
		}
	}
	for (let i in yz_obj) {
		if (!list[i]) {
			list[i] = ""
		}
	}
	if (yzdata[yzdata.length - 1] && yzdata[yzdata.length - 1].MC) {
		yz_yzlx = yzdata[yzdata.length - 1].YZLX
	}
	yzindex = yzdata.length - 1
	if (yz_yzlx == '') {
		list.YZLX = isyzlb(list)
	} else if (list.YZLB == 3) {
		list.YZLX = yz_yzlx || ""
		if (yz_yzlx != 'cy' && yz_yzlx != 'zcydy') {
			list.YZLX = 'cy'
			yz_yzlx = "cy"
		}
	} else if (list.YZLB != 3) {
		list.YZLX = yz_yzlx || ""
		if (yz_yzlx == 'cq') {
			list.YZLX = 'cq'
			yz_yzlx = "cq"
		} else if (yz_yzlx != 'ls' && yz_yzlx != 'cydy') {
			list.YZLX = 'ls'
			yz_yzlx = "ls"
		}
	}
	let param = {}
	for (let i in list) {
		if (i != "XH" && i != "ZH" && i != "ZSZH") {
			param[i] = list[i]
		}
	}
	yzdata[yzindex] = param
	sortData()
	let obj = {
		lx: "药",
		mc: list.MC || "",
		ypid: list.YPID || "",
		gg: list.YPGG || "",
		sj: list.SJ || "",
		bz: list.ZTBZ || "",
		dataop: yp
	}
	yzlogic("MC" + yzindex, obj, yzindex)
}
//返回
function goback(ev) {
	let temp = getypgrmb(WRT_config.ypgrmb, ev.innerHTML)
	$('#mb_gryp').removeClass('none')
	$("#dv_mbHtml_model").html(temp)
}
//模板个人模板
function getypgrmb(arr) {
	var html = `
    <table id="dv_mbHtml" border="0" width="600px" cellspacing="0" cellpadding="0">
        <tr class="headerbody">
            <td width="60px">类别</td>
            <td width="250px">模板名称 </td>
            <td width="60px">拼音码</td>
            <td width="40px">详细</td>
        </tr>
        ${_.map(arr, (obj, index) =>
		`
            <tr>
                <td width="60px">${obj.YZLB == 1 ? '西药' : obj.YZLB == 2 ? '中成药' : '草药'}</td>
                <td width="250px">${obj.MBMC}</td>
                <td width="60px">${obj.PY}</td>
                <td width="40px"><button class="e_btn" onclick="YpMbDetail(${index});$('#mb_gryp').addClass('none')">详细</button></td>
            </tr>`
	).join('')}
    </table>`
	return html
}
//模板个人模板详细
function YpMbDetail(index) {
	let obj = WRT_config.ypgrmb[index]
	if (obj) {
		WRT_e.api.yz_sz.getZkYfdms({
			params: { al_zkid: WRT_config.yz_sz.zkid, as_yqdm: WRT_config.yz_sz.yqdm },
			success(data) {
				let arr = data.Result || []
				let YFDM = ''
				arr.map(function (item) {
					if (item.YZLB == obj.YZLB) {
						YFDM = item.YFDM
						return
					}
				})
				WRT_e.api.yz_sz.GetYpMbDetail({
					params: {
						al_zkid: obj.ZKID || WRT_config.yz_sz.zkid,
						as_mbmc: obj.MBMC,
						as_yzlb: obj.YZLB,
						as_lb: obj.LB,
						al_blid: WRT_config.url.as_blid,
						as_yfdm: YFDM
					},
					success(data) {
						yp_mb = data.Result || []
						let ZH = ''
						yp_mb.map(function (item, idx) {
							if (ZH == '') {
								ZH = item.ZH
								item.show = true
							} else if (ZH == item.ZH) {
								item.show = false
							} else if (ZH != item.ZH) {
								ZH = item.ZH
								item.show = true
							}
						})
						let temp = getMBdetilhtml(yp_mb || [], '返回我的个人模板')
						$("#dv_mbHtml_model").html(temp)
					}
				})
			}
		})
	}
}
//回车事件
function calAge() {
	var evt = window.event || e;
	if (evt.keyCode == 13) {
		if (type = 'mbgryp') {
			grypmb()
			return;
		}
		tytjMb()
	}
}
//药品个人模板查询
function grypmb() {
	let name = $("#td_gryp")[0].value
	let array = []
	let arr = WRT_config.ypgrmb_copy
	arr.map(function (item) {
		if (item.PY.indexOf(name) >= 0 || item.MBMC.indexOf(name) >= 0) {
			array.push(item)
		}
	})
	WRT_config.ypgrmb = array
	let temp = getypgrmb(array)
	$("#dv_mbHtml_model").html(temp)

}
//通用试剂查询
function tytjMb() {
	WRT_e.api.yz_sz.GetTytjMb({
		params: {
			as_mc: $("#txt_mc")[0].value
		},
		success(data) {
			WRT_config.tysj = data.Result || []
			let temp = `<tr class="headerbody">
                <td width="60px">类别</td>
                <td width="250px">模板名称 </td>
                <td width="60px">拼音码</td>
                <td width="40px">详细</td>
            </tr>
            ${_.map(WRT_config.tysj, (obj, index) =>
				`
                <tr>
                    <td width="60px">${obj.YZLB == 1 ? '西药' : obj.YZLB == 2 ? '中成药' : '草药'}</td>
                    <td width="250px">${obj.MBMC}</td>
                    <td width="60px">${obj.PY}</td>
                    <td width="40px"><button class="e_btn" onclick="tysjDetail(${index})">详细</button></td>
                </tr>`
			).join('')}`
			$("#dv_tysjHtml").html(temp)
		}
	})
}
//通用试剂模板
function gotysjmb(arr) {
	var html = `
        <div style="height: 40px;">
            模板名称：<input id="txt_mc" autocomplete="off" onkeydown="calAge(event)" />
            <button class="e_btn" onclick="tytjMb()">查询</button>
        </div>
        <table id="dv_tysjHtml" border="0" width="600px" cellspacing="0" cellpadding="0" style="max-height: 400px;position: absolute;">
            <tr class="headerbody">
                <td width="60px">类别</td>
                <td width="250px">模板名称 </td>
                <td width="60px">拼音码</td>
                <td width="40px">详细</td>
            </tr>
            ${_.map(arr, (obj, index) =>
		`
                <tr>
                    <td width="60px">${obj.YZLB == 1 ? '西药' : obj.YZLB == 2 ? '中成药' : '草药'}</td>
                    <td width="250px">${obj.MBMC}</td>
                    <td width="60px">${obj.PY}</td>
                    <td width="40px"><button class="e_btn" onclick="tysjDetail(${index})">详细</button></td>
                </tr>`
	).join('')}
        </table>
    `
	return html
}
//通用试剂模板详细
function tysjDetail(index) {
	let obj = WRT_config.tysj[index]
	if (obj) {
		WRT_e.api.yz_sz.getZkYfdms({
			params: { al_zkid: WRT_config.yz_sz.zkid, as_yqdm: WRT_config.yz_sz.yqdm },
			success(data) {
				let arr = data.Result || []
				let YFDM = ''
				arr.map(function (item) {
					if (item.YZLB == obj.YZLB) {
						YFDM = item.YFDM
						return
					}
				})
				WRT_e.api.yz_sz.GetYpMbDetail({
					params: {
						al_zkid: obj.ZKID || WRT_config.yz_sz.zkid,
						as_mbmc: obj.MBMC,
						as_yzlb: obj.YZLB,
						as_lb: obj.LB,
						al_blid: WRT_config.url.as_blid,
						as_yfdm: YFDM
					},
					success(data) {
						yp_mb = data.Result || []
						let ZH = ''
						yp_mb.map(function (item, idx) {
							if (ZH == '') {
								ZH = item.ZH
								item.show = true
							} else if (ZH == item.ZH) {
								item.show = false
							} else if (ZH != item.ZH) {
								ZH = item.ZH
								item.show = true
							}
						})
						let temp = getMBdetilhtml(yp_mb || [])
						WRT_e.ui.model({
							id: "if_tysj_detail",
							title: "通用试剂明细",
							width: "650px",
							content: temp,
							iframe: false,
						})
					}
				})
			}
		})
	}
}

//治疗医嘱模板
function getMbZlMb() {
	ZlmbMxLbList = []
	WRT_config.zlmbmx = {}
	let left = getMbZlMbLeftHtml()
	let right = getMbZlMbRightHtml()
	let html = `
    ${left}
    <div class="zlmbright">
        <div id="Panel1" style="font-family:宋体;font-size:12px;height:500px;overflow-y:scroll;">
            <legend class="legython" style="width:700px">
                已选择的治疗医嘱
            </legend>
            <table cellspacing="1" style="width:700px" id="tb_zlyz">
                ${right}
            </table>
            </br>
            <input id="btn_sub" class="e_btn" type="button" value="确定" onclick="f_zlclick(this)"></input>
        </div>
    </div>
    `
	WRT_e.ui.model({
		id: 'if_zlyzmb',
		title: '治疗医嘱模板',
		width: "945px",
		content: html,
		iframe: false,
	})
}
//治疗医嘱模板左侧
function getMbZlMbLeftHtml() {
	let html = `
    <div class="zhmbleft">
        <div class="Zlmb_title">个人模板</div>
        <div id="dv_grmb">
            ${WRT_config.zlmb1 && WRT_config.zlmb1.length > 0 ? `${_.map(WRT_config.zlmb1, (obj, index) =>
		`<div onclick="showMbZl(this,${obj.MBID})" class="Zlmb_title1" title="${obj.MC}">&nbsp;&nbsp;&nbsp;&nbsp;${obj.MC}</div>
                <div id="zl_grmb${obj.MBID}" class="Zldetail"></div>`
	).join('')}` : `<div class="Zlmb_title1">&nbsp;&nbsp;&nbsp;&nbsp;没有个人模板！</div>`}
        </div>
        <div class="Zlmb_title">专科模板</div>
        <div id="dv_zkmb">
            ${WRT_config.zlmb2 && WRT_config.zlmb2.length > 0 ? `${_.map(WRT_config.zlmb2, (obj, index) =>
		`<div onclick="showMbZl(this,${obj.MBID})" class="Zlmb_title1" title="${obj.MC}">&nbsp;&nbsp;&nbsp;&nbsp;${obj.MC}</div>
                <div id="zl_grmb${obj.MBID}" class="Zldetail"></div>`
	).join('')}` : `<div class="Zlmb_title1">&nbsp;&nbsp;&nbsp;&nbsp;没有专科模板！</div>`}
        </div>
    </div>
    `
	return html
}
//治疗模板左侧详情
function showMbZl(ev, id) {
	if ($(`#zl_grmb${id}`)[0].style && $(`#zl_grmb${id}`)[0].style.display == 'block') {
		$(`#zl_grmb${id}`).css("display", "none")
		return
	}
	if (WRT_config.zlmbmx[`ZlMbMx${id}`]) {
		let temp = MbZlHtml(WRT_config.zlmbmx[`ZlMbMx${id}`], id)
		$(`#zl_grmb${id}`).html(temp)
		$(`#zl_grmb${id}`).css("display", "block")
		return
	}
	WRT_e.api.yz_sz.getZlMbMx({
		params: { al_mbid: id },
		success(data) {
			if (data && data.Code == 1) {
				WRT_config.zlmbmx[`ZlMbMx${id}`] = JSON.parse(data.Result)
				let temp = MbZlHtml(WRT_config.zlmbmx[`ZlMbMx${id}`], id)
				$(`#zl_grmb${id}`).html(temp)
				$(`#zl_grmb${id}`).css("display", "block")
			}
		}
	})
}
//治疗模板左侧详情html
function MbZlHtml(arr, id) {
	let html = `
    ${_.map(arr, (obj, index) =>
		`&nbsp;&nbsp;&nbsp;&nbsp;
        <input type="checkbox" id="${id}_${obj.MC}" name="cbzlyzmb" onclick="f_clickZlMx(this,${id},'${obj.MC}',${index})">
        <label for="${id}_${obj.MC}" style="font-weight: initial;margin-bottom:0px">${obj.MC}</label>`
	).join("</br>")}
    `
	return html
}
//点击选中
function f_clickZlMx(ev, id, mc, index) {
	let list = WRT_config.zlmbmx[`ZlMbMx${id}`][index]
	if (list) {
		if (ev.checked) {
			let obj = {}
			for (let j in yz_obj) {
				obj[j] = yz_obj[j] || ''
			}
			for (let i in list) {
				obj[i] = list[i] || ''
			}
			obj.title = mc
			obj.LB = 1
			obj.XMID = 'z' + obj.YZMLID
			ZlmbMxLbList.push(obj)
		} else {
			ZlmbMxLbList.map(function (item, index) {
				if (item.MBID == list.MBID && item.YZMLID == list.YZMLID) {
					ZlmbMxLbList.splice(index, 1)
					return
				}
			})
		}
		let html = getMbZlMbRightHtml()
		$("#tb_zlyz").html(html)
	}
}
//治疗医嘱模板右侧
function getMbZlMbRightHtml() {
	let html = `
        ${ZlmbMxLbList && ZlmbMxLbList.length > 0 ? `
        <tbody>
        <tr style="background: #3D6CC8;color: #fff;">
            <td style="width: 80px;" align="center">模板名称</td>
            <td style="width: 130px;" align="center">医嘱名称</td>
            <td style="width: 30px;" align="center">数量</td>
            <td style="width: 50px;" align="center">持续天数</td>
            <td style="width: 90px;" align="center">执行频率</td>
            <td style="width: 80px;" align="center">执行方法</td>
            <td style="width: 80px;" align="center">特殊用法</td>
            <td style="width: 105px;" align="center">计划开始时间</td>
        </tr>
        ${_.map(ZlmbMxLbList, (obj, index) =>
		`<tr class="str">
                <td style="width: 80px;" align="center">
                    <span>${obj.title}</span>
                </td>
                <td style="width: 130px; color: blue;" align="center">
                    <span>${obj.MC}</span>
                </td>
                <td style="width: 30px;" align="center">
                    <input type="text" name="tb_sl" value="${obj.SFSL || ''}" autocomplete="off" ="f_onchange(this,'SFSL',${index})" style="width:26px;"></td>
                <td style="width: 50px;" align="center">
                    <input type="text" name="tb_cxts" value="${obj.CXTS || 999}" autocomplete="off" oninput="f_onchange(this,'CXTS',${index})" style="width:48px;">
                </td>
                <td style="width: 90px;" align="center">
                    <select name="ddlt_zxpl" onchange="f_onchange(this,'ZXPL',${index},'select')" style="width:88px;">
                        <option value=""></option>
                        ${_.map(WRT_config.pl, (item) =>
			`<option ${item.DM == obj.ZXPL ? `selected='selected'` : ""} value="${item.DM}">${item.MC}</option>`
		)}
                    </select>
                </td>
                <td style="width: 80px;" align="center">
                    <input type="text" name="tb_zxff" value="${obj.ZXFF || ""}" oninput="f_onchange(this,'ZXFF',${index})" autocomplete="off" style="width:78px;">
                </td>
                <td style="width:80px;" align="center">
                    <input type="text" name="tb_tsyf" value="${obj.TSSM || ''}" oninput="f_onchange(this,'TSSM',${index})" autocomplete="off" style="width:78px;">
                </td>
                <td style="width: 105px;" align="center">
                    <input name="dtm_kssj" type="text" value="${gettime(obj.KSSJ_TXT || '', true)}" id="dtm_kssj${index}" onblur="f_onchange(this,'KSSJ_TXT',${index},'time')" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm',readOnly:false})" autocomplete="off" style="width:105px;">
                </td>
            </tr>`
	).join('')}
        </tbody>`: ``}
        `
	return html
}
//数据回写
function f_onchange(ev, name, index, type) {
	if (type == 'select') {
		ZlmbMxLbList[index][name] = ev[ev.selectedIndex].value
	} else if (type == 'time') {
		ZlmbMxLbList[index][name] = $(`#dtm_kssj${index}`)[0].value
	} else {
		ZlmbMxLbList[index][name] = ev.value
	}
}
//治疗模板
function f_zlclick() {
	ZhmbSrdata = ZlmbMxLbList || []
	if (ZhmbSrdata.length > 0) {
		ZhmbSrindex = 0
		zhmbsrDr(0)
	}
}

//综合医嘱模板
function getZhmbByLb() {
	ZhmbByLbList = []
	let left = getZhmbByLbLeftHtml()
	let right = getZhmbByLbRightHtml()
	let html = `
    ${left}
    <div class="zhmbright">
        <div id="Panel1" style="font-family:宋体;font-size:12px;height:500px;overflow-y:scroll;">
            <legend class="legython">
                医嘱模板明细
            </legend>
            <table cellspacing="1" style="width:1200px">
                <tbody>
                    <tr style="background: #3D6CC8;color: #fff;">
                        <td style="width: 40px;" align="center">选择</td>
                        <td style="width: 140px;" align="center">模板名称</td>
                        <td style="width: 40px;" align="center">类别</td>
                        <td style="width: 200px;" align="center">医嘱名称</td>
                        <td style="width: 20px;" align="center">组</td>
                        <td style="width: 130px;" align="center">规格/单价</td>
                        <td style="width: 50px;" align="center">一次用量</td>
                        <td style="width: 50px;" align="center">剂量单位</td>
                        <td style="width: 90px;" align="center">用法</td>
                        <td style="width: 90px;" align="center">频率</td>
                        <td style="width: 90px;" align="center">用药/执行时间</td>
                        <td style="width: 100px;" align="center">医嘱说明/备注说明</td>
                        <td style="width: 50px;" align="center">持续天数</td>
                        <td style="width: 50px;" align="center">数量/剂数</td>
                        <td style="width: 50px;" align="center">单位</td>
                        <td style="width: 50px;" align="center">次数</td>
                    </tr>
                </tbody>
            </table>
            <table cellspacing="1" style="width:1200px" id="tb_yzmbmx">
            ${right}
            </table>
            </br>
            <input id="btn_sub" class="e_btn" type="button" value="导入" onclick="f_subclick(this)"></input>
            <input id="btn_selectAll" class="e_btn" type="button" value="全选" onclick="f_selectAll()"></input>
            <input id="btn_unselectAll" class="e_btn" type="button" value="全不选" onclick="f_unselectAll()"></input>
            <input id="btn_fanSelect" class="e_btn" type="button" value="反选" onclick="f_fanSelect()"></input>
        </div>
    </div>
    `
	WRT_e.ui.model({
		id: 'if_zhyzmb',
		title: '综合医嘱模板',
		width: "1245px",
		content: html,
		iframe: false,
	})
}
//综合模板界面左侧HTML
function getZhmbByLbLeftHtml() {
	let html = `
    <div class="zhmbleft">
        <div class="Zhmb_title">个人模板</div>
        <div id="dv_grmb">
            ${WRT_config.ZhmbByLb4 && WRT_config.ZhmbByLb4.length > 0 ? `${_.map(WRT_config.ZhmbByLb4, (obj, index) =>
		`<div onclick="showMb(this,${obj.MBID})" class="Zhmb_title1" title="${obj.MC}">&nbsp;&nbsp;┗&nbsp;&nbsp;${obj.MC}</div>`
	).join('')}` : `<div class="Zhmb_title1">&nbsp;&nbsp;&nbsp;&nbsp;没有个人模板！</div>`}
        </div>
        <div class="Zhmb_title">专科模板</div>
        <div id="dv_zkmb">
            ${WRT_config.ZhmbByLb3 && WRT_config.ZhmbByLb3.length > 0 ? `${_.map(WRT_config.ZhmbByLb3, (obj, index) =>
		`<div onclick="showMb(this,${obj.MBID})" class="Zhmb_title1" title="${obj.MC}">&nbsp;&nbsp;┗&nbsp;&nbsp;${obj.MC}</div>`
	).join('')}` : `<div class="Zhmb_title1">&nbsp;&nbsp;&nbsp;&nbsp;没有专科模板！</div>`}
        </div>
        <div class="Zhmb_title">全院模板</div>
        <div id="dv_qymb">
            ${WRT_config.ZhmbByLb1 && WRT_config.ZhmbByLb1.length > 0 ? `${_.map(WRT_config.ZhmbByLb1, (obj, index) =>
		`<div onclick="showMb(this,${obj.MBID})" class="Zhmb_title1" title="${obj.MC}">&nbsp;&nbsp;┗&nbsp;&nbsp;${obj.MC}</div>`
	).join('')}` : `<div class="Zhmb_title1">&nbsp;&nbsp;&nbsp;&nbsp;没有全院模板！</div>`}
        </div>
    </div>
    `
	return html
}
//点击查询综合模板详细
function showMb(ev, mid) {
	WRT_config.ZhmbByLbTitle = null
	WRT_e.api.yz_sz.getZhMbmx({
		params: { al_mbid: mid, al_blid: WRT_config.url.as_blid },
		success(data) {
			if (data && data.Code == 1) {
				let zh = ""
				data.Result.map(function (item) {
					item.checked = true
					item.show = true
					if (item.ZHTXT) {
						item.show = false
						if (zh == item.ZH) {
							if (item.ZHTXT == '┐') {
								item.show = true
							} else {
								item.show = false
							}
						} else if (zh != item.ZH) {
							zh = item.ZH
							if (item.ZHTXT == '┐') {
								item.show = true
							} else {
								item.show = false
							}
						}
					}
				})
				ZhmbByLbList = data.Result
				WRT_config.ZhmbByLbTitle = ev.title
				let html = getZhmbByLbRightHtml(ev.title)
				$("#tb_yzmbmx").html(html)
			}
		}
	})

}
//综合模板界面右侧HTML
function getZhmbByLbRightHtml(title) {
	let html = `
        ${ZhmbByLbList && ZhmbByLbList.length > 0 ? `<tbody>
        ${_.map(ZhmbByLbList, (obj, index) =>
		`<tr class="trc">
                <td style="width: 40px; border-bottom: dimgray 1px dashed" align="center">
                    ${obj.show ? `<input onclick="xzZhyzmbmx_click(this,${index})" ${obj.checked ? `checked="checked"` : ""} value="${obj.MBMXID}" val="${obj.ZH}" type="checkbox" name="zhyzmbxzAll" />` : ""}
                </td>
                <td style="width: 140px; border-bottom: dimgray 1px dashed" align="center"><input type="hidden" name="hdf_mbid" value="6" />
                    <span>${title || ""}</span>
                </td>
                <td style="width: 40px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.LB == 1 ? '治疗' : obj.LB == 2 ? '饮食' : obj.LB == 3 ? '药品' : '嘱托'}</span>
                </td>
                <td style="width: 200px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.MC || ""}</span>
                </td>
                <td style="width: 20px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.ZHTXT || ""}</span>
                </td>
                <td style="width: 130px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.GG || ""}</span>
                </td>
                <td style="width: 50px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.YCYL || ""}</span>
                </td>
                <td style="width: 50px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.JLDW || ""}</span>
                </td>
                <td style="width: 90px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.ZXFF || ""}</span>
                </td>
                <td style="width: 90px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.ZXPL || ""}</span>
                </td>
                <td style="width: 90px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.GYSJ || ""}</span>
                </td>
                <td style="width: 100px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.TSYF || ""}</span>
                </td>
                <td style="width: 50px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.CXTS || ""}</span>
                </td>
                <td style="width: 50px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.SFSL || ""}</span>
                </td>
                <td style="width: 50px; border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.DW || ""}</span>
                </td>
                <td style="width:50px;border-bottom: dimgray 1px dashed" align="center">
                    <span>${obj.SFCS || ""}</span>
                </td>
            </tr>`
	).join('')}
        </tbody>`: `${title ? `【${title}】该模板没有内容！` : ""}`}
        `
	return html
}
//勾选
function xzZhyzmbmx_click(ev, index) {
	let list = ZhmbByLbList[index]
	if (list && list.ZHTXT) {
		ZhmbByLbList.map(key => {
			if (key.ZH == list.ZH) {
				key.checked = ev.checked
			}
		})
	} else {
		ZhmbByLbList[index].checked = ev.checked
	}
}
//导入
function f_subclick() {
	let arr = []
	ZhmbByLbList.map(item => {
		if (item.checked) {
			arr.push(item)
		}
	})
	if (arr.length == 0) {
		WRT_e.ui.hint({ type: "error", msg: "没有数据，无法导入。" })
		return
	}
	ZhmbSrdata = arr || []
	if (ZhmbSrdata.length > 0) {
		ZhmbSrindex = 0
		zhmbsrDr(0)
	}
}
//全选
function f_selectAll() {
	ZhmbByLbList.map(item => {
		item.checked = true
	})
	let html = getZhmbByLbRightHtml(WRT_config.ZhmbByLbTitle)
	$("#tb_yzmbmx").html(html)
}
//全不选
function f_unselectAll() {
	ZhmbByLbList.map(item => {
		item.checked = false
	})
	let html = getZhmbByLbRightHtml(WRT_config.ZhmbByLbTitle)
	$("#tb_yzmbmx").html(html)
}
//反选
function f_fanSelect() {
	ZhmbByLbList.map(item => {
		item.checked = !item.checked
	})
	let html = getZhmbByLbRightHtml(WRT_config.ZhmbByLbTitle)
	$("#tb_yzmbmx").html(html)
}

//历史用药切换
function lsyybtn(ev, name) {
	let start = $("#txt_kssj")[0].value || getNowFormatDate()
	let end = $("#txt_jssj")[0].value || getNowFormatDate()
	WRT_e.api.yz_sz.getLsyyMb({
		params: { al_blid: WRT_config.url.as_blid, as_lsyylb: name, as_kssj: start, as_jssj: end },
		success(data) {
			if (data.Code == 1) {
				let arr = data.Result
				yp_mb = arr
				let temp = getMBhtml(arr)
				$("#yplistls_table").html(temp)
			}
		}
	})
}
//查询历史用药
function searchlsyy() {
	let name = getRadioButtonCheckedValue('rbl_lsyy')
	let start = $("#txt_kssj")[0].value || getNowFormatDate()
	let end = $("#txt_jssj")[0].value || getNowFormatDate()
	WRT_e.api.yz_sz.getLsyyMb({
		params: { al_blid: WRT_config.url.as_blid, as_lsyylb: name, as_kssj: start, as_jssj: end },
		success(data) {
			if (data.Code == 1) {
				let arr = data.Result
				yp_mb = arr
				let temp = getMBhtml(arr)
				$("#yplistls_table").html(temp)
			}
		}
	})
}
//判断单选框是否选中
function getRadioButtonCheckedValue(tagNameAttr) {
	var radio_tag = document.getElementsByName(tagNameAttr);
	for (var i = 0; i < radio_tag.length; i++) {
		if (radio_tag[i].checked) {
			var checkvalue = radio_tag[i].value;
			return checkvalue;
		}
	}
}

//模板展示
function goModelhtml(title, name) {
	getKSSJ_TXT() // 通过接口获取时间  WRT_config.defaultKSSJ_TXT后续新增使用这个时间
	WRT_e.api.yz_sz.getYpMbByLb({
		params: { as_type: name, al_blid: WRT_config.url.as_blid, al_zkid: WRT_config.yz_sz.zkid, as_yqdm: WRT_config.yz_sz.yqdm, as_yzlx: WRT_config.url.as_yzlb },
		success(data) {
			if (data.Code == 1) {
				yp_mb = data.Result
				let temp = ""
				if (title == "中成药分餐") {
					temp = getZCYhtml(data.Result)
				} else if (title == "外购药品" || title == "赠送药品") {
					temp = getXNhtml(data.Result, title)
				} else if (title == "新版外购药品") {
					title = '外配药品'
					temp = getXBXNhtml(data.Result, title)
				} else if(title == "检查用药列表"){
					temp = getJCYYhtml(data.Result, title)
				} else {
					temp = getMBhtml(data.Result, title)
				}

				WRT_e.ui.model({
					id: name,
					title: title,
					width: `${title=='外配药品'?'1150px':'650px'}`,
					content: temp,
					iframe: false,
				})
			}
		}
	})
}
function wgypChange(e){
	var evt = window.event || e;
	if (evt.keyCode == 13) {
		onSearchWGYP()
	}
}
// 外购药搜索
function onSearchWGYP() {
	let text=$("#left_Search_input")[0].value
	WRT_e.api.yz_sz.GetNewWgyp({
		params: { 
			// as_key: "测试",//搜索名称（支持拼音和中文）
			// al_zkid: 88,
			// al_blid: 1493631,
			// as_yfdms: "2A3^2A2^2A2",//药房代码
			as_key: text,//搜索名称（支持拼音和中文）
			al_zkid: WRT_config.yz_sz.zkid,
			al_blid: WRT_config.url.as_blid,
			as_yfdms: WRT_config.zkyf,//药房代码
			as_type: ""//传空
		},
		success(data) {
			if (data.Code == 1) {
				yp_mb = data.Result
				getXBXNhtml(data.Result, 'xbwgyTable')
			}
		}
	})
}
//处理删除组号
function delZHTXT(index) {
	index = parseInt(index)
	let list = yzdata[index]
	if (list && list.ZHTXT) {
		let fd = []
		let xh = yzdata[index].XH
		if (typeof xh == 'number') {

		} else if (xh.indexOf("-") >= 0) {
			xh = xh.split("-")[0]
		} else {
			xh = parseInt(xh)
		}
		yzdata.map(function (item) {
			let t = item.XH
			if (typeof t == 'number') {

			} else if (t.indexOf("-") >= 0) {
				t = t.split("-")[0]
			} else {
				t = parseInt(t)
			}
			if (xh === t) {
				fd.push(item)
			}
		})
		if (fd && fd.length == 1) {
			yzdata[index].ZHTXT = ''
		} else if (fd && fd.length == 2) {
			if (list.ZHTXT == '┐') {
				yzdata[index + 1].ZHTXT = ''
			} else {
				yzdata[index - 1].ZHTXT = ''
			}
			yzdata[index].ZHTXT = ''
		} else if (fd && fd.length >= 3) {
			if (list.ZHTXT == '┐') {
				yzdata[index + 1].ZHTXT = '┐'
			} else if (list.ZHTXT == '┘') {
				yzdata[index - 1].ZHTXT = '┘'
			}
			yzdata[index].ZHTXT = ''
		}
	}
	if (index > 0) {
		yzindex = index - 1
	} else if (index == 0) {
		yzindex = null
	}

}

//处理未提交序号
function sortData() {
	let index = 0, ZH = 0
	// yzdata=
	yzdata.map(function (item, k) {
		if (item.YZLX == 'cy' || item.YZLX == 'zcydy') {
			if (item.ZHTXT == '┐') {
				index++;
				ZH = 0
				ZH++;
				item.XH = index + '-' + ZH
			} else if (item.ZHTXT == '|') {
				ZH++;
				item.XH = index + '-' + ZH
			} else if (item.ZHTXT == '┘') {
				ZH++;
				item.XH = index + '-' + ZH
			} else {
				index++;
				ZH = 0
				ZH++;
				item.XH = index + '-' + ZH
			}
		} else {
			if (item.ZHTXT == '┐') {
				index++;
				item.XH = index
			} else if (item.ZHTXT == '|') {
				item.XH = index
			} else if (item.ZHTXT == '┘') {
				item.XH = index
			} else {
				index++;
				// item.BH=k
				item.XH = index
			}
		}
		// return item
	})
	// delZHTXT()
}
//设置下一个焦点聚焦的对象
function SetNextFocusObj(obj) {
	// order=order_list
	if (!obj) {
		obj = {
			LB: "",
			YZLX: WRT_config.url.as_yzlb
		}
	}
	let tmptype = "", j_yzlx = ""
	if (obj.LB == "" || !obj.LB) {
		tmptype = "zl"
	} else {
		tmptype = obj.LB
	}
	j_yzlx = obj.YZLX
	switch (tmptype) {
		case 1:
		case '1':
		case 4:
		case '4':
			order = _SYS_ZL_SORT
			break;
		case 3:
		case '3':
			if (j_yzlx == "cydy") //cydy出院带药
				order = _SYS_DY_SORT;
			else if (j_yzlx == "zcydy" || j_yzlx == "cy")//zcydy草药带药  cy草药医嘱 
				order = _SYS_CY_SORT;
			else
				order = _SYS_YP_SORT;
			break;
		default:
			order = _SYS_YS_SORT;
			break;
	}

}
//
function oncheck(index) {
	let list = WRT_config.BrYz[index]
	WRT_config.BrYz[index].check = !list.check
}
//选中数组
function selectcolor(index){
	
	// $("#data_list .headertable")[index].addClass("trbody_color")
	if ($(`#data_list .tryzsz${index}`).hasClass('trbody_color')){ //判断元素是否已经包含指定的class
		$(`#data_list .tryzsz${index}`).removeClass('trbody_color'); //移除该class
	} else {
		$("#data_list .headertable").removeClass("trbody_color")
		$(`#data_list .tryzsz${index}`).addClass('trbody_color'); //添加该class
	}
}
//勾选数组
function dbsel(index) {
	let list = WRT_config.BrYz[index]
	list.check = !list.check
	if (list && list.ZH != '' && list.ZH != 0) {
		WRT_config.BrYz.map(function (item) {
			if (item.ZH == list.ZH) {
				item.check = list.check
			}
		})
	}
	WRT_config.BrYz[index] = list
	var yzsz = new yzsz_View();
	yzsz.$el = $("#data_list");
	yzsz.init({
		data: XHData(WRT_config.BrYz)
	}).render();
}

//处理已提交序号
function XHData(arr) {
	let index = 0, ZH = ''
	let target = arr.map(function (item) {
		item.YZLX_copy = item.YZLX
		let fd = arr.find(e => e.YZID != item.YZID && item.YZ_RQ == e.YZ_RQ)
		if (fd && fd.YZ_RQ) {
			arr.map(e => {
				if (e.YZID != item.YZID && item.YZ_RQ == e.YZ_RQ) {
					e.YZ_RQ = ""
				}
			})
		}
		if (item.ZUSTR == '┐') {
			index++;
			item.XH = index
			item.ZXFF = ''
			item.ZXPL = ''
			item.GYSJ = ''
			item.YSQM = ""
			item.ZXBQ = ""
			item.TZ_RQ = ""
			item.DCRY = ""
			item.SCLRSJSTR = ""
			item.ZXPL2 = ""
			item.DCSJ_RQ = ""
			item.YZLX = -1
			item.show = false
		} else if (item.ZUSTR == '|' || item.ZUSTR == '│') {
			// item.XH=index
			item.XH = ""
			item.show = true
			item.ZXFF = ''
			item.ZXPL = ''
			item.GYSJ = ''
			item.ZXBQ = ""
			item.YSQM = ""
			item.DCRY = ""
			item.DCSJ_RQ = ""
			item.ZXPL2 = ""
			item.TZ_RQ = ""
			item.SCLRSJSTR = ""
			item.YZLX = -1
		} else if (item.ZUSTR == '┘') {
			// item.XH=index;
			item.XH = ""
			item.show = true
		} else if (item.ZUSTR == '') {
			index++;
			item.XH = index
		}
		return item
	})
	return target
}

//处理快捷键事件
function model_input(ev, index) {
	let el = ev + index
	yzindex = index
	let obj = yzdata[yzindex]
	if (!obj) {
		return
	}
	inputClent($(`#${el}`).parent(), el, yzindex)
	if (ev.indexOf('ZXFF') >= 0) {
		let html = ``
		if (obj.YZLX == 'cy' || obj.YZLX == 'zcydy') {
			html = ypyfplHtml(WRT_config.cyyf)
		} else {
			html = ypyfplHtml(WRT_config.yf)
		}
		$(`#${el}_list`).html(html)
	} else if (ev.indexOf('ZXPL') >= 0) {
		let html = ``
		if (obj.YPID == "z" + WRT_config.yz_sz.xtmlid) {
			html = ypyfplHtml(WRT_config.xtpl, "ZXPL", yzindex)
		} else {
			html = ypyfplHtml(WRT_config.pl, "ZXPL", yzindex)
		}
		$(`#${el}_list`).html(html)
	}
	$(".isModel").css("display", "none")
	yz_isModel = el
	$(`#${el}_list`).css("display", "block")
}
//拼音，简称查询
function search(el, val, target) {
	let arr = []
	target.map(function (item) {
		let py = item.PY.toLowerCase()
		if (item.DM.indexOf(val) >= 0 || py.indexOf(val) >= 0) {
			arr.push(item)
		}
	})
	let html = ypyfplHtml(arr)
	$(`#${el}_list`).html(html)
	if (arr.length > 0 && val) {
		currentLine = 1
		sroll(el)
	} else {
		currentLine = 0
	}
}
//保存模板
function SaveAsMb(type) {
	var jqYplist = $("#tb_mdetail input[name='mbypxz']");
	var ls_zhlst = [];
	var yzlb = "";
	for (var i = 0, j = jqYplist.length; i < j; i++) {
		var isChecked = jqYplist[i].checked;
		if (isChecked) {
			if (type == '2') {
				var dataops = jqYplist.eq(i).attr("dataop");//dataop = ypid,ycyl,jldw,ff,pl,gysj,tsyf,mc,gg,yfdm,yzlb,jx
				var j_data = dataops.split(',');
				if (ls_zhlst == '') {
					yzlb = j_data[10];//yzlb
				}
				else {
					if (yzlb != j_data[10]) {
						WRT_e.ui.message({
							title: '提示信息',
							content: '保存失败，西药、中成药、草药不能混合保存为一个模板',
							onOk() {
							}
						})
						return;
					}
				}
			}
			let val = jqYplist.eq(i).val() || ""
			let ZH = jqYplist[i].className
			ls_zhlst.push(parseInt(ZH))
			// let fd=WRT_config.YpByTime.filter(ev=>ev.ZH==ZH)
			// if(fd&&fd.length>0){
			//     fd.map(function(k){
			//         if(k.ZH==ZH){
			//             ls_zhlst.push(k.YPID)
			//         }
			//     })
			// }
		}
	}
	if (ls_zhlst.length == 0) {
		WRT_e.ui.message({
			title: '提示信息',
			content: '请选择药品！',
			onOk() {
			},
			onCancel() { }
		})
		return;
	}
	let title = ""
	if (type == '1') {
		SaveAsMb_ajax(ls_zhlst, type, '常用药');
	} else if (type == '2') {
		WRT_e.ui.message({
			title: '模板名称',
			content: '<input type="text" name="mbname" autocomplete="off" style="border:1px solid">',
			onOk() {
				let div = $("input[name='mbname']")
				title = div[0].value
				if (!title) {
					WRT_e.ui.hint({ type: 'error', message: '请输入模板名称！' })
					return
				} else if (title.length > 10) {
					WRT_e.ui.hint({ type: 'error', message: '模板名称不能大于10个字！' })
					return;
				}
				SaveAsMb_ajax(ls_zhlst, type, title);
			}
		})
	}
}
//保存模板
function SaveAsMb_ajax(ls_zhlst, type, mbmc) {
	WRT_e.api.yz_sz.SaveAsYpMb({
		params: { as_zhlst: ls_zhlst.join(","), as_type: type, as_mbmc: mbmc },
		success(data) {
			if (data.CodeMsg) {
				WRT_e.ui.message({
					title: '提示信息',
					content: data.CodeMsg,
					onOk() {
					}
				})
			}
		}
	})
}

//获取弹窗内容
function getContent() {
	let content = '';
	cacheList.forEach((item, index) => {
		content += `
            <tr class="${move_index === index ? "moveClass" : ''}" style="border:2px solid #88A0B9;">
                <td width="30" >
                    <input type="checkbox" ${item.show ? `checked="${item.key}"` : ``} onchange="onCheckbox('${index}')"/>
                </td> 
                <td width="350" style="text-align:left">${item.key}</td> 
                <td >
                    <button class="e_btn" onclick="moveItem('up',${index})">上移</button>
                </td> 
                <td>
                    <button class="e_btn" style="margin:3px 3px" onclick="moveItem('down',${index})">下移</button>
                </td> 
            <tr>`
	})
	let html = `
    <div style=" height: 550px;overflow: auto;">
        <table id="model_swap" style="border:2px solid #88A0B9;text-align:center">${content}</table>
        <button class="e_btn" style="position:fixed; bottom:30px;right:30px;" onclick="upSortList()">确认</button>
    </div>
            `
	return html
}
//表格配置弹窗
function swapColumn() {
	let arr = sortList.map((key) => {
		let list = key
		let fd = WRT_config.yzbtgxh.filter(k => k.key == key.key)
		if (fd && fd.length > 0) {
			list.show = fd[0].show
		} else {
			list.show = false
		}
		return { ...list }
	})
	cacheList = arr.map((item) => {
		return { ...item }
	})
	// WRT_e.api.blcxgj.getSearch({
	//     params: {
	//         "as_tjyj": ""
	//     },
	//     success(data) {
	//         if (data.Code == 1) {
	//             WRT_config.search = data.Result
	//             $("#resultShow").html(
	//                 new result_View().init({
	//                     data: WRT_config.search
	//                 }).render().$el
	//             )
	//         }
	//     }
	// })
	move_index = ''
	WRT_e.ui.model({
		id: "tableConfig",
		title: "表格配置",
		width: "650px",
		content: getContent(),
		iframe: false,
		iframeHeight: "200px"
	})

}
//选择表格头
function onCheckbox(index) {
	cacheList[index].show = !cacheList[index].show;
}
//移动表格头
function moveItem(value, index) {
	let itemUp = {}, itemDown = {};
	if (value == 'up' && index > 0) {
		itemUp = { ...cacheList[index - 1] };
		itemDown = { ...cacheList[index] };
		cacheList[index - 1] = itemDown;
		cacheList[index] = itemUp;
		move_index = index - 1

	} else if (value == 'down' && index < cacheList.length - 1) {
		itemUp = { ...cacheList[index] };
		itemDown = { ...cacheList[index + 1] };
		cacheList[index] = itemDown;
		cacheList[index + 1] = itemUp;
		move_index = index + 1
	}
	$("#tableConfig")
		.find(".izi_modal_content")
		.html(getContent())
}

//表格自定义确认
function upSortList() {
	let arr = cacheList;
	// localStorage.clear()
	// localStorage.setItem('swapColumn_' + WRT_config.yz_sz.brxm, JSON.stringify(sortList))
	WRT_e.api.yz_sz.saveYzbtGxh({
		params: { "as_yzbtgxh": JSON.stringify(arr) },
		success(data) {
			if (data.Code == 1) {
				WRT_config.yzbtgxh = arr
				WRT_e.ui.hint({ type: 'success', msg: '确认成功' })
				$("#tableConfig").iziModal('destroy');
				refresh_init()
			}
		}
	})
}
//双签名点击
function doublesign() {
	WRT_e.ui.model({
		id: "if_doublesign",
		title: "二次密码校验",
		width: "400px",
		iframeURL: WRT_config.server + "/confirmPWD.aspx?as_tempid=0.8856296195472312&as_version=djm",
		iframe: true,
	})
}
//双签名确认
function submitDoublesign() {
	let val = $("#tb_doublesign_bz").val()
	if (WRT_config.Sqm) {
		WRT_e.api.yz_sz.submitSqm({
			params: {
				al_blid: WRT_config.url.as_blid,
				al_ypZh: al_ypZh,
				as_msg: val
			},
			success(data) {
				if (data.Code == 1) {
					WRT_e.ui.hint({ type: 'success', msg: '确认成功' })
					$("#if_Sqm").iziModal('destroy')
				}
			}
		})
	}
}
//双签名取消
function submitDoublesign() {
	$("#if_Sqm").iziModal('destroy')
}

//独立成组处理
function f_btdlcz(index) {
	if (yzdata[index].ZHTXT == '┐') {
		if (yzdata[index + 1].ZHTXT == '┘') {
			yzdata[index + 1].ZHTXT = ''
		} else if (yzdata[index + 1].ZHTXT == '|') {
			yzdata[index + 1].ZHTXT = '┐'
		}
		yzdata[index].ZHTXT = ''
	} else if (yzdata[index].ZHTXT == '|') {
		if (yzdata[index - 1].ZHTXT == '┐') {
			yzdata[index - 1].ZHTXT = ''
		}
		yzdata[index].ZHTXT = '┐'
	} else if (yzdata[index].ZHTXT == '┘') {
		if (yzdata[index - 1].ZHTXT == '┐') {
			yzdata[index - 1].ZHTXT = ''
		} else if (yzdata[index - 1].ZHTXT == '|') {
			yzdata[index - 1].ZHTXT = '┘'
		}
		// yzdata[index-1].ZHTXT='┘'
		yzdata[index].ZHTXT = ''
	}
}



/************************************************* */
var app = {
	init: function () {
		//定义时间格式
		$.fn.datetimebox.defaults.formatter = function (date) {
			// let val=gettime(date)
			var d = new Date(date);
			var year = d.getFullYear();
			var month = change(d.getMonth() + 1);
			var day = change(d.getDate());
			var hour = change(d.getHours());
			var minute = change(d.getMinutes());
			var second = change(d.getSeconds());
			function change(t) {
				if (t < 10) {
					return "0" + t;
				} else {
					return t;
				}
			}
			var time = ''
			if (second == "00") {
				time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
			} else {
				time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
			}
			return time;
		}
		$(document).bind("click", function (e) {
			var target = $(e.target);
			if (target[0] && target[0].id) {
				let id = target[0].id
				// if(target[0].id)
				// let params=delindex(id)
				// let value=params.index
				// if(!isNaN(value)&&value!=''){
				//     yzindex=value
				// }
				// if(id.indexOf("MC"))
			}

			if (yz_isModel && (target.attr('id') != yz_isModel)) {
				if (yz_isModel.indexOf('SJZZXPL') >= 0) {
					let k = yz_isModel.slice(7)
					let name = yz_isModel.slice(0, 7)
					let value = yzdata[k][name]
					$(`#${yz_isModel}`)[0].value = $(`#${yz_isModel}`)[0].ov || getName(value, 'SJZZXPL') || ""
				} else if (yz_isModel.indexOf('ZXFF') >= 0 || yz_isModel.indexOf('ZXPL') >= 0 || yz_isModel.indexOf('GYSJ') >= 0) {
					let k = yz_isModel.slice(4)
					let name = yz_isModel.slice(0, 4)
					let value = yzdata[k][name]
					$(`#${yz_isModel}`)[0].value = $(`#${yz_isModel}`)[0].ov || getName(value, 'ZXPL') || ""
				}
				if (yz_isModel.indexOf('MC') < 0 || target.attr('id')) {
					if (yz_isModel.indexOf('MC') > -1 && $(`#${yz_isModel}`)[0]) {
						if (yzdata[yzindex] && yzdata[yzindex].MC) {
							$(`#${yz_isModel}`)[0].value = WRT_config.defaultValue || $(`#${yz_isModel}`)[0].ov || ""
						} else {
							$(`#${yz_isModel}`)[0].value = ''
						}
						$(`.mclist`).hide();
					}
					if (target.closest(yz_isModel).length == 0) {
						$(`#${yz_isModel}_list`).hide();
						yz_isModel = false
					}
				}

			} else if (!yz_isModel) {
				$(".isModel").css("display", "none")
			}
			if (WRT_config.yz_isMc && target.attr('id') != WRT_config.yz_isMc) {
				if ($(`#${WRT_config.yz_isMc}`)[0] && $(`#${WRT_config.yz_isMc}`)[0].ov) {
					$(`#${WRT_config.yz_isMc}`)[0].value = $(`#${WRT_config.yz_isMc}`)[0].ov || ""
				}
				WRT_config.yz_isMc = false
			}
		})
		//绘制头部按钮模块
		var btnlist = new btnlist_View();
		btnlist.$el = $("#btn_list");
		btnlist.init({ data: WRT_config.yz_sz }).render();
		//启动hover
		// $('.dropdown-toggle').dropdownHover();
		// if(WRT_config.url.as_yzlb=="ls"){
		//     $(`#datetimebox1`).datetimebox({
		//         value: $(`#datetimebox1`)[0].value,
		//         required: true,
		//         showSeconds: true,
		//     });

		//     $(`#datetimebox1`).datetimebox({
		//         stopFirstChangeEvent: true,
		//         onChange: function () {
		//             var options = $(this).datetimebox('options');
		//             if (options.stopFirstChangeEvent) {
		//                 options.stopFirstChangeEvent = false;
		//                 return;
		//             }
		//             if (this.id) {
		//                 let time = $(this).datetimebox('getValue');
		//                 $(`#datetimebox1`)[0].value=time
		//             }
		//         }
		//     });
		// }
		refresh_init()
	}
}

//模板处理html(新版外购药品)
var getXBXNhtml_View = WRT_e.view.extend({
	render() {
		var html = `
		<table id="tb_yplistdetail" border="0" cellspacing="0" cellpadding="0" style="width: 100%;">
			<tbody>
				<tr>
					<td width="80px">选择</td>
					<td width="250px">药品名称</td>
					<td width="80px">规格</td>
					<td width="40px">剂型</td>
					<td width="250px">社保代码</td>
					<td width="150px">厂家信息</td>
					<td width="120px">是否国谈药品</td>
				</tr>
				${_.map(this.data, (obj, index) =>
				`<tr>
					<td width="80px">
						<input type="button" class="e_btn" value="选择" onclick="onselectYGYP(this,${index})">
					</td>
					<td width="250px">${obj.MC}</td>
					<td width="80px">${obj.YPGG}</td>
					<td width="40px">${obj.JX || ''}</td>
					<td width="250px">${obj.SBDM}</td>
					<td width="150px" title="${obj.CDMC||''}" style="white-space: normal;">${obj.CDMC||''}</td>	
					<td width="120px">${obj.GTYP}</td>			
				</tr>`).join('')}
			</tbody>
		</table>`
		this.$el.html(html)
		return this;
	}
})
//头部按钮块
var btnlist_View = WRT_e.view.extend({
	render: function () {
		// <li onclick="goModelhtml('新版外购药品','xbwgyp')"><a href="#">外配药品</a></li>
		//<li onclick="goModelhtml('外购药品','wgyp')"><a href="#">外购药品</a></li>
		var html = `
        <table>
            <tr>
            <td><input type="button" class="e_btn btn-refresh" value="刷新本页"></td>
            <td class="checkbox-list-left">
                <div class="syyz checkbox">
                    <label>
                        <input id="syyz" type="checkbox" >所有医嘱
                    </label>
                </div>
            </td>
            <td class="checkbox-list-right">
                <div class="xstzyz checkbox">
                    <label>
                        <input id="xstzyz" type="checkbox" checked>显示停止医嘱
                    </label>
                </div>
            </td>
            <td class="spance_btn"><button class="stopYz e_btn">${WRT_config.url.as_yzlb == 'cq' ? "立即停" : "撤销"}(9)</button></td>
            <td class="spance_btn"><button class="disSubmitYz e_btn">撤回(0)</button></td>
            ${WRT_config.url.as_yzlb == 'cq' ? `<td class="spance_btn"><button class="stopCqYz e_btn">执行后停(p)</button></td>` : ""}
            ${WRT_config.url.as_yzlb == 'cq' ? `<td class="spance_btn"><button class="stopAt8Tomorrow e_btn">次日停(M)</button></td>` : ""}
            ${this.data.sfxt ? `<td class="spance_btn"><button class="submitSqm e_btn">双签名</button></td>` : ``}
            ${WRT_config.url.as_yzlb == 'ls' ? `<td class="spance_btn"><button class="lzzlyzZx e_btn">医嘱打√执行</button></td>` : ""}
            ${WRT_config.url.as_yzlb == 'ls' ? `<td class="spance_btn"><button class="yzcxzx e_btn">撤销执行</button></td>` : ""}
            <td class="spance_btn">
                <div class="dropdown">
                <div class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown">
                    <a class="downmenu">历史用药<span class="glyphicon glyphicon-triangle-bottom" aria-hidden="true"></span></a>
                </div>
                <ul class="dropdown-menu dropdown-menu-left">
                    <li class="if_winMb"><a href="#">历史用药(H)</a></li>
                    <li class="if_zykfy"><a href="#">在用口服药(K)</a></li>
                    <li class="if_zyzj"><a href="#">在用针剂</a></li>
										<li class="if_lczyyy"><a href="#">历次住院用药</a></li>
                </ul>
                </div>
            </td>
            <td class="spance_btn"><button class="btn_ypsms e_btn">药品说明书(Y)</button></td>
            <td class="spance_btn">
                <div class="dropdown">
                <div class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown">
                    <a class="downmenu">其他功能<span class="glyphicon glyphicon-triangle-bottom" aria-hidden="true"></span></a>
                </div>
                <ul class="dropdown-menu dropdown-menu-left">
                    <li class="hzyz360"><a href="#">患者医嘱360</a></li>
                    <li class="showbhzs"><a href="#">闭环展示</a></li>
                    <li class="showblsjsb"><a href="#">不良事件上报</a></li>
										<li class="sclsyz"><a href="#">生成临时医嘱</a></li>
                </ul>
                </div>
            </td>
            <td class="spance_btn">
                <div class="dropdown">
                <div class="dropdown-toggle" data-toggle="dropdown" data-hover="dropdown">
                    <a class="downmenu">虚拟药库<span class="glyphicon glyphicon-triangle-bottom" aria-hidden="true"></span></a>
                </div>
                <ul class="dropdown-menu dropdown-menu-left">
									<li onclick="goModelhtml('新版外购药品','xbwgyp')"><a href="#">外配药品</a></li>
									<li onclick="goModelhtml('赠送药品','zsyp')"><a href="#">赠送药品</a></li>
									<li onclick="goModelhtml('外购药品','wgyp')"><a href="#" style="color: rgb(0 0 0 / 30%);">外购药品</a></li>
                </ul>
                </div>
            </td>
            <td class="spance_btn">
                ${WRT_config.url.as_yzlb == 'cq' ? `<button class="e_btn" onclick="goModelhtml('中成药分餐','zcyfc')">中成药分餐</button>` : `${WRT_config.url.as_yzlb == 'jcyy' ? ``:`<button class="e_btn tysj">通用汤剂</button>`}`}
            </td>
            <td class="spance_btn">
                ${WRT_config.url.as_yzlb == 'cq' ? `<button class="e_btn cwyytc">肠外营养套餐</button>` : ``}
            </td>
            <td class="spance_btn"><button class="e_btn" onclick="swapColumn()">表格配置</button></td>
            <td class="spance_btn"><span class="red">按F11可全屏固定显示下方的医嘱按钮，无需下拉滚动条。</span></td>
            </tr>
        </table>`
		this.$el.html(html)
		return this;
	},
	events: {
		"change .xstzyz": "checkbox",
		"change .syyz": "checkbox",
		"click .if_winMb": "if_winMb",
		"click .if_zykfy": "if_zykfy",
		"click .if_zyzj": "if_zyzj",
		"click .hzyz360": "hzyz360",
		"click .showbhzs": "showbhzs",
		"click .btn-refresh": "refresh",
		"click .stopYz": "stopYz",
		"click .stopCqYz": "stopCqYz",
		"click .stopAt8Tomorrow": "stopAt8Tomorrow",
		"click .disSubmitYz": "disSubmitYz",
		"click .tysj": "tysj",
		"click .submitSqm": "submitSqm",
		"click .lzzlyzZx": "lzzlyzZx",
		"click .btn_ypsms": "btn_ypsms",
		"click .cwyytc": "cwyytc",
		"click .yzcxzx": "yzcxzx",
		"click .showblsjsb": "showblsjsb",
		"click .sclsyz": "sclsyz",
		"click .if_lczyyy": "if_lczyyy"
	},
	//长期新增自动生成临时医嘱功能
	sclsyz(){
		let val = getYzDl()
		if (val.list.length == 0) {
			WRT_e.ui.message({
				title: '提示窗口',
				content: '至少选中一条医嘱',
				onOk() {
				},
			})
			return
		}else{
			WRT_e.api.yz_sz.copyToLsyz({
				params: { as_yzlst: val.ids, al_blid: WRT_config.url.as_blid },
				success(data) {
					if (data) {
						WRT_e.ui.hint({
							type: "success",
							msg: `生成医嘱成功`
						})
					} else {
						WRT_e.ui.message({
							title: '提示信息',
							content: res.msg,
							onOk() { },
						})
					}
				}
			})
		}
	},
	//肠外营养套餐
	cwyytc() {
		WRT_e.ui.model({
			id: "Cwyytc",
			title: "肠外营养套餐",
			width: "1250px",
			iframeURL: WRT_config.server + `/yz_sz/wcyytc.aspx?as_blid=${WRT_config.url.as_blid}&as_openmode=ehr3`,
			iframeHeight: '500px',
			iframe: true,
		})
	},
	//医嘱执行
	lzzlyzZx() {
		let val = getYzDl()
		if (val.list.length == 0) {
			WRT_e.ui.message({
				title: '提示窗口',
				content: '至少选中一条医嘱',
				onOk() {
				},
			})
			return
		} else if (WRT_config.url.as_yzlb == "cq") {
			return
		} else {
			let rtn = true
			val.list.map(function (item) {
				if (item.color != "black") {
					WRT_e.ui.message({
						title: '提示窗口',
						content: '请选择要已导出医嘱',
						onOk() {
						},
					})
					rtn = false
				}
			})
			if (!rtn) {
				return
			}
		
			// <iframe class="iziModal-iframe" src="${WRT_config.server}/newsstzd/rykxx.aspx?as_version=3&av_tx=0&temp=${Math.random()}&as_openmode=ehr3" style="height: 600px;"></iframe>
			WRT_e.ui.model({
		    id:'if_yzzx',
		    title: '医嘱执行',
		    width: "645px",
				content:`
				<div>
					执行时间：<input type="text" id="datetimebox1" class="Wdate" value="${gettime(false)}" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',readOnly:false})" autocomplete="off" style="width: 140px;border: 1px solid #ddd;height: 30px;margin-bottom: 5px;" tag="0" /></br>
					执行人员：<input id="yzzx_text" type="text" value="${sessionStorage.getItem("yhxm")}" onclick="onselectZXRY()" style="width: 140px;border: 1px solid #ddd;" /></br></br>
					<button class="e_btn" onclick="onzxyzadd()">确定</button>
					<button class="e_btn" onclick="$('#if_yzzx').iziModal('destroy')">取消</button>
				</div>
				`,
		    iframe: false,
			})



			// WRT_e.ui.message({
			// 	title: '提示窗口',
			// 	content: '是否确定医嘱执行',
			// 	onOk() {
			// 		WRT_e.api.yz_sz.Lsyzzx({
			// 			params: {
			// 				as_yzlst: val.ids,
			// 				al_zyid: WRT_config.yz_sz.zyid,
			// 				al_zkid: WRT_config.yz_sz.zkid,
			// 				al_bqid: WRT_config.yz_sz.bqid,
			// 				al_blid: JSON.parse(WRT_config.url.as_blid),
			// 				as_zxsj: $("#datetimebox1")[0].value || ""
			// 			},
			// 			success(data) {
			// 				if (data.Code == 1) {
			// 					getBrYz()
			// 					refresh_init()
			// 					WRT_e.ui.hint({
			// 						type: "success",
			// 						msg: '医嘱执行成功'
			// 					})
			// 				} else {
			// 					WRT_e.ui.message({
			// 						title: '提示信息',
			// 						content: data.CodeMsg,
			// 						onOk() { },
			// 					})
			// 				}
			// 			}
			// 		})
			// 	},
			// 	onCancel() { }
			// })
		}
	},
	//撤销执行
	yzcxzx() {
		let val = getYzDl()
		if (val.list.length == 0) {
			WRT_e.ui.message({
				title: '提示窗口',
				content: '至少选中一条医嘱',
				onOk() {
				},
			})
			return
		} else if (WRT_config.url.as_yzlb == "cq") {
			return
		} else {
			let rtn = true
			val.list.map(function (item) {
				if (item.type != "已执行") {
					WRT_e.ui.message({
						title: '提示窗口',
						content: '请选择要已执行医嘱',
						onOk() {
						},
					})
					rtn = false
				}
			})
			if (!rtn) {
				return
			}
			let html = `
            <div style="width:100%">撤销执行的理由：</div>
            <textarea id="tb_cxzxbz" rows="3" cols="30" />
            <div id="yzcxzx_title" style="display:none;color:red">字段长度超过50</div></br>
            <button class="e_btn" onclick="submityzcxzx();">确认撤销</button>
            <button class="e_btn" onclick="$('#if_yzcxzx').iziModal('destroy')">关闭</button>
            `
			WRT_e.ui.model({
				id: "if_yzcxzx",
				title: "撤销执行",
				width: "500px",
				content: html,
				iframe: false,
			})
		}
	},
	//审方双签名
	submitSqm() {
		let val = getYzDl()
		al_ypZh = ""
		if (val.list.length == 0) {
			WRT_e.ui.message({
				title: '提示窗口',
				content: '至少选中一条医嘱',
				onOk() {
				},
			})
			return
		} else {
			val.list.map(function (item) {
				if (item.YZLX != 1) {
					WRT_e.ui.hint({ type: 'error', msg: '双签名仅针对药品医嘱' })
					return
				}
				if (item.color != "black") {
					WRT_e.ui.hint({ type: 'error', msg: '双签名仅针对除了未导出和停止(撤销)' })
					return
				}
				al_ypZh = item.ZH
			})
		}
		let html = `
        <label style="width:80px">医生姓名：</label><input id="tb_doublesign_ysqm" type="text" readOnly="readOnly" onclick="doublesign()" style="border: 1px solid;width:180px" />
        <input id="hdf_doublesign_ysqm" type="hidden" /><br />
        <label style="width:80px">备注(选填)：</label><textarea id="tb_doublesign_bz" cols="20" rows="3" style="width:180px"></textarea><br /><br /><br />
        &nbsp;&nbsp;<input class="e_btn" type="button" value="确认" onclick="submitDoublesign();" /> &nbsp;&nbsp;
        <input class="e_btn" type="button" value="取消" onclick="closeDoublesign();" />
        `
		WRT_e.ui.model({
			id: "if_Sqm",
			title: "审方双签名",
			width: "500px",
			content: html,
			iframe: false,
		})
	},
	//停止医嘱
	stopYz(type) {
		let rtn = true
		let title = `${WRT_config.url.as_yzlb == 'cq' ? "立即停" : "撤销"}`
		let val = getYzDl()
		let arr = val.list || []
		let LX=1
		if(type==true){
			LX=2
			title='执行后停'
		}else if(WRT_config.url.as_yzlb == 'ls'){
			LX=null
		}
		if(WRT_config.url.as_yzlb == 'ls'){
			title='撤销'
		}
		if (arr.length == 0) {
			WRT_e.ui.message({
				title: '提示信息',
				content: `请选择要${title}的医嘱?`,
				onOk() { },
			})
			rtn = false
			return
		}
		let Mtype = false, ZH_arr = []
		arr.map(function (item) {
			if(item.YZLX==1){
				ZH_arr.push(item.ZH)
			}
			if (!rtn) {
				return
			}
			if (item.LXMC == '出院带药' || item.LXMC == '草药带药') {
				Mtype = true
			}
			if (item.type == '未导出' || item.color == 'blue') {
				WRT_e.ui.message({
					title: '提示信息',
					content: `未导出医嘱不允许${title}`,
					onOk() { },
				})
				rtn = false
				return
			}
		})
		if (!rtn) {
			return
		}
		if(WRT_config.url.as_yzlb == 'cq'&&LX==1&&arr.length>0){
			WRT_e.api.yz_sz.getJingPeiZtByZh({
				params:ZH_arr,
				success(msg){
					if(msg.hasError==0 &&msg.data.length>0){
						let text=''
						msg.data.map(function(item){
							if(item){
								text+='请确认是否立即停。'+`</br></br>`+`${item}`+`</br></br>选择是表示立即停，选择否表示不停`
							}
						})
						WRT_e.ui.message({
							title: '提示信息',
							content:text,
							okText:'是',
							cancelText:'否',
							onOk(){
								checkkYpSfzt()
							},
							onCancel(){}
						})
					}else{
						checkkYpSfzt()
					}
				}
			})
		}else{
			checkkYpSfzt()
		}
		function checkkYpSfzt(){
			if (Mtype) {
				WRT_e.api.yz_sz.checkYpSfzt({
					params: {
						as_zh: ZH_arr.join(","),
						al_blid: WRT_config.url.as_blid
					},
					success(data) {
						let text = ''
						if (data.Code == 1) {
							text = data.CodeMsg
							WRT_e.ui.message({
								title: '提示信息',
								content: text,
								onOk() { 
									showmodel('',title,LX)
								},
								onCancel(){}
							})
							return
						}
						showmodel(text,title,LX)
					}
				})
			} else {
				showmodel(``,title,LX)
			}
		}
		function showmodel(text,title,key) {
			if(WRT_config.url.as_yzlb == 'ls'){
				key=0
			}
			let rtn=false
			WRT_config.BrYz.map(function(item){
				if(val.ids.indexOf(item.VAL)>=0){
					if(item.ZXFF=="iv-vp"&&item.ZXPL=="维持"){
						rtn=true
					}
				}
			})
			if(rtn){
				WRT_e.ui.message({
					title: `提示信息`,
					content: '停止内容包含微泵类医嘱，请选择“立即停” /“执行后停”',
					okText:'立即停',
					cancelText:'执行后停',
					onOk() {
						btn_stop(title,1)
					},
					onCancel() {
						btn_stop(title,2)
					}
				})
				function btn_stop(title,tzlx){
					WRT_e.api.yz_sz.stopYz({
						params: { 
							as_yzlst: val.ids, 
							as_lb: WRT_config.url.yzlb.value, 
							al_blid: WRT_config.url.as_blid,
							tingZhiLX:tzlx
						},
						success(data) {
							if (data.Code == 1) {
								WRT_config.BrYz.forEach(e => {
									if (val.ids.indexOf(e.VAL) >= 0) {
										// WRT_config.yz_sz.lstys = 1
										return
									}
								})
	
								getBrYz()
								refresh_init()
								WRT_e.ui.hint({
									type: "success",
									msg: `${tzlx==1?'立即停':'执行后停'}嘱成功`
								})
							} else {
								WRT_e.ui.message({
									title: '提示信息',
									content: data.CodeMsg,
									onOk() { },
								})
							}
						}
					})
				}
			}else{
				WRT_e.ui.message({
					title: `提示信息`,
					content: `选择的医嘱确定${key==1?'立即停':key==0?'撤销':'执行后停'}吗?`,
					onOk() {
						WRT_e.api.yz_sz.stopYz({
							params: { 
								as_yzlst: val.ids, 
								as_lb: WRT_config.url.yzlb.value, 
								al_blid: WRT_config.url.as_blid,
								tingZhiLX:key
							},
							success(data) {
								if (data.Code == 1) {
									WRT_config.BrYz.forEach(e => {
										if (val.ids.indexOf(e.VAL) >= 0) {
											// WRT_config.yz_sz.lstys = 1
											return
										}
									})
	
									getBrYz()
									refresh_init()
									WRT_e.ui.hint({
										type: "success",
										msg: `${key==1?'立即停':key==0?'撤销':'执行后停'}医嘱成功`
									})
								} else {
									WRT_e.ui.message({
										title: '提示信息',
										content: data.CodeMsg,
										onOk() { },
									})
								}
							}
						})
					},
					onCancel() { }
				})
			}
			
		}
	},
	//全停医嘱
	stopCqYz() {
		let header = new btnlist_View()
		header.stopYz(true)
		// WRT_e.ui.message({
		// 	title: '全停医嘱',
		// 	content: "确定停止全部长期医嘱?",
		// 	onOk() {
		// 		WRT_e.api.yz_sz.stopCqYz({
		// 			params: { al_blid: WRT_config.url.as_blid },
		// 			success(data) {
		// 				if (data.Code == 1) {
		// 					// WRT_config.yz_sz.lstys = 1
		// 					getBrYz()
		// 					refresh_init()
		// 					WRT_e.ui.hint({
		// 						type: "success",
		// 						msg: '停止全部长期医嘱成功'
		// 					})
		// 				} else {
		// 					WRT_e.ui.message({
		// 						title: '提示信息',
		// 						content: data.CodeMsg,
		// 						onOk() { },
		// 					})
		// 				}
		// 			}
		// 		})
		// 	},
		// 	onCancel() { }
		// })

	},
	//明日8点停医嘱
	stopAt8Tomorrow() {
		let rtn = true
		if (WRT_config.url.as_yzlb !== 'cq') {
			WRT_e.ui.message({
				title: '提示信息',
				content: "长期医嘱才允许次日停?",
				onOk() { },
			})
			rtn = false
			return
		}
		let val = getYzDl()
		let arr = val.list || []
		if (arr.length == 0) {
			WRT_e.ui.message({
				title: '提示信息',
				content: "请选择要次日停的医嘱?",
				onOk() { },
			})
			rtn = false
			return
		}
		arr.map(function (item) {
			if (!rtn) {
				return
			}
			if (item.type == '未导出') {
				WRT_e.ui.message({
					title: '提示信息',
					content: "未导出医嘱不允许次日停?",
					onOk() { }
				})
				rtn = false
				return
			} else if (item.type == '停') {
				WRT_e.ui.message({
					title: '提示信息',
					content: "已停止的医嘱不允许次日停?",
					onOk() { },
				})
				rtn = false
				return
			}
		})
		if (!rtn) {
			return
		}
		WRT_e.ui.message({
			title: '提示信息',
			content: "确定选中医嘱次日停?",
			onOk() {
				WRT_e.api.yz_sz.stopAt8Tomorrow({
					params: { as_yzlst: val.ids, al_blid: WRT_config.url.as_blid },
					success(data) {
						if (data.Code == 1) {
							getBrYz()
							refresh_init()
							WRT_e.ui.hint({
								type: "success",
								msg: '次日停成功'
							})
						} else {
							WRT_e.ui.message({
								title: '提示信息',
								content: data.CodeMsg,
								onOk() { },
							})
						}
					}
				})
			},
			onCancel() { }
		})

	},
	//撤回医嘱
	disSubmitYz() {
		let val = getYzDl()
		let arr = val.list || []
		let rtn = true
		if (arr.length == 0) {
			WRT_e.ui.message({
				title: '提示信息',
				content: "请选择要撤回医嘱?",
				onOk() { },
			})
			rtn = false
			return
		}
		arr.map(function (item) {
			if (item.color == 'blue') {
			} else if (item.color == 'red') {
				WRT_e.ui.message({
					title: '提示信息',
					content: "不允许撤回?",
					onOk() { },
				})
				rtn = false
				return
			} else {
				WRT_e.ui.message({
					title: '提示信息',
					content: "已导出医嘱不允许撤回?",
					onOk() { },
				})
				rtn = false
				return
			}
		})
		if (!rtn) {
			return
		}

		WRT_e.ui.message({
			title: '提示信息',
			content: "是否确认撤回",
			onOk() {
				WRT_e.api.yz_sz.disSubmitYz({
					params: { as_yzlst: val.ids, al_blid: WRT_config.url.as_blid },
					success(data) {
						if (data.Code == 1) {
							getBrYz()
							refresh_init()
							WRT_e.ui.hint({
								type: "success",
								msg: '撤回成功'
							})
						} else {
							WRT_e.ui.message({
								title: '提示信息',
								content: data.CodeMsg,
								onOk() { },
							})
						}
					}
				})
			},
			onCancel() { }
		})
	},
	//刷新
	refresh() {
		parent.getbrmrjmsyl()
		window.location.reload()
	},
	//勾选事件
	checkbox() {
		getBrYz()
	},
	//历史用药
	if_winMb() {
		let start = WRT_config.yz_sz.bqryrq.split(" ")
		let time = getNowFormatDate()
		if (start) {
			time = start[0]
		}
		WRT_e.api.yz_sz.getLsyyMb({
			params: { al_blid: WRT_config.url.as_blid, as_lsyylb: 'lscq', as_kssj: time, as_jssj: getNowFormatDate() },
			success(data) {
				if (data.Code == 1) {
					let arr = data.Result
					yp_mb = arr
					let temp = getMBhtml(arr)
					let html = `
                    <div>
                        <tr>
                            <td>
                                
                                <label for="rbl_lsyy_0" onclick="lsyybtn(this,'cqzy')"><input id="rbl_lsyy_0" type="radio" name="rbl_lsyy" value="cqzy">长期在用药品</label>
                            </td>
                            <td>
                                
                                <label for="rbl_lsyy_1"onclick="lsyybtn(this,'lscq')"><input id="rbl_lsyy_1" type="radio" name="rbl_lsyy" value="lscq" checked="checked">历史用药（长期）</label>
                            </td>
                            <td>
                                
                                    <label for="rbl_lsyy_2" onclick="lsyybtn(this,'lsls')"><input id="rbl_lsyy_2" type="radio" name="rbl_lsyy" value="lsls">历史用药（临时）</label>
                            </td>
                        </tr>
                    </div>
                    <div style="height: 35px;">
                    时间:<input type="date" id="txt_kssj" type="text" value="${time}" id="txt_kssj" style="margin: 0 5px;border: 1px solid #000;">-
                    <input type="date" id="txt_jssj" type="text" value="${getNowFormatDate()}" id="txt_jssj" style="margin: 0 5px;border: 1px solid #000;"">     
                    <button class="e_btn" onclick="searchlsyy()">查询</button>
                    </div>
                    <div id="yplistls_table">
                    ${temp}
                    </div>
                    `
					WRT_e.ui.model({
						id: "if_winMb",
						title: "病人历史用药",
						width: "650px",
						content: html,
						iframe: false,
					})
				}
			}
		})
	},
	//在用口服药
	if_zykfy() {
		WRT_e.api.yz_sz.getYpMbByLb({
			params: { as_type: 4, al_blid: WRT_config.url.as_blid, al_zkid: WRT_config.yz_sz.zkid, as_yqdm: WRT_config.yz_sz.yqdm, as_yzlx: WRT_config.url.as_yzlb },
			success(data) {
				if (data.Code == 1) {
					yp_mb = data.Result
					let temp = getMBhtml(data.Result)
					WRT_e.ui.model({
						id: "if_zykfy",
						title: "在用口服药",
						width: "650px",
						content: temp,
						iframe: false,
					})
				}
			}
		})
	},
	//在用针剂
	if_zyzj() {
		WRT_e.api.yz_sz.getYpMbByLb({
			params: { as_type: 'zyzj', al_blid: WRT_config.url.as_blid, al_zkid: WRT_config.yz_sz.zkid, as_yqdm: WRT_config.yz_sz.yqdm, as_yzlx: WRT_config.url.as_yzlb },
			success(data) {
				if (data.Code == 1) {
					yp_mb = data.Result
					let temp = getMBhtml(data.Result)
					WRT_e.ui.model({
						id: "if_zyzj",
						title: "在用针剂",
						width: "650px",
						content: temp,
						iframe: false,
					})
				}
			}
		})
	},
	//历次住院用药
	if_lczyyy(){
		WRT_e.api.yz_sz.getBinRenXXNoQuanXianByParam({
			params: { param:WRT_config.yz_sz.empi },
			success(msg) {
				if (msg.data) {
					let arr=[]
					msg.data.map(function(item,index){
						if(index>0&&index<=3){
							arr.push(item)
						}
					})
					let temp=getlczyhtml(arr)
					WRT_config.lczy_lists = arr
					let html = `
						<div id="lczyyy_lists">
							${temp}
						</div>
						`
					WRT_e.ui.model({
						id: "if_lczyyy",
						title: "历次住院用药",
						width: "650px",
						content: html,
						iframe: false,
					})
				}
			}
		})
	},
	//不良事件上报
	showblsjsb() {
		var ls_url = WRT_config.yz_sz.blsjsb
		var iWidth = 700;
		var iHeight = 800;
		var iTop = (window.screen.availHeight - 30 - iHeight) / 2;
		var iLeft = (window.screen.availWidth - 10 - iWidth) / 2;
		window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
	},
	btn_ypsms() {
		let val = getYzDl()
		let arr = val.ids
		let zh = arr.split(",")
		let id = WRT_config.BrYz.find(e => e.VAL == zh) || {}
		let url = WRT_config.yz_sz.hlyySms.split("@ypid@")
		var ls_url = url[0] + id.YZID || ""
		var iWidth = 1200;
		var iHeight = 800;
		var iTop = (window.screen.availHeight - 30 - iHeight) / 2;
		var iLeft = (window.screen.availWidth - 10 - iWidth) / 2;
		window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
	},
	//患者医嘱360
	hzyz360() {
		window.open(WRT_config.yz_sz.hzyz360, "_blank")
	},
	// //医疗安全（不良）事件
	// showYlaqbg() {
	//     var ls_url = WRT_config.yz_sz.ylaqbg
	//     var iWidth = 700;
	//     var iHeight = 800;
	//     var iTop = (window.screen.availHeight - 30 - iHeight) / 2;
	//     var iLeft = (window.screen.availWidth - 10 - iWidth) / 2;
	//     window.open(ls_url, "_blank", "height=" + iHeight + ",width=" + iWidth + ",top=" + iTop + ",left=" + iLeft + ",status=no;scroll=yes;resizable=no");
	// },
	//闭环展示
	showbhzs() {
		let ls_url = WRT_config.yz_sz.bhzsdz + '?as_tempid=' + Math.random();
		//var rtn = window.open(ls_url, null, "height=700px;width=1200px;status=no;scroll=no;resizable=no");
		window.open(ls_url, "_blank");
	},
	//通用试剂
	tysj() {
		WRT_e.api.yz_sz.GetTytjMb({
			params: {
				as_mc: ''
			},
			success(data) {
				WRT_config.tysj = data.Result || []
				let temp = gotysjmb(data.Result || [])
				WRT_e.ui.model({
					id: "if_tysj",
					title: "通用试剂",
					width: "650px",
					iframeHeight: "200px",
					content: temp,
					iframe: false,
				})
			}
		})
	}
})
//表格头
var head_View = WRT_e.view.extend({
	render: function () {
		let listObj = {
			'选择': `<td width="30px" >选择<br/><input type="checkbox" id="tb_title_xzall" class="xzAll" /></td>`,
			'组号': `<td width="30px" >组号</td>`,
			'医嘱状态': `<td width="60px" >医嘱状态</td>`,
			'医嘱类型': `<td width="65px" >医嘱类型</td>`,
			'开始日期': `<td width="80px" >开始日期</td>`,
			'开始时间': `<td width="40px" >开始<br>时间</td>`,
			'医嘱名称': `<td width="155px" >医嘱名称3</td>`,
			'组': `<td width="20px" >组</td>`,
			'一次用量': `<td width="55px" >一次<br>用量</td>`,
			'剂量单位': `<td width="60px" >剂量单位</td>`,
			'用法': `<td width="75px" >用法</td>`,
			'频率': `<td width="120px" >频率</td>`,
			'时间针频率': `<td width="120px" >时间针频率</td>`,
			'用药执行时间': `<td width="70px" >用药/<br>执行时间</td>`,
			'规格/单价': `<td width="110px" >规格/单价</td>`,
			'医师姓名': `<td width="50px" >医师<br>姓名</td>`,
			'类别': `<td width="55px" ><test>类别</test><br>
            <select id="as_lb" >
            <option value="">全部</option>
            <option value="1">药品</option>
            <option value="0">饮食</option>
            <option value="2">治疗</option>
            <option value="98">嘱托</option>
            <option value="3">化验</option>
            <option value="4">检查</option>
            <option value="99">其他</option>
            </select>
            </td>`,
			'费用类型': `<td width="75px" ><test>费用类型</test><br>
            <select id="as_fylx">
            <option value="">全部</option>
            <option value="0">自费</option>
            <option value="1">甲类</option>
            <option value="2">乙类</option>
            <option value="99">其他</option>
            </select>
            </td>`,
			'自备': `<td width="45px" >自备</td>`,
			'今日临停': `<td width="65px" >今日临停</td>`,
			'特殊用法/备注说明': `<td width="150px" >特殊用法/备注说明</td>`,
			'执行病区': `<td width="150px" >执行病区</td>`,
			'数量剂数': `<td width="35px" >数量/<br>剂数</td>`,
			'持续天数': `<td width="35px" >持续<br>天数</td>`,
			'用药天数': `<td width="35px" >用药<br>天数</td>`,
			'单位': `<td width="35px" >单位</td>`,
			'次数': `<td width="35px" >次数</td>`,
			'预计出院': `<td width="65px" >预计出院</td>`,
			'是否代煎': `<td width="65px" >是否代煎</td>`,
			'滴速': `<td width="75px" >滴速</td>`,
			'结束时间': `<td width="120px" >结束时间</td>`,
			'手术通知单': `<td width="110px" >手术通知单</td>`,
			'医嘱时间': `<td width="120px" >医嘱时间</td>`,
			'导入人员': `<td width="50px" >导入<br>人员</td>`,
			'导入时间': `<td width="120px" >导入时间</td>`,
			'医嘱ID': `<td width="80px" >医嘱ID</td>`,
			'闭环': `<td width="120px" >闭环</td>`,
			'不良反应上报': `<td width="120px" >不良反应上报</td>`,
		}
		if (!((WRT_config.yz_sz.zkid == 49 || WRT_config.yz_sz.zkid == 2961) && WRT_config.url.as_yzlb == 'ls')) {
			delete listObj["时间针频率"]
		}
		function gethtml() {
			let content = '';
			WRT_config.yzbtgxh.forEach((item) => {
				if (item.show) {
					content += listObj[item.key];
				}
			})
			return content;
		}
		let html = `
			<tr align="center" bgcolor="steelblue" class="table_header">
			${gethtml()}
			</tr>
		`
		this.$el.html(html)
		return this;
	},
	events: {
		"change #as_lb": "select",
		"change #as_fylx": "select",
		"click .xzAll": "xzAll"
	},
	//表头下拉选择事件
	select() {
		getBrYz()
	},
	xzAll(ev) {
		let checked = true
		if (ev.target.checked) {
			checked = true
		} else {
			checked = false
		}
		WRT_config.BrYz.map(function (item) {
			item.check = checked
		})
		var yzsz = new yzsz_View();
		yzsz.$el = $("#data_list");
		yzsz.init({
			data: XHData(WRT_config.BrYz)
		}).render();
	}
})
//医嘱展示
var yzsz_View = WRT_e.view.extend({
	render: function () {
		var arr = WRT_config.yzbtgxh
		if (!((WRT_config.yz_sz.zkid == 49 || WRT_config.yz_sz.zkid == 2961) && WRT_config.url.as_yzlb == 'ls')) {
			arr.map(function (item, index) {
				if (item.key == '时间针频率') {
					arr.splice(index, 1)
					return
				}
			})
		}
		function getText(key, index) {
			let text = '';
			arr.forEach((item) => {
				if (item.show) {
					switch (item.key) {
						case '选择':
							text += `<td width="30">${(key.COLOR == 'red' && key.YZZTMC != '到期') || (key.YZLX > 2 && key.YZLX != 98 && key.YZLX != -1) ? "" : key.show ? '' : `<input type="checkbox" ${key.check ? `checked="checked"` : ""} ZH="${key.ZH}" check="${key.show}" YSID="${key.YSID}" value="${key.VAL}" onchange="oncheck(${index})" />`}</td>`;
							break;
						case '组号':
							text += `<td width="30">${key.XH}</td>`;
							break;
						case '医嘱状态':
							text += `<td width="60">${key.XH ? key.YZZTMC : ""}</td>`;
							break;
						case '医嘱类型':
							text += `<td width="65">${key.XH ? key.LXMC : ""}</td>`;
							break;
						case '开始日期':
							text += `<td width="80">${key.YZ_RQ || ''}</td>`;
							break;
						case '开始时间':
							text += ` <td width="40">${key.XH ? key.YZ_SJ : ""}</td>`;
							break;
						case '医嘱名称':
							text += `<td class="mc_title" width="155" YZID=${key.YZID}" XMID=${key.XMID}" oncontextmenu="OnRight(${index});return false;">${key.YZLX == 1 || key.YZLX == -1 ? key.MC : key.YZNR}</td>`;
							break;
						case '组':
							text += `<td width="20">${key.ZUSTR || ''}</td>`;
							break;
						case '一次用量':
							text += ` <td width="55">${key.YCYL || ''}</td>`;
							break;
						case '剂量单位':
							text += `<td width="60">${key.JLDW || ''}</td>`;
							break;
						case '用法':
							text += `<td width="75">${key.ZXFF == 'null' ? '' : key.ZXFF ? key.ZXFF : ''}</td>`;
							break;
						case '频率':
							text += `<td width="120">${key.ZXPL || ''}</td>`;
							break;
						case '时间针频率':
							text += `<td width="120">${key.ZXPL2 || ''}</td>`;
							break;
						case '用药执行时间':
							text += `<td width="70">${key.GYSJ || ''}</td>`;
							break;
						case '规格/单价':
							text += `<td width="110">${key.GG || ''}</td>`;
							break;
						case '医师姓名':
							text += `<td width="50" title="${key.YSQM || ''}">${key.YSQM || ''}</td>`;
							break;
						case '类别':
							text += `<td width="55">${key.YZLX == 0 ? "饮食" : key.YZLX == 1 ? "药品" : key.YZLX == 2 ? "治疗" : key.YZLX == 3 ? "化验" : key.YZLX == 4 ? "检查" : ''}</td>`;
							break;
						case '费用类型':
							text += `<td width="75">${key.FYLXMC || ''}</td>`;
							break;
						case '自备':
							text += `<td width="45">${key.YPLBMC || ''}</td>`;
							break;
						case '今日临停':
							text += `<td width="65">${key.JINRILT=='1'?'是':''}</td>`;
							break;
						case '特殊用法/备注说明':
							text += `<td width="150">${key.TSSM || ''}</td>`;
							break;
						case '执行病区':
							text += `<td width="150">${key.ZXBQ || ''}</td>`;
							break;
						case '数量剂数':
							text += `<td width="35">${key.LXMC == '长期医嘱' && key.CXTS > 0 ? `` : key.YZLB == 3 ? `${key.SFCS}` : key.LXMC == '临时医嘱' && key.CXTS == 0 ? `${key.SFSL}` : key.LXMC == '出院带药' && key.CXTS < 0 ? `${key.SFSL}` : ""}</td>`;
							break;
						case '持续天数':
							text += `<td width="35">${key.LXMC == '长期医嘱' && key.CXTS > 0 ? `${key.CXTS}` : key.YZLB == 3 ? `` : key.LXMC == '临时医嘱' && key.CXTS == 0 ? `` : key.LXMC == '出院带药' && key.CXTS < 0 ? `${key.YYTS || ''}` : ""}</td>`;
							break;
						case '用药天数':
							text += `<td width="35">${key.YYTS >= 0&&(key.YYTS!=null) ? `${key.YYTS}` :''}</td>`;
							break;
						case '单位':
							text += `<td width="35">${key.DW || ''}</td>`;
							break;
						case '次数':
							text += `<td width="35">${key.LXMC == '长期医嘱' && key.CXTS > 0 ? `` : key.YZLB == 3 ? `${key.YZCS || ''}` : key.LXMC == '临时医嘱' && key.CXTS == 0 ? `${key.SFCS || ''}` : key.LXMC == '出院带药' && key.CXTS < 0 ? `${key.SFCS || ''}` : ""}</td>`;
							break;
						case '预计出院':
							text += `<td width="65">${key.JRCYMC || ''}</td>`;
							break;
						case '是否代煎':
							text += `<td width="65">${key.ZSBQ == 3 ? '代煎' : key.ZSBQ == 4 ? '自煎' : ''}</td>`;
							break;
						case '滴速':
							text += `<td width="75">${key.YPDSBZ && key.YPDSBZ != 0 ? key.YPDSBZ : ""}</td>`;
							break;
						case '结束时间':
							text += `<td width="120">${key.TZ_RQ || ''}</td>`;
							break;
						case '手术通知单':
							text += `<td width="110">${key.SSTZDMC || ''}</td>`;
							break;
						case '医嘱时间':
							text += `<td width="120">${key.SCLRSJSTR || ''}</td>`;
							break;
						case '导入人员':
							text += `<td width="50">${key.DCRY || ''}</td>`;
							break;
						case '导入时间':
							text += `<td width="120">${key.DCSJ_RQ || ''}</td>`;
							break;
						case '医嘱ID':
							text += `<td width="80">${key.YZID}</td>`;
							break;
						case '闭环':
							text += `<td width="120">${key.YZLX == 0 || key.YZLX == 1 || key.YZLX == 2 ? `<a class="bhleft" onclick="OnLeft(${index})">闭环<a/>` : ""}</td>`;
							break;
						case '不良反应上报':
							text += `<td width="120">${key.YZLX_copy == 1 ? `<a class="bhleft" onclick="OnBlfy(${index})">不良反应上报<a/>` : ""}</td>`;
							break;
					}
				}
			})
			return text;
		}
		var html = `
			${_.map(this.data, (key, index) =>
			`<tr class="headertable tryzsz${index} ${"tr" + key.COLOR} ${key.XH ? "trbody" : "trnobody"}" onclick="selectcolor(${index})" ondblclick="dbsel(${index})">
				${getText(key, index)}
			</tr>
		`
		).join('')}`
		this.$el.html(html)
		return this;
	},
	events: {
	}
})
//病人信息列表
var person_View = WRT_e.view.extend({
	render() {
		var html = `
			<span class="border-space border-title">病人基本信息</span>
			<span class="e-tag e-tag-blue">${this.data.bqzdym}-${this.data.cwh}</span>
			<span class="e-tag e-tag-blue">${this.data.jslx == 00 ? '自费' : this.data.jslx == 01 ? '社保' : this.data.jslx == 02 ? '新农合' : this.data.jslx == 03 ? '成人公费' : '儿童统筹'}</span>
			<span class="border-space"><a class="border-black">${this.data.brxm}</a></span>
			<span class="tag-no">${this.data.empi || ''}</span>
			<span class="border-space">${this.data.nianling}</span>
			<span class="border-space">
			<span class="border-title">药品检索方式：</span>
			<label class="border-space">
			<input type="radio" name="optionsRadios" id="optionsRadios1" value="option1" checked>
			精确
			</label>
			<label>
			<input type="radio" name="optionsRadios" id="optionsRadios2" value="option2">
			模糊
			</label>
			</span>
			<span class="border-space"><a class="border-black">草药总价:</a><span id="cysum_title">${getcysum()}</span>元</span>
			${this.data.dqmrkcl ? `<span class="border-space"><a class="dqmrkcl-red">当前母乳库存量:<span id="dqmrkcl_title">${this.data.dqmrkcl}</span></a></span>` : ''}
		`
		this.$el.html(html)
		return this;
	}
})
//医嘱表格头
var yzhead_View = WRT_e.view.extend({
	render() {
		var html = `
        <tr align="center" bgcolor="steelblue" class="table_header">
            <td width="30px">
                <input type="checkbox" checked="checked" id="tb_newtitle_xzall" class="xzAll">
            </td>
            <td width="40px">组号</td>
            <td width="60px">&nbsp;</td>
            <td width="65px">医嘱类型</td>
            <td width="120px">计划开始时间</td>
            <td width="155px">医嘱名称</td>
            <td width="20px">组</td>
            <td width="55px">一次<br>用量</td>
            <td width="60px">剂量单位</td>
            <td width="75px">用法</td>
            <td width="120px">频率</td>
            ${((WRT_config.yz_sz.zkid == 49 || WRT_config.yz_sz.zkid == 2961) && WRT_config.url.as_yzlb == 'ls') ? `<td width="120px">时间针频率</td>` : ""}
            <td width="70px">用药/<br>执行时间</td>
            <td width="110px">规格/单价</td>
            <td width="50px">医师<br>姓名</td>
            <td width="55px">类别</td>
            <td width="75px">费用类型</td>
            <td width="45px">自备</td>
						<td width="65px">今日临停</td>
            <td width="150px">特殊用法/备注说明</td>
            <td width="150px">执行病区</td>
            <td width="35px">数量/<br>剂数</td>
            <td width="35px">持续<br>天数</td>
						<td width="35px">用药<br>天数</td>
            <td width="35px">单位</td>
            <td width="35px">次数</td>
            <td width="65px">预计出院</td>
            <td width="65px">是否代煎</td>
            <td width="75px">滴速</td>
            <td width="120px">结束时间</td>
            <td width="110px">手术通知单</td>
            <td width="120px">医嘱时间</td>
            <td width="50px">导入<br>人员</td>
            <td width="120px">导入时间</td>
            <td width="120px">闭环</td>
        </tr>
        `
		this.$el.html(html)
		return this;
	},
	events: {
		"click .xzAll": "xzAll"
	},
	xzAll(ev) {
		let checked = true
		if (ev.target.checked) {
			checked = true
		} else {
			checked = false
		}
		yzdata.map(function (item) {
			item.check = checked
		})
	}
})
//医嘱表格输入
var yzlist_View = WRT_e.view.extend({
	render() {
		yz_isModel = false
		let lx = [{ title: "长期医嘱", value: "cq" }]
		
		if (WRT_config.url.as_yzlb != 'cq' && WRT_config.url.as_yzlb != 'jcyy') {
			lx = [{ title: "临时医嘱", value: "ls" }, { title: "出院带药", value: "cydy" }, { title: "草药医嘱", value: "cy" }, { title: "草药带药", value: "zcydy" }]
		} else if (WRT_config.url.as_yzlb == 'jcyy') {
			lx = [{ title: "临时医嘱", value: "ls" }]
		}
		var html = `
        ${_.map(this.data, (obj, index) =>
				`<tr class="trc lists_table">
            <td width="30px" align="center">
                ${obj.check != undefined ? `
                    <input type="checkbox" ${obj.check ? `checked="checked"` : ""} name="xz" onclick="xz_click(this,'check',${index},'check')" tag="0" style="width:18px">
                `: `<input type="checkbox" checked="checked" name="xz" onclick="xz_click(this,'check',${index},'check')" tag="0">
                `}
                
            </td>xz_
            <td width="40px" align="center">
                <label name="groupNo" style="width:18px; font-size:12px;display: contents;" tag="0">${obj.XH}</label>
            </td>
            <td width="60px">&nbsp;</td>
            <td width="65px">
                ${obj.YZID||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5')? `<input type="text" value="${yzlbhtml[obj.YZLX]}" name="YZLX" style="width:62px;" readonly="readonly" class="zdclassname bd" autocomplete="off"  disabled="disabled">` :
				`<input type="text" value="${yzlbhtml[obj.YZLX]}" id="YZLX${index}" data-index="${index}" class="YZLX model_input zdclassname" ov="" hisvalue="" style="width:62px" autocomplete="off" class="bd" tag="0" val="LI">
                <div id="YZLX${index}_list" class="isModel yzlxlist" data-index="${index}" data="YZLX${index}">
                    ${_.map(lx, (key) => `
                    <input type="text" class="li" value="${key.title}" title="${key.value}">
                    `).join('')}
                </div>
                `}
            </td>
            <td width="120px">
                <input id="KSSJ_TXT${index}" class="Wdate" onblur="mouseout(this,'KSSJ_TXT',${index})" onfocus="changeFunc(this,'KSSJ_TXT',${index})" data-index="${index}" name="birthday" value="${obj.KSSJ_TXT}" autocomplete="off" style="width:118px"  ${obj.LYLB=='1'|| obj.LYLB=='2'?'disabled="disabled"':''}>
            </td>
            <td class="td" width="155px">
								${WRT_config.url.as_yzlb == 'jcyy'||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ?`
									<input type="text" class="MC_search" id="MC${index}" readonly="readonly" data-index="${index}" value="${obj.MC}" oncontextmenu="OnRightdata(${index});return false;" autocomplete="off" hisvalue="" name="MC" style="width: 152px; background-color: white;">
								`:`<input type="text" class="MC_search" id="MC${index}" data-index="${index}" value="${obj.MC}" oncontextmenu="OnRightdata(${index});return false;" autocomplete="off" hisvalue="" name="MC" style="width: 152px; background-color: white;">
								`}
                <div id="MC${index}_list" class="isModel mclist" data-index="${index}" data="MC${index}">
                    
                </div>
            </td>
            <td width="20px">
                <label name="groupNo" style="width:20px; font-size:12px;" tag="0">${obj.ZHTXT}</label>
            </td>
            <td width="55px"> 
                ${(obj.LB != "" && obj.LB != '3')||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" id="YCYL${index}" data-index="${index}" disabled="disabled" value="${obj.YCYL}" readonly="readonly" name="YCYL" style="width:52px" autocomplete="off" class="model_input bd" tag="0"></input>` : `<input type="text" id="YCYL${index}" data-index="${index}" value="${obj.YCYL}" onchange="xz_ycly(this,'YCYL',${index},'input')" name="YCYL" style="width:52px" autocomplete="off" class="model_input bd" tag="0">`}
            </td>
            <td width="60px">
                ${(obj.LB != "" && obj.LB != '3')||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" id="JLDW${index}" disabled="disabled" readonly="readonly" name="JLDW" value="${obj.JLDW}" style="width:57px" autocomplete="off" class="bd" tag="0"></input>` :
				`<select id="JLDW${index}" name="JLDW" onchange="xz_click(this,'JLDW',${index},'select')"style="width: 57px; display: inline;" tag="0">
                    ${obj.JLDW_list ? `${_.map(obj.JLDW_list, (key) => `<option value="${key.JLDW}" ${key.JLDW == obj.JLDW ? "selected='selected'" : ""}}>${key.JLDW}</option>`)}` : `<option value="${obj.JLDW}">${obj.JLDW}</option>`}
                </select>`}
            </td>
            <td width="75px">
                ${(obj.LB != "" && obj.LB == '2')||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" disabled="disabled" readonly="readonly" value="${getName(obj.ZXFF, 'ZXFF', obj.YZLX, obj)}" style="width:72px" class="bd" tag="0"></input>` :
				`<input type="text" value="${getName(obj.ZXFF, 'ZXFF', obj.YZLX, obj)}" id="ZXFF${index}" data-index="${index}" ons class="ZXFF model_input search_input zdclassname" ov="" hisvalue="" name="ZXFFlist${index}"style="width:72px" autocomplete="off" tag="0" val="LI">
                <div id="ZXFF${index}_list" class="isModel yflist" data-index="${index}" data="ZXFF${index}">
                </div>`}
            </td>
            <td width="120px">
                ${(obj.LB != "" && obj.LB == '2')||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" disabled="disabled" readonly="readonly" value="${getName(obj.ZXPL, 'ZXPL')}" style="width:117px" class="bd" tag="0"></input>` :
				`<input type="text" value="${getName(obj.ZXPL, 'ZXPL')}" id="ZXPL${index}" data-index="${index}" class="ZXPL model_input search_input zdclassname" ov="" hisvalue="" name="ZXPL"style="width:117px" autocomplete="off" tag="0" val="q6h">
                <div id="ZXPL${index}_list" class="isModel pllist" data-index="${index}" data="ZXPL${index}">
                </div>`}
                <!--  onchange放在了moveupdown()中  -->
            </td>
            ${((WRT_config.yz_sz.zkid == 49 || WRT_config.yz_sz.zkid == 2961) && WRT_config.url.as_yzlb == 'ls') ? `<td width="120px">
                ${(obj.LB != "" && obj.LB == '2')||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" disabled="disabled" readonly="readonly" value="${getName(obj.SJZZXPL, 'ZXPL')}" style="width:117px" class="bd" tag="0"></input>` :
					`<input type="text" value="${getName(obj.SJZZXPL, 'ZXPL')}" id="SJZZXPL${index}" data-index="${index}" class="ZXPL model_input search_input zdclassname" ov="" hisvalue="" name="ZXPL"style="width:117px" autocomplete="off" tag="0" val="q6h">
                <div id="SJZZXPL${index}_list" class="isModel pllist" data-index="${index}" data="SJZZXPL${index}">
                </div>`}
                <!--  onchange放在了moveupdown()中  -->
            </td>`: ``}
            <td width="70px">
                ${(obj.LB != "" && obj.LB == '2') ||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5')? `<input type="text" disabled="disabled" readonly="readonly" value="${getName(obj.GYSJ, 'GYSJ')}" style="width:67px" class="bd" tag="0"></input>` :
				`<input type="text" value="${getName(obj.GYSJ, 'GYSJ')}" id="GYSJ${index}" data-index="${index}" class="GYSJ model_input search_input zdclassname" ov="" hisvalue="" name="GYSJ" style="width:67px" autocomplete="off" tag="0" val="pc">
                <div id="GYSJ${index}_list" class="isModel gysjlist" data-index="${index}" data="GYSJ${index}">
                ${ypyfplHtml(WRT_config.gysj)}
                </div>`}
                <!--  onchange放在了moveupdown()中  -->
            </td>
            <td width="110px">
                <input type="text" value="${obj.GG == 0 ? obj.GG : obj.GG ? obj.GG : obj.DJ}" name="GG" data-index="${index}" onchange="xz_click(this,'GG',${index},'input')" autocomplete="off" style="width:107px" readonly="readonly" class="bd" tag="0">
            </td>
            <td width="50px">
                <label name="YSXM" style="width:47px; font-size:12px;" tag="0"></label>
            </td>
            <td width="55px">
                ${isLB(index)||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" class="LB model_input zdclassname" id="LB${index}" data-index="${index}" value="${obj.LB ? yz_lb[obj.LB] : ''}" name="LB" style="width:52px;" autocomplete="off" class="bd" tag="0" val="yp">` : `<input type="text" value="${obj.LB ? yz_lb[obj.LB] : ""}" ov="药品" name="lb" style="width:52px;" autocomplete="off" readonly="readonly" class="bd" tag="0" val="yp" disabled="disabled">`}
                <div id="LB${index}_list" class="isModel lblist" data-index="${index}" data="LB${index}">
                ${lbHtml(obj.YZLX)}
                </div>
            </td>
            <td width="75px" style="text-align:center;">
                <label name="FYLX" tag="0">${getFylx(obj) == 1 ? '自费' : getFylx(obj) == 0 ? '' : getFylx(obj) ? `${getFylx(obj)}` : ''}</label>
            </td>
            <td width="45px">
							${(obj.LB != "" && obj.LB != '3')||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="checkbox" disabled="disabled" readonly="readonly" style="width:42px" class="bd" tag="0"></input>` :
							`<input type="checkbox" ${obj.SFZB == '1' ? `checked="checked"` : ""} value="" ov="" name="SFZB" onchange="xz_click(this,'SFZB',${index},'SFZB')" autocomplete="off" style="width:42px;" readonly="readonly" class="bd" tag="0">`}
            </td>
						<td width="65px">
							${(obj.LB != "" && obj.LB != '3') ? `<input type="checkbox" disabled="disabled" readonly="readonly" style="width:42px" class="bd" tag="0"></input>` :
							`<input type="checkbox" ${obj.JINRILT == '1' ? `checked="checked"` : ""} value="1" ov="" name="JINRILT" onchange="xz_click(this,'JINRILT',${index},'JINRILT')" autocomplete="off" style="width:42px;" readonly="readonly" class="bd" tag="0">`}
            </td>
            <td width="150px" id="TSYF${index}">
                ${(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5')?
								`<input id="tsyf${index}" data-index="${index}" type="text" value="${obj.TSYF}" disabled="disabled" readonly="readonly" name="TSYF" style="width:147px;" autocomplete="off" class="zdclassname bd" tag="0">`
								:obj.YZLX == 'cy' || obj.YZLX == 'zcydy' ? `
                <select id="tsyf${index}" name="TSYF" onchange="xz_click(this,'TSYF',${index},'select')" style="width:147px;" class="sel" tag="0">
                <option value=""}></option>
                ${_.map(WRT_config.cyts, (key) => `
                    <option value="${key.MC}" ${obj.TSYF == key.MC ? "selected='selected'" : ""}>${key.MC}</option>
                `)}
                </select>
                `: `<input id="tsyf${index}" data-index="${index}" type="text" value="${obj.TSYF}" onchange="xz_click(this,'TSYF',${index},'input')" name="TSYF" style="width:147px;" autocomplete="off" class="zdclassname bd" tag="0">`
							}
            </td>
            <td width="150px">
                <select name="BQID" id="BQID" onchange="xz_click(this,'BQID',${index},'select')" style="width:147px;" class="sel" tag="0" ${obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5'?'disabled="disabled"':''}>
                ${_.map(WRT_config.zxbq, (key) => `
                    <option value="${key.BMID}" ${obj.BQID == key.BMID ? "selected='selected'" : ""}>${key.BMMC}</option>
                `)}
                </select>
            </td>
            <td width="35px">
                ${!getSFSLHtml(obj, index)||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" value="${setSFSL(obj)}" id="SFSL${index}" data-index="${index}" name="SFSL" style="width:32px" disabled="disabled" class="bd">` : `
                    <input type="text" class="sfsl_change" data-index="${index}" value="${setSFSL(obj)}" id="SFSL${index}" name="SFSL" style="width:32px" autocomplete="off" class="bd">`
			}
            </td>
            <td width="35px">
                ${!isCXTS(obj)||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" value="${obj.CXTS < 0 || obj.YZLX != 'cq' ? '' : obj.CXTS}" id="CXTS${index}" data-index="${index}" name="CXTS" style="width:32px" disabled="disabled" autocomplete="off" class="bd">` : `
                <input type="text" value="${getCXTS(obj)}" id="CXTS${index}" oninput="xz_click(this,'CXTS',${index},'input')" name="cxts" autocomplete="off" style="width:32px" class="bd" tag="0">`
								}
            </td>
						<td width="35px">
                ${(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" value="${obj.YYTS < 0 || obj.YZLX != 'cq' ? '' : obj.YYTS}" id="YYTS${index}" data-index="${index}" name="YYTS" style="width:32px" disabled="disabled" autocomplete="off" class="bd">` : `
                <input type="text" value="${obj.YYTS?obj.YYTS:''}" id="YYTS${index}" oninput="xz_click(this,'YYTS',${index},'input')" name="YYTS" autocomplete="off" style="width:32px" class="bd" tag="0">`
								}
            </td>
            <td width="35px">
                <input type="text" value="${obj.DW}" id="DW${index}" data-index="${index}" onchange="xz_click(this,'DW',${index},'input')" name="dw" style="width:32px" disabled="disabled" class="bd" tag="0">
            </td>
            <td width="35px">
                <input type="text" value="" id="CS${index}" data-index="${index}" oninput="xz_click(this,'CS',${index},'input')" name="cs" style="width:32px" disabled="disabled" autocomplete="off" class="bd" tag="0">
                <input type="hidden" name="hdf_cszt" value="0" tag="0">
            </td>
            <td width="65px">
                ${(obj.YZLX == 'zcydy' && WRT_config.yz_sz.curyszkid == 33) ? `
                    <select name="JRCY" onchange="xz_click(this,'JRCY',${index},'select')" style="width:62px;" tag="0">
                        <option value="" ${obj.JRCY == '' ? "selected='selected'" : ""}></option>
                        <option value="1" ${obj.JRCY == 1 ? "selected='selected'" : ""}>患者自取</option><!---默认项，原名今日出院-->
                        <option value="2" ${obj.JRCY == 2 ? "selected='selected'" : ""}>医院配送</option><!-原名明日出院-->
                        <option value="3" ${obj.JRCY == 3 ? "selected='selected'" : ""}>医院快递</option><!-原名医院快递-->
                    </select>`: obj.YZLX == 'cydy' ? `
                    <select name="JRCY" onchange="xz_click(this,'JRCY',${index},'select')" style="width:62px;" tag="0">
                        <option value="" ${obj.JRCY == '' ? "selected='selected'" : ""}></option>
                        <option value="1" ${obj.JRCY == 1 ? "selected='selected'" : ""}>患者自取</option><!---默认项，原名今日出院-->
                        <option value="2" ${obj.JRCY == 2 ? "selected='selected'" : ""}>医院配送</option><!-原名明日出院-->
                    </select>`: `<input type="text" value="${obj.JRCY}" name="JRCY" style="width:62px" disabled="disabled">`
			}
            </td>
            <td width="65px">
                ${obj.YZLX == 'zcydy' && WRT_config.yz_sz.curyszkid == 33 ? `
                    <select name="ZSBQ" onchange="xz_click(this,'ZSBQ',${index},'select')" style="width:62px;" tag="0">
                        <option value="" ${obj.ZSBQ == '' ? "selected='selected'" : ""}></option>
                        <option value="3" ${obj.ZSBQ == 3 ? "selected='selected'" : ""}>代煎</option>
                        <option value="4" ${obj.ZSBQ == 4 ? "selected='selected'" : ""}>自煎</option>
                    </select>`: `<input type="text" value="${obj.ZSBQ || ""}" name="ZSBQ" style="width:62px" disabled="disabled">`
			}
            </td>
            <td width="75px"> 
                ${(obj.LB != "" && obj.LB != '3')||(obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5') ? `<input type="text" disabled="disabled" readonly="readonly" value="${obj.YPDSBZ ? obj.YPDSBZ : ""}" style="width:72px" class="bd" tag="0"></input>` :
				`<input type="text" id="YPDSBZ${index}" value="${obj.YPDSBZ ? obj.YPDSBZ : ""}" data-index="${index}" oninput="xz_click(this,'YPDSBZ',${index},'input')" name="YPDSBZ" style="width:72px" autocomplete="off" class="zdclassname bd" tag="0">`}
            </td>
            <td width="120px">
                <input type="text" class="Wdate" onblur="mouseout(this,'JSSJ_TXT',${index})" onclick="changeFunc(this,'JSSJ_TXT',${index})" id="JSSJ_TXT${index}" data-index="${index}" value="${obj.JSSJ_TXT}" hisvalue="" name="JSSJ_TXT" autocomplete="off" style="width:117px;" tag="0" ${obj.LYLB=='1'|| obj.LYLB=='2'?'disabled="disabled"':''}>
            </td>
            <td width="110px">
                <select name="SSTZD" onchange="xz_click(this,'TZDID',${index},'select')" style="width:107px;" value="${obj.SSTZD}" class="sel" tag="0" ${obj.LYLB=='1'|| obj.LYLB=='2'|| obj.LYLB=='5'?'disabled="disabled"':''}>
                <option value="" ></option>
                ${_.map(WRT_config.sstzd, (key) => `
                    <option value="${key.TZDID}" ${obj.TZDID == key.TZDID ? "selected='selected'" : ""}>${getsstzd(key.NSSSJ)}</option>
                `)}
                </select>
            </td>
            <td width="120px">
                    <label value="${obj.YZSJ}" name="YZSJ" style="width:117px; font-size:12px;" tag="0"></label>
            </td>
            <td width="50px">
                <label value="${obj.DRRY}" name="DRRY" style="width:47px; font-size:12px;" tag="0"></label>
            </td>
            <td width="120px">
                <label value="${obj.DRSJ}" name="DRSJ" style="width:117px; font-size:12px;" tag="0"></label>
            </td>
            <td width="120px">
                <label style="width:117px; font-size:12px;" tag="0"></label>
            </td>
        </tr>`
		).join('')}`
		this.$el.html(html)
		return this;
	},
	events: {
		"click .model_input": function (ev) {
			keyCode = ""
			currentLine = 0
			// let params=delindex(ev.target.id)
			yzindex = ev.target.dataset.index
			let obj = yzdata[yzindex]
			inputClent($(ev.target).parent(), ev.target.id, yzindex)
			if (ev.target.id.indexOf('ZXFF') >= 0) {
				let html = ``
				if (obj.YZLX == 'cy' || obj.YZLX == 'zcydy') {
					html = ypyfplHtml(WRT_config.cyyf)
				} else {
					html = ypyfplHtml(WRT_config.yf)
				}
				$(`#${ev.target.id}_list`).html(html)
			} else if (ev.target.id.indexOf('ZXPL') >= 0) {
				let html = ``
				if (obj.YPID == "z" + WRT_config.yz_sz.xtmlid) {
					html = ypyfplHtml(WRT_config.xtpl, "ZXPL", yzindex)
				} else {
					html = ypyfplHtml(WRT_config.pl, "ZXPL", yzindex)
				}
				$(`#${ev.target.id}_list`).html(html)
			}
			keyCode = ev.target.id.split(yzindex)[0]
			if (ev.target.id.indexOf("LB") < 0) {
				if (yz_isModel) {
					$(`#${yz_isModel}`)[0].value = $(`#${yz_isModel}`)[0].ov || ""
				}
				yz_isModel = ev.target.id
				if (ev.target.value == "") {
					ev.target.value = $(`#${ev.target.id}`)[0].ov || ""
				}
				$(`#${ev.target.id}`)[0].ov = ev.target.value || $(`#${ev.target.id}`)[0].ov
				if (ev.target.id.indexOf('ZXFF') >= 0 || ev.target.id.indexOf('ZXPL') >= 0 || ev.target.id.indexOf('GYSJ') >= 0) {
					ev.target.value = ""
				}
			}
			isModel(ev.target.id)
		},
		"input .search_input": "search_input",
		"click .MC_search": "MCinput",
		"blur .MC_search": "MCblur",
		"input .MC_search": "yzHtml",
		"click .isModel>input": "dbSelect",
		"click .yzlxlist>input": "dbinput",
		"click .trlist": "yzSelect",
		"change .sfsl_change": function (ev) {
			sfsl_type = true
		},
		"click .sfsl_change": function (ev) {
			if (keyCode && keyCode != 'SFSL') {
				keyCode = ''
			} else {
				keyCode = 'SFSL'
			}
		},
		"blur .sfsl_change": function (ev) {
			let i = ''
			let value = ''
			if (ev.target && ev.target.dataset && ev.target.dataset.index) {
				i = ev.target.dataset.index
				yzindex = i
				value = ev.target.value
			}
			if (sfsl_type) {
				timer = setTimeout(function (ev) {
					sfsl_type = null
					xz_sfsl(value, 'SFSL', i)
				}, 120);
			}
		},
		// "click .lists_table td input":"keycode",
		// "keydown .zdclassname":"zdclassname",
		"click .easyui-datetimebox": function (ev) {
			let key = ev.target.dataset.index
			let name = ev.target.id.split(key)[0]
			if (ev.target.value) {
				$(`#${ev.target.id}`).datetimebox({
					value: gettime(ev.target.value, true),
					required: true,
					showSeconds: false,
				});
			} else {
				keyCode = name
				// let params=delindex(this.id)
				WRT_e.api.yz_sz.getDateString({
					params: { days: 0 }, success(data) {
						if (data.Result) {
							yzdata[key][name] = gettime(data.Result, true)
							$(`#${ev.target.id}`).datetimebox({
								value: gettime(data.Result),
								required: true,
								showSeconds: false,
							});
						}
					}
				})
			}
			$(`#${ev.target.id}`).datetimebox({
				stopFirstChangeEvent: true,
				onChange: function (data) {
					var options = $(this).datetimebox('options');
					// if (options.stopFirstChangeEvent) {
					//     options.stopFirstChangeEvent = false;
					//     return;
					// }
					if (this.id) {
						// let params=delindex(this.id)
						// let name = params.name
						// let list = params.index
						let time = $(this).datetimebox('getValue');
						if (gettime(time, true) != gettime(yzdata[key][name], true) || yzdata[key][name] == "") {
							yzdata[key][name] = gettime(time, true)
							xz_Same(name, key, time)
						}
					}
				}
			});
		},
	},
	//enetr
	// zdclassname(ev){
	//     itemEvent(ev)
	// },
	// keycode(ev){
	//     let index=ev.target.dataset.index
	//     let id=ev.target.id
	//     let text=id.split(index)
	//     if(text[0]){
	//         keyCode=text[0]
	//     }
	// },
	//查询下拉菜单
	search_input(ev) {
		let target = []
		// let params=delindex(ev.target.id)
		yzindex = ev.target.dataset.index
		let obj = yzdata[yzindex]
		if (ev.target.id.indexOf('ZXFF') >= 0) {
			if (obj.YZLX == 'cy' || obj.YZLX == 'zcydy') {
				target = WRT_config.cyyf
			} else {
				target = WRT_config.yf
			}
		} else if (ev.target.id.indexOf('ZXPL') >= 0) {
			if (obj.YPID == "z" + WRT_config.yz_sz.xtmlid) {
				target = islv(WRT_config.xtpl, "ZXPL", yzindex)
			} else {
				target = islv(WRT_config.pl, "ZXPL", yzindex)
			}
		} else if (ev.target.id.indexOf('GYSJ') >= 0) {
			target = WRT_config.gysj
		}
		search(ev.target.id, ev.target.value, target)
	},
	//下拉框选中
	dbSelect(ev) {
		let name = ev.target.parentNode.id
		let k = name.split("_")[0]
		if (k) {
			// let params=delindex(k)
			let key = ev.target.parentNode.dataset.index
			let div = k.split(key)[0]
			if (order.length == 0) {
				SetNextFocusObj(yzdata[key])
			}
			let title = div
			keyCode = ""
			for (let i = 0, j = order.length; i < j; i++) {
				if (order[i] == title && order[i + 1] == 'LB' && yzdata[key].LB) {
					title = 'LB'
				}
				if (order[i] == title) {
					keyCode = order[i + 1]
				}
			}
			yzindex = key
			yzdata[key][div] = ev.target.title
			yz_yzlx = ev.target.title
			if (ev.target.title.indexOf('术前') >= 0 || ev.target.title.indexOf('术中') >= 0) {
				issstzd()
			} else {
				yzdata[key].TZDID = ""
			}
			xz_Same(div, key, ev.target.title)
			if (yzdata[key].YZLX == 'cq'&&yzdata[key].iswpy) {
				sumxspjssj(key)
			}
		}
	},
	//根据医嘱类型修改其他html
	dbinput(ev) {
		// let name = ev.target.parentNode.id
		// let params=delindex(ev.target.parentNode.id)
		// let name=params.index
		let jrcy = '', zsbq = '';
		let list = ev.target.parentNode.dataset.index
		// let k = name.split("_")[0]
		// let list = k.substr(k.length - 1, 1)
		SetNextFocusObj(yzdata[list])
		yzdata[list].JRCY = ""
		yzdata[list].ZSBQ = ""
		if (ev.target.value.indexOf('草药') == 0) {
			if (list) {
				let temp = ypyfplHtml(WRT_config.cyyf)
				$(`#ZXFF${list}_list`).html(temp)
				let temp1 = `
                <select name="TSYF" style="width:145px;" class="sel" tag="0">
                ${_.map(WRT_config.cyts, (key) => `
                    <option value="${key.MC}">${key.MC}</option>
                `)}
                </select>`
				$(`#TSYF${list}`).html(temp1)
				let temp2 = lbHtml(ev.target.value)
				$(`#LB${list}_list`).html(temp2)
				// $(`#SFSL${list}`)[0].disabled = false
				$(`#CXTS${list}`)[0].disabled = true
				yzdata[list].LB = '3'
			}
			if (ev.target.value == '草药带药' && WRT_config.yz_sz.curyszkid == 33) {
				yzdata[list].JRCY = "1"
				yzdata[list].ZSBQ = "4"
				jrcy = '1'; zsbq = '4'
			}
		} else if (ev.target.value == '出院带药') {
			// yz_yzlx='cydy'
			// $(`#SFSL${list}`)[0].disabled = false
			// $(`#CXTS${list}`)[0].disabled = false
			let temp2 = lbHtml(ev.target.value)
			$(`#LB${list}_list`).html(temp2)
			yzdata[list].LB = '3'
			yzdata[list].JRCY = "2"
			jrcy = '2'; zsbq = ''
		} else {
			let temp = ypyfplHtml(WRT_config.yf)
			$(`#ZXFF${list}_list`).html(temp)
			let temp1 = `<input type="text" value="" name="TSYF" style="width:140px;" autocomplete="off" class="bd" tag="0">`
			$(`#TSYF${list}`).html(temp1)
			let temp2 = lbHtml(ev.target.value)
			$(`#LB${list}_list`).html(temp2)
			// $(`#SFSL${list}`)[0].disabled = true
			$(`#CXTS${list}`)[0].disabled = true
		}
		sortData()
		xz_Same('JRCY', list, jrcy)
		xz_Same('ZSBQ', list, zsbq)
		if (ev.target.value == '草药医嘱') {
			WRT_e.api.yz_sz.getBrLsCy({
				params: { al_blid: WRT_config.url.as_blid },
				success(data) {
					if (data.Code == 1 && data.Result) {
						WRT_config.BrLsCy = data.Result
					}
				}
			})
		}

	},
	MCblur(ev) {
		let id = ev.target.id
		let index = ev.target.dataset.index
		WRT_config.yz_isMc = ev.target.id
		if (yzdata[index] && yzdata[index].LB == 4 && ev.target.value) {
			$(`#${id}`)[0].ov = ev.target.value
			yzdata[index].MC = ev.target.value
		}
	},
	MCinput(ev) {
		let id = ev.target.id
		if (WRT_config.url.as_yzlb == 'jcyy' && ev.target.value=='') {
			WRT_e.ui.message({
				title: '提示信息',
				content: '只能从检查用药列表中选择药品！',
				onOk() {
				},
			})
			return
		} 
		else if (WRT_config.url.as_yzlb == 'jcyy' && ev.target.value!=''){
			return
		}
		if (yzindex >= yzdata.length) {
			yzindex = yzdata.length - 1
		}
		if (yzindex && yzindex >= 0) {
			$(`#MC${yzindex}`)[0].value = yzdata[yzindex].MC
		}
		// let params=delindex(id)
		// let name=params.name
		keyCode = 'MC'
		let index = ev.target.dataset.index
		yzindex = index
		currentLine = 0
		yz_isModel = false
		WRT_config.yz_isMc = false
		$(`#${id}`)[0].ov = yzdata[index].MC
		if (yzdata[index].LB != 4) {
			ev.target.value = ""
		}
		WRT_config.yz_isMc = ev.target.id
		// if(ev.target.value==list.MC||list.MC==""){
		// }
	},
	//根据输入的关键字查询
	yzHtml(ev) {
		currentLine = 0
		// WRT_config.defaultValue = ev.target.defaultValue
		let mhss = 1
		if ($("#optionsRadios1").is(":checked")) {
			mhss = 0
		}
		if ($("#optionsRadios2").is(":checked")) {
			mhss = 1
		}
		let id = ev.target.id;
		// let para=delindex(id)
		let key = ev.target.dataset.index
		if (ev.target.value == ".") {
			let foot = new footbtn_View()
			yzindex = key
			foot.ypgrmb()
			return
		}
		let list = yzdata[key]
		let as_jx = ""
		if (list && (list.YZLX == 'cy' || list.YZLX == 'zcydy') && list.ZHTXT) {
			let fd = yzdata.filter(item => item.ZH == list.ZH)
			if (fd && fd.length > 0) {
				fd.map(function (item) {
					if (item.MC) {
						let dataop = item.yp_dataop || {}
						as_jx = item.JX || dataop.jx || ""
						return
					}
				})
			}
		}
		let askey = ev.target.value
		if (askey.indexOf("'") >= 0) {
			askey = askey.replace(/\'/g, '');
		}
		let as_type = gettype(key)
		let params = {
			"al_bqid": WRT_config.yz_sz.bqid,
			"al_blid": WRT_config.url.as_blid,
			"as_type": as_type,
			"as_key": askey,
			"al_zkid": WRT_config.yz_sz.zkid,
			"as_mhss": mhss,
			"as_yfdms": WRT_config.zkyf,
			"as_jx": as_jx,
			"as_yzlb": "",
			"as_yzlx": list.YZLX
		}
		WRT_e.api.yz_sz.getItemList({
			params: params,
			success(data) {
				let temp = `<tr>
					<td width="30px" bgcolor="LIGHTSTEELBLUE">&nbsp;</td>
					<td width="260px" bgcolor="LIGHTSTEELBLUE">药品名称/治疗模板名称/膳食名称</td>
					<td width="80px" bgcolor="LIGHTSTEELBLUE">&nbsp;</td>
					<td width="80px" bgcolor="LIGHTSTEELBLUE">&nbsp;</td>
					<td width="120px" bgcolor="LIGHTSTEELBLUE">规格</td>
					<td width="80px" bgcolor="LIGHTSTEELBLUE">售价</td>
					<td width="40px" bgcolor="LIGHTSTEELBLUE">标准</td>
					<td width="25px" bgcolor="LIGHTSTEELBLUE"></td>
				</tr>`
				if (data.Code == 1) {
					if (ev.target.value == askey) {
						let json = JSON.parse(data.Result)
						var html = `
              ${temp}
							${_.map(json, (k, i) =>
							`<tr id="tr${i}" class="trlist" data-gcp="${k.gcp}" data="${k.mc}">
								<td>${k.lx}</td>
								<td>${k.mc}</td>
								${WRT_config.yz_sz.gjmkg == '1' ? `<td id="${k.val}"><div class="gjm_title" title="${k.gjm}">${k.gjm ? '●●●' : ''}</div></td>` : WRT_config.yz_sz.gjmkg == '2' ? `<td id="${k.val}"><div class="gjm_title" title="${k.gjm}">${k.gjm}</div></td>` : `<td id="${k.val}"></td>`}
								<td>${k.jc}</td>
								<td>${k.gg}</td>
								<td>${k.sj}</td>
								<td>${k.bz}</td>
								<td title="${k.dataop}"></td>
							</tr>`
						)}
                        `
						yz_isModel = ev.target.id
						inputClent($(`#${ev.target.id}`).parent(), ev.target.id, yzindex)
						$(`#${ev.target.id}_list`).html(html)
						$(`#${ev.target.id}_list`).css("display", "block")
					}
				} else {
					yz_isModel = ev.target.id
					$(`#${ev.target.id}_list`).html(temp)
					$(`#${ev.target.id}_list`).css("display", "block")
				}
			}
		})
	},
	//选中医嘱
	yzSelect(ev) {
		getKSSJ_TXT() 
		// console.log('(新增)触发选中医嘱', WRT_config.defaultKSSJ_TXT);
		WRT_config.ypfjxx = "";
		WRT_config.ypsyff = ""
		let el = yz_isModel
		if (!el) {
			yz_isModel = "MC" + yzindex
		}
		// let para=delindex(yz_isModel)

		let key = $(`#${el}`)[0].dataset.index
		let list = yzdata[key]
		for (let i in list) {
			if (list[i] != "") {
				if (yz_szlist.indexOf(i) >= 0) {
					list[i] = ""
				}
			}
		}
		let json = ev.currentTarget.children
		if (json) {
			let obj = {
				lx: json[0].innerHTML,
				mc: json[1].innerText,
				ypid: json[2].id,
				// gjm: json[3].innerHTML,
				gg: json[4].innerHTML,
				sj: json[5].innerHTML,
				bz: json[6].innerHTML,
				dataop: json[7].title,
				gcp:ev.currentTarget.dataset?ev.currentTarget.dataset.gcp:''
			}
			yzlogic(el, obj, key, true)
		}
	}
})

//底部按钮列表
var footbtn_View = WRT_e.view.extend({
	render() {
		var html = `
            <button class="submit e_btn">审核提交(t)</button>&nbsp;
            <button class="SaveYz e_btn">保存(s)</button>&nbsp;
						${WRT_config.url.as_yzlb == 'jcyy' ?``:`
							<button class="addgroup e_btn">新增组(1)</button>&nbsp;
							<button class="add e_btn">新增项(2)</button>&nbsp;
							<button class="insert e_btn">插入项(2)</button>&nbsp;
						`}
            <button class="delgroup e_btn">删除组(3)</button>&nbsp;
            <button class="del e_btn">删除项(4)</button>&nbsp;
						${WRT_config.url.as_yzlb == 'jcyy' ?``:`
							<button class="ypgrmb e_btn">药品个人模板(5)</button>&nbsp;
							<button class="e_btn" onclick="goModelhtml('常用药模板',0)">常用药(6)</button>&nbsp;
							<button class="e_btn zlyzmb">治疗医嘱模板(7)</button>&nbsp;
							<button class="e_btn zhyzmb">综合医嘱模板(8)</button>&nbsp;
						`}
            <button class="e_btn bt_dlcz">独立成组(Z)</button>&nbsp;
						${WRT_config.url.as_yzlb == 'jcyy' ?`
							<button class="e_btn"onclick="goModelhtml('检查用药列表','jcyy')">检查用药(J)</button>&nbsp;
						`:`
            	<button class="e_btn YpByMb">保存模板</button>&nbsp;
						`}
            <span id="span_blyz">
                <input type="checkbox" id="ck_blyz">
                <label id="lbl_ck_blyz" for="ck_blyz" title="新增医嘱时，复制上一条的开始时间">复制上条时间</label>&nbsp;
            </span>&nbsp;
            <span id="span_fzzxbq">
                <input type="checkbox" id="ck_fzzxbq">
                <label id="lbl_ck_fzzxbq" for="ck_fzzxbq" title="新增医嘱时，复制上一条的执行病区">复制上条病区</label>&nbsp;
            </span>&nbsp;
        `
		this.$el.html(html)
		return this;
	},
	events: {
		"click .submit": "submit",
		"click .SaveYz": "SaveYz",
		"click .addgroup": "addgroup",
		"click .add": "add",
		"click .insert": "insert",
		"click .del": "del",
		"click .delgroup": "delgroup",
		"click .ypgrmb": "ypgrmb",
		"click .zlyzmb": "zlyzmb",
		"click .zhyzmb": "zhyzmb",
		"click .bt_dlcz": "bt_dlcz",
		"click .YpByMb": "YpByMb",
	},
	//治疗医嘱模板
	zlyzmb() {
		getKSSJ_TXT() // 通过接口获取时间  WRT_config.defaultKSSJ_TXT后续新增使用这个时间
		WRT_e.api.yz_sz.getZlMb({
			params: { as_lb: 1 },
			success(data) {
				WRT_config.zlmb1 = JSON.parse(data.Result) || []
				if (WRT_config.zlmb1 && WRT_config.zlmb2) {
					getMbZlMb()
				}
			}
		})
		WRT_e.api.yz_sz.getZlMb({
			params: { as_lb: 2 },
			success(data) {
				WRT_config.zlmb2 = JSON.parse(data.Result) || []
				if (WRT_config.zlmb1 && WRT_config.zlmb2) {
					getMbZlMb()
				}
			}
		})
		// WRT_e.ui.model({
		//     id:'if_zlyzmb',
		//     title: '治疗医嘱模板',
		//     width: "945px",
		//     iframeURL:`${WRT_config.server}/zyyz/zlyzsr_mbnewSZ.aspx?t=0.5629146804746785&as_openmode=ehr3`,
		//     iframeHeight:'550px',
		//     iframe: true,
		// })
	},
	//综合医嘱模板
	zhyzmb() {
		getKSSJ_TXT() // 通过接口获取时间  WRT_config.defaultKSSJ_TXT后续新增使用这个时间
		if (WRT_config.ZhmbByLb1 && WRT_config.ZhmbByLb2 && WRT_config.ZhmbByLb3 && WRT_config.ZhmbByLb4) {
			getZhmbByLb()
			return
		}
		WRT_e.api.yz_sz.getZhmbByLb({
			params: { as_mblb: 1 },
			success(data) {
				if (data && data.Code == 1) {
					WRT_config.ZhmbByLb1 = data.Result
					if (WRT_config.ZhmbByLb1 && WRT_config.ZhmbByLb2 && WRT_config.ZhmbByLb3 && WRT_config.ZhmbByLb4) {
						getZhmbByLb()
					}
				}
			}
		})
		WRT_e.api.yz_sz.getZhmbByLb({
			params: { as_mblb: 2 },
			success(data) {
				if (data && data.Code == 1) {
					WRT_config.ZhmbByLb2 = data.Result
					if (WRT_config.ZhmbByLb1 && WRT_config.ZhmbByLb2 && WRT_config.ZhmbByLb3 && WRT_config.ZhmbByLb4) {
						getZhmbByLb()
					}
				}
			}
		})
		WRT_e.api.yz_sz.getZhmbByLb({
			params: { as_mblb: 3 },
			success(data) {
				if (data && data.Code == 1) {
					WRT_config.ZhmbByLb3 = data.Result
					if (WRT_config.ZhmbByLb1 && WRT_config.ZhmbByLb2 && WRT_config.ZhmbByLb3 && WRT_config.ZhmbByLb4) {
						getZhmbByLb()
					}
				}
			}
		})
		WRT_e.api.yz_sz.getZhmbByLb({
			params: { as_mblb: 4 },
			success(data) {
				if (data && data.Code == 1) {
					WRT_config.ZhmbByLb4 = data.Result
					if (WRT_config.ZhmbByLb1 && WRT_config.ZhmbByLb2 && WRT_config.ZhmbByLb3 && WRT_config.ZhmbByLb4) {
						getZhmbByLb()
					}
				}
			}
		})
		// WRT_e.ui.model({
		//     id:'if_zhyzmb',
		//     title: '综合医嘱模板',
		//     width: "945px",
		//     iframeURL:`${WRT_config.server}/yz_sz/zhmbsr.aspx?al_blid=${WRT_config.url.as_blid}&as_yzlbtype=${WRT_config.url.as_yzlb}&r=${new Date}&as_openmode=ehr3`,
		//     iframeHeight:'550px',
		//     iframe: true,
		// })
	},
	//药品个人模板
	ypgrmb() {
		getKSSJ_TXT() // 通过接口获取时间  WRT_config.defaultKSSJ_TXT后续新增使用这个时间
		WRT_e.api.yz_sz.getYpMbByLb({
			params: { as_type: 1, al_blid: WRT_config.url.as_blid, al_zkid: WRT_config.yz_sz.zkid, as_yqdm: WRT_config.yz_sz.yqdm, as_yzlx: WRT_config.url.as_yzlb },
			success(data) {
				if (data.Code == 1) {
					WRT_config.ypgrmb = data.Result
					WRT_config.ypgrmb_copy = data.Result
					let temp = `<div id="mb_gryp"><label>模板查询:</label><input id="td_gryp" class="text_class" type="text" autocomplete="off" value="" onkeydown="calAge(event,'mbgryp')" /></div><div id="dv_mbHtml_model">`
					temp += getypgrmb(data.Result)
					temp += '</div>'
					WRT_e.ui.model({
						id: 'mbHtml',
						title: '药品个人模板',
						width: "650px",
						content: temp,
						iframe: false,
					})
				}
			}
		})
	},
	//提交
	submit() {
		// setTimeout(function (ev) {
		// 	getyzData('submit')
		// }, 150)
		getyzData('submit')
		// let params = {
		//     as_yzlst: arr.join(","),
		//     al_blid: WRT_config.url.as_blid,
		//     al_cy: 0
		// }
		// WRT_config.api.yz_sz.SubmitYz({
		//     params: params, success(data) {
		//         if (data.Code == 1) {
		//             WRT_e.ui.hint({ msg: data.CodeMsg })
		//         }
		//     }
		// })
	},
	//保存
	SaveYz(ev) {
		getyzData('save')
		// let rtn = true
		// if (WRT_config.url.as_lclj == 1 && WRT_config.url.as_yzlb == "cq") {
		// 	yzdata.map(function (item) {
		// 		if (item.LB == 3) {
		// 			rtn = false
		// 			return
		// 		}
		// 	})
		// }
		// if (rtn) {
		// 	setTimeout(function (ev) {
		// 		getyzData('save')
		// 	}, 150)
		// } else {
		// 	WRT_e.ui.message({
		// 		title: '信息窗口',
		// 		content: '病人已进入临床路径，不能在长期医嘱里开药品医嘱，只能提交！',
		// 		onOk() {
		// 		},
		// 	})
		// }
	},
	//增加组
	addgroup() {
		let index = yzdata.length
		let obj = {}, lists = yzdata[index - 1]
		keyCode = "MC"
		if (index == 0) {
			for (let i in yz_obj) {
				obj[i] = ""
			}
			obj.YZLX = WRT_config.url.as_yzlb
			obj.BQID = WRT_config.yz_sz.bqid
			if (obj.YZLX == "cy" || obj.YZLX == "zcydy") {
				obj.ZXFF = '煎服'
				obj.ZXPL = 'bid'
				obj.GYSJ = 'pc'
				obj.SFSL = "7"
			}
			obj.XH = 1
			yzdata.push(obj)
			yzindex = 0
		} else if (lists && lists.MC) {
			index = parseInt(index)
			let check = $("#ck_fzzxbq").is(":checked")
			let sj = $("#ck_blyz").is(":checked")
			for (let i in yz_obj) {
				obj[i] = ""
				if (check) {
					if (i == 'YZLX' || i == 'LB' || i == 'BQID') {
						obj[i] = lists[i]
					}
				}
				if (sj) {
					if (i == 'KSSJ_TXT' || i == 'JSSJ_TXT') {
						obj[i] = lists[i]
					}
				}
			}
			if (obj.BQID == '') {
				obj.BQID = WRT_config.yz_sz.bqid
			}
			obj.YZLX = lists.YZLX || WRT_config.url.as_yzlb
			obj.LB = lists.LB
			obj.JRCY = ''
			if (obj.YZLX == "cydy") {
				obj.JRCY = lists.JRCY || "2"
			}
			if (obj.YZLX == "zcydy" && WRT_config.yz_sz.curyszkid == 33) {
				obj.JRCY = lists.JRCY || "1"
				obj.ZSBQ = lists.ZSBQ || "4"
			}
			let num = parseInt(lists.XH || 1) + 1
			if (obj.YZLX == "cydy" || obj.YZLX == "zcydy") {
				obj.XH = num + '-' + 1
			} else {
				obj.XH = num
			}
			yzdata.push(obj)
			yzindex = index + 1
			// yzindex++
		}
		sortData()
	},
	//增加项
	add() {
		let index = yzindex
		keyCode = "MC"
		if (!yzindex || index < 0 || index >= yzdata.length) {
			index = yzdata.length - 1
		}
		let check = $("#ck_fzzxbq").is(":checked")
		let sj = $("#ck_blyz").is(":checked")
		let obj = {}, lists = yzdata[index]
		if (!lists) {
			lists = yzdata[yzdata.length - 1] || {}
		}
		if (index < 0) {
			for (let i in yz_obj) {
				obj[i] = ""
			}
			obj.BQID = WRT_config.yz_sz.bqid
			obj.YZLX = WRT_config.url.as_yzlb
			if (obj.YZLX == "cy" || obj.YZLX == "zcydy") {
				obj.ZXFF = '煎服'
				obj.ZXPL = 'bid'
				obj.GYSJ = 'pc'
				obj.SFSL = "7"
			}
			obj.XH = parseInt(yzindex) + 1
			yzdata.push(obj)
			yzindex = 0
		} else {
			index = parseInt(index)
			for (let i in yz_obj) {
				obj[i] = ""
				if (check) {
					if (i == 'YZLX' || i == 'LB' || i == 'BQID') {
						obj[i] = lists[i] || ""
					}
				}
				if (i == 'KSSJ_TXT' || i == 'JSSJ_TXT') {
					obj[i] = lists[i] || ""
				}
			}
			if (obj.BQID == '') {
				obj.BQID = lists.BQID || WRT_config.yz_sz.bqid || ""
			}
			if (lists.LB == "2" || lists.LB == "1") {

			} else {
				// if (obj.BQID == '') {
				// }
				if (lists.ZHTXT == '┐') {
					yzdata[index].ZHTXT = '┐'
					obj.ZHTXT = '|'
				} else if (lists.ZHTXT == '|') {
					obj.ZHTXT = '|'
				} else if (lists.ZHTXT == "┘") {
					yzdata[index].ZHTXT = '|'
					obj.ZHTXT = '┘'
				} else if (lists.ZHTXT == '') {
					yzdata[index].ZHTXT = '┐'
					obj.ZHTXT = '┘'
				}
				obj.TZDID = lists.TZDID || ''
				obj.ZXFF = lists.ZXFF || ''
				obj.GYSJ = lists.GYSJ || ''
				obj.ZXPL = lists.ZXPL || 'qd'
				if (WRT_config.yz_sz.zkid == 49 && WRT_config.url.as_yzlb == 'ls') {
					obj.SJZZXPL = lists.SJZZXPL || ""
				}
			}
			obj.JRCY = ''
			obj.YZLX = lists.YZLX || WRT_config.url.as_yzlb
			if (obj.YZLX == "cy" || obj.YZLX == "zcydy") {
				obj.SFSL = lists.SFSL || lists.SFCS || '7'
			} else if (obj.YZLX == "cydy") {
				obj.JRCY = lists.JRCY || "2"
			}
			if (obj.YZLX == "zcydy" && WRT_config.yz_sz.curyszkid == 33) {
				obj.JRCY = lists.JRCY || "1"
				obj.ZSBQ = lists.ZSBQ || "4"
			}
			obj.LB = lists.LB
			yzdata.splice(index + 1, 0, obj)
			// if(!obj.KSSJ_TXT){
			//     setTime(index)
			// }
			yzindex = index + 1
		}
		// yzindex++
		sortData()
		// $(`#MC${yzindex}_list`).focus()
	},
	//插入项
	insert() {
		let index = yzindex
		keyCode = "MC"
		if (!index || index < 0 || index >= yzdata.length) {
			index = yzdata.length - 1
		}
		let check = $("#ck_fzzxbq").is(":checked")
		let sj = $("#ck_blyz").is(":checked")
		let obj = {}
		if (index < 0) {
			for (let i in yz_obj) {
				obj[i] = ""
			}
			obj.BQID = WRT_config.yz_sz.bqid
			obj.YZLX = WRT_config.url.as_yzlb
			if (obj.YZLX == "cy" || obj.YZLX == "zcydy") {
				obj.ZXFF = '煎服'
				obj.ZXPL = 'bid'
				obj.GYSJ = 'pc'
				obj.SFSL = "7"
			}
			obj.XH = parseInt(yzindex) + 1
			yzdata.push(obj)
			yzindex = 0
		} else {
			let list = yzdata[index]
			if (!list) {
				list = yzdata[yzdata.length - 1] | {}
			}
			if (list.LB == "2" || list.LB == "1") {
				addinsert(check, false)
				sortData()
				return
			}
			if (list.ZHTXT && list.ZHTXT != "┐") {
				addinsert(check, true)
			} else {
				WRT_e.ui.message({
					title: '信息窗口',
					content: "是否作为" + list.MC + "的项插入",
					onOk() {
						addinsert(check, true)
					},
					onCancel() {
						addinsert(check, false)
					}
				})
			}
			function addinsert(check, type) {
				let obj = {}
				let lists = yzdata[index]
				// if(!lists.MC){
				//     yzdata.splice(index,1)
				// }
				for (let i in yz_obj) {
					obj[i] = ""
					if (check) {
						if (i == 'YZLX' || i == 'LB' || i == 'BQID') {
							obj[i] = lists[i] || ""
						}
					}
					if (sj) {
						if (i == 'KSSJ_TXT' || i == 'JSSJ_TXT') {
							obj[i] = lists[i] || ""
						}
					}
				}
				if (obj.BQID == '') {
					obj.BQID = lists.BQID || WRT_config.yz_sz.bqid || ""
				}
				if (type) {
					for (let i in lists) {
						if (i == 'ZH' || i == 'XH' || i == 'YZLX' || i == 'KSSJ_TXT' || i == 'JSSJ_TXT' || i == 'LB' || i == 'ZXFF' || i == 'ZXPL' || i == 'GYSJ' || i == 'BQID') {
							obj[i] = lists[i]
						} else {
							obj[i] = ''
						}
					}
					if (lists.ZHTXT == '┐') {
						yzdata[index].ZHTXT = '|'
						obj.ZHTXT = '┐'
					} else if (lists.ZHTXT == '|') {
						obj.ZHTXT = '|'
					} else if (lists.ZHTXT == "┘") {
						obj.ZHTXT = '|'
					} else if (lists.ZHTXT == '') {
						yzdata[index].ZHTXT = '┘'
						obj.ZHTXT = '┐'
					}
					obj.JRCY = ''
					obj.TZDID = lists.TZDID || ''
					if (obj.YZLX == "cydy") {
						obj.JRCY = lists.JRCY || "2"
						obj.ZSBQ = lists.ZSBQ || "2"
					}
					if (obj.YZLX == "zcydy" && WRT_config.yz_sz.curyszkid == 33) {
						obj.JRCY = lists.JRCY || "1"
						obj.ZSBQ = lists.ZSBQ || "4"
					}
					if (WRT_config.yz_sz.zkid == 49 && WRT_config.url.as_yzlb == 'ls') {
						obj.SJZZXPL = lists.SJZZXPL || ''
					}
					if (obj.YZLX == 'cy' || obj.YZLX == 'zcydy') {
						obj.SFSL = lists.SFSL || lists.SFCS || '7'
					}
					yzdata.splice(index, 0, obj)
					// if(!obj.KSSJ_TXT){
					//     setTime(index)
					// }
					yzindex = index
					// if(yzindex==null||yzindex<=0){
					// }else{
					//     yzindex = index - 1
					// }
				} else {
					obj.JRCY = ''
					obj.ZHTXT = ''
					obj.YZLX = lists.YZLX || WRT_config.url.as_yzlb
					obj.LB = lists.LB
					if (obj.YZLX == 'cy' || obj.YZLX == 'zcydy') {
						obj.SFSL = lists.SFSL || lists.SFCS || '7'
					}
					yzdata.splice(index, 0, obj)
					// if(!obj.KSSJ_TXT){
					//     setTime(index)
					// }
					yzindex = index

				}
				// yzdata.splice(index,0,obj)
				sortData()
			}
		}
		sortData()
	},
	//删除项
	del() {
		let index = yzindex
		if (index < 0 || !index || yzindex > yzdata.length - 1) {
			index = yzdata.length - 1
		}
		// if(yzdata[index]&&(yzdata[index].LYLB==1||yzdata[index].LYLB==2||yzdata[index].LYLB==5)){
		// 	WRT_e.ui.hint({msg:`【${yzdata[index].MC}】无法删除（该医嘱由肠外营养医嘱、肠外营养套餐、临床路径导入）`})
		// 	return
		// }
		if (yzdata[index]) {
			index = parseInt(index)
			if (yzdata[index].YZID) {
				as_delidlist.push(yzdata[index].YZID)
			}
			if (yzdata[index].ZHTXT) {
				if (yzdata[index].ZHTXT == '┐') {
					if (yzdata[index + 1] && yzdata[index + 1].ZHTXT == '|') {
						yzdata[index + 1].ZHTXT = '┐'
					} else if (yzdata[index + 1] && yzdata[index + 1].ZHTXT == '┘') {
						yzdata[index + 1].ZHTXT = ''
					}
				} else if (yzdata[index].ZHTXT == '┘') {
					if (yzdata[index - 1] && yzdata[index - 1].ZHTXT == '|') {
						yzdata[index - 1].ZHTXT = '┘'
					} else if (yzdata[index - 1] && yzdata[index - 1].ZHTXT == '┐') {
						yzdata[index - 1].ZHTXT = ''
					}
					// yzdata[index-1].ZHTXT='┘'
				}
			}

			yzdata.splice(index, 1)
			yzindex = index - 1
		}
		sortData()
	},
	//删除组
	delgroup() {
		let index = yzindex
		if (index < 0 || !index || yzindex > yzdata.length - 1) {
			index = yzdata.length - 1
		}
		// if(yzdata[index]&&(yzdata[index].LYLB==1||yzdata[index].LYLB==2||yzdata[index].LYLB==5)){
		// 	WRT_e.ui.hint({msg:`【${yzdata[index].MC}】无法删除（该医嘱由肠外营养医嘱、肠外营养套餐、临床路径导入）`})
		// 	return
		// }
		if (yzdata[index]) {
			let xh = yzdata[index].XH
			let zhbs = yzdata[index].ZHTXT
			if (typeof xh == 'number') {

			} else if (xh.indexOf("-") >= 0) {
				xh = xh.split("-")[0]
			} else {
				xh = parseInt(xh)
			}
			as_delidlist.push(yzdata[index].YZID)
			// yzdata.splice(index, 1);
			let tdlist = JSON.stringify(yzdata)
			let arr = JSON.parse(tdlist)
			if (zhbs) {
				for (let i = 0; i < arr.length; i++) {
					let t = arr[i].XH
					if (typeof t == 'number') {

					} else if (t.indexOf("-") >= 0) {
						t = t.split("-")[0]
					} else {
						t = parseInt(t)
					}
					if (xh === t) {
						if (arr[i] && arr[i].YZID) {
							as_delidlist.push(arr[i].YZID)
						}
						arr.splice(i, 1);
						i--;
					}
				}
			} else {
				arr.splice(index, 1);
			}
			if (arr.length == 0) {
				$("#newyztitle_pad").css('display', 'none')
				$("#newyz_pad").css('display', 'none')
				$("#yz_pad").css('height', 'calc(100% - 100px)')
			} else {
				$("#newyztitle_pad").css('display', 'block')
				$("#newyz_pad").css('display', 'block')
				$("#yz_pad").css('height', '170px')
			}
			var yzlist = new yzlist_View()
			//监听已选遗数组
			yzdata = deepProxy(arr, (type, data) => {
				refresh = 0
				if (data.target.length > 0) {
					data.target.map(function (item, index) {
						if (item.KSSJ_TXT == '' && item.MC) {
							item.KSSJ_TXT = WRT_config.defaultKSSJ_TXT
						}
					})
				}
				if (data.target.KSSJ_TXT == '') {
					data.target.KSSJ_TXT = WRT_config.defaultKSSJ_TXT
				}

				yzlist.render()
				if (yzdata.length == 0) {
					$("#newyztitle_pad").css('display', 'none')
					$("#newyz_pad").css('display', 'none')
					$("#yz_pad").css('height', 'calc(100% - 100px)')
					$("#box_pad").css('display', 'none')
				} else {
					$("#newyztitle_pad").css('display', 'block')
					$("#newyz_pad").css('display', 'block')
					$("#yz_pad").css('height', '170px')
					$("#box_pad").css('display', 'block')
				}
				if (keyCode) {
					currentLine = 0
					let el = ""
					if (order.length == 0) {
						SetNextFocusObj(yzdata[yzindex])
					}
					if ((keyCode == "KSSJ_TXT" || keyCode == "JSSJ_TXT") && yzdata[yzindex][keyCode]) {
						keyCode = 'MC'
					}
					if ($(".izi_message").length > 0) {
						return
					}
					if (keyCode == "MC") {
						el = "MC"
						if (yzindex < 0 || yzindex == null || yzindex >= yzdata.length) {
							yzindex = yzdata.length - 1
						}
						if (yzdata[yzindex].MC) {
							$(`#${el}${yzindex}`)[0].ov = yzdata[yzindex].MC
						}
						$(`#${el}${yzindex}`)[0].value = ''
						$(`#${el}${yzindex}`).focus()
						return
					}
					el = keyCode
					if (el == "ZXFF" || el == "ZXPL" || el == "GYSJ" || el == "LB") {
						if (el == "LB" && yzdata[yzindex].LB >= 0) {
						} else {
							model_input(el, yzindex)
							$(`#${el}${yzindex}`)[0].value = ""
						}
						$(`#${el}${yzindex}`).focus()
						// if($(".izi_message").length<=0){
						// }
					} else {
						let val = $(`#${el}${yzindex}`).val()
						$(`#${el}${yzindex}`).focus().val('').val(val)
					}
					return
				}
			});
			//初始化
			yzlist.$el = $("#yzdata_list");
			yzlist.init({ data: yzdata }).render();
			yzindex = yzdata.length - 1
		}
		sortData()
	},
	//独立成组
	bt_dlcz() {
		WRT_e.ui.message({
			title: '提示信息',
			content: "确定要拆分这组医嘱吗?",
			onOk() {
				let index = yzindex
				if (index < 0 || !index) {
					index = yzdata.length - 1
				}
				if (yzdata[index] && yzdata[index].ZHTXT) {
					let fd = yzdata.filter(item => item.ZH == yzdata[index].ZH)
					if (fd && fd.length > 3) {
						fd.map(function (item, k) {
							if (item.MC == yzdata[index].MC) {
								if (k == 0 || k == fd.length - 1) {
									f_btdlcz(index)
								} else if (k >= parseInt(fd.length / 2)) {
									yzdata[index - 1].ZHTXT = '┘'
									yzdata[index].ZHTXT = '┐'
								} else {
									f_btdlcz(index)
								}
							}
							return
						})
					} else {
						f_btdlcz(index)
					}
				}
				sortData()
			},
			onCancel() { }
		})
	},
	//保存模板
	YpByMb() {
		WRT_e.api.yz_sz.GetYpByTime({
			params: {
				al_blid: WRT_config.url.as_blid,
				al_zkid: WRT_config.yz_sz.zkid
			},
			success(data) {
				if (data.Result) {
					WRT_config.YpByTime = data.Result || []
					let arr = data.Result || []
					let ZH = ''
					arr.map(function (item) {
						let dataop = item.DATAOP.split(",")
						if (ZH == '') {
							ZH = item.ZH
							item.check = true
							if (item.ZH == ZH) {
								item
							}
						} else if (item.ZH == ZH) {
							item.check = false
						} else if (item.ZH != ZH) {
							ZH = item.ZH
							item.check = true
						}
					})
					let temp = `<div>
                    <input type="button" class="e_btn" value="保存为常用药" onclick="SaveAsMb('1');">
                    <input type="button" class="e_btn" value="保存为个人模板" onclick="SaveAsMb('2');">
                    <p style="color:red;">以下是你最近&nbsp;2&nbsp;天内对该病人开过的药品：</p>
                    <table border="0" width="380px" id="tb_mdetail">
                        <tbody>
                            <tr>
                                <td width="20px" bgcolor="LIGHTSTEELBLUE">&nbsp;</td>
                                <td width="280px" bgcolor="LIGHTSTEELBLUE">药品名称</td>
                                <td width="80px" bgcolor="LIGHTSTEELBLUE">规格</td>
                            </tr>
                            ${_.map(arr, (obj) =>
						`<tr>
                                <td>
                                ${obj.check ? `<input type="checkbox" value="${obj.YPID}" class="${obj.ZH}" dataop="${obj.DATAOP}" name="mbypxz">` : ''}
                                </td>
                                <td>${obj.MC}</td>
                                <td>${obj.YPGG}</td>
                            </tr>`).join('')}
                        </tbody>
                    </table>
                    </div>`
					WRT_e.ui.model({
						id: "YpByMb",
						title: "保存模板",
						width: "450px",
						content: temp,
						iframe: false,
					})
				}
			}
		})
	}
})
