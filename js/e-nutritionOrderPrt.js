let url = window.location.href.split("?") || []
let text = url[1].split("&")
let params = {}
for (let i of text) {
  let fd = i.split("=")
  params[fd[0]] = fd[1]
}
//统一页面启动(新营养医嘱)
$(document).ready(() => {
	// 3、页面初始化函数
	WRT_e.api.nutriOrderList.getInit({
		params: {
			al_blid: params["as_blid"],
		},
		success(data) {
			WRT_config.nOList_Init = JSON.parse(data.d) // 页面初始化基础信息
			console.log('页面初始化上个页面已经存放在',WRT_config.getNOLInit,JSON.parse(data.d)) 
			// 7、获取单条医嘱单的记录
			WRT_e.api.nutriOrderList.getNurtionOrder({
				params: {
					al_yzdid: params["as_id"],
					// al_blid: params["as_blid"],
				},
				success(data) {
					// WRT_config.nutriOrderListOne = data.d
					if(params["as_id"] == 0){
						// 新增
						WRT_config.nutriOrderListOne = {}
						// BMIcalumet()
						// Beecalumet()
					} else {
						// 详情
						WRT_config.nutriOrderListOne = JSON.parse(data.d) // 指定医嘱单
					}
					// 初始化
					app.init()
				}
			})
		}
	})
  
})
var app = {
  init: function () {
    // 基础信息
    var NutriBase = new Base_View();
    NutriBase.$el = $("#nutriOrderShowBaseInfo");
    NutriBase.init({
      data: {
        baseInit: WRT_config.nOList_Init, // 基础信息
        nolOne: WRT_config.nutriOrderListOne, // 患者营养医嘱单的记录
      } 
      // Bdata: WRT_config.nutriOrderListBase
    }).render();
    // 第二部分
		// 8、获取营养医嘱药品列表(list上所有药品)
    WRT_e.api.nutriOrderList.getNurtionDrugList({
      params: {
        as_yfdm:  WRT_config.nOList_Init.YFDM, // 药房代码
        // al_yzdid: params["as_id"]
      },
      success(data) {
        WRT_config.nurtionDrugList = JSON.parse(data.d) // 营养医嘱药品列表
        try {
          if(params["as_id"] != 0){
            // 9、获取营养医嘱药品列表
            WRT_e.api.nutriOrderList.getNurtionOrderDrugs({
              params: {
                al_yzdid: params["as_id"]
              },
              success(data) {
                WRT_config.nurtionDrugListBrYyJL = JSON.parse(data.d)
              }
            })
            // // 11、获取病人相关检验结果
            // WRT_e.api.nutriOrderList.getHyjg({
            //   params: {
            //     al_yzdid: params["as_id"],
            //     al_blid: params["as_blid"],
            //     as_zyh: WRT_config.nutriOrderListOne.ZYH,
            //   },
            //   success(data) {
            //     WRT_config.noxgjcHyjg = JSON.parse(data.d)
            //   }
            // })
          }
        } finally {
					var NutriAllTable = new allTable_View();
					NutriAllTable.$el = $("#nutriOrderTableAll");
					NutriAllTable.init({
						data: {
							baseInit: WRT_config.nOList_Init, // 基础信息
							nolOne: WRT_config.nutriOrderListOne, // 患者营养医嘱单的记录
							listData: WRT_config.nurtionDrugList, // 列表
              listJLData:  WRT_config.nurtionDrugListBrYyJL || [], // 对应病人用药剂量（对应详情表格初始参数 ：一次用量）
              // xgjcHyjg: WRT_config.noxgjcHyjg 
						} 
					}).render();
				}
			}
		})
		if (WRT_config.nutriOrderListOne.ZTBZ == "2" || WRT_config.nutriOrderListOne.ZTBZ == "1"){
			window.print();
		} else {
			WRT_e.ui.hint({
				type: 'info',
				msg:'当前状态下不能进行打印!'
			})
		}

  }
}
/********************公用方法********************/
// 获取当前时间
function getDateTimeNow() {
	var time = new Date()
	var Y = time.getFullYear()
	var M = time.getMonth()
	var D = time.getDay()
	var h = time.getHours()
	var m = time.getMinutes()
	var s = time.getSeconds()
	return Y + '-' + M + '-' + D + ' ' + h + ':' + m + ':' + s
}


/********************视图********************/
var Base_View = WRT_e.view.extend({
	render: function () {
		// 初始数据
		let html = `
		<div>
			<div style="font-size:22px;texr-align:center;">胃肠外营养医嘱单</div>
			<div>
				<span class="tBinfo"><strong>姓名：</strong>${this.data.baseInit.BRXM}</span>
				<span class="tBinfo"><strong>性别：</strong>${this.data.baseInit.BRXB=='1'?`男`:`女`}</span>
				<span class="tBinfo"><strong>年龄：</strong>${this.data.baseInit.NL} 岁</span>
				<span class="tBinfoBold" style="padding-left: 10px;min-width: 135px; max-width: 355px;">
					身高：<span class="tBinfoInput" name="height" >${this.data.nolOne.SG}</span> CM
				</span>
				<span class="tBinfoBold" style="min-width: 134px; max-width: 355px;">
					体重： <span class="tBinfoInput" name="weight" >${this.data.nolOne.TZ}</span> KG
				</span>
				<span>${this.data.baseInit.BQID}病区</span>
				<span>${this.data.baseInit.CWH}床</span>
				<span><strong>病案号：</strong>${this.data.baseInit.BAH}</span>
			</div>
		</div>
		`
		this.$el.html(html)
		return this;
	},
})

var allTable_View = WRT_e.view.extend({
	render: function () {
		
		// <span>开单医生：${this.data.nolOne.KDYS}</span>
		// 初始数据
		let html = `
		<div>
			<div class="line1">
			${this.table1().indexOf(',')!=-1?`${this.table1().replace(/,/g, '')}`:``}
			</div>
			<div class="line2">
			  ${this.table2()}
			</div>
			<div class="line3">
				<span style="width:35%">开单医生：</span>
				<span>打印时间：${getDateTimeNow()}</span>
			</div>
		</div>
		`
		this.$el.html(html)
		return this;
	},
	getData:function() {
		// 表格(营养医嘱药品列表)
    let initialData = {
      baseInit: this.data.baseInit, // 基础信息
      nolOne: this.data.nolOne, // 获取单条医嘱单的记录(获取身高体重等基础信息)
      // xgjcHyjgArr: this.data.xgjcHyjg,  // 相关检验结果
      listBodyData: [], // 列表(展示)
      list1Data: [], // 列表(存在数据)
      table1Data:[], // 计算表格
      btnData:[], //  按钮部分
      table2Data:[], // 展示表格
    }
		  // 列表
			this.data.listData.map((item,index) => {
				item.BH = index+1
				initialData.listBodyData.push(item)
			})
			// 对应病人用药剂量（对应详情表格初始参数 ：一次用量）
			this.data.listJLData.map((item,index) => {
				initialData.list1Data.push(initialData.listBodyData.filter(e=>{
					if(e.MC == item.YPMC){
						e.YCYL = item.YCYL
					}}
				))
			})
			return initialData
	},
	 // 展示列数（list部分）
	 pages:function(){
    var pages = []
    var objArr = this.getData();
    var obj = objArr.listBodyData
    obj.forEach((item, index) => {
      var page = Math.floor(index / ((obj.length/2)))
      if (!pages[page]) {
        pages[page] = []
      }
      pages[page].push(item)
    })
    return pages
  },
	table1: function(){
    var objArr = this.pages();
		return `
    ${_.map(objArr,(obj,index)=>`
      <table align="start" id="tb_Table" class="tb_title`+index+`">
        <tbody>
          <tr>
            <td>
              ${_.map(obj,(item,i)=>`
                <tr align="center" id="table_header" class="table_headerD table_header`+i+`">
                  <td id="table-nr`+i+`" class="tablelist-nr" style="padding: 0 5px;">${item.BH}</td>
                  <td id="table-nr`+i+`" class="tablelist-nr">${item.MC}</td>
                  <td id="table-nr`+i+`" class="tablelist-nr">${item.JL} * </td>
                  <td id="table-nr`+i+`" class="tablelist-nr">
                    <input id="table_nrInput`+i+`" class="table_nrInput" type="text" value="${item.YCYL || ''}" ${this.data.nolOne.ZTBZ == '2' || this.data.nolOne.ZTBZ == '0'?`readonly`:``}/>
                  </td>
                  <td id="table-nr`+i+`" class="tablelist-nr" style="padding: 0 5px;">${item.JLDW}</td>
                </tr>
              `)}
            </td>
          </tr>
        </tbody>
      </table>
    `)}`
	},
	table2: function(){
		var objArr = this.getData();
		return`
		<table border="1" align="start" class="show_Table">
      <tbody>
        <tr>
          <td>
            <tr align="start">
              <td class="table-nr">
                <span>
									<span>基础能量消耗：${this.data.nolOne.BEE || 0}</span>
									<span>Kcal/d</span>
                </span>
              </td>
              <td class="table-nr">
                <span>
                  <span>非蛋白能量：${this.data.nolOne.FDBNL || 0}</span>
									<span>Kcal</span>
                </span>
              </td>
              <td class="table-nr">
                <span>
									<span>总氮量：${this.data.nolOne.ZDL || 0}</span>
                	<span>g</span>
                </span>
              </td>
              <td class="table-nr">
                <span>
									<span>总液体量：${this.data.nolOne.ZYTL || 0}</span>
									<span>ml</span>
                </span>
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr">
                <span>
									<span>糖:胰岛素: ${this.data.nolOne.T_YDS || 0}</span>
                <span>
              </td>
              <td class="table-nr">
								<span>能量:氮: ${this.data.nolOne.NL_D || 0}</span>
							</td>
              <td class="table-nr">
                <span>糖:脂: ${this.data.nolOne.T_ZF || 0}</span>
              </td>
              <td class="table-nr">
                <span>NRS2002: ${this.data.nolOne.NRS || 0}</span>
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr">
                <span>BMI: ${this.data.nolOne.BMI || 0}</span>
              </td>
              <td class="table-nr">
                <span>渗透压: ${this.data.nolOne.STY || 0}</span>
              </td>
              <td colspan="2"></td>
            </tr>
            <tr align="start">
              <td class="table-nr" colspan="4">
                <span>临床诊断: ${this.data.nolOne.LCZD || ''}</span>
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr1" colspan="4">
								<span>
									<span style="width:30%">开始时间: ${(this.data.nolOne.KSSJ).replace('T',' ') || ''}</span>
									<span style="width: 70%; display: flex; justify-content: flex-end;">
										<span style="padding-right:20px">持续输液: ${this.data.nolOne.CXTS || ''} 天</span>
										<span style="padding-right:20px">用药频率: ${this.data.nolOne.ZXPL || ''}</span>
										<span style="padding-right:20px"> 用法:&nbsp;营养静脉滴注</span>
									</span>
								</span>
              </td>
            </tr>
          </td>
        <tr>
      </tbody>
    </table>`
	},
})

