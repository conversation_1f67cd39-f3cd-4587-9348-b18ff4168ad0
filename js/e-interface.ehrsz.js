WRT_e.api = WRT_e.api || {}
WRT_e.api.ehrSz = {
  //获取主页内页数据
  getInit: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_init',
        type: 'Post',
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取病人数据 params, success, error
  getPatientList: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      for (let i = 0; i < brLists.length; i++) {
        if (o.params.as_lb == brLists[i].as_lb) {
          o.msg = brLists[i].data
        }
      }
    } else {
      if (this.ajaxPatient) this.ajaxPatient.abort()
      this.ajaxPatient = $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetPatientList',
        type: 'post',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //切换病区病人数据 params, success, error
  getPatientListByZkBqid: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      for (let i = 0; i < brLists.length; i++) {
        if (o.params.as_lb == brLists[i].as_lb) {
          o.msg = brLists[i].data
        }
      }
    } else {
      if (this.ajaxPatient) this.ajaxPatient.abort()
      this.ajaxPatient = $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetPatientListByZkBqid',
        type: 'post',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //危急值处理弹窗控制
  getWjzMessage: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "成功获取数据!",
        "Result": "0"
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_getWjzMessage',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //医嘱点评提示窗口控制接口
  grjkPop: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "成功获取数据!",
        "Result": "0"
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_grjkPop',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //院感提示传染列表弹窗控制接口
  showCrbfh: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "成功获取数据!",
        "Result": "0"
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_showCrbfh',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //院感监控预警列表获取接口
  grjkpopinit: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "成功获取数据!",
        "Result": "0"
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/grjkpop.aspx/e_init',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //预出院列表获取接口
  ycypopinit: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "成功获取数据!",
        "Result": "0"
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/brmainleft.aspx/getJhcysjSignal',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //电子病历调阅申请待处理弹框提醒
  getPopTip: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "成功获取数据!",
        "Result": "0"
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_PopTip',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //根据菜单ID获取URL
  GetJkurl: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetMenuUrl',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //切换专科，获取病区信息
  getZkdyBq: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功!",
        "Result": "[{\"bqid\":\"117\",\"zdym\":\"82\",\"bmmc\":\"血透病区\"},{\"bqid\":\"3315\",\"zdym\":\"92\",\"bmmc\":\"A-血透病区\"}]"
      })
    } else {
      if (this.ajaxPatient) this.ajaxPatient.abort()
      this.ajaxPatient = $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetZkdyBq',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //判断当前选择病区与当前显示病人信息中第一个病人病区是否同一个病区
  getIsSameBQ: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "病区相同!",
        "Result": 1
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_IsSameBQ',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //病人质控信息数量
  getPaQcCnt: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功!",
        "Result": 0
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetPaQcCnt',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //得到医生是否存在未CA签章文书
  getDocUnsign: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetDocUnsign',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //登出
  info_Retoken: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server2 + '/authentication/v1/login/getTokenUser',
        type: 'get',
        dataType: "json",
        data: '',
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (o.success) o.success(error)
        }
      })
    }
  },
  //Cookie校验
  info_cookie: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_SessionISValid',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (o.success) o.success(error)
        }
      })
    }
  },
  //得到会诊单信息数量
  getHzdCount: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {} else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetHzdCount',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //大数据预测
  getDSJYC: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetDSJYC',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //会诊单详细信息
  getHzdInfo: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetHzdInfo',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //需要医务科参加的会诊通知
  getHZDNeedYWCJ: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetHZDNeedYWCJ',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //特殊抗菌药物会诊单、三联抗菌药物会诊单
  getKjywhzd: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetKjywhzd',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //辅助登录接口
  getFZDLParms: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetFZDLParms',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //清空我的病人
  clearAllMyPat: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_clearAllMyPat',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //通过blid去除我的病人某个病人
  deleteMyPatByBlid: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_deleteMyPatByBlid',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //根据类型得到我的病人 2按病人 1按床位
  getMyPatByLX: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "成功获取数据!",
        "Result": "{\"BRXX\":[{\"_RYJLRQ\":null,\"_SCJLRQ\":null,\"BQCYSJ\":null,\"ZGHSID\":null,\"ZGYSID\":null,\"TZ\":null,\"BZDM\":\"\",\"CRBS\":false,\"QTZJH\":null,\"QTZJLX\":null,\"DWDZ\":null,\"XZZ\":null,\"XZZDM\":null,\"DRGZT\":null,\"RYYQ\":null,\"FLAG\":null,\"BKIMG\":\"\",\"ZYID\":1492686,\"BLID\":1492686,\"ZYH\":\"1537030\",\"BRBH\":\"00011410956108\",\"JSLX\":\"自费\",\"JSDM\":\"000\",\"BQID\":3040,\"ZKID\":88,\"CWH\":\"002\",\"BRXM\":\"王小明\",\"BRXB\":\"1\",\"CSRQ\":\"0001-01-01T00:00:00\",\"HYZK\":null,\"ZYDM\":\"263\",\"GJDM\":null,\"JGDM\":null,\"MZDM\":null,\"SFZH\":null,\"LXDZ\":null,\"LXDH\":null,\"JSXM\":null,\"JSGX\":null,\"JSDH\":null,\"DWDM\":null,\"DWMC\":\"青霉素针(400万)[基]$+^青霉素针(400万)[基]$+^青霉素针(400万)[基]$+^\",\"JSX\":null,\"RYQK\":null,\"HLJB\":\"一级护理\",\"JZXE\":0.0,\"JZXEZJ\":0.0,\"ZFYJK\":0.0,\"ZFTZZE\":0.0,\"ZZFJE\":0.0,\"ZJZJE\":0.0,\"ZZLJE\":0.0,\"ZJMJE\":0.0,\"CYSJ\":null,\"JSSJ\":null,\"SCRYSJ\":\"2019-05-06T12:32:09\",\"CWRYSJ\":\"2019-05-06T12:32:09\",\"BQRYSJ\":\"2019-05-06T14:06:27\",\"ZDDM\":null,\"RYZD\":\"关节痛\",\"ZTBZ\":\"4\",\"CZZID\":null,\"YZY\":null,\"ZDYM\":\"263\",\"ZLZID\":0,\"JBID\":null,\"SCJLRQ\":null,\"RYJLRQ\":null,\"ZFYE\":0.0,\"PASSPORT\":null,\"WZBR\":false,\"RJSS\":null,\"MDRO\":\"\",\"QCSL\":0,\"BQMC\":null,\"VTETX\":\"\",\"ZYID_MOTHER\":null}]}"
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetMyPatByLX',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //保存个性化记录
  SaveGXHJL: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_SaveGXHJL',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 保存移动建议审批通知个性化
  SaveYdspGXH: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_SaveYdspGXH',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取超过两天没有归档的病人、外购药品申请记录、转/跨科病历修正申请的数量
  get2DayUndocumented: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_Get2DayUndocumented',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //得到某个病人的备注/跨科病历修正申请的数量
  getBz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_getBz',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //修改某个病人的备注/跨科病历修正申请的数量
  addBz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_addBz',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //得到某个病人检查检验的地址
  getJyJcUrl: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_getJyJcUrl',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取全部专科信息或当前用户权限范围内的专科信息
  GetAllZkxx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/xggrxx.aspx/e_GetAllZkxx',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取全部专科信息或当前用户权限范围内的专科信息
  ChangeZK: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/xggrxx.aspx/e_ChangeZK',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //得到接收的反馈信息数量
  GetFKCount: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/fkzxxt/fkzxxt.ashx?Method=GetFKXXCount',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //审方结果数量
  GetSfxtShjgCount: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/hlyy/sfxt.aspx/e_GetSfxtShjgCount',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //密码即将到期
  PWDTip: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_PWDTip',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //密码即将到期
  CheckSjzyz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/tj/sjzyz.aspx/e_CheckSjzyz',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //血糖管理虚拟病区病人列表接口文档
  GetXTGLInfo: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/blcx/xtglbrlist.aspx/GetXTGLInfo',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //未填报单病种
  GetUnfilledInfo: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/ehrTX.aspx/e_GetUnfilledInfo',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取主页内页数据
  GetSysMgs: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/BrMain.aspx/e_GetSysMgs',
        type: 'Post',
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取病历归档提示数据
  GetUndocumentedPatList: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetUndocumentedPatList',
        type: 'Post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取消息提醒
  getMessageAggre: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_getMessageAggre',
        type: 'Post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 面切换专科时，记录日志
  getSaveQhzkRz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/saveQhzkRz',
        type: 'Post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 公共床位 params, success, error （入参出参和 专科病区病人 菜单调用的接口一致）
  getPatientListByZkBqidGgcw: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      for (let i = 0; i < brLists.length; i++) {
        if (o.params.as_lb == brLists[i].as_lb) {
          o.msg = brLists[i].data
        }
      }
    } else {
      if (this.ajaxPatient) this.ajaxPatient.abort()
      this.ajaxPatient = $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetPatientListByZkBqid_ggcw',
        type: 'post',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 面切换专科时，记录日志
  CheckeLogin: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_CheckLogin',
        type: 'Post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // qc
  GetZkfkxx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetZkfkxx',
        type: 'Post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 全院清点单自查表url  EhrSz.aspx/e_GetFMenuUrl
  
  GetFMenuUrl: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetFMenuUrl',
        type: 'Post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  
  getyhxx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_yhxx',
        type: 'post',
        dataType: "json",
        // data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //处方用法用量列表
  getYjkYfylxgsqZY: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/drugs/v1/drugInfo/getYjkYfylxgsqZY?tianShu=&yiShiID=${o.params.yiShiID}&zhuanKeID=${o.params.zhuanKeID}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //根据处方号获取未审批的用法用量修改申请数据
  getYjkYfylxgsqWithBRXXByCFH: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/drugs/v1/drugInfo/getYjkYfylxgsqWithBRXXByCFH?chuFangHao=${o.params.chuFangHao}`,
        type: 'post',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  usageDosageListUpdate: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/drugs/v1/drugInfo/usageDosageListUpdate?caoZuoLX=${o.params.caoZuoLX}&jiLuID=${o.params.jiLuID}`,
        type: 'post',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("userId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  getWeiYangYJ: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/nursing/Ehr/getWeiYangYJ?zhuanKeID=${o.params.zhuanKeID}`,
        type: 'post',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //传染病列表
  getCriticalValueListByUserId: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/app-intelligentmedical/v1/criticalvalue/getCriticalValueListByUserId`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //30分钟后继续填报
  updateInfectiousDiseaseList: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/app-intelligentmedical/v1/criticalvalue/updateInfectiousDiseaseList`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //根据拼音或五笔获传染病取诊断列表
  getInfectiousDiagnosesInfoByPinYinOrWuBi: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/app-doctorstation/v1/diagnose/getInfectiousDiagnosesInfoByPinYinOrWuBi?leiBie=${o.params.leiBie}&pinYinOrWuBi=${o.params.pinYinOrWuBi}&pageSize=${o.params.pageSize}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //医生站_传染病处理_上报传染病url
  infectiousDiseaseReportURL: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/app-intelligentmedical/v1/criticalvalue/infectiousDiseaseReportURL?bingRenBH=${o.params.bingRenBH}&jiBingID=${o.params.jiBingID}&yiChangFKJLID=${o.params.yiChangFKJLID}&zhenLiaoHDID=0&jiLuID=0`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //查询_查询是否已上报传染病
  shiFouSB: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/app-intelligentmedical/v1/criticalvalue/shiFouSB?bingRenBH=${o.params.bingRenBH}&jiBingID=${o.params.jiBingID}`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //传染病处理_上报传染病更新危急值记录
  updateInfectiousDisease: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/app-intelligentmedical/v1/criticalvalue/updateInfectiousDisease`,
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },

  // 获取病区数据：e_getAllBqData（无参）
  getAllBqData: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_getAllBqData',
        type: 'post',
        dataType: "json",
        data: '',
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },

  // 根据病区ID查询：e_getPreviousDayXZZDByBqid(long ll_bqid) data: JSON.stringify(o.params),
  getSearchBqid: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_getPreviousDayXZZDByBqid',
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },

  // 根据病案号查询：e_getPreviousDayXZZDByBAH(string as_bah)
  getSearchBAH: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_getPreviousDayXZZDByBAH',
        type: 'post',
        dataType: "json",
        data:JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //新生儿电子病历提醒
  getXinShengErTx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
    } else {
      $.ajax({
        url: WRT_config.server2 + `/medicaladvice/v1/AdviceInpatient/getXinShengErTx`,
        type: 'get',
        dataType: "json",
        // data:JSON.stringify(o.params),
        beforeSend: function (XMLHttpRequest) {
          XMLHttpRequest.setRequestHeader("token", sessionStorage.getItem("token"));
          // XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
          XMLHttpRequest.setRequestHeader("currUserId", sessionStorage.getItem("userid"));
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
}

