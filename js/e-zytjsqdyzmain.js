/******************公共对象 ******************/
var params = {} //url带参
var ysbmid = '' //医生部门id
var zNodes = [] //目录
var sqdstate = 0 //编辑状态 0新增 1修改
/******************统一页面启动 ******************/
$(document).ready(() => {
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  WRT_e.api.zytjsqdyzmain.getInit({
    params: {
      ls_zllx: params['av_zllx'],
      ls_zyid: params['av_blid'],
      ll_blid: params['av_blid'],
      ll_zkid: params['av_zkid'],
      ll_bqid: params['av_bqid']
    },
    success(data) {
      if (data.Code == 1) {
        console.log(data.Result)
        ysbmid = data.Result.ysbmid
        //左侧目录
        //过滤掉个人模板和专科模板
        left_menu(data.Result.mbtree.filter(e => e.yzmlid > -5))
        //历史申请单
        $("#zytjsqdyzmain_lssqd").html(
          new lssqd_View().init({
            data: data.Result.lssqd
          }).render().$el
        )
      }
    }
  })
  //搜索
  txtKeySearch()
})

/******************公共方法 ******************/
//设置树杈目录
function left_menu(data) {
  //目录
  function getMenuBtnList(arr, list, pId = 0) {
    for (let item of arr) {
      list.push({
        id: item.yzmlid,
        pId,
        name: item.mc
      })
      if (item.children.length > 0) {
        getMenuBtnList(item.children, list, item.yzmlid);
      }
    }
  }
  getMenuBtnList(data, zNodes)
  function toTree(data) {
    let result = []
    if(!Array.isArray(data)) {
        return result
    }
    data.forEach(item => {
        delete item.children;
    });
    let map = {};
    data.forEach(item => {
        map[item.id] = item;
    });
    data.forEach(item => {
        let parent = map[item.pId];
        if(parent) {
            (parent.children || (parent.children = [])).push(item);
        } else {
            result.push(item);
        }
    });
    return result;
  }
  let arr=toTree(zNodes)
  let html = `
  <ul>
    ${_.map(arr,(obj,index)=>`
      <li>
        <div class="firstli" onclick="treechange(this,${index},${obj.id},${obj.pId},'${obj.name}',${obj.children?"true":"false"})">${obj.name}${obj.children?`<i class="glyphicon glyphicon-menu-down"></i>`:""}</div>
        <ul class="boxul" style="display:none;padding-left: 5px;">${treeDataHtml(obj.children)}</ul>
      </li>`
    ).join('')}
  </ul>`
  $("#treeDemo").html(html)
  // var setting = {
  //   data: {
  //     simpleData: {
  //       enable: true
  //     }
  //   },
  //   view: {
  //     showIcon: false,
  //     dblClickExpand: false
  //   },
  //   callback: {
  //     onClick: zTreeOnClick
  //   }
  // };
  // $.fn.zTree.init($("#zytjsqdyzmain_list_tree"), setting, zNodes);
}
//绘制树结构
function treeDataHtml(target){
  if(!target||(target&&target.length==0)){
    return ''
  }
  let temp=`
  
    ${_.map(target,(obj,key)=>
      `<li>
        <div class="boxli" onclick="treetwochange(this,${key},${obj.id},${obj.pId},'${obj.name}',${obj.children?"true":"false"})">${obj.children?`<i class="iconfont icon-xiala" style="font-size: 10px;"></i>`:`<span style="padding-left:12px"></span>`}${obj.name}</div>
        <ul class="boxul" style="display:none;padding-left: 5px;">${treeDataHtml(obj.children)}</ul>
      </li>
        `  
    ).join("")}
  
  `
  return temp
}
//树点击
function treechange(ev,index,id,pId,name,type){
  let el=ev.className.split(" ")[0]
  if(!type){
    zNodeOnClick({id:id,pId:pId,name:name})
  }
  let div=$(`.${el}`).parent()
  for(let i=0;i<div.length;i++){
    let mc=""
    if(i!=index){
      let td=div[i].children[0].className
      if(td.indexOf("firstactive")>=0){
        let tf=td.split("firstactive")
        mc=tf[0]+tf[1]
        div[i].children[0].className=mc
        if(div[i].children[0].children[0]!=undefined){
          div[i].children[0].children[0].className="glyphicon glyphicon-menu-down"
        }
      }
    }
    div[i].children[1].style.display='none'
  }
  let td=div[index].children[0].className
  if(td.indexOf("firstactive")>=0){
    let tf=td.split("firstactive")
    div[index].children[0].className=tf[0]+tf[1]
    if(div[index].children[0].children[0]!=undefined){
      div[index].children[1].style.display='none'
      div[index].children[0].children[0].className="glyphicon glyphicon-menu-down"
    }
  }else{
    div[index].children[0].className+=" firstactive"
    if(div[index].children[0].children[0]!=undefined){
      div[index].children[0].children[0].className="glyphicon glyphicon-menu-up"
      div[index].children[1].style.display='block'
    }
  }
}
//二级点击事件
function treetwochange(ev,index,id,pId,name,type){
  let el=ev.className.split(" ")[0]
  if(!type){
    zNodeOnClick({id:id,pId:pId,name:name})
  }
  let div=ev.parentElement.parentNode.children
  for(let i=0;i<div.length;i++){
    let mc=""
      let td=div[i].children[0].className
      if(td.indexOf("boxactive")>=0){
        let tf=td.split("boxactive")
        mc=tf[0]+tf[1]
        div[i].children[0].className=mc
        if(div[i].children[0].children[0].localName!='span'){
          div[i].children[0].children[0].className="iconfont icon-xiala"
        }
      }
    
    div[i].children[1].style.display='none'
  }
  if(ev.className.indexOf("boxactive")>=0){
    let di=ev.nextElementSibling
    let tf=ev.className.split("boxactive")
    let text=tf[0]+tf[1]
    ev.className=text
    if(ev.children[0].className!=""){
      ev.children[0].className="iconfont icon-xiala"
      di.style.display='none'
    }
    return
  }
  if(ev.className.indexOf("boxli")>=0){
    let di=ev.nextElementSibling
    ev.className+=" boxactive"
    if(ev.children[0].className!=""){
      ev.children[0].className="iconfont icon-xiangshang"
      di.style.display='block'
    }
    return
  }
}
//树单机事件
function zNodeOnClick(obj) {
  $('#tjsqdmain').attr("src", `e-zytjsqdtx.html?ly=tjyz&av_yzmlid=${obj.id}&av_blid=${params['av_blid']}&av_bmid=${ysbmid}&av_zkid=${params['av_zkid']}&av_zyid=${params['av_zyid']}&av_zllx=${params['av_zllx']}&av_sqdstate=0&av_bqid=${params['av_bqid']}`)
}
//树单机事件
function zTreeOnClick(event, id, obj, index) {
  console.log(obj)
  //判断是否为叶目录
  if (obj.isParent == false) {
    $('#tjsqdmain').attr("src", `e-zytjsqdtx.html?ly=tjyz&av_yzmlid=${obj.id}&av_blid=${params['av_blid']}&av_bmid=${ysbmid}&av_zkid=${params['av_zkid']}&av_zyid=${params['av_zyid']}&av_zllx=${params['av_zllx']}&av_sqdstate=0&av_bqid=${params['av_bqid']}`)
  } else {
    var zTree = $.fn.zTree.getZTreeObj("zytjsqdyzmain_list_tree");
    zTree.expandNode(obj);
  }
}
//保存成功回调
function f_jumpok() {
  WRT_e.ui.hint({
    type: 'success',
    msg: '保存成功'
  })
  $('#tjsqdmain').attr("src", ``)
  //新增历史单
  WRT_e.api.zytjsqdyzmain.getInit({
    params: {
      ls_zllx: params['av_zllx'],
      ls_zyid: params['av_blid'],
      ll_blid: params['av_blid'],
      ll_zkid: params['av_zkid'],
      ll_bqid: params['av_bqid']
    },
    success(data) {
      if (data.Code == 1) {
        //历史申请单
        $("#zytjsqdyzmain_lssqd").html(
          new lssqd_View().init({
            data: data.Result.lssqd
          }).render().$el
        )
      }
    }
  })
}
// 搜索框
function txtKeySearch() {
  $.ajaxSetup({
    cache: false
  });
  $("#txtKeySearch").focus();
  var $popDiv = $("<div id='popDiv' style='display:none; border:1px solid black; background-color:white; font-family:Microsoft YaHei; padding-left:2px; width:400px; height:300px; overflow:auto; position:absolute;'></div>").appendTo("body");
  $("#txtKeySearch").keydown(function (e) {
    if (e.which == 13) {
      return false;
    }
  });
  $("#txtKeySearch").on("input propertychange", function () {
    $('#popDiv').css({
      top: $("#txtKeySearch").offset().top,
      left: $("#txtKeySearch").offset().left + $("#txtKeySearch").width() + 40
    })
    var strKw = $.trim($("#txtKeySearch").val());
    if (strKw.length <= 0) {
      $popDiv.hide();
      return;
    }
    $popDiv.show();
    setTimeout(function () {
      $.ajax({
        url: "/ehr/zyyz/TjsqdHandler.ashx?Method=GetKeySearch",
        dataType: "json",
        beforeSend: function () {
          // $("#popDiv").showLoading();
        },
        data: {
          kw: strKw,
          zllx: (getArgs()).av_zllx
        },
        success: function (data) {

          // $("#popDiv").hideLoading();
          $("#popDiv").html("");
          $("<div style='font-weight:bold; font-size:12px; margin-bottom:3px;'><span id='spanHintClose' style='cursor:pointer; width:16px; height:16px; display:inline-block; background-image:url(../images/cross-white.png); float:right;'></span>包含关键字“" + strKw + "”的特检申请单：</div>").appendTo($("#popDiv"));
          $("#spanHintClose").click(function () {
            $popDiv.hide();
            $("#txtKeySearch").val("").focus();
          });
          $.each(data, function (i, item) {
            $("<div class='resultitem' yzmlid='" + item.YZMLID + "' style='font-size:12px;margin-bottom:2px; cursor:pointer;'>" + item.MLPATH + "</div>").appendTo($("#popDiv"));
          });
          $("div#popDiv div.resultitem").hover(function () {
            $(this).css("background-color", "#c4e1ff");
          }, function () {
            $(this).css("background-color", "");
          }).click(function () {
            let yzmlid = $(this).attr("yzmlid")
            $("#popDiv").hide();
            $("#txtKeySearch").val("");
            $('#tjsqdmain').attr("src", `e-zytjsqdtx.html?ly=tjyz&av_yzmlid=${yzmlid}&av_blid=${params['av_blid']}&av_bmid=${ysbmid}&av_zkid=${params['av_zkid']}&av_zyid=${params['av_zyid']}&av_zllx=${params['av_zllx']}&av_sqdstate=0&av_bqid=${params['av_bqid']}`)
          });
        }
      });
    }, 10);
  });
}

function getArgs() {
  var args = {};
  var match = null;
  var search = decodeURIComponent(location.search.substring(1));
  var reg = /(?:([^&]+)=([^&]+))/g;
  while ((match = reg.exec(search)) !== null) {
    args[match[1]] = match[2];
  }
  return args;
}
/********************视图********************/
//设置历史申请单
var lssqd_View = WRT_e.view.extend({
  render: function () {
    console.log(zNodes)
    var html = `
		<div class="zytjsqdyzmain_lssqd">
      <div class="head">
        患者所有申请单
      </div>
      <div class="list">
        <table style="width: 100%;table-layout: fixed;">
          <tr>
            <th>申请单名称</th>
            <th width="80">申请日期</th>
            <th width="50">状态</th>
            <th width="35">操作</th>
            <th width="35">查看</th>
          </tr>
          ${_.map(this.data, obj=> 
            {
              return `
              <tr data-sqdsjid="${obj.SQDSJID}">
                <td><a class="name" href="javascript:;">${obj.TSSM }</a></td>
                <td>${(new Date(parseInt(obj.SCLRSJ.replace("/Date(", "").replace(")/", "")))).Format("yyyy-MM-dd")}</td>
                <td>
                ${function(){
                  let findObj =_.find([{value:0,label:'已暂停'},{value:1,label:'已收费'},{value:2,label:'部分收费'},{value:3,label:'已执行'},{value:9,label:'已退费'}],(e)=>e.value == obj.ZTBZ)
                  return (findObj?'<span class="e_tag_green">' + findObj.label + '</span>':`<span class="e_tag_red">未收费</span>`)
                }()}
                </td>
                <td><a class="e-btn-del" href="javascript:;">删除</a></td>
                <td><a class="e-btn-blue" href="${obj.BS}" target="_blank">闭环</a></td>
              </tr>`
            }).join('')}
        </table>
      </div>
    </div>`
    this.$el.html(html)
    return this;
  },
  events: {
    "click .zytjsqdyzmain_lssqd .name": "edit",
    "click .zytjsqdyzmain_lssqd .del": "del",
  },
  edit(event) { //编辑
    let obj = this.data.find(e => e.SQDSJID == $(event.currentTarget).parent().parent().data('sqdsjid'))
    let av_sqdstate = (obj.ZTBZ == null ? 1 : 2) //1可编辑 2不可修改
    $('#tjsqdmain').attr("src", `e-zytjsqdtx.html?ly=tjyz&av_yzmlid=${obj.MLID}&av_blid=${params['av_blid']}&av_sqdsjid=${obj.SQDSJID}&av_sqdstate=${av_sqdstate}&av_bmid=${ysbmid}&av_zkid=${params['av_zkid']}&av_zyid=${params['av_zyid']}&av_zllx=${params['av_zllx']}&av_bqid=${params['av_bqid']}`)
  },
  del(event) { //删除
    let that = this
    let sqdsjid = $(event.currentTarget).parent().parent().data('sqdsjid')
    WRT_e.ui.message({
      title: '删除操作',
      content: `确定要删除这条检查申请单医嘱吗？`,
      onOk() {
        WRT_e.api.zytjsqdyzmain.deleteSqd({
          params: {
            ll_blid: params['av_blid'],
            ll_sqdid: sqdsjid,
          },
          success(data) {
            if (data.Code == 1) {
              WRT_e.ui.hint({
                msg: '删除成功'
              })
              $('#tjsqdmain').attr("src", ``)
              that.data = that.data.filter(e => e.SQDSJID != sqdsjid)
              that.render()
            }
          }
        })
      },
      onCancel() {},
    })
  }
})