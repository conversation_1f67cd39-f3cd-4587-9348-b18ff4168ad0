var model=null;//病人选择
var model_index=null//序号
var modelDeail=null;//病人文书选择
let url = window.location.href.split("?") || []
let text = url[1].split("&")
let params = {}
for (let i of text) {
  let fd = i.split("=")
  params[fd[0]] = fd[1]
}
// console.log(params)
var obj1 = []
var lsList = []
var listGetSpList = []
// 统一页面启动
$(document).ready(() => {
  left_menu()
})
/********************初始化********************/
function left_menu(o={}){
  // model=null;
  // modelDeail=null;
  const start = new Date()
  const end=start.getTime() - 3600 * 1000 * 24 * 7
  $("#datetimebox1")[0].value=o.start||gettime(end)
  $("#datetimebox2")[0].value=o.end||gettime('')

  WRT_e.api.ycwsp.GetSpList({
    params:{
      as_bz:o.bz||'',
      as_kssj:o.start||gettime(end),//gettime(end)
      as_jssj:o.end||gettime('')
    },
    success(data){
      if(data){
        let lists=JSON.parse(data);
        WRT_config.SpList=lists;
        WRT_config.SpList_old_copy=lists
        let arr=[];
        lists.map(function(item){
          arr.push(item.BLID)
        })
        WRT_e.api.ycwsp.GetPatientInfo({
          params:{as_blids:arr.join(',')},
          success(data){
            if(data){
              let arr=JSON.parse(data)
              function unique(arr){            
                for(var i=0; i<arr.length; i++){
                    for(var j=i+1; j<arr.length; j++){
                        if(arr[i].BRXM==arr[j].BRXM){
                            arr.splice(j,1);
                            j--;
                        }
                    }
                }
                return arr;
              }
              
              WRT_config.PatientInfo=unique(arr)
              WRT_config.PatientInfo_copy=unique(arr)
              // console.log(WRT_config.PatientInfo)
              init()
            }
          }
        })
      }
    }
  })
}
function init() {
  //左侧
  var Menuleft = new Menuleft_View();
  Menuleft.$el = $("#ycwsp_lists");
  Menuleft.init({ data: WRT_config.PatientInfo }).render();  
}

var Menuleft_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    ${_.map(this.data,(obj,index)=>`
      <li onclick="dbselect(${index})"><label>${obj.ZKMC} ${obj.BRXM} ${obj.ZDYBQDM}-${obj.CWH}</label></li>
    `
    ).join('')}
    `)
    return this
  },
})


/********************公共方法***************** */
//查询
function e_search(){
  let start=$("#datetimebox1")[0].value
  let end=$("#datetimebox2")[0].value
  let bz=$(".select_bz option:selected")[0].value
  left_menu({start,end,bz})
  $("#deail_title").html('')
  $("#ycwspdeail_lists").html('')
  $(".blzqnr").html('');
}
//作废
function xz_zf(type){
  if(modelDeail){
    // WRT_e.api.ycwsp.ZF({
    //   params:{
    //     al_id:modelDeail.ID,
    //   },
    //   success(data){
    //     if(data == '1'){
    //       //if(data == '1'){
    //         // 作废成功 
    //       //  WRT_e.ui.message({
    //       //    title:'提示',
    //       //    content:`您好，
    //       //      ${lsList.ZKMC}，${lsList.ZDYBQDM}-${lsList.CWH}，${lsList.BRXM}，${params['as_blid']}
    //       //      患者的“伤病器官切除手术审批单”文书内容不规范【${data.as_zgly}】，
    //       //      请尽快登录电子病历系统完善该审批单内容后再发起，谢谢配合。  （医务处 558026）。
    //       //    `,
    //       //    onOk(){
    //             WRT_e.ui.hint({msg:'作废成功',type:'success'})
    //             left_menu()
    //       //    }
    //       //  })
    //       //}
    //       // WRT_e.ui.hint({msg:'作废成功',type:'success'})
    //       // left_menu()
    //     } else {
    //       WRT_e.ui.hint({msg:'作废失败',type:'success'})
    //     }c
    //   },
    //   catch (err){
    //     WRT_e.ui.hint({msg: err,type:'success'})
    //   }
    // })
    WRT_e.ui.model({
      id: 'if_zfModel',
      title: '作废要求',
      width: 450,
      content: `<div>作废的理由：<textarea id="zfly" name="text" style="width:350px;height:60px" /></div>`,
      iframe: false,
      onOk(model) {
        var zfly = $("#zfly")[0].value
        WRT_e.api.ycwsp.ZF({
          params:{
            al_id:modelDeail.SPJLID,
            as_zfly: zfly,
            al_yhid: params["al_yhid"],
          },
          success(data){
            if(data=='1'){
              WRT_e.ui.hint({msg:'作废成功',type:'success'})
              left_menu()
              model.iziModal('destroy') //关闭modal
            } else {
             WRT_e.ui.hint({msg:'作废失败',type:'success'})
            }
          },
          catch (err){
            WRT_e.ui.hint({msg:err,type:'error'})
          }
        })
      },
    })
    
  //   WRT_e.ui.message({
  //     title:'提示',
  //     content:`您好，
  //       ${lsList.ZKMC}，${lsList.ZDYBQDM}-${lsList.CWH}，${lsList.BRXM}，${params['as_blid']}
  //       患者的“伤病器官切除手术审批单”文书内容不规范【xxx】，
  //       请尽快登录电子病历系统完善该审批单内容后再发起，谢谢配合。  （医务处 558026）。
  //     `,
  //     onOk(){
  //     },
  //     onCancel(){

  //     }

  //   })
    
  }
}
//审批
// <button class="e_btn" onclick="xz_sp('0')">不通过</button>
// <button class="e_btn" onclick="xz_zf()">作废</button>
function db_spModel(){
  let temp=`
  <div>审批意见：<textarea id="td_cpyj" name="text" style="width:350px;height:60px" /></div>
  <button class="e_btn" onclick="xz_sp('1')">通过</button>
  `
  WRT_e.ui.model({
    id: 'if_spModel',
    title: '审批',
    width: 450,
    content: temp,
    iframe: false,
    // iframeURL: `${WRT_config.server}/blcx/xggrxx.aspx?cbfuncname=openwin_easyuiCb`
  })
}
//审批
function xz_sp(type){
  let td_cpyj=$("#td_cpyj")[0].value
  // 之前问了一下说是用e_GetSpList返回的字段获取ID和GSDM所以加了&& listGetSpList[0].ID == modelDeail.ID
  if(modelDeail && listGetSpList[0].ID == modelDeail.ID){
    WRT_e.api.ycwsp.Sp({
      params:{
        al_wsid:modelDeail.ID,
        al_blid:modelDeail.BLID,
        as_gsdm :modelDeail.GSDM,
        al_yhid:params["al_yhid"],
        as_spyj:td_cpyj,
        as_spzt:type,
      },
      success(data){
        if(data == '1'){
          WRT_e.ui.hint({msg:'审批成功',type:'success'})
          $("#if_spModel").iziModal('destroy')
          // left_menu()
          dbselect(model_index)
          modelDeail=null
        } else if(data == '0') {
          WRT_e.ui.hint({msg:'审批失败',type:'error'})
        }
      }
    })
  }
  else {
    if(listGetSpList[0]){
      WRT_e.api.ycwsp.Sp({
        params:{
          al_wsid:listGetSpList[0].ID,
          al_blid:listGetSpList[0].BLID,
          as_gsdm :listGetSpList[0].GSDM,
          al_yhid:params["al_yhid"],
          as_spyj:td_cpyj,
          as_spzt:type,
        },
        success(data){
          if(data == '1'){
            WRT_e.ui.hint({msg:'审批成功',type:'success'})
            $("#if_spModel").iziModal('destroy')
            // left_menu()
            dbselect(model_index)
            modelDeail=null
          } else if(data == '0') {
            WRT_e.ui.hint({msg:'审批失败',type:'error'})
          }
        }
      })
    }
  }
}

//选择
function dbselect(index){
  let list=WRT_config.PatientInfo[index];
  lsList = WRT_config.PatientInfo[index];
  listGetSpList = WRT_config.SpList.filter(item=> item.BLID == list.BLID)// 为了获取e_GetSpList返回的字段获取ID和GSDM用于审批
  if(list){
    WRT_e.api.ycwsp.GetXgWsList({
      params:{
        al_blid:list.BLID
      },
      success(data){
        if(data){
          let arr=JSON.parse(data);
          model=list
          model_index=index
          let array=[]
          WRT_config.SpList_old_copy.map(function(item){
            if(item.BLID==list.BLID){
              array.push(item)
            }
          })
          WRT_config.XgWsList=[...arr,...array]
          let temp=`${_.map(WRT_config.XgWsList,(obj,index)=>`
                <li onclick="dbselectdeail(${index})"><label>${obj.GSMC} ${obj.YSQM} ${gettime(obj.JLSJ,true)}</label></li>
              `
              ).join('')}`
          if(WRT_config.XgWsList.length==0){
            temp='<li><label>无数据</li>'
          }
          $("#ycwspdeail_lists").html(temp)
          let title=`${list.ZKMC} ${list.BRXM} ${list.ZDYBQDM}-${list.CWH}`
          $("#deail_title").html(title)
        }
      }
    })
  }
}

//选择详情
function dbselectdeail(index){
  let obj=WRT_config.XgWsList[index];
  // console.log('选择详情',WRT_config.XgWsList,obj)
  // obj.GDBZ="1"
  if(obj){
    if(obj.GDBZ=="1"){
      modelDeail=obj
      let url=`${WRT_config.server}/zyblws/zyblwsPdf.aspx?as_blid=${obj.BLID}&as_gsdm=${obj.GSDM}&as_zyid=${model.ZYID}&as_wsid=${obj.ID}&as_wslx=${obj.WSLX}&tmpid=${Math.random()}`
      let temp=` <iframe class="dynr" id="if_${obj.ID}" name="${obj.GSMC}" src="${url}" rameborder="0" width="100%" height="700px"></iframe>`;
      $(".blzqnr").html(temp);
    } else {
      modelDeail=obj
      let url=`${WRT_config.server}/zyblws/print/zyblwsSz.aspx?as_blid=${obj.BLID}&as_gsdm=${obj.GSDM}&as_zyid=${model.ZYID}&as_wsid=${obj.ID}&as_wslx=${obj.WSLX}&tmpid=${Math.random()}`
      let temp=` <iframe class="dynr" id="if_${obj.ID}" name="${obj.GSMC}" src="${url}" rameborder="0" width="100%" height="700px"></iframe>`;
      $(".blzqnr").html(temp);
    }
  }
}

//查找回车事件
function calAge() {
  var evt = window.event || e;
  if (evt.keyCode == 13) {
    left_init()
  }
}
//刷新
function left_init(){
  let search=$(".mc_search")[0].value
  let arr=[]
  if(search){
    let py=search.trim().toUpperCase()
    WRT_config.PatientInfo_copy.map(function(item){
      if((item.BRXM.indexOf(search)>=0)||item.ZKMC.indexOf(search)>=0){
        arr.push(item)
      }
    })
  }else{
    arr=WRT_config.PatientInfo_copy
  }
  WRT_config.PatientInfo=arr
  var Menuleft = new Menuleft_View();
  Menuleft.$el = $("#ycwsp_lists");
  Menuleft.init({ data: arr }).render();
}

//处理时间
function gettime(val,type){
  let d=null
  if(val){
    d = new Date(val);
  }else{
    d = new Date();
  }
  var year = d.getFullYear();
  var month = change(d.getMonth() + 1);
  var day = change(d.getDate());
  var hour = change(d.getHours());
  var minute = change(d.getMinutes());
  var second = change(d.getSeconds());
  function change(t) {
    if (t < 10) {
        return "0" + t;
    } else {
        return t;
    }
  }
  var time = year + '-' + month + '-' + day
  if(type){
    time=year + '-' + month + '-' + day+' '+hour+':'+minute+':'+second
  }
  return time;
}