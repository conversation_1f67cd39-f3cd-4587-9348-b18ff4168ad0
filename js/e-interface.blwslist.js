WRT_e.api = WRT_e.api || {}

WRT_e.api.blwslist = {
  // 4、(有)病程记录/入院记录初始化e_init
  getBlwslist: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: blwssinit
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_init',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 5、(有)根据页面类型获取病人已书写文书列表e_GetBrWssByLx
  getBrWssByLx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyls
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_GetBrWssByLx',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 6、(有)初始化专科病程录选项e_GetWslbItem
  getWslbItem: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_GetWslbItem',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 7、(有)获取格式名称e_getGsmc
  getGsmc: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_getGsmc',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 8、(有)新增文书_GetNewWsHtml
  getNewWsHtml: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_GetNewWsHtml',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //  9、(有)获取文书详情e_GetWsDetail
  getWsDetail: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_GetWsDetail',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 10、判断文书是否可以多份e_CheckGsdm
  GetCheckGsdm: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_CheckGsdm',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 11、(有)获取文书类型e_GetWslx
  GetWslx: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_GetWslx',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 12、(有)获取危急值列表e_GetWjzLst
  GetWjzLst: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_GetWjzLst',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 13、(有)获取病人辅助状态（出院时间）e_CheckFjzt
  GetCheckFjzt: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_CheckFjzt',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 14、(有)获取病人改良Rankin量表(mRS)评分情况<请求参数不同>e_CheckFjzt
  GetCheckFjztmRS: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_CheckFjzt',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        async: false,
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 14、(有)获取病人改良Rankin量表(mRS)评分情况<请求参数不同>e_ChkmRs
  ChkmRs: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_ChkmRs',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        async: false,
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 15、(有)获取病人神经功能缺损评分表(NIHSS)评分情况e_ChkNIHSS
  GetChkNIHSS: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        // Result: brwssbyitem
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_ChkNIHSS',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        async: false,
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 16、(有)获取病人妊娠期及产褥期静脉血栓血塞证(VTE)的危险因素评分情况e_CkVte
  GetCkVte: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_CkVte_CKVTE',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        async: false,
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 17.1、(有)获取内或外科住院患者静脉血栓栓塞症的风险评估(VTE)评分情况e_CkNWkVte
  GetCkNWkVte: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_CkNWkVte',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        async: false,
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 17.1、(有)获取完成PHQ-4量表后才可书写首次病程记录e_CheckPfb_pfid(废除)
  // CheckPfb_pfid: function(o = {}) {
  //   o.params = o.params || {}
  //   if (WRT_config.mockType == '1') {
  //     if (o.success) o.success({
  //       Code: "1",
  //       CodeMsg: "获取数据成功！",
  //       Result: []
  //     })
  //   } else {
  //     $.ajax({
  //       url: WRT_config.server + '/zyblws/blwslist.aspx/e_CheckPfb_pfid',
  //       type: 'POST',
  //       data: JSON.stringify(o.params),
  //       dataType: "json",
  //       async: false,
  //       cache:false,
  //       crossDomain:true, //设置跨域为true
  //       xhrFields: {
  //         withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
  //       },
  //       contentType: "application/json; charset=utf-8",
  //       success: function (msg) {
  //         if (o.success) o.success(msg.d)
  //       },
  //       error: function (error) {
  //         if (error.statusText == 'error') {
  //           WRT_e.ui.hint({
  //             type: 'error',
  //             msg: "服务器连接失败"
  //           })
  //         }
  //       }
  //     })
  //   }
  // },
  // PHQ量表不填不能新增首程
  CheckPfb_pfid_phq: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_CheckPfb_pfid_phq',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        async: false,
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 17.2、(有)ICU患者填写出院记录需填ICU质量控制登记表e_ICUCheck
  GetICUCheck: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/blwslist.aspx/e_ICUCheck',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        async: false,
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 修改个人信息
  GetBrMainEinit: function(o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/BrMain.aspx/e_init',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 初始化接口增加返回参数 VTECS  wslx == 11
  GetVTEJsonId: function(o = {}) {
    o.params = o.params || {}
    console.log(params["as_blid"],JSON.stringify(params["as_blid"]))
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/ashx/CommonService.ashx?Method=getVTEJsonId&as_blid=' + params["as_blid"] +'&as_wslx=11',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        async: false,
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
    // 初始化接口增加返回参数 VTECS wslx == 15
    GetVTEJsonId2: function(o = {}) {
      o.params = o.params || {}
      if (WRT_config.mockType == '1') {
        if (o.success) o.success({
          Code: "1",
          CodeMsg: "获取数据成功！",
          Result: []
        })
      } else {
        $.ajax({
          url: WRT_config.server + '/ashx/CommonService.ashx?Method=getVTEJsonId&as_blid=' + params["as_blid"] +'&as_wslx=15',
          type: 'POST',
          data: JSON.stringify(o.params),
          dataType: "json",
          async: false,
          cache:false,
          crossDomain:true, //设置跨域为true
          xhrFields: {
            withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
          },
          contentType: "application/json; charset=utf-8",
          success: function (msg) {
            if (o.success) o.success(msg)
          },
          error: function (error) {
            if (error.statusText == 'error') {
              WRT_e.ui.hint({
                type: 'error',
                msg: "服务器连接失败"
              })
            }
          }
        })
      }
    },
}