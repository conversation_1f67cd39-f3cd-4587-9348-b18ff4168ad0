
//统一页面启动
$(document).ready(() => {
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  let params = {}
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  WRT_config.url = params
  //获取获取全部专科信息或当前用户权限范围内的专科信息
  WRT_e.api.ehrSz.GetAllZkxx({
    params: {},
    success(data) {
      if (data.Code == 1) {
        let lists = JSON.parse(data.Result)
        WRT_config.AllZkxx = lists.ZKXX || []
        //初始化
        app.init()

      }
    }
  })
})
//
var app = {
  init: function () {
    $("#head_info").html(
      new head_View().init({
        data:[]
      }).render().$el
    )
    
  }
}

var head_View = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
      <div class="head_inner">
        <label><span>专科:</span><select id="zkid" class="select">
            <option></option>
            ${_.map(WRT_config.AllZkxx,obj=>`<option value="${obj.KEY}" ${WRT_config.url.as_zkid == obj.KEY ? "selected='selected'" : ""}>${obj.VAL}</option>`).join('')}
          </select></label>
        <label><span>住院号:</span><input type="text" class="text" /></label></br>
        <label><span>入院开始日期:</span><input type="text" id="datetimebox" class="Wdate" value="" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',readOnly:false})" autocomplete="off" tag="0" /></label>
        <label><span>入院结束日期:</span><input type="text" id="datetimebox1" class="Wdate" value="" onfocus="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',readOnly:false})" autocomplete="off" tag="0" /></label></br>
        <label>
          <span>是否在院:</span>
          <select id="ztbz" class="select">
            <option></option>
            <option value="1">是</option>
            <option value="0">否</option>
          </select>
        </label></br>
        <button class="e_btn_primary" onclick="onSeach()">查询</button>
      </div>
    `)
    return this
  },
  events: {
  },
})

/********************公用方法********************/
function onSeach(){
  let zkid=$("#zkid option:selected").val()
  let zyh=$(".text")[0].value||""
  let start=$("#datetimebox")[0].value||""
  let end=$("#datetimebox1")[0].value||""
  let ztbz=$("#ztbz option:selected").val()
  //血糖列表
  WRT_e.api.ehrSz.GetXTGLInfo({
    params:{
      as_sfzk:zkid?'true':'false',
      as_sfzy:ztbz?'true':'false',
      as_sfzyh:zyh?'true':'false',
      as_zkid:zkid,
      tb_zyh:zyh,//1845214
      as_kssj:start,
      as_jssj:end
    },
    success(data){
      if(data.Message){
        let temp=`${_.map(data.Message,obj=>`
          <tr style="line-height: 30px;">
            <td>${obj.zkmc}</td>
            <td>${obj.brxm}</td>
            <td>${obj.ZYH}</td>
            <td>${obj.xb}</td>
            <td>${obj.csrq}</td>
            <td>${obj.bqmc}</td>
            <td>${obj.CWH}</td>
            <td>${obj.rysj}</td>
            <td>${obj.ryzd}</td>
          </tr>
        `).join('')}`
        $("#xtgl_data").html(temp)
      }
    }
  })
}