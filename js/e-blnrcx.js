//统一页面启动
$(document).ready(() => {
  app.init()
})
  //
var app = {
  init: function () {
    //获取关键字段数据
    WRT_e.api.blnrcx.getinit({
      success(data) {
        if (true) {
          // .append()是为指定元素尾部附加内容, 而.html()函数是重置元素内部的html内容。
          $("#container-blnrcx").append(
            new chooseTable_View().init({
              data: blnrcx
            }).render().$el
          )
        }
      }
    })
  }
}
// 病历内容查询病历表格
var chooseTable_View = WRT_e.view.extend({
  render:function(){
    let html =`
    <div class="table-header">
      <span>按病历内容查询病历</span>
    </div>
    <div id="content-belw" class="content-belw">
      <from class="input_list" action="" method="post">
        <div class="table_frame">
          <table border="1" bordercolor="#B7C3DA" class="table_content" id="table_content">
            <thead>
              <tr class="row_headings">
                <th class="keyword">查询关键字</th>
                <th class="equalion">等式</th>
                <th class="queries">查询内容值</th>
                <th class="remmark">备注</th>
              </tr>
            </thead>
            <tbody id="blnrTable">
              <tr class="column_tag">
                <td>
                  <select name="please_choose" id="please_choose" class="zplease_choose">
                    <option value=" " selected>请选择</option>
                    ${_.map(this.data['GJZXX'], (obj)=> `<option value="${obj.mc}" id="${obj.jglx}">${obj.mc}</option>`).join('')}
                  </select>
                </td>
                <td>
                  <select name="equality" id="equality">
                    <option value="=" selected>等于</option>
                    <option value="left">左包含</option>
                    <option value="right">右包含</option>
                    <option value="like">包含</option>
                  </select>
                </td>
                <td class="threeColum" id="threeColum"></td>
                <td>and</td>
              </tr>
              <tr class="column_tag">
                <td>
                  <select name="please_choose" id="please_choose1" class="zplease_choose">
                    <option value=" " selected>请选择</option>
                    ${_.map(this.data['GJZXX'], (obj)=> `<option value="${obj.mc}" id="${obj.jglx}">${obj.mc}</option>`).join('')}
                  </select>
                </td>
                <td>
                  <select name="equality" id="equality1">
                    <option value="=" selected>等于</option>
                    <option value="left">左包含</option>
                    <option value="right">右包含</option>
                    <option value="like">包含</option>
                  </select>
                </td>
                <td class="threeColum1" id="threeColum"></td>
                <td>and</td>
              </tr>
              <tr class="column_tag">
                <td>
                  <select name="please_choose" id="please_choose2" class="zplease_choose">
                    <option value=" " selected>请选择</option>
                    ${_.map(this.data['GJZXX'], (obj)=> `<option value="${obj.mc}" id="${obj.jglx}">${obj.mc}</option>`).join('')}
                  </select>
                </td>
                <td>
                  <select name="equality" id="equality2">
                    <option value="=" selected>等于</option>
                    <option value="left">左包含</option>
                    <option value="right">右包含</option>
                    <option value="like">包含</option>
                  </select>
                </td>
                <td class="threeColum2" id="threeColum"></td>
                <td>and</td>
              </tr>
              <tr class="column_tag">
                <td>
                  <select name="please_choose" id="please_choose3" class="zplease_choose">
                    <option value=" " selected>请选择</option>
                    ${_.map(this.data['GJZXX'], (obj)=> `<option value="${obj.mc}" id="${obj.jglx}">${obj.mc}</option>`).join('')}
                  </select>
                </td>
                <td>
                  <select name="equality" id="equality3">
                    <option value="=" selected>等于</option>
                    <option value="left">左包含</option>
                    <option value="right">右包含</option>
                    <option value="like">包含</option>
                  </select>
                </td>
                <td class="threeColum3" id="threeColum"></td>
                <td>and</td>
              </tr>
            <tbody id="myTable">
          </table>
        </div>
        <div class="message-option">
          <span>入院日期：</span>
          <input id="dt" class="dt" type="text" name="birthday">
          <span>——</span>
          <input class="appear" id="appear" type="text" class="easyui-datebox" required="required">
          <span>所属专科：</span>
          <select class="departmentList" name="departmentList"  id="departmentList">
            <option value ="-1">全院</option>
            ${_.map(this.data['ZKXX'], (obj)=> `<option value="${obj.TXT}"  id="${obj.VAL}">${obj.TXT}</option>`).join('')}
          </select>
        </div>
      </from>
      
      <div id="inquire-btn">
        <button id="search-btn" type="submit" name="submit"><span>查询</span></button>
      </div>
      <div class="reslutData"></div>
    </div>`
    this.$el.html(html);
    return this
  },
  events:{
    "change #please_choose":"chg",
    "change #please_choose1":"chg1",
    "change #please_choose2":"chg2",
    "change #please_choose3":"chg3",
    "click #dt":"enterData",
    "click #appear":"appearData",
    "click #search-btn":"Search",
  },
  // 切换不同的输入框
  chg:function(){
    var obj = document.getElementById("please_choose"); 
    var val = obj.options[obj.selectedIndex].id
    // 获取不到上面的this.value
    if (val !=''){
      if((val =="YD") || (val =="TB")){
        $(".threeColum").html(`<textarea type="text" name="input_box" id="input_box"></textarea>`)
      }else {
        $(".threeColum").html(`<input type="text" name="input_field" id="input_box">`)
      }
    }else{
      $(".threeColum").html(``)
    }
  },
  chg1:function(){
    var obj = document.getElementById("please_choose1"); 
    var val = obj.options[obj.selectedIndex].id
    // 获取不到上面的this.value
    if (val !=''){
      if((val =="YD") || (val =="TB")){
        $(".threeColum1").html(`<textarea type="text" name="input_box" id="input_box1"></textarea>`)
      }else {
        $(".threeColum1").html(`<input type="text" name="input_field" id="input_box1">`)
      }
    }else{
      $(".threeColum1").html(``)
    }
  },
  chg2:function(){
    var obj = document.getElementById("please_choose2"); 
    var val = obj.options[obj.selectedIndex].id
    // 获取不到上面的this.value
    if (val !=''){
      if((val =="YD") || (val =="TB")){
        $(".threeColum2").html(`<textarea type="text" name="input_box" id="input_box2"></textarea>`)
      }else {
        $(".threeColum2").html(`<input type="text" name="input_field" id="input_box2">`)
      }
    }else{
      $(".threeColum2").html(``)
    }
  },
  chg3:function(){
    var obj = document.getElementById("please_choose3"); 
    var val = obj.options[obj.selectedIndex].id
    // 获取不到上面的this.value
    if (val !=''){
      if((val =="YD") || (val =="TB")){
        $(".threeColum3").html(`<textarea type="text" name="input_box" id="input_box3"></textarea>`)
      }else {
        $(".threeColum3").html(`<input type="text" name="input_field" id="input_box3">`)
      }
    }else{
      $(".threeColum3").html(``)
    }
  },
  enterData(){
    // $("#enter").css("position","relative","width", "8.8%","height", "32","display", "inline")
    $('#dt').datebox({
      required:true,
    });
    // $('#enter').combo({
    //   panelWidth:182,
    // })
  },
  appearData(){
    $('#appear').datebox({
      required:true,
    });
    $('#appear').combo({
      panelWidth:182,
    })
  },
  // 搜索
  Search:function(){ 
		//	初始化二维数组
    WRT_e.api.blnrcx.getSearch({
      success(data) {
        if (data.Code == 1) {
          $(".reslutData").html(
            new choose_View().init({
              data: blnrcx['SearchResult']
            }).render().$el
          )
        }
      },
    })
  }
})
var choose_View = WRT_e.view.extend ({
  render:function(){
    this.$el.html(`
    <div id="Object_list" class="Object_list">
      <table border="2" cellpadding="100px" bordercolor="#B7C3DA" class="table_searchContent">
        <tr class="search_table">
          <th>住院ID</th>
          <th>病例ID</th>
          <th>住院号</th>
          <th>姓名</th>
          <th>性别</th>
          <th>现存放病区名称</th>
          <th>床位号</th>
          <th>专科代码</th>
          <th>专科名称</th>
          <th>入院诊断</th>
          <th>出生日期</th>
          <th>财务入院时间</th>
          <th>财务出院时间</th>
          <th>状态标识</th>
          <th>门诊号</th>
          <th>是否是新版病人</th>
          <th>病人类型</th>
        </tr>
        ${_.map(blnrcx['SearchResult'][0].Result, (obj)=>`
        <tr class="search_content">
          <td value="${obj.IDB}">${obj.IDB}</td>
          <td value="${obj.BLID}">${obj.BLID}</td>
          <td value="${obj.BLID}">${obj.ZYH}</td>
          <td value="${obj.XM}">${obj.XM}</td>
          <td value="${obj.XB}">${obj.XB}</td>
          <td value="${obj.BQDM}">${obj.BQDM}</td>
          <td value="${obj.CWH}">${obj.CWH}</td>
          <td value="${obj.ZKDM}">${obj.ZKDM}</td>
          <td value="${obj.ZKMC}">${obj.ZKMC}</td>
          <td value="${obj.RYZD}">${obj.RYZD}</td>
          <td value="${obj.CSRQ}">${obj.CSRQ}</td>
          <td value="${obj.RYRQ}">${obj.RYRQ}</td>
          <td value="${obj.CYRQ}">${obj.CYRQ}</td>
          <td value="${obj.ZTBZ}">${obj.ZTBZ}</td>
          <td value="${obj.MZH}">${obj.MZH}</td>
          <td value="${obj.ISNEW}">${obj.ISNEW}</td>
          <td value="${obj.BRLX}">${obj.BRLX}</td>
        </tr>`).join('')}
      </table>
    </div>`);
    return this
  }
})