
let url = window.location.href.split("?") || []
let text = url[1].split("&")
let param = {}
for (let i of text) {
  let fd = i.split("=")
  param[fd[0]] = fd[1]
}
/********公共参数*********/
var xtzArr = []
var xAxisData = [] //  获取x轴数据 
var xtzData = [] // 获取血糖值数据   
var speData = [] // 获取7.8数据定值
var tpnData = [] // 获取3.9数据定值
var yzdid // 医嘱单id
// var ypspList = [] // 
var mbindex=0 //
var dldr_lists=[] //导入队列
var yyyznr_lists=[] // （用于拼接完整药品数据）
var spxxList = []
var modalopen = 0 // 弹窗是都关闭
var showSave = -1
//统一页面启动(儿科营养医嘱)
$(document).ready(() => {
	// 初始化 儿科营养医嘱数据初始化 getInit
  WRT_e.api.pnOrderList.getInit({
    params: {
      ll_blid: param['as_blid'],
    },
    success(data) {
      if (data.d.Code == 1) {
      	WRT_config.nbPNC_init=JSON.parse(data.d.Result)
      	app.init()
      }
    }
  })
})

var app = {
	init: function () {
		// 基础数据
		var BaseInfo = new BaseInfo_View();
		BaseInfo.$el = $(".nbPNBaseInfo");
		BaseInfo.init({
		  data: WRT_config.nbPNC_init
		}).render();
    
    // 计算器列表
		var CountList = new CountList_View();
		CountList.$el = $(".nbPNCList");
		CountList.init({
		  data: {
        nbPNCInfo: WRT_config.nbPNC_init, // 基础信息（身高体重）

      }
		}).render();

		// 相关检查项表格
		var TableShowInfo = new TableShowInfo_View();
		TableShowInfo.$el = $(".nbPNTableContect");
		// TableShowInfo.$el = $(".nbPNTableAndChart");
		TableShowInfo.init({
		  data: {
        nbPNCInfo: WRT_config.nbPNC_init //血糖标准天
      }
		}).render();
    setTimeout(() => {
      pnNowChart()
      // initData()
    },2000)
  }
}
$(document).bind("click", function (e) {
  if(showSave == 1) {
    if($('.e_message').iziModal('getState') =='opened') {
      modalopen = 1
    }
  }
})
/********** 方法回调 **********/

//药品审批
function SetSpjg(data) {
  let index = mbindex-1
  if(index<0){
      index=0
  }
  let yemp='ypsp'
  if(WRT_config.ypfjxx.xzfw_bbzt==1){
      yemp='if_ypsp'
  }
  if (data) {
    if (data.sfzf == '9') {
      $(`#${yemp}`).iziModal('destroy')
      removeYp(dldr_lists[index].ZH,dldr_lists[index].MBMC)
      return
    }
    // WRT_config.nOList_Init.zykt  zykt的值，所有人都写1
    let type = setFylx(WRT_config.nbPNC_init['结算类型'], 1, data.sfzf, dldr_lists[index].KZJB, data.rowIndex)
    dldr_lists[index].ZF = type
    dldr_lists[index].SFZF = data.sfzf
    dldr_lists[index].splb = data.splb
    dldr_lists[index].xdfw = data.xdfw
    // dldr_lists[index].spsl = data.spsl||""
    dldr_lists[index].sqsl = data.spsl|| data.sqsl || ""
    exportMBList()
    $(`#${yemp}`).iziModal('destroy')
  } else {
    removeYp(dldr_lists[index].ZH,dldr_lists[index].MBMC)
    $(`#${yemp}`).iziModal('destroy')
    return
  }
}

/********************公用方法********************/

var medicalData;
var ypInfo;

//判断字符串是否为空
function stringISEmpty(str) {
  if (null == str || undefined == str || str == '' || (str == 0 || str == '0')) {
  // if (null == str || undefined == str || str == '' ) {
    return true;
  }
  return false;
}
function getTodayTime() {
  var date = new Date();
  var month = date.getMonth() + 1;
  var day = date.getDate();
  var hour = date.getHours();
  var min = date.getMinutes();
  var second = date.getSeconds();
  var today = date.getFullYear() + '-' + (month >= 10 ? month : '0' + month) + '-' + (day >= 10 ? day : '0' + day) + " " + (hour >= 10 ? hour : '0' + hour) + ":" + (min >= 10 ? min : '0' + min) + ":" + (second >= 10 ? second : '0' + second);
  return today;
}
// initData初始化一些数据（已经写入CountList_View中的getDateList）

//设置每次奶量 （保留：CountList_View中的events中的change方法有用到）
function setMilkNum1() {
  if (stringISEmpty($("#typeOfMilk1").val()) || stringISEmpty($("#typeOfMilk").val()) || stringISEmpty($("#mealOfMilk").val())) {
      $("#milkNum1").val("");
      return;
  }
  $("#milkNum1").val($("#milkNum").val());
}
//  typeCallBack(res) 初始化奶种类下拉框(已经通过接口写在CountList_View的getDateList()里了)

//计算奶能量（保留：setTotalEnergy()方法中有用到，setTotalEnergy()作用于calculate()）
function setMilkEnergy(typeOfMilk,mealOfMilk,milkNum,typeOfMilk1,mealOfMilk1,milkNum1) {
  //奶品种相应单位能量X每次用量X0.01X顿数stringISEmpty()
  //混合喂养，两者相加
  
  if ((stringISEmpty($("#typeOfMilk").val()) && stringISEmpty(typeOfMilk)) || 
    (stringISEmpty($("#mealOfMilk").val()) && stringISEmpty(mealOfMilk)) || 
    (stringISEmpty($("#milkNum").val()) && stringISEmpty(milkNum))
  ) {
      $("#milkEnergy").val(0);
      return 0;
  }
  var value;
  var value1;
  value = parseFloat(($("#typeOfMilk").val() || typeOfMilk)) * parseFloat(($("#milkNum").val() || milkNum)) * 0.01 * parseFloat(($("#mealOfMilk").val() || mealOfMilk));
  value1 = 0;
  
  if (!stringISEmpty($("#typeOfMilk1").val()) || !stringISEmpty(typeOfMilk1)) {
    value1 = parseFloat(($("#typeOfMilk1").val() || typeOfMilk1)) * parseFloat(($("#milkNum1").val() || milkNum1)) * 0.01 * parseFloat(($("#mealOfMilk1").val() || mealOfMilk1));
  }

  value = value + value1;
  // 这里和原来的取值都不是 value = value + value1;，只是 value
  $("#milkEnergy").val(Math.round(value * 10) / 10)
  
  return value;
}
//计算第二个顿数（保留：CountList_View中的events中的change方法有用到）
function setMealOfmilk1() {
  if (stringISEmpty($("#typeOfMilk1").val()) || stringISEmpty($("#typeOfMilk").val()) || stringISEmpty($("#mealOfMilk").val()) || stringISEmpty($("#milkNum").val())) {
      $("#mealOfMilk1").val("");
      return;
  }
  var timesToEat = parseFloat($("#timesToEat").val());
  var mealOfMilk = parseFloat($("#mealOfMilk").val());
  $("#mealOfMilk1").val(24 / timesToEat - mealOfMilk);
}

//设置糖浓度
function setText_TND() {
  //PN糖速X体重X60X24÷1000÷PN液量X100
  var sum = parseFloat($("#pnTS").val()) * parseFloat($("#weight").val()) * 60 * 24 / 1000 / parseFloat($("#PnFluidV").val()) * 100;
  if (sum >= 25) {
      // $.messager.alert("提示", "糖浓度要小于25%", "info");
      WRT_e.ui.message({
        title: '提示',
        content: `糖浓度要小于25%`,
        onOk() {
        },
      })
      $("#text_tnd").val("");
      return false;
  }
  $("#text_tnd").val(Math.round(sum * 10) / 10);
  return true;

}

//设置泵速
function setPNpumpSpeed() {
  //PN液量÷PN时间
  if (stringISEmpty($("#PnFluidV").val()) || stringISEmpty($("#pnTime").val())) {
    $("#PNpumpSpeed").val("");
  }
  else {
    var pnFluidV = parseFloat($("#PnFluidV").val());
    var sum = pnFluidV / parseFloat($("#pnTime").val());
    $("#PNpumpSpeed").val(Math.round(sum * 10) / 10);
  }
}

//设置Pn液量（getList1BodyHtml初始化已经加了，保留：events变化方法中会调用）
function setPnFluidV() {
  //体重X全天总液量-治疗液量-两种奶液体总量
  if (
    stringISEmpty($("#treatFluid").val()) || 
    stringISEmpty($("#allDayFluidV").val()) || 
    stringISEmpty($("#weight").val()) || 
    stringISEmpty($("#weight").val()) || 
    (!stringISEmpty($("#typeOfMilk").val()) && 
      (stringISEmpty($("#milkNum").val()) || stringISEmpty($("#mealOfMilk").val()))
    )
  ) {
    $("#PnFluidV").val("");
    return;
  }

  var weight = parseFloat($("#weight").val());
  var allDay = parseFloat($("#allDayFluidV").val());
  var treatFluid = parseFloat($("#treatFluid").val());
  var milk = 0;
  if (!stringISEmpty($("#typeOfMilk").val())) {
    milk += parseFloat($("#milkNum").val()) * parseFloat($("#mealOfMilk").val());
    if (!stringISEmpty($("#typeOfMilk1").val())) {
        milk += parseFloat($("#milkNum1").val()) * parseFloat($("#mealOfMilk1").val());
    }
  }

  var sum = weight * allDay - treatFluid - milk;
  sum = Math.round(sum);
  
  $("#PnFluidV").val(sum);
  // var sumAll = sum * parseInt($("#capacityCoe").val()*10)/10
  $("#realFluidV").val(sum * parseInt($("#capacityCoe").val()*10)/10);
}

//设置一价离子浓度
function setMonovalent() {
  //[(体重XPN钠量)+(体重XPN钾量)]÷ PN液量X1000
  var Na = 0;
  if (!stringISEmpty($("#NaInPN").val())) {
    Na = parseFloat($("#NaInPN").val());
  }
  var K = 0;
  if (!stringISEmpty($("#KInPN").val())) {
    K = parseFloat($("#KInPN").val());
  }
  var weight = parseFloat($("#weight").val());
  var pnFluid = parseFloat($("#PnFluidV").val());
  var value = weight * (Na + K) / pnFluid * 1000;
  if (value >= 150) {
    WRT_e.ui.message({
      title: '提示',
      content: `一价离子浓度必须小于150`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }

  $("#monovalent").val(Math.round(value * 10) / 10);
  return true;
}

//设置二价离子浓度
function setDivalent() {
  //[(体重XPN钙量)+(体重XPN镁量)]÷ PN液量X1000
  var Mg = 0;
  if (!stringISEmpty($("#MgInPN").val())) {
      Mg = parseFloat($("#MgInPN").val());
  }
  /*  var Ca = 0;
    if (!stringISEmpty($("#CaInPN").val())) {
        Ca = parseFloat($("#CaInPN").val());
    }
    parseFloat($("#CaInPN").val());*/
  var pnFluid = parseFloat($("#PnFluidV").val());
  var weight = parseFloat($("#weight").val());
  var value = weight * Mg / pnFluid * 1000;
  if (value >= 10) {
    WRT_e.ui.message({
      title: '提示',
      content: `二价离子浓度必须小于10`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
}

  $("#divalent").val(Math.round(value * 10) / 10);
  return true;
}

// getNutrition() 得到营养注射的药品（已经写入CountList_View中的getDateList）
// getNutCallBack(res)已经写入数据（已经写入CountList_View中的getList2BodyInnerHtml中）
//得到元素用量设置(已经写入CountList_View中的getList1BodyHtml)

//求糖能量   AA能量 Fat能量 氨基酸浓度 脂肪乳浓度 PN热氮比
function setSAFEnergyAndND() {

  /* var input_index = $("[name='input_index']");
   var input_type = $("input[name='input_type']");
   var input_num = $("input[name='input_num']");*/
  var SugarValue = 0;
  var AAValue = 0;
  var FATValue = 0;
  var pnTS = parseFloat($("#pnTS").val())

  var pIndex = 0;
  /* for (var i = 0 ; i < input_type.length; i++) {
    //0糖1维生素2氨基酸AJS3脂肪4Na5K6Ca7P8Mg
    switch (input_type.eq(i).val()) {
        case '0':
          //求糖
          pIndex = parseInt(input_index.eq(i).val());
          //糖质量=体积乘以浓度
          SugarValue += parseFloat(input_num.eq(i).val()) * parseFloat(ypInfo[pIndex].YPND) * 0.04;
          break;  
        case '2':
          pIndex = parseInt(input_index.eq(i).val());
          var value = parseFloat(input_num.eq(i).val()) * parseFloat(ypInfo[pIndex].YPND);
          AAValue += value * 0.04;
          break;
        case '3':
          pIndex = parseInt($("#select_ZFYP").val());
          var value = parseFloat(input_num.eq(i).val()) * parseFloat(ypInfo[pIndex].YPND);
          FATValue += value * 0.09;
          break;
          default: break;
      }
  }*/

  if (parseFloat($("#PnFluidV").val()) == 0) {
    $("#AAND").val("");
    $("#FATND").val("");
  } else {
    var weight = parseFloat($("#weight").val());
    var PnFluidV = parseFloat($("#PnFluidV").val());
    var aand = 0;
    //氨基酸量
    var ajsl = parseFloat($("#aminoAcid").val());
    //脂肪乳
    var zfrl = parseFloat($("#lipidEmulsion").val());
    //氨基酸量X体重÷PN液量X100%
    aand = Math.round(ajsl * weight / PnFluidV * 1000) / 10;
    //脂肪乳量X体重÷PN液量X100%
    var fatnd = Math.round(zfrl * weight / PnFluidV * 1000) / 10;
    if (zfrl > 0) {
      if (aand >= 2) {
        $("#AAND").val(aand);
      }
      else {
        WRT_e.ui.message({
          title: '提示',
          content: `氨基酸浓度要大于2%!`,
          onOk() {
          },
        })
        // $.messager.alert("提示", "", "info");
        $("#AAND").val("");
        return false;
      }
      if (aand < 3.5) {
        $("#AAND").val(aand);
      }
      else {
        WRT_e.ui.message({
          title: '提示',
          content: `氨基酸浓度要小于3.5%!`,
          onOk() {
          },
        })
        // $.messager.alert("提示", "", "info");
        $("#AAND").val("");
        return false;
      }
      if (fatnd >= 1)
        $("#FATND").val(fatnd);
      else {
        WRT_e.ui.message({
          title: '提示',
          content: `脂肪浓度要大于1%!`,
          onOk() {
          },
        })
        // $.messager.alert("提示", "", "info");
        $("#FATND").val("");
        return false;
      }
    }
    else {
      $("#AAND").val(aand);
      $("#FATND").val(fatnd);
    }
  }

  if (parseFloat($("#pnEnergy").val()) == 0) {
    $("#sugarEnergy").val("0%");
    $("#aaEnergy").val("0");
    $("#fatEnergy").val("0%");
  }
  else {
    var pnEnergy = parseFloat($("#pnEnergy").val());
    SugarValue = pnTS * weight * 60 * 24 / 1000 * 4;
    FATValue = parseFloat($("#lipidEmulsion").val()) * weight * 9;
    var pnRDB = Math.round((SugarValue + FATValue) / weight * 6.25);
    SugarValue = SugarValue / pnEnergy;
    //糖质量X4÷PN能量*100%
    SugarValue = Math.round(SugarValue * 100);
    $("#sugarEnergy").val(SugarValue + "%");

    /* AAValue = parseFloat($("#aminoAcid").val()) * weight * 4;*/
    // AAValue = AAValue / pnEnergy;
    //氨基酸质量X4÷PN能量*100%
    // AAValue = Math.round(AAValue * 100);
    $("#aaEnergy").val(ajsl);
    FATValue = FATValue / pnEnergy;
    //脂肪质量X9÷PN能量*100%
    FATValue = Math.round(FATValue * 100);
    $("#fatEnergy").val(FATValue + "%");
    $("#sugarAndfat").val(Math.round(100 * SugarValue / (SugarValue + FATValue)) + ":" + Math.round(100 * FATValue / (SugarValue + FATValue)));

    $("#pnRDB").val(pnRDB.toString() + ":1");
  }
  return true;
}
//设置pn能量
function setPnEnergy() {
  /* var input_index = $("[name='input_index']");
   var input_type = $("input[name='input_type']");
   var input_num = $("input[name='input_num']");*/
  var sum = 0;
  var pIndex = 0;
  var weight = parseFloat($("#weight").val());

  sum += parseFloat($("#pnTS").val()) * weight * 60 * 24 / 1000 * 4;
  sum += parseFloat($("#aminoAcid").val()) * weight * 4;
  sum += parseFloat($("#lipidEmulsion").val()) * weight * 9;
  /* for (var i = 0 ; i < input_type.length; i++) {
       //0 糖1 维生素2氨基酸AJS3脂肪4Na5K6Ca7P8Mg
       switch (input_type.eq(i).val()) {
           case '0':
               //求糖能量
               pIndex = parseInt(input_index.eq(i).val());
               sum += input_num.eq(i).val() * ypInfo[pIndex].YPND * 0.04;
               break;
           case '2'://求AA能量
               pIndex = parseInt(input_index.eq(i).val());
               sum += input_num.eq(i).val() * ypInfo[pIndex].YPND * 0.04;
               break;
           case '3':
               //求脂肪能量
               pIndex = parseInt($("#select_ZFYP").val());
               sum += input_num.eq(i).val() * ypInfo[pIndex].YPND * 0.09;
               break;
           default: break;
       }
   }*/
  $("#pnEnergy").val(Math.round(sum * 10) / 10);

}
//设置每kg能量（保留：作用于calculate()）
function setTotalEnergy() {
  var millEnergy = setMilkEnergy();
  var pnEnergy = $("#pnEnergy").val();
  if (parseFloat($("#weight").val()) == 0) {
      $("#totalEnergy").val("");
      return false;
  }

  value = (parseFloat(millEnergy) + parseFloat(pnEnergy)) / parseFloat($("#weight").val());
  $("#totalEnergy").val(Math.round(value * 10) / 10);
  return true;
}

//总能量
function setAllEnergy() {
  var millEnergy =  $("#milkEnergy").val();
  var pnEnergy = $("#pnEnergy").val();
  value = parseFloat(millEnergy) + parseFloat(pnEnergy);
  $("#allEnergy").val(Math.round(value*10)/10);
}

//求渗透压
function setOsmolality() {
  var input_index = $("input[name='input_index']");
  var input_type = $("input[name='input_type']");
  var input_num = $("input[name='input_num']");
  var sum = 0;
  var pIndex = 0;
  for (var i = 0 ; i < input_type.length; i++) {
    //0糖1维生素2氨基酸3脂肪4Na5K6Ca7P8Mg
    switch (input_type.eq(i).val()) {
      case '1':
        break;
        /* case '2'://求AA
              if (stringISEmpty(input_num.eq(i).val())) {
                  break;
              }
              pIndex = parseInt($("#select_AJSND").val());
              sum += parseFloat(input_num.eq(i).val()) * ypInfo[pIndex].STY;
              break;*/
      case '3':
        //求脂肪

        pIndex = parseInt($("#select_ZFYP").val());

        sum += parseFloat(input_num.eq(i).val()) * parseFloat(ypInfo[pIndex].STY);
        break;

      default:
        pIndex = parseInt(input_index.eq(i).val());
        if (parseFloat(input_num.eq(i).val()) >= 0) {
            sum += parseFloat(input_num.eq(i).val()) * parseFloat(ypInfo[pIndex].STY);
        }
        break;
    }
  }

  var pnFluidV = $("#PnFluidV").val();
  sum = sum / pnFluidV;
  sum = Math.round(sum);
  if (sum > 300) {
    if ($("#sel_method").val() == '1') {
      if (sum > 850) {
        WRT_e.ui.message({
          title: '提示',
          content: `外周静脉的渗透压不能超过850!`,
          onOk() {
          },
        })
        // $.messager.alert("提示", "", "info");
        return false;
      }
    }
  }
  else {
    WRT_e.ui.message({
      title: '提示',
      content: `渗透压不能小于300!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }

  $("#osmolality").val(sum);
  return true;
}

//全天总液量变化
function allDayFluidVChange(value) {
  if (value > 200) {
    // $(this).numberbox("setValue", ""); // 报错
    $(this).numberbox({value: ""});
    WRT_e.ui.message({
      title: '提示',
      content: `全天总液量不能超过200!`,
      onOk() {
      },
    })
    // $.messager.alert('提示', '', 'info');
  }
  else {
    setPnFluidV();
    setPNpumpSpeed();
  }
}

//PN中钠量
function NaOfPNChange(value) {
  if (value > 5) {
    // $(this).numberbox("setValue", "");
    $(this).numberbox({value: ""});
    WRT_e.ui.message({
      title: '提示',
      content: `PN中钠量不能超过5!`,
      onOk() {
      },
    })
    // $.messager.alert('提示', '', 'info');
  }
}

//PN中钾量
function KOfPNChange(value) {
  if (value > 3) {
    // $(this).numberbox("setValue", "");
    $(this).numberbox({value: ""});
    WRT_e.ui.message({
      title: '提示',
      content: `PN中钾量不能超过3!`,
      onOk() {
      },
    })
    // $.messager.alert('提示', '', 'info');
  }
}

//PN中钙量
function CaOfPNChange(value) {
  if (value > 3.5) {
    // $(this).numberbox("setValue", "");
    $(this).numberbox({value: ""});
    WRT_e.ui.message({
      title: '提示',
      content: `PN中钙量不能超过3.5!`,
      onOk() {
      },
    })
    // $.messager.alert('提示', '', 'info');
  }
}
//PN中磷量
function POfPNChange(value) {
  if (value > 3.5) {
    // $(this).numberbox("setValue", "");
    $(this).numberbox({value: ""});
    WRT_e.ui.message({
      title: '提示',
      content: `PN中磷量不能超过3.5!`,
      onOk() {
      },
    })
    // $.messager.alert('提示', '', 'info');
  }

}
//PN中镁量
function MgOfPNChange(value) {
  if (value > 0.3) {
      // $(this).numberbox("setValue", "");
      $(this).numberbox({value: ""});
      WRT_e.ui.message({
        title: '提示',
        content: `PN中镁量不能超过0.3!`,
        onOk() {
        },
      })
      // $.messager.alert('提示', 'PN中镁量不能超过0.3!', 'info');
  }

}

//PN糖速
function TSOfPNChange(value) {
  if (value > 12) {
      // $(this).numberbox("setValue", "");
      $(this).numberbox({value: ""});
      WRT_e.ui.message({
        title: '提示',
        content: `PN糖速不能超过12!`,
        onOk() {
        },
      })
      // $.messager.alert('提示', 'PN糖速不能超过12!', 'info');
  }
  else {
      setPNpumpSpeed();

  }
}

function setREE() {
  var weight = parseFloat($("#weight").val());
  if ($("#hfd_brxb").val() == "2") {
      $("#REE").val((58.3 * weight - 31).toFixed(1));
  }
  else {
      $("#REE").val((59.5 * weight - 30).toFixed(1));
  }


}

//显示化验结果(加这是代码中了) setHYJG(hyjgStr)  hydCallBack(res)

//尿液量
function urineCallBack(res) {
  var data = res;
  $("#urineTable").append("<tr><td cols='5'>尿液量</td></tr>")
  for (var i = 0; i < data.length; i++) {
    $("#urineTable").append("<tr><td style='width:16%'>" + data[i].JLSJ.replace("T", " ") + "</td><td style='width:16%'>尿(液)</td><td style='width:16%'>出量</td><td style='width:16%'>" + (data[i].COLOR == null ? '&nbsp;' : data[i].COLOR) + "</td><td style='width:16%'>" + (data[i].QUALITY == null ? '&nbsp;' : data[i].QUALITY) + "</td><td style='width:16%'>" + data[i].LL + "ml</td></tr>");
  }

}

//点击计算按钮，检查输入的项是否输入
function checkInputVal() {
  var yzarrNow = WRT_config.YZDByYzdid?WRT_config.YZDByYzdid.ly_ekpnyzd :WRT_config.PreviousYZDByBlid
  if (stringISEmpty($("#weight").val()) && (yzarrNow[0].Weight == '' || yzarrNow[0].Weight== undefined)) {
    WRT_e.ui.message({
      title: '提示',
      content: `体重不能为空!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }

  if (!stringISEmpty($("#typeOfMilk").val())) { //  && WRT_config.TypeOfMilk
    if (stringISEmpty($("#milkNum").val())) {
      WRT_e.ui.message({
        title: '提示',
        content: `请填写奶的量!`,
        onOk() {
        },
      })
      // $.messager.alert("提示", "", "info");
      return false;
    }

    if (stringISEmpty($("#mealOfMilk").val())) {
      WRT_e.ui.message({
        title: '提示',
        content: `请填写每天的顿数!`,
        onOk() {
        },
      })
      // $.messager.alert("提示", "", "info");
      return false;
    }

  }

  if (stringISEmpty($("#allDayFluidV").val()) && (yzarrNow[0].AllDayFluidV== '' || yzarrNow[0].AllDayFluidV== undefined)) {
    WRT_e.ui.message({
      title: '提示',
      content: `全天总液量不能为空!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }

  if (stringISEmpty($("#treatFluid").val()) && (yzarrNow[0].TreatFluid == '' || yzarrNow[0].TreatFluid== undefined)) {
    WRT_e.ui.message({
      title: '提示',
      content: `治疗液体不能为空!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }

  if (!stringISEmpty($("#NaInPN").val())) {
    if ($("#NaInPN").val() > 5) {
      $("#NaInPN").numberbox("clear");
      WRT_e.ui.message({
        title: '提示',
        content: `PN中钠量不能超过5!`,
        onOk() {
        },
      })
      // $.messager.alert("提示", "", "info");
      return false;
    }
  }

  if (!stringISEmpty($("#KInPN").val())) {
    if ($("#KInPN").val() > 3) {
        $("#KInPN").numberbox("clear");
        WRT_e.ui.message({
          title: '提示',
          content: `PN中钾量不能超过3!`,
          onOk() {
          },
        })
      // $.messager.alert("提示", "", "info");
      return false;
    }
  }

  /*  if (!stringISEmpty($("#CaInPN").val())) {
        if ($("#CaInPN").val() > 3.5) {
            $.messager.alert("提示", "PN中钙量不能超过3.5!", "info");
            return false;
        }
    }*/

  if (!stringISEmpty($("#PInPN").val())) {
    if ($("#PInPN").val() > 3.5) {
      WRT_e.ui.message({
        title: '提示',
        content: `PN中磷量不能超过3.5!`,
        onOk() {
        },
      })
      // $.messager.alert("提示", "", "info");
      return false;
    }
  }
  
  if (!stringISEmpty($("#pnTS").val()) || (yzarrNow[0].PnTs != '' || yzarrNow[0].PnTs != undefined)) {
    if ($("#pnTS").val() > 12) {
      WRT_e.ui.message({
        title: '提示',
        content: `PN糖速不能超过12!`,
        onOk() {
        },
      })
      // $.messager.alert("提示", "", "info");
      return false;
    }
  }
  else {
    WRT_e.ui.message({
      title: '提示',
      content: `PN糖速不能为空!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }


  return true;
}

//计算静脉营养药品的用量
function calculate() {
  if (!checkInputVal()) {
      return false;
  }
  //计算Pn液量
  setPnFluidV();
  var weight = parseFloat($("#weight").val());
  var condition = 0;

  var fluid = parseFloat($("#PnFluidV").val());

  //钠
  var NaValue = 0;
  if (!stringISEmpty($("#NaInPN").val())) {
    NaValue = parseFloat($("#NaInPN").val());
  }
  // 体重XPN中钠量X58.5÷1000÷PN液量
  condition = weight * NaValue * 58.5 / 1000 / fluid;
  if (condition >= 0.003) {
    $("#NaInPN").numberbox("clear");
    WRT_e.ui.message({
      title: '提示',
      content: `钠量占比超过0.3%，请重新填写计算!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }

  //钾
  var KValue = 0;
  if (!stringISEmpty($("#KInPN").val())) {
      KValue = parseFloat($("#KInPN").val());
  }
  // 体重XPN中钾量X74.55÷1000÷PN液量
  condition = weight * KValue * 74.55 / 1000 / fluid;
  if (condition >= 0.003) {
    $("#KInPN").numberbox("clear");
    WRT_e.ui.message({
      title: '提示',
      content: `钾量占比超过0.3%，请重新填写计算!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }

  /*
      var Ca = 0;
      if (!stringISEmpty($("#CaInPN").val())) {
          Ca = parseFloat($("#CaInPN").val());
      }
      if (Ca > 3.5) {
          $.messager.alert("提示", "钙量不能超过3.5，请重新填写计算!", "info");
          return;
      }*/

  var P = 0;
  if (!stringISEmpty($("#PInPN").val())) {
    P = parseFloat($("#PInPN").val());
  }
  if (P > 3.5) {
    WRT_e.ui.message({
      title: '提示',
      content: `磷量不能超过3.5，请重新填写计算!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }
  var Mg = 0;
  if (!stringISEmpty($("#MgInPN").val())) {
    Mg = parseFloat($("#MgInPN").val());
  }

  if (Mg > 0.3) {
    WRT_e.ui.message({
      title: '提示',
      content: `镁量不能超过0.3，请重新填写计算!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }

  //条件: [体重XPN中钙量÷PN液量X1000] X [体重XPN磷量÷PN液量X1000]<200
  /* condition = (Ca * weight) / fluid * 1000 * (weight * P) / fluid * 1000;
  if (condition >= 200) {
      $.messager.alert("提示", "钙量或磷量过多，请重新填写计算!", "info");
      return;
  }*/

  //条件: [(体重 X PN中钙量  + 体重 X PN镁量) ÷ PN液量X1000]<10
  /*  condition = (weight * Ca + weight * Mg) / fluid * 1000;
    if (condition >= 10) {
        $.messager.alert("提示", "钙量或镁量过多，请重新填写计算!", "info");
        return;
    }*/
  var hasNagetive = false;
  var pnTS = parseFloat($("#pnTS").val());
  var input_index = $("[name='input_index']");
  var input_type = $("input[name='input_type']");
  var input_num = $("input[name='input_num']");
  var selected_WSS = 0;

  //是复合磷酸氢钾体积
  var isKP = false;
  var KPV = 0;
  var KCapacity = 0;
  //甘油磷酸钠体积
  var NaPV = 0;
  var NaCapacity = 0;
  //容量系数
  var capacityCoe = parseFloat($("#capacityCoe").val());
  var value = 0;
  var fluidSum = 0;
  var pIndex = 0;

  if (fluid < 0) {
    WRT_e.ui.message({
      title: '提示',
      content: `PN液量是负数!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return false;
  }

  for (var i = 0 ; i < input_type.length; i++) {
    //0 糖1 维生素2氨基酸AJS3脂肪4甘油磷酸钠5复合磷酸钾6水7左卡尼汀  Na K Ca P Mg
    switch (input_type.eq(i).val()) {
      case '0':
        //求糖
        // pIndex = parseInt(input_index.eq(i).val());
        //糖速X体重X60X24÷1000÷可维护浓度
        /* value = pnTS * weight * 60 * 24 / 1000 / parseFloat(ypInfo[pIndex].YPND) * 100;
          value = Math.round(value);
          if (value < 0) {
              hasNagetive = true;
          }
          input_num.eq(i).val(value);
          fluidSum += value;*/
        break;
      case '1':
        //求维生素
        if ($("[name='selected_WSS']").eq(selected_WSS++).val() == '0') {
          input_num.eq(i).val("");
          break;
        }
        pIndex = parseInt(input_index.eq(i).val());
        var sum = 0;


        if (ypInfo[pIndex].MC.indexOf("水溶性") > -1) {
            //水溶性维生素
          //体重X0.1 上限为1
          sum = weight * 0.1;
          if (sum > 1) {
            WRT_e.ui.message({
              title: '提示',
              content: `水溶性维生素要小于1支`,
              onOk() {
              },
            })
            // $.messager.alert("提示", "", "info");
            input_num.eq(i).val("");
            return false;
          }
        }
        else {
          //脂溶性维生素
          //体重X0.2 上限为2
          sum = weight * 0.2;
          if (sum > 2) {
            WRT_e.ui.message({
              title: '提示',
              content: `脂溶性维生素要小于2支`,
              onOk() {
              },
            })
            // $.messager.alert("提示", "", "info");
            input_num.eq(i).val("");
            return false;
          }
        }
        value = Math.round(sum * 10) / 10;
        if (value < 0) {
          hasNagetive = true;
        }
        input_num.eq(i).val(value);
        break;
      case '2'://求氨基酸
        var aminoAcid = parseFloat($("#aminoAcid").val());
        //氨基酸X体重÷可维护浓度
        value = Math.round(aminoAcid * weight * 100 / parseFloat(ypInfo[pIndex].YPND));
        if (value < 0) {
            hasNagetive = true;
        }
        input_num.eq(i).val(value);
        fluidSum += value;
        break;
      case '3':
        //求脂肪
        //脂肪乳x体重÷可维护浓度
        var le = parseFloat($("#lipidEmulsion").val());
        pIndex = parseInt($("#select_ZFYP").val());
        value = Math.round(weight * le * 100 / parseFloat(ypInfo[pIndex].YPND));
        fluidSum += value;
        if (value < 0) {
            hasNagetive = true;
        }
        input_num.eq(i).val(value);
        break;
      case '4':
        //甘油磷酸钠
        //体重XPN中磷量÷20X可维护甘油磷酸钠容量
        pIndex = parseInt(input_index.eq(i).val());
        NaCapacity = parseFloat(ypInfo[pIndex].Capacity);
        value = weight * P * NaCapacity / 20;
        value = Math.round(value * 100) / 100;
        NaPV = value;

        fluidSum += value;
        if (value < 0) {
            hasNagetive = true;
        }
        input_num.eq(i).val(value);
        break;
      case '5':
        //求复合磷酸氢钾
        //体重XPN中磷量÷复合磷酸氢钾mmolX复合磷酸氢钾容量
        isKP = true;
        pIndex = parseInt(input_index.eq(i).val());
        KCapacity = parseFloat(ypInfo[pIndex].Capacity);
        value = weight * P * KCapacity / parseFloat(ypInfo[pIndex].MMOL);
        value = Math.round(value * 100) / 100;
        KPV = value;
        fluidSum += value;
        if (value < 0) {
            hasNagetive = true;
        }
        input_num.eq(i).val(value);
        break;

      case '7':
        //左卡尼汀 
        //体重X25÷1000÷可维护浓度
        if ($("[name='selected_ZKND']").eq(0).val() == '0') {
          input_num.eq(i).val("");
          break;
        }
        pIndex = parseInt(input_index.eq(i).val());
        value = weight * 25 / 1000 / (parseFloat(ypInfo[pIndex].YPND) / 100);
        input_num.eq(i).val(value);
        if (value < 0) {
          hasNagetive = true;
        }
        fluidSum += value;

        break;
        /*  case 'Ca':
            //求Ca
            //体重XPN中钙量X448.4÷1000÷可维护葡萄糖酸钙浓度
          
            pIndex = parseInt(input_index.eq(i).val());
            value = weight * Ca * 448.4 / 1000 / (parseFloat(ypInfo[pIndex].YPND) / 100);
          
            var Mg = 0;
            if (!stringISEmpty($("#MgInPN").val())) {
                Mg = parseFloat($("#MgInPN").val());
            }
          

            value = Math.round(value * 100) / 100;
            fluidSum += value;
            if (value < 0) {
                hasNagetive = true;
            }
            input_num.eq(i).val(value);
            break;*/
      case 'Mg':
        //求含Mg的溶液
        //体重XPN中镁量X246.48÷1000÷可维护硫酸镁浓度
        pIndex = parseInt(input_index.eq(i).val());
        value = weight * Mg * 246.48 / 1000 / (parseFloat(ypInfo[pIndex].YPND) / 100);
        value = Math.round(value * 100) / 100;
        fluidSum += value;
        if (value < 0) {
          hasNagetive = true;
        }
        input_num.eq(i).val(value);
        break;

      default: break;
    }
  }
  //葡萄糖浓度 
  var pptND = new Array(2);
  var pptNDIndex = 0;
  for (var i = 0 ; i < input_type.length; i++) {
    //0 糖1 维生素2氨基酸AJS3脂肪4Na5K6Ca7P8Mg
    switch (input_type.eq(i).val()) {
      case '0':
        pptND[pptNDIndex] = parseInt(input_index.eq(i).val());
        pptNDIndex++;
        break;
      case 'Na'://求Na
        pIndex = parseInt(input_index.eq(i).val());
        if (!isKP) {
            value = (weight * NaValue - (NaPV * 20 / NaCapacity)) * 58.5 / 1000 / (parseFloat(ypInfo[pIndex].YPND) / 100);
        }
        else {
            value = weight * NaValue * 58.5 / 1000 / (parseFloat(ypInfo[pIndex].YPND) / 100);
        }

        value = Math.round(value * 10) / 10;
        fluidSum += value;
        if (value < 0) {
            hasNagetive = true;
        }
        input_num.eq(i).val(value);
        break;
      case 'K'://求K
        pIndex = parseInt(input_index.eq(i).val());
        if (isKP) {
            value = (weight * KValue - (KPV / KCapacity * 8.8)) * 74.55 / 1000 / (parseFloat(ypInfo[pIndex].YPND) / 100);
        }
        else {
            value = (weight * KValue) * 74.55 / 1000 / (parseFloat(ypInfo[pIndex].YPND) / 100);
        }
        value = Math.round(value * 10) / 10;
        fluidSum += value;
        if (value < 0) {
            hasNagetive = true;
        }
        input_num.eq(i).val(value);
        break;
        default: break;
    }
  }
  // 临时去掉
  if (ypInfo[pptND[0]].YPND < ypInfo[pptND[1]].YPND) {
    var t = pptND[0];
    pptND[0] = pptND[1];
    pptND[1] = t;
  }

  //葡萄糖计算
  pptNDIndex = -1;
  for (var i = 0 ; i < input_type.length; i++) {
    if (input_type.eq(i).val() == "0") {
      pptNDIndex++;

      switch (pptNDIndex) {
        case 1:
          value = fluid - fluidSum;
          break;
        default:
          pIndex = pptND[pptNDIndex];
          var a = ypInfo[pptND[pptNDIndex + 1]];
          var b = ypInfo[pIndex];
          value = (pnTS * weight * 60 * 24 / 1000 - (fluid - fluidSum) * parseFloat(a.YPND) / 100) / (parseFloat(b.YPND) / 100 - parseFloat(a.YPND) / 100);

          break;
      }
      value = Math.round(value);
      fluidSum += value;
      if (value < 0) {
        hasNagetive = true;
      }
      input_num.eq(i).val(value);
    }
  }

  /*
      for (var i = 0 ; i < input_type.length; i++) {
          if (stringISEmpty(input_num.eq(i).val())) {
              continue;
          }
          value = parseFloat(input_num.eq(i).val());
          switch (input_type.eq(i).val()) {
              case '2':
              case '3':
              case '0':
                  //求糖
                  value = Math.round(value *capacityCoe);
                  input_num.eq(i).val(value);
                  break;
              case '1':
              case 'K':
              case 'Na':
                  //求维生素
                  value = Math.round(value * capacityCoe * 10) / 10;
                  input_num.eq(i).val(value);
                  break;
              case '4':
              case 'P':
              case '7':
              case 'Ca':
              case 'Mg':
                  //甘油磷酸钠
                  value = Math.round(value * capacityCoe * 100) / 100;
                  input_num.eq(i).val(value);
                  break;
              default: break;
          }
      }*/
   
  setPnEnergy();
  if (!setTotalEnergy()) {
    return false;
  }
  if (!setMonovalent()) {
    return false;
  }
  if (!setDivalent()) {
    return false;
  }
  if (!setText_TND()) {
    return false;
  }
  setAllEnergy();
  setREE();
  if (!setSAFEnergyAndND()) {
    return false;
  }

  if (!setOsmolality()) {
    return false;
  }

  if (hasNagetive) {
    //$("input[name='input_num']").val("");
    //$("input[name='input_Attributes']").val("");
    WRT_e.ui.message({
        title: '提示',
        content: `计算出来的结果有负数!`,
        onOk() {
        },
      })
    // $.messager.alert("提示", "", "info");
    return false;
  }
  return true;
}

//保存营养属性的量
function checkSubmit() {
  var yyList = {};
  // yyList.Blid = parseInt($("#hfd_blid").val());
  yyList.Blid = param['as_blid']
  yyList.SelectedMethod = $("#sel_method").val();
  yyList.Weight = 0;
  if (stringISEmpty($("#weight").val()) && (WRT_config.YZDByYzdid.ly_ekpnyzd[0].Weight== '' || WRT_config.YZDByYzdid.ly_ekpnyzd[0].Weight== undefined)) {
    WRT_e.ui.message({
      title: '提示',
      content: `体重不能为空!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return;
  }
  //yyList.Weight = parseInt($("#weight").val());
  yyList.Weight = $("#weight").val();
  var yzdid = -1;
  if (!stringISEmpty(param['as_yzdid']) && param['as_yzdid'] != 0) {
    yzdid = parseInt(param['as_yzdid']);
  }

  yyList.Yzdid = yzdid;

  if (!stringISEmpty($("#typeOfMilk").val())) {
    yyList.TypeOfMilk = $("#typeOfMilk").get(0).selectedIndex;
    if (stringISEmpty($("#milkNum").val())) {
      WRT_e.ui.message({
        title: '提示',
        content: `奶的量不能为空!`,
        onOk() {
        },
      })
      // $.messager.alert("提示", "", "info");
      return;
    }

    yyList.MilkNum = parseFloat($("#milkNum").val());
    if (stringISEmpty($("#mealOfMilk").val())) {
      WRT_e.ui.message({
        title: '提示',
        content: `吃第一种奶的次数不能为空!`,
        onOk() {
        },
      })
      // $.messager.alert("提示", "", "info");
      return;
    }

    yyList.MealOfMilk = parseInt($("#mealOfMilk").val());
    yyList.TimesToEat = parseInt($("#timesToEat").val());
    if (stringISEmpty($("#typeOfMilk1").val()) && 24 / yyList.TimesToEat != $("#mealOfMilk").val()) {
      WRT_e.ui.message({
        title: '提示',
        content: `吃第一种奶的次数与一天的顿数不一致!`,
        onOk() {
        },
      })
      // $.messager.alert("提示", "", "info");
      return;
    }
    yyList.TypeOfMilk1 = $("#typeOfMilk1").get(0).selectedIndex;
  }

  if (stringISEmpty($("#allDayFluidV").val()) && (WRT_config.YZDByYzdid.ly_ekpnyzd[0].AllDayFluidV== '' || WRT_config.YZDByYzdid.ly_ekpnyzd[0].AllDayFluidV== undefined)) {
    WRT_e.ui.message({
      title: '提示',
      content: `全天总液量不能为空!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return;
  }

  yyList.AllDayFluidV = parseFloat($("#allDayFluidV").val());

  if (stringISEmpty($("#treatFluid").val())) {
    WRT_e.ui.message({
      title: '提示',
      content: `治疗液量不能为空!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return;
  }

  yyList.TreatFluid = $("#treatFluid").val();
  yyList.AminoAcid = $("#aminoAcid").val();
  yyList.LipidEmulsion = $("#lipidEmulsion").val();

  yyList.Microelement = "";
  var microelement = $("[name='microelement']");
  var flag = 0;
  for (var i = 0 ; i < microelement.length; i++) {
    // if (!stringISEmpty(microelement.eq(i).val())) {
    if (!stringISEmpty(microelement[i].value) || microelement[i].value=='0.0') {
      if (flag == 1) {
        yyList.Microelement += "_";
      }
      yyList.Microelement += microelement.eq(i).val();
      flag = 1;
    }
    else {
      if (flag == 1) {
        yyList.Microelement += "_";
      }
      yyList.Microelement += "0";
    }
  }

  if (stringISEmpty($("#pnTime").val())) {
    WRT_e.ui.message({
      title: '提示',
      content: `pn时间不能为空!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return;
  }
  yyList.PnTime = $("#pnTime").val();

  if (stringISEmpty($("#pnTS").val())) {
    WRT_e.ui.message({
      title: '提示',
      content: `pn糖速不能为空!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return;
  }
  yyList.PnTS = parseFloat($("#pnTS").val());
  yyList.COE = $("#capacityCoe").val();
  yyList.HYJG = JSON.stringify(WRT_config.HYJG) || null;
  yyList.PnBZ = $("#PNpumpSpeed").val();
  return yyList;

}

// 计算按钮边上的保存按钮
function save_ekpnyy() {
  
  if (calculate()) {
    var yyList = checkSubmit();
    var ypList = save_ekpnyyyp(yyList.Yzdid);
    
    if (ypList.length != 0) {
      WRT_e.api.pnOrderList.SaveData({
        params: {
          as_ekpnyy: JSON.stringify(yyList),
          as_ekpnyp: JSON.stringify(ypList)
        },
        success(data) {
          if (data.d.Code == 1) {
            // let params= {
            //   as_blid: param['as_blid'],
            //   as_yzdid: param['as_yzdid']==0?yzdid:param['as_yzdid'],
            //   as_zxpl:  $("#yypl").val(),
            //   as_zxts: parseInt($("#cxsyts").val()),
            //   as_yzsj: WRT_config.nbPNC_init['药房代码'],
            //   as_spxx: $("#dtm_txrq").val()
            // }
            
            saveCallback(data.d)
          }
        }
      })
      
        // nbPNCalculator.SaveData(JSON.stringify(yyList), JSON.stringify(ypList), saveCallback);
    }
  }
}

// 保存药品
function save_ekpnyyyp(yzdid) {
  var ypList = [];
  var weight = $("#weight").val();
  // var input_index = $("input[name='input_index']");
  var input_index = ypInfo;
  // var input_type = $("input[name='input_type']");
  // var input_num = $("input[name='input_num']");

  var value = 0;
  var selected_WSS = 0;
  
  for (var i = 0 ; i < input_index.length; i++) {
    //0糖1维生素2氨基酸3脂肪4Na5K6Ca7P8Mg
    switch (input_index[i].YPTYPE) {
      case '0':
        //糖
      case '2':
        //氨基酸
        // pIndex = parseInt(input_index.eq(i).val());
        pIndex = i
        if (!stringISEmpty($('#aminoAcid option:selected').val())) {
            ypList.push({
              YPID: ypInfo[pIndex].YPID,
              YCYL: $('#aminoAcid option:selected').val(),
              YZDID: yzdid
            });
        }
        else {
          ypList.push({
              YPID: ypInfo[pIndex].YPID,
              YCYL: 0,
              YZDID: yzdid
          });
        }
        break;
      case '1':
        //求维生素
        // pIndex = parseInt(input_index.eq(i).val());
        pIndex = i
        // if ($("[name='selected_WSS']").eq(selected_WSS++).val() == '0') {
        if ($('#selected_WSS option:selected').val() == '0') {
            ypList.push({
              YPID: ypInfo[pIndex].YPID,
              YCYL: 0,
              YZDID: yzdid
            });
            break;
        }

        ypList.push({
          YPID: ypInfo[pIndex].YPID,
          // YCYL: $("[name='selected_WSS']").eq(selected_WSS++).val(),
          YCYL: $(`.num`+pIndex+``).val(),
          YZDID: yzdid
        });
        break;

      case '3':  //求脂肪
        // pIndex = parseInt($("#select_ZFYP").val());
        pIndex = i
        if (stringISEmpty($('#select_ZFYP option:selected').val())) {
            ypList.push({
              YPID: ypInfo[pIndex].YPID,
              YCYL: 0,
              YZDID: yzdid
            });
            break;
        }
        ypList.push({
          YPID: ypInfo[pIndex].YPID,
          YCYL: $('#select_ZFYP option:selected').val(),
          YZDID: yzdid
        });
        break;
      case 'Na':  //求Na
        // break;
      case 'K': //求K
        // break;
        //  case 'Ca': //求Ca
      case '4':   //求P
        // break;
      case '5': 
        // break;
      case '6': 
        // break;
      case '7': 
        // break; 
      case 'Mg':  //求Mg
        // pIndex = parseInt(input_index.eq(i).val());
        pIndex = i
        if (!stringISEmpty($(`.num`+i+``).val())) {
          
          ypList.push({
            YPID: ypInfo[pIndex].YPID,
            YCYL: $(`.num`+pIndex+``).val(),
            YZDID: yzdid
          });
        } else {
          ypList.push({
            YPID: ypInfo[pIndex].YPID,
            YCYL: 0,
            // name: ypInfo[pIndex].MC,
            YZDID: yzdid
          });
        }
        break;
      default: break;
    }
  }

  if (ypList.length == 0) {
    WRT_e.ui.message({
      title: '提示',
      content: `没有要保存的药品,请点击计算后保存或重新填写相关数据计算后保存`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return;
  }
  return ypList;
}
// 保存回调（用于保存医嘱）
function saveCallback(as_res) {
  var value =as_res
  // var value = eval("(" + as_res.value + ")");
  if (value.Code == 1) {
    let earlyData = sessionStorage.getItem('pnOorderList_init')
    WRT_e.api.pnOrderList.getInitList({
      params: {
        ll_blid: param["as_blid"]
        // al_blid: WRT_config.BrMainLeft.BRJBXX.BLID
      },
      success(data) {
        if (data.d.Code == 1) {
          WRT_config.pnOorderList_init=JSON.parse(data.d.Result)
          console.log('是否新增',JSON.parse(earlyData),earlyData == data.d.Result)
          // sessionStorage.setItem('pnOorderList_init',data.d.Result)
          // console.log('儿科营养医嘱列表初始化',WRT_config.pnOorderList_init)
        }
      }
    })
    // opener.reloadData();
    WRT_e.ui.message({
      title: '提示',
      content: `保存成功,可以提交医嘱！`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    if (stringISEmpty(param['as_yzdid'])) {
      yzdid = value.CodeMsg
      // $("#hfd_yzdid").val(value.Message);
    }
  }
  else {
    alert(value.CodeMsg);
  }
}

// 保存医嘱单
function SaveYPYZ() {
  if (stringISEmpty(param['as_yzdid'])) {
    WRT_e.ui.message({
      title: '提示',
      content: `请先保存医嘱单!`,
      onOk() {
      },
    })
    // $.messager.alert("提示", "", "info");
    return;
  }

  if (calculate()) {
      var yyList = checkSubmit();
      var ypList = save_ekpnyyyp(yyList.Yzdid);

      if (ypList.length != 0) {
        // 新
        WRT_e.api.pnOrderList.SaveData({
          params: {
            as_ekpnyy: JSON.stringify(yyList),
            as_ekpnyp: JSON.stringify(ypList)
          },
          success(data) {
            console.log('保存',data)
            if (data.d.Code == 1) {
              saveCallback1(data.d)
            }
          }
        })
        // nbPNCalculator.SaveData(JSON.stringify(yyList), JSON.stringify(ypList), saveCallback1);
      }
  }
  
}
// 保存回调（用于保存医嘱单）
function saveCallback1(as_res) {
  var value =as_res
  // var value = eval("(" + as_res.value + ")");
  if (value.Code == 1) {
    // opener.reloadData();
    if (stringISEmpty(param['as_yzdid']) || param['as_yzdid'] ==0) {
      // yzdid = value.CodeMsg
      yzdid = value.Result
      // $("#hfd_yzdid").val(value.Message);
    }

    if (stringISEmpty($("#cxsyts").val())) {
      WRT_e.ui.message({
        title: '提示',
        content: `请填写持续输液天数!`,
        onOk() {
        },
      })
      // $.messager.alert("提示", "", "info");
      return;
    } else if (Number($("#cxsyts").val())==NaN){
      WRT_e.ui.message({
        title: '提示',
        content: `请填写数字!`,
        onOk() {
        },
      })
    } else {
      var yyList = checkSubmit();
      var ypList = save_ekpnyyyp(yyList.Yzdid);
      // ypInfo数据更完整
      console.log(yyList,ypList,ypInfo)
      // 增加药品审批
      yyyznr_lists = ypInfo
      yyyznr_lists.map((e,i)=>{
        e.SQSL = e.SQSL==null?'':e.SQSL
        e.XSP =  e.XSP==null?'':e.XSP
        // e.XDFW = ''
      })
      // try {
        dldr_lists = yyyznr_lists
        mbindex = 0
        for(let i = 0;i<dldr_lists.length;i++){
          mbindex++
          SetYp_callback_new(dldr_lists[i],i+1)
        }
        // var spxxList = []
        dldr_lists.forEach((item)=>{
          spxxList.push({
            ypid: item.YPID,
            xsp: item.XSP,
            sqsl: item.SQSL==''?0: item.SQSL
          })
        })
        console.log(spxxList)
      // } finally {
        
        showSave = 1
        setTimeout(() => {
          if(modalopen != 0 && modalopen == 2 && ($('.e_message').iziModal('getState')!='opened' && $('.e_message ').iziModal('getState')!='opening')){
            let params = {
              as_blid: parseInt(param['as_blid']),
              as_yzdid: param['as_yzdid']==0?parseInt(yzdid):parseInt(param['as_yzdid']),
              as_zxpl:  $("#yypl").val(),
              as_zxts: parseInt($("#cxsyts").val()),
              as_yzsj: $("#dtm_txrq").val(),
              as_spxx: JSON.stringify(spxxList)
              // as_spxx: $("#dtm_txrq").datetimebox("getValue"),
            }
            saveCallbackPort(params)
          } else {
            whileFunProt(spxxList)
          }
        }, 2000);
      // WRT_e.api.pnOrderList.SaveYPYZ({
      //   params: {
      //     as_blid: parseInt(param['as_blid']),
      //     as_yzdid: param['as_yzdid']==0?parseInt(yzdid):parseInt(param['as_yzdid']),
      //     as_zxpl:  $("#yypl").val(),
      //     as_zxts: parseInt($("#cxsyts").val()),
      //     as_yzsj: $("#dtm_txrq").val(),
      //     as_spxx: JSON.stringify(spxxList)
      //     // as_spxx: $("#dtm_txrq").datetimebox("getValue"),
      //   },
      //   success(data) {
      //     if (data.d.Code == 1) {
      //       saveCallback(data.d)
      //     }
      //   }
      // })
    }
    // nbPNCalculator.SaveYPYZ(parseInt($("#hfd_blid").val()), parseInt(param['as_yzdid']), $("#yypl").val(), parseInt($("#cxsyts").val()), $("#hfd_yfdm").val(), $("#dtm_txrq").datetimebox("getValue"), saveYPYZCallBack);
  }
  else {
    alert(value.CodeMsg);
  }
}
// 判断弹窗是否都关闭了
function whileFunProt(spxxList){
  setTimeout(() => {
    if(modalopen != 0 && modalopen == 2 && ($('.e_message').iziModal('getState')!='opened' && $('.e_message ').iziModal('getState')!='opening')){
      let params = {
        as_blid: parseInt(param['as_blid']),
        as_yzdid: param['as_yzdid']==0?parseInt(yzdid):parseInt(param['as_yzdid']),
        as_zxpl:  $("#yypl").val(),
        as_zxts: parseInt($("#cxsyts").val()),
        as_yzsj: $("#dtm_txrq").val(),
        as_spxx: JSON.stringify(spxxList)
        // as_spxx: $("#dtm_txrq").datetimebox("getValue"),
      }
      saveCallbackPort(params)
    } else {
      whileFunProt(spxxList)
    }
  },2000)
}
function saveCallbackPort(paramData){
  WRT_e.api.pnOrderList.SaveYPYZ({
    params: paramData,
    success(data) {
      console.log('新营养医嘱数据SaveYPYZ',data)
      if (data.d.Code == 1) {
        saveYPYZCallBack(data.d)
      }
    }
  })
}
function saveYPYZCallBack(res) {
  var value =res
  // var value = eval("(" + res.value + ")");
  if (value.Code == 0) {
    WRT_e.ui.message({
      title: '提示',
      content: value.CodeMsg,
      onOk() {
      },
    })
    // $.messager.alert("提示", , "info");
  }
  else {
    // WRT_e.ui.message({
    //   title: '提示',
    //   content: value.CodeMsg,
    //   onOk() {
    //     window.close();
    //   },
    // })
    alert(value.CodeMsg);
    
    window.close();
  }
}

// 画图(血糖标准天)
function pnNowChart() {
  // 血糖天数
  // initialData.XTBZTS = WRT_config.nbPNC_init['血糖标准天']
  xtzArr = WRT_config.nbPNC_init['血糖标准天']
  let newarr = []
  // let narr = []
  if(xtzArr.length>0){
    // 获取x轴数据    获取血糖值数据      
    xAxisData = [], xtzData = []
    xtzArr.forEach(item => {
      newarr.push(($.extend({},item,{temp: new Date(item.CLSJ).getTime()})))
    });
    let resultArr = newarr.sort(function (a,b) {
      return a.temp - b.temp
    })
    resultArr.map((item,index)=>{
      xtzData.push(Number(item.CLZ))
      speData.push(7.8)
      tpnData.push(3.9)
      xAxisData.push((item.CLSJ).replace('T',' '))
    })
  }
  var chartDom = document.getElementById('nbPNChartContect');
  var myChart = echarts.init(chartDom);
  var option = {
    title: {
      text: '血糖标准天',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['血糖值', '7.8', '3.9'],
      left: 'left',
      orient: 'vertical',
      align: 'left',
      top: 'middle',
      // padding: [
      //   5, // 上
      //   0, // 右
      //   5, // 下
      //   10 // 左
      // ]
      padding: 5
    },
    grid: {
      left: '15%',
      right: '4%',
      bottom: '10%',
      containLabel: true
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    xAxis: {
      type: 'category',
      // boundaryGap: false,
      data: xAxisData
    },
    yAxis: {
      type: 'value',
      min: 0,
      max: 15,
      // data: ['0', '2', '3', '5', '6', '8', '9', '11', '12', '14', '15']
      // data: [0, 2, 3, 5, 6, 8, 9, 11, 12, 14, 15]
    },
    series: [
      {
        name: '3.9',
        type: 'line',
        // stack: '3.9',
        data: tpnData,
        markPoint: {
          data: [{ name: '最低血糖', value: 3.9, xAxis: 0, yAxis: 3.9 }],
          symbolRotate:'180',
          // symbolOffset:[0, '50%'],
          label: {
            offset: [0, 10]
          }
        },
        // data: [150, 232, 201, 154, 190, 330, 410]
      },
      {
        name: '血糖值',
        type: 'line',
        // stack: 'Total',
        data: xtzData
        // data: [120, 132, 101, 134, 90, 230, 210]
      },
      {
        name: '7.8',
        type: 'line',
        // stack: '7.8',
        data: speData,
        markPoint: {
          data: [{ name: '最高血糖', value: 7.8, xAxis: 1, yAxis: 7.8 }],
        },
        // data: [220, 182, 191, 234, 290, 330, 310]
      },
    ]
  };
  option && myChart.setOption(option);
}

// 药品审批(用于提交医嘱)

// //设置费用类型
function setFylx(jslx, zykt, sfzf, kzjb) {
  let type = 0
  if (zykt == "0")
      type = 1
  else if (jslx == "00")
      type = 1
  else if (sfzf == "1")
      type = 1
  else {
      type = kzjb
  }
  return type
}

// // 保存前获取申请数量对应弹窗方法
// //判断
function SetYp_callback_new(yp_list){
  let zh = "", mbmc = "";
  // mbindex = index
  // zh = yp_list.ZH;
  // zh = WRT_config.nutriOrderListOne.ZH;
  // mbmc = yp_list.MBMC; // 模板名称该页面没有接口返回这个参数
  mbmc = yp_list.YPID; // 是唯一值设置药品id
  if(yp_list.YPID==0){
    WRT_e.ui.message({
      title:'提示',
      content:"【" + yp_list.YPMC + "】已被药房停止使用!"
    })
    removeYp(zh, mbmc);
    return;
  }
  // if (yp_list.CLTS == "1") { // 貌似用不到
  //   WRT_e.ui.message({
  //     title:'提示',
  //     content:"根据医院处方点评工作组反馈和医院规定,您被限制开具药品【" + yp_list.YPMC + "】"
  //   })
  //   removeYp(zh, mbmc);
  //   return;
  // }
  WRT_e.api.nutriOrderList.getYpFjxx({
    params: { 
      al_ypid: yp_list.YPID, 
      as_yfdm: WRT_config.nbPNC_init['药房代码'] || "", 
      as_jslx: WRT_config.nbPNC_init['结算类型'], 
      al_zyid: WRT_config.nbPNC_init['住院id'], 
      as_yzlx: 'cq', 
      al_zkid: WRT_config.nbPNC_init['专科id'] 
    },
    success(data) {
      if (data.Code == 1) {
        let array = JSON.parse(data.Result)
        WRT_config.ypfjxx = array[0] || {}
        if (WRT_config.ypfjxx.gwyp == 1) {
        // if (WRT_config.ypfjxx.gwyp == 1 && (yp_list.yts==undefined || yp_list.yts==0)) {
          // yp_list.yts = 1
          WRT_e.ui.message({
            title: '信息窗口',
            content: `${yp_list.YPMC || yp_list.MC}是高警示药品`,
            onOk() {
              modalopen = 2
            }
          })
        }
        
        if (WRT_config.ypfjxx) {
          yp_list.XSP=WRT_config.ypfjxx.xsp||yp_list.xsp||''
        //   // let index=mbindex
          index=mbindex-1
          if(index<0){
              index=0
          }
          dldr_lists[index].XSP=yp_list.XSP || yp_list.xsp
          // dldr_lists[index].xsp=yp_list.XSP || yp_list.xsp
          setSflb(yp_list)
          // iskjywpd(yp_list,zh,mbmc)
        }
      } else {
        WRT_e.ui.message({
          title: '信息窗口',
          content: data.CodeMsg,
          onOk() {
            exportMBList()
            modalopen = 2
          },
        })
        // WRT_e.ui.hint({type:'error',msg:data.CodeMsg})
      }
    }
  })
}

//导入队列
function exportMBList() {
  // let rtn=false
  // yyyznr_lists = dldr_lists
  // console.log('导入队列',mbindex ,yyyznr_lists,dldr_lists, dldr_lists.length,mbindex == dldr_lists.length)
  if (dldr_lists.length > 0) {
    if (mbindex <= dldr_lists.length) {
      if (mbindex == dldr_lists.length) {
        // 判断哪些药品是关闭弹窗的状态
        // 将带DEL字段的数据删除
        for (let index = 0; index < dldr_lists.length; index++) {
          if(dldr_lists[index].DEL==1){
            dldr_lists.splice(index,1)
            index=0
          }
        }
        // dldr_lists.filter(e=>(e.DEL!=1 || e.DEL==undefined))
        mbindex=0
        //   if(dldr_lists[index].DEL==1){
        //     dldr_lists.splice(index,1)
        //     // yyyznr_lists.splice(i,1)
        //     i--;
        //   }
        //   // dldr_lists.splice(index,1)
        //   index=0
        // }
        // // mbindex=0
        // // yyyznr_lists=[...yyyznr_lists,...dldr_lists]
        return dldr_lists
      }
      // mbindex++
      // SetYp_callback_new(dldr_lists[mbindex-1])
      // mbindex++
    }
  }
}

//药品审批
function setSflb(yp_list) {
  // console.log('药品审批',yp_list,indexNow,WRT_config.nbPNC_init['结算类型'] == '00')  JSLX == ['结算类型']
  //自费或者住院未开通
  if (WRT_config.nbPNC_init['结算类型'] == '00') {
    //强制审批
    if (yp_list.XSP == '2' || yp_list.XSP == '3') {
      let as_sbxzfw = escape(yp_list.XZFW)
      // let as_brzd = escape(WRT_config.nbPNC_init['诊断']) ['诊断'] == .BRZD
      let as_brzd = escape(WRT_config.nbPNC_init['诊断'])
      if(WRT_config.ypfjxx.xzfw_bbzt==1){
        let html=ypsp_html(WRT_config.ypfjxx)
        WRT_e.ui.model({
          id: "if_ypsp",
          title: "医保限制支付范围提示",
          width: "650px",
          content: html,
          closeButton: false,
          closeOnEscape: false,
          iframe: false,
        })
      }else if(WRT_config.ypfjxx.xzfw_bbzt==2){
        let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.nbPNC_init['结算类型']}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=`
        // page_iframe.add("审批", url)
        // yzdata.splice(index, 1)
        WRT_e.ui.model({
          id: "ypsp",
          title: "医保限制支付范围提示",
          width: "650px",
          iframeURL: url,
          closeButton: false,
          closeOnEscape: false,
          iframe: true,
        })
        $("#ypsp iframe").load(function () {
          $("#ypsp iframe").contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
          $("#ypsp iframe").contents().find("#tb_brzd").text(WRT_config.nbPNC_init['诊断']);
        })
      }
      return;
    }
  } else {
    let xzfw_type=''
    if(WRT_config.ypfjxx.xzfw_bbzt==1){
      xzfw_type='[]'
    }else if(WRT_config.ypfjxx.xzfw_bbzt==2){
      xzfw_type=''
    }
    if (yp_list.XSP == "0" && WRT_config.ypfjxx.xzfw == xzfw_type) {
      if (yp_list.KZJB !=null && yp_list.KZJB.indexOf('自费') >= 0 && parseFloat(yp_list.XZDJ) > 2) {
        
        WRT_e.ui.message({
          title: '信息窗口',
          content: '该药为自费药品,请务必告知病人签字后方可使用!',
          onOk() {
            modalopen = 2
          }
        })
        return;
      }
    }
    else if((yp_list.XSP=='0'&&WRT_config.ypfjxx.xzfw!=xzfw_type)||(yp_list.XSP==null)||(WRT_config.ypfjxx.xzfw=='')){
      //无需审批
      exportMBList()
      return;
    }
    else {
      let as_sbxzfw = escape(WRT_config.ypfjxx.xzfw)
      // let as_brzd = escape(WRT_config.yz_sz.zd)
      if(WRT_config.ypfjxx.xzfw_bbzt==1){
        let html=ypsp_html(WRT_config.ypfjxx)
        WRT_e.ui.model({
          id: "if_ypsp",
          title: "医保限制支付范围提示",
          width: "650px",
          content: html,
          closeButton: false,
          closeOnEscape: false,
          iframe: false,
        })
      }else if(WRT_config.ypfjxx.xzfw_bbzt==2){
        let url = `${WRT_config.server}/xypyz/ypsp.aspx?as_jslx=${WRT_config.nbPNC_init['结算类型']}&as_splb=${WRT_config.ypfjxx.xsp}&as_sfzf=0&as_version=djm&as_rowindex=`
        // page_iframe.add("审批", url)
        WRT_e.ui.model({
          id: "ypsp",
          title: "医保限制支付范围提示",
          width: "650px",
          iframeURL: url,
          closeButton: false,
          closeOnEscape: false,
          iframe: true,
        })
        $("#ypsp iframe").load(function () {
          $("#ypsp iframe").contents().find("#tb_sbxzfw").text(WRT_config.ypfjxx.xzfw);//val
          $("#ypsp iframe").contents().find("#tb_brzd").text(WRT_config.nbPNC_init['诊断']);
        })
      }
      return
    }
  }
  exportMBList()
}
//药品审批html
function ypsp_html(ypfjxx){
  let text=''
  let zxfw=JSON.parse(ypfjxx.xzfw)
  WRT_config.XDFW_list=zxfw.map(function(item){
    return item.XDFW
  })
  // zxfw=JSON.parse('[{"XDFW":"(国版)限肝功能衰竭"},{"XDFW":"(国版)无法使用甘草酸口服制剂的患者"},{"XDFW":"(省版)限抢救"},{"XDFW":"(省版)限肝病"}]')
  let type=false
  let arr=[...zxfw,{XDFW:'均不符合，需自费，请告知患者'}]
  if(ypfjxx.xsp=='0'){
    text=''
    type=false
  }else if(ypfjxx.xsp=='1'){
    type=true
    text='该项目为需【医保窗口】审批项目。'
  }else if(ypfjxx.xsp=='2'){
    type=true
    text='该项目为需【药房】和【医保窗口】审批项目。'
  }else if(ypfjxx.xsp=='3'){
    type=true
    text='该项目为需【药房】审批项目。'
  }else if(ypfjxx.xsp){
    type=true
    text='该项目为需审批项目。'
  }
  // <textarea name="tb_sbxzfw" rows="2" cols="20" id="tb_sbxzfw" style="height:130px;width:369px;">${ypfjxx.xzfw}</textarea>
  let temp=`
    <div id="XDFW_table">
      <span style="font-weight:bold;">药品名称：${ypfjxx.ypmc}</span><br>
      <span id="Label1" style="font-weight:bold;">医保使用限制范围如下，请根据疾病诊断准确选择：</span><br>
      <ul style="height:130px;width:369px;border: 1px solid;overflow: auto;padding: 3px 5px;">
      ${_.map(arr,(item,index)=>`
      <li><input type="checkbox" name="xzfw_check" onclick="editCheckboxTrue(this)" value="${item.XDFW}"><span ${index==arr.length-1?'style="color:red"':''}>${item.XDFW}</sapn></li>
      `).join('')}
      </ul>
      <br>
      <span id="Label2" style="font-weight:bold;">病人诊断</span>&nbsp;<br>
      <textarea name="tb_brzd" rows="2" cols="20" id="tb_brzd" disabled="true" style="height:74px;width:367px;">${WRT_config.nbPNC_init['诊断']}</textarea><br>
      ${ypfjxx.xsp!=0?`<span id="lb_tsxx" style="display:inline-block;height:49px;width:372px;">${text}</span><br>`:''}
      <span style="display:inline-block;height:49px;width:372px;color:red">注：请根据患者病情如实勾选并在病历中体现！</span><br>
      <table style="width: 370px" cellpadding="0" cellspacing="0">
        <tbody>
          <tr id="tr_spxm">
            ${ypfjxx.xsp!=0&&ypfjxx.xsp?`<td style="width: 180px; height: 24px;padding: 3px 0;"><span id="Label3">申请审批数量</span>
              <input name="tb_spsl" type="text" id="tb_spsl" style="width:59px;border: 1px solid #736b6b;">
            </td>`:''}
          </tr>
          <tr align="center">
            <td style="width: 180px; height: 15px;" align="center">
              <button class="e_btn" value="确定" onclick="bt_click(1);return false;" id="bt_qd" style="width:160px;">${type?'确认提交审批':'确定'}</button>
            </td>
            <td style="width: 180px; height: 15px;" align="center">
              <button class="e_btn" value="取消" onclick="SetSpjg('',true);return false;" id="bt_qx" style="width:160px;">取消</button>
            </td>
          </tr>
        </tbody>
      </table>
      &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;
      <br>
    </div>
  `
  return temp
}

//审批确定
function bt_click(lx){
  var splb = WRT_config.ypfjxx.xsp;
  var obj = new Object();
  obj.SFZF = "";
  obj.SPLB = splb;
  let check=$('#XDFW_table input[type=checkbox]:checked')
  obj.xdfw=''
  if(check[0]){
      obj.xdfw=check[0].value
  }else if(check.length==0){
      alert("社保限制范围必须勾选");
      return
  }

  if (lx=='1')
  {
      if (splb == "0"){
          obj.SFZF = "0";
          if (obj.xdfw=='均不符合，需自费，请告知患者'){
              obj.SFZF = "1";
          }
      }
      else {
          if (splb != "3") {
              if (WRT_config.lclj_init['结算类型'] == "00")
                  obj.SFZF = "0";
              else {
                  if (WRT_config.XDFW_list.indexOf(obj.xdfw)>=0)
                      obj.SFZF = "0";
                  if (obj.xdfw=='均不符合，需自费，请告知患者')
                      obj.SFZF = "1";
              }
              if (obj.SFZF == "") {
                  alert("请选择 是否自费还是公费!");
                  return;
              }
          }
          else {
              obj.SFZF = 0;
          }
          var spsl = $("#tb_spsl")[0].value;
          if (spsl == "") 
          {
              alert("请输入审批数量");
              return;
          }
          if (isNaN(spsl)) 
          {
              alert("审批数量请输入数字!")
              return;
          }
          if (parseFloat(spsl) <= 0) 
          {
              alert("审批数量不能为零或负数!");
              return;
          }
          obj.SPSL = spsl;
      }
  }
  
  if (obj.SPSL == ""||obj.SPSL == undefined)
      obj.SPSL = 0;

  SetSpjg({sfzf: obj.SFZF, spsl: obj.SPSL, splb: obj.SPLB,xdfw:obj.xdfw},true)
}

//删除
function removeYp(zh, mbmc,ID) {
  if (dldr_lists.length > 0)
  {
    for (var i=dldr_lists.length-1; i>=0 ;i--)
    {
      if (dldr_lists[i].ZH == zh && dldr_lists[i].YOID == mbmc)
      {
        dldr_lists[i].DEL = "1";
      }
      else
        break;
    }
  }
  // dealYznr();
  exportMBList()
}

/********************视图********************/
// 基础数据
var BaseInfo_View = WRT_e.view.extend({
  render: function () {
    // 初始数据
    // <span class="tBinfoBcNum" style="padding: 1px 8px;">04-003</span>
    let name_CH= this.data['姓名床号'].split('/')
    let name= name_CH[0]
    let CH= name_CH[1]
    let html = `
			<span class="nbTBinfoR">
				<span style="color:#489CFF;padding-right:30px;font-weight: bolder;">●</span>
				<span class="tBinfoBold">${name}</span>
        <span class="tBinfoBcNum" style="padding: 1px 8px;">${CH}</span>
			</span>
		`
    this.$el.html(html)
    return this;
  }
})

// 计算器列表
var CountList_View = WRT_e.view.extend({
  render: function () {
    //  class="form-inline" role="form"
    // let html = `
    //   <div class="list1">
    //     ${this.getList1BodyHtml()}
    //   </div>
    // `
    let html = `
      <div class="list1">
        ${this.getList1BodyHtml()}
      </div>
      <div class="list2">
        ${this.getList2BodyHtml()}
      </div>
      <div class="list3">
        ${this.getList3BodyHtml()}
      </div>
		`
    this.$el.html(html)
    return this;
  },
  // 数据初始化
  getDateList:function() {
    // 初始化
    // getNutrition() // 获取药品信息
    let allData = {
      timesToEat: [ // 用餐频率（母乳）
        { label:'q2h', val:'2'},
        { label:'q3h', val:'3'},
        { label:'q4h', val:'4'},
        { label:'q6h', val:'6'},
        { label:'q8h', val:'8'},
        { label:'q12h', val:'12'},
      ],
      aminoAcidOption: [ // 氨基酸
        { label:'0.25', val:'0.25'},
        { label:'0.5', val:'0.5'},
        { label:'0.75', val:'0.75'},
        { label:'1', val:'1'},
        { label:'1.25', val:'1.25'},
        { label:'1.5', val:'1.5'},
        { label:'1.75', val:'1.75'},
        { label:'2', val:'2'},
        { label:'2.25', val:'2.25'},
        { label:'2.5', val:'2.5'},
        { label:'2.75', val:'2.75'},
        { label:'3', val:'3'},
        { label:'3.25', val:'3.25'},
        { label:'3.5', val:'3.5'},
      ],
      lipidEmulsion: [ // 脂肪乳
        { label:'0', val:'0'},
        { label:'0.25', val:'0.25'},
        { label:'0.5', val:'0.5'},
        { label:'0.75', val:'0.75'},
        { label:'1', val:'1'},
        { label:'1.25', val:'1.25'},
        { label:'1.5', val:'1.5'},
        { label:'1.75', val:'1.75'},
        { label:'2', val:'2'},
        { label:'2.25', val:'2.25'},
        { label:'2.5', val:'2.5'},
        { label:'2.75', val:'2.75'},
        { label:'3', val:'3'},
        { label:'3.25', val:'3.25'},
        { label:'3.5', val:'3.5'},
      ],
      NPTime: [ // PN时间
        { label:'24', val:'24'},
        { label:'20', val:'20'},
        { label:'16', val:'16'},
        { label:'12', val:'12'},
      ],
      mealsNum: [ //  餐数（顿下拉框）第一个q2h // getList1BodyHtml
        { label:'', val:'', sel:'selected'},
        { label:'1', val:'1'},
        { label:'2', val:'2'},
        { label:'3', val:'3'},
        { label:'4', val:'4'},
        { label:'5', val:'5'},
        { label:'6', val:'6'},
        { label:'7', val:'7'},
        { label:'8', val:'8'},
        { label:'9', val:'9'},
        { label:'10', val:'10'},
        { label:'11', val:'11'},
        { label:'12', val:'12'},
      ],
      capacityCoe: [ // 容量系数
        { label:'1.0', val:'1.0'},
        { label:'1.1', val:'1.1'},
        { label:'1.2', val:'1.2'},
        { label:'1.3', val:'1.3'},
        { label:'1.4', val:'1.4'},
      ]
    }
    
    // // 得到奶类型 和医嘱频率
    // allData.TypeOfMilk = WRT_config.TypeOfMilk
    // // 得到药品  Nutrition
    // allData.Nutrition = WRT_config.Nutrition
    // // 根据医嘱单id得到相应的药品  NutriByYzdid
    // allData.NutriByYzdid = WRT_config.NutriByYzdid
    // // 根据医嘱单ID获取医嘱单信息
    // allData.YZDByYzdid = WRT_config.YZDByYzdid
    // // 得到前一份医嘱单的所填数据  PreviousYZDByBlid
    // allData.PreviousYZDByBlid = WRT_config.PreviousYZDByBlid
    // // 获取前一天8点到今天8点的尿液量
    // allData.Urine = WRT_config.Urine
    

    // 得到奶类型 和医嘱频率
    // allData.TypeOfMilk = WRT_config.TypeOfMilk
    WRT_e.api.pnOrderList.getTypeOfMilk({
      params: {
        ll_blid: param['as_blid'],
      },
      success(data) {
        if (data.d.Code == 1) {
          WRT_config.TypeOfMilk = JSON.parse(data.d.Result)
          allData.TypeOfMilk  = WRT_config.TypeOfMilk.filter(e=>e.MC.indexOf('母乳')!=-1)
          allData.TypeOfMilk1  = WRT_config.TypeOfMilk.filter(e=>e.MC.indexOf('母乳')==-1)
          
        }
      }
    })
    // 获取前一天8点到今天8点的尿液量
    // allData.Urine = WRT_config.Urine
    WRT_e.api.pnOrderList.getUrine({
      params: {
        as_yzdid: param['as_yzdid'],
        as_blid: param['as_blid'],
      },
      success(data) {
        if (data.d.Code == 1) {
          if(data.d.Result!=null){
            WRT_config.Urine = JSON.parse(data.d.Result)
            allData.Urine = WRT_config.Urine
            urineCallBack( allData.Urine)
          } else {
            allData.Urine = []
          }
        }
      }
    })
    if (!stringISEmpty(param['as_yzdid']) && param['as_yzdid']!=0) {
      // 根据医嘱单ID获取医嘱单信息
      // allData.YZDByYzdid = WRT_config.YZDByYzdid
      WRT_e.api.pnOrderList.getYZDByYzdid({
        params: {
          as_yzdid: param['as_yzdid'],
          // ll_blid: param['as_blid'],
        },
        success(data) {
          if (data.d.Code == 1) {
            WRT_config.YZDByYzdid = JSON.parse(data.d.Result)
            allData.YZDByYzdid = WRT_config.YZDByYzdid
          
          }
        }
      })

      // 根据医嘱单id得到相应的药品  NutriByYzdid
      // allData.NutriByYzdid = WRT_config.NutriByYzdid
      WRT_e.api.pnOrderList.getNutritionByYzdid({
        params: {
          as_yzdid: param['as_yzdid'],
          ll_blid: param['as_blid'],
        },
        success(data) {
          if (data.d.Code == 1) {
            WRT_config.NutriByYzdid = JSON.parse(data.d.Result)
            allData.NutriByYzdid = WRT_config.NutriByYzdid
          }
        }
      })
    }
    else {
      // 得到前一份医嘱单的所填数据  PreviousYZDByBlid(与初始化返回数据格式一致)
      // allData.PreviousYZDByBlid = WRT_config.PreviousYZDByBlid
      WRT_e.api.pnOrderList.getPreviousYZDByBlid({
        params: {
          // as_yzdid: param['as_yzdid'],
          as_blid: param['as_blid'],
        },
        success(data) {
          if (data.d.Code == 1) {
            WRT_config.PreviousYZDByBlid = JSON.parse(data.d.Result)
            allData.PreviousYZDByBlid =  WRT_config.PreviousYZDByBlid
          }
        }
      })

      // 得到药品  Nutrition
      // allData.Nutrition = WRT_config.Nutrition
      WRT_e.api.pnOrderList.getNutrition({
        params: {
          as_yfdm: this.data.nbPNCInfo['药房代码'],
          ll_blid: param['as_blid'],
        },
        success(data) {
          if (data.d.Code == 1) {
            WRT_config.Nutrition = JSON.parse(data.d.Result)
            allData.Nutrition = WRT_config.Nutrition
          }
        }
      })
    }
    return allData
  },
  getList1BodyHtml:function(){
    var obj = this.getDateList()
    // 基础数据
    let list1Data = obj.YZDByYzdid?obj.YZDByYzdid.ly_ekpnyzd : obj.PreviousYZDByBlid
    // Pn液量计算（初始化）
    var weight = parseFloat(list1Data[0].Weight);
    var allDay = parseFloat(list1Data[0].AllDayFluidV);
    var treatFluid = parseFloat(list1Data[0].TreatFluid);
    var milk = 0;
    if (!stringISEmpty(list1Data[0].TypeOfMilk)) {
      milk += parseFloat(list1Data[0].MilkNum) * parseFloat(list1Data[0].MealOfMilk);
      if (!stringISEmpty(list1Data[0].TypeOfMilk1)) {
        milk += parseFloat(list1Data[0].MilkNum) * parseFloat(24 / list1Data[0].TimesToEat - list1Data[0].MealOfMilk);
      }
    }
    // PN液量
    var reslSum = weight * allDay - treatFluid - milk;
    reslSum = Math.round(reslSum);
    // PN泵速
    //PN液量÷PN时间
    var pnPS
    if (stringISEmpty(reslSum) || stringISEmpty(list1Data[0].PnTime)) {
      pnPS = ''
    }
    else {
      var pnPS  = parseFloat(reslSum) / parseFloat(list1Data[0].PnTime);
      pnPS = Math.round(pnPS * 10) / 10
      // $("#PNpumpSpeed").val(Math.round(sum * 10) / 10);
    }
   
    // || obj.PreviousYZDByBlid
    console.log(888,list1Data,(list1Data[0].Ztbz=='2' || WRT_config.nbPNC_init['是否出院']=='1') && param['as_yzdid']!=0)
    // value="${obj.YZDByYzdid.ly_ekpnyzd[0].AllDayFluidV}"  .ly_ekpnyzd[0].Weight
    return `
      <div class="form-group">
        <label>输注方式</label>
        <select id="sel_method">
            <option value="0" ${list1Data&&list1Data[0].SelectedMethod==0?`selected='selected'`:''}>中心静脉</option>
            <option value="1" ${list1Data&&list1Data[0].SelectedMethod==1?`selected='selected'`:''}>外周静脉</option>
        </select>
      </div>

      <div class="form-group">
        <label>体重</label>
        <span class="inputLine">
          <input 
            type="number"
            class="form-control" 
            id="weight" 
            min="0.00" 
            step="0.01"
            value="${list1Data?list1Data[0].Weight:''}"
          />
          <span style="padding:0 8px">kg</span>
        </span>
        <span>最近有效值：<span id="lab_xtz">  ${this.data.nbPNCInfo['有效体重']}</span></span>
      </div>

      <div class="form-group">
        <label>
          <select id="typeOfMilk">
            <option value=""></option>
            ${_.map(obj.TypeOfMilk,(item,index)=>`
              <option ${list1Data && list1Data[0].TypeOfMilk == index+1 ?`selected='selected'`:''} value="${item.BZ}">${item.MC}</option>
            `)}
          </select>
        </label>
        <span class="inputLine">
          <input 
            type="number" 
            class="form-control" 
            id="milkNum" 
            min="0"
            value="${list1Data?list1Data[0].MilkNum || '':''}"
          />
          <span style="padding:0 2px 0 4px">ml/次</span>
        </span>
        <span>
          <select id="timesToEat">
            ${_.map(obj.timesToEat,(item)=>`
              <option value="${item.val}" ${list1Data && list1Data[0].TimesToEat==item.val?`selected='selected'`:``}>${item.label}</option>
            `)}
          </select>
          <select id="mealOfMilk">
            ${this.getList1BodymealOfMilkHtml(obj.mealsNum)}
          </select> 顿
        </span>
      </div>
      
      <div class="form-group">
        <label>
          <select id="typeOfMilk1">
            <option value=""></option>
            ${_.map(obj.TypeOfMilk1,(item,index)=>`
              <option ${list1Data && list1Data[0].TypeOfMilk1 == index+1 ?`selected='selected'`:''} value="${item.BZ}">${item.MC}</option>
            `)}
          </select>
        </label>
        <span class="inputLine">
          <input 
            id="milkNum1"
            readonly 
            type="text" 
            class="form-control"
            value="${list1Data && list1Data[0].TypeOfMilk1!=0?list1Data[0].MilkNum || '':''}"
          />
          <span style="padding:0 2px">ml/次</span>
        </span>
        <span class="inputLine">
          <select id="timesToEat1" disabled="disabled">
            ${_.map(obj.timesToEat,(item)=>`
              <option value="${item.val}" ${(list1Data && list1Data[0].TypeOfMilk1!=0) && list1Data[0].TimesToEat==item.val?`selected='selected'`:``}>${item.label}</option>
            `)}
          </select>
          <input 
            id="mealOfMilk1" 
            readonly 
            type="text" 
            class="form-control" 
            style="width:41px;margin: 0 4px;" 
            value="${list1Data && list1Data[0].TypeOfMilk1!=0?(24 / list1Data[0].TimesToEat-list1Data[0].MealOfMilk):''}"/>
          顿
        </span>
      </div>

      <div class="form-group">
        <label>全天每kg总液量</label>
        <input 
          type="text" 
          class="form-control" 
          id="allDayFluidV"
          value="${list1Data?list1Data[0].AllDayFluidV || '':''}"
        />
        <span style="padding:0 4px">ml/kg</span>
      </div>

      <div class="form-group">
        <label>治疗液体量</label>
        <input 
          type="text" 
          class="form-control" 
          id="treatFluid" 
          value="${list1Data?list1Data[0].TreatFluid || '':''}"
        />
        <span style="padding:0 4px">ml</span>
      </div>

      <div class="form-group">
        <label>氨基酸</label>
        <select id="aminoAcid" >
          ${_.map(obj.aminoAcidOption,(item)=>`
            <option value="${item.val}" ${list1Data && list1Data[0].AminoAcid == item.val?`selected='selected'`:``}>${item.label}</option>
          `)}
        </select>
        <span style="padding:0 4px">g/(kg*d)</span>
      </div>

      <div class="form-group">
        <label>乳脂肪</label>
        <select id="lipidEmulsion">
          ${_.map(obj.lipidEmulsion,(item)=>`
            <option value="${item.val}" ${list1Data && list1Data[0].LipidEmulsion == item.val?`selected='selected'`:``}>${item.label}</option>
          `)}
        </select>
        <span style="padding:0 4px">g/(kg*d)</span>
      </div>

      <div class="form-group">
        <label>PN糖速</label>
        <input 
          id="pnTS" 
          type="text" 
          class="form-control"
          value="${list1Data?list1Data[0].PnTs || '':''}"
        />
        <span style="padding:0 4px">mg/(kg*min)</span>
      </div>

      <div class="form-group">
        <label>PN中钠量</label>
        <input 
          type="text" 
          class="form-control" 
          name="microelement"
          id="NaInPN" 
          value="${list1Data?`${list1Data[0].Microelement.split('_')[0]}`:``}"
        />
        <span style="padding:0 4px">mmol/(kg*d)</span>
      </div>

      <div class="form-group">
        <label>PN中钾量</label>
        <input 
          type="text" 
          class="form-control" 
          name="microelement"
          id="KInPN"
          value="${list1Data?`${list1Data[0].Microelement.split('_')[1]}`:``}"
        />
        <span style="padding:0 4px">mmol/(kg*d)</span>
      </div>

      <div class="form-group">
        <label>PN中磷量</label>
        <input 
          type="text" 
          class="form-control" 
          name="microelement"
          id="PInPN"
          value="${list1Data?`${list1Data[0].Microelement.split('_')[2]}`:``}"
        />
        <span style="padding:0 4px">mmol/(kg*d)</span>
      </div>

      <div class="form-group">
        <label>PN中镁量</label>
        <input 
          type="text" 
          class="form-control" 
          name="microelement"
          id="MgInPN"
          value="${list1Data?`${list1Data[0].Microelement.split('_')[3]}`:``}"
        />
        <span style="padding:0 4px">mmol/(kg*d)</span>
      </div>

      <div class="form-group">
        <label>PN时间</label>
        <span class="inputLine">
          <select id="pnTime">  
          ${_.map(obj.NPTime,(item)=>`
            <option value="${item.val}" ${list1Data && list1Data[0].PnTime == item.val?`selected='selected'`:``}>${item.label}</option>
          `)}
          </select> 小时
          <span style="padding: 0 4px">PN泵值</span>
          <input 
            readonly 
            type="text" 
            class="form-control" 
            id="PNpumpSpeed"
            value="${list1Data? pnPS : ''
              // list1Data && (!stringISEmpty(list1Data[0].PnFluidV) && !stringISEmpty(list1Data[0].PnTime))? pnPS || Math.round((list3Data[0].PnFluidV/list3Data[0].PnTime)*10)||list1Data[0].PNBZ || '':''
            }"
          />
          <span style="padding:0 2px 0 4px">ml/h</span>
        </span>
      </div>

      <div class="form-group">
        <label>实际提交的液量</label>
        <input 
          readonly 
          type="text" 
          class="form-control" 
          id="realFluidV"
          value="${reslSum * parseInt(list1Data[0].COE*10)/10}"
        />
        <span style="padding:0 4px">ml</span>
      </div>
      <div id="saveTR" ${param['as_yzdid']!=0 && ((list1Data[0].Ztbz=='2' && obj.YZDByYzdid.m_yl_zyypyz!=null) || WRT_config.nbPNC_init['是否出院']=='1')?`style='display: none;'`:``} style="padding-left:5px;float:left" >
        <button type="submit" class="btn btn_save" style="color: #fff" onclick="save_ekpnyy()">保存</button>
        <button type="submit" class="btn btn_save" style="color: #fff" onclick="calculate()">计算</button>
      </div>
    `
  },
  getList2BodyHtml:function(){
    var obj = this.getDateList()
    let list2Data = obj.YZDByYzdid?obj.YZDByYzdid.ly_ekpnyzd : obj.PreviousYZDByBlid
    // ${this.getList3BodyInnerHtml()}
    // <input class="easyui-datetimebox Btn_nrInput" id="dtm_txrq" value="${new Date().toLocaleString()}" type="datetime-local"/>
    // ${this.getList3BodyInnerHtml().indexOf(',')!=-1?`${this.getList3BodyInnerHtml().replace(/,/g, '')}`:`${this.getList3BodyInnerHtml()}`}
    return `
      <div id="medicalTable">
        ${this.getList2BodyInnerHtml().indexOf(',')!=-1?`${this.getList2BodyInnerHtml().replace(/,/g, '')}`:`${this.getList2BodyInnerHtml()}`}
      </div>
      <div class="form-group">
        <label>医嘱开始时间</label>
        <span>
        <input class="easyui-datetimebox Btn_nrInput" id="dtm_txrq" value="${((list2Data[0].Ztbz=='2' && obj.YZDByYzdid.m_yl_zyypyz!=null) || WRT_config.nbPNC_init['是否出院']=='1') && param['as_yzdid']!=0?obj.YZDByYzdid.m_yl_zyypyz.KSSJ.replace("T", " "):getTodayTime()}" type="datetime-local"/>
        </span>
      </div><div class="form-group">
        <label>
          <span style="padding-right: 4px">持续输液</span>
          <input type="text" class="easyui-numberbox form-control" ${((list2Data[0].Ztbz=='2' && obj.YZDByYzdid.m_yl_zyypyz!=null) || WRT_config.nbPNC_init['是否出院']=='1') && param['as_yzdid']!=0?` disabled="disabled" value='${obj.YZDByYzdid.m_yl_zyypyz.CXTS}' `:`value=''`} id="cxsyts">
          <span style="padding: 0 4px;font-weight:400;">天</span>
        </label>
        <span>
          <span style="padding-right: 4px">用药频率</span>
          <select id="yypl" disabled="disabled">
            <option value="st">st</option>
          </select>
        </span>
      </div>

      <div class="form-group" id="TJTR" ${((list2Data[0].Ztbz=='2' && obj.YZDByYzdid.m_yl_zyypyz!=null) || WRT_config.nbPNC_init['是否出院']=='1') && param['as_yzdid']!=0?`style='display: none;'`:``} >
        <label>用法:营养静脉滴注</label>
      </div>

      <div class="form-group" ${((list2Data[0].Ztbz=='2' && obj.YZDByYzdid.m_yl_zyypyz!=null) || WRT_config.nbPNC_init['是否出院']=='1') && param['as_yzdid']!=0?`style='display: none;'`:``} >
        <button type="submit" class="btn btn_save"style="color: #fff" onclick="SaveYPYZ()">提交医嘱</button>
      </div>
    `
  },
  getList3BodyHtml:function(){
    var obj = this.getDateList()
    let list3Data = obj.YZDByYzdid?obj.YZDByYzdid.ly_ekpnyzd : obj.PreviousYZDByBlid
    // 数据（初始化）
    // Pn液量计算（初始化）
    var weight = parseFloat(list3Data[0].Weight);
    var allDay = parseFloat(list3Data[0].AllDayFluidV);
    var treatFluid = parseFloat(list3Data[0].TreatFluid);
    var milk = 0;
    if (!stringISEmpty(list3Data[0].TypeOfMilk)) {
      milk += parseFloat(list3Data[0].MilkNum) * parseFloat(list3Data[0].MealOfMilk);
      if (!stringISEmpty(list3Data[0].TypeOfMilk1)) {
        milk += parseFloat(list3Data[0].MilkNum) * parseFloat(24 / list3Data[0].TimesToEat - list3Data[0].MealOfMilk);
      }
    }
    var PnSum = weight * allDay - treatFluid - milk;
    // 糖浓度 = PN糖速X体重X60X24÷1000÷PN液量X100
    var sum = parseFloat(list3Data[0].PnTs) * parseFloat(weight) * 60 * 24 / 1000 / parseFloat(PnSum) * 100;
    if (sum >= 25) {
      // $.messager.alert("提示", "糖浓度要小于25%", "info");
      WRT_e.ui.message({
        title: '提示',
        content: `糖浓度要小于25%`,
        onOk() {
        },
      })
    }
      
    if(param['as_yzdid']!=0){
      // REE值
      var REEVal
      if (this.data.nbPNCInfo['病人性别'] == "2") {
        REEVal = (58.3 * weight - 31).toFixed(1)
        // $("#REE").val((58.3 * weight - 31).toFixed(1));
      }
      else {
        REEVal = (59.5 * weight - 30).toFixed(1)
      }
      // 奶能量
      let typeOfMilk,typeOfMilk1
      typeOfMilk = obj.TypeOfMilk.filter((e,i)=>list3Data[0].TypeOfMilk == i+1).length!=0?obj.TypeOfMilk.filter((e,i)=>list3Data[0].TypeOfMilk == i+1)[0].BZ:''
      typeOfMilk1 = obj.TypeOfMilk1.filter((e,i)=>list3Data[0].TypeOfMilk1 == i+1).length!=0?obj.TypeOfMilk1.filter((e,i)=>list3Data[0].TypeOfMilk1 == i+1)[0].BZ:''
      var MilkEnergy =  setMilkEnergy(
        typeOfMilk,
        list3Data[0].MealOfMilk,
        list3Data[0].MilkNum,
        typeOfMilk1,
        list3Data[0].TypeOfMilk1!=0?(parseFloat(24 / list3Data[0].TimesToEat - list3Data[0].MealOfMilk)):'null',
        list3Data[0].TypeOfMilk1!=0?list3Data[0].MilkNum:'null'
      )
        
      // PN能量
      var pnEnergySum = 0
      pnEnergySum += parseFloat(list3Data[0].PnTs) * weight * 60 * 24 / 1000 * 4;
      // pnEnergySum += parseFloat(0.25) * weight * 4;
      pnEnergySum += parseFloat(list3Data[0].AminoAcid) * weight * 4;
      pnEnergySum += parseFloat(list3Data[0].LipidEmulsion) * weight * 9;
      var pnEnergy = Math.round(pnEnergySum * 10) / 10
      
      // 总能量
      var AllEnergyValue = Math.round((parseFloat(MilkEnergy) + parseFloat(pnEnergy)) * 10) / 10
      
      // 每kg能量
      var totalEnergy = Math.round(((parseFloat(MilkEnergy) + parseFloat(pnEnergy)) / weight) * 10) / 10

      // 求糖能量   AA能量 Fat能量 氨基酸浓度 脂肪乳浓度 PN热氮比  <糖能量 和 FAT能量 (这两个数据不显示)>
      // 糖脂比
      var SugarValue = 0; // 糖能量 
      var AAValue = 0; // AA能量
      var FATValue = 0; // Fat能量
      var AAND,FATND,ajsl,zfrl // 氨基酸浓度
      var SugarAndFat // 糖脂比
      var pnRDB// PN热氮比
      // PnFluidV == PnSum
      if(PnSum == 0){
        AAND = '' // 氨基酸浓度
        FATND = ''// 脂肪浓度
      } else {
        AAND = 0
        //氨基酸量
        ajsl = list3Data[0].AminoAcid
        //脂肪乳
        zfrl = list3Data[0].LipidEmulsion
        //氨基酸量X体重÷PN液量X100%
        AAND = Math.round(ajsl * weight / PnSum * 1000) / 10;
        // 脂肪浓度 = 脂肪乳量X体重÷PN液量X100%
        FATND = Math.round(zfrl * weight / PnSum * 1000) / 10;
        if (zfrl > 0) {
          if(AAND<2){
            WRT_e.ui.message({
              title: '提示',
              content: `氨基酸浓度要大于2%!`,
              onOk() {}
            })
            AAND = ''
          }
          if(AAND >= 3.5){
            WRT_e.ui.message({
              title: '提示',
              content: `氨基酸浓度要小于3.5%!`,
              onOk() {}
            })
            AAND = ''
          }
          if(FATND < 1){
            WRT_e.ui.message({
              title: '提示',
              content: `脂肪浓度要大于1%!`,
              onOk() {}
            })
            FATND= ''
          }
        }
      }
      if(pnEnergy == 0){
        SugarValue = '0%'
        AAValue = '0'
        FATValue = '0%'
      } else {
        SugarValue = parseFloat(list3Data[0].PnTs) * weight * 60 * 24 / 1000 * 4;
        FATValue = parseFloat(list3Data[0].LipidEmulsion) * weight * 9;
        pnRDB = Math.round((SugarValue + FATValue) / weight * 6.25);
        SugarValue = SugarValue / pnEnergy;
        //糖质量X4÷PN能量*100%
        SugarValue = Math.round(SugarValue * 100);

        FATValue = FATValue / pnEnergy;
        //脂肪质量X9÷PN能量*100%
        FATValue = Math.round(FATValue * 100);  // $("#fatEnergy").val(FATValue + "%");
        SugarAndFat = Math.round(100 * SugarValue / (SugarValue + FATValue)) + ":" + Math.round(100 * FATValue / (SugarValue + FATValue))
        
        // SugarValue = SugarValue + "%"
        // FATValue = FATValue + "%"   // $("#fatEnergy").val(FATValue + "%");
        pnRDB = pnRDB.toString() + ":1"
      }
    }
    // 渗透压
    // var input_index = $("input[name='input_index']");
    // var input_type = $("input[name='input_type']");
    // var input_num = $("input[name='input_num']");
    var input_num = ypInfo
    var stySum = 0;
    var pstyIndex = 0;
    // var objNow = obj.NutriByYzdid || obj.Nutrition  // objNow.lst_yfyp==ypInfo
    for (var i = 0 ; i < ypInfo.length; i++) {
      switch (ypInfo[i].YPTYPE) {
        case '1':
          break;
        case '3':
          //求脂肪
          pstyIndex = i
          stySum += parseFloat(ypInfo[pstyIndex].YYSL) * parseFloat(ypInfo[pstyIndex].STY);
          // pstyIndex = parseInt($("#select_ZFYP").val());
          // stySum += parseFloat(input_num.eq(i).val()) * parseFloat(ypInfo[pIndex].STY);
          break;
        default:
          pstyIndex = i
          if (parseFloat(ypInfo[pstyIndex].YYSL) >= 0) {
            stySum += parseFloat(ypInfo[pstyIndex].YYSL) * parseFloat(ypInfo[pstyIndex].STY);
          }
          break;
      }
    }
    stySum = stySum / PnSum;
    stySum =  Math.round(stySum);
    // if (stySum > 300) {
    //   if (list3Data[0].SelectedMethod == '1') {
    //       if (sstySumm > 850) {
    //         WRT_e.ui.message({
    //           title:'提示',
    //           content:'外周静脉的渗透压不能超过850!',
    //           onOK(){}
    //         })
    //       }
    //   }
    // }
    // else {
    //   WRT_e.ui.message({
    //     title:'提示',
    //     content:'渗透压不能小于300!',
    //     onOK(){}
    //   })
    // }
    // 一价离子浓度
    //[(体重XPN钠量)+(体重XPN钾量)]÷ PN液量X1000
    var Na = 0;
    if (!stringISEmpty(list3Data[0].Microelement.split('_')[0])) {
      Na = parseFloat(list3Data[0].Microelement.split('_')[0]);
    }
    var K = 0;
    if (!stringISEmpty(list3Data[0].Microelement.split('_')[1])) {
      K = parseFloat(list3Data[0].Microelement.split('_')[1]);
    }
    var MonovalentVal = weight * (Na + K) / PnSum * 1000;
    if (MonovalentVal >= 150) {
      WRT_e.ui.message({
        title:'提示',
        content:'一价离子浓度必须小于150!',
        onOK(){}
      })
    }
    var Monovalent = Math.round(MonovalentVal * 10) / 10

    // 二价离子浓度
    //[(体重XPN钙量)+(体重XPN镁量)]÷ PN液量X1000
    var Mg = 0;
    if (!stringISEmpty(list3Data[0].Microelement.split('_')[2])) {
      Mg = parseFloat(list3Data[0].Microelement.split('_')[2]);
    }
    /*  var Ca = 0;
      if (!stringISEmpty($("#CaInPN").val())) {
          Ca = parseFloat($("#CaInPN").val());
      }
      parseFloat($("#CaInPN").val());*/
    var divalentVal = weight * Mg / PnSum * 1000;
    if (divalentVal >= 10) {
      WRT_e.ui.message({
        title:'提示',
        content:'二价离子浓度必须小于10!',
        onOK(){}
      })
    }
    divalent = Math.round(divalentVal * 10) / 10
    //  || obj.PreviousYZDByBlid PnSum,sum,MilkEnergy,totalEnergy,
    return `
    <form>
      <div class="form-group">
        <label>REE</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            class="form-control" 
            name="input_Attributes"
            id="REE"
            value="${REEVal==0?'0':REEVal || ''}"
          />
          <span style="padding: 0 4px">kcal/d</span>
        </span>
      </div>
      
      <div class="form-group">
        <label>总能量</label>
        <span class="inputLine">
          <input 
            readonly
            type="text" 
            class="form-control" 
            name="input_Attributes"
            id="allEnergy"
            value="${AllEnergyValue || ''}"
          />
          <span style="padding: 0 4px">kcal</span>
        </span>
      </div>
      
      <div class="form-group">
        <label>奶能量</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text"
            class="form-control" 
            name="input_Attributes"
            id="milkEnergy"
            value="${MilkEnergy==0?'0':MilkEnergy || ''}"
          />
          <span style="padding: 0 4px">kcal</span>
        </span>
      </div>
      
      <div class="form-group">
        <label>PN能量</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            name="input_Attributes"
            class="form-control" 
            id="pnEnergy"
            value="${pnEnergy==0?'0':pnEnergy || ''}"
          />
          <span style="padding: 0 4px">kcal</span>
        </span>
      </div>

      <div class="form-group">
        <label>每kg能量</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            class="form-control"
            name="input_Attributes"
            id="totalEnergy"
            value="${list3Data[0].Weight==0?'':`${totalEnergy || ''}`}"
          />
          <span style="padding: 0 4px">kcal/kg</span>
        </span>
      </div>
      
      <div class="form-group">
        <label>氨基酸</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            name="input_Attributes"
            class="form-control"
            id="aaEnergy"
            value="${ajsl || ''}"
          />
          <span style="padding: 0 4px">g/(kg*d)</span>
        </span>
      </div>
      
      <div class="form-group" style="display: none;">
        <label>糖能量</label>
        <span>
          <input 
            readonly 
            type="text"
            class="form-control" 
            name="input_Attributes"
            id="sugarEnergy"
            value="${pnEnergy==0?'0%':SugarValue}"
          />
        </span>
      </div>
      
      <div class="form-group" style="display: none;">
        <label>FAT能量</label>
        <span>
          <input 
            readonly 
            type="text"
            class="form-control" 
            name="input_Attributes"
            id="fatEnergy"
            value="${FATValue?FATValue:''}"
          />
        </span>
      </div>
      
      <div class="form-group">
        <label>糖脂比</label>
        <span>
          <input 
            readonly 
            type="text"
            class="form-control" 
            name="input_Attributes"
            id="sugarAndfat"
            value= "${SugarAndFat || ''}"
          />
        </span>
      </div>
      
      <div class="form-group">
        <label>PN热氮比</label>
        <span>
          <input 
            readonly
            type="text"
            class="form-control"
            name="input_Attributes"
            id="pnRDB"
            value="${pnRDB || ''}"
          />
        </span>
      </div>

      <div class="form-group">
        <label>渗透压</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            class="form-control"
            name="input_Attributes"
            id="osmolality"
            value="${stySum || ''}"
          />
          <span style="padding: 0 4px">mOsm/L</span>
        </span>
      </div>

      <div class="form-group">
        <label>糖浓度</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            class="form-control" 
            id="text_tnd"
            value="${list3Data && sum < 25?`${Math.round(sum * 10) / 10}`:``}"
          />
          <span style="padding: 0 4px">%</span>
        </span>
      </div>
      
      <div class="form-group">
        <label>氨基酸浓度</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            class="form-control" 
            name="input_Attributes" 
            id="AAND"
            value="${AAND==0?'0':`${AAND || ''}`}"
          />
          <span style="padding: 0 4px">%</span>
        </span>
      </div>
      
      <div class="form-group">
        <label>脂肪浓度</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            class="form-control"
            name="input_Attributes"
            id="FATND"
            value="${FATND==0?'0':`${FATND || ''}`}"
          />
          <span style="padding: 0 4px">%</span>
        </span>
      </div>
      
      <div class="form-group">
        <label>一价离子浓度</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            class="form-control"
            name="input_Attributes"
            id="monovalent"
            value="${Monovalent==0?'0':Monovalent || ''}"
          />
          <span style="padding: 0 4px">mmol/L</span>
        </span>
      </div>
      
      <div class="form-group">
        <label>二价离子浓度</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            class="form-control"
            name="input_Attributes"
            id="divalent"
            value ="${divalent==0?'0':divalent || ''}"
          />
          <span style="padding: 0 4px">mmol/L</span>
        </span>
      </div>

      <div class="form-group">
        <label>PN液量</label>
        <span class="inputLine">
          <input 
            readonly 
            type="text" 
            class="form-control"
            name="input_Attributes"
            id="PnFluidV"
            value="${
              list3Data && !(stringISEmpty(list3Data[0].TreatFluid) || stringISEmpty(list3Data[0].AllDayFluidV) || stringISEmpty(list3Data[0].Weight) || stringISEmpty(list3Data[0].Weight) || (!stringISEmpty(list3Data[0].TypeOfMilk) && (stringISEmpty(list3Data[0].MilkNum) || stringISEmpty(list3Data[0].MealOfMilk))))?PnSum:``}"
          />
          <span style="padding: 0 4px">ml</span>
        </span>
      </div>
      
      <i class="glyphicon glyphicon-question-sign tipIcon"></i>
    </from>
    `
  },
  
  // 奶量顿数变化
  getList1BodymealOfMilkHtml:function(milkMeal){
    let list1mealOfMilk = WRT_config.YZDByYzdid?WRT_config.YZDByYzdid.ly_ekpnyzd : WRT_config.PreviousYZDByBlid
    return `
    ${_.map(milkMeal,(item)=>`
      <option ${list1mealOfMilk && list1mealOfMilk[0].MealOfMilk==item.val?`selected='selected'`:'' } value="${item.val}">${item.label}</option>
    `)}`
  },
  // 第二列药品数据展示
  getList2BodyInnerHtml:function(){
    var obj = this.getDateList()
    var objNow = obj.NutriByYzdid || obj.Nutrition
    var rlxsObj = obj.YZDByYzdid?obj.YZDByYzdid.ly_ekpnyzd : obj.PreviousYZDByBlid
    var data = objNow.lst_yfyp;
    ypInfo = objNow.lst_yfyp
    var str = "";
    var j = 0;
    ypInfo.map((item,i)=>{
      if(item.YPTYPE == '1') {
        str += `
        <div class='form-group'>
          <label>
            <select id="selected_WSS" name='selected_WSS'  onchange="calculate()" >
        `
        str += `
              <option value= ''>有</option>
              <option value='0'>无</option>
            </select>
            <span name='input_ypmc'>${item.MC}</span>
            <input type='hidden' class='form-control' value='${i}'  name='input_index' />
            <input type='hidden' class='form-control' name='input_type' value='${item.YPTYPE}'  />
          </label>
        `
        str += `
          <span class='inputLine'>
            <input 
              readonly 
              class='form-control num${i}' 
              type='text' 
              name='input_num' 
              id="name"
              value="${item.YYSL || item.YYSL==0?item.YYSL : ''}"
            />
            <span style="padding: 0 4px">${item.JLDW}</span>
          </span>
        </div>`
      } else if(item.YPTYPE == '3') {
        str += `
        <div class='form-group'>
          <label>
            <select  id='select_ZFYP' onchange="calculate()">
        `
        for (j = i; j < data.length && data[j].YPTYPE == '3' ; j++) {
          str += `<option value= '${j}'>${data[j].MC}</option>`
        }
        str += `
            </select>
            <input class='form-control' type='hidden'  name='input_index'  />
            <input class='form-control' type='hidden' name='input_type'  value='${data[i].YPTYPE}'  />
          </label>
        `
        str += `
          <span class='inputLine'>
            <input 
              readonly 
              class='form-control num${i}' 
              type='text' 
              name='input_num' 
              id="name" 
              value="${data[i].YYSL || data[i].YYSL==0?data[i].YYSL : ''}" 
            />
            <span style="padding: 0 4px"> ${data[i].JLDW}</span>
          </span>
        </div>`
        i = j > i ? j - 1 : i;
      } else if(item.YPTYPE == '7') {
        str += `
        <div class='form-group'>
          <label>
            <select  id='selected_ZKND' onchange="calculate()">
        `
        str += `
              <option value= ''>有</option>
              <option value='0'>无</option>
            </select>
            <span name='input_ypmc'>${item.MC}</span>
            <input type='hidden' class='form-control' value='${i}'  name='input_index' />
            <input type='hidden' class='form-control' name='input_type' value='${item.YPTYPE}'  />
          </label>
        `
        str += `
          <span class='inputLine'>
            <input 
              readonly 
              class='form-control num${i}' 
              type='text' 
              name='input_num' 
              id="name"
              value="${item.YYSL || item.YYSL==0?item.YYSL : ''}"
            />
            <span style="padding: 0 4px">${item.JLDW}</span>
          </span>
        </div>`
      } else {
        str += `
        <div class='form-group'>
          <label>
            <span name='input_ypmc'>${item.MC}</span>
            <input type='hidden' class='form-control' value='${i}'  name='input_index' />
            <input type='hidden' class='form-control' name='input_type' value='${item.YPTYPE}'  />
          </label>
        `
        str += `
          <span class='inputLine'>
            <input 
              readonly 
              class='form-control num${i}' 
              type='text' 
              name='input_num' 
              id="name"
              value="${item.YYSL || item.YYSL==0?item.YYSL : ''}"
            />
            <span style="padding: 0 4px">${item.JLDW}</span>
          </span>
        </div>`
      }
    })
    return `
      ${str}
      <div class='form-group'>
        <label>容量系数</label>
        <span>
          <select id='capacityCoe'>
          ${_.map(obj.capacityCoe,(item)=>`
            <option value='${item.val}' ${rlxsObj[0].COE == item.val?`selected='selected'`:``}>${item.label}</option>
          `)}
          </select>
          <input type='hidden' id='capOld' />
        </span>
      </div>
    `
  },

	events: {
    // 操作指南
    "click .tipIcon": function(){
      let html = `
        <iframe style="width:100%;height: 450px;" src="e-pnOrderListTip.html"></iframe>
      `
      WRT_e.ui.model({
        id: "testWindow",
        title: '操作指南',
        width: "880px",
        height:"450px",
        content: html,
        // closeButton:false,
        // closeOnEscape:false,
        iframe: false,
      })
    },
    // getList1BodyHtml
    // 体重
    "change #weight": function(){
      if($('#weight').val().indexOf('.')==-1){
        $('#weight').val(Number($('#weight').val()).toFixed(2))
      }
      setPnFluidV();
      setPNpumpSpeed();
    },
    // 母乳
    "change #milkNum": function(){
      setMilkNum1();
      setMealOfmilk1();
      setPnFluidV();
      setPNpumpSpeed();
    },
    // 第一次q2h
    "change #timesToEat": function(ev){
      var oldValue = $("#mealOfMilk").val();
      $("#mealOfMilk").empty();
      var maxValue = 24 / ev.target.value;
      $("#mealOfMilk").append("<option value =''></option>");
      for (var i = maxValue; i >= 0 ; i--) {
        $("#mealOfMilk").append("<option value ='" + i + "'>" + i + "</option>");
      }

      if (!stringISEmpty(oldValue) && oldValue < maxValue) {
        $("#mealOfMilk").val(oldValue);
      }

      setPnFluidV(); // 可以运行
      setPNpumpSpeed();
      if (stringISEmpty($("#typeOfMilk1").val())) {
        return;
      }
      $("#timesToEat1").val($("#timesToEat").val());
      
      setMealOfmilk1();
    },
    //奶的类型
    "change #typeOfMilk": function(){
      setPnFluidV();
      setPNpumpSpeed();
    },
    "change #typeOfMilk1": function () {
      $("#timesToEat1").val($("#timesToEat").val());
      setMealOfmilk1();
      setMilkNum1();
      setPnFluidV();
      setPNpumpSpeed();
    },
    // 几顿
    "change #mealOfMilk":function() {
      setMilkNum1();
      setMealOfmilk1();
      setPnFluidV();
      setPNpumpSpeed();
    },
    "change #pnTime":function() {
      setPNpumpSpeed();
    },

    // 全天每kg总液量
    "change #allDayFluidV": function(){
      allDayFluidVChange();
    },
    // 治疗液体量
    "change #treatFluid": function(){
      setPnFluidV();
      setPNpumpSpeed(); 
    },
    // PN糖速
    "change #pnTS": function(){
      if($('#pnTS').val().indexOf('.')==-1){
        $('#pnTS').val(Number($('#pnTS').val()).toFixed(1))
      }
      TSOfPNChange();
    },
    // PN中钠量
    "change #NaInPN": function(){
      if($('#NaInPN').val().indexOf('.')==-1){
        $('#NaInPN').val(Number($('#NaInPN').val()).toFixed(1))
      }
      NaOfPNChange();
    },
    // PN中钾量
    "change #KInPN": function(){
      if($('#KInPN').val().indexOf('.')==-1){
        $('#KInPN').val(Number($('#KInPN').val()).toFixed(1))
      }
      KOfPNChange();
    },
    // PN中磷量
    "change #PInPN": function(){
      if($('#PInPN').val().indexOf('.')==-1){
        $('#PInPN').val(Number($('#PInPN').val()).toFixed(1))
      }
      POfPNChange();
    },
    // PN中镁量
    "change #MgInPN": function(){
      if($('#MgInPN').val().indexOf('.')==-1){
        $('#MgInPN').val(Number($('#MgInPN').val()).toFixed(1))
      }
      MgOfPNChange();
    },
    // PN时间

	},
})
// 相关检查项表格
var TableShowInfo_View = WRT_e.view.extend({
  render: function () {
    // 初始数据
    // ${this.getChartBodyHtml()}
    // <div class="nbPNChartTitle">血糖标准天</div>
    // let html = `
		// 	<div class="nbPNTable">
		// 		<div class="nbPNTableTitle">相关检验项目</div>
		// 		<div class="nbPNTableContect">
		// 			${this.getTableBodyHtml()}
		// 		</div>
    //     <div class="nbPNChart">
    //       <div class="nbPNChartContect" id="nbPNChartContect" style="height:300px;width: 100%;">
    //         ${this.getChartBodyHtml()}
    //       </div>
    //     </div>
		// 	</div>
		// `
    let html = `
      ${this.getTableBodyHtml()}
		`
    this.$el.html(html)
    return this;
  },
	 // 数据初始化
	getData:function() {
    // 初始数据
    let initialData = []
    // initialData.HYJG = WRT_config.HYJG
    // 获取化验结果 
    WRT_e.api.pnOrderList.getHYJG({
      params: {
        // as_zyh: ,
        as_blid: param['as_blid'],
      },
      success(data) {
        if (data.d.Code == 1) {
          WRT_config.HYJG = JSON.parse((data.d.Result).replace(/'/g,'"'))
          initialData.HYJG = WRT_config.HYJG
          // setHYJG(initialData.HYJG);
        } else {
          initialData.HYJG = []
          // WRT_e.ui.hint({
          //   type:'error',
          //   msg:data.d.CodeMsg
          // })
        }
      }
    })
    return initialData
  },
	// Body部分（即Table部分）
	getTableBodyHtml:function(){
    var obj = this.getData()
		return `
		<table border="1" align="start" class="show_Table">
      <tbody>
        <tr>
          <td>
            <tr align="start">
              <td class="table-nr"> 总胆红素:	 
                ${!stringISEmpty(obj.HYJG['0001']) && (obj.HYJG['0001'].indexOf('↑')!=-1 || obj.HYJG['0001'].indexOf('↓')!=-1)?`
                  <span id="lb_0001" style="color:red">${obj.HYJG['0001']}</span>
                `:`
                  <span id="lb_0001">${obj.HYJG['0001']?obj.HYJG['0001']:''}</span>
                `}
              </td>
              <td class="table-nr"> 直接胆红素: 
                ${!stringISEmpty(obj.HYJG['0002']) && (obj.HYJG['0002'].indexOf('↑')!=-1 || obj.HYJG['0002'].indexOf('↓')!=-1)?`
                  <span id="lb_0002" style="color:red">${obj.HYJG['0002']}</span>
                `:`
                  <span id="lb_0002">${obj.HYJG['0002']?obj.HYJG['0002']:''}</span>
                `}
                </span>
              </td>
              <td class="table-nr"> 白蛋白: 
                ${!stringISEmpty(obj.HYJG['0004']) && (obj.HYJG['0004'].indexOf('↑')!=-1 || obj.HYJG['0004'].indexOf('↓')!=-1)?`
                  <span id="lb_0004" style="color:red">${obj.HYJG['0004']}</span>
                `:`
                  <span id="lb_0004">${obj.HYJG['0004']?obj.HYJG['0004']:''}</span>
                `}
              </td>
              <td class="table-nr"> 丙氨酸氨基转移酶: 
                ${!stringISEmpty(obj.HYJG['0011']) && (obj.HYJG['0011'].indexOf('↑')!=-1 || obj.HYJG['0011'].indexOf('↓')!=-1)?`
                  <span id="lb_0011" style="color:red">${obj.HYJG['0011']}</span>
                `:`
                  <span id="lb_0011">${obj.HYJG['0011']?obj.HYJG['0011']:''}</span>
                `}
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr"> 天冬酸氨酸氨基转移酶: 
                ${!stringISEmpty(obj.HYJG['0012']) && (obj.HYJG['0012'].indexOf('↑')!=-1 || obj.HYJG['0012'].indexOf('↓')!=-1)?`
                  <span id="lb_0012" style="color:red">${obj.HYJG['0012']}</span>
                `:`
                  <span id="lb_0012">${obj.HYJG['0012']?obj.HYJG['0012']:''}</span>
                `}
              </td>
              <td class="table-nr"> 碱性磷酸酶: 
                ${!stringISEmpty() && (obj.HYJG['0013'].indexOf('↑')!=-1 || obj.HYJG['0013'].indexOf('↓')!=-1)?`
                  <span id="lb_0013" style="color:red">${obj.HYJG['0013']}</span>
                `:`
                  <span id="lb_0013">${obj.HYJG['0013']?obj.HYJG['0013']:''}</span>
                `}
              </td>
              <td class="table-nr"> γ谷氨酰基转移酶: 
                ${!stringISEmpty(obj.HYJG['0020']) && (obj.HYJG['0020'].indexOf('↑')!=-1 || obj.HYJG['0020'].indexOf('↓')!=-1)?`
                  <span id="lb_0020" style="color:red">${obj.HYJG['0020']}</span>
                `:`
                  <span id="lb_0020">${obj.HYJG['0020']?obj.HYJG['0020']:''}</span>
                `}
              </td>
              <td class="table-nr"> 肌酐: 
                ${!stringISEmpty(obj.HYJG['0008']) && (obj.HYJG['0008'].indexOf('↑')!=-1 || obj.HYJG['0008'].indexOf('↓')!=-1)?`
                  <span id="lb_0008" style="color:red">${obj.HYJG['0008']}</span>
                `:`
                  <span id="lb_0008">${obj.HYJG['0008']?obj.HYJG['0008']:''}</span>
                `}
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr"> 尿素氮: 
                ${!stringISEmpty(obj.HYJG['0007']) && (obj.HYJG['0007'].indexOf('↑')!=-1 || obj.HYJG['0007'].indexOf('↓')!=-1)?`
                  <span id="lb_0007" style="color:red">${obj.HYJG['0007']}</span>
                `:`
                  <span id="lb_0007">${obj.HYJG['0007']?obj.HYJG['0007']:''}</span>
                `}
              </td>
              <td class="table-nr"> 甘油三脂: 
                ${!stringISEmpty(obj.HYJG['0010']) && (obj.HYJG['0010'].indexOf('↑')!=-1 || obj.HYJG['0010'].indexOf('↓')!=-1)?`
                  <span id="lb_0010" style="color:red">${obj.HYJG['0010']}</span>
                `:`
                  <span id="lb_0010">${obj.HYJG['0010']?obj.HYJG['0010']:''}</span>
                `}
              </td>
              <td class="table-nr"> 血清钾: 
                ${!stringISEmpty(obj.HYJG['0014']) && (obj.HYJG['0014'].indexOf('↑')!=-1 || obj.HYJG['0014'].indexOf('↓')!=-1)?`
                  <span id="lb_0014" style="color:red">${obj.HYJG['0014']}</span>
                `:`
                  <span id="lb_0014">${obj.HYJG['0014']?obj.HYJG['0014']:''}</span>
                `}
              </td>
              <td class="table-nr"> 血清钠: 
                ${!stringISEmpty(obj.HYJG['0015']) && (obj.HYJG['0015'].indexOf('↑')!=-1 || obj.HYJG['0015'].indexOf('↓')!=-1)?`
                  <span id="lb_0015" style="color:red">${obj.HYJG['0015']}</span>
                `:`
                  <span id="lb_0015">${obj.HYJG['0015']?obj.HYJG['0015']:''}</span>
                `}
              </td>
            </tr>
            <tr align="start">
              <td class="table-nr"> 血清氯: 
                ${!stringISEmpty(obj.HYJG['0016']) && (obj.HYJG['0016'].indexOf('↑')!=-1 || obj.HYJG['0016'].indexOf('↓')!=-1)?`
                  <span id="lb_0016" style="color:red">${obj.HYJG['0016']}</span>
                `:`
                  <span id="lb_0016">${obj.HYJG['0016']?obj.HYJG['0016']:''}</span>
                `}
              </td>
              <td class="table-nr"> 血清钙: 
              ${!stringISEmpty(obj.HYJG['0017']) && (obj.HYJG['0017'].indexOf('↑')!=-1 || obj.HYJG['0017'].indexOf('↓')!=-1)?`
                <span id="lb_0017" style="color:red">${obj.HYJG['0017']}</span>
              `:`
                <span id="lb_0017">${obj.HYJG['0017']?obj.HYJG['0017']:''}</span>
              `}
              </td>
              <td class="table-nr"> 血清磷: 
                ${!stringISEmpty(obj.HYJG['0018']) && (obj.HYJG['0018'].indexOf('↑')!=-1 || obj.HYJG['0018'].indexOf('↓')!=-1)?`
                  <span id="lb_0018" style="color:red">${obj.HYJG['0018']}</span>
                `:`
                  <span id="lb_0018">${obj.HYJG['0018']?obj.HYJG['0018']:''}</span>
                `}
              </td>
              <td class="table-nr"> 血清镁: 
                ${!stringISEmpty(obj.HYJG['0019']) && (obj.HYJG['0019'].indexOf('↑')!=-1 || obj.HYJG['0019'].indexOf('↓')!=-1)?`
                  <span id="lb_0019" style="color:red">${obj.HYJG['0019']}</span>
                `:`
                  <span id="lb_0019">${obj.HYJG['0019']?obj.HYJG['0019']:''}</span>
                `}
              </td>
            </tr>
          </td>
        <tr>
      <tbody>
    </table>
		`
	},
})
