/*
统一视图管理类
*/


WRT_e.view = {
    // 扩展一个新的视图类
    extend:function(e){
        return (new WRT_View()).extend(e);
    }
}


// 定义视图基础类
function WRT_View() {
}

// 初始化操作
WRT_View.prototype.init = function (o) {
    o = o || {}
    if (o.data) this.data = o.data;
    if (!this.$el) this.$el = $("<" + (this.tagName || "div") + "/>")
    this.undelegateEvents();
    this.delegateEvents();
    return this;
};

// 渲染视图
WRT_View.prototype.render = function () {
    // 实现 data -> $el 的代码
    this.$el.html("TestRenderOK")
    return this;
};

// 继承和扩展，一次！
WRT_View.prototype.extend = function (protoProps) {
    var child = function () {
    }
    child.prototype = new WRT_View();
    child.prototype.constructor = child;
    if (protoProps) _.extend(child.prototype, protoProps);
    return child;
};

// 挂载事件
WRT_View.prototype.delegateEvents = function (events) {
    events = events || this.events;
    if (!events) return;
    this.undelegateEvents();
    for (var key in events) {
        var method = events[key];
        if (!_.isFunction(method))
            method = this[events[key]];
        if (!method)
            throw new Error('方法 "' + events[key] + '" 不存在！');
        var delegateEventSplitter = /^(\S+)\s*(.*)$/;
        var match = key.match(delegateEventSplitter);
        var eventName = match[1],
            selector = match[2];
        method = _.bind(method, this);
        if (selector === '') {
            this.$el.bind(eventName, method);
        } else {
            this.$el.delegate(selector, eventName, method);
        }
    }
};

// 取消事件
WRT_View.prototype.undelegateEvents = function () {
    this.$el.unbind();
};

// 开始侦听
WRT_View.prototype.on = function (name, func, callbackObj) {
    if(!this.listens){
        this.listens = []
    }
    this.listens.push({name:name, func:func, callbackObj:callbackObj})
};

// 处理侦听
WRT_View.prototype.trigger = function (name, o) {
    if(this.listens){
        for (var i = 0; i < this.listens.length; i++) {
            if(this.listens[i].name == name){
                if(this.listens[i].callbackObj){
                    this.listens[i].func.call(this.listens[i].callbackObj, this.data, o)
                }else{
                    this.listens[i].func(this.data, o);
                }
            }
        }
    }
};

// 解除绑定
WRT_View.prototype.off = function () {
    if(this.listens){
        this.listens = []
    }
};

// 摧毁对象，注意事件绑定和内存泄漏问题
WRT_View.prototype.remove = function () {
    this.undelegateEvents();
    this.off();
    this.$el.empty();
};
