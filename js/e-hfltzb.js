
//统一页面启动
$(document).ready(() => {
    let params={}
    //获取关键字数据
    let url = window.location.href.split("?") || []
    let text = url[1].split("&")
    for (let i of text) {
        let fd = i.split("=")
        params[fd[0]] = fd[1]
    }
    WRT_config.url=params
    app.init()
})

var app = {
    init: function () {
        // console.log(WRT_config.hfltzb)
        // WRT_e.api.hfltzb.GetZxjl({
        //     params: { "al_blid": 1492776,"al_yzid":34546980 },
        //     success(data) {
        //         if (data.Code == 1) {
        //             let res=JSON.parse(data.Result)
        //             console.log(res)
        //             $("#hfltzb_left").html(
        //                 new left_View().init({
        //                     data: WRT_config.hfltzb
        //                 }).render().$el
        //             )
        //         }
        //     }
        // })
        WRT_e.api.hfltzb.GetHfl({
            params: { "al_blid": WRT_config.url["as_blid"] },
            success(data) {
                if (data.Code == 1) {
                    WRT_config.hfltzb=JSON.parse(data.Result)
                    $("#hfltzb_left").html(
                        new left_View().init({
                            data: WRT_config.hfltzb
                        }).render().$el
                    )
                }
            }
        })
        WRT_e.api.hfltzb.GetInrAptt({
            params: { "al_blid": WRT_config.url["as_blid"] },
            success(data) {
                if (data.Code == 1) {
                    let res=JSON.parse(data.Result)
                    console.log(res)
                    let times=[],list=[]
                    res.map(function(item){
                        let r=gettime(item.CHECKTIME)
                        times.push(r)
                        list.push(item.TESTRESULT)
                    })
                    var myChart = echarts.init(document.getElementById('main'));
 
                    // 指定图表的配置项和数据
                    var option = {
                        title: {
                            text: '出凝血INR内容（最近7日）'
                        },
                        tooltip: {},
                        legend: {
                            data:['结果']
                        },
                        xAxis: {
                            data: times
                        },
                        yAxis: {},
                        series: [{
                            name: '结果',
                            type: 'line',
                            data: list
                        }]
                    };
            
                    // 使用刚指定的配置项和数据显示图表。
                    myChart.setOption(option);
                    $("#hfltzb_right").html(
                        new right_View().init({
                            data: res
                        }).render().$el
                    )
                }
            }
        })
    },

}
/** 公共方法 */
//日期年月日
function gettime(val) {
    var d
    if (!val) {
        return ''
    } else {
        d = new Date(val)
    }
    // var d = new Date(val);
    var year = d.getFullYear();
    var month = change(d.getMonth() + 1);
    var day = change(d.getDate());
    var hour = change(d.getHours());
    var minute = change(d.getMinutes());
    var second = change(d.getSeconds());
    function change(t) {
        if (t < 10) {
            return "0" + t;
        } else {
            return t;
        }
    }
    var time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
    return time
}
function btn_show(index){
    let list=WRT_config.hfltzb[index]||{}
    let msg=`${list.MC} </br>${list.YCYL||''}${list.JLDW||''} ${list.ZXPL||''} ${list.ZXFF||''}`
    WRT_e.api.hfltzb.GetZxjl({
        params: { "al_blid": list.BLID,"al_yzid":list.YZID },
        success(data) {
            if (data.Code == 1) {
                let res=JSON.parse(data.Result)
                let html=``
                if(res.length>0){
                    html=`
                    <table>
                        <tr>
                            <th style="width: 50px;">执行人</th>
                            <th>执行时间</th>
                        </tr>
                        ${_.map(res,obj=>
                            `
                            <tr>
                                <td style="width: 50px;">${obj.YHXM}</td>
                                <td>${gettime(obj.ZXSJ)}</td>
                            </tr>`
                        ).join('')}
                    </table> 
                    `
                }else{
                    html='未查询数据！'
                }
                WRT_e.ui.message({
                    title:msg ,
                    content: html,
                    onOk() {}
                })
            }
        }
    })
}

var left_View=WRT_e.view.extend({
    render:function(){
        let html=`
            <div class="hf_header left_head"><span class="header_title">华法令服用剂量（最近7日）</span></div>
            <table border="1" style="border: 1px solid #95B8E7;">
                <tbody>
                    <tr class="hf_tbody">
                        <th style="width: 17px;"></th>
                        <th style="width: 92px;">病案号</th>
                        <th style="width: 112px;">计划开始时间</th>
                        <th style="width: 112px;">结束时间</th>
                        <th style="width: 192px;">药品名称</th>
                        <th style="width: 72px;">一次用量</th>
                        <th style="width: 72px;">剂量单位</th>
                        <th style="width: 72px;">执行频率</th>
                        <th style="width: 72px;">执行方法</th>
                        <th style="width: 72px;">给药时间</th>
                        <th style="width: 72px;">执行记录</th>
                    </tr>
                    ${_.map(WRT_config.hfltzb,(obj,index)=>
                        `<tr>
                            <td style="width: 17px;">${index+1}</td>
                            <td style="width: 92px;">${obj.ZYH||''}</td>
                            <td style="width: 170px;">${gettime(obj.KSSJ)||''}</td>
                            <td style="width: 170px;">${gettime(obj.JSSJ)||''}</td>
                            <td style="width: 192px;">${obj.MC||''}</td>
                            <td style="width: 52px;">${obj.YCYL||''}</td>
                            <td style="width: 52px;">${obj.JLDW||''}</td>
                            <td style="width: 72px;">${obj.ZXPL||''}</td>
                            <td style="width: 72px;">${obj.ZXFF||''}</td>
                            <td style="width: 72px;">${obj.GYSJ||''}</td>
                            <td style="width: 52px;"><a class="btn_show" onclick="btn_show(${index})">查看</a></td>
                        </tr>`
                    ).join('')}
                </tbody>
            </table>
        `
        this.$el.html(html)
        return this;
    },
})
var right_View=WRT_e.view.extend({
    render:function(){
        let html=`
            <div class="hf_header right_head"><span class="header_title">出凝血INR内容（最近7日）</span></div>
            <table border="1" style="border: 1px solid #95B8E7;">
                <tbody>
                    <tr class="hf_tbody">
                        <th style="width: 17px;"></th>
                        <th style="width: 92px;">病案号</th>
                        <th style="width: 192px;">名称</th>
                        <th style="width: 192px;">英文名称</th>
                        <th style="width: 192px;">结果</th>
                        <th style="width: 72px;">检查时间</th>
                    </tr>
                    ${_.map(this.data,(obj,index)=>
                        `<tr>
                            <td style="width: 17px;">${index+1}</td>
                            <td style="width: 92px;">${obj.PATIENTID||''}</td>
                            <td style="width: 112px;">${obj.CHINESENAME||''}</td>
                            <td style="width: 112px;">${obj.ENGLISHAB||''}</td>
                            <td style="width: 192px;">${obj.TESTRESULT||''}</td>
                            <td style="width: 170px;">${gettime(obj.CHECKTIME)||''}</td>
                        </tr>`
                    ).join('')}
                </tbody>
            </table>
        `
        this.$el.html(html)
        return this;
    }
})