var JHCDSignal = false//简化菜单
var istab = null//关闭关闭tab
var page = 1//页码
var caidan_lists = null
var lclj = null
var docdata = []
let tssm_array=[]//规则引擎特殊说明提示数组
let tssm_index=0//规则引擎特殊说明提示序号
let tssm_model={} //规则引擎特殊说明
//统一页面启动
$(document).ready(() => {
  //初始化
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  let params = {}
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  WRT_config.url = params
  WRT_e.api.BrMainLeft.brmrjmsyl({
    params: {
      al_blid: params["al_blid"]
    },
    success(data) {
      WRT_config.brmrjmsyl = data.Result
      $("#lb_brsyl").html(WRT_config.brmrjmsyl)
    }
  })

  //获取主页内页数据
  WRT_e.api.ehrSz.getInit({
    success(data) {
      let obj = JSON.parse(data.Result)
      if (data.CodeMsg == 1) {
        //用户信息
        WRT_config.ehrSz_init = obj
        WRT_e.api.BrMainLeft.getinit({
          params: {
            ll_blid: params["al_blid"],
            ls_idb: params["ls_idb"],
            al_zkid: WRT_config.ehrSz_init.YHXX.ZKID,
            al_yhid: WRT_config.ehrSz_init.YHXX.YSYHID
          },
          success(data) {
            let obj = JSON.parse(data.Result)
            if (data.Code == 1) {
              WRT_config.BrMainLeft = obj
              WRT_config.Limited = obj.isLimited
              JHCDSignal = obj.JHCD_Signal
              console.log('获取主页内页数据初始数据（WRT_e.api.ehrSz.getInit）', WRT_config.BrMainLeft)
              let bing = ["乳腺癌", "肝胆癌", "胰腺癌", "小细胞肺癌", "宫颈癌", "结肠癌", "非小细胞肺癌"]
              WRT_config.DiseaseGuide = false
              if (bing.indexOf(WRT_config.BrMainLeft.BRJBXX.RYZD) >= 0) {
                WRT_e.api.BrMainLeft.getDiseaseGuide({
                  params: { as_diagnosis: WRT_config.BrMainLeft.BRJBXX.RYZD },
                  success(data) {
                    if (data.Code == 1) {
                      WRT_config.DiseaseGuide = data.Result
                      if (WRT_config.DiseaseGuide != 0) {
                        $("#znimg").removeClass("none")
                      }
                    }
                  }
                })
              }
              //console.log('init请求完成，可以简化菜单与MainEinit请求：'+new Date())
              WRT_e.api.BrMainLeft.GetBrMainEinit({
                params: {
                  idb: (obj.BRJBXX.ZYID).toString(),
                  blid: (obj.BRJBXX.BLID).toString(),
                  showbt: "", nowblid: ""
                },
                success(data) {
                  if (data.Code == "1") {
                    WRT_config.BrMainEinit = JSON.parse(data.Result)
                    if (WRT_config.BrMainEinit.history_patient == 1) {
                      $("#brmain").html("历史病人已弹出窗口显示,请关闭此tab页")
                      page_iframe.add("历史病人", WRT_config.server + '/' + WRT_config.BrMainEinit.url)
                      return
                    }

                    WRT_e.api.BrMainLeft.GetGxbMsg({
                      params: {
                        al_blid: WRT_config.url["al_blid"],
                      },
                      success(data) {
                        // if (data.Code == "1") { data.Code == "1"有数据；data.Code == "0"无需显示
                        WRT_config.GetGxbMsg = data.Result
                        // }
                        app.init()
                      }
                    })
                    // app.init()

                  }
                }
              })
              // 应该相关提醒(WRT_config.BrMainLeft.glim_url,WRT_config.BrMainLeft.hzd_url);
              // if(WRT_config.BrMainLeft.glim_url || WRT_config.BrMainLeft.hzd_url){
              //   if (WRT_config.BrMainLeft.glim_url && WRT_config.BrMainLeft.glim_url!=undefined) {
              //     WRT_e.ui.message({
              //       title: '提示',
              //       content: "患者NRS-2002评分结果大于3分，存在营养风险，请医生进行GLIM量表评估。",
              //       // okText:'知道了',
              //       cancelText:'自行处理',
              //       onOk() {
              //         let url = WRT_config.server+'/'+WRT_config.BrMainLeft.glim_url
              //         page_iframe.add("GLIM评估量表", url)
              //       },
              //       onCancel(){
              //         WRT_e.api.BrMainLeft.saveYySignal({
              //           params: {
              //             al_blid: params["al_blid"],
              //             al_czzid: WRT_config.ehrSz_init.YHXX.YSYHID
              //           },
              //           success(data) {
              //           }
              //         })
              //       }
              //     })
              //     // page_iframe.add("GLIM评估量表", WRT_config.server+'/'+WRT_config.BrMainLeft.glim_url)
              //   }
              //   if (WRT_config.BrMainLeft.hzd_url && WRT_config.BrMainLeft.hzd_url!=undefined) {
              //     // WRT_e.ui.message({
              //     //   title: '提示',
              //     //   content: "患者NRS-2002评分结果大于等于5分，营养风险较高，请医生发起营养科会诊。",
              //     //   onOk() {
              //     //     // let url = WRT_config.server+'/'+WRT_config.BrMainLeft.hzd_url
              //     //     // page_iframe.add("营养会诊单", url)
              //     //   },
              //     // })
              //     page_iframe.add("临床营养中心会诊", `e-yykhz.html?as_lx=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}`);
              //   }
              // }
              // // 新冠丙肝都存在(2023-5-8要求新冠打开传染病报告功能下架)
              // if (WRT_config.BrMainLeft.Covid_signal == "1" && WRT_config.BrMainLeft.HCV_signal == "1") {
              //   WRT_e.ui.message({
              //     id: 'addContagionCard',
              //     title: '信息窗口',
              //     content:'该患者住院期间新冠和丙肝检测结果为阳性，请主管医师前往填写传染病报告卡！',
              //     onOk() {
              //       page_iframe.add("传染病报告卡",WRT_config.server+`/token/token.aspx?idb=${WRT_config.url["ls_idb"]}&as_openmode==ehr3&tmpid=${Math.random()}`);
              //     }
              //   })
              // } else {
              //   // Covid_signal字段说明该患者住院期间新冠核酸结果为阳性（Covid_signal为"1"），需要打开传染病报告卡页面并提醒医生填报。
              //   if(WRT_config.BrMainLeft.Covid_signal == "1") {
              //     WRT_e.ui.message({
              //       id: 'addContagionCard',
              //       title: '信息窗口',
              //       // okText:'立即前往',
              //       content:'该患者住院期间新冠核酸结果为阳性，请主管医师前往填写传染病报告卡！',
              //       onOk() {
              //         page_iframe.add("传染病报告卡",WRT_config.server+`/token/token.aspx?idb=${WRT_config.url["ls_idb"]}&as_openmode==ehr3&tmpid=${Math.random()}`);
              //       }
              //     })
              //   }
              //术后首程超时提醒
              if (WRT_config.BrMainLeft.shsctx) {
                WRT_e.ui.message({
                  title: '提示',
                  content: WRT_config.BrMainLeft.shsctx,
                  onOk() { },
                })
              }
              // 患者丙肝检测结果阳性时进入患者页面强制报传染病报告卡
              if (WRT_config.BrMainLeft.HCV_signal == "1") {
                WRT_e.ui.message({
                  id: 'addContagionCard',
                  title: '信息窗口',
                  content: '该患者住院期间丙肝检测结果为阳性，请主管医师前往填写传染病报告卡！',
                  onOk() {
                    page_iframe.add("传染病报告卡", WRT_config.server + `/token/token.aspx?idb=${WRT_config.url["ls_idb"]}&as_openmode==ehr3&tmpid=${Math.random()}`);
                  }
                })
              }
              // }
              // 判断 BrMainleft.aspx/e_init 里的ZRJBR对象，不为null时弹窗显示MSG的内容
              if (WRT_config.BrMainLeft.ZRJBR != null) {
                WRT_e.ui.message({
                  id: 'tipCard',
                  title: '信息窗口',
                  content: WRT_config.BrMainLeft.ZRJBR.msg,
                  onOk() { }
                })
              }

              // 'NIHSS数据',WRT_config.BrMainLeft,WRT_config.BrMainLeft.NIHSS);
              if (WRT_config.BrMainLeft.NIHSS != undefined && WRT_config.BrMainLeft.NIHSS != "") {
                WRT_e.ui.message({
                  id: 'tipCard',
                  title: '信息窗口',
                  // content: WRT_config.BrMainLeft.NIHSS,
                  content: '住院单入院第一诊断为颅内出血，当前为病人病区入院第五天且未进行NIHSS评分，请进行评分',
                  onOk() {
                    page_iframe.add("NIHSS评分", WRT_config.server + `/pfbgl/pfbym.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_pfid=17&as_pfzhid=0&temp=123456789`);
                  }
                })
              }
              // WRT_config.BrMainLeft.glim_message = true（新增）
              if (WRT_config.BrMainLeft.glim_message != undefined && WRT_config.BrMainLeft.glim_message == true) {
                WRT_e.ui.message({
                  id: 'tipCard',
                  title: '信息窗口',
                  content: '该病人入院评估时意识不清，无法进行营养风险筛查，请医生进行GLIM评估',
                  onOk() {
                    page_iframe.add("GLIM评估表", WRT_config.server + `/yyfxsc/GLIM.aspx?al_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&temp=${Math.random()}`);
                  }
                })
              }
              // WRT_config.BrMainLeft.XbHzd_message = true  //（新增）
              if (WRT_config.BrMainLeft.XbHzd_message != undefined && WRT_config.BrMainLeft.XbHzd_message == true) {
                WRT_e.ui.message({
                  id: 'tipCard',
                  title: '信息窗口',
                  content: '该病人24小时内有相关检验结果为阳性，请发起性病会诊',
                  onOk() {
                    // page_iframe.add("会诊单",WRT_config.server+`/zyblhzd/addhzd.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_mrzk=31&temp=${Math.random()}`);
                    page_iframe.add("性病会诊单", WRT_config.server + `/zyblhzd/hzdlist.aspx?as_hzdlb=1&as_mrzk=31&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&temp=${Math.random()}`);
                  }
                })
              }
              // WRT_config.BrMainLeft.ShowVTEjmxssszfxgzs = '1' ;  WRT_config.BrMainLeft.ShowVTEjmxssszfxgzs = '0'不显示下次更新，v81
              if (WRT_config.BrMainLeft.ShowVTEjmxssszfxgzs != undefined && WRT_config.BrMainLeft.ShowVTEjmxssszfxgzs != null && WRT_config.BrMainLeft.ShowVTEjmxssszfxgzs != "" && WRT_config.BrMainLeft.ShowVTEjmxssszfxgzs == '1') {
                WRT_e.ui.message({
                  id: 'VTEjmxssszfxgzs',
                  title: '信息窗口',
                  okText: '知道了',
                  content: '该患者VTE风险评估为中/高危，请及时告知患者相关风险，并签署《静脉血栓栓塞症风险告知书》',
                  onOk() { }
                })
              }
              // 增加Nutric评分提醒icu_nutric_signal
              if (WRT_config.BrMainLeft.icu_nutric_signal != undefined && WRT_config.BrMainLeft.icu_nutric_signal == true) {
                WRT_e.ui.message({
                  id: 'tipCard',
                  title: '信息窗口',
                  content: '请进行Nutric评分',
                  onOk() {
                    page_iframe.add("Nutric评分", WRT_config.server + `/pfbgl/pfbym.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_pfid=5&as_pfzhid=0&temp=${Math.random()}`);
                  }
                })
              }
              // VTE弹窗提醒填写VTE不良事件
              if (WRT_config.BrMainLeft.BLSJVTE) {
                Vtechange()
                // WRT_e.ui.message({
                //   id: 'if_BLSJVTE',
                //   title: '信息窗口',
                //   content: '如为院内VTE，请及时上报不良事件，奖励20元/例',//
                //   okText: '是',
                //   cancelText: '否',
                //   onOk() {
                //     window.open(WRT_config.BrMainLeft.BLSJVTE);
                //   },
                //   onCancel() { }
                // })
              }
              // checkCroTs
              WRT_e.api.BrMainLeft.checkCroTs({
                params:{
                  al_blid:WRT_config.url["al_blid"],
                  al_bqid:WRT_config.BrMainLeft.BRJBXX.BQID
                },
                success(msg){
                  if(msg==1){
                    WRT_e.ui.message({
                      title: '信息窗口',
                      content: '根据风险评估，建议该患者执行CRKP主动筛查',
                      onOk() {
                      }
                    })
                  }
                }
              })
              if(!WRT_config.BrMainLeft["出院时间"]){
                //需要会诊
                WRT_e.api.BrMainLeft.getYyHzdcontrolsignal({
                  params:{
                    al_blid:WRT_config.url["al_blid"],
                  },
                  success(msg){
                    if(msg.Result){
                      setTimeout(function(){
                        WRT_e.ui.model({
                          id: "if_lcyyzxhz",
                          title: '临床营养中心会诊',
                          width: "650px",
                          closeButton: false,
                          closeOnEscape: false,
                          iframeHeight: "400px",
                          iframe: true,
                          iframeURL: `e-yykhz.html?as_lx=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_openmodel=wzfyy`
                        })
                      },900)
                      // page_iframe.add("临床营养中心会诊", `e-yykhz.html?as_lx=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}`);
                    }
                  }
                })
              }
              // // app.init()
            }
          }
        })

      }
    }
  })
})
function setlcyyzxhz(yyhz){
  // $("#if_lcyyzxhz").iziModal("destroy")
  if (yyhz == '发起肠外营养会诊') {
    // console.log(WRT_config.url,88,yyhz);
    $("#if_lcyyzxhz").iziModal("destroy")
    page_iframe.add(yyhz, `${WRT_config.server}/zyblhzd/addhzd.aspx?as_blid=${WRT_config.url.al_blid}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_hzbz=9`);
    page_iframe.del('临床营养中心会诊')
  } else if (yyhz == '发起营养会诊') {
    // console.log(WRT_config.url,99,yyhz);
    $("#if_lcyyzxhz").iziModal("destroy")
    page_iframe.add(yyhz, `${WRT_config.server}/zyblhzd/hzdlist.aspx?as_hzdlb=1&as_blid=${WRT_config.url.al_blid}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_mrzk=56`);
    page_iframe.del('临床营养中心会诊')
  } else {
    WRT_e.ui.hint({msg:'请选择发起营养科会诊'})
  }
}
var app = {
  init: function () {
    //定义时间格式
    $.fn.datebox.defaults.formatter = function (date) {
      var y = date.getFullYear();
      var m = date.getMonth() + 1;
      var d = date.getDate();
      return y + '-' + m + '-' + d;
    }
    //标签页右键事件
    var righttab = new BootstrapMenu('#page .nav_list_header li', {
      fetchElementData: function (e) {
        return e
      },
      actions: [{
        name: '关闭标签',
        onClick(e) {
          closeTab(e, 'close')
        }
      }, {
        name: '关闭右侧标签',
        onClick(e) {
          closeTab(e, 'right')
        }
      }, {
        name: '关闭其他标签',
        onClick(e) {
          closeTab(e, 'other')
        }
      }, {
        name: '关闭左侧标签',
        onClick(e) {
          closeTab(e, 'left')
        }
      }]
    })

    function closeTab(e, type) {
      let rtn = false
      let arr = $(e)[0].parentNode.children
      switch (type) {
        case 'close':
          if (e[0] && (e[0].outerText == '住院医嘱' && e[0].outerText == '留观医嘱')) {
            rtn = true
          }
          if (rtn) {
            WRT_e.ui.hint({ msg: e[0].outerText + '不允许关闭' })
            return
          } else {
            let bl_id = $(e).find('a').attr("href").substr(1)
            page_iframe.del(bl_id)
          }
          break;
        case 'right':
          for (let i = 0; i < arr.length; i++) {
            if (i > arr.length) {
              return
            }
            if (rtn) {
              let id = arr[i].firstChild.hash.substr(1)
              page_iframe.del(id)
              i--
            }
            if (arr[i].textContent == e[0].textContent) {
              rtn = true
            }
          }
          break;
        case 'other':
          for (let i = 0; i < arr.length; i++) {
            if (i > arr.length) {
              return
            }
            if (arr[i].textContent == e[0].textContent) {

            } else if (arr[i].textContent != '住院医嘱' && arr[i].textContent != '留观医嘱') {
              let id = arr[i].firstChild.hash.substr(1)
              page_iframe.del(id)
              i--
            }
          }
          break;
        case 'left':
          for (let i = 0; i < arr.length; i++) {
            if (i > arr.length) {
              return
            }
            if (arr[i].textContent == e[0].textContent) {
              rtn = true
            }
            if (!rtn && (arr[i].textContent != '住院医嘱' && arr[i].textContent != '留观医嘱')) {
              let id = arr[i].firstChild.hash.substr(1)
              page_iframe.del(id)
              i--
            }
          }
          break;
      }
    }

    //绘制病人基本信息
    var Menus = new Menus_View();
    Menus.$el = $("#menu-left");
    Menus.init({
      data: WRT_config.BrMainLeft
    }).render();
    //
    $("#arrow_open").removeClass("none")
    //绘制右侧标签页
    var right = new right_View();
    right.$el = $("#menu-right");
    right.init({
      data: WRT_config.BrMainLeft
    }).render();
    // 应该相关提醒  WRT_config.BrMainLeft.glim_url && WRT_config.BrMainLeft.glim_url!=undefined
    if (WRT_config.BrMainLeft.glim_url || WRT_config.BrMainLeft.hzd_url) {
      if (WRT_config.BrMainLeft.glim_url && WRT_config.BrMainLeft.glim_url != undefined && WRT_config.BrMainLeft.glim_open_signal == '1') {
        WRT_e.ui.message({
          title: '提示',
          content: `患者NRS-2002评分结果${WRT_config.BrMainLeft["营养风险筛查分数"]}分，存在营养风险，请医生进行GLIM量表评估。`,
          // okText:'知道了',
          cancelText: '自行处理',
          onOk() {
            let url = WRT_config.server + '/' + WRT_config.BrMainLeft.glim_url
            page_iframe.add("GLIM评估量表", url)
          },
          onCancel() {
            WRT_e.api.BrMainLeft.saveYySignal({
              params: {
                al_blid: WRT_config.url["al_blid"],
                al_czzid: WRT_config.ehrSz_init.YHXX.YSYHID
              },
              success(data) {
                if (data.Code == 1) {
                  WRT_e.ui.hint({ type: "success", msg: "操作成功！" })
                }
              }
            })
          }
        })
        // page_iframe.add("GLIM评估量表", WRT_config.server+'/'+WRT_config.BrMainLeft.glim_url)
      }
      // console.log(WRT_config.BrMainLeft.hzd_url);
      if (WRT_config.BrMainLeft.hzd_url && WRT_config.BrMainLeft.hzd_url != undefined&&!WRT_config.BrMainLeft["出院时间"]) {
        WRT_e.ui.model({
          id: "if_lcyyzxhz",
          title: '临床营养中心会诊',
          width: "650px",
          closeButton: false,
          closeOnEscape: false,
          iframeHeight: "400px",
          iframe: true,
          iframeURL: `e-yykhz.html?as_lx=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_openmodel=wzfyy`
        })
        // page_iframe.add("临床营养中心会诊", `e-yykhz.html?as_lx=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}`);
      }
    }

    //肠内营养决策提醒
    if(WRT_config.BrMainLeft.changNeiYyjcTx){
      let json=WRT_config.BrMainLeft.changNeiYyjcTx
      if(json&&json.hasError==0){
        WRT_config.changNeiYyjcTx=json.data||[]
        NeiYyjcT_Change(0)
      }
    }
    //监听
    $("#page").on('contextmenu', '.nav li', function (e) {
      listeriframe()
    })

    WRT_e.api.BrMainLeft.GetLiquidSignal({
      params: { al_blid: WRT_config.BrMainLeft.BRJBXX.BLID },
      success(data) {
        if (data && data.Code == 1) {
          WRT_config.LiquidSignal = JSON.parse(data.Result)
          if (WRT_config.LiquidSignal != 0) {
            let html = liquHtml()
            $("#Liquid .table-value").html(html)
            $("#Liquid").css("display", "contents")
          }
        }
      }
    })
    WRT_e.api.BrMainLeft.getJWS({
      params: {
        ll_blid: WRT_config.url["al_blid"],
        ll_idb: WRT_config.url["ls_idb"]
      },
      success(data) {
        if (data.Code == 1) {
          let list = JSON.parse(data.Result)
          let YLXGBGK = ylbgklist(list)
          list.hzd_list.map(function (item) {
            item.check = true
          })
          list.zksqd_list.map(function (item) {
            item.check = true
          })
          let hzdlist = WRT_config.BrMainLeft.HZDCD.concat(list.hzd_list)
          let ZKKBLSQDCD = WRT_config.BrMainLeft.ZKKBLSQDCD.concat(list.zksqd_list)
          let mzzyjwbs = [{ M_NAME: "浙江省区域卫生信息平台共享信息调阅", M_URL: "浙江省区域卫生信息平台共享信息调阅" },
          { M_NAME: "温州市区域医疗平台链接", M_URL: "温州市区域医疗平台链接" }, ...list.JWS,
          { M_NAME: "转诊信息", M_URL: "http://172.16.203.155:18087/webroot/decision/view/report?ref_c=d1dc9333-5ea0-4d18-908b-7d943cefd05d&viewlet=ysgzz%252Fqyzl_xzsj_no-limit.cpt&ref_t=design&bah=" + WRT_config.BrMainLeft["病案号"] + "&token=" + sessionStorage.getItem('token') } || []]

          let div_html1 = "#collapse";
          let div_html2 = "#collapse";
          let div_html3 = "#collapse";
          let div_html4 = "#collapse";
          WRT_config.list_YLXGBGK = YLXGBGK
          hzdlist = [{
            M_NAME: "MDT会诊",
            M_URL: "MDT会诊"
          }, ...hzdlist]
          WRT_config.list_hzdlist = hzdlist
          // 临时加的
          WRT_config.list_hzdlist.unshift({
            M_NAME: "临床营养中心会诊",
            M_URL: "",
            M_TYPE: "12",
            M_PX: 2,
            M_PARAMLIST: "as_blid",
            M_ID: null,
            M_YYZDM: null,
            M_YYQX: 0,
            M_MKDM: null,
            M_PID: null,
            M_ZTBZ: null,
            subMenus: null
          })
          WRT_config.list_mzzyjwbs = mzzyjwbs
          caidan_lists.map(function (item, index) {
            if (item.title == '医疗相关报告卡') {
              div_html1 = "#collapse" + index
            }
            if (item.title == '会诊单管理') {
              div_html2 = "#collapse" + index
            }
            if (item.title == '跨科病例修正申请单') {
              div_html4 = "#collapse" + index
            }
            if (item.title == '门住院既往病史') {
              div_html3 = "#collapse" + index
            }
          })
          let temp1 = `<div class="panel-body">
            <ul>
              ${_.map(YLXGBGK, (item) =>
            `${`<li class="list-item"><a data="${item.M_URL}" dataid="${item.M_ID}" ${item.YLXGBGKtype ? `data-YLXGBGKtype=true` : ""} ${item.check ? `data-hyhz=true` : ""} dataop="${item.M_PARAMLIST}">${item.M_NAME}</a></li>`}`
          ).join('')}
            </ul>
          </div>`
          $(div_html1).html(temp1)

          let temp2 = `<div class="panel-body">
            <ul>
              ${_.map(hzdlist, (item) =>
            `${`<li class="list-item"><a data="${item.M_URL}" dataid="${item.M_ID}" ${item.check ? `data-hyhz=true` : ""} dataop="${item.M_PARAMLIST}">${item.M_NAME}</a></li>`}`
          ).join('')}
            </ul>
          </div>`
          $(div_html2).html(temp2)

          let temp4 = `<div class="panel-body">
            <ul>
              ${_.map(ZKKBLSQDCD, (item) =>
            `${`<li class="list-item"><a data="${item.M_URL}" dataid="${item.M_ID}" ${item.check ? `data-zkblsq=true` : ""} dataop="${item.M_PARAMLIST}">${item.M_NAME}</a></li>`}`
          ).join('')}
            </ul>
          </div>`
          $(div_html4).html(temp4)

          let temp3 = `<div class="panel-body">
            <ul>
              ${_.map(mzzyjwbs, (item) =>
            `${`<li class="list-item"><a data="${item.M_URL}" dataid="${item.M_ID}" data-mzybs=true ${item.check ? `data-hyhz=true` : ""} dataop="${item.M_PARAMLIST}">${item.M_NAME}</a></li>`}`
          ).join('')}
            </ul>
          </div>`
          $(div_html3).html(temp3)
        }
      }
    })
    // WRT_config.BrMainLeft.VVST_message='该患者入院后24小时内未完成VTE风险评估，请主管医生及时完成！'
    if (WRT_config.BrMainLeft.VVST_message) {
      WRT_e.ui.message({
        title: '信息窗口',
        content: WRT_config.BrMainLeft.VVST_message,
        okText: '已知悉',
        onOk() {
          WRT_e.api.BrMainLeft.inertVVST_Data({
            params: {
              al_blid: WRT_config.url["al_blid"],
              al_czzid: WRT_config.ehrSz_init.YHXX.YSYHID
            },
            success(data) {
              if (data.Code == 1) {
                WRT_e.ui.hint({ type: "success", msg: "操作成功！" })
              }
            }
          })
        },
      })
    }


    //弹出24小时vte评分表
    if (WRT_config.BrMainLeft.Show24RYVTEFXPG == 1) {
      let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`
      page_iframe.add("评分表", url)
      WRT_e.ui.message({
        title: '信息窗口',
        content: "该患者入院后24小时内未完成VTE风险评估，请主管医生及时完成！",
        onOk() { },
      })
    }
    //弹出36小时vte评分表
    if (WRT_config.BrMainLeft.Show36SHVTEFXPG == 1) {
      let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`
      page_iframe.add("评分表", url)
      WRT_e.ui.message({
        title: '信息窗口',
        content: "该患者入院后36小时内未完成VTE风险评估，请主管医生及时完成！",
        onOk() { },
      })
    }
    //弹出自杀风险评分表
    if (WRT_config.BrMainLeft.Show24FXYSPGLB == 1) {
      let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`
      page_iframe.add("评分表", url)
      WRT_e.ui.message({
        title: '信息窗口',
        content: "该患者入院后24小时内未完成自杀风险评分表，请主管医生及时完成！",
        onOk() { },
      })
    }
    //弹出VTE风险评分中高危评估
    if (WRT_config.BrMainLeft.Show24VTECXPG == 1) {
      let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`
      page_iframe.add("评分表", url)
      WRT_e.ui.message({
        title: '信息窗口',
        content: "该患者入院后24小时内未完成VTE风险评分中高危且未评相关出血评估，请主管医生及时完成！",
        onOk() { },
      })
    }
    //弹出ICU质量控制
    if (WRT_config.BrMainLeft.ShowICUZLKZDJB == 1) {
      let url = WRT_config.server + `/pfbgl/pfbym.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_pfid=103&as_pfzhid=0`
      page_iframe.add("ICU质量控制", url)
      WRT_e.ui.message({
        title: '信息窗口',
        content: "该患者入院后ICU质量控制评估，请主管医生及时完成！",
        onOk() { },
      })
    }
    //住院超过30天
    if (WRT_config.BrMainLeft.ShowC30TBRJL == 1) {
      let times = new Date().getTime();
      let url = WRT_config.server + `/c30tbrjl/c30tbrtj.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_temp=${times}`
      page_iframe.add("住院超过30天", url,'zycgt')
      WRT_e.ui.message({
        title: '信息窗口',
        content: "该患者住院超过30天评估，请主管医生及时完成！",
        onOk() { },
      })
    }
    let rtn = showmessage()
    function showmessage() {
      let type = true
      if (WRT_config.BrMainLeft.BRJBXX.ZKID == 2961 || WRT_config.BrMainLeft.BRJBXX.ZKID == 49 || WRT_config.BrMainLeft.BRJBXX.ZKID == 46) {
        type = false
      }
      if (WRT_config.BrMainLeft.BRJBXX.ZYH.indexOf('A') >= 0 || WRT_config.BrMainLeft.BRJBXX.ZYH.indexOf('B') >= 0 || WRT_config.BrMainLeft.BRJBXX.ZYH.indexOf('C') >= 0 || WRT_config.BrMainLeft.BRJBXX.ZYH.indexOf('D') >= 0 || WRT_config.BrMainLeft.BRJBXX.ZYH.indexOf('E') >= 0) {
        type = false
      }
      return type
    }
    if (rtn) {
      //
      WRT_e.api.BrMainLeft.CheckBrTsxx({
        params: {
          al_blid: WRT_config.url["al_blid"],
          al_zkid: WRT_config.BrMainLeft.BRJBXX.ZKID
        },
        success(data) {
          if (data) {
            let arr = JSON.parse(data)
            arr.map(function (item) {
              if (WRT_config.BrMainLeft.ShowImg_label == 1) {
                return
              }
              if (item.CodeMsg && WRT_config.BrMainLeft.ShowImg_label != 1) {
                WRT_e.ui.message({
                  title: '信息窗口',
                  content: item.CodeMsg,
                  onOk() { },
                })
              }
            })
          }

        }
      })
    }
    //当天医嘱暂停执行申请审批
    WRT_e.api.BrMainLeft.getJYSPJLListByBingLiID({
      params: {
        bingLiID: WRT_config.BrMainLeft.BRJBXX.BLID,
        zhuangTaiBZ: 0
      },
      success(res) {
        if (res) {
          if (res.data && res.data.length > 0) {
            WRT_config.JYSPJLList_copy = res.data
            let lists = {}, arr = []
            WRT_config.JYSPJLList_copy.map(function (item) {
              if (lists[item.shenPiLSH]) {
                lists[item.shenPiLSH].push(item)
              } else {
                lists[item.shenPiLSH] = [item]
              }
            })
            for (let i in lists) {
              if (lists[i]) {
                let times = []
                lists[i].map(function (item) {
                  if (item.jiHuaSJ) {
                    times.push(item.jiHuaSJ)
                  }
                })
                let list = lists[i][0]
                list.jiHuaSJ = times.join('</br>')
                arr.push(list)
              }
            }
            WRT_config.JYSPJLList = arr
            JYSPHtml()
          }
        }
      }
    })
  }
}

/********************公用方法********************/
function NeiYyjcT_Change(index){
  let list=WRT_config.changNeiYyjcTx[index]
  if(list){
    let html=`<div style="text-align: center;"><h>${list.biaoTi}</h></div>
    ${_.map(list.neiRongs,(obj)=>{
      `<div>${obj}</div>`
    }).join('')}
    <div><button class="e_btn">已知晓</button></div>
    `
    WRT_e.ui.model({
      id:'NeiYyjcT',
      title: '肠内营养决策提醒',
      width: "850px",
      content: html,
      iframe: false,
      
    })
  }

}
function onSaveNeiYyjcT_Change(index){
  $("#NeiYyjcT").iziModal("destroy")
  WRT_e.api.BrMainLeft.saveChangNeiYyjcTxZx({
    params: {
      bingLiID: list.bingLiID,
      tiXingLB: list.tiXingLB,
      tiXingWZ: list.tiXingWZ
    },
    success(data) {
      if(data){
        NeiYyjcT_Change(index+1)
      }
    }
  })
}
//当天医嘱暂停执行申请审批
function JYSPHtml() {
  let html = `
  <table id="tbJYSP" cellspacing="5" cellpadding="10" border="0" class="btable">
    <tbody>
      <tr>
        <td width="200px">医嘱名称</td>
        <td width="140px">开立时间</td>
        <td width="160px">停用的任务执行时间</td>
        <td width="80px">发起人员</td>
        <td width="200px">操作</td>
      </tr>
      ${_.map(WRT_config.JYSPJLList, (obj, index) =>
    `${_.map(obj.ylZyypyzVos, (item, key) =>
      `<tr>
          <td width="200px">${item.mingCheng||''}</td>
          <td width="140px">${item.shouCiLRSJ}</td>
          <td width="160px">${obj.jiHuaSJ}</td>
          <td width="80px">${obj.faQiRYXM}</td>
          <td width="200px">${key == 0 ? `<button class="e_btn" onclick="e_action(${index},1)" style="margin-right: 5px;">同意</button><button class="e_btn" onclick="getBz(${index})">不同意</button>` : ''}</td>
        </tr>`
    ).join('')}`
  ).join('')}
    </tbody>
  </table>
  `
  WRT_e.ui.model({
    id: "if_JYSP",
    title: "当天医嘱暂停执行申请审批",
    width: "850px",
    content: html,
    iframe: false,
  })
}

function getBz(index) {
  let temp = `<div><label>原因：</label><textarea id="tb_bz" rows="2" cols="30" style="vertical-align: top;" value=""></textarea></br></br></div>
  <button class="e_btn" onclick="setBz(${index})" style="margin-right: 5px;">确定</button><button class="e_btn" onclick="$('#if_JYSP_Bz').iziModal('destroy')">取消</button>
  `
  WRT_e.ui.model({
    id: "if_JYSP_Bz",
    title: "备注",
    width: "450px",
    content: temp,
    iframe: false,
  })
}
//备注原因
function setBz(index) {
  let val = $("#tb_bz").val()
  e_action(index, 2, val)
  $('#if_JYSP_Bz').iziModal('destroy')
}

//简易流程审批更新用户审批c
function e_action(index, type, bz) {
  let list = WRT_config.JYSPJLList[index]
  if (list) {
    WRT_e.api.BrMainLeft.AuditByUser({
      params: {
        applyId: list.shenPiLSH,
        bizCode: '15',
        isPass: type,
        opinion: JSON.stringify({ content: bz || '' }),
        stepNo: 1

      },
      success(res) {
        if (res.hasError != 0) {
          WRT_e.ui.hint({ msg: '审批失败! ' + res.errorMessage, type: 'error' })
        }
        else {
          if (res.data.returnCode != 0) {
            WRT_e.ui.hint({ msg: '审批失败! ' + res.data.returnMessage, type: 'error' })
          }
          else {
            WRT_e.ui.hint({ msg: '审批成功! ', type: 'success' })
          }
        }
      }
    })
  }
}

// 关闭一键开单tab（或定位到长期医嘱tab）———对方调用  测试 <a onclick="closeYJKD();">关闭</a>
function closeYJKD() {
  let el = $("a[href='#一键开单']")
  let e = $("a[href='#长期医嘱']")
  el.remove()
  e.click();
  // var tabDel = $(`#一键开单 iframe`)[0]
  // // .contentWindow;
  // tabDel.remove()
  return
}

function listeriframe() {
  let arr = $(`.tab-content iframe`)
  for (let i in arr) {
    if (arr[i] && arr[i].contentDocument) {
      arr[i].contentDocument.addEventListener("click", dothis, false);
    }
  }
}
function dothis() {
  parent.dothis()
  $('.bootstrapMenu').css('display', 'none')
}
//血糖单选框
function checkradio(type) {
  if (type == 'true') {
    $("#textarea_if").addClass("none")
  } else {
    $("#textarea_if").removeClass("none")
  }
}

//血糖保存
function XTGL_save() {
  let check = $('input[name="Fruit"]:checked').val();
  let jjly = ''
  if (check == 0) {
    jjly = $("#textarea_if")[0].value
  } else {
    // page_iframe.add('')
    // return
  }
  //医生否认病人需要内分泌会诊时保存的拒绝理由
  // WRT_e.api.BrMainLeft.saveXTGLJJLY({
  //   params:{
  //     blid:WRT_config.BrMainLeft.BRJBXX.BLID,
  //     jjly:jjly
  //   },
  //   success(data){
  //     if(data.Message==1){
  //       $('#if_XTGL').iziModal('destroy')
  //     }
  //   }
  // })
}
//vte不良事件
function dbVtechange(){
  $('.box_foot1').addClass('none')
  Vtechange()
}
//
//vte不良事件
function Vtechange(){
  WRT_e.ui.model({
    id: 'if_BLSJVTE',
    title: "信息窗口",
    width: "400px",
    content: `
      <div>如为院内VTE，请及时上报不良事件，奖励20元/例</div></br>
      <div style="float: right;">
      <button class="e_btn" onclick="Vteclick()">是</button>
      <button class="e_btn" onclick="$('#if_BLSJVTE').iziModal('destroy')">否</button>
      <button class="e_btn_primary" onclick="dearVte()">待处理</button>
      </div>
    `,
    iframe: false,
  })
}
//vte不良事件确定
function Vteclick(){
  $('#if_BLSJVTE').iziModal('destroy')
  window.open(WRT_config.BrMainLeft.BLSJVTE);
}
//vte不良事件待处理
function dearVte(){
  $('#if_BLSJVTE').iziModal('destroy');
  $('.box_foot1').removeClass('none')
}

//侧边栏收放
function img_click(type) {
  if (type == 'open') {
    $("#arrow_open").addClass("none")
    $("#arrow_close").removeClass("none")
    $("#menu-left").addClass("none")
    $("#left_img").addClass("left_img")
  } else {
    $("#arrow_open").removeClass("none")
    $("#arrow_close").addClass("none")
    $("#menu-left").removeClass("none")
    $("#left_img").removeClass("left_img")
  }
}

//展开菜单
function dgchangeitem(ev) {
  if (ev == 'One') {
    if ($(`#collapse${ev}`)[0].className.indexOf("in") >= 0) {
      $(`#head${ev} .icon-xiala`).css("display", "block")
      $(`#head${ev} .icon-xiangshang`).css("display", "none")
    } else {
      changeitem()
      $(`#head${ev} .icon-xiala`).css("display", "none")
      $(`#head${ev} .icon-xiangshang`).css("display", "block")
    }
    return
  }
  if ($(`#collapse${ev}`)[0].className.indexOf("in") >= 0) {
    $(`#heading${ev} h4 .icon-xiala`).css("display", "block")
    $(`#heading${ev} h4 .icon-xiangshang`).css("display", "none")
  } else {
    changeitem()
    $(`#heading${ev} h4 .icon-xiala`).css("display", "none")
    $(`#heading${ev} h4 .icon-xiangshang`).css("display", "block")
  }
}
//菜单点击
function changeitem(index) {
  let div_lists = $("#panel_group").find(".default_list")
  for (let i = 0; i < div_lists.length; i++) {
    let id = div_lists[i].id
    $(`#${id} h4 .icon-xiala`).css("display", "block")
    $(`#${id} h4 .icon-xiangshang`).css("display", "none")
  }
  if (index == undefined) {
    return
  }
  if ($(`#collapse${index}`)[0].className.indexOf("in") >= 0) {
    $(`#heading${index} h4 .icon-xiala`).css("display", "block")
    $(`#heading${index} h4 .icon-xiangshang`).css("display", "none")
  } else {
    $(`#heading${index} h4 .icon-xiala`).css("display", "none")
    $(`#heading${index} h4 .icon-xiangshang`).css("display", "block")
  }
}
//计算日期差
function daysBetween(sDate1, sDate2, type, CWRYSJ) {
  if (new Date(sDate1).getTime() <= 0) {
    sDate1 = CWRYSJ
  }
  var d = new Date(sDate2);
  var year = d.getFullYear();
  var month = change(d.getMonth() + 1);
  var day = change(d.getDate());
  function change(t) {
    if (t < 10) {
      return "0" + t;
    } else {
      return t;
    }
  }
  var time = year + '-' + month + '-' + day
  //Date.parse() 解析一个日期时间字符串，并返回1970/1/1 午夜距离该日期时间的毫秒数
  var time1 = Date.parse(new Date(sDate1));
  var time2 = Date.parse(new Date(time));
  switch (type) {
    case 'year':
      let yyy = (time2 - time1) / 1000 / 3600 / 24
      if (String(yyy).indexOf(".") >= 0) {
        yyy = Math.abs(parseInt(yyy)) + 1
      }
      return yyy;
    case 'day':
      return Math.abs(parseInt((time2 - time1) / 1000 / 3600 / 24 / 365));
  }
}
//判断住院号
function getRYSJ(k, x, y) {
  let rtn = 'none'
  if (k && (!x || x == '')) {
    rtn = 'green'
  }
  if (y && y.indexOf("WSH") >= 0) {
    [
      rtn = 'red'
    ]
  }
  return rtn
}
function OpenTab(url, title) {
  if (title == '留观医嘱' || title == '住院医嘱') {
    $(`#page a[href="#住院医嘱"]`).tab("show")
    return
  }
  page_iframe.add(title, url)
}
//回车事件主管医生
function calAge() {
  var evt = window.event || e;
  if (evt.keyCode == 13) {
    onSearch()
  }
}
//查询主管医生
function onSearch() {
  let val = $("#doc_mc")[0].value
  let check = $("#cb_zk")[0].checked
  let zkid = sessionStorage.getItem("zkid") || WRT_config.ehrSz_init.YHXX.ZKID
  let arr = []
  // || (item.XM)
  WRT_config.docdata.map(function (item) {
    // if ( (item.PY.indexOf(val) >= 0 || item.PY.indexOf(val.toUpperCase()) >= 0) || (item.ZSDM && item.ZSDM.indexOf(val) >= 0) ) {
    if ((item.PY.indexOf(val) >= 0 || item.PY.indexOf(val.toUpperCase()) >= 0) || (item.ZSDM && (item.ZSDM.indexOf(val) >= 0 || item.ZSDM.indexOf(val.toUpperCase()) >= 0)) || (item.WB && (item.WB.indexOf(val) >= 0 || item.WB.indexOf(val.toUpperCase()) >= 0)) || (item.XM && item.XM.indexOf(val) >= 0)) {
      if (check) {
        if (item.XZKID == zkid) {
          arr.push(item)
        }
      } else {
        arr.push(item)
      }
    }
  })
  // 可能修改逻辑
  // if(check) {
  //   docdata = arr
  // } else {
  //   docdata = WRT_config.docdata
  // }
  docdata = arr
  page = 1
  let array = []
  docdata.map(function (item, index) {
    if (index < page * 20 && index >= (page - 1) * 20) {
      array.push(item)
    }
  })
  let temp = docDataHtml(array)
  $("#doc_lists").html(temp)
}
// 计划出院（预出院）弹窗清空按钮  接口调用示例：   
function clearJHCY() {
  WRT_e.api.BrMainLeft.clearJhcysj({
    params: {
      as_zyid: WRT_config.BrMainLeft.BRJBXX.ZYID
    },
    success(data) {
      // console.log('计划出院（预出院）弹窗清空按钮',data);
      var val = JSON.parse(data);
      if (val.Message == "0") {
        WRT_e.ui.hint({ msg: '保存成功', type: 'success' })
        $('#saveJhcysj').iziModal("destroy")
      } else {
        WRT_e.ui.hint({ msg: '保存失败', type: 'error' })
        $('#saveJhcysj').iziModal("destroy")
      }
    },
    error: function (err) {
      WRT_e.ui.hint({ msg: '问题' + err.status + '\nresponseText：' + err.responseText, type: 'error' })
    }
  })
}
//主管医生
function onFoolter(type) {
  if (type == 'home') {
    page = 1
  } else if (type == 'previous') {
    if (page - 1 >= 0) {
      page = page - 1
    } else {
      page = 0
    }
  } else if (type == 'next') {
    if (page + 1 <= parseInt(docdata.length / 20)) {
      page = page + 1
    } else {
      page = parseInt(docdata.length / 20)
    }
  } else {
    page = parseInt(docdata.length / 20)
  }
  let arr = []
  docdata.map(function (item, index) {
    if (index < page * 20 && index >= (page - 1) * 20) {
      arr.push(item)
    }
  })
  let temp = docDataHtml(arr)
  $("#doc_lists").html(temp)
}
//修改主管医生
function onSelect(index) {
  let key = (page - 1) * 20 + parseInt(index)
  let obj = docdata[key]
  if (obj) {
    WRT_e.api.BrMainLeft.SetZgys({
      params: {
        al_zgysid: obj.YHID,
        al_zyid: WRT_config.BrMainLeft.BRJBXX.ZYID
      },
      success(data) {
        if (data && data.Code == 1) {
          $("#ZGYSXM a").html(obj.XM)
          WRT_e.ui.hint({ type: "success", msg: "更新主管医生成功！" })
          $("#if_zgys").iziModal("destroy")
        } else {
          WRT_e.ui.hint({ type: "error", msg: "更新主管医生失败！" })
        }
      }
    })
  }
}
//主管医生html
function docDataHtml(arr) {
  //  obj.GZDM原本职称字段
  let html = `
  <table border="0" width="400px" cellspacing="0" cellpadding="0" style="border-left: 1px solid;border-right: 1px solid;border-bottom: 1px solid;">
    <tr class="docbody" style="color:White;background-color:#507CD1;font-size:15px;font-weight:bold;">
      <td width="100px">姓名</td>
      <td width="100px">终身码</td>
      <td width="100px">职称</td>
    </tr>
    ${_.map(arr, (obj, index) =>
    `
        <tr class="doctr" ondblclick="onSelect(${index})">
          <td width="100px">${obj.XM}</td>
          <td width="100px">${obj.ZSDM || ''}</td>
          <td width="100px">${obj.ZHICHENG}</td>
        </tr>`
  ).join('')}
  </table>
  <div class="doc_btns">共${docdata.length || 0}条记录 共${parseInt(docdata.length / 20) == 0 ? 1 : parseInt(docdata.length / 20)}页 当前第${page}页 <a onclick="onFoolter('home')">首页</a> <a onclick="onFoolter('previous')">上一页</a> <a onclick="onFoolter('next')">下一页</a> <a onclick="onFoolter('back')">尾页</a></div>`
  return html
}

//液体出入平衡
function liquHtml() {
  if (!WRT_config.LiquidSignal) {
    return `<span id="Liquid_title">未设置</span><a class="e-tagbtn-prime" onclick="upLiqu()">设置</a>`
  }
  let classname = ""
  let val = WRT_config.LiquidSignal["L-G="].split("=")
  if (val[1]) {
    let value = null
    value = val[1].split("ml")[0]
    value = parseInt(value)
    if ((value >= 400 && value < 500) || (value <= -400 && value > -500)) {
      classname = '#a5960e'
    } else if (value >= 500 || value <= -500) {
      classname = '#f5222d'
    }
  }
  let liqu = WRT_config.LiquidSignal["L-G="]||null
  let html = `<span id="Liquid_title" title="${WRT_config.LiquidSignal["病人少尿提醒"] || ""}" style="${classname ? `color:${classname}` : ""}">${liqu}</span><a class="e-tagbtn-prime" onclick="upLiqu()">设置</a>`
  return html

}
//修改液体出入量平衡
function upLiqu() {
  let temp = `<div>
      目标值：<input id="Liqu_title" type="text" value="${WRT_config.LiquidSignal["G值"]}" onkeydown="change(this)" autocomplete="off" style="border: 1px solid;" />
      <button class="e_btn" onclick="onSave()">保存</button>
      <button class="e_btn" onclick="$('#if_Liqu').iziModal('destroy')">取消</button>
    </div>
  `
  WRT_e.ui.model({
    id: "if_Liqu",
    title: "修改液体出入量平衡",
    iframe: false,
    content: temp
  })
}
function change(event) {
  var e = window.event || event;
  if (e.keyCode === 13) {
    onSave();
  }
}
//
function onSave() {
  let val = $("#Liqu_title")[0].value || ""
  if (val) {
    WRT_e.api.BrMainLeft.updateLiquid_G({
      params: {
        al_blid: WRT_config.BrMainLeft.BRJBXX.BLID,
        al_yhid: WRT_config.BrMainLeft.YHXX.YHBMID,
        ad_G: val
      },
      success(data) {
        if (data && data.Code == 1) {
          WRT_e.ui.hint({ type: "success", msg: "修改G值成功" })
          $("#if_Liqu").iziModal("destroy")
          WRT_e.api.BrMainLeft.GetLiquidSignal({
            params: { al_blid: WRT_config.BrMainLeft.BRJBXX.BLID },
            success(data) {
              if (data && data.Code == 1) {
                WRT_config.LiquidSignal = JSON.parse(data.Result)
                if (WRT_config.LiquidSignal != 0) {
                  let html = liquHtml()
                  $("#Liquid .table-value").html(html)
                  // $("#Liquid").css("display","block")
                }
              }
            }
          })
        } else {
          WRT_e.ui.hint({ type: "error", msg: "修改G值失败" })
        }
      }
    })
  }
}

//病人初始化主页回调
function getBrMain() {
  let val = WRT_config.BrMainEinit.blzt + '^' + WRT_config.BrMainEinit.qtqx
  return val
}
//刷新
function resultData() {
  WRT_e.api.BrMainLeft.getinit({
    params: {
      ll_blid: WRT_config.url["al_blid"],
      ls_idb: WRT_config.url["ls_idb"],
      al_zkid: WRT_config.ehrSz_init.YHXX.ZKID,
      al_yhid: WRT_config.ehrSz_init.YHXX.YSYHID
    },
    success(data) {
      let obj = JSON.parse(data.Result)
      if (data.Code == 1) {
        WRT_config.BrMainLeft = obj
        console.log('刷新页面获取初始数据', WRT_config.BrMainLeft)
        lclj = obj.LCLJ
        WRT_e.api.BrMainLeft.getDiseaseGuide({
          params: { as_diagnosis: WRT_config.BrMainLeft.BRJBXX.RYZD },
          success(data) {
            if (data.Code == 1) {
              WRT_config.DiseaseGuide = data.Result
              var Menus = new Menus_View();
              Menus.$el = $("#menu-left");
              Menus.init({
                data: WRT_config.BrMainLeft
              }).render();
            }
          }
        })
      }
    }
  })
}

//修改日间手术
function saveRjss() {
  let _this = this;
  let text = $("#tb_tcly")[0].value
  if (!text) {
    alert('理由必须填写！')
    return
  }
  let lb = ''
  if (WRT_config.BrMainLeft.BRJBXX.RJSS == '1') {
    lb = '2'
  } else if (WRT_config.BrMainLeft.BRJBXX.RJSS == '2') {
    lb = '1'
  }
  WRT_e.api.BrMainLeft.delRjss({
    params: {
      as_reason: text,
      al_zyid: WRT_config.url["al_blid"],
      al_yhid: WRT_config.ehrSz_init.YHXX.YSYHID,
      as_lb: lb
    },
    success(data) {
      if (data.Code == 1) {
        WRT_e.ui.hint({ msg: data.CodeMsg, type: 'success', })
        $("#if_tag_icon").iziModal('destroy')
        _this.resultData()
      } else {
        WRT_e.ui.hint({ msg: data.CodeMsg, type: 'error', })
      }
    },
    catch(e) {
      WRT_e.ui.hint({ msg: e, type: 'error', })
    }
  })
}
function checkRjss(e) {
  if ($(e).is(':checked')) {
    $("#btn_save").removeClass("none")
    $("#btn_save_none").addClass("none")
  } else {
    $("#btn_save").addClass("none")
    $("#btn_save_none").removeClass("none")
  }
}

//临床路径回调
function SetLcljPg(ljid) {
  var j_url = WRT_config.server + "/lclj/ljpg.aspx?as_new=1&as_blid=" + WRT_config.BrMainLeft.BRJBXX.BLID + "&as_ljid=" + ljid + "&tmpid=" + Math.random();
  // $(".lclj").html()
  let el = $(`#page a[href="#临床路径"]`).parent()
  el.remove()
  setTimeout(() => {
    $(`#page #临床路径`).remove()
  }, 200)
  page_iframe.add("临床路径评估", j_url)
  // this.SetLclj()
}
//确认临床路径回调
function SetLclj(bz, urls) {
  this.resultData()
  let el = $(`#page a[href="#临床路径评估"]`).parent()
  el.remove()
  setTimeout(() => {
    $(`#page #临床路径评估`).remove()
  }, 200)
  WRT_config.BrMainLeft.LCLJ = bz
  let url = ""
  if (bz == 0) {
    url = WRT_config.server + `/lclj/lclj_qzrj.aspx?as_new=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_zddm=0&tmpid=${Math.random()}`
  } else if (bz != 0) {
    let fd = WRT_config.BrMainLeft.dept_attribute.filter(item => item.SXMC.indexOf("是否使用新版临床路径") >= 0)
    if (fd && fd.length > 0) {
      url = `e-lclj.html?al_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&al_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&al_zlzid=${WRT_config.BrMainLeft.BRJBXX.ZLZID}&as_openmode=ehr3&tmpid=${Math.random()}`
    } else {
      url = WRT_config.server + `/lclj/lclj.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zlzid=${WRT_config.BrMainLeft.BRJBXX.ZLZID}&tmpid=${Math.random()}`
    }
  }
  page_iframe.add("临床路径", url)
}

//标签页式iframe
var page_iframe = {
  //添加选项卡页面
  add(name, url, id) {
    //添加dom
    //判断标签项最多为8个
    if (!id) {
      id = name
    }
    if ($('#page>.nav>li').length >= 8) {
      WRT_e.ui.hint({
        type: 'info',
        msg: '工作标签页太多,请适当删减！'
      })
      return
    }
    if (url.indexOf("tmpid") == -1) {
      if (url.indexOf("?") >= 0) {
        url = url + `&tmpid=${Math.random()}`
      } else {
        url = url + `?tmpid=${Math.random()}`
      }
    }
    if (url.indexOf("openmode") == -1) {
      if (url.indexOf("?") >= 0) {
        url = url + `&openmode=ehr3&as_openmode=ehr3`
      } else {
        url = url + `?openmode=ehr3&as_openmode=ehr3`
      }
    }
    if (url.indexOf('zyblFrame.aspx') >= 0) {
      window.open(url)
      return
    }

    // console.log('添加选项卡页面',name, url,id);
    //判断是否有该标签
    if ($('#page>.nav>li').find(`a[href="#${id}"]`).length == 0) {
      $('#page>.nav').append(`<li><a href="#${id}" data-id="${id}" data-toggle="tab">${name}</a><i class="iconfont icon-cuo close"></i></li>`)
      $('#page>.tab-content').append(`<div class="tab-pane fade" id="${id}"><iframe src="${url}" scrolling="auto" frameborder="0" style="width:100%;height:100%"></iframe></div>`)
    } else if (name == '专科会诊'&&$("#专科会诊 iframe")[0]) {
      $("#专科会诊 iframe")[0].src = url
      return
    } else if ($(`#${id} iframe`)[0] && url) {
      $(`#${id} iframe`)[0].src = url
      return
    }
    $("#page>.tab-content .fade").removeClass("active")
    $("#page>.tab-content .fade").removeClass("in")
    //激活当前选项卡
    $(`#page a[href="#${id}"]`).tab('show')
  },
  //删除选项卡页面
  del(e, title) {
    if (decodeURI(e)) {
      e = decodeURI(e)
    }
    let el = $(`#page a[href="#${e}"]`).parent()
    istab = null;
    if (e == '病程记录' || e == '病历知情记录汇总') {
      istab = e
      var childWindow = $(`#${e} iframe`)[0].contentWindow;
      childWindow.delTab();
      return
    }
    type = false
    if (title == '手术相关记录' || title == '入院记录') {
      istab = e
      var childWindow = ''
      if ($(`#${e} iframe`)[0]) {
        childWindow = $(`#${e} iframe`)[0].contentWindow;
      } else {
        childWindow = $(`#${div} iframe`)[0].contentWindow;
      }
      // type=childWindow.CheckWsChanged();
      try {
        type = childWindow.CheckWsChanged();
      } catch (e) {
      }
    }
    if (type) {
      return
    }
    //激活下一个选项卡
    if (el[0].className == 'active') {
      if (el.prev().length == 0) {
        el.parent().find('li:first-child a').tab('show')
      } else {
        el.prev().children('a').tab('show')
      }
    }
    // //删除dom
    el.remove()
    setTimeout(() => {
      $(`#page #${e}`).remove()
    }, 200)
  }
}
//父界面
function delTab() {
  WRT_config.deltab = true
  let div = $("#menu-right .nav_list_header")
  if (div[0]) {
    let arr = div[0].children
    let rtn = false
    for (let i = 0; i < arr.length; i++) {
      if (arr[i].innerText == "病程记录") {
        rtn = true
      }
    }

    if (rtn) {
      istab = '病程记录'
      var childWindow = $(`#病程记录 iframe`)[0].contentWindow;
      childWindow.delTab();
    } else {
      parent.delBlws()
    }
  } else {
    parent.delBlws()
  }
}
//病程子页面调用
function delBlws() {
  if (WRT_config.deltab) {
    parent.delBlws()
    return
  }
  if (!istab) {
    return
  }
  let el = $(`#page a[href="#${istab}"]`).parent()
  if (el[0].className == 'active') {
    if (el.prev().length == 0) {
      el.parent().find('li:first-child a').tab('show')
    } else {
      el.prev().children('a').tab('show')
    }
  }
  // //删除dom
  el.remove()
  setTimeout(() => {
    $(`#page #${istab}`).remove()
  }, 200)
}
//菜单获取URL路径
function e_GetJkurl(o = {}) {
  if (o.title == '留观医嘱' || o.title == '住院医嘱') {
    $(`#page a[href="#住院医嘱"]`).tab("show")
    return
  }
  if (o.m_id == 1) {
    page_iframe.add("", "病人一览表");
    return;
  }
  if (o.title == "住院医嘱") {
    $(`#page a[href="#住院医嘱"]`).tab('show')
    return
  }
  if (o.title == "急诊留抢/观病案首页") {
    page_iframe.add('急诊留抢观病案首页', `${'/ehr' + o.url}`);
    return
  }
  if (o.title == "病人特检报告查询") {
    page_iframe.add(o.title, `${WRT_config.server + '/' + WRT_config.BrMainLeft.TJCX}`);
    // window.open(`${WRT_config.server+'/'+WRT_config.BrMainLeft.TJCX}`)
    return
  }
  if (o.title == "MDT会诊") {
    page_iframe.add(o.title, `${WRT_config.BrMainLeft.HZMDT.M_URL}`);
    // window.open(`${WRT_config.server+'/'+WRT_config.BrMainLeft.TJCX}`)
    return
  }
  if (o.title == "手机短信通知") {
    page_iframe.add(o.title, `e-sms.html?al_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&al_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&al_yhid=${WRT_config.BrMainLeft.YHXX.YSYHID}&ls_idb=${WRT_config.url.ls_idb}`);
    // window.open(`${WRT_config.server+'/'+WRT_config.BrMainLeft.TJCX}`)
    return
  }
  if (o.title == "检查预约") {
    page_iframe.add(o.title, WRT_config.server + `/zyyz/jumpToYSZ.aspx?al_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&al_type=1`);
    // window.open(`${WRT_config.server+'/'+WRT_config.BrMainLeft.TJCX}`)
    return
  }
  //临时加
  if (o.title == '新血液净化医嘱CRRT') {
    page_iframe.add(o.title, `e-CrrtYzjld.html?blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&yongHuID=${WRT_config.ehrSz_init.YHXX.YSYHID}`);
    return;
  }
  if (o.title == '新营养医嘱') {
    page_iframe.add(o.title, `e-cwYyyzNew.html?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`);
    return;
  }
  // if (o.title == "住院特检预约") {
  //   page_iframe.add(o.title, `e-zytjyy.html?empi=${WRT_config.BrMainLeft["病案号"]}&al_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&al_yhid=${WRT_config.BrMainLeft.YHXX.YSYHID}&ls_idb=${WRT_config.url.ls_idb}`);
  //   // window.open(`${WRT_config.server+'/'+WRT_config.BrMainLeft.TJCX}`)
  //   return
  // }
  if (o.YLXGBGKtype) {
    page_iframe.add(o.title, `${WRT_config.server + '/' + o.url + "&as_openmode=ehr3"}`);
    return
  }
  if (o.title == '留观病案首页' || o.title == '留观病历首程' || o.title == '留观小结') {
    page_iframe.add(o.title, `${WRT_config.server + '/' + o.url}`);
    return
  }
  if (o.title == "门诊复查预约") {
    f_getYYguid()
    function f_getYYguid() {
      WRT_e.api.BrMainLeft.getFzyyAddressByApi({
        params: {
          al_yhid: WRT_config.BrMainLeft.YHXX.YSYHID,
          al_blid: WRT_config.url.al_blid,
          as_brbh: WRT_config.BrMainLeft.BRJBXX.BRBH,
          as_zyh: WRT_config.BrMainLeft.BRJBXX.ZYH
        },
        success(data) {
          if (data.Code == 1) {
            window.open(data.Result);
          }
        }
      })

    }
    return
  }
  if (o.title == "反馈列表") {
    page_iframe.add(o.title, "/FKYJ/buglist.aspx");
    return;
  }
  if (o.title == "开住院单") {
    page_iframe.add(o.title, WRT_config.server + '/' + o.url);
    return;
  }
  // let fd = WRT_config.trialuser.find(e => e == WRT_config.BrMainLeft.YHXX.YHZH)
  if (o.title == '华法令调整表') {
    page_iframe.add(o.title, `e-hfltzb.html?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`);
    return;
  }
  // 临时加
  // if (o.title == '新营养医嘱') {
  //   page_iframe.add(o.title, `e-nutriOrderList.html?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`);
  //   return;
  // }
  // 临时加
  // if (o.title == '儿科营养医嘱') {
  //   page_iframe.add(o.title, `e-pnOrderList.html?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`);
  //   return;
  // }
  if (o.title == '病人体温单') {
    page_iframe.add(o.title, `e-brtwdlb.html?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`);
    return;
  }
  if (o.title == '生长发育曲线') {
    page_iframe.add(o.title, `e-szfyqx.html?al_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&al_sex=${WRT_config.BrMainLeft.BRJBXX.BRXB}`);
    return;
  }
  if (o.title == '病程记录' || o.title == '留观病程录') {
    page_iframe.add(o.title, `e-blwslist.html?as_lx=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_zkdm=${WRT_config.BrMainLeft.BRJBXX.ZKDM}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}`);
    return;
  }

  if (o.title == "动态血糖监测报告") {
    page_iframe.add("动态血糖监测报告", 'http://172.16.202.86/#/reportPrepare?hospital=WZFYYY&admitNo=' + WRT_config.BrMainLeft.BRJBXX.ZYH + '&cureNo=' + WRT_config.BrMainLeft.BRJBXX.ZYH + "&as_openmode=ehr3")
    return
  }

  // if (o.title == '出院记录') {
  //   page_iframe.add(o.title, `e-cywslist.html?as_lx=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_zkdm=${WRT_config.BrMainLeft.BRJBXX.ZKDM}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}`);
  //   return;
  // }
  if (o.title == '病历知情记录汇总') {
    page_iframe.add(o.title, `e-zqjllistall.html?as_lx=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_zkdm=${WRT_config.BrMainLeft.BRJBXX.ZKDM}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}`);
    return;
  }

  // // 透析记录单
  // if (o.title == '透析记录单') {
  //   window.open(`http://172.16.201.160:8080/hemodialysis/xt/dialyseRecord?bah=${WRT_config.BrMainLeft["病案号"]||''}&timeStr=`)
  // }
  if (o.title == "大数据预测") {
    page_iframe.add("大数据预测AKI、CKD", "/dsjyc/dsjycfsgl.aspx");
    return;
  }
  if (o.check == "true") {
    page_iframe.add("专科会诊单", WRT_config.server + '/' + o.url + "&as_openmode=ehr3")
    return
  }
  if (o.hyhz) {
    page_iframe.add("专科会诊", WRT_config.server + '/' + o.url + "&as_openmode=ehr3")
    return
  }
  if (o.zkblsq) {
    page_iframe.add("跨科病例修正单", WRT_config.server + '/zyblws/' + o.url + "&as_openmode=ehr3")
    return
  }
  if (o.title == "浏览护理文书") {
    page_iframe.add("浏览护理文书", WRT_config.BrMainLeft["浏览护理文书URL"] + "&as_openmode=ehr3")
    return
  }
  if (o.title == "静脉通路") {
    page_iframe.add("静脉通路", `http://172.16.204.204/hlpt/hlhz/syhz_jld_list.aspx?lx=jmtl&as_blid=${WRT_config.url.al_blid}&as_brbh=${WRT_config.BrMainLeft.BRJBXX.BRBH}&as_zyh=${WRT_config.BrMainLeft.BRJBXX.ZYH}` + "&as_openmode=ehr3")
    return
  }
  if (o.title == "浙江省区域卫生信息平台共享信息调阅") {
    if (WRT_config.Qywsxx) {
      window.open(WRT_config.Qywsxx.Url)
      return
    }
    WRT_e.api.BrMainLeft.getQywsxx({
      params: { al_blid: WRT_config.url.al_blid },
      success(data) {
        if (data && data.Code == 1) {
          let res = JSON.parse(data.Result)
          if (res == 0) {
            WRT_e.ui.message({
              title: '提示',
              content: "身份证号信息为空，请及时补录",
              onOk() { },
            })
            return
          }
          WRT_config.Qywsxx = res
          if (res.Url) {
            window.open(res.Url)
          }
        }
      }
    })
    return
  }
  if (o.title == "温州市区域医疗平台链接") {
    if (WRT_config.Qywsxx) {
      window.open(WRT_config.Qywsxx.Url_wz)
      return
    }
    WRT_e.api.BrMainLeft.getQywsxx({
      params: { al_blid: parseInt(WRT_config.url.al_blid) },
      success(data) {
        if (data && data.Code == 1) {
          let res = JSON.parse(data.Result)
          if (res == 0) {
            WRT_e.ui.message({
              title: '提示',
              content: "身份证号信息为空，请及时补录",
              onOk() { },
            })
            return
          }
          WRT_config.Qywsxx = res
          if (res.Url_wz) {
            window.open(res.Url_wz)
          }
        }
      }
    })
    return
  }
  if (o.mzybs && o.url.indexOf("brmain.aspx") >= 0) {
    window.open(WRT_config.server + '/' + o.url)
    return
  } else if (o.title == "转诊信息") {
    // window.open('http://172.16.203.155:18087/webroot/decision/view/report?ref_c=d1dc9333-5ea0-4d18-908b-7d943cefd05d&viewlet=ysgzz%252Fqyzl_xzsj.cpt&ref_t=design')
    window.open(o.url)
    // window.location.href = 'http://172.16.203.155:18087/webroot/decision/view/report?ref_c=d1dc9333-5ea0-4d18-908b-7d943cefd05d&viewlet=ysgzz%252Fqyzl_xzsj.cpt&ref_t=design'
    return
  } else if ((o.title.indexOf('门住院既往病史') < 0 && o.title != "转诊信息") && o.mzybs) {
    page_iframe.add(o.title, WRT_config.server + '/' + o.url)
    return
  }

  if (o.title == "新版门住院既往病史") {
    page_iframe.add(o.title, WRT_config.server + `/zyyz/jumpToYSZ.aspx?al_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&al_type=2`);
    return
  }
  // if ((o.title == "门住院既往病史" ||o.title == "2023-04-23入院 急诊抢救" ||o.title == "2023-04-25入院 神经内科" ||o.title == "2023-05-08入院 康复科" ) && o.m_id== 'undefined' ) {
  //   window.open(o.url)
  //   return
  // }
  // if (o.title == "转诊信息") {
  //   // window.open('http://172.16.203.155:18087/webroot/decision/view/report?ref_c=d1dc9333-5ea0-4d18-908b-7d943cefd05d&viewlet=ysgzz%252Fqyzl_xzsj.cpt&ref_t=design')
  //   window.open(o.url)
  //   // window.location.href = 'http://172.16.203.155:18087/webroot/decision/view/report?ref_c=d1dc9333-5ea0-4d18-908b-7d943cefd05d&viewlet=ysgzz%252Fqyzl_xzsj.cpt&ref_t=design'
  //   return
  // }
  // 临时加的(原来是只有WRT_e.api.ehrSz.GetJkurl的)
  if (o.title == "临床营养中心会诊") {
    page_iframe.add(o.title, `e-yykhz.html?as_lx=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}`);
  } else {
    WRT_e.api.ehrSz.GetJkurl({
      params: {
        al_mid: o.m_id
      },
      success(data) {
        var url = data.Result;
        if (data.Code != "1") {
          WRT_e.ui.message({
            title: '信息窗口',
            content: '菜单地址未维护,请联系信息处',
            onOk() { },
          })
          return;
        }
        var param = "";
        if (o.paramList != "") {
          param = GetParam(o.paramList);
        }
        if (param != "") {
          if (url.indexOf("?") >= 0)
            url += "&" + param;
          else
            url += "?" + param;
        }
        if (o.title == "门住院既往病史") {
          page_iframe.add(o.title, url + `&as_openmode=ehr3&tmpid=${Math.random()}`);
          return
        }

        // if (o.title == "转诊信息") {
        //   // window.open('http://172.16.203.155:18087/webroot/decision/view/report?ref_c=d1dc9333-5ea0-4d18-908b-7d943cefd05d&viewlet=ysgzz%252Fqyzl_xzsj.cpt&ref_t=design')
        //   window.location.href = 'http://172.16.203.155:18087/webroot/decision/view/report?ref_c=d1dc9333-5ea0-4d18-908b-7d943cefd05d&viewlet=ysgzz%252Fqyzl_xzsj.cpt&ref_t=design'
        //   return
        // }
        // if(o.title=='病历集中打印及归档'||o.m_id=='129'){
        //    page_iframe.add("病历集中打印及归档", 'http://172.16.204.221/ehr/' + '/' + url + "&as_openmode=ehr3");
        // }
        if (o.title == '病历集中打印及归档' || o.m_id == '129') {
          WRT_e.api.BrMainLeft.GetNbmy({
            // params:{},
            success(data) {
              if (data) {
                let url = 'http://172.16.204.221/ehr/doLogin.aspx'
                if (WRT_config.BrMainLeft.YHXX.YSYHID == '12235' || WRT_config.BrMainLeft.YHXX.YSYHID == '23492') {
                  url = 'http://172.16.204.125/ehr/doLogin.aspx'
                }
                let csn = `as_idb=${WRT_config.BrMainLeft.BRJBXX.ZYID}&${data.Result}&as_ymdz=ctlblwsjlzy&as_ymdzcs=${encodeURIComponent(param)}`
                page_iframe.add("病历集中打印及归档", url + '?' + csn);
                return
              }
            }
          })
          return
        }
        if (o.title == "量表/评分表") {
          page_iframe.add("量表评分表", WRT_config.server + '/' + url + "&as_openmode=ehr3");
          return
        }
        if (o.title == '新增转/跨科病历修正通知单') {
          page_iframe.add("新增转科病历修正通知单", WRT_config.server + '/' + url + "&as_openmode=ehr3");
          return;
        }
        if (o.title == '特殊抗菌药物使用申请') {
          window.open(WRT_config.server + '/' + url + "&as_openmode=ehr3");
          return;
        }
        if (o.title == '三联抗菌药物使用申请') {
          window.open(WRT_config.server + '/' + url + "&as_openmode=ehr3");
          return;
        }
        let t_name = o.title
        if (o.m_id) {
          t_name = "Br" + o.m_id
        }
        page_iframe.add(o.title, WRT_config.server + '/' + url + "&as_openmode=ehr3", t_name)
      }
    })
  }
}
// function btnTrue() {
//   const yyhz = $('input[name=fqhz0]:checked').val()
//   if (yyhz == '发起肠外营养会诊') {
//     parent.page_iframe.add(yyhz, `${WRT_config.server}/zyblhzd/addhzd.aspx?as_blid=${WRT_config.url.as_blid}&as_zkid=${WRT_config.url.as_zkid}&as_hzbz=9`);
//     parent.page_iframe.del('临床营养中心会诊')
//   } else if (yyhz == '发起营养科会诊') {
//     parent.page_iframe.add(yyhz, `${WRT_config.server}/zyblhzd/addhzd.aspx?as_blid=${WRT_config.url.as_blid}&as_zkid=${WRT_config.url.as_zkid}&as_hzbz=56`);
//     parent.page_iframe.del('临床营养中心会诊')
//   } else {
//     WRT_e.ui.hint({msg:'请选择发起营养科会诊'})
//   }
// }

//获取日期
function gettime(val, type) {
  val = val || ''
  var d = new Date(val);
  var year = d.getFullYear();
  var month = change(d.getMonth() + 1);
  var day = change(d.getDate());
  var hour = change(d.getHours());
  var minute = change(d.getMinutes());
  var second = change(d.getSeconds());
  function change(t) {
    if (t < 10) {
      return "0" + t;
    } else {
      return t;
    }
  }
  var time = year + '-' + month + '-' + day + ' ' + hour + ':' + minute + ':' + second;
  if (type) {
    time = year + '-' + month + '-' + day
  }
  return time
}

//获取输液量
function getbrmrjmsyl() {
  WRT_e.api.BrMainLeft.brmrjmsyl({
    params: {
      al_blid: WRT_config.url["al_blid"]
    },
    success(data) {
      WRT_config.brmrjmsyl = data.Result
      $("#lb_brsyl").val(WRT_config.brmrjmsyl)
    }
  })
}

//处理URL拼接参数
function GetParam(paramlist) {
  if (paramlist.indexOf(",") >= 0) {
    var j_param = paramlist.split(',');
    var plist = "";
    var p_tmp = "";
    for (var i = 0; i < j_param.length; i++) {
      if (j_param[i] == "") continue;
      p_tmp = GetParamDz(j_param[i]);
      if (plist == "")
        plist = p_tmp;
      else
        plist += "&" + p_tmp;
    }
    return plist;
  } else {
    if (paramlist.indexOf("=") >= 0)
      return paramlist;
    else
      return GetParamDz(paramlist);
  }
}
//判断参数并拼接
function GetParamDz(param) {
  var ls_rtn;
  switch (param) {
    case "al_blid":
    case "as_blid":
    case "av_blid":
    case "blid":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BRJBXX.BLID;
      break;
    case "as_zyid":
    case "av_zyid":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BRJBXX.ZYID;
      break;
    case "as_idb":
    case "idb":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BRJBXX.ZYID;
      break;
    case " al_zkid":
      ls_rtn = "al_zkid" + "=" + WRT_config.BrMainLeft.BRJBXX.ZKID;
      break;
    case "av_zkid":
    case "as_zkid":
    case "zkid":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BRJBXX.ZKID;
      break;
    case "as_zkdm":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BRJBXX.ZKDM;
      break;
    case "as_zlzid":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BRJBXX.ZLZID;
      break;
    case "as_blzt":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BLZT;
      break;
    case "as_sfzh":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BRJBXX.SFZH;
      break;
    case "as_brbh":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BRJBXX.BRBH;
      break;
    case "as_yhzh":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.YHXX.YHZH;
      break;
    case "as_yhid":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.YHXX.YSYHID;
      break;
    case "as_zyh":
      ls_rtn = param + "=" + WRT_config.BrMainLeft.BRJBXX.ZYH;
      break;
    case " as_hzdlb":
      ls_rtn = param + "=" + ""
      break;
    default:
      param = "";
      break;
  }
  return ls_rtn;
}
//判断护理级别
function getHljb(hljb) {
  var j_hljb = "";
  if (hljb == 'ADL') {
    j_hljb = '一级护理(ADL)'
  } else {
    switch (parseInt(hljb)) {
      case 1:
        j_hljb = "特级护理";
        break;
      case 2:
        j_hljb = "一级护理";
        break;
      case 3:
        j_hljb = "二级护理";
        break;
      case 4:
        j_hljb = "三级护理";
        break;
      default:
        j_hljb = "未设置";
        break;
    }
  }
  return j_hljb
}

function setHljb(hljb) {
  var j_hljb = "";
  if (hljb == '一级护理(ADL)') {
    j_hljb = 'ADL'
  } else {
    switch (hljb) {
      case '特级护理':
        j_hljb = 1;
        break;
      case '一级护理':
        j_hljb = 2;
        break;
      case '二级护理':
        j_hljb = 3;
        break;
      case "三级护理":
        j_hljb = 4;
        break;
      default:
        j_hljb = '';
        break;
    }
  }
  return j_hljb
}

// 获取当前日期后一天时间
function formatDate(n) {
  var date = new Date();
  var year, month, day;
  date.setDate(date.getDate() + n);
  year = date.getFullYear();
  month = date.getMonth() + 1;
  day = date.getDate();
  s = year + '-' + (month < 10 ? ('0' + month) : month) + '-' + (day < 10 ? ('0' + day) : day);
  return s
}

/********弹出框回调 */
//一键下转回调
function resizeWin(width, height) {
  $("#lb_brzt").css('max-width', width)
  $("#lb_brzt").css('height', height)
}

//病人状态先心筛查录入回调
function continueCycz() {
  $("#lb_brzt").iziModal("destroy")
  url = `${WRT_config.server}/brcyczSZ.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_lclj=${WRT_config.BrMainLeft.LCLJ}&as_openmode=ehr3`
  WRT_e.ui.model({
    id: "lb_brzt",
    title: "修改病人住院状态",
    iframeHeight: "400px",
    iframe: true,
    iframeURL: url
  })
}

//入院诊断单修改回调
function xgBrzd_callback(val) {
  $("#lb_ryzd").text(val);
  $("#ryzd").iziModal('destroy')
}
//护理级别修改回调
function SetHljb(val) {
  let hljb = getHljb(val)
  $("#av_hljb").text(hljb);
  $("#hy_hljb").iziModal('destroy')
  $('.box_foot').addClass('none')
  var childWindow = $(`#长期医嘱 iframe`)[0].contentWindow;
  childWindow.getBrYz();
}
//治疗组修改回调
function SetZlz(id, val) {
  $("#ra_zkz").text(val);
  WRT_config.BrMainLeft.BRJBXX.ZLZID = id
  $("#hlzlz").iziModal('destroy')
}
// 修改住院病人状态
function SetBrCyzt(code, cyzt, msg) {
  $("#lb_brzt").iziModal('destroy')
  if (msg != '') {
    WRT_e.ui.message({
      title: '提示信息',
      content: msg,
      onOk() { },
    })
  }
  // WRT_config.BrMainLeft.BRLYZT=getcyzt(cyzt)
  // var Menus = new Menus_View();
  // Menus.$el = $("#menu-left");
  // Menus.init({
  //   data: WRT_config.BrMainLeft
  // }).render();

  WRT_e.api.BrMainLeft.getinit({
    params: {
      ll_blid: WRT_config.url["al_blid"],
      ls_idb: WRT_config.url["ls_idb"],
      al_zkid: WRT_config.ehrSz_init.YHXX.ZKID,
      al_yhid: WRT_config.ehrSz_init.YHXX.YSYHID
    },
    success(data) {
      let obj = JSON.parse(data.Result)
      if (data.Code == 1) {
        WRT_config.BrMainLeft = obj
        console.log('shuju1', WRT_config.BrMainLeft);
        var Menus = new Menus_View();
        Menus.$el = $("#menu-left");
        Menus.init({
          data: WRT_config.BrMainLeft
        }).render();
      }
    }
  })
}
//病人状态废弃
function getcyzt(cyzt) {
  let text = '';
  switch (cyzt) {
    case "0":
      text = "医师医嘱出院";
      break;
    case "1": text = "医嘱转院"//f_sfsf 0 
      break;
    case "2":
      text = "医转社卫生院"
      break;
    case "3":
      text = "非医曝离院"
      break;
    case "9":
      text = "死亡"
      break;
    case "C":
      text = "在院"
      break;
    default:
      break
  }
  return text
}
//修改营养风险筛查
function SetHz() {
  $("#lb_sfhz").iziModal('destroy')
}
//修改营养风险筛查
function e_OpenTab(url, title, params) {
  $("#lb_sfhz").iziModal('destroy')
  let urls = ''
  if (url.indexOf('http://') >= 0 || url.indexOf('https://') >= 0) {
    urls = url
  } else {
    urls = WRT_config.server + '/' + url
  }
  if (params) {
    urls = urls + '?' + params
  }
  page_iframe.add(title, urls)
}
//弹出框关闭
function CloseModalWin() {
  $(".iziModal").iziModal('destroy')
}
//新冠评估回调
function Closexgpg() {
  $(".iziModal").iziModal('tag_sgpg')
  this.resultData()
}
//主管医生回调
function e_SetZgys(name) {
  if (name) {
    $("#ZGYSXM").html(name)
  }
}


/**
 * //规则引擎
 */
let yxyz = {}
let callback_fun=null
function saveJcNotice(a) {
  alert(111,JSON.stringify(a))
}
function saveBeforjc(data, callback) {
  //规则引擎
  callback_fun=callback
  let arr_zgyq = []
  yxyz=data||{}
  yxyz.map(function(item){
    if(item){
      let list = {
        shouFeiXMID: item.shouFeiXMID,
        xiangMuMC: item.xiangMuMC,
        shuLiang: item.shuLiang,
        xiangMuLX: 'zl'
      }
      arr_zgyq.push(list)
    }
  })
  
  tssm_array = []
  WRT_e.api.au.getApprovalProjectByRule({
    params: {
      "biaoShiHao": WRT_config.url["al_blid"],
      "bingAnHao": WRT_config.BrMainLeft["病案号"],
      //  "biaoShiHao": "2817729",
      //  "bingAnHao": "0014607518",
      "shiFouMZ": false,
      "shiFouTJ": false,
      "shouFeiXMList": arr_zgyq,
      "yongHuID": WRT_config.ehrSz_init.YHXX.YSYHID,
      "changJingDM": "cwsf_zyyz"
    },
    success(msg) {
      if (msg.data && msg.data.linShiTJ) {
        if (msg.data.linShiTJ.length > 0) {
          linShiTJ_array = msg.data.linShiTJ
          let fd = msg.data.linShiTJ.filter(item => item.tiaoJianLB == '7')
          if (fd && fd.length > 0) {
            let name = arr_zgyq[fd[0].chaRuSX - 1].xiangMuMC
            WRT_e.ui.message({
              title: '提示信息',
              content: `【${name}】${fd[0].xunWenNR}`,
              onOk() {
              }
            })
            return
          }
          let text = ''
          let LBtype = false
          msg.data.linShiTJ.map(function (item) {
            let name = arr_zgyq[item.chaRuSX - 1].xiangMuMC
            if (item.tiaoJianLB == '6') {
              if (item.xunWenNR) {
                LBtype = true
                text += `【${name}】${item.xunWenNR},是否自费` + `</br></br>`
              }
            } else if (item.tiaoJianLB == '3') {
              if (item.xunWenNR) {
                text += `【${name}】${item.xunWenNR}` + `</br></br>`
              }
            } else if (item.tiaoJianLB == '4') {
              if (item.xunWenNR) {
                text += `【${name}】${item.xunWenNR},修改为自费` + `</br></br>`
              }
            } else if (item.tiaoJianLB == '10') {
              tssm_array.push(item)
            }
          })
          if (text) {
            if (LBtype) {
              WRT_e.ui.message({
                title: '提示信息',
                content: text,
                onOk() {
                  checktype()
                },
                onCancel() { }
              })
            } else {
              WRT_e.ui.message({
                title: '提示信息',
                content: text,
                onOk() {
                  callback_fun(yxyz)
                }
              })
            }
          } else {
            SavecheckTssm()
          }

        } else {
          callback_fun(yxyz)
        }
      } else {
        // WRT_e.ui.hint({msg:'规则引擎连接失败，请联系信息科！'})
        callback_fun(yxyz)
      }
    },
    error(msg) {
      callback_fun(yxyz)
    }
  })
  //消息提醒
  function checktype() {
    linShiTJ_array.forEach((item) => {
      if (item.tiaoJianLB == '6' || item.tiaoJianLB == '3' || item.tiaoJianLB == '4' || item.tiaoJianLB == '10') {
        if(yxyz.length>=item.chaRuSX){
          yxyz[item.chaRuSX-1].sfzf=1
        }else{
          yxyz[item.chaRuSX].sfzf=1
        }
      }
    })
    SavecheckTssm()
  }
  //规则引擎特殊说明
  function SavecheckTssm() {
    if (tssm_array.length > 0 && tssm_index < tssm_array.length) {
      tssmhtml(tssm_array[tssm_index])
    }else{
      callback_fun(yxyz)
    }
  }
   
  //特殊说明提醒
  function tssmhtml(obj) {
    let temp = `
     <div>${obj.xunWenNR}</div>
     <div><input id="tssm_input" class="none" type="text" autocomplete="off" placeholder="特殊说明输入"></div>
     <div>
       <button class="e_btn" onclick="tssm_click()">特殊说明</button>
       <button class="e_btn" onclick="check_click(${obj.chaRuSX})">确认</button>
       <button class="e_btn" onclick="$('#tssmhtml').iziModal('destroy');callback_fun(yxyz)">取消</button>
     </div>
     `
    WRT_e.ui.model({
      id: 'tssmhtml',
      title: '信息提示',
      width: "650px",
      content: temp,
      iframe: false,
    })
  }
}
//特殊说明确定
function check_click(index) {
  let tssm = $("#tssm_input")[0].value
  if (!tssm) {
    yxyz.sfzf = 1
    tssm_index++
    if (tssm_array.length > 0 && tssm_index < tssm_array.length) {
      tssmhtml(tssm_array[tssm_index])
    } else {
      yxyz = []
      callback_fun(yxyz)
    }
    $('#tssmhtml').iziModal('destroy')
  } else {
    WRT_e.api.au.saveShuoMing({
      params: {
        "biaoShi": WRT_config.url["al_blid"], //诊疗活动ID/病例ID
        "bingAnHao": WRT_config.BrMainLeft["病案号"], //病案号
        "changJingDM": "cwsf_zyyz", //场景代码
        "yingYongDM": "020", //应用代码
        "yongHuID": WRT_config.ehrSz_init.YHXX.YSYHID, //用户ID
        "teShuSM": tssm, //特殊说明
        "jiLuSJ": gettime(), //记录时间
        "shiFouMZ": "2" //是否门诊，1门诊，2住院
      },
      success(msg) {
        if (msg.hasError == '0') {
          WRT_e.ui.hint({ type: 'success', msg: '记录成功！' })
        }
        $('#tssmhtml').iziModal('destroy')
        tssm_index++
        if (tssm_array.length > 0 && tssm_index < tssm_array.length) {
          tssmhtml(tssm_array[tssm_index])
        } else {
          callback_fun(yxyz)
          yxyz = []
        }
      }
    })
  }

}
//特殊说明点击
function tssm_click() {
  $("#tssm_input").removeClass("none")
}
//处理医疗报告卡菜单
function ylbgklist(target) {
  let YLXGBGK = [...WRT_config.BrMainLeft.YLXGBGK]
  if (target.CRBBGK && target.CRBBGK.length > 0) {
    target.CRBBGK.sort(function (a, b) {
      return b.BGSJ < a.BGSJ ? -1 : 1
    })
    target.CRBBGK.map(function (item) {
      YLXGBGK.push({
        M_NAME: "传染病报告" + gettime(item.BGSJ, true),
        M_URL: item.BZ,
        YLXGBGKtype: true
      })
    })
  }
  if (target.ZLBGK && target.ZLBGK.length > 0) {
    target.ZLBGK.map(function (item) {
      YLXGBGK.push({
        M_NAME: "肿瘤报告卡" + gettime(item.BGRQ, true),
        M_URL: item.SHBZ,
        YLXGBGKtype: true
      })
    })
  }
  if (target.TNBBGK && target.TNBBGK.length > 0) {
    target.TNBBGK.map(function (item) {
      YLXGBGK.push({
        M_NAME: "糖尿病报告卡" + gettime(item.BGRQ, true),
        M_URL: item.SHBZ,
        YLXGBGKtype: true
      })
    })
  }
  if (target.XNBGK && target.XNBGK.length > 0) {
    target.XNBGK.map(function (item) {
      YLXGBGK.push({
        M_NAME: "心脑报告卡" + gettime(item.BGRQ, true),
        M_URL: item.SHBZ,
        YLXGBGKtype: true
      })
    })
  }
  if (target.CRBBGK1 && target.CRBBGK1.length > 0) {
    target.CRBBGK1.map(function (item) {
      YLXGBGK.push({
        M_NAME: "传染病报告卡" + gettime(item.XGSJ, true),
        M_URL: item.CUSTOMER,
        YLXGBGKtype: true
      })
    })
  }
  return YLXGBGK
}
// 发送短信验证码给工作人员（用于再次授权金额）
function sendCode() {
  $(`.showSdBtn`).css({
    "display": "none"
  })
  $(`.ishowSdBtn`).css({
    "display": "inline"
  })
  // 发送验证码给值班人员（e_sendOverdraftMessage）
  WRT_e.api.BrMainLeft.sendOverdraftMessage({
    params: {
      ll_blid: WRT_config.url["al_blid"]
    },
    success(data) {
      if (data.Code == '1') {
        WRT_e.ui.hint({
          msg: '验证码发送成功',
          type: 'success'
        })
      } else {
        if (data.CodeMsg == '未查询到发送对象号码!') {
          WRT_e.ui.hint({
            msg: '未查询到发送对象号码!',
            type: 'error'
          })
          $(`.showSdBtn`).css({
            "display": "inline"
          })
          $(`.ishowSdBtn`).css({
            "display": "none"
          })
        }
        if (data.CodeMsg == '该患者再次授权金额次数已达到上限') {
          WRT_e.ui.hint({
            msg: '该患者再次授权金额次数已达到上限',
            type: 'error'
          })
          $(`.showSdBtn`).css({
            "display": "inline"
          })
          $(`.ishowSdBtn`).css({
            "display": "none"
          })
        }
        if (data.CodeMsg == '发送短信失败！') {
          WRT_e.ui.hint({
            msg: '发送短信失败！',
            type: 'error'
          })
          $(`.showSdBtn`).css({
            "display": "inline"
          })
          $(`.ishowSdBtn`).css({
            "display": "none"
          })
        }
      }
    },

  })
}

/********************视图********************/

//病人基本信息
var Menus_View = WRT_e.view.extend({
  render: function () {
    // console.log('页面绘制初始数据', this.data)
    let BRJBXX = this.data.BRJBXX
    let mzjk = this.data["麻醉接口记录"] || ""
    // this.data.hzd_list.map(function (item) {
    //   item.check = true
    // })

    var arr = WRT_config.list_hzdlist || []
    // mzjkarr.map(function(item){
    //   if(item){
    //     array.push({M_NAME:item+"主刀手术"})
    //   }
    // })
    let YLXGBGK = WRT_config.list_YLXGBGK || []
    let ZYYZCD = this.data.ZYYZCD || []
    if(WRT_config.BrMainLeft.BRJBXX.ZKID != 42){
      ZYYZCD.map(function(item,index){
        if(item.M_NAME=='血液净化医嘱CRRT'||item.M_NAME=='新血液净化医嘱CRRT'){
          ZYYZCD.splice(index,1)
        }
      })
    }
    let lists = []
    this.data.XTJL.push({
      M_NAME: "动态血糖监测报告",
      M_URL: 'http://172.16.202.86/#/reportPrepare?hospital=WZFYYY&admitNo=' + WRT_config.BrMainLeft.BRJBXX.ZYH + '&cureNo=' + WRT_config.BrMainLeft.BRJBXX.ZYH
    })
    if (JHCDSignal) {
      ZYYZCD = []
      this.data.ZYYZCD.map(function (item) {
        if (item.M_NAME == "住院医嘱") {
          item.M_NAME = "留观医嘱"
        }
        if (item.M_NAME == '留观医嘱' || item.M_NAME == '已开医嘱查询' || item.M_NAME == '手术通知单' || item.M_NAME == '量表/评分表' || item.M_NAME == '医嘱打印' || item.M_NAME == '输血医嘱' || item.M_NAME == '麻醉处方打印' || item.M_NAME == '检验检查医嘱执行情况' || item.M_NAME == '医用食品医嘱' || item.M_NAME == '华法令调整表') {
          ZYYZCD.push(item)
        }
      })
      if (WRT_config.BrMainLeft.KZYD == 1) {
        ZYYZCD.push({
          M_NAME: "开住院单",
          M_URL: WRT_config.BrMainLeft["开住院单URL"]
        })
      }
      if (this.data.ZYBLWSCD && this.data.ZYBLWSCD.length > 0) {
        this.data.ZYBLWSCD.map(function (item) {
          if (item.M_NAME == "病案首页") {
            item.M_NAME = "留观病案首页"
            item.M_URL = WRT_config.BrMainLeft["留观病历首页URL"]
          } else if (item.M_NAME == "入院记录") {
            item.M_NAME = "留观病历首程"
            item.M_URL = WRT_config.BrMainLeft["留观病历首程URL"]
          } else if (item.M_NAME == "出院记录") {
            item.M_NAME = "留观小结"
            item.M_URL = WRT_config.BrMainLeft["留观小结URL"]
          } else if (item.M_NAME == "病程记录") {
            item.M_NAME = "留观病程录"
            item.M_URL = WRT_config.BrMainLeft["病程记录URL"]
          }
        })
        // this.data.ZYBLWSCD.splice(3, 0, {
        //   M_NAME: "会诊记录",
        //   M_URL: "会诊记录"
        // })
        // 判断是否为日间病人(ShowImg_label==1是日间病人)

        let zy = this.data.ZYBLWSCD || []
        if (WRT_config.BrMainLeft.ShowImg_label != 1) {
          this.data.ZYBLWSCD.map(function (item, index) {
            if (item.M_NAME == "日间病历") {
              zy.splice(index, 1)
            }
          })
        }
        lists.push({
          title: "住院病历文书",
          children: [{
            M_NAME: "急诊留抢/观病案首页",
            M_URL: '/jzlgsy/jzlgsy.aspx?blid=' + BRJBXX.BLID
          }, ...zy],
          icon: "icon-wenshu"
        })
      }
      if (this.data.XTJL && this.data.XTJL.length > 0) {
        lists.push({
          title: "血糖检测记录查询",
          children: this.data.XTJL,
          icon: "icon-chaxun1"
        })
      }
      if (mzjk && mzjk.length > 0) {
        lists.push({
          title: "麻醉接口记录",
          children: mzjk,
          icon: "icon-mazuike--"
        })
      }
      lists.push({
        title: "会诊单管理",
        children: [{
          M_NAME: "MDT会诊",
          M_URL: "MDT会诊"
        }, ...arr],
        icon: "icon-jiludanzilishijilu"
      })

      lists.push({
        title: "病人检查查询",
        children: [{
          M_NAME: "病人特检报告查询",
          M_URL: "病人特检报告查询"
        }, {
          M_NAME: "手机短信通知",
          M_URL: "手机短信通知"
        },
        {
          M_NAME: "检查预约",
          M_URL: "检查预约"
        }
        ],
        icon: "icon-chaxun"
      })
      lists.push({
        title: "医疗相关报告卡",
        children: YLXGBGK,
        icon: "icon-qiapian"
      })
      // if (this.data.JWS && this.data.JWS.length > 0) {
      lists.push({
        title: "门住院既往病史",
        children: WRT_config.list_mzzyjwbs || [],
        icon: "icon-zhuyuanjilu"
      })
      // }
    } else {
      let zyb_list = []
      if (WRT_config.BrMainLeft.JZLG_Signal) {
        this.data.ZYBLWSCD.map(function (item) {
          if (item.M_NAME == "病案首页") {
            item.M_NAME = "留观病案首页"
            item.M_URL = WRT_config.BrMainLeft["留观病历首页URL"]
          }
        })
      }
      // 判断是否为日间病人(ShowImg_label==1是日间病人)
      let zy = this.data.ZYBLWSCD || []
      if (WRT_config.BrMainLeft.ShowImg_label != 1) {
        this.data.ZYBLWSCD.map(function (item, index) {
          if (item.M_NAME == "日间病历") {
            zy.splice(index, 1)
          }
        })
      }
      zyb_list = zy || []
      if (WRT_config.BrMainLeft.BRJBXX.ZKID == 3777 || WRT_config.BrMainLeft.BRJBXX.ZKID == 49) {
        zyb_list = [{
          M_NAME: "急诊留抢/观病案首页",
          M_URL: '/jzlgsy/jzlgsy.aspx?blid=' + BRJBXX.BLID
        }, ...zy]
      }

      // zyb_list.push({
      //   M_NAME: "透析记录单",
      //   M_URL: `http://172.16.201.160:8080/hemodialysis/xt/dialyseRecord?bah=${this.data["病案号"]||''}&timeStr=`
      // })
      lists = [
        {
          title: "住院病历文书",
          children: zyb_list,
          icon: "icon-wenshu"
        },
        {
          title: "血糖检测记录查询",
          children: this.data.XTJL,
          icon: "icon-chaxun1"
        },
        // {
        //   title: "血糖检测记录查询",
        //   children: [{
        //     M_NAME: "动态血糖监测报告",
        //     M_URL: 'http://172.16.202.86/#/reportPrepare?hospital=WZFYYY&admitNo='+WRT_config.BrMainLeft.BRJBXX.ZYH+'&cureNo='+WRT_config.BrMainLeft.BRJBXX.ZYH
        //   },...this.data.XTJL],
        //   icon: "icon-chaxun1"
        // },
        {
          title: "麻醉接口记录",
          children: mzjk,
          icon: "icon-mazuike--"
        },
        {
          title: "会诊单管理",
          children: [{
            M_NAME: "MDT会诊",
            M_URL: "MDT会诊"
          }, ...arr],
          icon: "icon-jilu"
        },
        {
          title: "跨科病例修正申请单",
          children: this.data.ZKKBLSQDCD,
          icon: "icon-guanlikeshi"
        },
        {
          title: "病人检查查询",
          children: [{
            M_NAME: "病人特检报告查询",
            M_URL: "病人特检报告查询"
          }, {
            M_NAME: "手机短信通知",
            M_URL: "手机短信通知"
          },
          {
            M_NAME: "检查预约",
            M_URL: "检查预约"
          }
          ],
          icon: "icon-chaxun2"
        },
        {
          title: "医疗相关报告卡",
          children: YLXGBGK,
          icon: "icon-qiapian"
        },
        {
          title: "门诊复查预约",
          children: [{
            M_NAME: "门诊复查预约",
            M_URL: "门诊复查预约"
          }],
          icon: "icon-yuyue"
        },
        // {
        //   title: "门住院既往病史",
        //   children: [{ M_NAME: "浙江省区域卫生信息平台共享信息调阅", M_URL: "浙江省区域卫生信息平台共享信息调阅" }, ...this.data.JWS],
        //   icon: "icon-zhuyuanjilu"
        // }
      ]
      // if (this.data.JWS && this.data.JWS.length > 0) {
      lists.push({
        title: "门住院既往病史",
        children: WRT_config.list_mzzyjwbs || [],
        icon: "icon-zhuyuanjilu"
      })
      // }
    }
    // this.data.BRNL='出生时间：2022-09-16 12：11'
    caidan_lists = lists
    let hljb = getHljb(this.data.BRJBXX.HLJB)
    // this.data.BRLYZ = '已财务出院'
    // this.data.BRLYZT = '已财务出院'
    // console.log('自己url',ZYYZCD,'对方页面',this.data.WDCYCD)
    // 原2377 ${this.data.LCLJ == 1 ? `<li class="list-item1"><a class="lclj" style="color:red">临床路径</a></li>` : this.data.LCLJ == 9 ? `<li class="list-item1"><a class="lclj" style="color:red">临床路径(已退出)</a></li>` : this.data.LCLJ == 0 ? `<li class="list-item1"><a class="lclj" style="color:red">加入临床路径</a></li>` : ""}
    // 加在2377 ${this.data.LCLJShow == 0?`<li class="list-item1"><a class="lclj" style="color:red">加入临床路径</a></li>`: this.data.LCLJShow == 1?`<li class="list-item1"><a class="lclj" style="color:red">临床路径</a></li>`: this.data.LCLJShow == 9?`<li class="list-item1"><a class="lclj" style="color:red">临床路径(已退出)</a></li>`:`` }
    let html = `
    <div id="panel_group">
      <div class="panel panel-default">
        <h4 id="headOne" class="flex-row menu-head align-items-center justify-content-between panel-title"
            data-toggle="collapse" data-parent="#accordion" href="#collapseOne" onclick="dgchangeitem('One')">
            <span>
              <i class="glyphicon glyphicon-list-alt"></i>
              <span class="panel-title-text">病人基本信息--${BRJBXX.ZKMC}</span>
            </span>
            <i class="iconfont icon-xiangshang"></i>
            <i class="iconfont icon-xiala" style="display:none"></i>
        </h4>
        <div class="panel-heading" role="tab" id="headingOne" style="padding: 10px 10px;">
          <div class="tag flex-row justify-content-between">
            <div>
              <span class="e-tag margin_1 ${BRJBXX.CRBS ? 'e-tag-red' : 'e-tag-blue'}" style="font-size: 14px;">${BRJBXX.ZDYBQDM}</span>
              <span class="e-tag margin_1 e-tag-red">
              ${this.data.BRLYZT == '已财务出院' ? `已出院` : `
                ${daysBetween(BRJBXX.BQRYSJ, new Date(), 'year', BRJBXX.CWRYSJ)}天
              `}
              </span>
              ${WRT_config.BrMainEinit.QCSL && WRT_config.BrMainEinit.QCSL != 0 ? `<span class="e-tag e-tag-red">质控</span>` : ''}
            </div>
            <div>
              ${WRT_config.Limited ? `<span class="e-tag e-tag-red" style="font-size: 10px;padding: 0 2px;">限</span>` : ''}
              <span class="e-tag e-tag-blue">${BRJBXX.JSDM ? BRJBXX.JSDM : ''}</span>
            </div>
          </div>
          <div class="flex-row align-items-center">
            <img src="${BRJBXX.BRXB == 1 ? './images/man.png' : './images/woman.png'}" width="40" alt="">
            <div class="flex-fill user_text">
              <strong style="font-size: 16px;">${this.data.EMPI || ''}</strong>
              ${this.data.P_signal == '0' ? `` : `<strong style="color:#f5222d;font-size: 16px;">P</strong>`} <br />
              <b style="font-size: 18px;">${BRJBXX.BRXM}</b>${WRT_config.BrMainLeft.ShowImg_label && (WRT_config.BrMainLeft.fsx == '' || WRT_config.BrMainLeft.anlh == true || WRT_config.BrMainLeft.hmd == true || WRT_config.BrMainLeft.zzbr == 1) ? `<br />` : ``}&nbsp;&nbsp;${BRJBXX.BRXB == 1 ? '男' : '女'}${this.data.BRNL.indexOf('出生时间') >= 0 ? `</br>${this.data.BRNL}` : `&nbsp;&nbsp;${this.data.BRNL}`}
            </div>
            <div>
              ${WRT_config.BrMainLeft.gcpbs && WRT_config.BrMainLeft.gcpbs.hasError == 0 && WRT_config.BrMainLeft.gcpbs.data == true ? `<div style="color:#f5222d">GCP</div>` : ''}
              ${WRT_config.BrMainLeft.right_top_btn_text ? `<div class="sgpg xgpg_text">${WRT_config.BrMainLeft.right_top_btn_text}</div>` : ''}
              ${WRT_config.BrMainLeft.ShowImg_label == 1 ? `<img src="./images/rjss2.png" class="tag-icon" width="15" height="15" style="float:right">` : `${WRT_config.BrMainLeft.ShowImg_label == 2 ? `<img src="./images/rjss1.png" class="tag-icon" width="15" height="15" style="float:right">` : ``}`} 
              ${WRT_config.BrMainLeft.anlh == true ? `<span style="font-size:12px;font-weight: bold;font-family:'宋体';position: relative;right: 2px">安</span>` : ``}
              ${WRT_config.BrMainLeft.hmd == true ? `<img src="./images/hmd.png" width="15" height="13" style="float:right;padding-right: 2px;">` : ``}
              ${WRT_config.BrMainLeft.fsx != '' ? `<img src='./images/fsx.png' width="15" height="13"  style="padding-right: 2px;">` : ``}
              ${WRT_config.BrMainLeft.zzbr == 1 ? `<span style="color:red;font-size:12px;font-weight: bold;padding-right: 2px;">转诊</span>` : ``} 
            </div>
          </div>
          <div class="status" style="background:${function () {
        switch (BRJBXX.HLJB) {
          case "1"://特级护理
            return '#6000DB'
          case "2"://一级护理
            return '#E20000'
          case "3"://二级护理
            return '#F1D900'
          case "4"://三级护理
            return '#31D742'
          default://无护理级别
            return '#dae6fc'
        }
      }()};"></div>
        <div id="collapseOne" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingOne">
          <div class="panel-body" style="overflow: hidden;padding:0px">
            <table>
              <tbody>
                ${BRJBXX.SFZH == "" || BRJBXX.SFZH == null ? `<tr><a class="JYHLJB_title">身份证号为空！请及时去护士站或收费处补录！</a></tr>` : ""}
                <tr>
                  <td class="table-title">病案号：</td>
                  <td class="table-value" title="${BRJBXX.BLID}">${this.data["病案号"] || ''}
                  </td>
                </tr>
                
                <tr>
                  <td class="table-title">入院单诊断：</td>
                  <td class="table-value "><a id="lb_ryzd" class="ryzd blackbody">${BRJBXX.RYZD || ""}</a><img id="znimg" src="./images/指南.png" class="disease ${WRT_config.DiseaseGuide && WRT_config.DiseaseGuide != 0 ? `` : `none`}" style="float: right;cursor: pointer;" /></td>
                </tr>
                <tr>
                  <td class="table-title">联系电话：</td>
                  <td class="table-value ">${BRJBXX.LXDH}</td>
                </tr>
                <tr>
                  <td class="table-title">财务入院：</td>
                  <td class="table-value">${gettime(BRJBXX.CWRYSJ)}</td>
                </tr>
                <tr>
                  <td class="table-title">病区入院：</td>
                  <td id="bqrysj1" class="table-value none"><input id="dt" type="text" name="birthday" style="width:117px" /><a class="bqrysjshow">修改</a></td>
                  <td id="bqrysj2" class="table-value">${this.data.XGBQRYSJ == 1 ? `<a class="bqrysj">${BRJBXX.BQRYSJ ? gettime(BRJBXX.BQRYSJ) : ''}</a>` : `${BRJBXX.BQRYSJ ? gettime(BRJBXX.BQRYSJ) : ''}`}</td>
                </tr>
                <tr>
                  <td class="table-title">开住院单医生：</td>
                  <td class="table-value zydj">${this.data.KDYS}</td>
                </tr>
                <tr>
                  <td class="table-title">当前操作医生：</td>
                  <td class="table-value">${this.data.YHXX.YHXM}</td>
                </tr>
                <tr>
                  <td class="table-title">当前病人饮食：</td>
                  <td class="table-value">${this.data["病人饮食"] ? this.data["病人饮食"] : "未下达"}</td>
                </tr>
                <tr>
                  <td class="table-title">当前护理级别：</td>
                  <td class="table-value">${this.data.XGHLJB == 1 ? `<a id="av_hljb" class="hy_hljb" style="${hljb == "未设置" ? "color:red" : ""}">${hljb}</a>` : `${hljb}`}</td>
                </tr>
                <tr id="Liquid" ${WRT_config.LiquidSignal != 0 ? "style='display:none'" : ""}>
                  <td class="table-title" style="box-sizing: content-box;">液体出入量平衡：</td>                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           
                  <td class="table-value">${liquHtml()}</td>
                </tr>
                <tr>
                  <td class="table-title">当前治疗组：</td>
                  <td class="table-value">${this.data.XGZLZ == 1 ? `<a id="ra_zkz" class="hlzlz">${this.data.hl_zlz}</a>` : `${this.data.hl_zlz}`}</td>
                </tr>
                ${JHCDSignal ? "" : `<tr>
                  <td class="table-title">主管医生：</td>
                  <td id="ZGYSXM" class="table-value"><a class="zgys">${this.data.ZGYSXM || '未设置'}</a></td>
                </tr>`}
                <tr>
                  <td class="table-title">病人状态：</td>
                  <td class="table-value"><span id="lb_brzyzt" class="blackbody">${this.data.BRLYZT || ''}</span>${this.data.BRLYZT == '未病区入院' ? `` : this.data.BRLYZT != '已财务出院' ? `<a class="spacing e-tagbtn-prime lb_brzt">修改</a>` : ''} ${this.data.BRLYZT == '在院' ? `<a class="spacing e-tagbtn-prime lb_yjzx"> 一键下转</a>` : ''}</td>
                </tr>
                ${WRT_config.BrMainLeft["在院状态代码"] != 0 ? `<tr>
                  <td>
                    ${WRT_config.BrMainLeft["出院时间"]}
                  </td>
                </tr>`: ""}
                <tr>
                  <td class="table-title">
                    <a class="e-tagbtn-prime hyddy">化验单打印</a>
                  </td>
                </tr>
                ${WRT_config.BrMainLeft["在院状态代码"] == 0 ? `
                  ${this.data.FYQK && this.data.FYQK.ZFYE && this.data.FYQK.ZFYE < 0 ? `
                  <tr>
                    <td class="table-title" >
                      <span id="lb_ts" class="lb_ts_tooltip" >
                        <span style="color:Red;ursor: default;">预缴余额：</span>
                        <span class="lb_ts_tooltiptext">
                          <span id="lb_ts">因医保报销原因，实际数额会有出入，以出院结算为准</span><br/>
                        </span>
                      </span>
                    </td>
                    <td style="color:red;font-weight:bold">
                      ${this.data.FYQK.ZFYE}
                    </td>
                  </tr>`: `
                  <tr>
                    <td class="table-title">预缴余额：</td>
                    <td class="table-value">
                      ${this.data.FYQK ? this.data.FYQK.ZFYE : 0}
                      ${this.data.K_signal == '0' ? `` : `<span class="e-tag-red" style="color:#fff;background:#f5222d;padding:1px 3px;border-radius: 50%;">K</span>`}
                    </td>
                  </tr>`}
                <tr>
                  <td class="table-title">总记账金额：</td>
                  <td class="table-value">${this.data.FYQK ? this.data.FYQK.ZJZJE : 0}</td>
                </tr>
                <tr>
                  <td class="table-title">总自付金额：</td>
                  <td class="table-value">${this.data.FYQK ? this.data.FYQK.ZZFJE : 0}</td>
                </tr>`: ""}
                ${WRT_config.GetGxbMsg != "无需显示" ? `
                <tr>
                  <td class="dlys" style="display: inline-block; width: 100%;">
                    <span style="display: inline-block; width: 200%;">${WRT_config.GetGxbMsg}</span>
                  </td>
                </tr>
                `: ``}
                ${WRT_config.BrMainLeft.BRJBXX.ZKID == 28 && this.data.B_signal ? ` <tr>
                  <td class="table-title">困难人群白内障
                  <td class="table-value">
                    <span>：</span>
                    <span id="lb_knrqbnz" class="blackbody">
                      ${this.data.B_signal == 1 ? `是` : this.data.B_signal == 0 ? `否` : ''}
                      <a class="bnzSingle e-tagbtn-prime">修改</a>
                    </span>
                  </td>
                </tr>
                `: ""}
                ${this.data["CAPRINI评分"] == '--分' ? "" : `<tr>
                  <td class="table-title">
                    <a class="blackbody">caprini评分</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["CAPRINI评分"] ? this.data["CAPRINI评分"] : ""}</a>
                  </td>
                </tr>`}
                ${this.data["APACHE_II"] == '--分' || this.data["BRJBXX"].ZKID != 42 ? "" : `<tr>
                  <td class="table-title">
                    <a class="blackbody">APACHE评分</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["APACHE_II"] ? this.data["APACHE_II"] : ""}</a>
                  </td>
                </tr>`}
                ${this.data["SOFA"] == '--分' || this.data["BRJBXX"].ZKID != 42 ? "" : `<tr>
                  <td class="table-title">
                    <a class="blackbody">SOFA评分</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["SOFA"] ? this.data["SOFA"] : ""}</a>
                  </td>
                </tr>
                `}
                ${!this.data["death_rate"] || this.data["death_rate"] == '' || this.data["death_rate"] == '--分' || this.data["BRJBXX"].ZKID != 42 ? "" : `<tr>
                  <td class="table-title">
                    <a class="blackbody">预计死亡率</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["death_rate"] ? this.data["death_rate"] : ""}</a>
                  </td>
                </tr>`}
                ${this.data["PADUA评分"] == '--分' ? "" : `<tr>
                  <td class="table-title">
                    <a class="blackbody">padua评分</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["PADUA评分"] ? this.data["PADUA评分"] : ""}</a>
                  </td>
                </tr>`}
                ${this.data["产科VTE评分"] == '--分' ? "" : `<tr>
                  <td class="table-title">
                    <a class="blackbody">产科VTE评分</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["产科VTE评分"] ? this.data["产科VTE评分"] : ""}</a>
                  </td>
                </tr>`}
                ${this.data["PHQ-4评分"] == '--分' ? "" : `<tr>
                  <td class="table-title">
                    <a class="blackbody">PHQ-4评分</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["PHQ-4评分"] ? this.data["PHQ-4评分"] : ""}</a>
                  </td>
                </tr>`}
                ${this.data["NRS评分"] == '--分' ? "" : `<tr>
                  <td class="table-title">
                    <a class="blackbody">NRS评分</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["NRS评分"] ? this.data["NRS评分"] : ""}</a>
                  </td>
                </tr>`}
                ${this.data["ADL-MBI评分"] == '--分' ? "" : `
                <tr>
                  <td class="table-title">
                    <a class="blackbody">ADL-MBI评分</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["ADL_BI评分"] ? this.data["ADL_BI评分"] : ""}</a>
                  </td>
                </tr>`}
                
                ${this.data["otfj"] && this.data["otfj"] != -1 ? `<tr>
                  <td class="table-title">
                    <a class="blackbody">呕吐分级评级</a>
                  </td>
                  <td class="table-value">
                    <a class="blackbody">${this.data["otfj"]}</a>
                  </td>
                </tr>`: ""}
              </tbody>
            </table>
            <table>
              <tbody>
                <tr>
                  <td>
                    <a class="JYHLJB_title">${this.data.JYHLJB}</a>
                  </td>
                </tr>
                ${JHCDSignal ? "" : `<tr>
                  <td class="table-title">
                    <a class="yyfxsc blackbody">
                      营养管理
                      ${this.data["营养风险筛查分数"] && this.data["营养风险筛查分数"] != '' ? `（风险评估：<span class="JYHLJB_title">${this.data["营养风险筛查分数"] ? this.data["营养风险筛查分数"] + '分' : ""}</span>）` : ``}
                    </a>
                  </td>
                </tr>`}
                ${WRT_config.BrMainLeft["认知功能"] ? `<tr>
                  <td class="table-title">
                    <a class="blackbody">认知功能（风险评估：<span class="JYHLJB_title">${WRT_config.BrMainLeft["认知功能"]}分</span>）</a>
                  </td>
                </tr>`: ''}
                ${WRT_config.BrMainLeft["易致低钾药物使用中"] ? `<tr>
                  <td class="table-title">
                    <a class="blackbody">易致低钾药物使用中:${WRT_config.BrMainLeft["易致低钾药物使用中"]}</a>
                  </td>
                </tr>`: ''}
                <tr>
                ${WRT_config.BrMainLeft.yypg ? `<tr>
                  <td>
                    <span style="color: red;">${WRT_config.BrMainLeft.yypg}</span>
                  </td>
                </tr>`: ''}
                <tr>
                  <td>
                    <a class="ywjjxt blackbody">药物警戒系统</a>
                  </td>
                </tr>
                ${JHCDSignal ? "" : `<tr>
                <td>
                    <a class="txjld blackbody">透析记录单</a>
                  </td>
                </tr>
                <tr><td><a class="drgfzyc blackbody">DRG分组预测</a></td></tr>`}
                <tr><td><a class="blsj blackbody">不良事件链接</a></td></tr>
                ${WRT_config.BrMainLeft.BRJBXX.ZKID == 49 ? `<tr>
                  <td>
                    <a class="zcsqje e-tagbtn-prime">再次授权金额</a>
                  </td>
                </tr>`: ""}
                ${WRT_config.BrMainLeft.fsx != '' ? `<tr>
                  <td class="table-title">
                    <a class="blackbody" style="text-decoration:none">放射性植入术提示</a>
                  </td>
                </tr>`: ""}
                ${WRT_config.BrMainLeft.fsx != '' ? `<tr>
                  <td class="table-value">
                    <span>${WRT_config.BrMainLeft.fsx}，该患者进行放射性粒子植入术</span>
                  </td>
                </tr>`: ""}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      ${JHCDSignal ? "" : `<div class="panel panel-default">
          <div class="panel-heading box_list" role="tab" id="headingtwo" onclick="dgchangeitem('two')">
            <h4 class="flex-row align-items-center justify-content-between panel-title" class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapsetwo" aria-expanded="false" aria-controls="collapsetwo">
              <span>
                <i class="iconfont icon-caozuoren"></i>
                <span class="panel-title-text">病人操作</span>
              </span>
              <i class="iconfont icon-xiangshang"></i>
              <i class="iconfont icon-xiala" style="display:none"></i>
              <!--<i class="glyphicon glyphicon-list-alt"></i> <i class="glyphicon glyphicon-chevron-down"></i> -->
            </h4>
          </div>
          <div id="collapsetwo" class="list_body panel-collapse collapse in" role="tabpanel" aria-labelledby="headingtwo">
            <div class="panel-body">
              <ul>
                ${this.data.ShowBAJK == 1 ? `<li class="list-item1"><a class="li_bajk">病案首页接口</a></li>` : ``}
                ${this.data.ISMYPA == 1 ? `<li class="list-item1"><a class="remove">移除我的病人</a></li>` : `<li class="list-item"><a class="add">加入我的病人</a></li>`}
                <li class="list-item1"><a class="ygjk">院感监控</a></li>
                <li class="list-item1"><a class="kkzl">跨科治疗</a></li>
                ${WRT_config.BrMainLeft.BRJBXX.SFZH ? `<li class="list-item1"><a class="gz360">患者360</a></li>` : ''}
                ${this.data.LCLJ == 1 ? `<li class="list-item1"><a class="lclj" style="color:red">临床路径</a></li>` :
          this.data.LCLJ == 9 ? `<li class="list-item1"><a class="lclj" style="color:red">临床路径(已退出)</a></li>` :
            this.data.LCLJ == 0 && (this.data.LCLJ != '' && this.data.LCLJ != null) ? `<li class="list-item1"><a class="lclj" style="color:red">加入临床路径</a></li>` : ""}
                <li class="list-item1"><a class="jhjqcy" style="color:red;">${WRT_config.BrMainLeft.jhcysj_signal ? `预出院(已保存)` : "预出院(未保存)"}</a></li>
                ${WRT_config.BrMainLeft.KZYDURL ? `<li class="list-item1"><a class="kzyd">开住院单</a></li>` : ''}
                ${WRT_config.BrMainLeft.SHOWRJ == 1 ? `<li class="list-item1"><a class="showrj">加入日间手术</a></li>` : ''}
                
              </ul>
            </div>
          </div>
        </div>`}
        <div class="panel panel-default">
          <div class="panel-heading box_list" role="tab" id="headingtheeh" onclick="dgchangeitem('theeh')">
            <h4 class="flex-row align-items-center justify-content-between panel-title" class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapsetheeh" aria-expanded="false" aria-controls="collapsetheeh">
              <span>
                <i class="iconfont icon-ico_hushigongzuozhan_tiwendan"></i>
                <span class="panel-title-text">体温单</span>
              </span>
              <i class="iconfont icon-xiangshang"></i>
              <i class="iconfont icon-xiala" style="display:none"></i>
            </h4>
          </div>
          <div id="collapsetheeh" class="list_body panel-collapse collapse in" role="tabpanel" aria-labelledby="headingtheeh">
            <div class="panel-body">
              <ul>
                <li class="list-item"><a data="e-brtwdlb.html" datacheck="" dataid="" dataop="">病人体温单</a></li>
                ${BRJBXX.ZKID == 78 ? `<li class="list-item"><a data="e-szfyqx.html" datacheck="" dataid="" dataop="">生长发育曲线</a></li>` : ''}
              </ul>
            </div>
          </div>
        </div>
        <div class="panel panel-default">
          <div class="panel-heading box_list" role="tab" id="headingfour" onclick="dgchangeitem('four')">
            <h4 class="flex-row align-items-center justify-content-between panel-title" class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapsefour" aria-expanded="false" aria-controls="collapsefour">
              <span>
                <i class="iconfont icon-changyong--"></i>
                <span class="panel-title-text">我的常用</span>
              </span>
              <i class="iconfont icon-xiangshang"></i>
              <i class="iconfont icon-xiala" style="display:none"></i>
              <!-- <i class="glyphicon glyphicon-chevron-down"></i> -->
            </h4>
          </div>
          <div id="collapsefour" class="list_body panel-collapse collapse in" role="tabpanel" aria-labelledby="headingfour">
            <div class="panel-body">
              <ul>
              ${_.map(this.data.WDCYCD, (item) =>
              `${JHCDSignal && (item.M_NAME == '医用食品医嘱' || item.M_NAME == '自费查询') ? "" : `<li class="list-item"><a data="${item.M_URL}" dataid="${item.M_ID}" dataop="${item.M_PARAMLIST}">${item.M_NAME}</a></li>`}`
            ).join('')}
              </ul>
            </div>
          </div>
        </div>
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
          <div class="panel panel-default">
            <div class="panel-heading box_list" role="tab" id="headingfith" onclick="dgchangeitem('fith')">
              <h4 class="flex-row align-items-center justify-content-between panel-title" class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapsefith" aria-expanded="false" aria-controls="collapsefith">
                <span>
                  <i class="iconfont icon-ico_yiyuanguanjia_yizhuchaxun"></i>
                  <span class="panel-title-text">住院医嘱</span>
                </span>
                <i class="iconfont icon-xiangshang"></i>
                <i class="iconfont icon-xiala" style="display:none"></i>
                <!-- <i class="glyphicon glyphicon-chevron-down"></i> -->
              </h4>
            </div>
            <div id="collapsefith" class="list_body panel-collapse collapse in" role="tabpanel" aria-labelledby="headingfith">
              <div class="panel-body">
                <ul>
                ${_.map(ZYYZCD, (item) =>
              // item.M_NAME == '新营养医嘱' ||
              `${JHCDSignal && (item.M_NAME == '新营养医嘱' || item.M_NAME == '自费查询' || item.M_NAME == '康复执行计划' || item.M_NAME == '外购药品历史记录') ? "" : `<li class="list-item"><a data="${item.M_URL}" dataid="${item.M_ID}" dataop="${item.M_PARAMLIST}">${item.M_NAME}</a></li>`}`
            ).join('')}
                </ul>
              </div>
            </div>
          </div>
      ${_.map(lists, (obj, index) =>
              `<div class="panel panel-default">
          <div class="panel-heading box_list default_list" role="tab" id="heading${index}" onclick="changeitem(${index})">
            <h4 class="flex-row align-items-center justify-content-between panel-title" class="collapsed" data-toggle="collapse" data-parent="#accordion" href="#collapse${index}" aria-expanded="false" aria-controls="collapse${index}">
              <span>
                <i class="iconfont ${obj.icon}"></i>
                <span class="panel-title-text">${obj.title}</span>
              </span>
              <i class="iconfont icon-xiangshang" style="display:none"></i>
              <i class="iconfont icon-xiala"></i>
              <!-- <i class="glyphicon glyphicon-chevron-down"></i> -->
            </h4>
          </div>
          <div id="collapse${index}" class="list_body panel-collapse collapse" role="tabpanel" aria-labelledby="heading${index}">
            <div class="panel-body">
              <ul class="${obj.title == '麻醉接口记录' ? "coll_list" : ""}">
              ${obj.title == '麻醉接口记录' ? obj.children : _.map(obj.children, (item) =>
                `${JHCDSignal && (item.M_NAME == '机械通气临床记录' || item.M_NAME == '新增妊娠报告卡' || item.M_NAME == '新增肿瘤报告卡' || item.M_NAME == '新增心脑报告卡' || item.M_NAME == '新增糖尿病报告卡' || item.M_NAME == '患者授权书' || item.M_NAME == '特殊检查、特殊治疗同意书' || item.M_NAME == '病情谈话记录' || item.M_NAME == '其他由医生签署的知青同意书' || item.M_NAME == '量表/评分表' || item.M_NAME == '其他记录') ? "" : `<li class="list-item"><a data="${item.M_URL}" dataid="${item.M_ID}" ${obj.title == "门住院既往病史" ? `data-mzybs=true` : ""} ${item.YLXGBGKtype ? `data-YLXGBGKtype=true` : ""} ${item.check ? `data-hyhz=true` : ""} dataop="${item.M_PARAMLIST}">${item.M_NAME}</a></li>`}`
              ).join('')}
              </ul>
            </div>
          </div>
        </div>`
            ).join('')}
    </div>
    </div>
  `
    this.$el.html(html)
    return this;
  },
  events: {
    "click .add": "add",
    "click .remove": "remove",
    "click .list-item>a ": "goURL",
    "click .ryzd": "ryzd",
    "click .hlzlz": "hlzlz",
    "click .lb_brzt": "lb_brzt",
    "click .bnzSingle": "bnzSingle",
    "click .lb_yjzx": "lb_yjzx",
    "click .yyfxsc": "yyfxsc",
    "click .lb_sfhz": "lb_sfhz",
    // "click .yybl": "yybl",
    "click .ywjjxt": "ywjjxt",
    "click .txjld": "txjld",
    "click .drgfzyc": "drgfzyc",
    "click .hy_hljb": "hy_hljb",
    "click .bqrysj": "bqrysj",
    "click .bqrysjshow": "bqrysjshow",
    "click .li_bajk": "li_bajk",
    "click .tag-icon": "tag_icon",
    "click .zydj": "zydj",
    "click .hyddy": "hyddy",
    "click .ygjk": "ygjk",
    "click .lclj": "lclj",
    "click .zgys": "zgys",
    "click .disease": "disease",
    "click .kkzl": "kkzl",
    "click .sgpg": "sgpg",
    "click .gz360": "gz360",
    "click .jhjqcy": "jhjqcy",
    "click .kzyd": "kzyd",
    "click .showrj": "showrj",
    "click .zcsqje": "zcsqje",
    "click .blsj": "blsj",
    // "click .yyzlzxd": "yyzlzxd",
  },
  // //营养诊疗执行单链接
  // yyzlzxd(){
  //   page_iframe.add("营养诊疗执行单", WRT_config.server+'/'+`yyfxsc/yyzlzxd_list.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_temp=${Math.random()}`)
  // },
  //不良事件链接
  blsj(){
    let TOKEN = sessionStorage.getItem("token")
    window.open(`http://172.16.203.224:8080/aems/wzfy/nursesso/wzfyindex.html?&pageType=eventReport&firstEventType=E02&token=${TOKEN}&patientId=${WRT_config.BrMainLeft["病案号"]}&temp=${Math.random()}`)
  },
  // 困难人群白内障
  bnzSingle() {
    let that = this
    WRT_e.ui.message({
      id: 'diffBnz',
      title: '提示',
      content: `
      <div class="form-group">
        <span style="padding-right:8px">是否为困难人群白内障：</span>
        <label class="radio-inline">
          <input name="bnz" type="radio" value="1" ${that.data.B_signal == 1 ? `checked` : ''} >是</input>
        </label>
        <label class="radio-inline">
          <input name="bnz" type="radio" value="0" ${that.data.B_signal == 0 ? `checked` : ''} >否</input>
        </label>

      </div>
      `,
      // content: '是否要将困难人群白内障显示标记改为 '+ `${that.data.B_signal==0?`<span style="color:red"> 是</span>`:that.data.B_signal==1?`<span style="color:red"> 否</span>`:''}`,
      onOk() {
        const bnzVal = $('input[name=bnz]:checked').val()
        WRT_e.api.BrMainLeft.insertbSigna({
          params: {
            al_blid: WRT_config.url["al_blid"],
            al_czzid: WRT_config.ehrSz_init.YHXX.YSYHID,
            as_value: bnzVal
          },
          success(data) {
            if (data.Code == '1') {
              WRT_config.BrMainLeft.B_signal = bnzVal
              $('#lb_knrqbnz').html(`
                ${bnzVal == 1 ? `是` : bnzVal == 0 ? `否` : ''}
                <a class="bnzSingle e-tagbtn-prime">修改</a>
              `)
              // document.getElementById('lb_brzyzt').val(bnzVal)
              // WRT_config.BrMainLeft.B_signal = 1
            }

          }

        })
      },
      onCancel() { }
    })
  },
  // //营养评估单
  // yypgd(){
  //   page_iframe.add("营养评估单", WRT_config.server+'/'+`yyfxsc/yypg_list.aspx?al_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_temp=${Math.random()}`)
  // },
  // 再次授权金额
  zcsqje() {
    // 弹出框输入手机验证码，通过后调用againSqje接口
    WRT_e.ui.model({
      id: 'editSdCode',
      title: '获取再次授权金额权限',
      content: `
      <div>
        <label>验证码: </label>
        <input 
          class="form-control" 
          autocomplete="off" 
          name="yzm"
          style="width:60%; display: initial;"  
          placeholder='请输入验证码'>
        </input>
        <button class="e_btn showSdBtn" onclick="sendCode()">发送验证码</button>
        <button disabled="disabled" class="ishowSdBtn" style="display: none;">已发送验证码</button>
        <div style="color:red;padding: 8px 53px;font-size: 15px;">请联系总值班获取绿色通道的授权短信验证码。</div>
      </div>`,
      onOk(model) {
        const yzm = model.find('input[name=yzm]').val()
        // 发送短信接口
        // 校验验证码是否正确（e_verifyOverdraftMessage）
        WRT_e.api.BrMainLeft.verifyOverdraftMessage({
          params: {
            ll_blid: WRT_config.url["al_blid"],
            ll_yzm: yzm
          },
          success(data) {
            if (data.Code == '1') {
              if (data.CodeMsg == '获取数据成功！') {
                // WRT_e.ui.hint({
                //   msg: '短信验证码验证成功!',
                //   type:'success'
                // })
                toastr.options = {
                  showDuration: "300", // 显示动作时间
                  hideDuration: "2000", // 隐藏动作时间
                };
                WRT_e.ui.hint({ msg: '再次授权金额成功!', type: 'success' })
                model.iziModal('destroy') //关闭modal
              }
              // // 再次授权金额接口（e_financeOverdraft）
              // WRT_e.api.BrMainLeft.againSqje({
              //   params: {
              //     ll_blid: WRT_config.BrMainLeft.BRJBXX.BLID,
              //   },
              //   success(data){
              //     if(data.Code==1){
              //       // 请求成功，请求成功时Result为接口返回详细信息
              //       WRT_e.ui.hint({msg:'再次授权金额成功!',type: 'success'})

              //       model.iziModal('destroy') //关闭modal
              //     }else{
              //       WRT_e.ui.hint({msg:'再次授权金额失败!',type: 'error'})
              //       $(`.showSdBtn`).css({
              //         "display":"inline"
              //       })
              //       $(`.ishowSdBtn`).css({
              //         "display":"none"
              //       })
              //     }
              //   },
              //   catch(e){
              //     WRT_e.ui.hint({ msg: e, type:'error' })
              //   }
              // })
            } else {
              if (data.CodeMsg == '未查询到发送对象号码!') {
                toastr.options = {
                  showDuration: "300", // 显示动作时间
                  hideDuration: "2000", // 隐藏动作时间
                };
                WRT_e.ui.hint({
                  msg: '未查询到发送对象号码!',
                  type: 'error'
                })
                $(`.showSdBtn`).css({
                  "display": "inline"
                })
                $(`.ishowSdBtn`).css({
                  "display": "none"
                })
              } else {
                toastr.options = {
                  showDuration: "300", // 显示动作时间
                  hideDuration: "2000", // 隐藏动作时间
                };
                WRT_e.ui.hint({
                  msg: data.CodeMsg,
                  // msg: data.CodeMsg,
                  type: 'error'
                })
              }
            }
          }
        })
      }
    })
  },
  //加入日间手术
  showrj() {
    WRT_e.api.BrMainLeft.SetRjss({
      params: {
        al_zyid: WRT_config.url["al_blid"],
        al_blid: WRT_config.url["al_blid"],
        as_bqrysj: WRT_config.BrMainLeft.BRJBXX.BQRYSJ
      },
      success(data) {
        if (data.Code == 1) {
          WRT_e.ui.hint({ msg: '加入成功', type: 'success' })
          _this.resultData()
        } else {
          WRT_e.ui.hint({ msg: data.CodeMsg, type: 'error' })
        }
      }
    })
  },
  //开住院单
  kzyd() {
    if (WRT_config.BrMainLeft.KZYDURL) {
      page_iframe.add("开住院单", WRT_config.server + '/' + WRT_config.BrMainLeft.KZYDURL)
    }
  },
  //计划近期出院（预出院）
  jhjqcy() {
    // saveJhcysj
    WRT_e.api.BrMainLeft.getJhcysj({
      params: {
        as_zyid: WRT_config.BrMainLeft.BRJBXX.ZYID
      },
      success(data) {
        var objSj = JSON.parse(data)
        if (objSj.Message == '0') {
          if (objSj.date != '0001/01/01') {
            //查询计划出院时间存在
            // let timeNow = objSj.date.replace(/(\/)/g,'-') +'T00:00'
            let timeNow = objSj.date.replace(/(\/)/g, '-')
            // <input class="jhcysj" type="datetime-local" value="${timeNow}"></input>
            WRT_e.ui.model({
              id: 'saveJhcysj',
              title: '计划出院时间',
              width: 500,
              content: `
              <dl>
                <div class="jhcysjNr">
                  <strong>计划出院时间：</strong>
                  <span>
                  <input class="jhcysj" type="date" value="${timeNow}"></input>
                  </span>
                  <span style="color:#0fad0f;padding-left: 3px;font-size: 15px;">已预出院</span>
                  <span style="color:#3d6cc8;padding-left: 3px;font-size: 15px;cursor:pointer" onclick='clearJHCY()'>清空</span>
                </div>
              </dl>
              `,
              onOk() {
                var v = $(`.jhcysj`).val()
                // .replace('T',' ');
                // var v = $(`.jhcysj`).datetimebox('getValue');
                // var dateNow = v.replace(/\-/g,'\/')
                //保存计划出院时间
                WRT_e.api.BrMainLeft.saveJhcysj({
                  params: {
                    as_zyid: WRT_config.BrMainLeft.BRJBXX.ZYID,
                    as_sj: v
                  },
                  success(data) {
                    var saveData = JSON.parse(data)
                    if (saveData.Message == '0') {
                      WRT_e.ui.hint({ msg: '计划出院安排成功', type: 'success' })
                      $('#saveJhcysj').iziModal("destroy")
                    } else {
                      WRT_e.ui.hint({ msg: '计划出院安排失败', type: 'error' })
                      $('#saveJhcysj').iziModal("destroy")
                    }
                  },
                  error: function (err) {
                    WRT_e.ui.hint({ msg: err, type: 'error' })
                  }
                })
              }
            })
          } else {
            //查询计划出院时间不存在（出现输入时间弹窗强制调用保存接口成功后进行下一步）
            // 获取第二天时间
            let dayTwo = formatDate(1)
            WRT_e.ui.model({
              id: 'saveJhcysj',
              title: '保存计划出院时间',
              width: 500,
              content: `
              <dl>
                <div class="jhcysjNr">
                  <strong>计划出院时间：</strong>
                  <span>
                    <input class="jhcysj" type="date" value="${dayTwo}"></input>
                  </span>
                  <span style="color:red;padding-left: 3px;font-size: 15px;">未保存</span>
                </div>
              </dl>
              `,
              onOk() {
                var v = $(`.jhcysj`).val()
                // var v = $(`.jhcysj`).datetimebox('getValue');
                //保存计划出院时间
                WRT_e.api.BrMainLeft.saveJhcysj({
                  params: {
                    as_zyid: WRT_config.BrMainLeft.BRJBXX.ZYID,
                    as_sj: v
                  },
                  success(data) {
                    var saveData = JSON.parse(data)
                    if (saveData.Message == '0') {
                      WRT_e.ui.hint({ msg: '计划出院安排成功', type: 'success' })
                      $('#saveJhcysj').iziModal("destroy")
                    } else {
                      WRT_e.ui.hint({ msg: '计划出院安排失败', type: 'error' })
                      $('#saveJhcysj').iziModal("destroy")
                    }
                  },
                  error: function (err) {
                    WRT_e.ui.hint({ msg: err, type: 'error' })
                  }
                })
              }
            })
          }
        }
      }
    })
    // WRT_e.ui.message({
    //   title: '信息窗口',
    //   content: "是否计划24小时内给该病人办理出院手续？",
    //   onOk() {
    //     WRT_e.api.BrMainLeft.setJhcy({
    //       params:{
    //         al_zyid:WRT_config.BrMainLeft.BRJBXX.ZYID
    //       },
    //       success(data){
    //         if(data==1){
    //           WRT_e.ui.hint({msg:'计划出院安排成功',type:'success'})
    //         }else{
    //           WRT_e.ui.hint({msg:'计划出院安排失败',type:'error'})
    //         }
    //       }
    //     })
    //   },
    //   onCancel() { }
    // })
  },
  //患者360
  gz360() {
    let url = `http://172.16.203.141:8080/winsso/c/${WRT_config.BrMainLeft.YHXX.YSYHID}/${WRT_config.BrMainLeft.BRJBXX.SFZH || ''}/2/${WRT_config.BrMainLeft.BRJBXX.BRBH}/${WRT_config.BrMainLeft.BRJBXX.BLID}/0/0/${WRT_config.BrMainLeft.BRJBXX.BRBH}/-1/47000592-2/0/63/zyysclient`
    window.open(url)
  },
  //新冠评估/转科通知
  sgpg() {

    if (WRT_config.BrMainLeft.right_top_btn_text == '新冠评估') {
      WRT_e.ui.model({
        id: "tag_sgpg",
        title: "新冠评估",
        iframeHeight: "520px",
        width: '800px',
        iframe: true,
        iframeURL: `${WRT_config.server}/${WRT_config.BrMainLeft.right_top_btn_url}&as_openmode=ehr3`
      })
    } else if (WRT_config.BrMainLeft.right_top_btn_text == '转科通知') {
      // blcx/icuzktz.aspx?as_blid=病历ID&temid=时间戳,窗口title是科主任转科通知
      WRT_e.ui.model({
        id: "tag_sgpg",
        title: "科主任转科通知",
        iframeHeight: "320px",
        width: '800px',
        iframe: true,
        iframeURL: `${WRT_config.server}/${WRT_config.BrMainLeft.right_top_btn_url}&temid=${Math.random()}&as_openmode=ehr3`
      })
    }
  },
  //跨科治疗
  kkzl() {
    page_iframe.add("跨科治疗", WRT_config.server + `/kkzl/kkzl.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_zkdm=${WRT_config.BrMainLeft.BRJBXX.ZKDM}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}`);
  },
  //指南速查
  disease() {
    if (WRT_config.DiseaseGuide) {
      page_iframe.add("指南速查", WRT_config.DiseaseGuide)
    }
  },
  //院感监控0
  ygjk() {
    window.open(`http://172.16.200.88/nis/cdc?userid=${WRT_config.BrMainLeft.YHXX.YSYHID}&patientid=${WRT_config.BrMainLeft.BRJBXX.ZYH}`);
  },
  //临床路径
  lclj() {
    let url = ""
    if (WRT_config.BrMainLeft.LCLJ == 0) {
      url = WRT_config.server + `/lclj/lclj_qzrj.aspx?as_new=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_zddm=0&tmpid=${Math.random()}`
    } else if (WRT_config.BrMainLeft.LCLJ != 0) {
      let fd = WRT_config.BrMainLeft.dept_attribute.filter(item => item.SXMC.indexOf("是否使用新版临床路径") >= 0)
      if (fd && fd.length > 0) {
        url = `e-lclj.html?al_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&al_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&al_zlzid=${WRT_config.BrMainLeft.BRJBXX.ZLZID}&as_openmode=ehr3&tmpid=${Math.random()}`
      } else {
        url = WRT_config.server + `/lclj/lclj.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zlzid=${WRT_config.BrMainLeft.BRJBXX.ZLZID}&tmpid=${Math.random()}`
      }
    }
    page_iframe.add("临床路径", url)
  },
  //主管医生
  zgys() {
    // WRT_config.BrMainLeft.XGZGYS_Switch = 1
    if (WRT_config.BrMainLeft.XGZGYS_Switch == 1) {
      let target = sessionStorage.getItem("docdata") || null
      if (!target) {
        WRT_e.api.BrMainLeft.GetDocData({
          params: {
            as_bz: "",
            as_bqid: ""
          },
          success(data) {
            if (data && data.Code == 1) {
              WRT_config.docdata = JSON.parse(data.Result)
              sessionStorage.setItem("docdata", data.Result)
              docdata = WRT_config.docdata || []
              let temp = `<div>
              请输入人员终身码或拼音/五笔码/中文:<input id="doc_mc" autocomplete="off" onkeydown="calAge(event)" />
                <button class="e_btn" onclick="onSearch()">查询</button>
                <input id="cb_zk" type="checkbox" name="cb_zk" checked="checked" onclick="onSearch()">
                <label for="cb_zk">本专科</label>
              </div>
              <div id="doc_lists">`
              let arr = []
              WRT_config.docdata.map(function (item, index) {
                if (index < page * 20 && index >= (page - 1) * 20) {
                  arr.push(item)
                }
              })
              temp += docDataHtml(arr)
              temp += "</div>"
              WRT_e.ui.model({
                id: "if_zgys",
                title: "修改主管医生",
                width: "570px",
                iframe: false,
                content: temp
                // iframeURL: `${WRT_config.server}/${WRT_config.BrMainLeft.XGZGYS_Url}&as_openmode=ehr3`
              })

              // 可能修改逻辑
              onSearch()
            }
          }
        })
      } else {
        WRT_config.docdata = JSON.parse(target)
        docdata = JSON.parse(target)
        let temp = `<div>
          请输入人员终身码或拼音/五笔码/中文:<input id="doc_mc" autocomplete="off" onkeydown="calAge(event)" />
            <button class="e_btn" onclick="onSearch()">查询</button>
            <input id="cb_zk" type="checkbox" name="cb_zk" checked="checked" onclick="onSearch()">
            <label for="cb_zk">本专科</label>
          </div>
          <div id="doc_lists">`
        let arr = []
        WRT_config.docdata.map(function (item, index) {
          if (index < page * 20 && index >= (page - 1) * 20) {
            arr.push(item)
          }
        })
        temp += docDataHtml(arr)
        temp += "</div>"
        WRT_e.ui.model({
          id: "if_zgys",
          title: "修改主管医生",
          width: "570px",
          iframe: false,
          content: temp
          // iframeURL: `${WRT_config.server}/${WRT_config.BrMainLeft.XGZGYS_Url}&as_openmode=ehr3`
        })
        // 可能修改逻辑
        onSearch()
      }
    } else {
      WRT_e.ui.message({
        title: '信息窗口',
        content: "只有病人所属治疗组组长有权限修改主管医生",
        onOk() { },
      })
    }
  },
  //开住院单医生
  zydj() {
    let url = `${WRT_config.server}/zydj/zyd.aspx?idb=${this.data.BRJBXX.ZYID}&as_openmode=ehr3`
    page_iframe.add("病人住院单", url)
  },
  //日间手术
  tag_icon() {
    // <tr>
    //   <td>
    //     普通转日间：<input type="checkbox" id="box_tag" onclick="checkRjss(this)">
    //   </td>
    // </tr>
    let temp = `
    <table>
      <tbody>
      ${WRT_config.BrMainLeft.BRJBXX.RJSS == '1' ? `
        <tr>
          <td>
            取消日间手术：<input type="checkbox" id="box_tag" onclick="checkRjss(this)">
          </td>
        </tr>`: ``
      }
      ${WRT_config.BrMainLeft.BRJBXX.RJSS == '2' ? `
        <tr>
          <td>
            普通转日间：<input type="checkbox" id="box_tag" onclick="checkRjss(this)">
          </td>
        </tr>`: ``
      }
      <tr>
        <td>
          <span id="lbl_tcly" style="display:inline-block;height:16px;">理由：</span>
        </td>
      </tr>
      <tr>
        <td>
          <textarea name="tb_tcly" rows="2" cols="20" id="tb_tcly" style="background-color:SkyBlue;font-family:宋体;font-size:13px;height:40px;width:300px;"></textarea>                                   
        </td>
      </tr>
      <tr>
        <td align="right">
          ${WRT_config.BrMainLeft.BRJBXX.RJSS == '1' ? `
            <div style="display: flex;justify-content: space-around;align-items: center;">
              <span style="color:red;">
                普通转日间需医务处审批<br/>
                日间转普通需科主任审批
              </span>
              <input id="btn_save_none" type="button" value="保存" style="width:90px;height: 32px;">
              <button class="e_btn none" id="btn_save" type="button" style="width:90px;" onclick="saveRjss();">保存</button>
            </div>
          `: ``}
          ${WRT_config.BrMainLeft.BRJBXX.RJSS == '2' ? `
            <div style="display: flex;justify-content: space-around;align-items: center;">
              <span style="color:red;">
                普通转日间需医务处审批<br/>
                日间转普通需科主任审批
              </span>
              <input id="btn_save_none" type="button" value="发起申请" style="width:90px;height: 32px;color:red;">
              <button class="e_btn none" id="btn_save" type="button" style="width:90px;" onclick="saveRjss();">发起申请</button>
            </div>
          `: ``}
        </td>
      </tr>
    </tbody></table>`
    WRT_e.ui.model({
      id: "if_tag_icon",
      title: WRT_config.BrMainLeft.BRJBXX.RJSS == '1' ? `取消日间手术` : `普通转日间`,
      // title: WRT_config.BrMainLeft.BRJBXX.RJSS == '1' ? `取消日间手术` : WRT_config.BrMainLeft.BRJBXX.RJSS == '2' ? `普通转日间`:``,
      width: '430px',
      iframe: false,
      content: temp
    })
    $("#box_tag").focus()
  },
  //加入我的病人
  add() {
    WRT_e.api.BrMainLeft.AddMyPatient({
      params: {
        al_blid: this.data.BRJBXX.BLID,
        al_yhid: this.data.YHXX.YSYHID,
        as_lb: 1
      },
      success(data) {
        if (data.Code == 1) {
          resultData()
          WRT_e.ui.hint({
            type: 'success',
            msg: "加入成功"
          })
        }
      }
    })
  },
  //移除我的病人
  remove() {
    WRT_e.api.BrMainLeft.AddMyPatient({
      params: {
        al_blid: this.data.BRJBXX.BLID,
        al_yhid: this.data.YHXX.YSYHID,
        as_lb: 0
      },
      success(data) {
        if (data.Code == 1) {
          resultData()
          WRT_e.ui.hint({
            type: 'success',
            msg: "移除成功"
          })
        }
      }
    })
  },
  //病案首页接口
  li_bajk() {
    if (WRT_config.BrMainLeft.BRLYZT == '在院') {
      WRT_e.ui.message({
        title: '信息窗口',
        content: "病人未出院",
        onOk() {
        },
      })
    } else {
      let url = `${WRT_config.server}/newbasy/newbajk/basyjk.aspx?av_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&av_blid=${WRT_config.url["al_blid"]}`
      page_iframe.add("病案接口", url)
    }

  },
  //菜单栏URL
  goURL(ev) {
    let lists = {
      title: ev.target.innerText,
      url: ev.target.attributes["data"].value,
      paramList: ev.target.attributes["dataop"].value,
      m_id: ev.target.attributes["dataid"].value,
    }
    if (ev.target.attributes["datacheck"]) {
      lists.check = ev.target.attributes["datacheck"].value
    }
    if (ev.target.dataset && ev.target.dataset.hyhz) {
      lists.hyhz = true
    }
    if (ev.target.dataset && ev.target.dataset.zkblsq) {
      lists.zkblsq = true
    }
    if (ev.target.dataset && ev.target.dataset.ylxgbgktype) {
      lists.YLXGBGKtype = true
    }
    if (ev.target.dataset && ev.target.dataset.mzybs) {
      lists.mzybs = true
    }
    if (ev.target.dataset && ev.target.dataset.mzybsLs) {
      lists.mzybsLs = true
    }
    e_GetJkurl(lists) // 打开新标签
    // let terget = WRT_config.server + ev.target.attributes["data"].value+`?as_blid=${this.data.BRJBXX.BLID}`
    // $('#page>.nav').append(`<li><a href="#${ev.target.innerText}" data-toggle="tab">${ev.target.innerText}</a><i class="iconfont icon-cuo close" data="${ev.target.innerText}"></i></li>`)
    // $('#page>.tab-content').append(`<div class="tab-pane fade" id="${ev.target.innerText}" style="height: 100%;"><iframe src="${terget}" frameborder="0" scrolling="auto" style="width:100%;height:100%"></iframe></div>`)
    // //激活当前选项卡
    // $(`#page a[href="#${ev.target.innerText}"]`).tab('show')
  },
  //修改入院单诊断
  ryzd(ev) {
    let name = ev.target.innerText
    WRT_e.ui.model({
      id: "ryzd",
      title: "修改入院诊断",
      iframeHeight: "200px",
      iframe: true,
      iframeURL: `${WRT_config.server}/blcx/ryzd.aspx?as_new=1&ai_idb=${this.data.BRJBXX.ZYID}&as_ryzd=${name}&as_openmode=ehr3`
    })
  },
  //修改病区入院
  bqrysj() {
    $("#bqrysj1").css("display", "inline")
    $("#bqrysj2").css("display", "none")
    let time = $(".bqrysj")[0].innerHTML
    $("#dt").datetimebox({
      value: time,
      required: true,
      showSeconds: true
    });
  },
  //修改病区入院时间事件
  bqrysjshow() {
    let dt = $("#dt").datetimebox('getValue')
    if (dt) {
      WRT_e.api.BrMainLeft.UpdateBqrysj({
        params: {
          al_blid: this.data.BRJBXX.BLID,
          as_bqrysj: dt
        },
        success(data) {
          if (data.Code == 1) {
            $("#bqrysj1").css("display", "none")
            $(".bqrysj").text(gettime(dt));
            $("#bqrysj2").css("display", "inline")
          }
        }
      })
    }
  },
  //修改护理级别
  hy_hljb(ev) {
    WRT_e.api.BrMainLeft.checkHL_Mes({
      params: {
        ll_blid: WRT_config.url["al_blid"]
      },
      success(data) {
        if (data.Code == 1) {
          $(".box_foot").removeClass('none')
          // let aa=$("#hy_hljb")[0].getBoundingClientRect().right
          // $(".box_foot").css("right",Math.round((aa-520)/2))
          $("#box_lists").html(data.Result)
        }
      }
    })
    let name = setHljb(ev.target.innerText)
    WRT_e.ui.model({
      id: "hy_hljb",
      title: "修改护理级别",
      iframeHeight: "400px",
      iframe: true,
      iframeURL: `${WRT_config.server}/szhljbSZ.aspx?av_zyid=${this.data.BRJBXX.ZYID}&av_hljb=${name}&as_openmode=ehr3`
    })
  },
  //修改治疗组
  hlzlz(ev) {
    if (WRT_config.BrMainLeft.BRLYZT == '') {

    }
    WRT_e.ui.model({
      id: "hlzlz",
      title: "修改治疗组",
      // iframeHeight: "200px",
      iframe: true,
      iframeURL: `${WRT_config.server}/xgbrzlzSZ.aspx?as_zlzid=${this.data.BRJBXX.ZLZID}&as_zyid=${this.data.BRJBXX.ZYID}&as_zkid=${this.data.BRJBXX.ZKID}&as_openmode=ehr3`
    })
  },
  //修改住院病人状态
  lb_brzt() {
    if (WRT_config.BrMainLeft.BRJBXX.BQRYSJ) {
      WRT_e.api.BrMainLeft.getBrzt({
        params: {
          al_zyid: WRT_config.BrMainLeft.BRJBXX.ZYID
        },
        success(data) {
          let j_brzt = data
          if (j_brzt > 1) {  //已护士站出院或财务出院
            var j_tsxx;
            if (j_brzt == "2")
              j_tsxx = "已护士病区出院";
            else
              j_tsxx = "已财务出院";
            WRT_e.ui.message({
              title: '提示窗口',
              content: `病人已【${j_tsxx},不可修改病人的住院状态!`,
              onOk() { }
            })
          } else {
            if (j_brzt == -1) {  //需要填写 临床总体印象量表
              WRT_e.ui.message({
                title: "提示窗口",
                content: '精神科患者需要在出院当天完成“临床总体印象量表CGI,请及时填写保存!',
                onOk() {
                  page_iframe.add("量表评分表", `${WRT_config.server}/pfbgl/pfblist.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}`)
                }
              })
            } else {
              let url;
              if (WRT_config.BrMainLeft.xxsc == "1") {
                url = `${WRT_config.server}/xsesc.aspx?as_yqdm=${WRT_config.BrMainEinit.yqdm}`;
                WRT_e.ui.model({
                  id: "lb_brzt",
                  title: "修改病人住院状态",
                  iframeHeight: "600px",
                  iframe: true,
                  iframeURL: url
                })
              } else {
                url = `${WRT_config.server}/brcyczSZ.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_lclj=${WRT_config.BrMainLeft.LCLJ}&as_openmode=ehr3`
                WRT_e.ui.model({
                  id: "lb_brzt",
                  title: "修改病人住院状态",
                  iframeHeight: "550px",
                  iframe: true,
                  iframeURL: url
                })
              }

            }
          }
        }

      })
    } else {
      WRT_e.ui.message({
        title: '信息窗口',
        content: "病区未入院，无需操作医嘱出院"
      })
    }
  },
  //修改一键下转
  lb_yjzx() {
    let url = `${WRT_config.server}/brcyczSz.aspx?as_yjxz=1&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&as_lclj=${WRT_config.BrMainLeft.LCLJ}&as_openmode=ehr3`
    WRT_e.ui.model({
      id: "lb_yjzx",
      title: "一键下转",
      iframeHeight: "600px",
      width: "1000px",
      iframe: true,
      iframeURL: url
    })
  },
  //化验单打印
  hyddy() {
    let url = WRT_config.server + `/hyyz/testresult/zkhyddy.aspx?blid=${this.data.BRJBXX.BLID}`
    page_iframe.add("化验单打印", url)
  },
  //修改营养风险筛查
  yyfxsc() {
    let times = new Date().getTime();
    // let url = WRT_config.server + `/yyfxsc/yyfxsc.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_temp=${times}&as_openmode=ehr3`
    // page_iframe.add("营养风险筛查", url)
    let url = `e-yygl.html?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&isnew=1&isHis=1&patientID=${this.data.BRJBXX.BRBH}&visitID=${this.data.BRJBXX.ZYID}&inpNO=${this.data.BRJBXX.ZYH}&userNO=${this.data.YHXX.YSYHID}&userName=${this.data.BRJBXX.BRXM}&unitCode=${this.data.BRJBXX.ZKID}&unitName=${this.data.BRJBXX.ZKMC}&as_temp=${times}&as_openmode=ehr3`
    page_iframe.add("营养管理", url)
  },
  //修改病人有营养风险
  lb_sfhz() {
    WRT_e.ui.model({
      id: "lb_sfhz",
      title: "病人有营养风险",
      iframeHeight: "200px",
      iframe: true,
      iframeURL: `${WRT_config.server}/yyfxsc/yyfxsfhz.aspx?as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&isnew=1&as_tmpid=0.3480926212701332&as_openmode=ehr3`
    })
  },
  //营养食谱
  // yybl() {
  //   window.open(`http://172.16.203.20:8091/ScreenUI.aspx?isHis=1&patientID=${this.data.BRJBXX.BRBH}&visitID=${this.data.BRJBXX.ZYID}&inpNO=${this.data.BRJBXX.ZYH}&userNO=${this.data.YHXX.YSYHID}&userName=${this.data.BRJBXX.BRXM}&unitCode=${this.data.BRJBXX.ZKID}&unitName=${this.data.BRJBXX.ZKMC}&as_openmode=ehr3`)
  // },
  //药物警戒系统
  ywjjxt() {
    window.open(`http://172.16.200.232:8989/chps/jsp/albyuidandpno_yhzh.jsp?yhzh=${this.data.YHXX.YHZH}`)
  },
  //透析记录单
  txjld() {
    window.open(`http://172.16.201.160:8080/hemodialysis/xt/dialyseRecord?bah=${this.data["病案号"] || ''}&timeStr=`)
  },
  //DRG分组预测
  drgfzyc() {
    window.open(`http://172.16.203.247/drg_thd_detail/index?zyh=${this.data.BRJBXX.BLID}&org=47000592-233030411A1001`)
  }
})
//右侧标签页
var right_View = WRT_e.view.extend({
  render: function () {
    let cybz = WRT_config.BrMainEinit.cybz || ""
    // 过去一天摄入热量 ${WRT_config.BrMainLeft.rlDbz_rl} kcal，蛋白质 ${WRT_config.BrMainLeft.rlDbz_dbz} g 
    // WRT_config.BrMainLeft.rlDbz_signal=true
    var html = `
      <section class="inner-tab">
        <!-- 选项卡 -->
        <div class="page bqypage" id="page">
          <!-- Nav tabs -->
          <ul class="nav nav_list_header">
            <li class="active"><a href="#住院医嘱" data-toggle="tab">${JHCDSignal ? `留观医嘱` : `住院医嘱`}</a></li>
          </ul>
          <!-- Tab panes -->
          <div class="tab-content">
            <div class="tab-pane active in fade" id="住院医嘱">
              <ul id="myTabs" class="nav nav-tabs" role="tablist">
                ${cybz == '' || cybz == 3 ? `<li role="presentation" ${(WRT_config.url["as_type"] && WRT_config.url["as_type"] == 'ryzbzx') ? '' : `class="active"`}>
                  <a href="#长期医嘱" aria-controls="长期医嘱" role="tab" data-toggle="tab">长期医嘱</a>
                </li>`: ''}
                ${cybz == '' || cybz == 2 || cybz == 3 ? `<li role="presentation" ${cybz == 2 ? `class="active"` : ""}>
                  <a href="#临时医嘱" aria-controls="临时医嘱" role="tab" data-toggle="tab">临时医嘱</a>
                </li>`: ''}
                ${cybz == '' ? `<li role="presentation">
                  <a href="#化验医嘱" aria-controls="化验医嘱" role="tab" data-toggle="tab">化验医嘱</a>
                </li>`: ''}
                ${cybz == '' ? `<li role="presentation">
                  <a href="#医技检查" aria-controls="医技检查" role="tab" data-toggle="tab">医技检查</a>
                </li>`: ''}
                ${cybz == '' || cybz == 1 ? `<li role="presentation" ${cybz == 1 ? `class="active"` : ""}>
                  <a href="#医嘱单" aria-controls="医嘱单" role="tab" data-toggle="tab">医嘱单</a>
                </li>`: ''}
                ${WRT_config.url["as_type"] && WRT_config.url["as_type"] == 'ryzbzx' ? `<li role="presentation" class="active">
                  <a href="#一键开单" aria-controls="一键开单" role="tab" data-toggle="tab">一键开单</a>
                </li>`: ''}
              </ul>
              <div class="tab-content advice">
                ${WRT_config.BrMainLeft.rlDbz_signal == true && (WRT_config.BrMainLeft.rlDbz_rl != '0' && WRT_config.BrMainLeft.rlDbz_dbz != '0') ? `
                <div class="rldbzBtn" style="position:absolute; top:5px;right:320px;cursor: pointer;">
                  <button class="e_btn">摄入热量和蛋白质</button>
                </div>
              `: ``}
                ${cybz == '' || cybz == 1 || cybz == 2 ? `<div style="position:absolute; top:10px;right: 190px;">
                  长药输液量：<span id="lb_brsyl" title="本数据根据长期药品医嘱计算,仅供参考,不做医疗依据" style="color:Red;font-weight:bold;">${WRT_config.brmrjmsyl}</span>
                </div>`: ''}
                <div style="position:absolute; top:10px;right: 10px;">
                  <a id="qc_message" style="color:Red;font-weight:bold;">您有${WRT_config.BrMainEinit.qccnt}条质控信息需要处理</a>
                </div>
                ${cybz == '' || cybz == 3 ? `<div role="tabpanel" class="tab-pane ${(WRT_config.url["as_type"] && WRT_config.url["as_type"] == 'ryzbzx') ? '' : `active`}" id="长期医嘱">
                  <iframe id="yz_sz_cq" src="e-yz_sz.html?as_yzlb=cq&as_blid=${this.data.BRJBXX.BLID}&as_tmpid=${Math.random()}&as_zlzid=${this.data.BRJBXX.ZLZID}&as_lclj=${WRT_config.BrMainLeft.LCLJ}" frameborder="0" scrolling="auto" style="width:100%;height:99%"></iframe>
                </div>`: ''}
                ${cybz == '' || cybz == 2 || cybz == 3 ? `<div role="tabpanel" class="tab-pane ${cybz == 2 ? `active` : ""}" id="临时医嘱">
                  <iframe id="yz_sz_ls" src="${`e-yz_sz.html?as_yzlb=ls&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_tmpid=${Math.random()}&as_zlzid=${WRT_config.BrMainLeft.BRJBXX.ZLZID}&YHZH=${WRT_config.BrMainLeft.YHXX.YHZH}&as_lclj=${WRT_config.BrMainLeft.LCLJ}`}" frameborder="0" scrolling="auto" style="width:100%;height:99%"></iframe>
                </div>`: ''}
                ${cybz == '' ? `<div role="tabpanel" class="tab-pane" id="化验医嘱">
                  <iframe id="bqxz" src="${`e-bqxz.html?empi=${WRT_config.BrMainLeft["病案号"]}&ztbz=nhyd&as_brzt=${WRT_config.BrMainLeft.BRLYZT && WRT_config.BrMainLeft.BRLYZT.indexOf('护士病区出院') >= 0?1:0}&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_brbqdm=${WRT_config.BrMainLeft.BRJBXX.BQDM}&as_zlzid=${WRT_config.BrMainLeft.BRJBXX.ZLZID}&as_yszkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&al_yhid=${WRT_config.BrMainLeft.YHXX.YSYHID}&ys_bmid=${WRT_config.BrMainEinit.ys_bmid}&as_bqid=${WRT_config.BrMainLeft.BRJBXX.BQID}&as_switch=${WRT_config.BrMainLeft.HMZK}&as_bah=${WRT_config.BrMainLeft["病案号"]}&as_tmpid=${Math.random()}`}" frameborder="0" scrolling="auto" style="width:100%;height:99%"></iframe>
                </div>`: ''}
                ${cybz == '' ? `<div role="tabpanel" class="tab-pane" id="医技检查">
                  <iframe id="zytjsqdyzmain" src="${WRT_config.trialuser.find(e => e == WRT_config.BrMainLeft.YHXX.YHZH)?`e-zytjsqdyzmain.html?av_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&av_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&av_zllx=11&av_bqid=${WRT_config.BrMainLeft.BRJBXX.BQID}&av_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&tmpid=${Math.random()}`:WRT_config.server + `/bqxz.aspx?ztbz=nsqd&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_brbqdm=${WRT_config.BrMainLeft.BRJBXX.BQDM}&as_yszkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&tmpid=${Math.random()}`}" frameborder="0" scrolling="auto" style="width:100%;height:99%"></iframe>
                </div>`: ''}
                ${cybz == '' || cybz == 1 ? `<div role="tabpanel" class="tab-pane ${cybz == 1 ? `active` : ""}" id="医嘱单">
                <iframe id="zyyzcx" src="${`${WRT_config.server}/zyyzcx.aspx?as_idb=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&tmpid=${Math.random()}`}" frameborder="0" scrolling="auto" style="width:100%;height:99%"></iframe>
                </div>`: ''}
                ${WRT_config.url["as_type"] && WRT_config.url["as_type"] == 'ryzbzx' ? `<div role="tabpanel" class="tab-pane active" id="一键开单">
                  <iframe id="yzyyjkd" src="${WRT_config.server}/zyyz/yzyyjkd.aspx?as_idb=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&tmpid=${Math.random()}" frameborder="0" scrolling="auto" style="width:100%;height:99%"></iframe>
                </div>`: ''}
              </div>
            </div>
          </div>
        </div>
      </section>
      `
    this.$el.html(html)

    return this;
  },
  events: {
    "click .close": "close",
    "click .nav_li>a": "select",
    // "click #myTabs a": "ontab",
    "click #qc_message": "qc_message",
    "click .rldbzBtn": "onCharts"
  },
  // 柱状图(暂时未假数据等待接口数据)
  onCharts() {
    // let url = WRT_config.server+'/'+`e-nldbzCharts.html?al_blid=${WRT_config.url.al_blid}&ls_idb=${WRT_config.url.ls_idb}&ZKID=${WRT_config.ehrSz_init.YHXX.ZKID}&YSYHID=${WRT_config.ehrSz_init.YHXX.YSYHID}&as_temp=${Math.random()}`
    let url = `http://localhost:8081/e-nldbzCharts.html?al_blid=${WRT_config.url.al_blid}&ls_idb=${WRT_config.url.ls_idb}&ZKID=${WRT_config.ehrSz_init.YHXX.ZKID}&YSYHID=${WRT_config.ehrSz_init.YHXX.YSYHID}&as_temp=${Math.random()}`
    WRT_e.ui.model({
      id: 'rldbzCharts',
      title: '摄入热量和蛋白质柱状图',
      width: "1000px",
      iframe: true,
      iframeHeight: '600px',
      iframeURL: url
    })

  },
  //质控
  qc_message() {
    // parent.qc_message(WRT_config.BrMainEinit)
    if (WRT_config.BrMainEinit.QC_URL) {
      page_iframe.add("质控信息", WRT_config.server + '/' + WRT_config.BrMainEinit.QC_URL)
    }
  },
  //切换标签
  ontab(e) {
    // e.preventDefault()
    let src = "", id = ""
    let name = e.target.innerHTML
    switch (name) {
      case '长期医嘱':
        id = "yz_sz_cq"
        src = `e-yz_sz.html?as_yzlb=cq&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_tmpid=${Math.random()}&as_zlzid=${WRT_config.BrMainLeft.BRJBXX.ZLZID}&as_lclj=${WRT_config.BrMainLeft.LCLJ}`
        break;
      case '临时医嘱':
        id = "yz_sz_ls"
        src = `e-yz_sz.html?as_yzlb=ls&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&YHZH=${WRT_config.BrMainLeft.YHXX.YHZH}&as_tmpid=${Math.random()}&as_zlzid=${WRT_config.BrMainLeft.BRJBXX.ZLZID}&as_lclj=${WRT_config.BrMainLeft.LCLJ}`
        break;
      case '化验医嘱':
        let as_brzt = 0
        if (WRT_config.BrMainLeft.BRLYZT && WRT_config.BrMainLeft.BRLYZT.indexOf('护士病区出院') >= 0) {
          as_brzt = 1
        }
        id = "bqxz"
        src = `e-bqxz.html?empi=${WRT_config.BrMainLeft["病案号"]}&ztbz=nhyd&as_brzt=${as_brzt}&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_brbqdm=${WRT_config.BrMainLeft.BRJBXX.BQDM}&as_zlzid=${WRT_config.BrMainLeft.BRJBXX.ZLZID}&as_yszkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&al_yhid=${WRT_config.BrMainLeft.YHXX.YSYHID}&ys_bmid=${WRT_config.BrMainEinit.ys_bmid}&as_bqid=${WRT_config.BrMainLeft.BRJBXX.BQID}&as_switch=${WRT_config.BrMainLeft.HMZK}&as_bah=${WRT_config.BrMainLeft["病案号"]}&as_tmpid=${Math.random()}`
        break;
      case '医技检查':
        id = "zytjsqdyzmain"
        let fd = WRT_config.trialuser.find(e => e == WRT_config.BrMainLeft.YHXX.YHZH)
        if (fd) {
          src = `e-zytjsqdyzmain.html?av_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&av_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&av_zllx=11&av_bqid=${WRT_config.BrMainLeft.BRJBXX.BQID}&av_zkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&tmpid=${Math.random()}`
        } else {
          src = WRT_config.server + `/bqxz.aspx?ztbz=nsqd&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&as_zyid=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_brbqdm=${WRT_config.BrMainLeft.BRJBXX.BQDM}&as_yszkid=${WRT_config.BrMainLeft.BRJBXX.ZKID}&tmpid=${Math.random()}`
        }
        break;
      case '医嘱单':
        id = "zyyzcx"
        src = `${WRT_config.server}/zyyzcx.aspx?as_idb=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&tmpid=${Math.random()}`
        break;
      case '一键开单':
        id = "yzyyjkd"
        // src = `${WRT_config.server}/zyyz/yzyyjkd.aspx?as_idb=:zyid&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&tmpid=${Math.random()}`
        src = `${WRT_config.server}/zyyz/yzyyjkd.aspx?as_idb=${WRT_config.BrMainLeft.BRJBXX.ZYID}&as_blid=${WRT_config.BrMainLeft.BRJBXX.BLID}&tmpid=${Math.random()}`
        break;
    }
    if (id=="zytjsqdyzmain") {
      $(`#${id}`).attr('src', src)
    }
  },
  close(ev) {
    //激活下一个选项卡
    let e = ev.target.parentElement.innerText
    let el = $(`#page a[href="#${e}"]`).parent()
    let div = null
    if (el[0] == undefined) {
      let a = ev.target.parentElement.firstElementChild.dataset["id"]
      div = a
      el = $(`#page a[href="#${a}"]`).parent()
    }
    page_iframe.del(div || e, e)
  },
  select(ev) {
    if ($(".nav_li").hasClass("active")) {
      $(".nav_li").removeClass("active")
      $(ev.target).parent().tab("show")
    }
  }
})