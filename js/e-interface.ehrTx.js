WRT_e.api = WRT_e.api || {}

WRT_e.api.ehrTx = {
  //获取专业日志
  getPortal: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":portal
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/ehrTX.aspx/e_GetPortal',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //获取病人检查提醒的数量
  getBrcyblbg: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "__type": "BaseModel",
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":8
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/ehrTX.aspx/e_GetBrcyblbg',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //获取超2天未归档的病人（原来为上缴病人）
  getUndocumentedPatInfo: function (o = {}) { 
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if(o.success) o.success({
        "Code": "1",
        "CodeMsg": "获取数据成功！",
        "Result":PatInfo
      })
    }else{
      $.ajax({
        url: WRT_config.server+'/ehrTX.aspx/e_GetUndocumentedPatInfo',
        type: 'post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //登出操作
  getRelogin: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_relogin_Click',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //专科日志
  GetDataSimple: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/ehrTX.aspx/e_GetDataSimple',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //专科日志
  GetYjblFromLtzk: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/EhrSz.aspx/e_GetYjblFromLtzk',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
  //护理记录
  hljlmes: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {

    } else {
      $.ajax({
        url: WRT_config.server + '/ehrTX.aspx/e_hljlmes',
        type: 'post',
        dataType: "json",
        data: JSON.stringify(o.params),
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error:function(error){
          WRT_e.ui.hint({
            type: 'error',
            msg: "服务器连接失败"
          })
        }
      })
    }
  },
}