WRT_e.api = WRT_e.api || {}

WRT_e.api.bqxz = {
  //初始化
  getInit: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: bqxz
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_Init',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (o.error) o.error(error.responseText)
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取执行病区
  getZxbq: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: bqxz_bq
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetZxbq',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取院区代码
  getYqdm: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: "02"
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetYqdm',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取个人模板及专科模板
  getYl_hymbs: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: JSON.stringify(bqxz_zkgr)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetYl_hymbs',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取其他医嘱目录
  getYl_HYyzmls: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: JSON.stringify(bqxz_zkgr)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetYl_HYyzmls',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //不是模板获取模板项目
  getYl_hymbsDT: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetYl_hymbsDT',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //是模板获取目录项目
  getYl_hyxms: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetYl_hyxms',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取项目数据
  getYl_hyxmxx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetYl_hyxmxx',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取所有项目数据
  getYl_hyxmxxAll: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetYl_hyxmxxAll',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取样本类型
  getYl_hyYblx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetYl_hyYblx',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取所有需要填写备注的化验项目
  getYl_hyfjbz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetYl_hyfjbz',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 获取下拉框数据
  getZqlbByZqlx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetZqlbByZqlx',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //化验项目之间，所有项目间的包含关系
  getHykz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetHykz',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //指定病区，所有项目的包含关系
  getZdbqxm: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetZdbqxm',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //保存数据前，获取患者基本信息
  getHzxx: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetHzxx',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //社保审批控制
  sfxsSbsp: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_sfxssbspval',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //将数据提交到数据库
  saveHyyz: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyz.aspx/SaveHyyz',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //删除提交过的医嘱
  deleteHYYZ: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyz.aspx/deleteHYYZ',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 获取近期导出数据
  GetHyyzYdr: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_GetHyyzYdr',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 所有需要弹出申请单窗口的化验项目
  CheckHySqd: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyz.aspx/e_CheckHySqd',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 预住院病人是否发送短信功能
  checkYzyPat: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_checkYzyPat',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //预住院病人发送短信功能
  sendMessage: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyz.aspx/sendMessage',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 判断校验医嘱病区
  checkZxbq: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/yz_sz/yz_sz.aspx/e_checkZxbq',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // BrMainLeft.aspx/getStaticCdssUrl(string as_xmmc)
  getStaticCdssUrl: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/BrMainLeft.aspx/getStaticCdssUrl',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  // 开单套餐重复项目提示
  checkHytcRepeatItem: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      if (o.success) o.success({
        Code: "1",
        CodeMsg: "获取数据成功！",
        Result: []
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/newhyyz/hyyzWebMethod.aspx/e_checkHytcRepeatItem',
        type: 'POST',
        data: JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
}