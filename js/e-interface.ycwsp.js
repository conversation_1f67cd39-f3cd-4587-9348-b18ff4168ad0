WRT_e.api = WRT_e.api || {}
WRT_e.api.ycwsp = {
  //获取主页内页数据
  GetSpList: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/ycwsp.aspx/e_GetSpList',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //获取相关的文书信息
  GetXgWsList: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/ycwsp.aspx/e_GetXgWsList',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //审批
  Sp: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/ycwsp.aspx/e_Sp',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  //作废审批信息
  ZF: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/ycwsp.aspx/e_ZF',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
  GetPatientInfo: function (o = {}) {
    o.params = o.params || {}
    if (WRT_config.mockType == '1') {
      o.msg = ehrSzinit
      if (o.success) o.success({
        Code: 1,
        CodeMsg: 1,
        Result: JSON.stringify(o.msg)
      })
    } else {
      $.ajax({
        url: WRT_config.server + '/zyblws/ycwsp.aspx/e_GetPatientInfo',
        type: 'Post',
        data:JSON.stringify(o.params),
        dataType: "json",
        cache:false,
        crossDomain:true, //设置跨域为true
        xhrFields: {
          withCredentials: true //默认情况下，标准的跨域请求是不会发送cookie的
        },
        contentType: "application/json; charset=utf-8",
        success: function (msg) {
          if (o.success) o.success(msg.d)
        },
        error: function (error) {
          if (error.statusText == 'error') {
            WRT_e.ui.hint({
              type: 'error',
              msg: "服务器连接失败"
            })
          }
        }
      })
    }
  },
}
