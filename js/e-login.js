var _oQRCode=undefined;
var _codeid=0
var _intervalID=0
var Timeout=null
//统一页面启动
$(document).ready(
  function () {
    sessionStorage.clear()
    // 获取谷歌浏览器版本
    if(getChromeVersion()) {
      var version = getChromeVersion();
      if(version < 69||!version) {
        WRT_e.ui.message({
          title: '提示信息',
          content: `您当前谷歌浏览器版本(${version||""})过低，为了更好地体验请将浏览器升级到最新版本！`,
          onOk() {
          }
        })
      }
    }else if(getSafariVersion()){
      var ver=getSafariVersion()
      if(!ver) {
        WRT_e.ui.message({
          title: '提示信息',
          content: `您当前AppleWebkit浏览器版本(${version||""})过低，为了更好地体验请将浏览器升级到最新版本！`,
          onOk() {
          }
        })
      }
    }
    WRT_e.api.au.getQRinit({
      params:{},
      success(data){
        if(data.Code==1){
          WRT_config.url=data.Result
          console.log(WRT_config.url)
        }
      }
    })
    WRT_e.api.au.Getinit({
      params:{},
      success(data){
        if(data.Code==1){
          WRT_config.au=data.Result
          // $(".logo_describe p").html(WRT_config.au.pageTitle)
          sessionStorage.setItem("ipAddress",WRT_config.au.ipAddress)
          $("#ip_host").html(WRT_config.au.ipAddress)
        }
      }
    })
    document.onkeydown = function (e) {
      let key = window.event.keyCode;
      if (key == 13) {
        if (e.target.name == 'username') {
          $("input[name='password']").focus()
        } else if (e.target.name == 'password') {
          //登录按钮样式触发
          $("#btnLogin").addClass('Log_animation')
          setInterval(function () {
            $("#btnLogin").removeClass('Log_animation')
          }, 1000)
          Login();
        }
      }
    }
    $("input[name='username']").focus()
  }
)
$("#goURL").click(function(){
  window.location.href=WRT_config.nwserver
})
//点击切换登录清空密码和验证码
$('#login .nav>li:not("active")>a').click(function (e) {
  $('#login [name="password"]').val('').parent().removeClass("focus");
  $('#login [name="code"]').val('').parent().removeClass("focus");
})
//表单点击focus
//点击表单和离开表单动画效果
$(".e_form>.e_form_item>.e_input")
  .on("focus", function () {
    var _this = $(this)
    _this.parent().addClass("focus");
  })
  .on("blur", function () {
    var _this = $(this)
    if (_this.val() === "") {
      _this.parent().removeClass("focus");
    }
  })
//两个表单内账号双向绑定
let allUsername = $('#login .tab-content>.tab-pane [name="username"]')
allUsername.bind("input propertychange", function () {
  allUsername.val($(this).val())
  if ($(this).val() === "") {
    allUsername.each(function () {
      $(this).is(':focus') ? '' : $(this).parent().removeClass("focus")
    });
  } else {
    allUsername.parent().addClass("focus");
  }
})

//二维码获取
function GetQRCode(){
  WRT_e.api.au.getQRCode({
    url:WRT_config.url.url1,
    params:{
      appCode: '020',  //应用代码
      opCode: "1",
      tip: "请注意确认后,即授权他人登录您的账号！",
      appType: "WEB"
    },
    success(data){
      if (data.hasError == 0) {
        var codeContent = data.data;
        if (codeContent.codeId != "") {
          _codeid = codeContent.codeId;
          $("#qrcode")[0].innerHTML=''
          _oQRCode = new QRCode(document.getElementById("qrcode"), {
            width : 90,
            height : 90
          });
          _oQRCode.clear(); // clear the code.
          _oQRCode.makeCode(codeContent.content);
          var date=new Date(codeContent.expTime)
          let num=date.getTime()-new Date().getTime()
          let time=parseInt(num)/1000
          if(_intervalID!=null){
            clearTimeout(Timeout)
            Timeout=setTimeout(function(){GetQRCode},time)
          }
          if (_intervalID != 0)
            clearInterval(_intervalID);                     
            _intervalID = setInterval(function () { checkQRCodeLoginProcess(); }, 3000);
        } else {
          alert("生成二维码失败！");
        }
      }
    }
  })
}
//询问二维码是否有效、是否登陆；
function checkQRCodeLoginProcess(){
  WRT_e.api.au.getQRCodeProgress({
    url:WRT_config.url.url2,
    params:{
      codeId: _codeid
    },
    success(data){
      if (data.hasError == 0) {
        var codeContent = data.data;
        if (codeContent.isActive == "1") {
          if (codeContent.userId != 0) {
            clearInterval(_intervalID);
            console.log(codeContent)
            WRT_e.api.au.login_ewm({
              params:{json:codeContent},
              success(data){
                if(data){
                  WRT_e.ui.hint({
                    type: 'success',
                    msg: '登陆成功'
                  })
                  window.location.href = "e-EhrSz.html";
                }
              }
            })
            // sessionStorage.setItem("username",user)
          }
        } else {
          
        }
      }
    }
  })
}


//点击获取验证码
$('#btnCode').click(function () {
  let _this = $(this)
  //判断账号
  let username = $('#login .tab-content>.tab-pane.active [name="username"]')
  if (username.val() == '') {
    WRT_e.ui.hint({
      type: 'error',
      msg: '账号不能为空'
    })
    username.focus()
    return false
  }
  WRT_e.ui.message({
    type: 'info',
    title: '验证码',
    content: '<p>确定发送验证码吗？</p>',
    onOk() {
      //验证码计时器
      var countdown = 60;
      (function settime(obj) {
        console.log(countdown, obj)
        if (countdown == 0) {
          obj.removeAttr("disabled");
          obj.val("获取验证码")
          countdown = 60;
          return;
        } else {
          obj.attr("disabled", true);
          obj.val("重新发送(" + countdown + ")")
          countdown--;
        }
        setTimeout(function () {
          settime(obj)
        }, 1000)
      })(_this)
      WRT_e.api.au.getCheckCode({
        params: {
          au_username: username.val(),
        },
        success(data) {
          if (data.Code == 1) {
            sessionStorage.setItem("zkid",data.Result.ZKID)
            WRT_e.ui.hint({
              type: 'success',
              msg: data.CodeMsg
            })
          }
        }
      })
    },
    onCancel() {

    },
  })
})

//点击登录
$('#btnLogin').click(function () {
  Login()
})
//获取IE浏览器


// 获取谷歌浏览器版本
function getChromeVersion() {
  var arr = navigator.userAgent.split(' '); 
  var chromeVersion = '';
  for(var i=0;i < arr.length;i++){
      if(/chrome/i.test(arr[i]))
      chromeVersion = arr[i]
  }
  if(chromeVersion){
      return Number(chromeVersion.split('/')[1].split('.')[0]);
  } else {
      return false;
  }
}
function getSafariVersion(){
  var userAgent = navigator.userAgent; //取得浏览器的userAgent字符串
  var isSafari = userAgent.indexOf("Safari");
  var verson=""
  if(isSafari>-1){
    var Sys = {};
    var ua = navigator.userAgent.toLowerCase();
    var s = ua.match(/version\/([\d.]+).*safari/) ? Sys.safari = s[1] : 0
    verson=Sys.safari
  }
  if(verson){
    return Number(verson.split('.')[0])
  } else {
    return false;
  }
}

function Login() {
  var version = getChromeVersion();
  var ver=getSafariVersion()
  if((version < 69||!version)&&!ver) {
    // WRT_e.ui.message({
    //   title: '提示信息',
    //   content: `您当前谷歌浏览器版本(${version||""})过低，为了更好地体验请将浏览器升级到最新版本！`,
    //   onOk() {
    //   } 
    // })

    if(version){
      alert(`您当前谷歌浏览器版本(${version||""})过低，为了更好地体验请将浏览器升级到最新版本！`)
    }else if(!ver){
      alert(`您当前AppleWebkit浏览器版本(${version||""})过低，为了更好地体验请将浏览器升级到最新版本！`)
    }else{
      alert(`您当前浏览器版本过低，为了更好地体验请将浏览器升级到最新版本！`)
    }
    return
  }
  //当前表单
  let form = $('#login .tab-content>.tab-pane.active')
  let username = form.find('[name="username"]')
  let password = form.find('[name="password"]')
  let code = form.find('[name="code"]')
  //判断账号
  if (username.val() == '') {
    WRT_e.ui.hint({
      type: 'error',
      msg: '账号不能为空'
    })
    username.focus()
    return false
  }
  //判断密码
  if (password.val() == '') {
    WRT_e.ui.hint({
      type: 'error',
      msg: '密码不能为空'
    })
    password.focus()
    return false
  }
  //判断验证码
  if (code.val() == '') {
    WRT_e.ui.hint({
      type: 'error',
      msg: '验证码不能为空'
    })
    code.focus()
    return false
  }
  //验证登陆
  console.log(WRT_e.api)
  let user=(username.val()).toUpperCase()
  WRT_e.api.au.login({
    params: {
      au_username: user||"",
      au_password: password.val() || '',
      au_code: code.val() || ''
    },
    success(msg) {
      if(msg.Result){
        let name=window.btoa(user);
        let pass=window.btoa(password.val());
        sessionStorage.setItem("info_user",name+','+pass)
        sessionStorage.setItem("username",user)
        WRT_config.login=true
        if(WRT_config.loginByAccount&&WRT_config.login){
          WRT_e.ui.hint({
            type: 'success',
            msg: '登陆成功'
          })
          window.location.href = "e-EhrSz.html";
        }
      }else if(msg.CodeMsg){
        WRT_e.ui.hint({mes:msg.CodeMsg,type:'error'})
      }
    }
  })
  WRT_e.api.au.loginByAccount({
    params: {
      yongHuZH: user||"",
      yongHuMM: password.val() || '',
      yingYongDM: '020'
    },
    success(msg) {
      if(msg.data){
        console.log()
        sessionStorage.setItem("token",msg.data.accessToken)
        sessionStorage.setItem("userid",msg.data.yongHuID)
        WRT_config.loginByAccount=true
        if(WRT_config.loginByAccount&&WRT_config.login){
          WRT_e.ui.hint({
            type: 'success',
            msg: '登陆成功'
          })
          window.location.href = "e-EhrSz.html";
        }
      }else if(msg.errorMessage||(msg.data&&msg.data.hasError<0)||msg.data.errorMessage){
        WRT_e.ui.hint({msg:msg.errorMessage||msg.data.errorMessage,type:'error'})
      }
    }
  })
}