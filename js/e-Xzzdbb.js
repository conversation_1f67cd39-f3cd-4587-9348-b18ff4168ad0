

let params = {}
let url = window.location.href.split("?") || []
if(url[1]){
  let text = url[1].split("&")
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
}
// console.log(params)
var obj1 = []
var searchType = false
// 统一页面启动
$(document).ready(() => {
  init()
})
/********************初始化********************/
function init() {
  // 搜索框展示内容
  // $(".inputType").html(
  //   new searchTop_View().init().render().$el
  // )
  WRT_e.api.ehrSz.getAllBqData({
    success(data) {
      if (data.Code == '1') {
        var obj = JSON.parse(data.Result)
        WRT_config.ehrSz_ALlBQXX = obj
        // console.log(obj);
        $(".inputType").html(
          new searchTop_View().init({
            data: WRT_config.ehrSz_ALlBQXX
          }).render().$el
        )
      }
    }
  })
}

/********************公共方法********************/
// 点击病人名字打开该病人的NRS2002筛查表，地址为/ehr/yyfxsc/yyfxsc.aspx?as_blid={病例ID}&as_temp={随机数}
function openSCB(item) {
  console.log(item);
  let url =  `${WRT_config.server}/yyfxsc/yyfxsc.aspx?as_blid=${item.BLID}&as_temp=${Math.random()}`
  WRT_e.ui.model({
    id:'if_SCB',
    title: item.XM + 'NRS2002筛查表',
    width: "1200px",
    iframeURL:`${url}`,
    iframeHeight:'800px',
    iframe: true,
  })
}

function showOption() {
  // console.log('sdfgh',$('.iput').val());
  if ($('.iput').val()) {
    $('.op-list').toggleClass('hidden');
    search1($('.iput').val())
  } else {
    $('.op-list').toggleClass('hidden');
    $('.iop').show();
  }
}

function search(value) {
  // console.log('参数',value);
  $('.iput')[0].dataset.bqid = ""
  $('.iop').show();
  $('.iop').each(function () {
      var text = $(this).text();
      if (text.indexOf(value) == -1) {
          $(this).hide();
      }
  });
}
function search1(value) {
  // console.log('参数2',value);
  $('.iop').show();
  $('.iop').each(function () {
      var text = $(this).text();
      if (text.indexOf(value) == -1) {
          $(this).hide();
      }
  });
}

/******************** 视图 ********************/
// 患者基本信息
var searchTop_View = WRT_e.view.extend({
  render: function () {
    let html=`
      <div class='infoCondition'>
        <label class="control-label label-name" style="font-size: 22px;min-width:50px;padding: 0 50px 0 20px; border-left: 3px solid #155bd4; height: 34px; line-height: 34px;"> 查询方式 </label>
        <div class="infoR">
          <div class="infoRLabel">
            <label class="control-label label-name">
              <span class="iTabel1"> 病案号： </span>
              <input class="form-control" type='text' name='BAH' style="width:186px;"  placeholder="请输入病案号. . ."/>  
            </label>
            <button class='e_btn_primary searchT'>
              <i class="glyphicon glyphicon-search"></i>
              查询
            </button>
          </div>
          <div class="infoRLabel">
            <label class="control-label label-name" style="align-items: flex-start !important;">
              <span class="iTabel1"> 病区ID： </span>
              <div>
                <input class="form-control iput" placeholder="请点击或输入病区..." data-bqid="" onclick="showOption()" oninput="search(this.value)" onporpertychange="search(this.value)" onchange="search(this.value)">
                <div class="op-list hidden form-control" name=bqid>
                  ${_.map(this.data, (item)=> `
                    <div class="iop" data-bqid=${item.BMID}>${item.BMMC}</div>
                  `).join('')}
                </div>
              </div>
            </label>
            <button class='e_btn_primary searchT1'>
              <i class="glyphicon glyphicon-search"></i>
              查询
            </button>
          </div>
          
        </div>
      </div>
    `
    this.$el.html(html)
    return this;
  }, events: {
    // 查询
    "click .searchT":function () {
      let BAH =  $('.control-label input[name=BAH]').val()
      if (BAH && BAH !='') {
        WRT_e.api.ehrSz.getSearchBAH({
          params: {as_bah: BAH},
          success(data) {
            console.log(data);
            if (data.Code == 1) {
              let obj = JSON.parse(data.Result)
              WRT_config.BAHSearchRes = obj
              
              console.log(data,obj);
              var searchResTB = new searchResTB_View();
              searchResTB.$el = $(".resTB");
              searchResTB.init({ 
                data: {
                  init: WRT_config.BAHSearchRes,
                  tableName: '病案号查询结果'
                }
              }).render();
            }
          }
        })
      } else { // 请填写病案号
        WRT_e.ui.hint({
          type: 'error',
          msg: "请填写病案号"
        })
      } 
    },
    "click .searchT1":function () {
      let bqid = $('.iput')[0].dataset.bqid
      // $('.control-label div[name=bqid]').val()
      if(bqid) {
        console.log(bqid);
        $(".resTB").html(`  
          <img  class="load_img" src="./images/load.gif" style="width: 160px;" />
        `)
        WRT_e.api.ehrSz.getSearchBqid({
          params: {ll_bqid: bqid},
          success(data) {
            if (data.Code == 1) {
              let obj = JSON.parse(data.Result)
              WRT_config.BQIDSearchRes = obj

              console.log(data,obj);
              var searchResTB = new searchResTB_View();
              searchResTB.$el = $(".resTB");
              searchResTB.init({ 
                data: {
                  init: WRT_config.BQIDSearchRes,
                  tableName: '病区ID 查询结果'
                }
              }).render();
            }
          }
        })
      } else {
        // 请填写病区ID
        WRT_e.ui.hint({
          type: 'error',
          msg: "请选择对应病区ID"
        })
      } 
    },
    "click .iop":function (e) {
      var text = e.target.innerText;
      $('.iput')[0].dataset.bqid = e.target.dataset.bqid,
      console.log('参数选择', $('.iput'),$('.op-list'));
      $('.iput').val(text);
    }

  }
})

// 查询结果
var searchResTB_View = WRT_e.view.extend({
  render: function () {
    let html=`
      <h3 style="text-align: center;font-weight: bold;">${this.data.tableName}</h3>
      <div class="overall_table_frame">
        <table id="crrt_table">
          <thead>
            <tr class="row_head">
              <th style="text-align: center">专科</th>
              <th width="276" style="text-align: center">病区</th>
              <th style="text-align: center">床位</th>
              <th width="150" style="text-align: center">病人姓名</th>
              <th width="276" style="text-align: center">病案号</th>
              <th style="text-align: center">修正诊断</th>
              <th width="276" style="text-align: center">修改时间</th>
            </tr>
          </thead>
          <tbody class="line_nr" id="line_nr">
          ${this.data.init.length==0?`
            <tr>
              <td colspan="8" style='text-align: center;'>
              <div style='position:relative;font-size: 18px;padding: 10px 5px 10px;width: 80%;margin: 0 auto;text-align: center;'> 未查询到相关病人数据 </div>
              </td>
            </tr>
          `:`<tr class="row_nr">
            ${_.map(this.data.init,(item,index)=>`<td class='zk${index}' > ${item.ZKMC} </td>
              <td class='bq${index}' >  ${item.BQMC} </td>
              <td class='cwh${index}' > ${item.CWH} </td>
              <td class='xm${index}' > 
                <a href="javascript:void(0)"  onclick='openSCB(${JSON.stringify(item)})'>${item.XM}</a>
              </td>
              <td class='bah${index}' > ${item.GLOBAL_PID} </td>
              <td class='xzzd${index}' > ${item.MC} </td>
              <td class='xgsj${index}' > ${item.XGSJ} </td>
            </tr>
            `).join('')}
          `}
            
          </tbody>
        </table>
      </div>`
    this.$el.html(html)
    return this;
  }
})
