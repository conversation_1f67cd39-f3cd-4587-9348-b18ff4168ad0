let url = window.location.href.split("?") || []
let text = url[1].split("&")
let param = {}
for (let i of text) {
  let fd = i.split("=")
  param[fd[0]] = fd[1]
}
//统一页面启动(儿科营养医嘱)
$(document).ready(() => {
  // 儿科初始化
  WRT_e.api.pnOrderList.getInitList({
		params: {
			ll_blid: param["as_blid"]
			// al_blid: WRT_config.BrMainLeft.BRJBXX.BLID
    },
		success(data) {
			if (data.d.Code == 1) {
				WRT_config.pnOorderList_init=JSON.parse(data.d.Result)
        sessionStorage.setItem('pnOorderList_init',data.d.Result)
				console.log('儿科营养医嘱列表初始化',WRT_config.pnOorderList_init)
				// 初始化
        app.init()
			}
		}
	})
	// // 初始化
	// app.init()
})

var app = {
	init: function () {
		var TableInfo = new TableInfo_View();
		TableInfo.$el = $(".pnOLTable");
		TableInfo.init({
		  data: WRT_config.pnOorderList_init
		}).render();
	}
}

/********************公用方法********************/
function addNewNutritionOrder() {
  if(!WRT_config.pnOorderList_init['是否出院']){
    let url = '/e-nbPNCalculator.html?as_yzdid=0&as_blid=' + param["as_blid"] + '&tempid='+ Math.random()
    window.open(url)
  } else {
    WRT_e.ui.hint({
      type:'error',
      msg:'该病人已出院无法进行新增'
    })
  }
}
function openNuOrder2(id) {
  var src = "/e-nbPNCalculator.html?as_yzdid=" + id + "&as_blid=" + param["as_blid"] + "&tempid=" + Math.random();
  window.open(src, "儿科静脉营养医嘱单");
}
// // 关闭窗口
// function closeModalPnNow() {
//   $('#tabelIframe').iziModal('destroy')
//   app.init()
// }
function reloadData(){
  // window.location.reload(true);
  WRT_e.api.pnOrderList.getInitList({
		params: {
			ll_blid: param["as_blid"]
			// al_blid: WRT_config.BrMainLeft.BRJBXX.BLID
    },
		success(data) {
			if (data.d.Code == 1) {
				WRT_config.pnOorderList_init=JSON.parse(data.d.Result)
				console.log('儿科营养医嘱列表初始化',WRT_config.pnOorderList_init)
				// 初始化
        app.init()
			}
		}
	})
}

/********************视图********************/
// 表格
var TableInfo_View = WRT_e.view.extend({
  render: function () {
    // 初始数据
    console.log(this.data['医嘱单内容'])
    let initialData = this.data['医嘱单内容']
    // let initialData = [
		// 	{
		// 		'开始时间':'2023/2/23 15:27:55',
		// 		'开单医师':'王华晓',
		// 	},
		// 	{
		// 		'开始时间':'2023/2/23 15:27:55',
		// 		'开单医师':'王华晓',
		// 	},
    // ]
    // 行数
    // var comlum = $("#table_internal tr").length
		// bgcolor="#A3BEFF" #a3bef080 align="center"   <a class="cease" herf="javascript:void(0)" onclick="stopNuorder()">  停止 </a>
    // &nbsp;&nbsp;&nbsp;&nbsp;
    // <a class="print" herf="javascript:void(0)" onclick="prtNuOrderList()">  打印 </a>
          
    let html = `
    ${initialData.length>0?`
      <tr align="start" bgcolor="#A3BEFF" class="table_header">
        <td width="435px" class="table-title"><span>开始时间</span></td>
        <td width="300px" class="table-title"><span>开单医师</span></td>
        <td width="500px" class="table-title"><span>操作</span></td>
      </tr>
      ${_.map(initialData, (obj,index)=>`
        <tr align="start"  id="table_data" class="table_data`+index+`">
          <td class="table-nr">${(obj.YZSJ).replace('T',' ')}</td>
          <td class="table-nr">${obj.YHXM}</td>
          <td class="table-nr">
            <a class="detailed" herf="javascript:void(0)" onclick='openNuOrder2(${obj.YZDID})'> 查看 </a>
            &nbsp;&nbsp;&nbsp;&nbsp;
          </td>
        </tr>
      `)}
    `:``}
    `
    this.$el.html(html)
    return this;
  }
})

