var dqtl=[18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44]
var chart_list=['','','','','','','','','','','','','','','','','','','','','','','','','','','']
//统一页面启动
$(document).ready(() => {
  //初始化
  let url = window.location.href.split("?") || []
  let text = url[1].split("&")
  let params = {}
  for (let i of text) {
    let fd = i.split("=")
    params[fd[0]] = fd[1]
  }
  WRT_config.url = params
  /********************初始化获取页面数据********************/
  load()
})
//
var app = {
  init: function () {
    //初始化
    var Menus = new Menus_View();
    Menus.$el = $("#sx_form");
    Menus.init({
      data: WRT_config.Szfyqx
    }).render();
    var right = new right_View();
    right.$el = $("#right_list");
    right.init({
      data: WRT_config.Szfyqx
    }).render();
    let arr=chart_list
    if(WRT_config.Szfyqx){
        WRT_config.Szfyqx.map(function(item){
            let i=parseInt(item.DQTL)-18
            arr[i]=item.TZ
        })
    }
    let man=[{
        name: 'P3',
        type: 'line',
        data: ['','','','','','',356,444,534,628,724,825,935,1059,1205,1376,1576,1803,2053,2308,2515,2643,2723,2784,2839,'','']
      },
      {
        name: 'P10',
        type: 'line',
        data: ['','','','','','',434,538,645,753,865,980,1105,1244,1404,1590,1801,2035,2289,2543,2749,2877,2959,3021,3077,'','']
      },
      {
        name: 'P25',
        type: 'line',
        data: ['','','','','','',520,642,765,890,1017,1147,1286,1440,1614,1814,2036,2279,2536,2790,2877,2993,3203,3266,3323,'','']
      },
      {
        name: 'P50',
        type: 'line',
        stack: 'Total',
        data: ['','','','','','',624,766,909,1053,1196,1343,1497,1666,1857,2071,2306,2558,2820,3073,3273,3399,3482,3545,3602,'','']
      },
      {
        name: 'P75',
        type: 'line',
        data: ['','','','','','',737,901,1064,1226,1387,1549,1718,1902,2108,2337,2585,2847,3114,3366,3562,3685,3767,3830,3887,'','']
      },
      {
        name: 'P90',
        type: 'line',
        data: ['','','','','','',846,1031,1212,1390,1566,1742,1925,2122,2341,2584,2843,3114,3386,3637,3828,3949,4030,4092,4148,'','']
      },
      {
        name: 'P97',
        type: 'line',
        data: ['','','','','','',962,1166,1366,1561,1752,1941,2136,2346,2578,2830,3104,3384,3662,3912,4098,4215,4294,4355,4410,'','']
      }]
    let woman=[
        {
            name: 'P3',
            type: 'line',
            data: ['','','','','','',304,395,487,582,680,781,890,1012,1152,1314,1503,1719,1960,2204,2409,2543,2623,2681,2731,'','']
        },
        {
            name: 'P10',
            type: 'line',
            data: ['','','','','','',359,466,575,686,799,917,1042,1181,1338,1518,1722,1951,2197,2439,2640,2770,2849,2905,2945,'','']
        },
        {
            name: 'P25',
            type: 'line',
            data: ['','','','','','',425,550,677,806,936,1070,1212,1367,1541,1737,1955,2193,2445,2685,2879,3006,3083,3138,3185,'','']
        },
        {
            name: 'P50',
            type: 'line',
            stack: 'Total',
            data: ['','','','','','',513,662,811,960,1109,1261,1419,1591,1782,1993,2225,2472,2727,2964,3153,3275,3349,3402,3448,'','']
        },
        {
            name: 'P75',
            type: 'line',
            data: ['','','','','','',622,796,968,1138,1306,1474,1648,1835,2039,2264,2506,2760,3018,3251,3433,3550,3621,3673,3717,'','']
        },
        {
            name: 'P90',
            type: 'line',
            data: ['','','','','','',740,939,1132,1321,1504,1686,1872,2071,2285,2519,2768,3028,3286,3515,3691,3803,3872,3921,3963,'','']
        },
    ]
    let list=WRT_config.url.al_sex==1?man:woman
    var myChart = echarts.init(document.getElementById('main'));
    // 指定图表的配置项和数据
    var option = {
        title: {
          text: '儿科新增生长发育曲线'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: WRT_config.url.al_sex==1?['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97','实际体重']:['P3', 'P10', 'P25', 'P50', 'P75', 'P90','实际体重']
        },
        dataZoom: [
            {
                type: 'slider',
                show: false,
                handleSize: 8,
                filterMode: 'filter',
            },
            {
                type: 'inside',
                start: 94,
                end: 100
            },
            {
                type: 'slider',
                show: true,
                yAxisIndex: 0,
                filterMode: 'empty',
                handleSize: 8,
                showDataShadow: true,
            }
        ],
        grid: {
        },
        toolbox: {
        },
        xAxis: {
            name:'出生胎龄（周）',
            type: 'category',
            boundaryGap: false,
            data: dqtl
        },
        yAxis: {
            name:'胎儿体重（克）',
            type: 'value',
            min:0,
            // max:5000,
        },
        series: [
          ...list,
          {
            name: '实际体重',
            type: 'scatter',
            // colorBy:'#0512c2',
            symbolSize:'10',
            data: arr
          }
        ]
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
  }
}

/********************公用方法********************/
function load(){
    WRT_e.api.Szfyqx.getQuery({
        params: {
            BLID: WRT_config.url["al_blid"]||1492917
        },
        success(data) {
          WRT_config.Szfyqx = JSON.parse(data.Result)
          console.log(WRT_config.Szfyqx)
          app.init()
        }
      })
}

//编辑回调
function onsave(id){
    let CSTL=$(".cstl_text option:selected").val()
    let DQTL=$(".dqtl_text")[0].value
    let TZ=$(".tz_text")[0].value
    WRT_e.api.Szfyqx.getUpdate({params:{id:id,CSTL:CSTL,DQTL:DQTL,TZ:TZ},
        success(data){
            if(data.Code==1){
                $('#if_szfyqx').iziModal('destroy')
                WRT_e.ui.hint({type:"success",msg:'修改成功'})
                load()
            }else{
                WRT_e.ui.hint({msg:data.Codemsg})
            }
        }
    })
}
//方法调用

//新增
function editModel(id,index){
    let list=WRT_config.Szfyqx[index]||{}
    let html=`
    <span>出生胎龄：<select class="input_text cstl_text">
        ${_.map(dqtl,(obj)=>
            `<option value="${obj}" ${list.CSTL==obj?'selected':''}>${obj}</option>`
        ).join('')}
    </select></span>
    <span>当前胎龄：<input type="text" class="input_text dqtl_text" value="${list.DQTL||''}" /></span>
    <span>体重：<input type="text" class="input_text tz_text" value="${list.TZ||''}" /></span>
    <button class="e_btn_primary" onclick="onsave(${id})" style="height: 25px;">保存</button>
    <button class="e_btn_primary" style="height: 25px;" onclick="$('#if_szfyqx').iziModal('destroy')">取消</button>`
    WRT_e.ui.model({
        id: "if_szfyqx",
        title: "新增",
        width: "650px",
        content: html,
        iframe: false,
    })
}
//删除
function delModel(id){
    WRT_e.ui.message({
        title: '提示信息',
        content: "是否确认删除",
        onOk() {
            WRT_e.api.Szfyqx.getDelet({params:{id:id},
                success(data){
                    if(data.Code==1){
                        WRT_e.ui.hint({type:"success",msg:'删除成功'})
                        load()
                    }else{
                        WRT_e.ui.hint({msg:data.Codemsg})
                    }
                }
            })
        },
        onCancel(){}
    })
}


//视图
var Menus_View= WRT_e.view.extend({
    render: function () {
        
        var html=`
        <span>出生胎龄：<select class="input_text cstl">
            ${_.map(dqtl,(obj)=>
                `<option value="${obj}">${obj}</option>`
            ).join('')}
        </select>周</span>
        <span>当前胎龄：<input type="text" class="input_text dqtl" />周</span>
        <span>体重：<input type="text" class="input_text tz" />克</span>
        <button class="e_btn_primary save" style="height: 25px;">保存</button>`
        this.$el.html(html)
        return this
    },
    events:{
        "click .save":"save"
    },
    save(){
        let CSTL=$(".cstl option:selected").val()
        let DQTL=$(".dqtl")[0].value
        let TZ=$(".tz")[0].value
        WRT_e.api.Szfyqx.getInsert({params:{BLID:WRT_config.url.al_blid,CSTL:CSTL,DQTL:DQTL,TZ:TZ},
            success(data){
                if(data.Code==1){
                    WRT_e.ui.hint({type:"success",msg:'新增成功'})
                    load()
                }else{
                    WRT_e.ui.hint({msg:data.Codemsg})
                }
            }
        })
    }
})


var right_View= WRT_e.view.extend({
    render: function () {
        var html=`
        <div class="szfyqx_lssqd">
            <div class="head">
            生长发育曲线列表
            </div>
            <div class="list">
                <table style="width: 100%;table-layout: fixed;">
                <tr>
                    <th width="60">出生胎龄</th>
                    <th width="60">当前胎龄</th>
                    <th width="60">体重</th>
                    <th width="120">操作时间</th>
                    <th>操作</th>
                </tr>
                ${_.map(WRT_config.Szfyqx,(obj,index)=>
                `<tr data-id="${obj.ID}">
                    <td><a class="name" href="javascript:;">${obj.CSTL}</a></td>
                    <td><a class="name" href="javascript:;">${obj.DQTL}</a></td>
                    <td><a class="name" href="javascript:;">${obj.TZ}</a></td>
                    <td>${(new Date(obj.XGSJ)).Format("yyyy-MM-dd HH:mm")}</td>
                    <td><a class="e-btn-blue" onclick="editModel(${obj.ID},${index})" style="margin-right: 5px;">编辑</a><a class="e-btn-del" onclick="delModel(${obj.ID})">删除</a></td>
                </tr>`
                ).join('')}
                </table>
            </div>
        </div>`
        this.$el.html(html)
        return this
    },

})

