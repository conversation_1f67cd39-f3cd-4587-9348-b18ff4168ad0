let url = window.location.href.split("?") || []//console.log
let text = url[1].split("&")
let params = {}
for (let i of text) {
  let fd = i.split("=")
  params[fd[0]] = fd[1]
}
let obj1 = []
let objNew = []
var lNum = 1
var iframe_Id=null;
var noteViews=[];
var j_sfsc = "0";
var lsSaveId = '-1';
// //console.log(params)
//统一页面启动
$(document).ready(() => {
  //初始化
  // (√)4、病程记录/入院记录初始化e_init(√<2个>)
  WRT_e.api.blwslist.getBlwslist({
    params: {
      "as_blid": params["as_blid"],
      "as_zyid": params["as_zyid"],
      "as_lx": params["as_lx"],
      "as_zkid": params["as_zkid"],
      "as_zkdm": params["as_zkdm"],
      "as_baseurl": "http://localhost:8080/"
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.BrwsCsh = data.Result
        console.log(WRT_config.BrwsCsh)
        // //console.log("4、病程记录/入院记录初始化e_init(BrwsCsh):", WRT_config.BrwsCsh)
        // 上方
        $(".blwslist_info").html(
          new progress_top().init().render().$el
        )
        // 初始化接口增加返回参数 VTECS 
        // VTECS()
        // (√)12、获取危急值列表e_GetWjzLst（用于右上方）
        WRT_e.api.blwslist.GetWjzLst({
          params: {
            "as_blid": params["as_blid"]
          },
          success(data) {
            if (data.Code == 1) {
              WRT_config.WjzLst = data.Result
              // //console.log("12、获取危急值列表e_GetWjzLst", WRT_config.WjzLst)
              init()
            }
          }
        })
      }
    }
  })
})
/********************初始化********************/
function init() { 
  $(function(){
    var $url = "http://ipinfo.io/json";
    $.getJSON($url,function(data){
      console.log(data)
    })
  })
  // WRT_e.api.blwslist.GetVTEJsonId({
  //   params: {
  //     "Method": "getVTEJsonId",
  //     "as_blid": params["as_blid"],
  //     "as_wslx": '11',
  //   },
  //   success(data) {
  //     //console.log(data)
  //   }
  // })
  
  // console.log('病人年龄',parent.WRT_config.BrMainLeft.BRNL,parseFloat(parent.WRT_config.BrMainLeft.BRNL)>14)
  // (√)5、根据页面类型获取病人已书写文书列表e_GetBrWssByLx
  WRT_e.api.blwslist.getBrWssByLx({
    params: {
      "as_blid": params["as_blid"],
      "as_zyid": params["as_zyid"],
      "as_lx": params["as_lx"]==0?`01`:`02`,
      "as_zkid": params["as_zkid"],
      "as_zkdm": params["as_zkdm"],
      "as_baseurl": "http://localhost:8080/",
      "as_rjss": WRT_config.BrwsCsh.BRXX.RJSS
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.BrwsList = data.Result
        // //console.log(" 5、根据页面类型获取病人已书写文书列表e_GetBrWssByLx", WRT_config.BrwsList)
        let json = WRT_config.BrwsList.dvm_wslist.map(function (item) {
          item.two = 0
          item.urlBlws = 0
          return item
        })
        // //console.log(json.find(e=>e.WSLX=="11").WSLX=="11")
        // if (json.find(e=>e.WSLX=="11").WSLX=="11") {
        //   j_sfsc="1"
        //   $("#xz_nr option:nth-child(2)").prop("selected", 'selected')
        //   // ${j_sfsc=="1"?`${}`:``}
        // } else {
        //   j_sfsc="0"
        // }
        // //console.log(json)
        // 左
        // (√)6、初始化专科病程录选项e_GetWslbItem
        WRT_e.api.blwslist.getWslbItem({
          params: {
            as_zkid: params["as_zkid"],
          },
          success(data) {
            if (data.Code == 1) {
              WRT_config.WslbItem = data.Result
              // WRT_config.WslbItem = (data.Result).filter(item=>item.GSMC!='出院记录')
              console.log("6、初始化专科病程录选项e_GetWslbItem", WRT_config.WslbItem)
              $(".list").html(
                new progress_noteList().init({
                  data: {
                    dataRList: WRT_config.BrwsList,
                    dataWSLXS: WRT_config.BrwsCsh.WSLXS
                  }
                }).render().$el
              )
              if ((json.find(e=>e.WSLX=="11")!=undefined)&&(json.find(e=>e.WSLX=="11").WSLX=="11")) {
                j_sfsc="1"
                $("#xz_nr option:nth-child(2)").prop("selected", 'selected')
              } else {
                j_sfsc="0"
                $("#xz_nr option:nth-child(1)").prop("selected", 'selected')
              }
              // 右上
              $(".panel-group").html(
                new progress_notetop().init({
                  data:{
                    dataList: WRT_config.BrwsList.dvm_wslist,
                    data1: WRT_config.WsDetail,
                    // dataAdd: WRT_config.NewWsHtml
                  }
                }).render().$el
              )
              renderWxz();
            }
          }
        })
        function renderWxz(){
          $.each(json,(idx,ev) => {
            var content=new progress_noteContent().init();
            noteViews.push(content);
            $(".zd").append(content.$el);
            // 右
            content.init({
              data:{
                dataList:ev,
                data1: WRT_config.WsDetail,
                // dataAdd: WRT_config.NewWsHtml
              }
            }).render();
          })
        }
      }
    }
  })
  
  // 修改个人信息
  WRT_e.api.blwslist.GetBrMainEinit({
    params: {
      "idb": params["as_zyid"],
      "blid": params["as_blid"],
      "showbt": "",
      "nowblid": ""
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.BrMainEinit = data.Result
        // //console.log("修改个人信息",WRT_config.BrMainEinit)
      }
    }
  })
}


/***************公共方法***************/
//侧边栏收放
function img_click(type) {
  if (type == 'open') {
    $("#arrow_open").addClass("none")
    $("#arrow_close").removeClass("none")
    $(".blwslist_inner_left").addClass("none")
    // $("#left_img").addClass("left_img")
  } else {
    $("#arrow_open").removeClass("none")
    $("#arrow_close").addClass("none")
    $(".blwslist_inner_left").removeClass("none")
    // $("#left_img").removeClass("left_img")
  }
}

// 在上级医师查房记录点击签名保存后，取消编辑状态，进入预览状态
function confirm_callback(wsid, tmpid){
  //console.log(wsid, tmpid)
  //console.log(WRT_config.BrwsList.dvm_wslist)
  // var dataList = WRT_config.BrwsList.dvm_wslist
  var nowData =  WRT_config.BrwsList.dvm_wslist.find(item => {
    return item.WSID==wsid;
  })
  // var view = dataList.find(item => {
  //   return item.WSID==wsid;
  // })
  // var view =noteViews.find(el=>{
  //   if(el.data.dataList.WSID!=0){
  //     return el.data.dataList.WSID==wsid;
  //   }
  //   else{
  //     el.data.dataList.WSID=wsid

  //     el.data.dataList.id==tmpid;
  //     return el
  //   }
  // })
  // console.log(view)
  // view.edit2(ev)
  WRT_e.api.blwslist.getWsDetail({
    params: {
      "as_gsdm": nowData.GSDM,
      "as_blid": params["as_blid"],
      "as_zyid": params["as_zyid"],
      "as_wsid": wsid,
      "as_url": "http://localhost:8080/"
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.WsDetail = data.Result
        let json = nowData
        json.two ='1',
        json. WslbItem_nrUrl="as_blid=" + params["as_blid"] + "&as_gsdm=" + nowData.GSDM + "&as_zyid=" + params["as_zyid"] + "&as_wsid="+wsid+"&as_wslx=0&as_tmpid=t1&tmpid=0.44261011765709646"
        json.PREVIEW_HTML=WRT_config.WsDetail
        
        
        var view =noteViews.find(el=>{
          if(el.data.dataList.WSID!=0){
            el.data.dataList.PREVIEW_HTML=WRT_config.WsDetail
            el.data.dataList.WSID==wsid;
            return el
          }
          else{
            el.data.dataList.WSID=wsid
            el.data.dataList.id==tmpid;
            return el
          }
        })
        view.isEditor = false
        $("#panel"+json.WSID).html(view.$el);
        view.init({
          data:{
            dataList:json,
            data1: WRT_config.WsDetail
            // dataAdd: WRT_config.NewWsHtml
          }
        }).render();
        // console.log(view)
        // console.log(view.$el.find(".panel-collapse"));
        view.$el.find(".panel-collapse").collapse('show');
      }
    }
  })
}

// 判断病人VTE评分是否填写
function VTECS(wslx,gsdm){
  let urls=`http://172.16.202.14:5321/#/pc/huashan/jiankong/queren/`
  //注释
  // if(WRT_config.BrwsCsh.VTECS==1){
  //   urls=`http://172.16.202.41:5321/#/pc/huashan/jiankong/queren/`
  // }else if(WRT_config.BrwsCsh.VTECS==2){
  //   urls=`http://172.16.202.14:5321/#/pc/huashan/jiankong/queren/`
  // }
  var ret
  //console.log(wslx)
  if (wslx == "11") {
    WRT_e.api.blwslist.GetVTEJsonId({
      params: {},
      success(data) {
        //console.log(data)
        var d = data.code;
        if (d == "2" && data.msg== "入院评分") {
          ret = "1";
          WRT_e.ui.message({
            title: '提示',
            content: `完成内或外科住院患者静脉血栓栓塞症的风险评估(VTE)后才可书写首次病程记录`,
            onOk() {
              
              var url = urls + parent.WRT_config.BrMainLeft.YHXX.YHZH + "/" + WRT_config.BrwsCsh.BRXX.ZYH + "/" + WRT_config.BrwsCsh.BRXX.BLID + "/1/" + data.msg;
              // let url = WRT_config.server + `/pfbgl/pfbym.aspx?as_blid=${params["as_blid"]}&as_pfid=148&as_pfzhid=0`         
              // parent.page_iframe.add("表", url)
              window.open(url)
              // return;
            },
          })
        } else if (d == "0") {
          ret = "1";
          WRT_e.ui.message({
            title: '提示',
            content: `完成内或外科住院患者静脉血栓栓塞症的风险评估(VTE)后才可书写首次病程记录`,
            onOk() {
              var url =urls + parent.WRT_config.BrMainLeft.YHXX.YHZH + "/" +  WRT_config.BrwsCsh.BRXX.ZYH + "/" +  WRT_config.BrwsCsh.BRXX.BLID + "/" + data.msg;
              // let url = WRT_config.server + `/pfbgl/pfbym.aspx?as_blid=${params["as_blid"]}&as_pfid=148&as_pfzhid=0`         
              // parent.page_iframe.add("表", url)
              window.open(url)
              // return;
            },
          })
        } else if (d == "1") {
          AddNewWs(gsdm)
        }
         
      },
      error: function (err) {
        ret = "-2";
      }
    });
    if (ret == "1") {
      return;
    }
  } 
  else if (wslx == "15") {
    WRT_e.api.blwslist.GetVTEJsonId2({
      params: {},
      success(data) {
        //console.log(data)
        var d = data.code;
        if (d == "2" && data.msg== "转科后评分") {
          ret = "1";
          WRT_e.ui.message({
            title: '提示',
            content: `完成内或外科住院患者静脉血栓栓塞症的风险评估(VTE)后才可书写接科记录`,
            onOk() {
              var url =urls + parent.WRT_config.BrMainLeft.YHXX.YHZH + "/" + WRT_config.BrwsCsh.BRXX.ZYH + "/" + WRT_config.BrwsCsh.BRXX.BLID + "/1/" + data.msg;
              // let url = WRT_config.server + `/pfbgl/pfbym.aspx?as_blid=${params["as_blid"]}&as_pfid=148&as_pfzhid=0`         
              // parent.page_iframe.add("表", url)
              window.open(url)
              // return;
            },
          })
        } else if (d == "0") {
          ret = "1";
          WRT_e.ui.message({
            title: '提示',
            content: `完成内或外科住院患者静脉血栓栓塞症的风险评估(VTE)后才可书写接科记录`,
            onOk() {
              var url = urls + parent.WRT_config.BrMainLeft.YHXX.YHZH + "/" +  WRT_config.BrwsCsh.BRXX.ZYH + "/" +  WRT_config.BrwsCsh.BRXX.BLID + "/" + data.msg;
              // let url = WRT_config.server + `/pfbgl/pfbym.aspx?as_blid=${params["as_blid"]}&as_pfid=148&as_pfzhid=0`         
              // parent.page_iframe.add("表", url)
              window.open(url)
              // return;
            },
          })
        } else if (d == "1") {
          AddNewWs(gsdm)
        }
      },
      error: function (err) {
        ret = "-2";
      }
    });
    if (ret == "1") {
      return;
    }
  }
}

function entercode() {
  document.onkeydown= function(ev){
    ev.stopPropagation();
    if (ev.keyCode == "13") {
      $(".e_message button[name='confirm']").click()
      // name="confirm"
    }
  }
}
// 新增存放
function xzcf(list) {
  objNew.push({
    ...list, ...{
      // id:`t${lNum++}`,
      // two:'1',
      // WslbItem_nrUrl:"as_blid=" + params["as_blid"] + "&as_gsdm=" + WRT_config.NewWsHtml.GSDM + "&as_zyid=" + params["as_zyid"] + "&as_wsid=0&as_wslx=0&as_tmpid=t1&tmpid=0.44261011765709646"
    }})
  // //console.log("新增存放",objNew)
}
// 文书类型上级医师签名判断
function wslxName() {
  //4、病程记录/入院记录初始化e_init
  WRT_e.api.blwslist.getBlwslist({
    params: {
      "as_blid": params["as_blid"],
      "as_zyid": params["as_zyid"],
      "as_lx": params["as_lx"],
      "as_zkid": params["as_zkid"],
      "as_zkdm": params["as_zkdm"],
      "as_baseurl": "http://localhost:8080/"
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.BrwsCsh = data.Result
        // //console.log("4、病程记录/入院记录初始化e_init(BrwsCsh):", WRT_config.BrwsCsh)
      }
    }
  })
  obj2 = []
  $('.list_inner .left_listNr').each((idx, ev) => {
    obj2.push({
      listWslx: $(".left_listNr").attr("wslx")
    })
  })
  $(".list_inner_W").html(
    new leftlist_sx().init({
      data: {
        dataRList: WRT_config.BrwsList,
        dataWSLXS: WRT_config.BrwsCsh.WSLXS
      }
    }).render().$el
  )
  $(".list_title_W").html(
    new lefthead_sx().init({
      data: {
        dataRList: WRT_config.BrwsList,
        dataWSLXS: WRT_config.BrwsCsh.WSLXS
      }
    }).render().$el
  )
  // //console.log("文书类型", obj2)
}
// 子页面高度/宽度
function load(id) {
  var popUp = $("#simpleMode").is(':checked')
  if (popUp == false) {
    var nBheight = $(`#if_${id}`).contents().find("html").height()
    var dyHeight = $(`#if_${id}`).contents().find("embed").height()
    var ifBwidth= $(`#if_${id}`).contents().find("table").width()
    var nBwidth =  $(`#if_${id}`).contents().find("html").width()
    // var nBwidth = $(`#if_${id}`).contents().find("tbody").width()
    // //console.log(nBheight)
    // //console.log(dyHeight)
    
    // //console.log(nBwidth)
    // //console.log(ifBwidth)
    // //console.log($(".panel-body").width())
     $(`#if_${id}`).contents().find('table').css({
      'position':'relative',
      'padding':'0',
      'margin':'0',
      'width':'100%',
    });

    if($(`#type_${id}`).attr("class") == "panel-collapse collapse in"){
      if (dyHeight == null) {
        if ($(".panel-body").width()<1200) {
          // $(`#if_${id}`).css({
          //   "height":"auto",
          //   "width":"auto"
          // })
          $(`#if_${id}`).css({
            "height":`${nBheight}px`,
            "width":`${ifBwidth}px`
          })
        } else {
          // $(`#if_${id}`).css({
          //   "height":"auto",
          //   // "width":"auto"
          // })
          $(`#if_${id}`).css({
            // "height":`${nBheight}px`,
            "height":`630px`,
            // "width":`${ifBwidth}px`
          })
        }
      } else {
        $(`#if_${id}`).css({
          "height":"700px",
        })
      }
    }
  } else {
    $(".izi_modal_content").css({
      "padding":"0px"
    })
    if ($("#blwsList").height() >= 798) {
      // $(`#if_${id}`).css({
      //   "height":"auto"
      // })
      $(`.tc #if_${id}`).css({
        "height":"700px",
      })
    } else if($("#blwsList").height()>= 483 && $("#blwsList").height() <798){
      // $(`#if_${id}`).css({
      //   "height":"auto"
      // })
      $(`.tc #if_${id}`).css({
        "height":"400px",
      })
      // //console.log($(`#if_${id}`).height())
    } else if($("#blwsList").height() <=482){
      // $(`#if_${id}`).css({
      //   "height":"auto"
      // })
      $(`.tc #if_${id}`).css({
        "height":"300px",
      })
      //console.log($(`.tc #if_${id}`).height())
    }
    // //console.log($("#blwsList").height())
    // //console.log($(`#if_${id}`).height())
    
  }
}
// function docDataHtml(addVal,b_wsid,iframe_Id,WslbItem_nrUrl){
//   let html=`<div class="tc">
//     <div>
//       <iframe class="dynr1" id="if_${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" value="${addVal}" name="${addVal}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?` + WslbItem_nrUrl + `" rameborder="0" width="100%" ></iframe>
//     </div>
//     <div>
//       <button class="saveclick e_btn" id="save${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" wsid="${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}" onclick="popUp_button.save1('${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}')" type="button">保存</button>
//       <button class="delclick e_btn" id="del${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" wsid="${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}" onclick="popUp_button.delete_btn('${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}')" type="button">删除</button>
//       <button class="imgclick e_btn" id="img${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" wsid="${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}" onclick="popUp_button.imggl('${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}')" type="button">图片管理</button>
//       <button class="closetc e_btn" id="close${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" wsid="${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}" gsdm="${b_wsid.GSDM}" onclick="popUp_button.close1('${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}')" type="button" name="close">关闭</button>
//     </div>
//   </div>`
//   // load((iframe_Id==null)?`${b_wsid.id}`:`${wsid}`)
//   return html
  
// }
// 新增弹出窗方法
// function newPopUp(json,wsid,iframe_Id) {
//   // iframe_Id=null;
//   var addID = $("#xz_nr option:checked").attr("class")
//   var addLX = $("#xz_nr option:checked").attr("name")
//   var addVal = $("#xz_nr option:checked").val()
//   var b_wsidList = [...WRT_config.BrwsList.dvm_wslist, ...objNew]
//   var b_wsid = {id:`${json.id}`,WSID:0}
//   // //console.log("iframe_Id",iframe_Id)
//   let WslbItem_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + addID + "&as_zyid=" + params["as_zyid"] + "&as_wsid="+ `${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}` +"&as_wslx=" + addLX + "&as_tmpid=t1&tmpid=0.44261011765709646"
//   // //console.log("WslbItem_nrUrl",WslbItem_nrUrl)
//   // temp+=docDataHtml()
//   //         temp+="</div>"
//   WRT_e.ui.model({
//     id:'if_zlyzmb',
//     // title: '治疗医嘱模板',
//     width: "945px",
//     iframeURL:`${WRT_config.server}/zyblws/blwsdetail.aspx?${WslbItem_nrUrl}`,
//     iframeHeight:'550px',
//     iframe: true,
    
//     // id:"simpleModeModalnew",
//     // overlayClose: false, //点击遮罩关闭框
//     // width: 1332,
//     // iframe: false,
//     // iframeURL:`${WRT_config.server}/zyblws/blwsdetail.aspx?${WslbItem_nrUrl}`,
//     // iframeURL:`${WRT_config.server}/zyblws/blwsdetail.aspx?` + WslbItem_nrUrl + `,
//     // content:docDataHtml(addVal,b_wsid,iframe_Id,WslbItem_nrUrl)
//   })
//   // load((iframe_Id==null)?`${b_wsid.id}`:`${wsid}`)
// }



// 新增弹出窗方法
function newPopUp(json,wsid,iframe_Id) {
  // iframe_Id=null;
  var addID = $("#xz_nr option:checked").attr("class")
  var addLX = $("#xz_nr option:checked").attr("name")
  var addVal = $("#xz_nr option:checked").val()
  var b_wsidList = [...WRT_config.BrwsList.dvm_wslist, ...objNew]
  var b_wsid = {id:`${json.id}`,WSID:0}
  // //console.log("iframe_Id",iframe_Id)
  let WslbItem_nrUrl = "as_blid=" + params["as_blid"] + "&as_gsdm=" + addID + "&as_zyid=" + params["as_zyid"] + "&as_wsid="+ `${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}` +"&as_wslx=" + addLX + "&as_tmpid=t1&tmpid=0.44261011765709646"
  // //console.log("WslbItem_nrUrl",WslbItem_nrUrl)
  
  // 弹出窗
  let model = $("#simpleModeModalnew").iziModal({ //初始化modal
    overlayClose: false, //点击遮罩关闭框
    width: 1332,
  })
  //设置自定义内容
  model.iziModal('setContent',
    `<div class="tc">
      <div>
        <iframe class="dynr1" id="if_${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" value="${addVal}" name="${addVal}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?` + WslbItem_nrUrl + `" rameborder="0" width="100%" ></iframe>
      </div>
      <div>
        <button class="saveclick e_btn" id="save${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" wsid="${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}" onclick="popUp_button.save1('${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}')" type="button">保存</button>
        <button class="delclick e_btn" id="del${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" wsid="${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}" onclick="popUp_button.delete_btn('${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}')" type="button">删除</button>
        <button class="imgclick e_btn" id="img${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" wsid="${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}" onclick="popUp_button.imggl('${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}')" type="button">图片管理</button>
        <button class="closetc e_btn" id="close${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}" wsid="${(iframe_Id==null)?`${b_wsid.WSID}`:`${wsid}`}" gsdm="${b_wsid.GSDM}" onclick="popUp_button.close1('${(iframe_Id==null)?`${b_wsid.id}`:`${wsid}`}')" type="button" name="close">关闭</button>
      </div>
    </div>`);
    //onload="load(${(iframe_Id==null)?`'${b_wsid.id}'`:`${wsid}`})"
    load((iframe_Id==null)?`${b_wsid.id}`:`${wsid}`)
  //打开
  model.iziModal('open')
}

//病人质控信息（用于保存回掉内）
// function openQc(){
//   //var j_url = "zyblws/brqc.aspx?as_blid=" + params["as_blid"] + "&as_tmpid=" + Math.random(); 
//   var j_url = "";
//   OpenTab(j_url, "质控信息");
// }
// function docEditHtml(tcurl,popurl){
//   let html=`<div class="tc">
//   <div>
//     <iframe class="dynr" id="if_${tcurl.WSID}" name="${tcurl.GSMC}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?` + popurl + `" rameborder="0" width="100%"></iframe>
//   </div>
//   <div>
//     <a class="saveclick e_btn" id="save${tcurl.WSID}" name="${tcurl.WSID}" onclick="popUp_button.save1('${tcurl.WSID}')" type="button">保存</a>
//     <a class="delclick e_btn" id="del${tcurl.WSID}" name="${tcurl.WSID}" onclick="popUp_button.delete_btn('${tcurl.WSID}')" type="button">删除</a>
//     <a class="imgclick e_btn" id="img${tcurl.WSID}" name="${tcurl.WSID}" onclick="popUp_button.imggl('${tcurl.WSID}')" type="button">图片管理</a>
//     <a class="closetc e_btn" name="${tcurl.WSID}" onclick="popUp_button.close1('${tcurl.WSID}')" type="button" name="close">关闭</a>
//   </div>
// </div>`
// // load(tcurl.WSID)
// return html
// }
function checkQC() {
  WRT_e.api.ehrSz.getPaQcCnt({
    params: {al_blid:params["as_blid"],},
    success(data) {
      if (data.Code == 1) {
      }
    }
  })
  // setTimeout("checkQC()", 300000);
}
// 保存回调函数
function save_callback(wsid, jlsj, gsdm, wslx, gsmc, tmpid, sscx) {
  // //console.log("文书id:",wsid,"记录时间：",jlsj,"gsdm：", gsdm, "文书类型：",wslx,"gsmc:", gsmc,"临时id：",tmpid, "sscx:",sscx)
  WRT_e.ui.hint({
    type: 'success',
    msg: '保存成功'
  })
  WRT_e.api.blwslist.getBrWssByLx({
    params: {
      "as_blid": params["as_blid"],
      "as_zyid": params["as_zyid"],
      "as_lx": params["as_lx"]==0?`01`:`02`,
      "as_zkid": params["as_zkid"],
      "as_zkdm": params["as_zkdm"],
      "as_baseurl": "http://localhost:8080/",
      "as_rjss": WRT_config.BrwsCsh.BRXX.RJSS
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.BrwsList = data.Result
        var data1={
          dataList:data.Result.dvm_wslist.find(item=>{
            item.urlBlws = "as_blid=" + WRT_config.BrwsCsh.BRXX.BLID + "&as_gsdm=" + item.GSDM + "&as_zyid=" + WRT_config.BrwsCsh.BRXX.ZYID + "&as_wsid=" + item.WSID + "&as_wslx=" + item.WSLX + "&as_tmpid=-192&tmpid=0.054136330412483336"
            return item.WSID==wsid;
          })
        };
        // //console.log(data1)
        // var data1={
        //   dataList:data.Result.dvm_wslist.find(item=>{
        //     return item.WSID==wsid;
        //   })
        // };
        var view =noteViews.find(el=>{
          if(el.data.dataList.WSID!=0){
            return el.data.dataList.WSID==wsid;
          }
          else{
            if (lsSaveId.indexOf('t')!=-1 && el.data.dataList.id==lsSaveId) { // 说明是新增
              el.data.dataList.WSID=wsid
              el.data.dataList.id==tmpid;
              return el
            }
            //  else{
            //   el.data.dataList.WSID=wsid
            //   el.data.dataList.id==tmpid;
            //   return el
            // }
            // return  el.data.dataList.id==tmpid;
          }
        })
        // //console.log(view)
        if (view==undefined) {
          view = new progress_noteContent().init()
          $(".zd").append(view.$el)
          noteViews.push(view);
        }
        view.data=data1;
        view.render();
        // if (tmpid.indexOf("t")>-1) {
        //   // //console.log(11111)
        //   view.render();
        // }
        var popUp = $("#simpleMode").is(':checked')
        if (popUp == true) {
          if ($("#simpleModeModalnew").hasClass(`iziModal.isAttached.hasScroll`) == false && tmpid =="t1") {
            if (iframe_Id == null) {
              var iframeId = $("#simpleModeModalnew > #if_"+tmpid)
              iframeId.id= wsid
              iframe_Id=iframeId.id
              $(".tc > #if_"+tmpid).attr("id","if_"+iframe_Id)
              newPopUp(wsid,wsid,iframe_Id)
              // view.render();
            }
          }
        }
        if (((WRT_config.BrwsList.dvm_wslist).find(e=>e.WSLX=="11")!=undefined)&&((WRT_config.BrwsList.dvm_wslist).find(e=>e.WSLX=="11").WSLX=="11")) {
          j_sfsc="1"
          $("#xz_nr option:nth-child(2)").prop("selected", 'selected')
        } else {
          j_sfsc="0"
          $("#xz_nr option:nth-child(1)").prop("selected", 'selected')
        }
        $(".list_inner_W").html(
          new leftlist_sx().init({
            data: {
              dataRList: WRT_config.BrwsList,
              dataWSLXS: WRT_config.BrwsCsh.WSLXS
            }
          }).render().$el
        )
        $(".list_title_W").html(
          new lefthead_sx().init({
            data: {
              dataRList: WRT_config.BrwsList,
              dataWSLXS: WRT_config.BrwsCsh.WSLXS
            }
          }).render().$el
        )
        checkQC(); 
      }
    }
  })
}
// 删除回调函数
function del_callback(wsid, tmpid, sscx, wslx) {
  // //console.log(wsid, tmpid, sscx, wslx)
  var popUp = $("#simpleMode").is(':checked')
  WRT_e.ui.hint({
    type: 'success',
    msg: '删除成功'
  })
  WRT_e.api.blwslist.getBrWssByLx({
    params: {
      "as_blid": params["as_blid"],
      "as_zyid": params["as_zyid"],
      "as_lx": params["as_lx"]==0?`01`:`02`,
      "as_zkid": params["as_zkid"],
      "as_zkdm": params["as_zkdm"],
      "as_baseurl": "http://localhost:8080/",
      "as_rjss": WRT_config.BrwsCsh.BRXX.RJSS
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.BrwsList = data.Result
        noteViews=noteViews.filter(item=>{
          if(item.data.dataList.WSID==0){
            item.data.dataList.id==tmpid&&item.$el.remove();
          }
          else if(item.data.dataList.WSID==wsid)item.$el.remove();
          return item.data.dataList.WSID!=wsid
        })
        // //console.log($("div[data-objid$=`${id}`]"))
        if ((noteViews.find(e=>e.WSLX=="11")!=undefined)&&(noteViews.find(e=>e.WSLX=="11").WSLX=="11")) {
          j_sfsc="1"
          $("#xz_nr option:nth-child(2)").prop("selected", 'selected')
        } else {
          j_sfsc="0"
          $("#xz_nr option:nth-child(1)").prop("selected", 'selected')
        }
        if (popUp == false) {
          //$("#panel"+wsid).remove()
          $(".list_inner_W").html(
            new leftlist_sx().init({
              data: {
                dataRList: WRT_config.BrwsList,
                dataWSLXS: WRT_config.BrwsCsh.WSLXS
              }
            }).render().$el
          )
          $(".list_title_W").html(
            new lefthead_sx().init({
              data: {
                dataRList: WRT_config.BrwsList,
                dataWSLXS: WRT_config.BrwsCsh.WSLXS
              }
            }).render().$el
          )
        } else {
          //$("#panel"+wsid).remove()
          $('#simpleModeModalnew').iziModal("destroy")
          $("#simpleModeModal").iziModal("destroy")
          $(".list_inner_W").html(
            new leftlist_sx().init({
              data: {
                dataRList: WRT_config.BrwsList,
                dataWSLXS: WRT_config.BrwsCsh.WSLXS
              }
            }).render().$el
          )
          $(".list_title_W").html(
            new lefthead_sx().init({
              data: {
                dataRList: WRT_config.BrwsList,
                dataWSLXS: WRT_config.BrwsCsh.WSLXS
              }
            }).render().$el
          )
        }
        // $(".list_inner").html(
        //   new leftlist_sx().init({
        //     data: {
        //       dataRList: WRT_config.BrwsList,
        //       dataWSLXS: WRT_config.BrwsCsh.WSLXS
        //     }
        //   }).render().$el
        // )
      }
    }
  })
}
// 新增条件
function CheckAddNew(wslx, gsdm) {
  var j_ok;

  // 获取初始化病例状态<判断病例是否封存>
  var j_blzt = WRT_config.BrwsCsh.BRXX.BLZT
  if (j_blzt == "1") {
    // 新增文书时对于已封存病历有2份文书是允许新增的
    if(params["as_lx"] == "1"){
      //归档病理辅助检查记录
      if(wslx == "1Z"){
        // 13、获取病人辅助状态（出院时间）e_CheckFjzt(<返回参数<在Reslut中> 0=未出院,1=出院天数小于30天,2=出院超过30天>不清楚实际显示的是页面那部分需要的内容
        WRT_e.api.blwslist.GetCheckFjzt({
          params: {
            "as_blid": params["as_blid"]
          },
          success(data) {
            if (data.Code == 1) {
              WRT_config.CheckFjzt = data
              var j_zt = WRT_config.CheckFjzt.Result
              // //console.log("13、获取病人辅助状态（出院时间）e_CheckFjzt",WRT_config.CheckFjzt)
              if (j_zt == "1"){
                // 目前还没写
                // GetCheckGsdm
                // 10 判断文书是否可以多份
                WRT_e.api.blwslist.GetCheckGsdm({
                  params: {
                    "as_blid": params["as_blid"],
                    "as_gsdm": gsdm
                  },
                  success(data) {
                    if (data.Code == 1) {
                      WRT_config.CheckGsdm = data.Result
                      if (WRT_config.CheckGsdm == "0") {
                        AddNewWs(gsdm)
                      } else {
                        WRT_e.ui.message({
                          title: '提示信息',
                          content: `该记录一份病历中只允许存在一份,如页面上未显示,请刷新！`,
                          onOk() {
                          }
                        })
                      }
                    }
                  }
                })
              }
              if (j_zt == "2"){
                WRT_e.ui.message({
                  title: '提示信息',
                  content: `病人出院已超过30天，无法新增辅检记录`,
                  onOk() {
                  }
                })
              }
              if (j_zt == "0"){
                WRT_e.ui.message({
                  title: '提示信息',
                  content: `病人未出院，无法新增辅检记录`,
                  onOk() {
                  }
                })
              }
              return;
            }
          }
        })
      }else if(wslx == "1I"){
        AddNewWs(gsdm)
      }else{
        WRT_e.ui.message({
          type:'warning',
          title: '提示信息',
          content: `已封存病历不能进行该操作!`,
          onOk() {
          }
        })
        return
      }
    } else {
      WRT_e.ui.message({
        type:'warning',
        title: '提示信息',
        content: `已封存病历不能进行该操作!`,
        onOk() {
        }
      })
      return
    }
  } else {
    var zthq = $.parseJSON(WRT_config.BrMainEinit)// var xxcf = (WRT_config.BrMainEinit).split(',')
    var j_mr_right = zthq.qtqx
    console.log(zthq,zthq.qtqx) // 打开显示说j_mr_right这个蚕食是undefined
    if (j_mr_right != undefined && j_mr_right.substr(1, 1) != "1") {
      if ((wslx == "18" || wslx == "1A") && j_mr_right.substr(3, 1) == "1")   //会诊医师允许写有创操作记录和抢救记录
          j_ok = true;
      if (wslx == "19" && j_mr_right.substr(4, 1) == "1")  //手术权限的医师可以写术后首程
          j_ok = true;
      if (j_ok == false) {
        WRT_e.ui.message({
          type:'warning',
          title: '提示信息',
          content: `您没有新增该类文书的权限!`,
          onOk() {
            // return;
          }
        })
        return;
      }
    }
    var ret = "0";
    // wslx == "11"首次病程记录
    if (wslx == "11") {
      // var z_mc = (WRT_config.BrwsList.dvm_wslist)
      // var z_left=z_mc.find(e=>e.GSDM == $("#xz_nr option:checked").attr("class"))
      // if ((z_left != undefined)&&(wslx == z_left.GSMC)) {
      //   j_sfsc = "1";
      // } else {
      //   j_sfsc = "1";
      // }
      // 15、获取病人神经功能缺损评分表(NIHSS)评分情况e_ChkNIHSS(<返回参数 1=有未完成评分 其他=无或者评分已完成>不知道用在哪里)
      WRT_e.api.blwslist.GetChkNIHSS({
        params: {
          "as_blid": params["as_blid"],
          "lb": "1"
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.ChkNIHSS = data.Result
            ret = WRT_config.ChkNIHSS
            // //console.log("15、获取病人神经功能缺损评分表(NIHSS)评分情况e_ChkNIHSS",WRT_config.ChkNIHSS)
            if( WRT_config.ChkNIHSS == "1") {
              WRT_e.ui.message({
                title: '提示',
                content: `完成美国国立卫生院神经功能缺损评分表(NIHSS)评分后才可书写首次病程记录`,
                onOk() {
                  let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${params["as_blid"]}&as_pfid=17&as_pfzhid=0`
                  // $(".iziModal-overlay").css("display","none")
                  parent.page_iframe.add("NIHSS评分表", url)
                  // return;
                }
              })
            }
          }
        }
      })
      if (ret == "1") {
        return;
      }
      // 14、获取病人改良Rankin量表(mRS)评分情况<请求参数不同>e_CheckFjzt(<返回参数 1=有未完成评分 其他=无或者评分已完成>其中请求参数Lb找不到是从那个接口的返回值中获取的，由于有0或1不确定是否写死)
      WRT_e.api.blwslist.ChkmRs({
        params: {
          "as_blid": params["as_blid"],
          "lb": "1"
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.GetCheckFjztmRS = data.Result
            ret = WRT_config.GetCheckFjztmRS 
            // //console.log("14、获取病人改良Rankin量表(mRS)评分情况<请求参数不同>e_CheckFjzt",WRT_config.GetCheckFjztmRS)
            if(WRT_config.GetCheckFjztmRS =="1") {
              WRT_e.ui.message({
                title: '提示',
                content: `完成改良Rankin量表(mRS)评分后才可书写首次病程记录`,
                onOk() {
                  let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${params["as_blid"]}&as_pfid=72&as_pfzhid=0`
                  // $(".iziModal-overlay").css("display","none")
                  parent.page_iframe.add("mRs评分表", url)
                  // return;
                }
              })
            }
          }
        }
      })
      if (ret == "1") {
        return;
      }
      
      // 16、获取病人妊娠期及产褥期静脉血栓血塞证(VTE)的危险因素评分情况e_CkVte
      // (<返回参数 1=有未完成评分 其他=无或者评分已完成>其中请求参数Lb找不到是从那个接口的返回值中获取的，由于有0或1不确定是否写死)
      // console.log(parent.WRT_config.BrMainLeft.BRNL);
      if (parseFloat(parent.WRT_config.BrMainLeft.BRNL)>14) {
        WRT_e.api.blwslist.GetCkVte({
          params: {
            "as_blid": params["as_blid"],
            "lb": "1"
          },
          success(data) {
            if (data.Code == 1) {
              WRT_config.CkVt = data.Result
              ret = WRT_config.CkVt
              // //console.log("16、获取病人妊娠期及产褥期静脉血栓血塞证(VTE)的危险因素评分情况e_CkVte",WRT_config.CkVt)
              if(WRT_config.CkVt == "1") {
                let urls=''
                urls="http://172.16.202.14:5321/#/pc/huashan/jiankong/queren/" + WRT_config.BrwsCsh.YHXX.YHZH + "/" + WRT_config.BrwsCsh.BRXX.ZYH + "/" + params["as_blid"] + "/1/" + "孕早期";
                WRT_e.ui.message({
                  title: '提示',
                  content: `完成妊娠期及产褥期静脉血栓血塞证(VTE)的危险因素评分后才可书写首次病程记录`,
                  onOk() {
                      window.open(urls)
                    // return;
                  },
                })
              }
            }
          }
        })
        if (ret == "1") {
          return;
        }
      }
      WRT_e.api.blwslist.CheckPfb_pfid_phq({
        params: {
          "al_blid": params["as_blid"],
          "al_pfbid": "148"
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.CheckPfb = data.Result
            ret = WRT_config.CheckPfb
            if(WRT_config.CheckPfb == "1") {
              WRT_e.ui.message({
                title: '提示',
                content: `完成PHQ-4量表后才可书写首次病程记录`,
                onOk() {
                  let url = WRT_config.server + `/pfbgl/pfbym.aspx?as_blid=${params["as_blid"]}&as_pfid=148&as_pfzhid=0`         
                  parent.page_iframe.add("PHQ-4量表", url)
                  // return;
                },
              })
            }else if(WRT_config.CheckPfb == "2"){
              WRT_e.ui.message({
                title: '提示',
                content: `完成PHQ-9量表后才可书写首次病程记录`,
                onOk() {
                  let url = WRT_config.server + `/pfbgl/pfbym.aspx?as_blid=${params["as_blid"]}&as_pfid=274&as_pfzhid=0`         
                  parent.page_iframe.add("PHQ-9量表", url)
                  // return;
                },
              })
            }
          }
        }
      })
      if (ret == "1") {
        return;
      }
      if (WRT_config.BrwsCsh.VTECS == "1"||WRT_config.BrwsCsh.VTECS == "2"){
        VTECS(wslx,gsdm)
      } else {
        // (√)17.1、获取内或外科住院患者静脉血栓栓塞症的风险评估(VTE)评分情况e_CkNWkVte<在新增中使用>
        // "lb": "1"没有也可以
        WRT_e.api.blwslist.GetCkNWkVte({
          params: {
            "as_blid": params["as_blid"],
            "lb": "1" // 无论0或1结果的Reslut都是1<但以胡秀花为例网站是不能进行新增的但这里可以>
          },
          success(data) {
            if (data.Code == 1) {
              WRT_config.CkNWkVte = data.Result
              ret = WRT_config.CkNWkVte
              // //console.log("17.1、获取内或外科住院患者静脉血栓栓塞症的风险评估(VTE)评分情况e_CkNWkVte",WRT_config.CkNWkVte)
              if(WRT_config.CkNWkVte =="1") {
                WRT_e.ui.message({
                  title: '提示',
                  content: `完成内或外科住院患者静脉血栓栓塞症的风险评估(VTE)后才可书写首次病程记录`,
                  onOk() {
                    let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${params["as_blid"]}`
                    // $(".iziModal-overlay").css("display","none")
                    parent.page_iframe.add("评分表", url)
                    // return;
                  },
                })
              }
            }
          }
        })
        if (ret == "1") {
          return;
        }
      }
      
      // 不确定判断条件加了没 在医生住院单入院第一诊断“频内出血”(ICD 162.900)，在新增首次病程时，病人病区入院第五天时(第五天，打开病历时判断是否已有该评分表，如无，则弹出提示，如有，则不弹出)，新增出院记录时，进行强制NIHSS评分(pfid=17)只限制在神经内科
      
    }
    // wslx == "11"和15
    if (wslx == "15") {
      if (WRT_config.BrwsCsh.VTECS == "1"||WRT_config.BrwsCsh.VTECS == "2"){
        VTECS(wslx,gsdm)
      }
    }
    // wslx == "13"上级医师查房记录
    if(wslx == "13") {
      // 15、获取病人神经功能缺损评分表(NIHSS)评分情况e_ChkNIHSS(<返回参数 1=有未完成评分 其他=无或者评分已完成>不知道用在哪里)
      WRT_e.api.blwslist.GetChkNIHSS({
        params: {
          "as_blid": params["as_blid"],
          "lb": "2"
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.ChkNIHSS = data.Result
            ret = WRT_config.ChkNIHSS
            //console.log("15、获取病人神经功能缺损评分表(NIHSS)评分情况e_ChkNIHSS",WRT_config.ChkNIHSS,data)
            if( WRT_config.ChkNIHSS == "1") {
              WRT_e.ui.message({
                title: '提示',
                content: `该患者住院第五天需要完成美国国立卫生院神经功能缺损评分表(NIHSS)评分`,
                onOk() {
                  let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${params["as_blid"]}&as_pfid=17&as_pfzhid=0"`         
                  // $(".iziModal-overlay").css("display","none")
                  parent.page_iframe.add("NIHSS评分表", url)
                  // return;
                }
              })
            }
          }
        }
      })
      if (ret == "1") {
        return;
      }
    }
    // wslx == "1E"出院记录
    if(wslx == "1E") {
      // 14、获取病人改良Rankin量表(mRS)评分情况<请求参数不同>e_CheckFjzt(<返回参数 1=有未完成评分 其他=无或者评分已完成>其中请求参数Lb找不到是从那个接口的返回值中获取的，由于有0或1不确定是否写死)
      WRT_e.api.blwslist.GetCheckFjztmRS({
        params: {
          "as_blid": params["as_blid"],
          "lb": "0"
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.GetCheckFjztmRS = data.Result
            ret = WRT_config.GetCheckFjztmRS 
            // //console.log("14、获取病人改良Rankin量表(mRS)评分情况<请求参数不同>e_CheckFjzt",WRT_config.GetCheckFjztmRS)
            if(WRT_config.GetCheckFjztmRS =="1") {
              WRT_e.ui.message({
                title: '提示',
                content: `完成改良Rankin量表(mRS)评分后才可书写首次病程记录`,
                onOk() {
                  let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${params["as_blid"]}&as_pfid=72&as_pfzhid=0`         
                  // $(".iziModal-overlay").css("display","none")
                  parent.page_iframe.add("mRs评分表", url)
                  // return;
                }
              })
            }
          }
        }
      })
      if (ret == "1") {
        return;
      }
      // 17.2、ICU患者填写出院记录需填ICU质量控制登记表e_ICUChec(<返回参数 1=有未完成评分 其他=无或者评分已完成>不知道用在哪里)
      WRT_e.api.blwslist.GetICUCheck({
        params: {
          "as_blid": params["as_blid"]
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.ICUCheck = data.Result
            ret = WRT_config.ICUCheck
            // //console.log("17.2、ICU患者填写出院记录需填ICU质量控制登记表e_ICUChec",WRT_config.ICUCheck)
            if(WRT_config.ICUCheck == "1") {
              WRT_e.ui.message({
                title: '提示',
                content: `ICU患者填写出院记录需填ICU质量控制登记表！`,
                onOk() {
                  let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${params["as_blid"]}&as_pfid=103&as_pfzhid=0`         
                  // $(".iziModal-overlay").css("display","none")
                  parent.page_iframe.add("ICU质量控制登记表", url)
                  // return;
                }
              })
            }
          }
        }
      })
      if (ret == "1") {
        return;
      }
    }
    // wslx == "14"转科记录
    if(wslx == "14" || wslx == "08") {
      // 17.2、ICU患者填写出院记录需填ICU质量控制登记表e_ICUChec(<返回参数 1=有未完成评分 其他=无或者评分已完成>不知道用在哪里)
      WRT_e.api.blwslist.GetICUCheck({
        params: {
          "as_blid": params["as_blid"]
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.ICUCheck = data.Result
            ret = WRT_config.ICUCheck
            // //console.log("17.2、ICU患者填写出院记录需填ICU质量控制登记表e_ICUChec",WRT_config.ICUCheck)
            if(WRT_config.ICUCheck == "1") {
              WRT_e.ui.message({
                title: '提示',
                content: `ICU患者填写出院记录需填ICU质量控制登记表！`,
                onOk() {
                  let url = WRT_config.server + `/pfbgl/pfblist.aspx?as_blid=${params["as_blid"]}&as_pfid=103&as_pfzhid=0`         
                  // $(".iziModal-overlay").css("display","none")
                  parent.page_iframe.add("ICU质量控制登记表", url)
                  // return;
                }
              })
            }
          }
        }
      })
      if (ret == "1") {
        return;
      }
    }
    if (WRT_config.BrwsCsh.VTECS == "1"||WRT_config.BrwsCsh.VTECS == "2") {
      if (wslx != "15" && wslx != "11") {
        AddNewWs(gsdm)
      }
      // if (wslx != "15") {
      //   AddNewWs(gsdm)
      // }
      // else if(wslx != "11") {
      //   AddNewWs(gsdm)
      // }
    } else {
      AddNewWs(gsdm)
    }
    
    
  }
}
function AddNewWs(gsdm) {
  // //console.log(gsdm)
   // 8、（√）新增文书_GetNewWsHtml（不清楚属于哪里a.GSDM的值获取方式和位置不确定是否正确）
   WRT_e.api.blwslist.getNewWsHtml({
    params: {
      "as_gsdm": $("#xz_nr option:checked").attr("class"),
      "as_blid": params["as_blid"],
      "as_zyid": params["as_zyid"],
      "as_tmpid": "t2" // 文书临时id什么事t1,下面是t2
    },
    success(data) {
      if (data.Code == 1) {
        WRT_config.NewWsHtml = data.Result
        //console.log("8、新增文书_GetNewWsHtml", WRT_config.NewWsHtml)
        var addVal = $("#xz_nr option:checked").val()
        // var z_mc = (WRT_config.BrwsList.dvm_wslist).find(e =>e.GSMC == "死亡记录")
        var z_mc = (WRT_config.BrwsList.dvm_wslist)
        var z_left=z_mc.find(e=>e.GSDM == $("#xz_nr option:checked").attr("class"))
        // //console.log(z_mc.find(e=>e.GSDM == $("#xz_nr option:checked").attr("class")))
        if (WRT_config.NewWsHtml == "-1") {
          WRT_e.ui.message({
            title: '提示',
            content: `请先删除出院记录`,
            onOk() {
            }
          })
        } else {
          if ((addVal == "首次病程记录")&& (z_left != undefined)&&(addVal == z_left.GSMC)) {
            j_sfsc = "1"
          } 
          // else if ((addVal == "死亡记录") && (z_left != undefined)&&(addVal == z_left.GSMC)) {
          //   WRT_e.ui.message({
          //     title: '提示信息',
          //     content: `该记录,一份文书中只允许存在一份,如页面上未显示,请刷新！`,
          //     onOk() {
          //     }
          //   })
          // } 
          // else if(addVal == "上级医师查房记录"){} else if(addVal == "出院记录"){} else if(addVal == "转科记录"){}
          // else {
          j_sfsc = "0";
          var addVal = $("#xz_nr option:checked").val()
          WRT_e.api.blwslist.getWsDetail({
            params: {
              "as_gsdm": gsdm,
              "as_blid": params["as_blid"],
              "as_zyid": params["as_zyid"],
              "as_wsid": 0,
              "as_url": "http://localhost:8080/"
            },
            success(data) {
              if (data.Code == 1) {
                WRT_config.WsDetail = data.Result
                // //console.log("9、获取文书详情e_GetWsDetail", WRT_config.WsDetail)
                let json = WRT_config.NewWsHtml
                  json.id = `t${lNum++}`,
                  json.two ='1',
                  json. WslbItem_nrUrl="as_blid=" + params["as_blid"] + "&as_gsdm=" + WRT_config.NewWsHtml.GSDM + "&as_zyid=" + params["as_zyid"] + "&as_wsid=0&as_wslx=0&as_tmpid=t1&tmpid=0.44261011765709646"
                  json.PREVIEW_HTML=WRT_config.WsDetail
                  // return item
                // //console.log(json)
                // 弹窗
                var popUp = $("#simpleMode").is(':checked')
                if (popUp == false) {
                  // xzcf(gsdm)
                  var content=new progress_noteContent().init();
                      noteViews.push(content);
                      $(".zd").append(content.$el);
                      // 右
                      content.init({
                        data:{
                          dataList:json,
                          data1: WRT_config.WsDetail,
                          // dataAdd: WRT_config.NewWsHtml
                        }
                      }).render();
                      xzcf(json)
                      let id = '#panel_' + json.id
                      $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(id).offset().top-heightDW()},300);
                      // $('.zd').scrollTop($('.zd').scrollTop()+$(id).offset().top-heightDW());
                      // document.querySelector(".zd "+id).scrollIntoView(true);
                } else {
                  // //console.log(json)
                  newPopUp(json,WRT_config.NewWsHtml.WSID,iframe_Id)
                }
              }
            }
          })
        }
      }else if(data.Code==-1){
        WRT_e.ui.message({
          title: '提示',
          content: `请先删除出院记录`,
          onOk() {
          }
        })
      }
    }
  })
}
// 滚动定位
function heightDW(){
  var bool=((WRT_config.BrwsCsh.DV_WJZ === undefined) || (WRT_config.BrwsCsh.DV_WJZ === ''));
    var height=0;
    // var zheight = $(".right_top").height();
    var zheight =  $(".right_top_title").height()  // 标题40左右
    var sheight = $(".blwslist_info").height() // 带刷新按钮的内容
    ////console.log(zheight)
    ////console.log(sheight)
    ////console.log( $(".right_top_content").css("display"))
    // //console.log($(".right_top_title").height())
    // //console.log((WRT_config.WjzLst.length/2+WRT_config.WjzLst.length%2))
    //危机记录高度（条数*单行高度+title+下边框）
    // if(!bool)height+=WRT_config.WjzLst.length*26.5+36+5+34;
    // 危机记录高度+title+title边框+html上边距
    var wCountLong=0
    var wCountShort=0
    if(!bool){
      // height+=zheight+sheight+3+7;
      // height+=zheight+sheight+(WRT_config.WjzLst.length/2+WRT_config.WjzLst.length%2)*34
      WRT_config.WjzLst.forEach(e => {
        var JCJGWidth = (e.JCJG == null? `0`:`${e.JCJG.length}`)
        var CZBZWidth = (e.CZBZ == null? `0`:`${e.CZBZ.length}`)
        zWidth = parseInt(e.JCJG == null? `0`:`${e.JCJG.length}`) + parseInt(e.CZBZ == null? `0`:`${e.CZBZ.length}`)
        zWidth = parseInt(JCJGWidth) + parseInt(CZBZWidth)
        ////console.log("数据",zWidth);
        if (zWidth>=20) {
          wCountLong++;
        } else {
          wCountShort++
        }
      });
      if ($(".right_top_content").css("display") == "none") {
        height+=zheight+sheight+3+13
      } else {
        // height+=(wCountLong + Math.ceil(wCountShort/2))*34+40
        height+=zheight+sheight+(wCountLong + Math.ceil(wCountShort/2))*34+3+13
        // //console.log(zheight+sheight,1,height,($(".zd").offset().top))

      }
      
    }
    else{
      height+=sheight+3+5;
    }
    return height
}
// 所有按钮（弹窗+普通<新增,编辑除外>）
var popUp_button = {
  // 右侧页面保存
  save1: function (id) {
    if (document.getElementById("if_" + id).src.indexOf("blwsdetail") >= 0) {
      try {
        var childWindow = $("#if_" + id)[0].contentWindow;
        childWindow.ehr_save(id);
      }
      catch (e) {
        WRT_e.ui.message({
          title: '提示信息',
          content: `该状态病历文书只读，无法修改保存!`,
          onOk() {
          }
        })
      }
    }
    else {
      WRT_e.ui.message({
        title: '提示信息',
        content: `无保存功能!`,
        onOk() {
        }
      })
    }
  },
  // 右侧页面图片管理
  imggl: function (id) {
    let jsonimg = WRT_config.BrwsList.dvm_wslist.map(function (item) {
      return item
    })
    var j_blzt = WRT_config.BrwsCsh.BRXX.BLZT
    if (j_blzt == "1") {
      WRT_e.ui.message({
        type:'warning',
        title: '提示信息',
        content: `已封存病历不能进行该操作!`,
        onOk() {
        }
      })
      return;
    }
    // //console.log(jsonimg)
    var imgjson = jsonimg.find(ev => ev.WSID == id)
    // //console.log(imgjson)
    if(id =='t1'){
      WRT_e.ui.message({
        title: '提示信息',
        content: `请先保存后再操作!`,
        onOk() {
        }
      })
    } else {
      try {
        var winobj = window.open("../fileup/picUpload.aspx?wsid=" + imgjson.WSID + "&wslx=" + imgjson.WSLX, "_blank", "Height= 550px,Width =850px,resizable=yes,scrollbars=yes");
        var loop = setInterval(function () {
          if (winobj.closed) {
            clearInterval(loop);
          }
        })
      }
      catch (e) {
        WRT_e.ui.message({
          title: '提示',
          content: `图片查看失败`,
          onOk() {
          }
        })
      }
    }
   
  },
  // 右侧页面删除
  delete_btn: function (id) {
    var popUp = $("#simpleMode").is(':checked')
    if (popUp == false) {
      if (document.getElementById("if_" + id).src.indexOf("blwsdetail") >= 0) {
        try {
          if (id.indexOf("t") > -1) {
            // 直接删除
            objNew.splice((objNew.findIndex(item => item.id ==id)), 1)
            $("#panel"+id).remove()
          } else {
            var childWindow = $("#if_" + id)[0].contentWindow;
            childWindow.ehr_delete()
          }
        }
        catch (e) {
          WRT_e.ui.message({
            title: '提示信息',
            content: `该状态病历文书只读，无法删除!`,
            onOk() {
            }
          })
        }
      }
      else {
        WRT_e.ui.message({
          title: '提示信息',
          content: `无删除功能!`,
          onOk() {
          }
        })
      }
    } else {
      var addID = $("#xz_nr option:checked").attr("class")
      var j_blzt = WRT_config.BrwsCsh.BRXX.BLZT
      // 8、（√）新增文书_GetNewWsHtml（不清楚属于哪里a.GSDM的值获取方式和位置不确定是否正确）
      WRT_e.api.blwslist.getNewWsHtml({
        params: {
          "as_gsdm": addID,
          "as_blid": params["as_blid"],
          "as_zyid": params["as_zyid"],
          "as_tmpid": "t2" // 文书临时id什么事t1,下面是t2
        },
        success(data) {
          if (data.Code == 1) {
            WRT_config.NewWsHtml = data.Result
            // //console.log("8、新增文书_GetNewWsHtml", WRT_config.NewWsHtml)
            if (j_blzt == "1") {
              WRT_e.ui.message({
                type:'warning',
                title: '提示信息',
                content: `已封存病历不能进行该操作!`,
                onOk() {
                }
              })
              return;
            }
            if ($("#del"+id).attr("wsid") == 0) {
              // 直接删除
              objNew.splice((objNew.findIndex(item => item.id ==id)), 1)
              $("#panel"+id).remove()
              // $("#simpleModeModal").iziModal("destroy")
              $('#simpleModeModalnew').iziModal("destroy")
            } else {
              var childWindow = $("#if_" + id)[0].contentWindow;
              childWindow.ehr_delete()
              // $("#simpleModeModal").iziModal("destroy")
              // $('#simpleModeModalnew').iziModal("destroy")
            }
          }
        }
      })
    }
  },
  // 右侧页面打印
  print_btn: function (id) {
    // //console.log(id)
    // var id = $(".printclick").attr("name")
    if (document.getElementById("if_" + id).src.indexOf("blwsdetail") >= 0) {
      try {
        document.getElementById("if_" + id).contentWindow.ehr_print();
      }
      catch (e) {
        WRT_e.ui.message({
          type: 'error',
          title: '提示信息',
          content: `打印出错!`,
          onOk() {
          }
        })
      }
    }
    else {
      document.getElementById("if_" + id).contentWindow.print();
    }
  },
  // 关闭（还有问题）
  close1: function (id) {
    let keyDownEvent=document.onkeydown;

    document.onkeydown= function(ev){
      ev.stopPropagation();
      if (ev.keyCode == "13") {
        $(".e_message button[name='confirm']").click()
        // name="confirm"
      }
    }
    WRT_e.ui.message({
      title: '提示',
      content: `是否要关闭该窗口？`,
      onOk() {
        document.onkeydown=keyDownEvent;
        // xzcf(WRT_config.NewWsHtml)
        // 直接删除
        if (id.indexOf("t")>-1) {
          // var c_id = objNew.find(e => e.GSDM == $("#close"+id).attr("gsdm"))
          // objNew.splice((objNew.findIndex(item => item.id ==c_id.id)), 1)
          $("#simpleModeModalnew").iziModal("destroy")
        } else {
          $("#simpleModeModalnew").iziModal("destroy")
          $("#simpleModeModal").iziModal("destroy")
          
        }
        // }
      },
      onCancel() {
        document.onkeydown=keyDownEvent;
      },
    })
  }
}
// 时间
Date.prototype.toLocaleString = function () {
  return this.getFullYear() + "-" + (this.getMonth() + 1) + "-" + this.getDate() + "  " + this.getHours() + ":" + this.getMinutes() + ":" + this.getSeconds();
}
//父界面关闭调用时调用该接口
function delTab(){
  CheckWsChanged(val=>{
    if(val){
      return false;
    }
    else{
      parent.delBlws()
    }
  })
}
// 刷新
function bgmcNow(arr,i,cb){
  // //console.log(33,arr[i])
  let list=[]
  for(let j in arr){
    if(arr[j].contentWindow&&arr[j].contentWindow.name){
      try {
        if(arr[j].contentWindow.ehr_ischanged()){
          list.push(arr[j].contentWindow.name)
        }
      } 
      catch (e) {
        console.log(e)
      }
      //  else  {
      //   WRT_e.ui.hint({
      //     type: 'warning',
      //     msg: 'qingxian jiechu '
      //   })
      // }
    }
  }
  let html=''
  if(list.length==0){
    return cb(false)
  }
  list.map(function(item){
    html=html+'\u000d【'+item+'】'
  })
  var bgmc=arr[i].contentWindow.name
  let res=confirm(`你有${list.length}文书${html}有正在编辑的页面数据发生变化，留在本页请点确定，关闭请点取消`)
  if(res){
    cb(true)
  }else{
    cb(false)
  }
  // WRT_e.ui.message({
  //   title: '提示',
  //   content: `【${bgmc}】有正在编辑的页面数据发生变化，是否留着在本页`,
  //   onOk() {
  //       cb(true)
  //   },
  //   onCancel() {
  //     if(i<arr.length-1)bgmcNow(arr,i+1,cb);
  //     else
  //       cb(false)
  //   }
  // })
}
function CheckWsChanged(cb){
  var obj_lst = document.querySelectorAll(".panel iframe");
  var obj_lst1
  var obj_Lst=[]
  // var popNew = $("#simpleModeModalnew").attr("style")
  // var popEdit=  $("#simpleModeModal").attr("style")
  //
  var addBoolean=$("#simpleModeModalnew").length!=0&&$("#simpleModeModalnew").css("display")=="block"
  //
  var editBoolean=$("#simpleModeModal").length!=0&&$("#simpleModeModal").css("display")=="block"
  // //console.log(obj_lst,popEdit,popNew)
  if ((!addBoolean && !editBoolean) &&(obj_lst==null||obj_lst.length == 0)) {
    cb(false);
  }else {
    if (addBoolean) {
      obj_lst1= document.querySelectorAll("#simpleModeModalnew iframe")
      obj_Lst=$.merge(Array.prototype.slice.call(obj_lst),Array.prototype.slice.call(obj_lst1));
      bgmcNow(obj_Lst,0,cb);
    } else if(editBoolean){
      obj_lst1= document.querySelectorAll("#simpleModeModal iframe")
      obj_Lst=$.merge(Array.prototype.slice.call(obj_lst),Array.prototype.slice.call(obj_lst1));
      bgmcNow(obj_Lst,0,cb);
    }else{
      bgmcNow(obj_lst,0,cb);
    }
  }
  return false;
}
//计算日期差
function daysBetween(sDate1, sDate2, type,CWRYSJ) {
  // //console.log(sDate1, sDate2, type,CWRYSJ)
  // //console.log((new Date(parseInt((sDate1).substr(6)))).toLocaleString(), sDate2, type,(new Date(parseInt((CWRYSJ).substr(6)))).toLocaleString())
  if(sDate1){
    sDate1 = (new Date(parseInt((sDate1).substr(6)))).toLocaleString()
  }
  CWRYSJ = (new Date(parseInt((CWRYSJ).substr(6)))).toLocaleString()
  if(new Date(sDate1).getTime()<=0){
    sDate1=CWRYSJ
  }
  var d = new Date(sDate2);
  var year = d.getFullYear();
  var month = change(d.getMonth() + 1);
  var day = change(d.getDate());
  function change(t) {
    if (t < 10) {
        return "0" + t;
    } else {
        return t;
    }
  }
  var time = year + '-' + month + '-' + day
  //Date.parse() 解析一个日期时间字符串，并返回1970/1/1 午夜距离该日期时间的毫秒数
  var time1 = Date.parse(new Date(sDate1));
  var time2 = Date.parse(new Date(time));
  switch (type) {
    case 'year':
      let yyy=(time2 - time1) / 1000 / 3600 / 24
      // //console.log(yyy,parseInt(yyy))
      if(String(yyy).indexOf(".")>=0){
        yyy=Math.abs(parseInt(yyy))+1
      }
      return yyy;
    case 'day':
      return Math.abs(parseInt((time2 - time1) / 1000 / 3600 / 24 / 365));
  }
}
// 时间格式转换(/data(xxx)/转换为xxxx-xx-xx xx:xx:xx)
function conversionTime(dateTime){
  // console.log(dateTime)
  var date = new Date(parseInt((dateTime).substr(6)))
  // console.log(date)
  var year,month,day,H,M,S;
  year = date.getFullYear();
  month = date.getMonth()+1;
  day = date.getDate();
  H = date.getHours();
  M = date.getMinutes();
  S = date.getSeconds();
  time = year  + '-' + (month < 10 ? ( '0'+ month) : month) + '-' + (day<10 ? ('0'+day) : day) + ' ' + (H<10 ? ('0'+H) : H) + ':'+ (M<10 ? ('0'+M) : M) + ':'+ (S<10 ? ('0'+S) : S);
  // console.log(time)
  return time 
}

/****************视图****************/


//  上方
var progress_top = WRT_e.view.extend({
  render: function () {
    var obj=WRT_config.BrwsCsh.BRXX
    console.log(obj)
    this.$el.html(`
      <div class="flex-row align-items-center titleTop" style="width:${$("body").width()}">
        <div class="rightBtnTop">
        <span>
        <a href="javascript:void(0)" class="e_btn bcadd">新增</a>
        <a href="javascript:void(0)" class="e_btn right_show">展开</a>
        <a href="javascript:void(0)" class="e_btn right_hide">折叠</a>
        </span>
        </div>
        <div class="flex-row align-items-center">
        <span class="brBlh">
        &nbsp;&nbsp;&nbsp;&nbsp;<span style="color: #555555;"></span>
        <b>${WRT_config.BrwsCsh.EMPI||''}</b>
        </span>
        <span class="brName">
        &nbsp;&nbsp;&nbsp;&nbsp;<b>${obj.BRXM}</b>
        </span>
        <span class="brxx">
        &nbsp;&nbsp;&nbsp;&nbsp;${(obj.BRXB == 1) ? `男` : `女`}&nbsp;&nbsp;&nbsp;&nbsp;${obj.ZKMC}&nbsp;&nbsp;&nbsp;&nbsp;
        </span>
        <span class="e-tag e-tag-blue brBlue">
        ${obj.ZDYBQDM}-${obj.CWH}
        </span>
        <span class="e-tag e-tag-red brRed">
        ${daysBetween(obj.BQRYSJ,new Date(),'year',obj.CWRYSJ)}天
        </span>
        <span class="e-tag e-tag-blue brChangeColor">
        ${obj.JSLX == 00 ?'自费':obj.JSLX == 01?'社保':obj.JSLX == 02?'农保':'工费'}
        </span>
        <button class="btn_sx e_btn" type="button" style="height:40px;margin-left:20px">刷新本页</button>
        </div>
        <div class="rightTop">
          
          <label class="simpleMode checkbox-inline"">
            <input  type="checkbox" class="tui-checkbox " id="simpleMode"value="option1">弹窗模式
          </label>
        </div>
      </div>
      `)
    return this
    // <span class="e-tag e-tag-gray brGray">还没获取</span>
  },
  events: {
    "click .btn_sx": function () {
      CheckWsChanged(val=>{
        if(val){
          return false;
        }
        else{
          location.reload(true)
        }
      })
    },
    // 折叠按钮
    "click .right_hide": function () {
      // 控制左侧危急值记录提醒折叠（后面要求加的）
      $(".right_top_content").css("display","none")
      $(".zd").css({
        "height": `calc(100% - 40px)`,
        // "border":'1px solid red'
      })
      
      // 控制左侧病程记录折叠
      $('.panel-collapse').collapse('hide')
      $(".right_hide").css({
        "color": "#FFFFFF",
        "background": "#3D6CC8",
      })
      $(".right_show").css({
        "color": "#3D6CC8",
        "background": "#FFFFFF",
        "border":" 1px solid #3D6CC8",
      })
    },
    // 弹开按钮
    "click .right_show": function () {
      // 控制左侧危急值记录提醒展开（后面要求加的）
      $(".right_top_content").css("display","block")

      // 控制左侧病程记录展开
      $('.panel-collapse').collapse('show')
      $(".right_show").css({
        "color": "#FFFFFF",
        "background": "#3D6CC8",
      })
      $(".right_hide").css({
        "color": "#3D6CC8",
        "background": "#FFFFFF",
        "border":" 1px solid #3D6CC8",
      })
    },
    // 新增按钮
    "click .bcadd": function () {
      iframe_Id=null
      var j_gzdm = WRT_config.BrwsCsh.YHXX.GZDM
      if (j_gzdm == "0000") {
        WRT_e.ui.message({
          title: '提示信息',
          content: `非医生账号无法书写文书，请联系医务处或信息处进行确认修改`,
          onOk() {
          }
        })
        return;
      }
      // 10 判断文书是否可以多份
    WRT_e.api.blwslist.GetCheckGsdm({
      params: {
        "as_blid": params["as_blid"],
        "as_gsdm": $("#xz_nr option:checked").attr("class")
      },
      success(data) {
        if (data.Code == 1) {
          WRT_config.CheckGsdm = data.Result
          // //console.log(WRT_config.CheckGsdm)
          if (WRT_config.CheckGsdm == "0") {
            // 11、获取文书类型e_GetWslx（用到部分已经自行取用？）（返回值为文书类型，5（e_GetBrWssByLx这个接口返回值中的WSLX为这个内容））
            WRT_e.api.blwslist.GetWslx({
              params: {
                "as_gsdm": $("#xz_nr option:checked").attr("class")
              },
              success(data) {
                if (data.Code == 1) {
                  WRT_config.Wslx = data
                  // //console.log("11、获取文书类型e_GetWslx",WRT_config.Wslx)
                  // if (WRT_config.BrwsCsh.VTECS == "1") {
                  //   VTECS(WRT_config.Wslx.Result)
                  // } else {
                    CheckAddNew((WRT_config.Wslx.Result), $("#xz_nr option:checked").attr("class"))
                  // }
                  
                  // AddNewWs(gsdm)
                }
              }
            })
          } else {
            // //console.log(WRT_config.BrwsCsh)
            // var addVal = $("#xz_nr option:checked").val()
            // var z_mc = (WRT_config.BrwsList.dvm_wslist)
            // var z_left=z_mc.find(e=>e.GSDM == $("#xz_nr option:checked").attr("class"))
            // if ((addVal == "首次病程记录")&& (z_left != undefined)&&(addVal == z_left.GSMC)) {
            //   j_sfsc = "1"
            // }
            WRT_e.ui.message({
              title: '提示信息',
              content: `该记录,一份文书中只允许存在一份,如页面上未显示,请刷新！`,
              onOk() {
              }
            })
          }
        }
      }
    })
      // // 11、获取文书类型e_GetWslx（用到部分已经自行取用？）（返回值为文书类型，5（e_GetBrWssByLx这个接口返回值中的WSLX为这个内容））
      // WRT_e.api.blwslist.GetWslx({
      //   params: {
      //     "as_gsdm": $("#xz_nr option:checked").attr("class")
      //   },
      //   success(data) {
      //     if (data.Code == 1) {
      //       WRT_config.Wslx = data
      //       // //console.log("11、获取文书类型e_GetWslx",WRT_config.Wslx)
      //       CheckAddNew((WRT_config.Wslx.Result), $("#xz_nr option:checked").attr("class"))
      //       // AddNewWs(gsdm)
      //     }
      //   }
      // })
      
    },
  }
})
// 左侧显示
// 左侧病程记录列表
var progress_noteList = WRT_e.view.extend({
  render: function () {
    if ((WRT_config.BrwsCsh.WSLXS).indexOf(",")>-1) {
      var c=[]
      var toSplit = (WRT_config.BrwsCsh.WSLXS).split(",");
      for (var i = 0; i < toSplit.length; i++) {
        c.push(toSplit[i]);
      }
      this.data.dataWSLXS = c
    } else {
      this.data.dataWSLXS = WRT_config.BrwsCsh.WSLXS
    }
    //console.log(this.data.dataWSLXS)
    // //console.log(j_sfsc)
    // height:${`calc(${$(".blwslist_inner_right").height()}px - 40px - 36px)`}"
    // <div id="sidebar" style="height:${`calc(100% - ${$(".list_title").height()}px)`}">
    // style="${(WRT_config.BrwsList.dvm_wslist).length==0?`background: linear-gradient(180deg, #FFFFFF 0%, #D1D1D1 100%);border: 1px solid #9A9A9A;color:#333333`:``}"
    // //console.log(this.data,this.data.dataWSLXS,WRT_config.BrwsCsh.WSLXS, WRT_config.BrwsList)
    // //console.log((WRT_config.BrwsList.dvm_wslist[5].SXRY).split("/").length-1)
    // console.log(999,WRT_config.BrwsList.dvm_wslist,this.data)
    this.$el.html(` 
    <div class="list_title_W"> 
      <div class="list_title" style="${(WRT_config.BrwsList.dvm_wslist).length==0?`background: linear-gradient(180deg, #FFFFFF 0%, #D1D1D1 100%);border: 1px solid #9A9A9A;color:#333333`:``}">
        病程记录列表
      </div>
    </div>  
    <div id="sidebar">
      <div class="left_nr_line1">
        <select class="xz_nr form-control" name="xz_nr" id="xz_nr">
          ${_.map(WRT_config.WslbItem, (obj) => `${obj.WSLX!='1E'?`<option id="xz_nrx" class="${obj.GSDM}" name="${obj.WSLX}" value ="${obj.GSMC}" >${obj.GSMC}</option>`:''}`)}      
        </select>
      </div>
      <div class="list_inner_W" style="position: relative;height: 99%;">
        <ul class="list_inner" style="height:${`calc(100% - 30px)`}">
          ${_.map(WRT_config.BrwsList.dvm_wslist, (obj) => `
          <li class="submenu" id="submenu${obj.WSID}" wsid=""${obj.WSID} wslx="${obj.WSLX}" name="${obj.WSID}"  gsdm="${obj.GSDM}" value="${this.data.dataWSLXS}" style="color:${(obj.COLOR =="red")?`red`:`${(((obj.WSLX=="11")||(this.data.dataWSLXS.indexOf(obj.WSLX)>-1))&&(obj.QM=="0"))?`red` : `#000`}`}">
            <a href="javascript:void(0)" class="btnlist" name="${obj.WSID}" id="type_title${obj.WSID}" value="${obj.WSLX}">
              <div class="${obj.WSID}" style="color:${(obj.COLOR =="red")?`red`:(obj.QM !="0")?`#000` :`${(((obj.WSLX=="11")||(this.data.dataWSLXS.indexOf(obj.WSLX)>-1)))?`red`: `#000`}`}">
                  <div class="${obj.WSID}" id="submenu_title">
                  <span  class="${obj.WSID}" id="submenu_time">${obj.JLSJ}</span>
                  <span class="${obj.WSID}">${obj.GSMC}</span>
                  <span  class="${obj.WSID}" id="submenu_name">(${obj.SXRY&&(obj.SXRY).substr(0,1)=="/"?`${obj.SXRY&&(obj.SXRY).substr(1)}`:`${obj.SXRY||''}`})</span>
                  </div>
              </div>
            </a>
          </li>`).join('')}
        </ul>
      </div>
    </div>`)
    
    return this
    // <span  class="${obj.WSID}" id="submenu_name">$${obj.SXRY}</span>
    // <span  class="${obj.WSID}" id="submenu_name">${(obj.SXRY).substr(0,1)=="/"?`${(obj.SXRY).substr(1)}`:`${obj.SXRY}`}</span>
    // <div  class="${obj.WSID}" id="submenu_name1">${(obj.SXRY).substr(0,1)=="/"?`${(obj.SXRY).substr(1)}`:`${obj.SXRY}`}</div>
    // ${((WRT_config.BrwsList.dvm_wslist.SXRY).split("/").length-1>=2?`
    // `:``}
    // <span class="${obj.WSID}" style="color:${(obj.COLOR =="red")?`red`:`${(((obj.WSLX=="11")||(obj.WSLX == this.data.dataWSLXS))&&(obj.QM=="0"))?`red` : `#707070`}`}">${obj.JLSJ} ${obj.GSMC}(${obj.SXRY})</span>
  },
  events: {
    // 列表点击
    "click .btnlist": function (idname) {
      //console.log(idname)
      // //console.log(idname.target.className)
      // 获取初始化病例状态<判断病例是否封存>
      var j_blzt = WRT_config.BrwsCsh.BRXX.BLZT
      if (j_blzt == "1") {
        // WRT_e.ui.message({
        //   title: '提示信息',
        //   content: `已封存病历不能进行该操作!`,
        //   onOk() {
        //   }
        // })
        if (idname.target.name == undefined) {
          let id = '#panel_' + idname.target.className
          // $('.zd').animate({
          //   scrollTop:scrollTop($('.zd').scrollTop()+$(id).offset().top-heightDW())
          // }, 800);
          // //console.log(heightDW())
          $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(id).offset().top - heightDW()},300);
          // $('.zd').scrollTop($('.zd').scrollTop()+$(id).offset().top-heightDW());
        } else {
          // //console.log(heightDW())
          let id = '#panel_' + idname.target.name
          $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(id).offset().top - heightDW()},300);
          // $('.zd').scrollTop($('.zd').scrollTop()+$(id).offset().top-heightDW());
          // document.querySelector(".zd "+id).scrollIntoView(true);
        }
      } else {
         // 弹窗判断 false true
        var popUp = $("#simpleMode").is(':checked')
        if (popUp == false) {
          // 锚点定位
          if (idname.target.name == undefined) {
            var id = '#panel_' + idname.target.className
            var ct = '#panel' + idname.target.className
            var show = '#type_' + idname.target.className
            // (".panel-collapse")
            $(show).collapse('show')
            $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(id).offset().top-heightDW()},300);
            // // $('.zd').scrollTop($('.zd').scrollTop()+$(id).offset().top-heightDW());
          } else {
            // //console.log(heightDW())
            var id = '#panel_' + idname.target.name
            var ct = '#panel' + idname.target.name
            var show = '#type_' + idname.target.name
            $(show).collapse('show')
            $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(id).offset().top-heightDW()},300);
            // // $('.zd').scrollTop($('.zd').scrollTop()+$(id).offset().top-heightDW());
            // document.querySelector(".zd "+id).scrollIntoView(true);
          }
          // var data1={
          //   dataList:WRT_config.BrwsList.dvm_wslist.find(item=>{
          //     item.urlBlws = "as_blid=" + WRT_config.BrwsCsh.BRXX.BLID + "&as_gsdm=" + item.GSDM + "&as_zyid=" + WRT_config.BrwsCsh.BRXX.ZYID + "&as_wsid=" + item.WSID + "&as_wslx=" + item.WSLX + "&as_tmpid=-192&tmpid=0.054136330412483336"
          //     return item.WSID==idname.target.name || idname.target.className;
          //   })
          // };
          //console.log(idname.target.name, idname.target.className)
          var data1={
            dataList:WRT_config.BrwsList.dvm_wslist.find(item=>{
              if (idname.target.name == undefined){
                return item.WSID== idname.target.className;
              } else {
                return item.WSID== idname.target.name;
              }
            })
          };
          var view =noteViews.find(el=>{
            if (idname.target.name == undefined){
              return el.data.dataList.WSID== idname.target.className;
            } else {
              return el.data.dataList.WSID== idname.target.name;
            }
          })
          view.data=data1;
          $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(id).offset().top-heightDW()},300);
            
          // //console.log(view)
          // 点击左侧列表后是否切换为编辑状态
          // if ($(ct + " #if_"+view.data.dataList.WSID).length==0 || view.data.dataList.DATAOP_EDIT!=null) {
          //   view.edit1(idname);
          // }
        } else {
          // var data1={
          //   dataList:WRT_config.BrwsList.dvm_wslist.find(item=>{
          //     item.urlBlws = "as_blid=" + WRT_config.BrwsCsh.BRXX.BLID + "&as_gsdm=" + item.GSDM + "&as_zyid=" + WRT_config.BrwsCsh.BRXX.ZYID + "&as_wsid=" + item.WSID + "&as_wslx=" + item.WSLX + "&as_tmpid=-192&tmpid=0.054136330412483336"
          //     return item.WSID==idname.target.name || idname.target.className;
          //   })
          // };
          // //console.log(data1)
          var data1={
            dataList:WRT_config.BrwsList.dvm_wslist.find(item=>{
              if (idname.target.name == undefined){
                return item.WSID== idname.target.className;
              } else {
                return item.WSID== idname.target.name;
              }
            })
          };
          //console.log(data1)
          var view =noteViews.find(el=>{
            if (idname.target.name == undefined){
              return el.data.dataList.WSID== idname.target.className;
            } else {
              return el.data.dataList.WSID== idname.target.name;
            }
          })
          view.data=data1;

          view.edit1(idname);
          // view.editPop()
          //console.log(view)

          // WRT_e.ui.model({
          //   id:"simpleModeModal",
          //   overlayClose: false, //点击遮罩关闭框
          //   title: titlename,
          //   width: 1332,
          //   iframe: false,
          //   // iframeURL:`${WRT_config.server}/zyblws/blwsdetail.aspx?` + WslbItem_nrUrl + `,
          //   content:docEditHtml(tcurl,popurl),
          // })
          // load(tcurl.WSID)

          
          // let json = WRT_config.BrwsList.dvm_wslist.map(function (item) {
          //   if (item.WSID == idname.target.className) {
          //     // item.two = 1
          //     item.urlBlws = "as_blid=" + WRT_config.BrwsCsh.BRXX.BLID + "&as_gsdm=" + item.GSDM + "&as_zyid=" + WRT_config.BrwsCsh.BRXX.ZYID + "&as_wsid=" + item.WSID + "&as_wslx=" + item.WSLX + "&as_tmpid=-192&tmpid=0.054136330412483336"
          //     return item
          //   } else {
          //     return item
          //   }
          // })
          // var tcurl =view.data.dataList
          // var titlename = tcurl.JLSJ + tcurl.GSMC + "(书写人员：" + tcurl.SXRY + ")"
          // if (idname.target.name == undefined) {
          //   // var tcurl =json.find(e => e.WSID == idname.target.className)
          //   // var titlename = tcurl.JLSJ + tcurl.GSMC + "(书写人员：" + tcurl.SXRY + ")"
          //   var popurl = "as_blid=" + WRT_config.BrwsCsh.BRXX.BLID + "&as_gsdm=" + tcurl.GSDM + "&as_zyid=" + WRT_config.BrwsCsh.BRXX.ZYID + "&as_wsid=" + tcurl.WSID + "&as_wslx=" + tcurl.WSLX + "&as_tmpid=-192&tmpid=0.054136330412483336"
          //   tcurl.url = popurl
          // } else {
          //   // var tcurl =json.find(e => e.WSID == idname.target.name)
          //   // var titlename = tcurl.JLSJ + tcurl.GSMC + "(书写人员：" + tcurl.SXRY + ")"
          //   var popurl = "as_blid=" + WRT_config.BrwsCsh.BRXX.BLID + "&as_gsdm=" + tcurl.GSDM + "&as_zyid=" + WRT_config.BrwsCsh.BRXX.ZYID + "&as_wsid=" + tcurl.WSID + "&as_wslx=" + tcurl.WSLX + "&as_tmpid=-192&tmpid=0.054136330412483336"
          //   tcurl.url = popurl
          // }
          // let model = $("#simpleModeModal").iziModal({ //初始化modal
          //   overlayClose: false, //点击遮罩关闭框
          //   title: titlename,
          //   width: 1332
          // })
          // $('.iziModal-button-close').unbind('click').click(function(e){
          // //移除close属性并绑定点击事件
          //   $('.iziModal-button-close').removeAttr("data-izimodal-close")
          //   popUp_button.close1(`${tcurl.WSID}`)
          // })
          // // //设置自定义内容
          // model.iziModal('setContent',
          //   `<div class="tc">
          //     <div>
          //       <iframe class="dynr" id="if_${tcurl.WSID}" name="${tcurl.GSMC}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?` + popurl + `" rameborder="0" width="100%"></iframe>
          //     </div>
          //     <div>
          //       <a class="saveclick e_btn" id="save${tcurl.WSID}" name="${tcurl.WSID}" onclick="popUp_button.save1('${tcurl.WSID}')" type="button">保存</a>
          //       <a class="delclick e_btn" id="del${tcurl.WSID}" name="${tcurl.WSID}" onclick="popUp_button.delete_btn('${tcurl.WSID}')" type="button">删除</a>
          //       <a class="imgclick e_btn" id="img${tcurl.WSID}" name="${tcurl.WSID}" onclick="popUp_button.imggl('${tcurl.WSID}')" type="button">图片管理</a>
          //       <a class="closetc e_btn" name="${tcurl.WSID}" onclick="popUp_button.close1('${tcurl.WSID}')" type="button" name="close">关闭</a>
          //     </div>
          //   </div>`);
          // load(tcurl.WSID)
          // //打开
          // model.iziModal('open')
        }
      }
    }
  }
})
var lefthead_sx = WRT_e.view.extend({
  render: function () {
    this.$el.html(`
    <div class="list_title" style="${(this.data.dataRList.dvm_wslist).length==0?`background: linear-gradient(180deg, #FFFFFF 0%, #D1D1D1 100%);border: 1px solid #9A9A9A;color:#333333`:``}">
      病程记录列表
    </div>
    `)
    return this
  }
})
// 左侧侧列表内容刷新
var leftlist_sx = WRT_e.view.extend({
  render: function () {
    // (obj.WSLX == this.data.dataWSLXS)
    //console.log(this.data,this.data.dataWSLXS,WRT_config.BrwsCsh.WSLXS, WRT_config.BrwsList)
    this.$el.html(`
    <ul class="list_inner" style="height:${`calc(100% - 30px)`}">
    ${_.map(WRT_config.BrwsList.dvm_wslist, (obj) => `
    <li class="submenu" id="submenu${obj.WSID}" wsid=""${obj.WSID} wslx="${obj.WSLX}" name="${obj.WSID}"  gsdm="${obj.GSDM}" value="${this.data.dataWSLXS}" style="color:${(obj.COLOR =="red")?`red`:`${(((obj.WSLX=="11")||(this.data.dataWSLXS.indexOf(obj.WSLX)>-1))&&(obj.QM=="0"))?`red` : `#000`}`}">
      <a href="javascript:void(0)" class="btnlist" name="${obj.WSID}" id="type_title${obj.WSID}" value="${obj.WSLX}">
        <div class="${obj.WSID}" style="color:${(obj.COLOR =="red")?`red`:(obj.QM !="0")?`#000` :`${(((obj.WSLX=="11")||(this.data.dataWSLXS.indexOf(obj.WSLX)>-1)))?`red`: `#000`}`}">
            <div class="${obj.WSID}" id="submenu_title">
            <span  class="${obj.WSID}" id="submenu_time">${obj.JLSJ}</span>
            <span class="${obj.WSID}">${obj.GSMC}</span>
            <span  class="${obj.WSID}" id="submenu_name">(${(obj.SXRY).substr(0,1)=="/"?`${(obj.SXRY).substr(1)}`:`${obj.SXRY}`})</span>
            </div>
        </div>
      </a>
    </li>`).join('')}
    </ul>
    `)
    return this
  }
})
// 右侧显示
// 右上
var notetop=true
var progress_notetop = WRT_e.view.extend({
  render: function () {
    var bool=((WRT_config.BrwsCsh.DV_WJZ === undefined) || (WRT_config.BrwsCsh.DV_WJZ === ''));
    var height=0;
    var zheight = $(".blwslist_inner").height();
    var wCountLong=0
    var wCountShort=0
    //危机记录高度（条数*单行高度+title+下边框）
    if(!bool){
      //console.log(WRT_config.WjzLst);
      var JCJGWidth,CZBZWidth,zWidth
      // WRT_config.WjzLst=[...WRT_config.WjzLst,...WRT_config.WjzLst,...WRT_config.WjzLst,...WRT_config.WjzLst,...WRT_config.WjzLst,...WRT_config.WjzLst,...WRT_config.WjzLst,...WRT_config.WjzLst,...WRT_config.WjzLst]
      WRT_config.WjzLst.forEach(e => {
        // //console.log(e.JCJG,e.CZBZ);
        // if (e.JCJG == 134) {
        //   e.JCJG = "多发炎性、增殖、钙化灶 两侧胸腔少许积液 胆囊结石，左侧肾上腺增粗，请结合临床  "
        // } else if (e.JCJG == 170) {
        //   e.JCJG = "双侧大脑弥漫性水肿 两肺多发炎性、增殖、钙化灶 两侧胸腔少许积液 胆囊结石，左侧肾上腺增粗，请结合临床  "
        // } 
        var JCJGWidth = (e.JCJG == null? `0`:`${e.JCJG.length}`)
        var CZBZWidth = (e.CZBZ == null? `0`:`${e.CZBZ.length}`)
        zWidth = parseInt(e.JCJG == null? `0`:`${e.JCJG.length}`) + parseInt(e.CZBZ == null? `0`:`${e.CZBZ.length}`)
        zWidth = parseInt(JCJGWidth) + parseInt(CZBZWidth)
        //console.log("数据",zWidth);
        if (zWidth>=20) {
          wCountLong++;
        } else {
          wCountShort++
        }
      });
      //console.log(wCountLong,wCountShort);
      let num=(wCountLong + Math.ceil(wCountShort/2))
      if(num>5){
        num=5
      }
      height+=num*34+40
      // height+=((WRT_config.WjzLst.length/2 == 0.5)?`1`:`0`+WRT_config.WjzLst.length%2)*34+40
      // //console.log(2,height, Math.ceil(wCountShort/2))
    }
    // if(!bool)height+=WRT_config.WjzLst.length*26.5+40+3;
    // position: relative;width: 100%;border-bottom: 1px solid #DADADA;
    //  style="width:${(parseInt(obj.JCJG == null? `0`:`${obj.JCJG.length}`)+parseInt(obj.CZBZ == null? `0`:`${obj.CZBZ.length}`)>=20)?`100%`:`50%`}"
    // <table class="right_top_content">
    //       ${_.map(WRT_config.WjzLst, (obj) => `
    //         <td colspan="2" class="topline" id="topline${obj.BRID}">
    //           <span class="bgsj">${(new Date(parseInt((obj.BGSJ).substr(6)))).toLocaleString()}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
    //           <span class="jcxm">${obj.JCXM}：</span>
    //           <span class="jcjg">${obj.JCJG==null?``:`&nbsp;&nbsp;&nbsp;&nbsp;${obj.JCJG}&nbsp;&nbsp;&nbsp;&nbsp;`}${obj.CZBZ==null?``:`${obj.CZBZ}`}</span>
    //         </td>`).join('')}
    //     </table>
    var str =""
    // conversionTime(`/Date(1346515200)/`)
    // <span class="bgsj">${(new Date(parseInt((obj.BGSJ).substr(6)))).toLocaleString()}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
    // <span class="bgsj">${(new Date(parseInt((obj.BGSJ).substr(6)))).toLocaleString()}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
    this.$el.html(`
    ${ bool? `
    <div></div>
    `: `
      <div class="right_top">
        <div class="right_top_title"><span>危急值记录提醒</span></div>
        <div class="right_top_content" style="${(wCountLong + Math.ceil(wCountShort/2))>5?`overflow:auto;max-height: 173px;`:``}">
          ${_.map(WRT_config.WjzLst, (obj) => `
          ${(parseInt(obj.JCJG == null? `0`:`${obj.JCJG.length}`) + parseInt(obj.CZBZ == null? `0`:`${obj.CZBZ.length}`))>=20?`
          <div class="topline" id="topline${obj.BRID}" style="width: 100%;border-bottom: 1px solid #DADADA;border-top: 1px solid #DADADA;">
            <span>${conversionTime(obj.BGSJ)}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <span class="jcxm">${obj.JCXM}：</span>
            <span class="jcjg">${obj.JCJG==null?``:`&nbsp;&nbsp;&nbsp;&nbsp;${obj.JCJG}&nbsp;&nbsp;&nbsp;&nbsp;`}${obj.CZBZ==null?``:`${obj.CZBZ}`}</span>
          </div>
          `:`
          <div class="topline" id="topline${obj.BRID}" style="width: 50%;border-bottom: 1px solid #DADADA;top:1px;">
            <span>${conversionTime(obj.BGSJ)}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span>
            <span class="jcxm">${obj.JCXM}：</span>
            <span class="jcjg">${obj.JCJG==null?``:`&nbsp;&nbsp;&nbsp;&nbsp;${obj.JCJG}&nbsp;&nbsp;&nbsp;&nbsp;`}${obj.CZBZ==null?``:`${obj.CZBZ}`}</span>
          </div>
          `}`).join('')}
        </div>
      </div>`}
      <div class="zd" style="height:${(height)?`calc(100% - ${height}px)`:`100%`}">
      </div>
    `)
    // border:1px solid red;
    return this
  },
  events:{
    "click .right_top_title":"right_top_title"
  },
  right_top_title(){
    notetop=!notetop
    if(!notetop){
      $(".right_top_content").css("display","none")
      $(".zd").css({
        "height": `calc(100% - 40px)`,
        // "border":'1px solid red'
      })
    }else{
      $(".right_top_content").css("display","block")
    }
  }
})

{/* <div class="zd" style="height:${(height)?`calc(${zheight}px - ${height}px)`:`100%`}"> */}
// 右下progress_noteContent
var progress_noteContent = WRT_e.view.extend({
  isEditor:false,//是否编辑
  render:function(){
    var obj = this.data.dataList
    var nowId = this.getNowId();
    if(obj.WSID== 0)this.isEditor=true;
    if(this.$el.find(".panel").length==0){
      this.$el.html(`
      <div class="panel" id="panel${nowId}" data-objid="${nowId}">
          <div class="panel-title_line panel-heading" id="panel_${nowId}"data-toggle="collapse" data-parent="#accordion" href="#type_${nowId}">
            ${this.getHeaderHtml()}
          </div>
          <div id="type_${nowId}" name="${nowId}" class="panel-collapse collapse in" aria-labelledby="panel_${nowId}">
            ${this.getBodyHtml(nowId)}
          </div>
        </div>
      `)
    }
    else {
      this.refresh();
    }
    if (this.$el.find(".panel_ct").css("display")=='none' ) {
      this.$el.find(".editclick").css({
        "color": "#707070",
        "background":"#FFFFFF",
      })
      this.$el.find(".editclick").hover(function(){
        $(this).css({
          "color": "#FFFFFF",
          "background": "#888888",
          // "color": "#707070",
          // "background":"#FFFFFF",
        })
      },function(){
        $(this).css({
          "color": "#707070",
          "background":"#FFFFFF",
        })
      })
    } else {
      this.$el.find(".editclick").css({
        "color": "#FFFFFF",
        "background": "#888888",
      })
      this.$el.find(".editclick").hover(function(){
        $(this).css({
          "color": "#707070",
          "background":"#FFFFFF",
          // "color": "#FFFFFF",
          // "background": "#888888",
        })
      },function(){
        $(this).css({
          "color": "#FFFFFF",
          "background": "#888888",
          // "color": "#707070",
          // "background":"#FFFFFF",
        })
      })
    }
    // if (this.isEditor) {
    //   //console.log(111)
    // } else {
    //   //console.log(222)
    // }
    return this
  },
  events: {
    // 编辑
    "click .editclick":function(ev){
      // //console.log(ev)
      ev.stopPropagation();
      // 获取初始化病例状态<判断病例是否封存>
      var j_blzt = WRT_config.BrwsCsh.BRXX.BLZT
      if (j_blzt == "1") {
        WRT_e.ui.message({
          type:'warning',
          title: '提示信息',
          content: `已封存病历不能进行该操作!`,
          onOk() {
          }
        })
      } else {
        this.edit(ev);
      }
      // this.$el.find(".panel-collapse").collapse('show');
    },
    // 图片管理
    "click .imgclick":function(imggl){
      imggl.stopPropagation()
      var obj = this.getData()
      var imgglID = this.$el.find(".panel").attr("data-objid")
      var j_gzdm = WRT_config.BrwsCsh.YHXX.GZDM
      var j_dataop = obj.DATAOP_IMG
      var j_datas = j_dataop.split(',');
      if (j_gzdm == "0000") {
        WRT_e.ui.message({
          title: '提示信息',
          content: `非医生账号无法书写文书，请联系医务处或信息处进行确认修改`,
          onOk() {
          }
        })
        return;
      }
      if (obj.wslx != "1Z") {
        var j_blzt = WRT_config.BrwsCsh.BRXX.BLZT
        if (j_blzt == "1") {
          WRT_e.ui.message({
            type:'warning',
            title: '提示信息',
            content: `已封存病历不能进行该操作!`,
            onOk() {
            }
          })
          return;
        }
        var zthq = $.parseJSON(WRT_config.BrMainEinit)// var xxcf = (WRT_config.BrMainEinit).split(',')
        var j_mr_right = zthq.qtqx
        if (j_mr_right.substr(1, 1) == "0") {
          var j_ok = false;
          if ((j_datas[3] == "18" || j_datas[3] == "1A") && j_mr_right.substr(3, 1) == "1")   //会诊医师允许写有创操作记录和抢救记录
              j_ok = true;
          if (j_datas[3] == "19" && j_mr_right.substr(4, 1) == "1")  //手术权限的医师可以写术后首程
              j_ok = true;
          if (j_ok == false) {
              $.messager.alert("提示信息", " 您没有编辑修改该类文书的权限!", "warning");
              return;
          }
        }
      }
      
      if ((imgglID).substr(0, 1) == "0") {
        WRT_e.ui.message({
          title: '提示信息',
          content: `请先保存后再操作!`,
          onOk() {
          }
        })
      }
      else {
        popUp_button.imggl(imgglID)
      }
      if (j_datas[0] == "1") {
        WRT_e.ui.message({
          title: '提示信息',
          content: `文书已锁定,无法修改!`,
          onOk() {
          }
        })
        $("#" + j_datas[6]).show();
      }
      else if (j_datas[1] == "2") {
        WRT_e.ui.message({
          title: '提示信息',
          content: `上级医师已锁定,无法修改!`,
          onOk() {
          }
        })
      }
    },
    // 保存
    "click .saveclick": function (saveBtn) {
      saveBtn.stopPropagation()
      var saveID = this.$el.find(".panel").attr("data-objid")
      lsSaveId = saveID
      popUp_button.save1(saveID)
      let id = '#panel_' + saveID
      $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(id).offset().top-heightDW()},300);
      // $('.zd').scrollTop($('.zd').scrollTop()+$(id).offset().top-heightDW());
      // document.querySelector(".zd "+id).scrollIntoView(true);
    },
    // 删除
    "click .delclick": function (delBtn) {
      delBtn.stopPropagation()
      var delID = this.$el.find(".panel").attr("data-objid")
      // //console.log("按钮名字：", delID)
      var j_blzt = WRT_config.BrwsCsh.BRXX.BLZT
      if (j_blzt == "1") {
        WRT_e.ui.message({
          type:'warning',
          title: '提示信息',
          content: `已封存病历不能进行该操作!`,
          onOk() {
          }
        })
        return;
      }
      popUp_button.delete_btn(delID)
    },
    // 打印
    "click .printclick": function (printBtn) {
      // //console.log("按钮名字：", printBtn.target.name)
      printBtn.stopPropagation()
      var printID = this.$el.find(".panel").attr("data-objid")
      var j_blzt = WRT_config.BrwsCsh.BRXX.BLZT
      if (j_blzt == "1") {
        WRT_e.ui.message({
          type:'warning',
          title: '提示信息',
          content: `已封存病历不能进行该操作!`,
          onOk() {
          }
        })
        return;
      }
      popUp_button.print_btn(printID)
    }
  },
  getData:function(){
    return this.data.dataList;
  },
  getUrlBlws:function(){
    var obj = this.getData();
    //新增
    if(obj.WSID== 0)return obj.WslbItem_nrUrl
    return "as_blid=" + WRT_config.BrwsCsh.BRXX.BLID + "&as_gsdm=" + obj.GSDM + "&as_zyid=" + WRT_config.BrwsCsh.BRXX.ZYID + "&as_wsid=" + obj.WSID + "&as_wslx=" + obj.WSLX + "&as_tmpid=-192&tmpid=0.054136330412483336"
  },
  getNowId:function(){
    var obj = this.getData()
    return (obj.WSID==0?`${obj.id}`:`${obj.WSID}`)
  },
  // Header部分（即panel-title_line部分）
  getHeaderHtml:function(){
    var obj = this.getData();
    var j_dataop = obj.DATAOP_IMG
    var j_datas = j_dataop.split(',');
    // //console.log(j_datas)
    var popUp = $("#simpleMode").is(':checked')
    return `
    ${obj.GDBZ==1?`<i class="glyphicon glyphicon-floppy-disk" dataop="${obj.DATAOP_IMG}"></i>`:j_datas[0]=="0"?`
    <i class="glyphicon glyphicon-tag" dataop="${obj.DATAOP_IMG}"></i>`:`
    <i class="glyphicon glyphicon-glyphicon-floppy-disk" dataop="${obj.DATAOP_IMG}"></i>
    `}
    ${obj.WSID== 0?`
    <span class="titleAdd">&nbsp;&nbsp;&nbsp;&nbsp;新增&nbsp;&nbsp;&nbsp;&nbsp;${obj.GSMC}</span>`:`
    <span class="titleNow">
      <p title="${obj.GSMC}">${obj.GSMC}</p>
      <p>${obj.SXRY}</p>
      <p>${obj.JLSJ}</p>
    </span>
    `}
    <span class="titleBtn">
      <span class="panel_ct"  style="${this.isEditor&&!popUp?'':'display:none;'};margin-right: 0px">
        <span class="save"><a class="saveclick">保存</a></span>
        <span class="del"><a class="delclick">删除</a></span>
        <span class="print"><a class="printclick">打印</a></span>
      </span>
      ${obj.DATAOP_EDIT==null?``:`<span class="edit"><a class="editclick">编辑</a></span>`}
      ${obj.DATAOP_PIC==null?``:` <span class="imgManage"><a class="imgclick">图片管理</a></span>`}
    </span>`;
  },
  // Body部分（即type_${nowId}部分）
  getBodyHtml:function(){
    var obj = this.getData();
    var nowId = this.getNowId();
    var popUp = $("#simpleMode").is(':checked')
    var urlBlws=this.getUrlBlws();
    // console.log("当前编辑",obj)
    let url=`${WRT_config.server}/zyblws/blwsdetail.aspx?${urlBlws}`
    if(obj.GDBZ==1){
      url=`${WRT_config.server}/zyblws/zyblwsPdf.aspx?as_wsid=${obj.WSID}&as_blid=${params["as_blid"]}&tmpid=${Math.random()}`
    }
    return `<div class="panel-body">
    ${this.isEditor &&!popUp?`
    <iframe class="dynr" id="if_${nowId}" name="${obj.GSMC}" src="${url}" rameborder="0" width="100%" onload="load('${nowId}')" height="700px"></iframe>
    `:` <div class="xsNow">${obj.PREVIEW_HTML}</div>`}
    </div>`
  },
  // 刷新当前所在的Header+body部分
  refreshPanel:function(){
    var nowId = this.getNowId();
    //容器刷新
    var panel=this.$el.find(".panel");
    panel.attr("id",`panel${nowId}`);
    panel.attr("data-objid",`${nowId}`);
  },
   // 刷新Header（即title部分）
  refreshHeader:function(){
    var nowId = this.getNowId();
    //容器刷新
    var header=this.$el.find(".panel-heading");
    header.attr("id",`panel_${nowId}`);
    header.attr("href",`#type_${nowId}`);
    header.html(this.getHeaderHtml())
  },
  // 刷新Body（即iframe部分）
  refreshBody:function(){
    var nowId = this.getNowId();
    //容器刷新
    var body=this.$el.find(".panel-collapse");
    body.attr("id",`type_${nowId}`);
    body.attr("name",`${nowId}`);
    body.attr("aria-labelledby",`panel_${nowId}`);

    if(body.find(".dynr").length==0)body.html(this.getBodyHtml());
    else {
      var obj = this.getData();
      body.find(".dynr")[0].id=`if_${nowId}`

    }
  },
  // 刷新
  refresh:function(){
    this.refreshPanel();
    this.refreshHeader();
    this.refreshBody();
  },
  // 编辑按钮
  edit:function(ev){
    // //console.log(ev)
    // //console.log($("#panel-170"))
    ev.stopPropagation();
    //切换模式
    this.isEditor=!this.isEditor;
    var nowId=this.getNowId();
    //滚动到当前位置
    var panel=this.$el.find(".panel")[0]
    $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(panel).offset().top-heightDW()},300);
    // $('.zd').scrollTop($('.zd').scrollTop()+$(panel).offset().top-heightDW());
    // panel.scrollIntoView(true);

    var popUp = $("#simpleMode").is(':checked')
    if(popUp == true)this.editPop();
    else this.editBox();
    //展开内容面板
    this.$el.find(".panel-collapse").collapse('show');
  },
  edit1:function(ev){
    ev.stopPropagation();
    //切换模式
    this.isEditor=!this.isEditor;
    var nowId=this.getNowId();
    //滚动到当前位置
    var panel=this.$el.find(".panel")[0]
    $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(panel).offset().top-heightDW()},300);
    // $('.zd').scrollTop($('.zd').scrollTop()+$(panel).offset().top-heightDW());
    // panel.scrollIntoView(true);

    var popUp = $("#simpleMode").is(':checked')
    if(popUp)this.editPop1();
    else this.editBox1();
    //展开内容面板
    this.$el.find(".panel-collapse").collapse('show');
  },
  // 弹窗编辑
  editPop:function(){
    this.$el.find(".panel-collapse").html(this.getBodyHtml());
    this.$el.find(".editclick").hover(function(){
      $(this).css({
        "color": "#FFFFFF",
        "background": "#888888",
      })
    },function(){
      $(this).css({
        "color": "#707070",
        "background":"#FFFFFF",
      })
    })
    this.$el.find(".panel_ct").css({
      "display":"none"
    })
    var data=this.getData();
    if(data.WSID==0){//新增
      if(this.isEditor)newPopUp(data,data.WSID,iframe_Id);
      else this.$el.find(".panel_ct").css({
        "display":"none"
      })
    }else{
      if(this.isEditor){
        var title=`${data.JLSJ}${data.GSMC}(书写人员：${data.SXRY })}`;
        var url=this.getUrlBlws();
        let model = $("#simpleModeModal").iziModal({ //初始化modal
          overlayClose: false, //点击遮罩关闭框
          title: title,
          width: 1332
        })
        $('.iziModal-button-close').unbind('click').click(function(e){
          //移除close属性并绑定点击事件
            $('.iziModal-button-close').removeAttr("data-izimodal-close")
            popUp_button.close1(`${data.WSID}`)
          })
          model.iziModal('setContent',
            `<div class="tc">
              <div>
                <iframe class="dynr" id="if_${data.WSID}"  name="${data.GSMC}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?` + url + `" rameborder="0" width="100%"></iframe>
              </div>
              <div>
                <a class="saveclick e_btn" id="save${data.WSID}" name="${data.WSID}" onclick="popUp_button.save1('${data.WSID}')" type="button">保存</a>
                <a class="delclick e_btn" id="del${data.WSID}" name="${data.WSID}" onclick="popUp_button.delete_btn('${data.WSID}')" type="button">删除</a>
                <a class="imgclick e_btn" id="img${data.WSID}" name="${data.WSID}" onclick="popUp_button.imggl('${data.WSID}')" type="button">图片管理</a>
                <a class="closetc e_btn" name="${data.WSID}" onclick="popUp_button.close1('${data.WSID}')" type="button" name="close">关闭</a>
              </div>
            </div>`);
          load(data.WSID)
             //打开
          this.$el.find(".panel-collapse").html(this.getBodyHtml());
          model.iziModal('open')
      }
      else this.$el.find(".panel_ct").css({
        "display":"none"
      })
    }
    
  },
  // 非弹窗编辑
  editBox:function(){
    //obj.WSID
    var data=this.getData();
    //console.log(data)
    this.$el.find(".panel-collapse").html(this.getBodyHtml());
    if(this.isEditor){
      // this.$el.find(".editclick").css({
      //   "color": "#707070",
      //   "background":"#FFFFFF",
      // })
      this.$el.find(".editclick").hover(function(){
        $(this).css({
          "color": "#707070",
          "background":"#FFFFFF",
        })
      },function(){
        $(this).css({
          "color": "#FFFFFF",
          "background": "#888888",
        })
      })
      this.$el.find(".panel_ct").css({
        "display":""
      })
    }else{
      this.$el.find(".editclick").hover(function(){
        $(this).css({
          "color": "#FFFFFF",
          "background": "#888888",
        })
      },function(){
        $(this).css({
          "color": "#707070",
          "background":"#FFFFFF",
        })
      })
      this.$el.find(".panel_ct").css({
        "display":"none"
      })
    }
  },
  editPop1:function(){
    // if (this.$el.find("iframe").length!=0) {
    //   this.$el.find(".panel-collapse").html(this.getBodyHtml());
    // }
    this.$el.find(".editclick").hover(function(){
      $(this).css({
        "color": "#FFFFFF",
        "background": "#888888",
      })
    },function(){
      $(this).css({
        "color": "#707070",
        "background":"#FFFFFF",
      })
    })
    this.$el.find(".panel_ct").css({
      "display":"none"
    })
    var data=this.getData();
    if(data.WSID==0){//新增
      if(this.isEditor)newPopUp(data,data.WSID,iframe_Id);
      else this.$el.find(".panel_ct").css({
        "display":"none"
      })
    }else{
      if(this.isEditor || this.$el.find("iframe").length!=0){
        if (this.$el.find("iframe").length!=0) {
          this.$el.find(".panel-collapse").html(this.getBodyHtml());
        }
        var title=`${data.JLSJ}${data.GSMC}(书写人员：${data.SXRY })}`;
        var url=this.getUrlBlws();
        let model = $("#simpleModeModal").iziModal({ //初始化modal
          overlayClose: false, //点击遮罩关闭框
          title: title,
          width: 1332
        })
        $('.iziModal-button-close').unbind('click').click(function(e){
          //移除close属性并绑定点击事件
            $('.iziModal-button-close').removeAttr("data-izimodal-close")
            popUp_button.close1(`${data.WSID}`)
          })
          model.iziModal('setContent',
            `<div class="tc">
              <div>
                <iframe class="dynr" id="if_${data.WSID}"  name="${data.GSMC}" src="${WRT_config.server}/zyblws/blwsdetail.aspx?` + url + `" rameborder="0" width="100%"></iframe>
              </div>
              <div>
                <a class="saveclick e_btn" id="save${data.WSID}" name="${data.WSID}" onclick="popUp_button.save1('${data.WSID}')" type="button">保存</a>
                <a class="delclick e_btn" id="del${data.WSID}" name="${data.WSID}" onclick="popUp_button.delete_btn('${data.WSID}')" type="button">删除</a>
                <a class="imgclick e_btn" id="img${data.WSID}" name="${data.WSID}" onclick="popUp_button.imggl('${data.WSID}')" type="button">图片管理</a>
                <a class="closetc e_btn" name="${data.WSID}" onclick="popUp_button.close1('${data.WSID}')" type="button" name="close">关闭</a>
              </div>
            </div>`);
          load(data.WSID)
             //打开
          this.$el.find(".panel-collapse").html(this.getBodyHtml());
          model.iziModal('open')
      }
      else this.$el.find(".panel_ct").css({
        "display":"none"
      })
    }
    
  },
  editBox1:function(){
    // //console.log(this.$el.find("iframe").length)
    this.isEditor  = true
    if (this.$el.find("iframe").length==0) {
      this.$el.find(".panel-collapse").html(this.getBodyHtml());
    }
    this.$el.find(".editclick").css({
      "color": "#FFFFFF",
      "background": "#888888",
    })
    this.$el.find(".editclick").hover(function(){
      $(this).css({
        "color": "#707070",
        "background":"#FFFFFF",
      })
    },function(){
      $(this).css({
        "color": "#FFFFFF",
        "background": "#888888",
      })
    })
    this.$el.find(".panel_ct").css({
      "display":""
    })
  },
  edit2:function(ev){
    ev.stopPropagation();
    //切换模式
    this.isEditor=false;
    var nowId=this.getNowId();
    //滚动到当前位置
    var panel=this.$el.find(".panel")[0]
    $('.zd').animate({scrollTop:$('.zd').scrollTop()+$(panel).offset().top-heightDW()},300);

    // var popUp = $("#simpleMode").is(':checked')
    // if(popUp)this.editPop1();
    // else 
    this.editBox2();
    //展开内容面板
    this.$el.find(".panel-collapse").collapse('show');
  },
  editBox2:function(){
    //obj.WSID
    var data=this.getData();
    //console.log(data)
    this.$el.find(".panel-collapse").html(this.getBodyHtml());
    // if(this.isEditor){
    //   // this.$el.find(".editclick").css({
    //   //   "color": "#707070",
    //   //   "background":"#FFFFFF",
    //   // })
    //   this.$el.find(".editclick").hover(function(){
    //     $(this).css({
    //       "color": "#707070",
    //       "background":"#FFFFFF",
    //     })
    //   },function(){
    //     $(this).css({
    //       "color": "#FFFFFF",
    //       "background": "#888888",
    //     })
    //   })
    //   this.$el.find(".panel_ct").css({
    //     "display":""
    //   })
    // }else{
      this.$el.find(".editclick").hover(function(){
        $(this).css({
          "color": "#FFFFFF",
          "background": "#888888",
        })
      },function(){
        $(this).css({
          "color": "#707070",
          "background":"#FFFFFF",
        })
      })
      this.$el.find(".panel_ct").css({
        "display":"none"
      })
    // }
  },
})