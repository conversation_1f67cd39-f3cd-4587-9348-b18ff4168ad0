<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>登录</title>
  <meta name="description" content="登录页">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=2">
  <link rel="icon" href="favicon.ico" type="image/x-icon">
  <!-- <link rel="stylesheet" href="css/e-common.css"> -->
  <script type="text/javascript">
    var userAgent = navigator.userAgent;
    if (userAgent.indexOf("compatible") > -1 && userAgent.indexOf("MSIE") > -1|| (!!window.ActiveXObject || "ActiveXObject" in window)) {
      alert('请使用谷歌浏览器');
    }
    document.write("<link rel='stylesheet' href='css/e-common.css?v=" + Date.now() + "'>");
  </script>
  <!-- 页面CSS -->
  <!-- <link rel="stylesheet" href="css/e-style.css"> -->
  <script type="text/javascript">
    document.write("<link rel='stylesheet' href='css/e-style.css?v=" + Date.now() + "'>");
  </script>
</head>
<style>
  .Log_animation{
    transform: translateY(-3px);
    box-shadow: 0 3px 6px 2px rgb(61 108 200 / 70%);
  }
  .login_host{
    position: absolute;
    z-index: 4;
    bottom: 10px;
    text-align: center;
    justify-content: center;
    width: 43%;
    font-size: 20px;
  }
  .login_host a:hover{
    text-decoration:none
  }
  #goURL{
    color: #fff;
    opacity: .5;
    cursor: pointer;
  }
  #goURL:hover{
    text-decoration:none
  }
  #ip_host{
    color: #fff;
    opacity: .5;
  }
</style>
<body>
  <div class="login_container">
    <div class="login_box">
      <div class="login_host" style="bottom: 40px;"><a id="goURL">返回原版</a></div>
      <div class="login_host"><a id="ip_host"></a><span style="color: rgb(255 255 255 / 50%)">(v94)</span></div>
      <div class="left logo">
        <div class="mask"></div>
        <div class="logo_icon">
          <img src="./images/logo.png" alt="">
        </div>
        <div class="logo_describe">
          <p>温州医科大学附属第一医院</p>
          <div class="en">THE FIRST AFFILIATED HOSPITAL OF WENZHOU MEDICAL UNIVERSITY</div>
        </div>
      </div>
      <div class="right">
        <div class="form" id="login">
          <div class="form_title">电子病历系统</div>
          <!-- 切换登录方式 -->
          <ul class="nav">
            <li class="active"><a href="#login_type_one" data-toggle="tab">密码登录</a></li>
            <li><a href="#login_type_two" data-toggle="tab">验证码登录</a></li>
            <li onclick="GetQRCode()"><a href="#login_type_three" data-toggle="tab">二维码登录</a></li>
          </ul>
          <div class="tab-content">
            <!-- 密码登录表单 -->
            <div class="tab-pane fade in active" id="login_type_one">
              <div class="e_form">
                <div class="e_form_item">
                  <div class="e_form_item_label">账号</div>
                  <input class="e_input" type="text" name="username" style="text-transform:uppercase">
                </div>
                <div class="e_form_item">
                  <div class="e_form_item_label">密码</div>
                  <input class="e_input" type="password" name="password">
                </div>
                <!-- <div class="e_form_item">
                  <div class="e_checkbox">
                    <input class="e_checkbox_input" type="checkbox">
                    <span class="e_checkbox_inner"></span>
                    <span class="e_checkbox_label">记住密码</span>
                  </div>
                  <div class="forget_password">
                    <a href="#">忘记密码？</a>
                  </div>
                </div> -->
              </div>
            </div>
            <div class="tab-pane fade" id="login_type_two">
              <!-- 验证码登录表单 -->
              <div class="e_form">
                <div class="e_form_item">
                  <div class="e_form_item_label">账号</div>
                  <input class="e_input" type="text" name="username" style="text-transform:uppercase">
                </div>
                <div class="e_form_item code">
                  <div class="e_form_item_label">验证码</div>
                  <input class="e_input" type="password" name="code">
                  <input id="btnCode" class="btn" type="button" value="获取验证码"/>
                  <!-- <button type="submit">获取验证码</button> -->
                </div>
              </div>
            </div>
            <div class="tab-pane fade" id="login_type_three">
              <!-- 二维码登录表单 -->
                <div id="qrcode" style="transform: translate(160px, 10px);">

                </div>
            </div>
          </div>
          <div class="text-center">
            <button id="btnLogin" class="btn form_btn">登&nbsp;&nbsp;&nbsp;&nbsp;录</button>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 配置文件 -->
  <!-- <script src="./e-config.js"></script> -->
  <!-- 公用模块JS -->
  <script src="lib/QRCode/QRCode.js"></script>
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='./e-config.js?v=" + Date.now() + "'><\/script>");
    document.write("<script type='text/javascript' src='js/e-common.js?v=" + Date.now() + "'><\/script>");
  </script>
  <!-- 登录页js文件 -->
  <!-- <script src="js/e-login.js"></script> -->
  <script type="text/javascript">
    document.write("<script type='text/javascript' src='js/e-login.js?v=" + Date.now() + "'><\/script>");
  </script>
</body>

</html>